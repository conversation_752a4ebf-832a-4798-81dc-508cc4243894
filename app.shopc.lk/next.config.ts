import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    domains: [
      "images.unsplash.com",
      "source.unsplash.com",
      "i.imgur.com",
      "assets.rapidui.dev",
      "assets.rapidui.dev",
      "www.twblocks.com",
      "fumadocs.vercel.app",
      "assets.aceternity.com",
      "images.pexels.com",
      "placehold.co",
      "cdn.jsdelivr.net",
      "avatars.githubusercontent.com",
      "storage.googleapis.com",
      "images.unsplash.com",
      "example.com",
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
