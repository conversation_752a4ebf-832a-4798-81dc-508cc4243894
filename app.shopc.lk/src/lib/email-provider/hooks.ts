import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { EmailProviderSettings } from "@/types/email-provider";
import * as api from "./api";
import * as demo from "./demo";
import { toast } from "@/hooks/use-toast";

export function useProviders(isDemo = false) {
  return useQuery({
    queryKey: ["email-marketing", "providers", { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listProviders();
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listProviders();
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}

export function useProvider(id: string, isDemo = false) {
  return useQuery({
    queryKey: ["email-marketing", "providers", id, { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.getProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.getProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    enabled: !!id, // Only run the query if id is provided
  });
}

export function useCreateProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<EmailProviderSettings>) => {
      if (isDemo) {
        const response = await demo.createProvider(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.createProvider(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["email-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "Email provider created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useUpdateProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<EmailProviderSettings>;
    }) => {
      if (isDemo) {
        const response = await demo.updateProvider(id, data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.updateProvider(id, data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["email-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "Email provider updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useDeleteProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.deleteProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.deleteProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["email-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "Email provider deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useSetDefaultProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.setDefaultProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.setDefaultProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["email-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "Default email provider set successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

export function useSendEmail(isDemo = false) {
  return useMutation({
    mutationFn: async (data: {
      to: string | string[];
      subject: string;
      html?: string;
      text?: string;
      providerId?: string;
      templateId?: string;
      variables?: Record<string, any>;
    }) => {
      if (isDemo) {
        const response = await demo.sendEmail(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.sendEmail(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Email sent successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to send email: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}

export function useTemplates(providerId?: string, isDemo = false) {
  return useQuery({
    queryKey: ["email-marketing", "templates", { providerId, isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listTemplates({ providerId });
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listTemplates({ providerId });
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}

export function useCreateTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      name: string;
      subject: string;
      html: string;
      text?: string;
      providerId?: string;
      tags?: string[];
    }) => {
      if (isDemo) {
        const response = await demo.createTemplate(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.createTemplate(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["email-marketing", "templates"],
      });
      toast({
        title: "Success",
        description: "Email template created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}
