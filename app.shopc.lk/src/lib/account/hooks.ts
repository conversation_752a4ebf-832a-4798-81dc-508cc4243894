import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth-contexts";
import { ApiStatus } from "@/types/common";
import { toast } from "sonner";
import { UpdateProfileRequest, TwoFactorEnable } from "./types";
import {
  updateProfile,
  generate2FASecret,
  enable2FA,
  disable2FA,
  getUserProfileData,
  getTwoFactorStatus,
} from "./api";

// Query keys for cache management
export const accountKeys = {
  all: ["account"] as const,
  profile: () => [...accountKeys.all, "profile"] as const,
  twoFactor: () => [...accountKeys.all, "2fa"] as const,
};

// Profile update hook
export function useUpdateProfile() {
  const queryClient = useQueryClient();
  const { refreshProfile } = useAuth();

  return useMutation({
    mutationFn: async ({
      data,
      avatarFile,
    }: {
      data: UpdateProfileRequest;
      avatarFile?: File;
    }) => {
      const response = await updateProfile(data, avatarFile);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to update profile");
      }
      return response.data;
    },
    onSuccess: async (data) => {
      // Refresh the auth context profile
      await refreshProfile();

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: accountKeys.profile() });

      toast.success(data?.message || "Your profile has been updated successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 2FA setup hook
export function useGenerate2FASecret() {

  return useMutation({
    mutationFn: async () => {
      const response = await generate2FASecret();
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to generate 2FA secret");
      }
      return response.data;
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 2FA enable hook
export function useEnable2FA() {
  const queryClient = useQueryClient();
  const { refreshProfile } = useAuth();

  return useMutation({
    mutationFn: async (data: TwoFactorEnable) => {
      const response = await enable2FA(data);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to enable 2FA");
      }
      return response.data;
    },
    onSuccess: async (data) => {
      // Refresh the auth context profile to update 2FA status
      await refreshProfile();

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: accountKeys.twoFactor() });

      toast.success(data?.message || "Two-factor authentication has been enabled successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// 2FA disable hook
export function useDisable2FA() {
  const queryClient = useQueryClient();
  const { refreshProfile } = useAuth();

  return useMutation({
    mutationFn: async () => {
      const response = await disable2FA();
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to disable 2FA");
      }
      return response.data;
    },
    onSuccess: async (data) => {
      // Refresh the auth context profile to update 2FA status
      await refreshProfile();

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: accountKeys.twoFactor() });

      toast.success(data?.message || "Two-factor authentication has been disabled successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Profile data query hook
export function useUserProfileData() {
  return useQuery({
    queryKey: accountKeys.profile(),
    queryFn: async () => {
      const response = await getUserProfileData();
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch profile data");
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// 2FA status query hook
export function useTwoFactorStatus() {
  return useQuery({
    queryKey: accountKeys.twoFactor(),
    queryFn: async () => {
      const response = await getTwoFactorStatus();
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch 2FA status");
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
