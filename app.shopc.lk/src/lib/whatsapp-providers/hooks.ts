import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type {
  WhatsAppProviderSettings,
  WhatsAppProviderCreateInput,
  WhatsAppTemplate,
  WhatsAppMessage,
} from "@/types/whatsapp-provider";
import * as api from "./api";
import * as demo from "./demo";
import { toast } from "@/hooks/use-toast";

export function useProviders(isDemo = false) {
  return useQuery({
    queryKey: ["whatsapp-marketing", "providers", { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listProviders();
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listProviders();
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}

export function useProvider(id: string, isDemo = false) {
  return useQuery({
    queryKey: ["whatsapp-marketing", "providers", id, { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.getProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.getProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    enabled: !!id,
  });
}

export function useCreateProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: WhatsAppProviderCreateInput) => {
      if (isDemo) {
        const response = await demo.createProvider(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.createProvider(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "WhatsApp provider created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useUpdateProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<WhatsAppProviderCreateInput>;
    }) => {
      if (isDemo) {
        const response = await demo.updateProvider(id, data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.updateProvider(id, data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "providers", variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "WhatsApp provider updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useDeleteProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.deleteProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.deleteProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "WhatsApp provider deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useSetDefaultProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.setDefaultProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.setDefaultProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "Default WhatsApp provider updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

// Template hooks
export function useTemplates(providerId?: string, isDemo = false) {
  return useQuery({
    queryKey: ["whatsapp-marketing", "templates", { providerId, isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listTemplates({ providerId });
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listTemplates({ providerId });
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}

export function useTemplate(id: string, isDemo = false) {
  return useQuery({
    queryKey: ["whatsapp-marketing", "templates", id, { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.getTemplate(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.getTemplate(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    enabled: !!id,
  });
}

export function useCreateTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<WhatsAppTemplate>) => {
      if (isDemo) {
        const response = await demo.createTemplate(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.createTemplate(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "templates"],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "whatsapp-marketing",
          "templates",
          { providerId: data.providerId },
        ],
      });
      toast({
        title: "Success",
        description: "WhatsApp template created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useUpdateTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<WhatsAppTemplate>;
    }) => {
      if (isDemo) {
        const response = await demo.updateTemplate(id, data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.updateTemplate(id, data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "templates", variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "templates"],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "whatsapp-marketing",
          "templates",
          { providerId: data.providerId },
        ],
      });
      toast({
        title: "Success",
        description: "WhatsApp template updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useDeleteTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.deleteTemplate(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.deleteTemplate(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["whatsapp-marketing", "templates"],
      });
      queryClient.invalidateQueries({
        queryKey: [
          "whatsapp-marketing",
          "templates",
          { providerId: data.providerId },
        ],
      });
      toast({
        title: "Success",
        description: "WhatsApp template deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

// Message hooks
export function useSendMessage(isDemo = false) {
  return useMutation({
    mutationFn: async (data: {
      to: string;
      templateId: string;
      providerId?: string;
      variables?: Record<string, string>;
    }) => {
      if (isDemo) {
        const response = await demo.sendMessage(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.sendMessage(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "WhatsApp message sent successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useMessages(params?: {
  status?: string;
  providerId?: string;
  isDemo?: boolean;
}) {
  const isDemo = params?.isDemo || false;

  return useQuery({
    queryKey: [
      "whatsapp-marketing",
      "messages",
      { status: params?.status, providerId: params?.providerId, isDemo },
    ],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listMessages({
          status: params?.status,
          providerId: params?.providerId,
        });
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listMessages({
        status: params?.status,
        providerId: params?.providerId,
      });
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}
