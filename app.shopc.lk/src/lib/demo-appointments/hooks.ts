import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import {
  assignUserToDemoAppointment,
  cancelDemoAppointment,
  checkDemoAppointmentExists,
  createDemoAppointment,
  fetchDemoAppointment,
  updateDemoAppointment,
} from "./api";
import { DemoAppointment } from "@/types/demo-appointment";

// Query keys for demo appointments
export const demoAppointmentKeys = {
  all: ["demo-appointments"] as const,
  details: (id?: string) =>
    id
      ? ([...demoAppointmentKeys.all, "details", id] as const)
      : ([...demoAppointmentKeys.all, "details"] as const),
  exists: () => [...demoAppointmentKeys.all, "exists"] as const,
};

/**
 * Hook to fetch a demo appointment for the current business
 */
export function useDemoAppointment() {
  return useQuery({
    queryKey: demoAppointmentKeys.details(),
    queryFn: () => fetchDemoAppointment(),
  });
}

/**
 * Hook to check if a demo appointment exists for the current business
 */
export function useDemoAppointmentExists() {
  return useQuery({
    queryKey: demoAppointmentKeys.exists(),
    queryFn: () => checkDemoAppointmentExists(),
  });
}

/**
 * Hook to create a demo appointment
 */
export function useCreateDemoAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (
      data: Omit<DemoAppointment, "id" | "createdAt" | "updatedAt">
    ) => createDemoAppointment(data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Demo appointment scheduled successfully",
      });
      queryClient.invalidateQueries({ queryKey: demoAppointmentKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error
          ? error.message
          : "Failed to schedule demo appointment",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to update a demo appointment
 */
export function useUpdateDemoAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: Partial<DemoAppointment>;
    }) => updateDemoAppointment(id, data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Demo appointment updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: demoAppointmentKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error
          ? error.message
          : "Failed to update demo appointment",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to assign a user to a demo appointment (admin only)
 */
export function useAssignUserToDemoAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      appointmentId,
      userId,
    }: {
      appointmentId: string;
      userId: string;
    }) => assignUserToDemoAppointment(appointmentId, userId),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User assigned to demo appointment successfully",
      });
      queryClient.invalidateQueries({ queryKey: demoAppointmentKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error
          ? error.message
          : "Failed to assign user to demo appointment",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to cancel a demo appointment
 */
export function useCancelDemoAppointment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => cancelDemoAppointment(id),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Demo appointment cancelled successfully",
      });
      queryClient.invalidateQueries({ queryKey: demoAppointmentKeys.all });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error
          ? error.message
          : "Failed to cancel demo appointment",
        variant: "destructive",
      });
    },
  });
}
