import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import { ApiStatus } from "@/types/common";
import {
  fetchPaymentAccountReports,
  fetchPaymentAccountReportsByAccount,
  fetchPaymentAccountReportsSummary,
  exportReportsAsCsv,
} from "./queries";
import { PaymentAccountReportFilterParams } from "@/types/payment-account-report";
import { useIsDemo } from "@/hooks/use-is-demo";

// Query keys for react-query cache management
export const paymentAccountReportKeys = {
  all: ["payment-account-reports"] as const,
  lists: () => [...paymentAccountReportKeys.all, "list"] as const,
  list: (filters: PaymentAccountReportFilterParams) =>
    [...paymentAccountReportKeys.lists(), filters] as const,
  details: () => [...paymentAccountReportKeys.all, "detail"] as const,
  detail: (id: string) => [...paymentAccountReportKeys.details(), id] as const,
};

/**
 * Hook for fetching payment account reports
 */
export function usePaymentAccountReports(
  params: PaymentAccountReportFilterParams
) {
  const { isDemo } = useIsDemo();

  return useQuery({
    queryKey: ["payment-account-reports", isDemo, params],
    queryFn: () => fetchPaymentAccountReports(isDemo, params),
    staleTime: 30000, // 30 seconds
  });
}

/**
 * Hook for fetching payment account reports by account ID
 */
export function usePaymentAccountReportsByAccount(
  accountId: string,
  params: Omit<PaymentAccountReportFilterParams, "accountId">
) {
  const { isDemo } = useIsDemo();

  return useQuery({
    queryKey: ["payment-account-reports", "account", accountId, isDemo, params],
    queryFn: () =>
      fetchPaymentAccountReportsByAccount(isDemo, accountId, params),
    staleTime: 30000, // 30 seconds
    enabled: !!accountId,
  });
}

/**
 * Hook for fetching payment account reports summary
 */
export function usePaymentAccountReportsSummary(
  params: PaymentAccountReportFilterParams
) {
  const { isDemo } = useIsDemo();

  return useQuery({
    queryKey: ["payment-account-reports", "summary", isDemo, params],
    queryFn: () => fetchPaymentAccountReportsSummary(isDemo, params),
    staleTime: 30000, // 30 seconds
  });
}

/**
 * Hook for exporting payment account reports as CSV
 */
export function useExportPaymentAccountReports() {
  const { isDemo } = useIsDemo();
  const [isExporting, setIsExporting] = useState(false);

  const exportMutation = useMutation({
    mutationFn: (params: PaymentAccountReportFilterParams) => {
      setIsExporting(true);
      return exportReportsAsCsv(isDemo, params);
    },
    onSuccess: (data) => {
      setIsExporting(false);
      if (data.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Reports exported successfully",
        });
      } else if (isDemo) {
        toast({
          title: "Info",
          description: "Export functionality is not available in demo mode",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to export reports",
          variant: "destructive",
        });
      }
    },
    onError: () => {
      setIsExporting(false);
      toast({
        title: "Error",
        description: "Failed to export reports",
        variant: "destructive",
      });
    },
    onSettled: () => {
      setIsExporting(false);
    },
  });

  const exportReports = (params: PaymentAccountReportFilterParams) => {
    exportMutation.mutate(params);
  };

  return {
    exportReports,
    isExporting,
  };
}
