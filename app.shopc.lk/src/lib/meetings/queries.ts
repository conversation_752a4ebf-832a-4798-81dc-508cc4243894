import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createMeeting<PERSON>pi,
  getMeetingsApi,
  getMeetingApi,
  updateMeetingApi,
  deleteMeetingApi,
  checkAvailabilityApi,
  getCurrentBusinessMeetingApi,
  bulkDeleteMeetingsApi,
  bulkUpdateMeetingStatusApi,
  updateMeetingStatusApi,
  rescheduleMeetingApi,
} from "./api";
import {
  CreateMeetingDto,
  UpdateMeetingDto,
  CheckAvailabilityDto,
  BulkDeleteMeetingDto,
  BulkUpdateMeetingStatusDto,
  MeetingStatus,
} from "@/types/meeting";
import { GetMeetingsSchema } from "./validations";
import { toast } from "@/hooks/use-toast";

// Query keys
export const meetingKeys = {
  all: ["meetings"] as const,
  lists: () => [...meetingKeys.all, "list"] as const,
  list: (params: GetMeetingsSchema) =>
    [...meetingKeys.lists(), params] as const,
  details: () => [...meetingKeys.all, "detail"] as const,
  detail: (id: string) => [...meetingKeys.details(), id] as const,
  availability: () => [...meetingKeys.all, "availability"] as const,
  availabilityByDate: (params: CheckAvailabilityDto) =>
    [...meetingKeys.availability(), params] as const,
  current: () => [...meetingKeys.all, "current"] as const,
};

// Get meetings query
export const useGetMeetingsQuery = (params: GetMeetingsSchema) => {
  return useQuery({
    queryKey: meetingKeys.list(params),
    queryFn: () => getMeetingsApi(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get single meeting query
export const useGetMeetingQuery = (id: string) => {
  return useQuery({
    queryKey: meetingKeys.detail(id),
    queryFn: () => getMeetingApi(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Check availability query
export const useCheckAvailabilityQuery = (params: CheckAvailabilityDto) => {
  return useQuery({
    queryKey: meetingKeys.availabilityByDate(params),
    queryFn: () => checkAvailabilityApi(params),
    enabled: !!params.date,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get current business meeting query
export const useGetCurrentBusinessMeetingQuery = () => {
  return useQuery({
    queryKey: meetingKeys.current(),
    queryFn: () => getCurrentBusinessMeetingApi(),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Create meeting mutation
export const useCreateMeetingMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createMeetingApi,
    onSuccess: (response) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.availability() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.current() });
        toast({
          title: "Success",
          description: "Meeting created successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create meeting",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create meeting",
        variant: "destructive",
      });
    },
  });
};

// Update meeting mutation
export const useUpdateMeetingMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMeetingDto }) =>
      updateMeetingApi(id, data),
    onSuccess: (response, { id }) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.detail(id) });
        queryClient.invalidateQueries({ queryKey: meetingKeys.availability() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.current() });
        toast({
          title: "Success",
          description: "Meeting updated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update meeting",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update meeting",
        variant: "destructive",
      });
    },
  });
};

// Delete meeting mutation
export const useDeleteMeetingMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteMeetingApi,
    onSuccess: (response) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.availability() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.current() });
        toast({
          title: "Success",
          description: "Meeting deleted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete meeting",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete meeting",
        variant: "destructive",
      });
    },
  });
};

// Bulk delete meetings mutation
export const useBulkDeleteMeetingsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: bulkDeleteMeetingsApi,
    onSuccess: (response) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.availability() });
        toast({
          title: "Success",
          description: "Meetings deleted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete meetings",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete meetings",
        variant: "destructive",
      });
    },
  });
};

// Bulk update meeting status mutation
export const useBulkUpdateMeetingStatusMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: bulkUpdateMeetingStatusApi,
    onSuccess: (response) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        toast({
          title: "Success",
          description: "Meeting status updated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update meeting status",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update meeting status",
        variant: "destructive",
      });
    },
  });
};

// Update meeting status mutation
export const useUpdateMeetingStatusMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: MeetingStatus }) =>
      updateMeetingStatusApi(id, status),
    onSuccess: (response, { id }) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.detail(id) });
        toast({
          title: "Success",
          description: "Meeting status updated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update meeting status",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update meeting status",
        variant: "destructive",
      });
    },
  });
};

// Reschedule meeting mutation
export const useRescheduleMeetingMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      scheduledDateTime,
      duration,
    }: {
      id: string;
      scheduledDateTime: string;
      duration?: number;
    }) => rescheduleMeetingApi(id, scheduledDateTime, duration),
    onSuccess: (response, { id }) => {
      if (response.status === "success") {
        queryClient.invalidateQueries({ queryKey: meetingKeys.lists() });
        queryClient.invalidateQueries({ queryKey: meetingKeys.detail(id) });
        queryClient.invalidateQueries({ queryKey: meetingKeys.availability() });
        toast({
          title: "Success",
          description: "Meeting rescheduled successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to reschedule meeting",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to reschedule meeting",
        variant: "destructive",
      });
    },
  });
};
