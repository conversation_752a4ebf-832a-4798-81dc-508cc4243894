import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";

// Campaign Category Query Keys
export const campaignCategoryKeys = {
  all: ["campaign-categories"] as const,
  lists: () => [...campaignCategoryKeys.all, "list"] as const,
  list: (filters: string) =>
    [...campaignCategoryKeys.lists(), { filters }] as const,
  details: () => [...campaignCategoryKeys.all, "detail"] as const,
  detail: (id: string) => [...campaignCategoryKeys.details(), id] as const,
};

// Mock API calls for demo mode
const mockFetchCategories = async () => {
  return [
    { id: "1", name: "Promotions", description: "Promotional campaigns" },
    { id: "2", name: "Seasonal", description: "Season-based campaigns" },
    {
      id: "3",
      name: "Customer Acquisition",
      description: "Campaigns for new customers",
    },
  ];
};

const mockFetchCategory = async (id: string) => {
  const categories = await mockFetchCategories();
  return categories.find((category) => category.id === id) || null;
};

const mockCreateCategory = async (data: any) => {
  return { id: Math.random().toString(), ...data };
};

const mockUpdateCategory = async ({ id, ...data }: any) => {
  return { id, ...data };
};

const mockDeleteCategory = async (id: string) => {
  return { success: true };
};

// API functions for real API
const fetchCategories = async () => {
  // Replace with actual API call
  const response = await fetch("/api/campaign-categories");
  if (!response.ok) {
    throw new Error("Failed to fetch campaign categories");
  }
  return response.json();
};

const fetchCategory = async (id: string) => {
  // Replace with actual API call
  const response = await fetch(`/api/campaign-categories/${id}`);
  if (!response.ok) {
    throw new Error("Failed to fetch campaign category");
  }
  return response.json();
};

const createCategory = async (data: any) => {
  // Replace with actual API call
  const response = await fetch("/api/campaign-categories", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Failed to create campaign category");
  }

  return response.json();
};

const updateCategory = async ({ id, ...data }: any) => {
  // Replace with actual API call
  const response = await fetch(`/api/campaign-categories/${id}`, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error("Failed to update campaign category");
  }

  return response.json();
};

const deleteCategory = async (id: string) => {
  // Replace with actual API call
  const response = await fetch(`/api/campaign-categories/${id}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    throw new Error("Failed to delete campaign category");
  }

  return response.json();
};

// React Query Hooks
export function useCampaignCategories(isDemo: boolean = false) {
  return useQuery({
    queryKey: campaignCategoryKeys.lists(),
    queryFn: isDemo ? mockFetchCategories : fetchCategories,
  });
}

export function useCampaignCategory(id: string, isDemo: boolean = false) {
  return useQuery({
    queryKey: campaignCategoryKeys.detail(id),
    queryFn: () => (isDemo ? mockFetchCategory(id) : fetchCategory(id)),
    enabled: !!id,
  });
}

export function useCreateCampaignCategory(isDemo: boolean = false) {
  return useMutation({
    mutationFn: isDemo ? mockCreateCategory : createCategory,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Category created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Error creating category: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}

export function useUpdateCampaignCategory(isDemo: boolean = false) {
  return useMutation({
    mutationFn: isDemo ? mockUpdateCategory : updateCategory,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Category updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Error updating category: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}

export function useDeleteCampaignCategory(isDemo: boolean = false) {
  return useMutation({
    mutationFn: isDemo ? mockDeleteCategory : deleteCategory,
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Category deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Error deleting category: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}
