import { create } from "zustand";
import { persist } from "zustand/middleware";

interface Card {
  type:
    | "product-category"
    | "countdown"
    | "link"
    | "text"
    | "image"
    | "video"
    | "connect"
    | "map";
  title?: string;
  description?: string;
  link?: string;
  imageUrl?: string;
  videoUrl?: string;
  location?: string;
  countdownDate?: string;
}

interface MenuItem {
  id: string;
  label: string;
  link: string;
}

interface PaymentMethod {
  cashOnDelivery: boolean;
  bankTransfer: boolean;
  onlinePayment: boolean;
}

interface ShippingOptions {
  defaultMethod: string;
  shippingCost: number;
  freeShipping: boolean;
  freeShippingMinimum: number;
}

interface OrderSettings {
  sendEmailConfirmation: boolean;
  sendSMSNotification: boolean;
  guestCheckout: boolean;
  orderNotes: boolean;
  termsAndConditions: boolean;
}

interface AppearanceSettings {
  storeName: string;
  description: string;
  profileImage: string;
  primaryColor: string;
  backgroundColor: string;
  bannerText: string;
  bannerLink: string;
}

interface MenuSettings {
  showOnMobile: boolean;
  stickyMenu: boolean;
  showSearch: boolean;
}

interface ShopcState {
  // Storefront
  cards: Card[];
  addCard: (card: Card) => void;
  updateCard: (index: number, card: Card) => void;
  removeCard: (index: number) => void;
  setCards: (cards: Card[]) => void;

  // Checkout
  paymentMethods: PaymentMethod;
  shippingOptions: ShippingOptions;
  orderSettings: OrderSettings;
  updatePaymentMethods: (methods: Partial<PaymentMethod>) => void;
  updateShippingOptions: (options: Partial<ShippingOptions>) => void;
  updateOrderSettings: (settings: Partial<OrderSettings>) => void;

  // Appearance
  appearance: AppearanceSettings;
  updateAppearance: (settings: Partial<AppearanceSettings>) => void;

  // Menu
  menuItems: MenuItem[];
  menuSettings: MenuSettings;
  addMenuItem: (item: MenuItem) => void;
  updateMenuItem: (id: string, item: Partial<MenuItem>) => void;
  removeMenuItem: (id: string) => void;
  setMenuItems: (items: MenuItem[]) => void;
  updateMenuSettings: (settings: Partial<MenuSettings>) => void;
}

export const useShopcStore = create<ShopcState>()(
  persist(
    (set) => ({
      // Storefront state
      cards: [],
      addCard: (card) => set((state) => ({ cards: [...state.cards, card] })),
      updateCard: (index, card) =>
        set((state) => {
          const newCards = [...state.cards];
          newCards[index] = card;
          return { cards: newCards };
        }),
      removeCard: (index) =>
        set((state) => {
          const newCards = [...state.cards];
          newCards.splice(index, 1);
          return { cards: newCards };
        }),
      setCards: (cards) => set({ cards }),

      // Checkout state
      paymentMethods: {
        cashOnDelivery: false,
        bankTransfer: false,
        onlinePayment: false,
      },
      shippingOptions: {
        defaultMethod: "",
        shippingCost: 0,
        freeShipping: false,
        freeShippingMinimum: 0,
      },
      orderSettings: {
        sendEmailConfirmation: false,
        sendSMSNotification: false,
        guestCheckout: false,
        orderNotes: false,
        termsAndConditions: false,
      },
      updatePaymentMethods: (methods) =>
        set((state) => ({
          paymentMethods: { ...state.paymentMethods, ...methods },
        })),
      updateShippingOptions: (options) =>
        set((state) => ({
          shippingOptions: { ...state.shippingOptions, ...options },
        })),
      updateOrderSettings: (settings) =>
        set((state) => ({
          orderSettings: { ...state.orderSettings, ...settings },
        })),

      // Appearance state
      appearance: {
        storeName: "",
        description: "",
        profileImage: "",
        primaryColor: "#de1212",
        backgroundColor: "#8415e6",
        bannerText: "",
        bannerLink: "",
      },
      updateAppearance: (settings) =>
        set((state) => ({
          appearance: { ...state.appearance, ...settings },
        })),

      // Menu state
      menuItems: [
        {
          id: "1",
          label: "Home",
          link: "/",
        },
        {
          id: "2",
          label: "Products",
          link: "/products",
        },
        {
          id: "3",
          label: "About",
          link: "/about",
        },
        {
          id: "4",
          label: "Contact",
          link: "/contact",
        },
      ],
      menuSettings: {
        showOnMobile: true,
        stickyMenu: true,
        showSearch: false,
      },
      addMenuItem: (item) =>
        set((state) => ({
          menuItems: [...state.menuItems, item],
        })),
      updateMenuItem: (id, item) =>
        set((state) => ({
          menuItems: state.menuItems.map((menuItem) =>
            menuItem.id === id ? { ...menuItem, ...item } : menuItem
          ),
        })),
      removeMenuItem: (id) =>
        set((state) => ({
          menuItems: state.menuItems.filter((item) => item.id !== id),
        })),
      setMenuItems: (items) => set({ menuItems: items }),
      updateMenuSettings: (settings) =>
        set((state) => ({
          menuSettings: { ...state.menuSettings, ...settings },
        })),
    }),
    {
      name: "shopc-storage",
      skipHydration: true,
    }
  )
);

// Keep the old export for backward compatibility
export const useStorefrontStore = useShopcStore;
