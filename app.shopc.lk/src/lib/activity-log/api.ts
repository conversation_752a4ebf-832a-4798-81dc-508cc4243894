import {
  ActivityLogResponse,
  ActivityLogPaginatedResponse,
  ActivityLogIdResponse,
  CreateActivityLogDto,
  UpdateActivityLogDto,
  BulkDeleteActivityLogDto,
  BulkDeleteActivityLogResponse,
  ExportActivityLogsDto,
  SimpleActivityLogResponse,
  ActivityLogStatsResponse,
  ActivityLogExportResponse,
} from "@/types/activity-log";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetActivityLogsSchema } from "./validations";
import axios from "@/utils/axios";

// Get activity logs with pagination and filters
export async function getActivityLogsApi(
  params: GetActivityLogsSchema
): Promise<ActivityLogPaginatedResponse> {
  try {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append("page", String(params.page));
    if (params.perPage) searchParams.append("limit", String(params.perPage));
    if (params.searchTerm) searchParams.append("searchTerm", params.searchTerm);
    if (params.userId) searchParams.append("userId", params.userId);
    if (params.type) searchParams.append("type", params.type);
    if (params.entityType) searchParams.append("entityType", params.entityType);
    if (params.entityId) searchParams.append("entityId", params.entityId);
    if (params.status) searchParams.append("status", params.status);
    if (params.from) searchParams.append("from", params.from);
    if (params.to) searchParams.append("to", params.to);
    if (params.filters && params.filters.length > 0) {
      searchParams.append("filters", JSON.stringify(params.filters));
    }
    if (params.joinOperator)
      searchParams.append("joinOperator", params.joinOperator);
    if (params.sort && params.sort.length > 0) {
      searchParams.append("sort", JSON.stringify(params.sort));
    }

    const res = await axios.get(`/activity-logs?${searchParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get activity logs in slim format (for dropdowns/selects)
export async function getActivityLogsSlimApi(): Promise<SimpleActivityLogResponse> {
  try {
    const res = await axios.get("/activity-logs/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single activity log by ID
export async function getActivityLogApi(id: string): Promise<ActivityLogResponse> {
  try {
    const res = await axios.get(`/activity-logs/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Create a new activity log
export async function createActivityLogApi(
  data: CreateActivityLogDto
): Promise<ActivityLogIdResponse> {
  try {
    const res = await axios.post("/activity-logs", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update an activity log (usually not allowed, but included for completeness)
export async function updateActivityLogApi(
  id: string,
  data: UpdateActivityLogDto
): Promise<ActivityLogIdResponse> {
  try {
    const res = await axios.patch(`/activity-logs/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete an activity log
export async function deleteActivityLogApi(
  id: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  try {
    const res = await axios.delete(`/activity-logs/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete activity logs
export async function bulkDeleteActivityLogsApi(
  activityLogIds: string[]
): Promise<BulkDeleteActivityLogResponse> {
  try {
    const data: BulkDeleteActivityLogDto = { activityLogIds };
    const res = await axios.delete("/activity-logs/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get activity log statistics
export async function getActivityLogStatsApi(
  params: any  // Using any for now since we don't have the exact schema
): Promise<ActivityLogStatsResponse> {
  try {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        if (Array.isArray(value)) {
          searchParams.append(key, JSON.stringify(value));
        } else if (typeof value === "object") {
          searchParams.append(key, JSON.stringify(value));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });

    const res = await axios.get(`/activity-logs/stats?${searchParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Export activity logs
export async function exportActivityLogsApi(
  params: ExportActivityLogsDto
): Promise<ActivityLogExportResponse> {
  try {
    const res = await axios.post("/activity-logs/export", params);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Utility functions for data transformation
export function transformActivityLogForDisplay(activityLog: any) {
  return {
    ...activityLog,
    formattedDate: new Date(activityLog.createdAt).toLocaleString(),
    typeLabel: getActivityLogTypeLabel(activityLog.type),
    statusLabel: getActivityLogStatusLabel(activityLog.status),
    entityTypeLabel: getActivityLogEntityTypeLabel(activityLog.entityType),
  };
}

// Helper functions for labels
export function getActivityLogTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    create: "Create",
    update: "Update",
    delete: "Delete",
    view: "View",
    login: "Login",
    logout: "Logout",
    export: "Export",
    import: "Import",
    upload: "Upload",
    download: "Download",
    approve: "Approve",
    reject: "Reject",
    archive: "Archive",
    restore: "Restore",
  };
  return labels[type] || type;
}

export function getActivityLogStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    success: "Success",
    failed: "Failed",
    pending: "Pending",
  };
  return labels[status] || status;
}

export function getActivityLogEntityTypeLabel(entityType: string): string {
  const labels: Record<string, string> = {
    user: "User",
    category: "Category",
    service_category: "Service Category",
    product: "Product",
    service: "Service",
    customer: "Customer",
    supplier: "Supplier",
    staff: "Staff",
    location: "Location",
    invoice: "Invoice",
    order: "Order",
    payment: "Payment",
    inventory: "Inventory",
    business: "Business",
    account: "Account",
    task: "Task",
    project: "Project",
    campaign: "Campaign",
    brand: "Brand",
    asset: "Asset",
    vehicle: "Vehicle",
    equipment: "Equipment",
  };
  return labels[entityType] || entityType;
}