import { z } from "zod";
import { 
  ActivityLogType, 
  ActivityEntityType, 
  ActivityLogStatus 
} from "@/types/activity-log";

// Backend DTO: CreateActivityLogDto
export const createActivityLogSchema = z.object({
  type: z.enum([
    ActivityLogType.CREATE,
    ActivityLogType.UPDATE,
    ActivityLogType.DELETE,
    ActivityLogType.VIEW,
    ActivityLogType.LOGIN,
    ActivityLogType.LOGOUT,
    ActivityLogType.EXPORT,
    ActivityLogType.IMPORT,
    ActivityLogType.UPLOAD,
    ActivityLogType.DOWNLOAD,
    ActivityLogType.APPROVE,
    ActivityLogType.REJECT,
    ActivityLogType.ARCHIVE,
    ActivityLogType.RESTORE,
  ]),
  entityType: z.enum([
    ActivityEntityType.USER,
    ActivityEntityType.CATEGORY,
    ActivityEntityType.SERVICE_CATEGORY,
    ActivityEntityType.PRODUCT,
    ActivityEntityType.SERVICE,
    ActivityEntityType.CUSTOMER,
    ActivityEntityType.SUPPLIER,
    ActivityEntityType.STAFF,
    ActivityEntityType.LOCATION,
    ActivityEntityType.INVOICE,
    ActivityEntityType.ORDER,
    ActivityEntityType.PAYMENT,
    ActivityEntityType.INVENTORY,
    ActivityEntityType.BUSINESS,
    ActivityEntityType.ACCOUNT,
    ActivityEntityType.TASK,
    ActivityEntityType.PROJECT,
    ActivityEntityType.CAMPAIGN,
    ActivityEntityType.BRAND,
    ActivityEntityType.ASSET,
    ActivityEntityType.VEHICLE,
    ActivityEntityType.EQUIPMENT,
  ]),
  entityId: z.string().uuid("Invalid entity ID").optional(),
  entityName: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  metadata: z.record(z.any()).optional(),
  ipAddress: z.string().ip("Invalid IP address").optional(),
  userAgent: z.string().optional(),
  status: z
    .enum([
      ActivityLogStatus.SUCCESS,
      ActivityLogStatus.FAILED,
      ActivityLogStatus.PENDING,
    ])
    .optional()
    .default(ActivityLogStatus.SUCCESS),
});

// Schema for getting activity logs with pagination and filters
export const getActivityLogsSchema = z.object({
  page: z.number().min(1, "Page must be at least 1").default(1),
  perPage: z
    .number()
    .min(1, "Per page must be at least 1")
    .max(100, "Per page cannot exceed 100")
    .default(10),
  userId: z.string().uuid("Invalid user ID").optional(),
  type: z.string().optional(),
  entityType: z.string().optional(),
  entityId: z.string().uuid("Invalid entity ID").optional(),
  status: z.string().optional(),
  searchTerm: z.string().optional(),
  from: z.string().optional(), // ISO date string
  to: z.string().optional(), // ISO date string
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Schema for activity log statistics
export const getActivityLogStatsSchema = z.object({
  dateRange: z
    .object({
      from: z.string(), // ISO date string
      to: z.string(), // ISO date string
    })
    .optional(),
  entityType: z.string().optional(),
  userId: z.string().uuid("Invalid user ID").optional(),
});

// Schema for exporting activity logs
export const exportActivityLogsSchema = z.object({
  format: z.enum(["csv", "xlsx", "json"]).default("csv"),
  filters: z
    .object({
      userId: z.string().uuid("Invalid user ID").optional(),
      type: z.string().optional(),
      entityType: z.string().optional(),
      status: z.string().optional(),
      entityId: z.string().uuid("Invalid entity ID").optional(),
      dateFrom: z.string().optional(), // ISO date string
      dateTo: z.string().optional(), // ISO date string
      searchTerm: z.string().optional(),
    })
    .optional(),
  columns: z.array(z.string()).optional(),
  dateRange: z
    .object({
      from: z.string(), // ISO date string
      to: z.string(), // ISO date string
    })
    .optional(),
});

// Form schema for UI components (if needed for filtering)
export const activityLogFilterFormSchema = z.object({
  userId: z.string().optional(),
  type: z.string().optional(),
  entityType: z.string().optional(),
  status: z.string().optional(),
  entityId: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  searchTerm: z.string().optional(),
});

// Type exports for use in components
export type CreateActivityLogSchema = z.infer<typeof createActivityLogSchema>;
export type GetActivityLogsSchema = z.infer<typeof getActivityLogsSchema>;
export type GetActivityLogStatsSchema = z.infer<typeof getActivityLogStatsSchema>;
export type ExportActivityLogsSchema = z.infer<typeof exportActivityLogsSchema>;
export type ActivityLogFilterFormSchema = z.infer<typeof activityLogFilterFormSchema>;