import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  ActivityLogPaginatedResponse,
  ActivityLogResponse,
  SimpleActivityLogResponse,
  ActivityLogStatsResponse,
  ActivityLogExportResponse,
  CreateActivityLogDto,
} from "@/types/activity-log";
import { ApiResponse } from "@/types/common";
import {
  GetActivityLogsSchema,
  GetActivityLogStatsSchema,
  ExportActivityLogsSchema,
} from "./validations";
import {
  getActivityLogsTableData,
  getActivityLogsSlim,
  getActivityLog,
  createActivityLog,
  deleteActivityLog,
  bulkDeleteActivityLogs,
  getActivityLogStats,
  exportActivityLogs,
  getRecentActivityLogs,
} from "./queries";

// Query keys for cache management
export const activityLogKeys = {
  all: ["activity-logs"] as const,
  list: () => [...activityLogKeys.all, "list"] as const,
  filtered: (params: GetActivityLogsSchema & { isDemo?: boolean }) =>
    [...activityLogKeys.list(), params] as const,
  simple: () => [...activityLogKeys.all, "simple"] as const,
  detail: (id: string) => [...activityLogKeys.all, "detail", id] as const,
  stats: (params: GetActivityLogStatsSchema & { isDemo?: boolean }) =>
    [...activityLogKeys.all, "stats", params] as const,
  recent: (limit: number, isDemo: boolean) =>
    [...activityLogKeys.all, "recent", limit, isDemo] as const,
};

// Hook to get activity logs with pagination and filters
export function useActivityLogsData(
  params: GetActivityLogsSchema,
  isDemo: boolean = false
): UseQueryResult<ActivityLogPaginatedResponse> {
  return useQuery({
    queryKey: activityLogKeys.filtered({ ...params, isDemo }),
    queryFn: () => getActivityLogsTableData(params, isDemo),
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    placeholderData: (previousData) => previousData,
  });
}

// Hook to get activity logs in slim format (for dropdowns)
export function useActivityLogsSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleActivityLogResponse> {
  return useQuery({
    queryKey: [...activityLogKeys.simple(), { isDemo }],
    queryFn: () => getActivityLogsSlim(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get a single activity log by ID
export function useActivityLogData(
  id: string,
  isDemo: boolean = false
): UseQueryResult<ActivityLogResponse> {
  return useQuery({
    queryKey: [...activityLogKeys.detail(id), { isDemo }],
    queryFn: () => getActivityLog(id, isDemo),
    enabled: !!id,
  });
}

// Hook to get activity log statistics
export function useActivityLogStats(
  params: GetActivityLogStatsSchema,
  isDemo: boolean = false
): UseQueryResult<ActivityLogStatsResponse> {
  return useQuery({
    queryKey: activityLogKeys.stats({ ...params, isDemo }),
    queryFn: () => getActivityLogStats(params, isDemo),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook to get recent activity logs
export function useRecentActivityLogs(
  limit: number = 10,
  isDemo: boolean = false
): UseQueryResult<SimpleActivityLogResponse> {
  return useQuery({
    queryKey: activityLogKeys.recent(limit, isDemo),
    queryFn: () => getRecentActivityLogs(isDemo, limit),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

// Mutation hook to create an activity log
export function useCreateActivityLog(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateActivityLogDto) => createActivityLog(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch activity log queries
      queryClient.invalidateQueries({ queryKey: activityLogKeys.all });
    },
  });
}

// Mutation hook to delete an activity log
export function useDeleteActivityLog(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteActivityLog(id, isDemo),
    onSuccess: () => {
      // Invalidate and refetch activity log queries
      queryClient.invalidateQueries({ queryKey: activityLogKeys.all });
    },
  });
}

// Mutation hook to bulk delete activity logs
export function useBulkDeleteActivityLogs(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (activityLogIds: string[]) =>
      bulkDeleteActivityLogs(activityLogIds, isDemo),
    onSuccess: () => {
      // Invalidate and refetch activity log queries
      queryClient.invalidateQueries({ queryKey: activityLogKeys.all });
    },
  });
}

// Mutation hook to export activity logs
export function useExportActivityLogs(isDemo: boolean = false) {
  return useMutation({
    mutationFn: (data: ExportActivityLogsSchema) => exportActivityLogs(data, isDemo),
    // Note: We don't invalidate queries for export as it doesn't change data
  });
}

// Custom hook for activity log table with built-in refresh capability
export function useActivityLogsTable(
  params: GetActivityLogsSchema,
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();
  const query = useActivityLogsData(params, isDemo);

  const refresh = () => {
    queryClient.invalidateQueries({
      queryKey: activityLogKeys.filtered({ ...params, isDemo }),
    });
  };

  return {
    ...query,
    refresh,
  };
}

// Custom hook for activity log dashboard with stats and recent activities
export function useActivityLogDashboard(
  statsParams: GetActivityLogStatsSchema,
  recentLimit: number = 5,
  isDemo: boolean = false
) {
  const statsQuery = useActivityLogStats(statsParams, isDemo);
  const recentQuery = useRecentActivityLogs(recentLimit, isDemo);

  return {
    stats: statsQuery,
    recent: recentQuery,
    isLoading: statsQuery.isLoading || recentQuery.isLoading,
    error: statsQuery.error || recentQuery.error,
  };
}

// Legacy hooks for backward compatibility
export const useActivityLogsSimple = useActivityLogsSlim;
export const useActivityLogDetail = useActivityLogData;