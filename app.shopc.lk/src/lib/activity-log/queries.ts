import {
  ActivityLogPaginatedResponse,
  ActivityLogResponse,
  SimpleActivityLogResponse,
  ActivityLogStatsResponse,
  ActivityLogExportResponse,
  CreateActivityLogDto,
} from "@/types/activity-log";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  GetActivityLogsSchema,
  CreateActivityLogSchema,
  GetActivityLogStatsSchema,
  ExportActivityLogsSchema,
} from "./validations";

// Real API imports
import {
  getActivityLogsApi,
  getActivityLogsSlimApi,
  getActivityLogApi,
  createActivityLogApi,
  deleteActivityLogApi,
  bulkDeleteActivityLogsApi,
  getActivityLogStatsApi,
  exportActivityLogsApi,
} from "./api";

// Note: Demo data functions are not implemented as per requirements

// Get activity logs table data with pagination and filters
export async function getActivityLogsTableData(
  params: GetActivityLogsSchema,
  isDemo: boolean
): Promise<ActivityLogPaginatedResponse> {
  if (isDemo) {
    // Return empty response for demo mode since demo data is not required
    return {
      status: ApiStatus.SUCCESS,
      message: "Demo mode - no data available",
      data: {
        data: [],
        meta: {
          total: 0,
          page: 1,
          totalPages: 0,
        },
      },
    };
  } else {
    return await getActivityLogsApi(params);
  }
}

// Get activity logs in slim format (for dropdowns/selects)
export async function getActivityLogsSlim(
  isDemo: boolean
): Promise<SimpleActivityLogResponse> {
  if (isDemo) {
    // Return empty response for demo mode since demo data is not required
    return {
      status: ApiStatus.SUCCESS,
      message: "Demo mode - no data available",
      data: [],
    };
  } else {
    return await getActivityLogsSlimApi();
  }
}

// Get a single activity log by ID
export async function getActivityLog(
  id: string,
  isDemo: boolean
): Promise<ActivityLogResponse> {
  if (isDemo) {
    // Return empty response for demo mode since demo data is not required
    return {
      status: ApiStatus.FAIL,
      message: "Demo mode - no data available",
      data: null,
    };
  } else {
    return await getActivityLogApi(id);
  }
}

// Create a new activity log
export async function createActivityLog(
  data: CreateActivityLogDto,
  isDemo: boolean
): Promise<ActivityLogResponse> {
  if (isDemo) {
    // Return mock success response for demo mode
    return {
      status: ApiStatus.SUCCESS,
      message: "Activity log created (demo mode)",
      data: {
        id: `demo-${Date.now()}`,
        businessId: "demo-business",
        userId: "demo-user",
        userName: "Demo User",
        userEmail: "<EMAIL>",
        type: data.type,
        entityType: data.entityType,
        entityId: data.entityId,
        entityName: data.entityName,
        description: data.description,
        metadata: data.metadata,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        status: data.status || "success",
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    };
  } else {
    return await createActivityLogApi(data);
  }
}

// Delete an activity log
export async function deleteActivityLog(
  id: string,
  isDemo: boolean
): Promise<ApiResponse<{ message: string }>> {
  if (isDemo) {
    // Return mock success response for demo mode
    return {
      status: ApiStatus.SUCCESS,
      message: "Activity log deleted (demo mode)",
      data: { message: "Activity log deleted successfully" },
    };
  } else {
    return await deleteActivityLogApi(id);
  }
}

// Bulk delete activity logs
export async function bulkDeleteActivityLogs(
  activityLogIds: string[],
  isDemo: boolean
): Promise<ApiResponse<{ message: string; deletedCount: number }>> {
  if (isDemo) {
    // Return mock success response for demo mode
    return {
      status: ApiStatus.SUCCESS,
      message: "Activity logs deleted (demo mode)",
      data: {
        message: "Activity logs deleted successfully",
        deletedCount: activityLogIds.length,
      },
    };
  } else {
    return await bulkDeleteActivityLogsApi({ ids: activityLogIds });
  }
}

// Get activity log statistics
export async function getActivityLogStats(
  params: GetActivityLogStatsSchema,
  isDemo: boolean
): Promise<ActivityLogStatsResponse> {
  if (isDemo) {
    // Return mock stats for demo mode
    return {
      status: ApiStatus.SUCCESS,
      message: "Activity log stats (demo mode)",
      data: {
        totalActivities: 0,
        todayActivities: 0,
        successfulActivities: 0,
        failedActivities: 0,
        activitiesByType: {
          create: 0,
          update: 0,
          delete: 0,
          view: 0,
          login: 0,
          logout: 0,
          export: 0,
          import: 0,
          upload: 0,
          download: 0,
          approve: 0,
          reject: 0,
          archive: 0,
          restore: 0,
        },
        activitiesByEntityType: {
          user: 0,
          category: 0,
          service_category: 0,
          product: 0,
          service: 0,
          customer: 0,
          supplier: 0,
          staff: 0,
          location: 0,
          invoice: 0,
          order: 0,
          payment: 0,
          inventory: 0,
          business: 0,
          account: 0,
          task: 0,
          project: 0,
          campaign: 0,
          brand: 0,
          asset: 0,
          vehicle: 0,
          equipment: 0,
        },
        activitiesByStatus: {
          success: 0,
          failed: 0,
          pending: 0,
        },
        recentActivities: [],
      },
    };
  } else {
    return await getActivityLogStatsApi(params);
  }
}

// Export activity logs
export async function exportActivityLogs(
  data: ExportActivityLogsSchema,
  isDemo: boolean
): Promise<ActivityLogExportResponse> {
  if (isDemo) {
    // Return mock export response for demo mode
    return {
      status: ApiStatus.SUCCESS,
      message: "Activity logs exported (demo mode)",
      data: {
        downloadUrl: "#",
        fileName: `activity-logs-demo.${data.format}`,
        fileSize: 0,
      },
    };
  } else {
    return await exportActivityLogsApi(data);
  }
}

// Utility functions for data transformation
export function transformActivityLogForForm(activityLog: any) {
  return {
    type: activityLog.type || "",
    entityType: activityLog.entityType || "",
    entityId: activityLog.entityId || "",
    entityName: activityLog.entityName || "",
    description: activityLog.description || "",
    metadata: activityLog.metadata || {},
    status: activityLog.status || "success",
  };
}

// Legacy compatibility functions (for backward compatibility)
export async function getActivityLogsWithFilters(
  isDemo: boolean,
  filters?: any
): Promise<SimpleActivityLogResponse> {
  const params: GetActivityLogsSchema = {
    page: 1,
    perPage: 100,
    ...filters,
  };
  
  const response = await getActivityLogsTableData(params, isDemo);
  
  if (response.data?.data) {
    return {
      ...response,
      data: response.data.data.map(log => ({
        id: log.id,
        type: log.type,
        entityType: log.entityType,
        entityName: log.entityName,
        description: log.description,
        status: log.status,
        createdAt: log.createdAt,
        userName: log.userName,
      })),
    };
  }
  
  return {
    status: response.status,
    message: response.message,
    data: [],
  };
}

// Get recent activity logs
export async function getRecentActivityLogs(
  isDemo: boolean,
  limit: number = 10
): Promise<SimpleActivityLogResponse> {
  const params: GetActivityLogsSchema = {
    page: 1,
    perPage: limit,
    sort: [{ id: "createdAt", desc: true }],
  };
  
  const response = await getActivityLogsTableData(params, isDemo);
  
  if (response.data?.data) {
    return {
      ...response,
      data: response.data.data.map(log => ({
        id: log.id,
        type: log.type,
        entityType: log.entityType,
        entityName: log.entityName,
        description: log.description,
        status: log.status,
        createdAt: log.createdAt,
        userName: log.userName,
      })),
    };
  }
  
  return {
    status: response.status,
    message: response.message,
    data: [],
  };
}