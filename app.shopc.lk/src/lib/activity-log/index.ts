// API functions
export {
  getActivity<PERSON>ogs<PERSON>pi,
  getActivityLogsSlimApi,
  getActivityLogApi,
  createActivityLogApi,
  deleteActivityLogApi,
  bulkDeleteActivityLogsApi,
  getActivityLogStatsApi,
  exportActivityLogsApi,
  transformActivityLogForDisplay,
  getActivityLogTypeLabel,
  getActivityLogStatusLabel,
  getActivityLogEntityTypeLabel,
} from "./api";

// Query functions
export {
  getActivityLogsTableData,
  getActivityLogsSlim,
  getActivityLog,
  createActivityLog,
  deleteActivityLog,
  bulkDeleteActivityLogs,
  getActivityLogStats,
  exportActivityLogs,
  transformActivityLogForForm,
  getActivityLogsWithFilters,
  getRecentActivityLogs,
} from "./queries";

// React Query hooks
export {
  useActivityLogsData,
  useActivityLogsSlim,
  useActivityLogData,
  useActivityLogStats,
  useRecentActivityLogs,
  useCreateActivityLog,
  useDeleteActivityLog,
  useBulkDeleteActivityLogs,
  useExportActivityLogs,
  useActivityLogsTable,
  useActivityLogDashboard,
  // Legacy hooks
  useActivityLogsSimple,
  useActivityLogDetail,
  // Query keys
  activityLogKeys,
} from "./hooks";

// Validation schemas and types
export {
  createActivityLogSchema,
  getActivityLogsSchema,
  getActivityLogStatsSchema,
  exportActivityLogsSchema,
  activityLogFilterFormSchema,
  // Types
  type CreateActivityLogSchema,
  type GetActivityLogsSchema,
  type GetActivityLogStatsSchema,
  type ExportActivityLogsSchema,
  type ActivityLogFilterFormSchema,
} from "./validations";

// Re-export types for convenience
export type {
  ActivityLogDto,
  CreateActivityLogDto,
  ActivityLogSlimDto,
  ActivityLogTableData,
  ActivityLogPaginatedResponse,
  ActivityLogResponse,
  SimpleActivityLogResponse,
  ActivityLogStatsResponse,
  ActivityLogExportResponse,
  ActivityLogStatsData,
  ActivityLogFilterData,
  ActivityLogExportData,
  ActivityLogFormValues,
  ActivityLogTableDataExtended,
  // Enums
  ActivityLogType,
  ActivityLogStatus,
  ActivityEntityType,
} from "@/types/activity-log";