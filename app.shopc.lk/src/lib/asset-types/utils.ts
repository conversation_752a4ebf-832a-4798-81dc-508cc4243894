import {
  AssetType<PERSON><PERSON>,
  AssetTypeSlimDto,
  AssetTypeStatus,
  AssetTypeCategory,
  AssetTypeReferenceType,
  AssetTypeTableData,
} from "@/types/asset-type";

// Format asset type status for display
export function formatAssetTypeStatus(status: AssetTypeStatus): string {
  switch (status) {
    case AssetTypeStatus.ACTIVE:
      return "Active";
    case AssetTypeStatus.INACTIVE:
      return "Inactive";
    default:
      return "Unknown";
  }
}

// Get status color for badges/indicators
export function getAssetTypeStatusColor(status: AssetTypeStatus): string {
  switch (status) {
    case AssetTypeStatus.ACTIVE:
      return "bg-green-100 text-green-800";
    case AssetTypeStatus.INACTIVE:
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Format asset type category for display
export function formatAssetTypeCategory(category: AssetTypeCategory): string {
  switch (category) {
    case AssetTypeCategory.PHYSICAL:
      return "Physical";
    case AssetTypeCategory.DIGITAL:
      return "Digital";
    default:
      return "Unknown";
  }
}

// Get category color for badges/indicators
export function getAssetTypeCategoryColor(category: AssetTypeCategory): string {
  switch (category) {
    case AssetTypeCategory.PHYSICAL:
      return "bg-blue-100 text-blue-800";
    case AssetTypeCategory.DIGITAL:
      return "bg-purple-100 text-purple-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Format asset type reference type for display
export function formatAssetTypeReferenceType(
  referenceType?: AssetTypeReferenceType
): string {
  if (!referenceType) return "Manual";
  
  switch (referenceType) {
    case AssetTypeReferenceType.RENTAL_ITEM_CATEGORY:
      return "Rental Item Category";
    case AssetTypeReferenceType.VEHICLE_CATEGORY:
      return "Vehicle Category";
    default:
      return "Unknown";
  }
}

// Get reference type color for badges/indicators
export function getAssetTypeReferenceTypeColor(
  referenceType?: AssetTypeReferenceType
): string {
  if (!referenceType) return "bg-gray-100 text-gray-800";
  
  switch (referenceType) {
    case AssetTypeReferenceType.RENTAL_ITEM_CATEGORY:
      return "bg-orange-100 text-orange-800";
    case AssetTypeReferenceType.VEHICLE_CATEGORY:
      return "bg-teal-100 text-teal-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Check if asset type is automatically created from another module
export function isAutoCreatedAssetType(assetType: AssetTypeDto): boolean {
  return !!(assetType.referenceId && assetType.referenceType);
}

// Get asset type display name with hierarchy
export function getAssetTypeDisplayName(
  assetType: AssetTypeDto | AssetTypeTableData,
  parentName?: string
): string {
  const name = assetType.name;
  const parent = parentName || (assetType as AssetTypeTableData).parentName;
  
  if (parent) {
    return `${parent} > ${name}`;
  }
  
  return name;
}

// Build asset type hierarchy tree from flat list
export function buildAssetTypeHierarchy(
  assetTypes: AssetTypeSlimDto[]
): AssetTypeSlimDto[] {
  const assetTypeMap = new Map<string, AssetTypeSlimDto & { children?: AssetTypeSlimDto[] }>();
  const rootAssetTypes: (AssetTypeSlimDto & { children?: AssetTypeSlimDto[] })[] = [];

  // First pass: create a map of all asset types
  assetTypes.forEach((assetType) => {
    assetTypeMap.set(assetType.id, { ...assetType, children: [] });
  });

  // Second pass: build the hierarchy
  assetTypes.forEach((assetType) => {
    const assetTypeWithChildren = assetTypeMap.get(assetType.id)!;
    
    if (assetType.id && assetTypeMap.has(assetType.id)) {
      // This is a child asset type
      const parent = assetTypeMap.get(assetType.id);
      parent?.children?.push(assetTypeWithChildren);
    } else {
      // This is a root asset type
      rootAssetTypes.push(assetTypeWithChildren);
    }
  });

  return rootAssetTypes;
}

// Get all descendant asset type IDs
export function getDescendantAssetTypeIds(
  assetTypeId: string,
  assetTypes: AssetTypeSlimDto[]
): string[] {
  const descendants: string[] = [];
  
  function findDescendants(parentId: string) {
    assetTypes.forEach((assetType) => {
      if (assetType.id === parentId) {
        descendants.push(assetType.id);
        findDescendants(assetType.id);
      }
    });
  }
  
  findDescendants(assetTypeId);
  return descendants;
}

// Check if an asset type can be deleted (no children or assets)
export function canDeleteAssetType(assetType: AssetTypeTableData): boolean {
  return assetType.subcategoriesCount === 0 && assetType.assetsCount === 0;
}

// Get asset type deletion warning message
export function getAssetTypeDeletionWarning(assetType: AssetTypeTableData): string | null {
  if (assetType.subcategoriesCount > 0 && assetType.assetsCount > 0) {
    return `Cannot delete "${assetType.name}" because it has ${assetType.subcategoriesCount} subcategories and ${assetType.assetsCount} assets.`;
  } else if (assetType.subcategoriesCount > 0) {
    return `Cannot delete "${assetType.name}" because it has ${assetType.subcategoriesCount} subcategories.`;
  } else if (assetType.assetsCount > 0) {
    return `Cannot delete "${assetType.name}" because it has ${assetType.assetsCount} assets.`;
  }
  
  return null;
}

// Sort asset types by name (case-insensitive)
export function sortAssetTypesByName(assetTypes: AssetTypeSlimDto[]): AssetTypeSlimDto[] {
  return [...assetTypes].sort((a, b) => 
    a.name.toLowerCase().localeCompare(b.name.toLowerCase())
  );
}

// Filter asset types by status
export function filterAssetTypesByStatus(
  assetTypes: AssetTypeTableData[],
  status: AssetTypeStatus
): AssetTypeTableData[] {
  return assetTypes.filter((assetType) => assetType.status === status);
}

// Filter asset types by category
export function filterAssetTypesByCategory(
  assetTypes: AssetTypeTableData[],
  category: AssetTypeCategory
): AssetTypeTableData[] {
  return assetTypes.filter((assetType) => assetType.category === category);
}

// Get asset type statistics
export function getAssetTypeStatistics(assetTypes: AssetTypeTableData[]) {
  const total = assetTypes.length;
  const active = assetTypes.filter((at) => at.status === AssetTypeStatus.ACTIVE).length;
  const inactive = assetTypes.filter((at) => at.status === AssetTypeStatus.INACTIVE).length;
  const physical = assetTypes.filter((at) => at.category === AssetTypeCategory.PHYSICAL).length;
  const digital = assetTypes.filter((at) => at.category === AssetTypeCategory.DIGITAL).length;
  const autoCreated = assetTypes.filter((at) => at.referenceId && at.referenceType).length;
  const manual = total - autoCreated;
  const totalAssets = assetTypes.reduce((sum, at) => sum + at.assetsCount, 0);
  const totalSubcategories = assetTypes.reduce((sum, at) => sum + at.subcategoriesCount, 0);

  return {
    total,
    active,
    inactive,
    physical,
    digital,
    autoCreated,
    manual,
    totalAssets,
    totalSubcategories,
  };
}

// Validate asset type hierarchy (prevent circular references)
export function validateAssetTypeHierarchy(
  assetTypeId: string,
  parentId: string | null,
  allAssetTypes: AssetTypeSlimDto[]
): { isValid: boolean; error?: string } {
  if (!parentId) {
    return { isValid: true };
  }

  if (assetTypeId === parentId) {
    return { isValid: false, error: "Asset type cannot be its own parent" };
  }

  // Check for circular reference by traversing up the parent chain
  let currentParentId = parentId;
  const visited = new Set<string>([assetTypeId]);

  while (currentParentId) {
    if (visited.has(currentParentId)) {
      return { isValid: false, error: "This would create a circular reference" };
    }

    visited.add(currentParentId);
    const parent = allAssetTypes.find((at) => at.id === currentParentId);
    
    if (!parent) {
      return { isValid: false, error: "Parent asset type not found" };
    }

    // Move to the next parent in the chain
    currentParentId = (parent as any).parentId || null;
  }

  return { isValid: true };
}