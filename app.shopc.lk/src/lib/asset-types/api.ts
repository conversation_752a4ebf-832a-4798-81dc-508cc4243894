import {
  AssetTypeResponse,
  AssetTypePaginatedResponse,
  AssetTypeIdResponse,
  BulkAssetTypeIdsResponse,
  BulkDeleteAssetTypeDto,
  BulkDeleteAssetTypeResponse,
  AssetTypeNameAvailabilityResponse,
  SimpleAssetTypeResponse,
  AssetTypeHierarchyResponse,
  CreateAssetTypeDto,
  UpdateAssetTypeDto,
  BulkCreateAssetTypeDto,
  AssetTypeStatus,
  BulkUpdateAssetTypeHierarchyDto,
  BulkUpdateAssetTypeHierarchyResponse,
  BulkUpdateAssetTypeStatusDto,
  BulkUpdateAssetTypeStatusResponse,
  UpdateAssetTypePositionsDto,
  UpdateAssetTypePositionsResponse,
} from "@/types/asset-type";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetAssetTypesSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new asset type
export async function createAssetTypeApi(
  data: CreateAssetTypeDto
): Promise<AssetTypeIdResponse> {
  try {
    const res = await axios.post("/asset-types", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create asset types
export async function bulkCreateAssetTypesApi(
  assetTypes: BulkCreateAssetTypeDto[]
): Promise<BulkAssetTypeIdsResponse> {
  try {
    const res = await axios.post("/asset-types/bulk", { assetTypes });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk import asset types
export async function bulkImportAssetTypesApi(
  assetTypes: any[]
): Promise<BulkAssetTypeIdsResponse> {
  try {
    const res = await axios.post("/asset-types/import", { assetTypes });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get asset types with pagination and filters
export async function getAssetTypesApi(
  params: GetAssetTypesSchema
): Promise<AssetTypePaginatedResponse> {
  try {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append("page", String(params.page));
    if (params.perPage) searchParams.append("limit", String(params.perPage));
    if (params.name) searchParams.append("name", params.name);
    if (params.status) searchParams.append("status", params.status);
    if (params.from) searchParams.append("from", params.from);
    if (params.to) searchParams.append("to", params.to);
    if (params.filters && params.filters.length > 0) {
      searchParams.append("filters", JSON.stringify(params.filters));
    }
    if (params.joinOperator)
      searchParams.append("joinOperator", params.joinOperator);
    if (params.sort && params.sort.length > 0) {
      searchParams.append("sort", JSON.stringify(params.sort));
    }

    const res = await axios.get(`/asset-types?${searchParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check asset type name availability
export async function checkAssetTypeNameAvailabilityApi(
  name: string
): Promise<AssetTypeNameAvailabilityResponse> {
  try {
    const res = await axios.get(
      `/asset-types/check-name-availability?name=${encodeURIComponent(name)}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get asset types in slim format (for dropdowns/selects)
export async function getAssetTypesSlimApi(): Promise<SimpleAssetTypeResponse> {
  try {
    const res = await axios.get("/asset-types/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single asset type by ID
export async function getAssetTypeApi(id: string): Promise<AssetTypeResponse> {
  try {
    const res = await axios.get(`/asset-types/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update an asset type
export async function updateAssetTypeApi(
  id: string,
  data: UpdateAssetTypeDto
): Promise<AssetTypeIdResponse> {
  try {
    const res = await axios.patch(`/asset-types/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete an asset type
export async function deleteAssetTypeApi(
  id: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  try {
    const res = await axios.delete(`/asset-types/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete asset types
export async function bulkDeleteAssetTypesApi(
  assetTypeIds: string[]
): Promise<BulkDeleteAssetTypeResponse> {
  try {
    const data: BulkDeleteAssetTypeDto = { assetTypeIds };
    const res = await axios.delete("/asset-types/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update asset type status (convenience function)
export async function updateAssetTypeStatusApi(
  id: string,
  status: AssetTypeStatus
): Promise<AssetTypeIdResponse> {
  try {
    const res = await axios.patch(`/asset-types/${id}`, { status });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Batch update asset type positions
export async function updateAssetTypePositionsApi(
  updates: { id: string; position: number }[]
): Promise<UpdateAssetTypePositionsResponse> {
  try {
    const data: UpdateAssetTypePositionsDto = { updates };
    const res = await axios.patch("/asset-types/batch/positions", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to update asset type positions",
      data: null,
    };
  }
}

// Bulk update asset type hierarchy (parent-child relationships)
export async function bulkUpdateAssetTypeHierarchyApi(
  data: BulkUpdateAssetTypeHierarchyDto
): Promise<BulkUpdateAssetTypeHierarchyResponse> {
  try {
    const res = await axios.patch("/asset-types/batch/hierarchy", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message:
        error.response?.data?.message || "Failed to update asset type hierarchy",
      data: null,
    };
  }
}

// Get asset types in hierarchy format (for hierarchy displays)
export async function getAssetTypesHierarchyApi(): Promise<AssetTypeHierarchyResponse> {
  try {
    const res = await axios.get("/asset-types/hierarchy");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk update asset type status
export async function bulkUpdateAssetTypeStatusApi(
  assetTypeIds: string[],
  status: AssetTypeStatus
): Promise<BulkUpdateAssetTypeStatusResponse> {
  try {
    const data: BulkUpdateAssetTypeStatusDto = { assetTypeIds, status };
    const res = await axios.patch("/asset-types/bulk-status", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}