import {
  AssetTypePaginatedResponse,
  AssetTypeIdResponse,
  BulkAssetTypeIdsResponse,
  BulkDeleteAssetTypeResponse,
  AssetTypeNameAvailabilityResponse,
  SimpleAssetTypeResponse,
  AssetTypeResponse,
  CreateAssetTypeDto,
  UpdateAssetTypeDto,
  BulkCreateAssetTypeDto,
  AssetTypeStatus,
  BulkUpdateAssetTypeHierarchyDto,
  BulkUpdateAssetTypeHierarchyResponse,
  BulkUpdateAssetTypeStatusResponse,
  AssetTypeHierarchyResponse,
  UpdateAssetTypePositionsResponse,
} from "@/types/asset-type";
import { ApiResponse } from "@/types/common";
import { GetAssetTypesSchema } from "./validations";

// Real API imports
import {
  createAssetType<PERSON>pi,
  bulkCreateAssetTypesApi,
  bulkImportAssetTypesApi,
  getAssetTypesApi,
  checkAssetTypeNameAvailability<PERSON>pi,
  getAssetTypes<PERSON><PERSON><PERSON><PERSON>,
  get<PERSON>set<PERSON><PERSON><PERSON><PERSON>,
  update<PERSON>set<PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON>set<PERSON><PERSON><PERSON><PERSON>,
  bulkDeleteAssetTypesApi,
  updateAssetTypeStatus<PERSON>pi,
  updateAssetTypePositionsApi,
  bulkUpdateAssetTypeHierarchyApi,
  getAssetTypesHierarchyApi,
  bulkUpdateAssetTypeStatusApi,
} from "./api";

// Demo API imports
import {
  getDemoAssetTypesTableDataApi,
  getDemoAssetTypesSlimApi,
  getDemoAssetTypeApi,
  checkDemoAssetTypeNameAvailabilityApi,
  createDemoAssetTypeApi,
  bulkCreateDemoAssetTypesApi,
  bulkImportDemoAssetTypesApi,
  updateDemoAssetTypeApi,
  deleteDemoAssetTypeApi,
  bulkDeleteDemoAssetTypesApi,
  updateDemoAssetTypeStatusApi,
  updateDemoAssetTypePositionsApi,
  bulkUpdateDemoAssetTypeHierarchyApi,
  getDemoAssetTypesHierarchyApi,
  bulkUpdateDemoAssetTypeStatusApi,
} from "./demo";

// Get asset types table data with pagination and filters
export async function getAssetTypesTableData(
  params: GetAssetTypesSchema,
  isDemo: boolean
): Promise<AssetTypePaginatedResponse> {
  if (isDemo) {
    return await getDemoAssetTypesTableDataApi(params);
  } else {
    return await getAssetTypesApi(params);
  }
}

// Get asset types in slim format (for dropdowns/selects)
export async function getAssetTypesSlim(
  isDemo: boolean
): Promise<SimpleAssetTypeResponse> {
  if (isDemo) {
    return await getDemoAssetTypesSlimApi();
  } else {
    return await getAssetTypesSlimApi();
  }
}

// Get a single asset type by ID
export async function getAssetType(
  id: string,
  isDemo: boolean
): Promise<AssetTypeResponse> {
  if (isDemo) {
    return await getDemoAssetTypeApi(id);
  } else {
    return await getAssetTypeApi(id);
  }
}

// Check asset type name availability
export async function checkAssetTypeNameAvailability(
  name: string,
  isDemo: boolean
): Promise<AssetTypeNameAvailabilityResponse> {
  if (isDemo) {
    return await checkDemoAssetTypeNameAvailabilityApi(name);
  } else {
    return await checkAssetTypeNameAvailabilityApi(name);
  }
}

// Create a new asset type
export async function createAssetType(
  data: CreateAssetTypeDto,
  isDemo: boolean
): Promise<AssetTypeIdResponse> {
  if (isDemo) {
    return await createDemoAssetTypeApi(data);
  } else {
    return await createAssetTypeApi(data);
  }
}

// Bulk create asset types
export async function bulkCreateAssetTypes(
  assetTypes: BulkCreateAssetTypeDto[],
  isDemo: boolean
): Promise<BulkAssetTypeIdsResponse> {
  if (isDemo) {
    return await bulkCreateDemoAssetTypesApi(assetTypes);
  } else {
    return await bulkCreateAssetTypesApi(assetTypes);
  }
}

// Bulk import asset types
export async function bulkImportAssetTypes(
  assetTypes: any[],
  isDemo: boolean
): Promise<BulkAssetTypeIdsResponse> {
  if (isDemo) {
    return await bulkImportDemoAssetTypesApi(assetTypes);
  } else {
    return await bulkImportAssetTypesApi(assetTypes);
  }
}

// Update an asset type
export async function updateAssetType(
  id: string,
  data: UpdateAssetTypeDto,
  isDemo: boolean
): Promise<AssetTypeIdResponse> {
  if (isDemo) {
    return await updateDemoAssetTypeApi(id, data);
  } else {
    return await updateAssetTypeApi(id, data);
  }
}

// Delete an asset type
export async function deleteAssetType(
  id: string,
  isDemo: boolean
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  if (isDemo) {
    return await deleteDemoAssetTypeApi(id);
  } else {
    return await deleteAssetTypeApi(id);
  }
}

// Bulk delete asset types
export async function bulkDeleteAssetTypes(
  assetTypeIds: string[],
  isDemo: boolean
): Promise<BulkDeleteAssetTypeResponse> {
  if (isDemo) {
    return await bulkDeleteDemoAssetTypesApi(assetTypeIds);
  } else {
    return await bulkDeleteAssetTypesApi(assetTypeIds);
  }
}

// Update asset type status (convenience function)
export async function updateAssetTypeStatus(
  id: string,
  status: AssetTypeStatus,
  isDemo: boolean
): Promise<AssetTypeIdResponse> {
  if (isDemo) {
    return await updateDemoAssetTypeApi(id, { status });
  } else {
    return await updateAssetTypeStatusApi(id, status);
  }
}

// Batch update asset type positions
export async function updateAssetTypePositions(
  updates: { id: string; position: number }[],
  isDemo: boolean
): Promise<UpdateAssetTypePositionsResponse> {
  if (isDemo) {
    return await updateDemoAssetTypePositionsApi(updates);
  } else {
    return await updateAssetTypePositionsApi(updates);
  }
}

// Bulk update asset type hierarchy (parent-child relationships)
export async function bulkUpdateAssetTypeHierarchy(
  data: BulkUpdateAssetTypeHierarchyDto,
  isDemo: boolean
): Promise<BulkUpdateAssetTypeHierarchyResponse> {
  if (isDemo) {
    return await bulkUpdateDemoAssetTypeHierarchyApi(data);
  } else {
    return await bulkUpdateAssetTypeHierarchyApi(data);
  }
}

// Get asset types hierarchy data
export async function getAssetTypesHierarchyData(
  isDemo: boolean
): Promise<AssetTypeHierarchyResponse> {
  if (isDemo) {
    return await getDemoAssetTypesHierarchyApi();
  } else {
    return await getAssetTypesHierarchyApi();
  }
}

// Bulk update asset type status
export async function bulkUpdateAssetTypeStatus(
  assetTypeIds: string[],
  status: AssetTypeStatus,
  isDemo: boolean
): Promise<BulkUpdateAssetTypeStatusResponse> {
  if (isDemo) {
    return await bulkUpdateDemoAssetTypeStatusApi(assetTypeIds, status);
  } else {
    return await bulkUpdateAssetTypeStatusApi(assetTypeIds, status);
  }
}

// Utility functions for data transformation
export function transformAssetTypeForForm(assetType: any) {
  return {
    name: assetType.name || "",
    description: assetType.description || "",
    parentId: assetType.parentId || null,
    category: assetType.category || "physical",
    referenceId: assetType.referenceId || undefined,
    referenceType: assetType.referenceType || undefined,
    status: assetType.status || "active",
  };
}