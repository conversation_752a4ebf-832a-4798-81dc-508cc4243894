import {
  AssetTypePaginatedResponse,
  AssetTypeIdResponse,
  BulkAssetTypeIdsResponse,
  BulkDeleteAssetTypeResponse,
  AssetTypeNameAvailabilityResponse,
  SimpleAssetTypeResponse,
  AssetTypeResponse,
  CreateAssetTypeDto,
  UpdateAssetTypeDto,
  BulkCreateAssetTypeDto,
  AssetTypeStatus,
  AssetTypeCategory,
  AssetTypeReferenceType,
  BulkUpdateAssetTypeHierarchyDto,
  BulkUpdateAssetTypeHierarchyResponse,
  BulkUpdateAssetTypeStatusResponse,
  AssetTypeHierarchyResponse,
  UpdateAssetTypePositionsResponse,
  AssetTypeListDto,
} from "@/types/asset-type";
import { ApiStatus } from "@/types/common";
import { GetAssetTypesSchema } from "./validations";

// Mock data for demo purposes
const mockAssetTypes: AssetTypeListDto[] = [
  {
    id: "1",
    name: "Office Equipment",
    description: "Equipment used in office environments",
    status: AssetTypeStatus.ACTIVE,
    parentId: undefined,
    parentName: undefined,
    subcategoriesCount: 3,
    assetsCount: 15,
    createdAt: new Date("2023-01-15"),
    updatedAt: new Date("2023-01-15"),
  },
  {
    id: "2",
    name: "Computers",
    description: "Desktop and laptop computers",
    status: AssetTypeStatus.ACTIVE,
    parentId: "1",
    parentName: "Office Equipment",
    subcategoriesCount: 0,
    assetsCount: 8,
    createdAt: new Date("2023-01-16"),
    updatedAt: new Date("2023-01-16"),
  },
  {
    id: "3",
    name: "Printers",
    description: "Printing devices",
    status: AssetTypeStatus.ACTIVE,
    parentId: "1",
    parentName: "Office Equipment",
    subcategoriesCount: 0,
    assetsCount: 4,
    createdAt: new Date("2023-01-17"),
    updatedAt: new Date("2023-01-17"),
  },
  {
    id: "4",
    name: "Furniture",
    description: "Office furniture items",
    status: AssetTypeStatus.ACTIVE,
    parentId: "1",
    parentName: "Office Equipment",
    subcategoriesCount: 0,
    assetsCount: 3,
    createdAt: new Date("2023-01-18"),
    updatedAt: new Date("2023-01-18"),
  },
  {
    id: "5",
    name: "Software Licenses",
    description: "Digital software licenses",
    status: AssetTypeStatus.ACTIVE,
    parentId: undefined,
    parentName: undefined,
    subcategoriesCount: 2,
    assetsCount: 12,
    createdAt: new Date("2023-01-19"),
    updatedAt: new Date("2023-01-19"),
  },
  {
    id: "6",
    name: "Microsoft Office",
    description: "Microsoft Office suite licenses",
    status: AssetTypeStatus.ACTIVE,
    parentId: "5",
    parentName: "Software Licenses",
    subcategoriesCount: 0,
    assetsCount: 8,
    createdAt: new Date("2023-01-20"),
    updatedAt: new Date("2023-01-20"),
  },
  {
    id: "7",
    name: "Adobe Creative Suite",
    description: "Adobe Creative Suite licenses",
    status: AssetTypeStatus.ACTIVE,
    parentId: "5",
    parentName: "Software Licenses",
    subcategoriesCount: 0,
    assetsCount: 4,
    createdAt: new Date("2023-01-21"),
    updatedAt: new Date("2023-01-21"),
  },
  {
    id: "8",
    name: "Vehicles",
    description: "Company vehicles",
    status: AssetTypeStatus.INACTIVE,
    parentId: undefined,
    parentName: undefined,
    subcategoriesCount: 0,
    assetsCount: 0,
    createdAt: new Date("2023-01-22"),
    updatedAt: new Date("2023-01-22"),
  },
];

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Get demo asset types with pagination and filters
export async function getDemoAssetTypesTableDataApi(
  params: GetAssetTypesSchema
): Promise<AssetTypePaginatedResponse> {
  await delay(500); // Simulate network delay

  try {
    let filteredData = [...mockAssetTypes];

    // Apply name filter
    if (params.name) {
      filteredData = filteredData.filter((assetType) =>
        assetType.name.toLowerCase().includes(params.name!.toLowerCase())
      );
    }

    // Apply status filter
    if (params.status) {
      const statusArray = params.status.split(",").map((s) => s.trim());
      filteredData = filteredData.filter((assetType) =>
        statusArray.includes(assetType.status)
      );
    }

    // Apply date range filters
    if (params.from) {
      const fromDate = new Date(params.from);
      filteredData = filteredData.filter(
        (assetType) => assetType.createdAt >= fromDate
      );
    }

    if (params.to) {
      const toDate = new Date(params.to);
      toDate.setHours(23, 59, 59, 999);
      filteredData = filteredData.filter(
        (assetType) => assetType.createdAt <= toDate
      );
    }

    // Apply sorting
    if (params.sort && params.sort.length > 0) {
      const sortField = params.sort[0];
      filteredData.sort((a, b) => {
        let aValue: any;
        let bValue: any;

        switch (sortField.id) {
          case "name":
            aValue = a.name;
            bValue = b.name;
            break;
          case "createdAt":
            aValue = a.createdAt;
            bValue = b.createdAt;
            break;
          case "updatedAt":
            aValue = a.updatedAt;
            bValue = b.updatedAt;
            break;
          default:
            return 0;
        }

        if (sortField.desc) {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });
    }

    // Apply pagination
    const page = params.page || 1;
    const perPage = params.perPage || 10;
    const start = (page - 1) * perPage;
    const end = start + perPage;
    const paginatedData = filteredData.slice(start, end);

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset types retrieved successfully",
      data: {
        data: paginatedData,
        meta: {
          total: filteredData.length,
          page,
          totalPages: Math.ceil(filteredData.length / perPage),
        },
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve asset types",
      data: null,
    };
  }
}

// Get demo asset types in slim format
export async function getDemoAssetTypesSlimApi(): Promise<SimpleAssetTypeResponse> {
  await delay(300);

  try {
    const slimData = mockAssetTypes
      .filter((assetType) => assetType.status === AssetTypeStatus.ACTIVE)
      .map((assetType) => ({
        id: assetType.id,
        name: assetType.name,
      }));

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset types retrieved successfully",
      data: slimData,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve asset types",
      data: null,
    };
  }
}

// Get demo asset type by ID
export async function getDemoAssetTypeApi(id: string): Promise<AssetTypeResponse> {
  await delay(300);

  try {
    const assetType = mockAssetTypes.find((at) => at.id === id);
    
    if (!assetType) {
      return {
        status: ApiStatus.FAIL,
        message: "Asset type not found",
        data: null,
      };
    }

    // Convert to full DTO format
    const fullAssetType = {
      id: assetType.id,
      businessId: "demo-business-id",
      name: assetType.name,
      description: assetType.description,
      parentId: assetType.parentId,
      category: AssetTypeCategory.PHYSICAL, // Default for demo
      status: assetType.status,
      assetsCount: assetType.assetsCount,
      createdBy: "Demo User",
      updatedBy: "Demo User",
      createdAt: assetType.createdAt,
      updatedAt: assetType.updatedAt,
    };

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset type retrieved successfully",
      data: fullAssetType,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve asset type",
      data: null,
    };
  }
}

// Check demo asset type name availability
export async function checkDemoAssetTypeNameAvailabilityApi(
  name: string
): Promise<AssetTypeNameAvailabilityResponse> {
  await delay(200);

  try {
    const exists = mockAssetTypes.some(
      (assetType) => assetType.name.toLowerCase() === name.toLowerCase()
    );

    return {
      status: ApiStatus.SUCCESS,
      message: "Name availability checked",
      data: { available: !exists },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to check name availability",
      data: null,
    };
  }
}

// Create demo asset type
export async function createDemoAssetTypeApi(
  data: CreateAssetTypeDto
): Promise<AssetTypeIdResponse> {
  await delay(500);

  try {
    const newId = (mockAssetTypes.length + 1).toString();
    
    const newAssetType: AssetTypeListDto = {
      id: newId,
      name: data.name,
      description: data.description,
      status: data.status || AssetTypeStatus.ACTIVE,
      parentId: data.parentId,
      parentName: data.parentId 
        ? mockAssetTypes.find(at => at.id === data.parentId)?.name 
        : undefined,
      subcategoriesCount: 0,
      assetsCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    mockAssetTypes.push(newAssetType);

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset type created successfully",
      data: { id: newId },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to create asset type",
      data: null,
    };
  }
}

// Bulk create demo asset types
export async function bulkCreateDemoAssetTypesApi(
  assetTypes: BulkCreateAssetTypeDto[]
): Promise<BulkAssetTypeIdsResponse> {
  await delay(800);

  try {
    const ids: string[] = [];

    for (const data of assetTypes) {
      const newId = (mockAssetTypes.length + 1).toString();
      
      const newAssetType: AssetTypeListDto = {
        id: newId,
        name: data.name,
        description: data.description,
        status: data.status || AssetTypeStatus.ACTIVE,
        parentId: data.parentId,
        parentName: data.parentId 
          ? mockAssetTypes.find(at => at.id === data.parentId)?.name 
          : undefined,
        subcategoriesCount: 0,
        assetsCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockAssetTypes.push(newAssetType);
      ids.push(newId);
    }

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset types created successfully",
      data: { ids },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to create asset types",
      data: null,
    };
  }
}

// Bulk import demo asset types
export async function bulkImportDemoAssetTypesApi(
  assetTypes: any[]
): Promise<BulkAssetTypeIdsResponse> {
  await delay(800);
  try {
    const ids: string[] = [];
    for (const data of assetTypes) {
      const newId = (mockAssetTypes.length + 1).toString();
      
      const newAssetType: AssetTypeListDto = {
        id: newId,
        name: data.name,
        description: data.description,
        status: data.status || AssetTypeStatus.ACTIVE,
        parentId: data.parentId,
        parentName: data.parentId 
          ? mockAssetTypes.find(at => at.id === data.parentId)?.name 
          : undefined,
        subcategoriesCount: 0,
        assetsCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      mockAssetTypes.push(newAssetType);
      ids.push(newId);
    }
    return {
      status: ApiStatus.SUCCESS,
      message: "Asset types imported successfully",
      data: { ids },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to import asset types",
      data: null,
    };
  }
}

// Update demo asset type
export async function updateDemoAssetTypeApi(
  id: string,
  data: UpdateAssetTypeDto
): Promise<AssetTypeIdResponse> {
  await delay(400);

  try {
    const index = mockAssetTypes.findIndex((assetType) => assetType.id === id);
    
    if (index === -1) {
      return {
        status: ApiStatus.FAIL,
        message: "Asset type not found",
        data: null,
      };
    }

    mockAssetTypes[index] = {
      ...mockAssetTypes[index],
      ...data,
      parentName: data.parentId 
        ? mockAssetTypes.find(at => at.id === data.parentId)?.name 
        : undefined,
      updatedAt: new Date(),
    };

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset type updated successfully",
      data: { id },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update asset type",
      data: null,
    };
  }
}

// Delete demo asset type
export async function deleteDemoAssetTypeApi(
  id: string
): Promise<{ status: string; message: string; data: { success: boolean; message: string } | null }> {
  await delay(400);

  try {
    const index = mockAssetTypes.findIndex((assetType) => assetType.id === id);
    
    if (index === -1) {
      return {
        status: ApiStatus.FAIL,
        message: "Asset type not found",
        data: null,
      };
    }

    const assetType = mockAssetTypes[index];
    
    // Check for subcategories or assets
    if (assetType.subcategoriesCount > 0 || assetType.assetsCount > 0) {
      return {
        status: ApiStatus.FAIL,
        message: "Cannot delete asset type with subcategories or assets",
        data: null,
      };
    }

    mockAssetTypes.splice(index, 1);

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset type deleted successfully",
      data: {
        success: true,
        message: `Asset type "${assetType.name}" has been deleted`,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to delete asset type",
      data: null,
    };
  }
}

// Bulk delete demo asset types
export async function bulkDeleteDemoAssetTypesApi(
  assetTypeIds: string[]
): Promise<BulkDeleteAssetTypeResponse> {
  await delay(600);

  try {
    const deletedIds: string[] = [];
    
    for (const id of assetTypeIds) {
      const index = mockAssetTypes.findIndex((assetType) => assetType.id === id);
      
      if (index !== -1) {
        const assetType = mockAssetTypes[index];
        
        // Only delete if no subcategories or assets
        if (assetType.subcategoriesCount === 0 && assetType.assetsCount === 0) {
          mockAssetTypes.splice(index, 1);
          deletedIds.push(id);
        }
      }
    }

    return {
      status: ApiStatus.SUCCESS,
      message: "Asset types deleted successfully",
      data: {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} asset types`,
        deletedIds,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to delete asset types",
      data: null,
    };
  }
}

// Update demo asset type status
export async function updateDemoAssetTypeStatusApi(
  id: string,
  status: AssetTypeStatus
): Promise<AssetTypeIdResponse> {
  return updateDemoAssetTypeApi(id, { status });
}

// Update demo asset type positions
export async function updateDemoAssetTypePositionsApi(
  updates: { id: string; position: number }[]
): Promise<UpdateAssetTypePositionsResponse> {
  await delay(400);

  try {
    // In a real implementation, this would update the position field
    // For demo purposes, we'll just simulate success
    return {
      status: ApiStatus.SUCCESS,
      message: "Positions updated successfully",
      data: {
        updated: updates.length,
        message: `Successfully updated ${updates.length} asset type positions`,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update positions",
      data: null,
    };
  }
}

// Bulk update demo asset type hierarchy
export async function bulkUpdateDemoAssetTypeHierarchyApi(
  data: BulkUpdateAssetTypeHierarchyDto
): Promise<BulkUpdateAssetTypeHierarchyResponse> {
  await delay(500);

  try {
    let updated = 0;
    const failed: Array<{ id: string; error: string }> = [];

    for (const update of data.updates) {
      const index = mockAssetTypes.findIndex((at) => at.id === update.id);
      
      if (index !== -1) {
        mockAssetTypes[index].parentId = update.parentId;
        mockAssetTypes[index].parentName = update.parentId 
          ? mockAssetTypes.find(at => at.id === update.parentId)?.name 
          : undefined;
        updated++;
      } else {
        failed.push({ id: update.id, error: "Asset type not found" });
      }
    }

    return {
      status: ApiStatus.SUCCESS,
      message: "Hierarchy updated successfully",
      data: { updated, failed },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update hierarchy",
      data: null,
    };
  }
}

// Get demo asset types hierarchy
export async function getDemoAssetTypesHierarchyApi(): Promise<AssetTypeHierarchyResponse> {
  await delay(300);

  try {
    const hierarchyData = mockAssetTypes
      .filter((assetType) => assetType.status === AssetTypeStatus.ACTIVE)
      .map((assetType) => ({
        id: assetType.id,
        name: assetType.name,
        parentId: assetType.parentId,
      }));

    return {
      status: ApiStatus.SUCCESS,
      message: "Hierarchy data retrieved successfully",
      data: hierarchyData,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve hierarchy data",
      data: null,
    };
  }
}

// Bulk update demo asset type status
export async function bulkUpdateDemoAssetTypeStatusApi(
  assetTypeIds: string[],
  status: AssetTypeStatus
): Promise<BulkUpdateAssetTypeStatusResponse> {
  await delay(500);

  try {
    const updatedIds: string[] = [];
    const failed: Array<{ assetTypeId: string; error: string }> = [];

    for (const id of assetTypeIds) {
      const index = mockAssetTypes.findIndex((at) => at.id === id);
      
      if (index !== -1) {
        if (mockAssetTypes[index].status !== status) {
          mockAssetTypes[index].status = status;
          mockAssetTypes[index].updatedAt = new Date();
          updatedIds.push(id);
        } else {
          failed.push({
            assetTypeId: id,
            error: `Asset type already has status: ${status}`,
          });
        }
      } else {
        failed.push({
          assetTypeId: id,
          error: "Asset type not found",
        });
      }
    }

    return {
      status: ApiStatus.SUCCESS,
      message: "Status updated successfully",
      data: {
        updated: updatedIds.length,
        message: `Successfully updated status for ${updatedIds.length} asset types`,
        updatedIds,
        failed: failed.length > 0 ? failed : undefined,
      },
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to update status",
      data: null,
    };
  }
}