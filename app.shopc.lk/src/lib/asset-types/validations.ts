import { z } from "zod";
import { AssetTypeStatus, AssetTypeCategory, AssetTypeReferenceType } from "@/types/asset-type";

// Backend DTO: CreateAssetTypeDto
export const createAssetTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  parentId: z.string().uuid("Invalid parent ID").nullable().optional(),
  category: z.enum([AssetTypeCategory.PHYSICAL, AssetTypeCategory.DIGITAL]),
  referenceId: z.string().uuid("Invalid reference ID").optional(),
  referenceType: z
    .enum([
      AssetTypeReferenceType.RENTAL_ITEM_CATEGORY,
      AssetTypeReferenceType.VEHICLE_CATEGORY,
    ])
    .optional(),
  status: z
    .enum([AssetTypeStatus.ACTIVE, AssetTypeStatus.INACTIVE])
    .optional()
    .default(AssetTypeStatus.ACTIVE),
});

// Backend DTO: UpdateAssetTypeDto
export const updateAssetTypeSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  description: z.string().optional(),
  parentId: z.string().uuid("Invalid parent ID").nullable().optional(),
  category: z
    .enum([AssetTypeCategory.PHYSICAL, AssetTypeCategory.DIGITAL])
    .optional(),
  referenceId: z.string().uuid("Invalid reference ID").optional(),
  referenceType: z
    .enum([
      AssetTypeReferenceType.RENTAL_ITEM_CATEGORY,
      AssetTypeReferenceType.VEHICLE_CATEGORY,
    ])
    .optional(),
  status: z
    .enum([AssetTypeStatus.ACTIVE, AssetTypeStatus.INACTIVE])
    .optional(),
});

// Import asset type schema (for bulk import operations)
export const importAssetTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  parentId: z.string().uuid("Invalid parent ID").optional(),
  category: z.enum([AssetTypeCategory.PHYSICAL, AssetTypeCategory.DIGITAL]).optional().default(AssetTypeCategory.PHYSICAL),
  referenceId: z.string().optional(),
  referenceType: z
    .enum([
      AssetTypeReferenceType.RENTAL_ITEM_CATEGORY,
      AssetTypeReferenceType.VEHICLE_CATEGORY,
    ])
    .optional(),
  status: z
    .enum([AssetTypeStatus.ACTIVE, AssetTypeStatus.INACTIVE])
    .optional()
    .default(AssetTypeStatus.ACTIVE),
});

// Bulk import asset types schema
export const bulkImportAssetTypesSchema = z.object({
  assetTypes: z.array(importAssetTypeSchema).min(1, "At least one asset type is required"),
});

// Bulk create asset types schema
export const bulkCreateAssetTypesSchema = z.object({
  assetTypes: z.array(createAssetTypeSchema).min(1, "At least one asset type is required"),
});

// Query parameters for getting asset types
export const getAssetTypesSchema = z.object({
  page: z.number().min(1).optional().default(1),
  perPage: z.number().min(1).max(100).optional().default(10),
  name: z.string().optional(),
  status: z.string().optional(), // Comma-separated status values
  from: z.string().optional(), // Date string in YYYY-MM-DD format
  to: z.string().optional(), // Date string in YYYY-MM-DD format
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z.enum([
          "eq",
          "ne",
          "iLike",
          "notILike",
          "isEmpty",
          "isNotEmpty",
        ]),
        type: z.enum(["text", "select", "date"]),
        rowId: z.string(),
      })
    )
    .optional(),
  joinOperator: z.enum(["and", "or"]).optional().default("and"),
  sort: z
    .array(
      z.object({
        id: z.enum(["name", "createdAt", "updatedAt"]),
        desc: z.boolean().optional().default(false),
      })
    )
    .optional(),
});

// Bulk delete asset types schema
export const bulkDeleteAssetTypesSchema = z.object({
  assetTypeIds: z.array(z.string().uuid("Invalid asset type ID")).min(1, "At least one asset type ID is required"),
});

// Bulk update asset type status schema
export const bulkUpdateAssetTypeStatusSchema = z.object({
  assetTypeIds: z.array(z.string().uuid("Invalid asset type ID")).min(1, "At least one asset type ID is required"),
  status: z.enum([AssetTypeStatus.ACTIVE, AssetTypeStatus.INACTIVE]),
});

// Bulk update asset type hierarchy schema
export const bulkUpdateAssetTypeHierarchySchema = z.object({
  updates: z
    .array(
      z.object({
        id: z.string().uuid("Invalid asset type ID"),
        parentId: z.string().uuid("Invalid parent ID").nullable(),
      })
    )
    .min(1, "At least one update is required"),
});

// Update asset type positions schema
export const updateAssetTypePositionsSchema = z.object({
  updates: z
    .array(
      z.object({
        id: z.string().uuid("Invalid asset type ID"),
        position: z.number().min(1, "Position must be a positive integer"),
      })
    )
    .min(1, "At least one position update is required"),
});

// Asset type form values schema (for UI validation)
export const assetTypeFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  parentId: z.string().uuid("Invalid parent ID").nullable().optional(),
  category: z.enum([AssetTypeCategory.PHYSICAL, AssetTypeCategory.DIGITAL]),
  status: z
    .enum([AssetTypeStatus.ACTIVE, AssetTypeStatus.INACTIVE])
    .optional()
    .default(AssetTypeStatus.ACTIVE),
});

// Type exports for TypeScript usage
export type CreateAssetTypeSchema = z.infer<typeof createAssetTypeSchema>;
export type UpdateAssetTypeSchema = z.infer<typeof updateAssetTypeSchema>;
export type ImportAssetTypeSchema = z.infer<typeof importAssetTypeSchema>;
export type BulkImportAssetTypesSchema = z.infer<typeof bulkImportAssetTypesSchema>;
export type BulkCreateAssetTypesSchema = z.infer<typeof bulkCreateAssetTypesSchema>;
export type GetAssetTypesSchema = z.infer<typeof getAssetTypesSchema>;
export type BulkDeleteAssetTypesSchema = z.infer<typeof bulkDeleteAssetTypesSchema>;
export type BulkUpdateAssetTypeStatusSchema = z.infer<typeof bulkUpdateAssetTypeStatusSchema>;
export type BulkUpdateAssetTypeHierarchySchema = z.infer<typeof bulkUpdateAssetTypeHierarchySchema>;
export type UpdateAssetTypePositionsSchema = z.infer<typeof updateAssetTypePositionsSchema>;
export type AssetTypeFormSchema = z.infer<typeof assetTypeFormSchema>;