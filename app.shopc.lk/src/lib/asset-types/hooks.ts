import {
  useQuery,
  useMutation,
  useQuery<PERSON>lient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  AssetTypePaginatedResponse,
  AssetTypeIdResponse,
  BulkAssetTypeIdsResponse,
  BulkDeleteAssetTypeResponse,
  AssetTypeNameAvailabilityResponse,
  SimpleAssetTypeResponse,
  AssetTypeResponse,
  CreateAssetTypeDto,
  UpdateAssetTypeDto,
  BulkCreateAssetTypeDto,
  AssetTypeStatus,
  AssetTypeHierarchyResponse,
  BulkUpdateAssetTypeHierarchyResponse,
  BulkUpdateAssetTypeStatusResponse,
  UpdateAssetTypePositionsResponse,
} from "@/types/asset-type";
import { ApiResponse } from "@/types/common";
import { GetAssetTypesSchema } from "./validations";
import {
  getAssetTypesTableData,
  getAssetTypesSlim,
  getAssetType,
  checkAssetTypeNameAvailability,
  createAssetType,
  bulkCreateAssetTypes,
  bulkImportAssetTypes,
  updateAssetType,
  deleteAssetType,
  bulkDeleteAssetTypes,
  updateAssetTypeStatus,
  getAssetTypesHierarchyData,
  bulkUpdateAssetTypeHierarchy,
  bulkUpdateAssetTypeStatus,
  updateAssetTypePositions,
} from "./queries";

// Query keys for cache management
export const assetTypeKeys = {
  all: ["asset-types"] as const,
  list: () => [...assetTypeKeys.all, "list"] as const,
  filtered: (params: GetAssetTypesSchema & { isDemo?: boolean }) =>
    [...assetTypeKeys.list(), params] as const,
  simple: () => [...assetTypeKeys.all, "simple"] as const,
  detail: (id: string) => [...assetTypeKeys.all, "detail", id] as const,
  nameAvailability: (name: string) =>
    [...assetTypeKeys.all, "name-availability", name] as const,
  hierarchy: (isDemo: boolean) =>
    [...assetTypeKeys.all, "hierarchy", isDemo] as const,
};

// Hook to get asset types with pagination and filters
export function useAssetTypesData(
  params: GetAssetTypesSchema,
  isDemo: boolean = false
): UseQueryResult<AssetTypePaginatedResponse> {
  return useQuery({
    queryKey: assetTypeKeys.filtered({ ...params, isDemo }),
    queryFn: () => getAssetTypesTableData(params, isDemo),
    staleTime: 1000 * 60 * 5, // Always fetch fresh data when params change
    refetchOnMount: true, // Ensure it fetches on mount
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    placeholderData: (previousData) => previousData, // Keep previous data while loading new data
  });
}

// Hook to get asset types in slim format (for dropdowns)
export function useAssetTypesSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleAssetTypeResponse> {
  return useQuery({
    queryKey: [...assetTypeKeys.simple(), { isDemo }],
    queryFn: () => getAssetTypesSlim(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get a single asset type by ID
export function useAssetTypeData(
  id: string,
  isDemo: boolean = false
): UseQueryResult<AssetTypeResponse> {
  return useQuery({
    queryKey: [...assetTypeKeys.detail(id), { isDemo }],
    queryFn: () => getAssetType(id, isDemo),
    enabled: !!id,
  });
}

// Hook to check asset type name availability
export function useAssetTypeNameAvailability(
  name: string,
  isDemo: boolean = false
): UseQueryResult<AssetTypeNameAvailabilityResponse> {
  return useQuery({
    queryKey: [...assetTypeKeys.nameAvailability(name), { isDemo }],
    queryFn: () => checkAssetTypeNameAvailability(name, isDemo),
    enabled: !!name && name.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Hook for fetching asset types hierarchy data
export function useAssetTypesHierarchyData(
  isDemo: boolean = false
): UseQueryResult<AssetTypeHierarchyResponse> {
  return useQuery({
    queryKey: assetTypeKeys.hierarchy(isDemo),
    queryFn: () => getAssetTypesHierarchyData(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnMount: true,
  });
}

// Mutation hook to create an asset type
export function useCreateAssetType(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAssetTypeDto) => createAssetType(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset type queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.all });
    },
  });
}

// Mutation hook to bulk create asset types
export function useBulkCreateAssetTypes(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (assetTypes: BulkCreateAssetTypeDto[]) =>
      bulkCreateAssetTypes(assetTypes, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset type queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.all });
    },
  });
}

// Mutation hook to bulk import asset types
export function useBulkImportAssetTypes(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ assetTypes }: { assetTypes: any[] }) =>
      bulkImportAssetTypes(assetTypes, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset type queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.all });
    },
  });
}

// Mutation hook to update an asset type
export function useUpdateAssetType(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAssetTypeDto }) =>
      updateAssetType(id, data, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate specific asset type and list queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.simple() });
    },
  });
}

// Mutation hook to update asset type status
export function useUpdateAssetTypeStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: AssetTypeStatus }) =>
      updateAssetTypeStatus(id, status, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate specific asset type and list queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
    },
  });
}

// Mutation hook to delete an asset type
export function useDeleteAssetType(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteAssetType(id, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset type queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.all });
    },
  });
}

// Mutation hook to bulk delete asset types
export function useBulkDeleteAssetTypes(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (assetTypeIds: string[]) =>
      bulkDeleteAssetTypes(assetTypeIds, isDemo),
    onSuccess: () => {
      // Invalidate and refetch asset type queries
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.all });
    },
  });
}

// Mutation hook to bulk update asset type status
export function useBulkUpdateAssetTypeStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      assetTypeIds,
      status,
    }: {
      assetTypeIds: string[];
      status: AssetTypeStatus;
    }) => bulkUpdateAssetTypeStatus(assetTypeIds, status, isDemo),
    onSuccess: (_, { assetTypeIds }) => {
      // Invalidate specific asset type and list queries
      assetTypeIds.forEach((id) => {
        queryClient.invalidateQueries({ queryKey: assetTypeKeys.detail(id) });
      });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
    },
  });
}

// Mutation hook to bulk update asset type hierarchy
export function useBulkUpdateAssetTypeHierarchy(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: { id: string; parentId: string | null }[]) =>
      bulkUpdateAssetTypeHierarchy({ updates }, isDemo),
    onSuccess: (_, updates) => {
      // Invalidate specific asset type and list queries
      updates.forEach(({ id }) => {
        queryClient.invalidateQueries({ queryKey: assetTypeKeys.detail(id) });
      });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.simple() });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.hierarchy(false) });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.hierarchy(true) });
    },
  });
}

// Mutation hook to update asset type positions
export function useUpdateAssetTypePositions(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: { id: string; position: number }[]) =>
      updateAssetTypePositions(updates, isDemo),
    onSuccess: (_, updates) => {
      // Invalidate specific asset type and list queries
      updates.forEach(({ id }) => {
        queryClient.invalidateQueries({ queryKey: assetTypeKeys.detail(id) });
      });
      queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
    },
  });
}

// Custom hook for asset type table with built-in refresh capability
export function useAssetTypesTable(
  params: GetAssetTypesSchema,
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();
  const query = useAssetTypesData(params, isDemo);

  const refresh = () => {
    queryClient.invalidateQueries({
      queryKey: assetTypeKeys.filtered({ ...params, isDemo }),
    });
  };

  return {
    ...query,
    refresh,
  };
}

// Custom hook for asset type form with name availability check
export function useAssetTypeForm(isDemo: boolean = false) {
  const createMutation = useCreateAssetType(isDemo);
  const updateMutation = useUpdateAssetType(isDemo);

  return {
    createAssetType: createMutation,
    updateAssetType: updateMutation,
    isLoading: createMutation.isPending || updateMutation.isPending,
  };
}

// Legacy hooks for backward compatibility
export const useAssetTypesSimple = useAssetTypesSlim;
export const useAssetTypeDetail = useAssetTypeData;