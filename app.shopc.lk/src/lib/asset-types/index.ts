// Export all types
export * from "@/types/asset-type";

// Export API functions
export * from "./api";

// Export demo functions
export * from "./demo";

// Export hooks
export * from "./hooks";

// Export queries
export * from "./queries";

// Export utilities
export * from "./utils";

// Export validations
export * from "./validations";

// Re-export commonly used items for convenience
export {
  // Hooks
  useAssetTypesData,
  useAssetTypesSlim,
  useAssetTypeData,
  useAssetTypeNameAvailability,
  useAssetTypesHierarchyData,
  useCreateAssetType,
  useBulkCreateAssetTypes,
  useUpdateAssetType,
  useUpdateAssetTypeStatus,
  useDeleteAssetType,
  useBulkDeleteAssetTypes,
  useBulkUpdateAssetTypeStatus,
  useBulkUpdateAssetTypeHierarchy,
  useUpdateAssetTypePositions,
  useAssetTypesTable,
  useAssetTypeForm,
  // Query keys
  assetTypeKeys,
} from "./hooks";

export {
  // Query functions
  getAssetTypesTableData,
  getAssetTypesSlim,
  getAssetType,
  checkAssetTypeNameAvailability,
  createAssetType,
  bulkCreateAssetTypes,
  updateAssetType,
  deleteAssetType,
  bulkDeleteAssetTypes,
  updateAssetTypeStatus,
  updateAssetTypePositions,
  bulkUpdateAssetTypeHierarchy,
  getAssetTypesHierarchyData,
  bulkUpdateAssetTypeStatus,
  transformAssetTypeForForm,
} from "./queries";

export {
  // Utility functions
  formatAssetTypeStatus,
  getAssetTypeStatusColor,
  formatAssetTypeCategory,
  getAssetTypeCategoryColor,
  formatAssetTypeReferenceType,
  getAssetTypeReferenceTypeColor,
  isAutoCreatedAssetType,
  getAssetTypeDisplayName,
  buildAssetTypeHierarchy,
  getDescendantAssetTypeIds,
  canDeleteAssetType,
  getAssetTypeDeletionWarning,
  sortAssetTypesByName,
  filterAssetTypesByStatus,
  filterAssetTypesByCategory,
  getAssetTypeStatistics,
  validateAssetTypeHierarchy,
} from "./utils";

export {
  // Validation schemas
  createAssetTypeSchema,
  updateAssetTypeSchema,
  bulkCreateAssetTypesSchema,
  getAssetTypesSchema,
  bulkDeleteAssetTypesSchema,
  bulkUpdateAssetTypeStatusSchema,
  bulkUpdateAssetTypeHierarchySchema,
  updateAssetTypePositionsSchema,
  assetTypeFormSchema,
  // Schema types
  type CreateAssetTypeSchema,
  type UpdateAssetTypeSchema,
  type BulkCreateAssetTypesSchema,
  type GetAssetTypesSchema,
  type BulkDeleteAssetTypesSchema,
  type BulkUpdateAssetTypeStatusSchema,
  type BulkUpdateAssetTypeHierarchySchema,
  type UpdateAssetTypePositionsSchema,
  type AssetTypeFormSchema,
} from "./validations";

// Legacy aliases for backward compatibility
export {
  useAssetTypesSimple,
  useAssetTypeDetail,
} from "./hooks";