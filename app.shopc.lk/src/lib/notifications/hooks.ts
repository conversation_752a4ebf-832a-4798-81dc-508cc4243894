"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { notificationKeys } from "./queries";
import {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  type Notification,
} from "./demo";
import {
  getNotificationsApi,
  markNotificationAsRead<PERSON>pi,
  markAllNotificationsAsReadApi,
} from "./api";
import { ApiStatus } from "@/types/common";
import { toast } from "@/hooks/use-toast";

async function handleNotificationsResponse(
  promise: ReturnType<typeof getNotifications | typeof getNotificationsApi>
) {
  const response = await promise;
  if (response.status === ApiStatus.FAIL) {
    toast({
      variant: "destructive",
      title: "Error",
      description: response.message || "Failed to fetch notifications",
    });
    return [];
  }
  return response.data || [];
}

export function useNotifications(isDemo: boolean = false) {
  return useQuery({
    queryKey: notificationKeys.lists(),
    queryFn: () =>
      handleNotificationsResponse(
        isDemo ? getNotifications() : getNotificationsApi()
      ),
  });
}

async function handleMarkAsReadResponse(
  promise: ReturnType<
    typeof markNotificationAsRead | typeof markNotificationAsReadApi
  >
) {
  const response = await promise;
  if (response.status === ApiStatus.FAIL) {
    const errorMessage =
      response.message || "Failed to mark notification as read";
    toast({
      variant: "destructive",
      title: "Error",
      description: errorMessage,
    });
    throw new Error(errorMessage);
  }
  return response.data!;
}

export function useMarkNotificationAsRead(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number) =>
      handleMarkAsReadResponse(
        isDemo ? markNotificationAsRead(id) : markNotificationAsReadApi(id)
      ),
    onSuccess: (updatedNotification) => {
      queryClient.setQueryData<Notification[]>(
        notificationKeys.lists(),
        (oldData) => {
          if (!oldData) return [];
          return oldData.map((notification) =>
            notification.id === updatedNotification.id
              ? updatedNotification
              : notification
          );
        }
      );
    },
  });
}

async function handleMarkAllAsReadResponse(
  promise: ReturnType<
    typeof markAllNotificationsAsRead | typeof markAllNotificationsAsReadApi
  >
) {
  const response = await promise;
  if (response.status === ApiStatus.FAIL) {
    const errorMessage =
      response.message || "Failed to mark all notifications as read";
    toast({
      variant: "destructive",
      title: "Error",
      description: errorMessage,
    });
    throw new Error(errorMessage);
  }
  return response.data!;
}

export function useMarkAllNotificationsAsRead(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () =>
      handleMarkAllAsReadResponse(
        isDemo ? markAllNotificationsAsRead() : markAllNotificationsAsReadApi()
      ),
    onSuccess: (updatedNotifications) => {
      queryClient.setQueryData(notificationKeys.lists(), updatedNotifications);
    },
  });
}
