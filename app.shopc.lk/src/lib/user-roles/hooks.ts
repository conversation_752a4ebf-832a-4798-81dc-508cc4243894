import { useState } from "react";
import {
  useUserRoles,
  useCreateUserRole,
  useUpdateUserRole,
  useDeleteUserRole,
} from "./queries";
import { toast } from "@/hooks/use-toast";
import { z } from "zod";
import { createUserRoleSchema } from "./validations";
import { UserRoleTableData } from "@/types/user-role";
import { useQuery } from "@tanstack/react-query";
import { getAllUserRoles, getUserRoleById } from "./api";
import { SearchParams } from "@/types";

export function useUserRolesTable(isDemo: boolean = false) {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState("");
  const [sort, setSort] = useState<string | undefined>(undefined);
  const [order, setOrder] = useState<"asc" | "desc" | undefined>(undefined);

  const queryParams = {
    page,
    limit,
    search: search || undefined,
    sort,
    order,
    isDemo,
  };

  const { data, isLoading, isError, refetch } = useUserRoles(queryParams);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1);
  };

  const handleSearchChange = (newSearch: string) => {
    setSearch(newSearch);
    setPage(1);
  };

  const handleSortChange = (newSort: string, newOrder: "asc" | "desc") => {
    setSort(newSort);
    setOrder(newOrder);
  };

  return {
    roles: data?.data?.data || [],
    total: data?.data?.total || 0,
    page,
    limit,
    search,
    sort,
    order,
    isLoading,
    isError,
    refetch,
    handlePageChange,
    handleLimitChange,
    handleSearchChange,
    handleSortChange,
  };
}

export function useUserRoleActions(isDemo: boolean = false) {
  const createMutation = useCreateUserRole(isDemo);
  const updateMutation = useUpdateUserRole(isDemo);
  const deleteMutation = useDeleteUserRole(isDemo);

  const createUserRole = async (data: z.infer<typeof createUserRoleSchema>) => {
    try {
      const response = await createMutation.mutateAsync(data);
      if (response.status === "SUCCESS") {
        toast({
          title: "Success",
          description: "User role created successfully",
        });
        return response.data as UserRoleTableData;
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to create user role",
        });
        return null;
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while creating the user role",
      });
      return null;
    }
  };

  const updateUserRole = async (
    id: string,
    data: z.infer<typeof createUserRoleSchema>
  ) => {
    try {
      const response = await updateMutation.mutateAsync({ id, ...data });
      if (response.status === "SUCCESS") {
        toast({
          title: "Success",
          description: "User role updated successfully",
        });
        return true;
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to update user role",
        });
        return false;
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while updating the user role",
      });
      return false;
    }
  };

  const deleteUserRole = async (id: string) => {
    try {
      const response = await deleteMutation.mutateAsync(id);
      if (response.status === "SUCCESS") {
        toast({
          title: "Success",
          description: "User role deleted successfully",
        });
        return true;
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to delete user role",
        });
        return false;
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "An error occurred while deleting the user role",
      });
      return false;
    }
  };

  return {
    createUserRole,
    updateUserRole,
    deleteUserRole,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
  };
}

export function getUserRolesTableData(
  searchParamsValues: SearchParams & { limit: number }
): Promise<{
  data: UserRoleTableData[];
  total: number;
}> {
  const params = {
    ...searchParamsValues,
    limit: searchParamsValues.limit.toString(),
  };

  return getAllUserRoles(params as any).then(
    (res: { data: UserRoleTableData[]; total: number }) => {
      return {
        data: res.data,
        total: res.total,
      };
    }
  );
}

export function useUserRolesData(
  searchParamsValues: SearchParams & { limit: number }
) {
  return useQuery({
    queryKey: ["user-roles", searchParamsValues],
    queryFn: () => getUserRolesTableData(searchParamsValues),
  });
}

export function useUserRoleById(id?: string) {
  return useQuery({
    queryKey: ["user-role", id],
    queryFn: () => getUserRoleById(id as string),
    enabled: !!id,
  });
}
