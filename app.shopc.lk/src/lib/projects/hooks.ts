import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import {
  ProjectDto,
  ProjectSlimDto,
  ProjectListDto,
  CreateProjectDto,
  UpdateProjectDto,
  BulkCreateProjectDto,
  ProjectStatus,
  ProjectAutocompleteResponse,
} from "@/types/project";
import { ApiStatus } from "@/types/common";
import { GetProjectsSchema } from "./validations";
import {
  createProject,
  updateProject,
  deleteProject,
  getProject,
  getProjects,
  getProjectsSlim,
  getProjectsTableData,
  bulkCreateProjects,
  bulkDeleteProjects,
  checkProjectNameAvailability,
  updateProjectStatus,
  searchProjectsAutocomplete,
} from "./queries";

// Query keys
export const projectKeys = {
  all: ["projects"] as const,
  lists: () => [...projectKeys.all, "list"] as const,
  list: (filters: GetProjectsSchema) =>
    [...projectKeys.lists(), filters] as const,
  details: () => [...projectKeys.all, "detail"] as const,
  detail: (id: string) => [...projectKeys.details(), id] as const,
  slim: () => [...projectKeys.all, "slim"] as const,
  nameCheck: (name: string) => [...projectKeys.all, "nameCheck", name] as const,
};

// Get projects with pagination and filters
export function useProjects(params: GetProjectsSchema = {}) {
  return useQuery({
    queryKey: projectKeys.list(params),
    queryFn: () => getProjects(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get project by ID
export function useProject(id: string, enabled = true) {
  return useQuery({
    queryKey: projectKeys.detail(id),
    queryFn: () => getProject(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get project data (alias for useProject for consistency with categories)
export function useProjectData(id: string, isDemo: boolean = false) {
  return useProject(id, !!id);
}

// Get projects table data with pagination and filters
export function useProjectsTableData(params: any, isDemo: boolean = false) {
  return useQuery({
    queryKey: projectKeys.list(params),
    queryFn: () => getProjectsTableData(params, isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Bulk import projects with attachments
export function useBulkImportProjectsWithAttachments(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      projects,
      attachments,
    }: {
      projects: any[];
      attachments: File[];
    }) => {
      // For now, use the regular bulk create function
      // In a real implementation, you would have a specific import endpoint that handles attachments
      const result = await bulkCreateProjects({ projects });
      return result;
    },
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        const count = response.data?.ids?.length || 0;
        toast({
          title: "Success",
          description: `Successfully imported ${count} projects`,
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to import projects",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error("Failed to import projects:", error);
      toast({
        title: "Error",
        description: "Failed to import projects",
        variant: "destructive",
      });
    },
  });
}

// Get projects in slim format
export function useProjectsSlim() {
  return useQuery({
    queryKey: projectKeys.slim(),
    queryFn: getProjectsSlim,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Check project name availability
export function useProjectNameAvailability(name: string, enabled = true) {
  return useQuery({
    queryKey: projectKeys.nameCheck(name),
    queryFn: () => checkProjectNameAvailability(name),
    enabled: enabled && !!name && name.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Create project mutation
export function useCreateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      data,
      attachments,
    }: {
      data: CreateProjectDto;
      attachments?: File[];
    }) => createProject(data, attachments),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Project created successfully",
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create project",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create project",
        variant: "destructive",
      });
    },
  });
}

// Update project mutation
export function useUpdateProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
      attachments,
    }: {
      id: string;
      data: UpdateProjectDto;
      attachments?: File[];
    }) => updateProject(id, data, attachments),
    onSuccess: (response, { id }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Project updated successfully",
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.detail(id) });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update project",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update project",
        variant: "destructive",
      });
    },
  });
}

// Delete project mutation
export function useDeleteProject() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteProject,
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Project deleted successfully",
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete project",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete project",
        variant: "destructive",
      });
    },
  });
}

// Bulk create projects mutation
export function useBulkCreateProjects() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      data,
      attachments,
    }: {
      data: BulkCreateProjectDto;
      attachments?: File[];
    }) => bulkCreateProjects(data, attachments),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        const count = response.data?.ids?.length || 0;
        toast({
          title: "Success",
          description: `${count} projects created successfully`,
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create projects",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create projects",
        variant: "destructive",
      });
    },
  });
}

// Bulk delete projects mutation
export function useBulkDeleteProjects() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: bulkDeleteProjects,
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        const count = response.data?.deletedIds?.length || 0;
        toast({
          title: "Success",
          description: `${count} projects deleted successfully`,
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete projects",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete projects",
        variant: "destructive",
      });
    },
  });
}

// Update project status mutation
export function useUpdateProjectStatus() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: ProjectStatus }) =>
      updateProjectStatus(id, status),
    onSuccess: (response, { id }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Project status updated successfully",
        });
        queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
        queryClient.invalidateQueries({ queryKey: projectKeys.detail(id) });
        queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update project status",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update project status",
        variant: "destructive",
      });
    },
  });
}

// Bulk update project status
export function useBulkUpdateProjectStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      projectIds,
      status,
    }: {
      projectIds: string[];
      status: ProjectStatus;
    }) => {
      // For now, update each project individually
      // In a real implementation, you might have a bulk API endpoint
      const results = await Promise.all(
        projectIds.map(async (id) => {
          try {
            const result = await updateProjectStatus(id, status);
            return { id, status: result.status, message: result.message };
          } catch (error) {
            return { id, status: "error", message: "Failed to update" };
          }
        })
      );
      return results;
    },
    onSuccess: (results) => {
      const successCount = results.filter(
        (r) => r.status === ApiStatus.SUCCESS
      ).length;
      const failureCount = results.length - successCount;

      if (failureCount === 0) {
        toast({
          title: "Success",
          description: `Successfully updated ${successCount} project${
            successCount > 1 ? "s" : ""
          }`,
        });
      } else {
        toast({
          title: "Warning",
          description: `Updated ${successCount} projects, failed to update ${failureCount}`,
          variant: "destructive",
        });
      }

      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      queryClient.invalidateQueries({ queryKey: projectKeys.slim() });
    },
    onError: (error) => {
      console.error("Failed to bulk update project status:", error);
      toast({
        title: "Error",
        description: "Failed to update project status",
        variant: "destructive",
      });
    },
  });
}

// Custom hooks for specific use cases

// Hook to get projects by status
export function useProjectsByStatus(status: ProjectStatus) {
  return useProjects({ status });
}

// Hook to get projects by customer
export function useProjectsByCustomer(customerId: string) {
  return useProjects({ customerId });
}

// Hook to get active projects (not completed or cancelled)
export function useActiveProjects() {
  return useQuery({
    queryKey: [...projectKeys.lists(), "active"],
    queryFn: async () => {
      const response = await getProjects({});
      if (response.status === ApiStatus.SUCCESS && response.data) {
        const activeProjects = response.data.data.filter(
          (project) =>
            project.status !== ProjectStatus.COMPLETED &&
            project.status !== ProjectStatus.CANCELLED
        );
        return {
          ...response,
          data: {
            ...response.data,
            data: activeProjects,
            meta: {
              ...response.data.meta,
              total: activeProjects.length,
            },
          },
        };
      }
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get overdue projects
export function useOverdueProjects() {
  return useQuery({
    queryKey: [...projectKeys.lists(), "overdue"],
    queryFn: async () => {
      const response = await getProjects({});
      if (response.status === ApiStatus.SUCCESS && response.data) {
        const now = new Date();
        const overdueProjects = response.data.data.filter((project) => {
          if (
            !project.endDate ||
            project.status === ProjectStatus.COMPLETED ||
            project.status === ProjectStatus.CANCELLED
          ) {
            return false;
          }
          return new Date(project.endDate) < now;
        });
        return {
          ...response,
          data: {
            ...response.data,
            data: overdueProjects,
            meta: {
              ...response.data.meta,
              total: overdueProjects.length,
            },
          },
        };
      }
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get project statistics
export function useProjectStats() {
  return useQuery({
    queryKey: [...projectKeys.all, "stats"],
    queryFn: async () => {
      const response = await getProjects({});
      if (response.status === ApiStatus.SUCCESS && response.data) {
        const projects = response.data.data;
        const stats = {
          total: projects.length,
          notStarted: projects.filter(
            (p) => p.status === ProjectStatus.NOT_STARTED
          ).length,
          inProgress: projects.filter(
            (p) => p.status === ProjectStatus.IN_PROGRESS
          ).length,
          completed: projects.filter(
            (p) => p.status === ProjectStatus.COMPLETED
          ).length,
          cancelled: projects.filter(
            (p) => p.status === ProjectStatus.CANCELLED
          ).length,
          overdue: projects.filter((p) => {
            if (
              !p.endDate ||
              p.status === ProjectStatus.COMPLETED ||
              p.status === ProjectStatus.CANCELLED
            ) {
              return false;
            }
            return new Date(p.endDate) < new Date();
          }).length,
        };
        return {
          status: "success" as const,
          message: "Stats retrieved successfully",
          data: stats,
        };
      }
      return {
        status: "fail" as const,
        message: "Failed to retrieve stats",
        data: null,
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for project autocomplete search
export function useProjectsAutocomplete(
  search?: string,
  limit: number = 10,
  isDemo: boolean = false,
  enabled: boolean = true
) {
  return useQuery({
    queryKey: [...projectKeys.all, "autocomplete", search, limit],
    queryFn: () => searchProjectsAutocomplete(search, limit, isDemo),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
