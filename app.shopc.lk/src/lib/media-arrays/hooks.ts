import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import {
  createMediaArrayApi,
  getMediaArraysByReferenceApi,
  getMediaArrayApi,
  updateMediaArrayApi,
  deleteMediaArrayApi,
  reorderMediaArraysApi,
  deleteMediaArraysByReferenceApi,
  restoreMediaArrayApi,
  UpdateMediaArrayDto,
  ReorderMediaArrayDto,
} from "./api";
import { ApiStatus } from "@/types/common";

// Query keys
export const mediaArrayKeys = {
  all: ["media-arrays"] as const,
  byReference: (referenceId: string) =>
    [...mediaArrayKeys.all, "reference", referenceId] as const,
  detail: (id: string) => [...mediaArrayKeys.all, "detail", id] as const,
};

// Get media arrays by reference ID
export function useMediaArraysByReference(
  referenceId: string,
  includeMediaDetails = true,
  enabled = true
) {
  return useQuery({
    queryKey: mediaArrayKeys.byReference(referenceId),
    queryFn: () =>
      getMediaArraysByReferenceApi(referenceId, includeMediaDetails),
    enabled: enabled && !!referenceId,
  });
}

// Get single media array
export function useMediaArray(id: string, enabled = true) {
  return useQuery({
    queryKey: mediaArrayKeys.detail(id),
    queryFn: () => getMediaArrayApi(id),
    enabled: enabled && !!id,
  });
}

// Create media array mutation
export function useCreateMediaArray() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createMediaArrayApi,
    onSuccess: (response, variables) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Media added to collection successfully",
        });

        // Invalidate the reference query to refresh the list
        queryClient.invalidateQueries({
          queryKey: mediaArrayKeys.byReference(variables.referenceId),
        });
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to add media",
        variant: "destructive",
      });
    },
  });
}

// Update media array mutation
export function useUpdateMediaArray() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMediaArrayDto }) =>
      updateMediaArrayApi(id, data),
    onSuccess: (response, variables) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Media array updated successfully",
        });

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: mediaArrayKeys.detail(variables.id),
        });

        if (variables.data.referenceId) {
          queryClient.invalidateQueries({
            queryKey: mediaArrayKeys.byReference(variables.data.referenceId),
          });
        }
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to update media array",
        variant: "destructive",
      });
    },
  });
}

// Delete media array mutation
export function useDeleteMediaArray() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }: { id: string; referenceId: string }) =>
      deleteMediaArrayApi(id),
    onSuccess: (response, variables) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Media removed from collection successfully",
        });

        // Invalidate the reference query to refresh the list
        queryClient.invalidateQueries({
          queryKey: mediaArrayKeys.byReference(variables.referenceId),
        });
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete media",
        variant: "destructive",
      });
    },
  });
}

// Reorder media arrays mutation
export function useReorderMediaArrays() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      referenceId,
      reorderData,
    }: {
      referenceId: string;
      reorderData: ReorderMediaArrayDto;
    }) => reorderMediaArraysApi(referenceId, reorderData),
    onSuccess: (response, variables) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Media order updated successfully",
        });

        // Invalidate the reference query to refresh the list
        queryClient.invalidateQueries({
          queryKey: mediaArrayKeys.byReference(variables.referenceId),
        });
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to reorder media",
        variant: "destructive",
      });
    },
  });
}

// Delete all media arrays by reference mutation
export function useDeleteMediaArraysByReference() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteMediaArraysByReferenceApi,
    onSuccess: (response, referenceId) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "All media removed successfully",
        });

        // Invalidate the reference query
        queryClient.invalidateQueries({
          queryKey: mediaArrayKeys.byReference(referenceId),
        });
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to delete media",
        variant: "destructive",
      });
    },
  });
}

// Restore media array mutation
export function useRestoreMediaArray() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }: { id: string; referenceId: string }) =>
      restoreMediaArrayApi(id),
    onSuccess: (response, variables) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Media restored successfully",
        });

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: mediaArrayKeys.byReference(variables.referenceId),
        });
      } else {
        toast({
          title: "Error",
          description: response.message,
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.message || "Failed to restore media",
        variant: "destructive",
      });
    },
  });
}
