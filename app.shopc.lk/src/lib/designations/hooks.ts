import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  DesignationPaginatedResponse,
  SimpleDesignationResponse,
  DesignationResponse,
  DesignationNameAvailabilityResponse,
  DesignationIdResponse,
  BulkDesignationIdsResponse,
  DeleteDesignationResponseDto,
} from "@/types/designations";
import {
  getDesignationsTableData,
  getSimpleDesignations,
  getDesignation,
  createDesignation,
  bulkCreateDesignations,
  bulkImportDesignationsWithImages,
  updateDesignation,
  deleteDesignation,
  checkDesignationNameAvailability,
  getDesignationsHierarchy,
  bulkUpdateDesignationHierarchy,
  bulkDeleteDesignations,
  bulkUpdateDesignationStatus,
} from "./queries";
import {
  GetDesignationsSchema,
  CreateDesignationSchema,
  UpdateDesignationSchema,
  BulkCreateDesignationSchema,
  BulkImportDesignationsWithImagesSchema,
} from "./validations";

// Query keys for React Query cache management
export const designationKeys = {
  all: ["designations"] as const,
  lists: () => [...designationKeys.all, "list"] as const,
  list: (params: GetDesignationsSchema & { isDemo?: boolean }) =>
    [...designationKeys.lists(), params] as const,
  filtered: (params: GetDesignationsSchema & { isDemo?: boolean }) =>
    [...designationKeys.lists(), params] as const, // Alias for list for backward compatibility
  simple: () => [...designationKeys.all, "simple"] as const,
  simpleWithDemo: (isDemo: boolean) =>
    [...designationKeys.simple(), isDemo] as const,
  slim: () => [...designationKeys.all, "slim"] as const,
  details: () => [...designationKeys.all, "detail"] as const,
  detail: (id: string, isDemo: boolean) =>
    [...designationKeys.details(), id, isDemo] as const,
  nameAvailability: (name: string, isDemo: boolean) =>
    [...designationKeys.all, "nameAvailability", name, isDemo] as const,
  hierarchy: (isDemo?: boolean) =>
    [...designationKeys.all, "hierarchy", isDemo] as const,
};

// Hook to get designations with pagination and filtering
export function useDesignationsData(
  params: GetDesignationsSchema,
  isDemo: boolean = false
): UseQueryResult<DesignationPaginatedResponse> {
  return useQuery({
    queryKey: designationKeys.list({ ...params, isDemo }),
    queryFn: () => getDesignationsTableData(params, isDemo),
    staleTime: 0,
    refetchOnMount: true,
  });
}

// Hook to get simple designations (slim version)
export function useSimpleDesignations(
  isDemo: boolean = false
): UseQueryResult<SimpleDesignationResponse> {
  return useQuery({
    queryKey: designationKeys.simpleWithDemo(isDemo),
    queryFn: () => getSimpleDesignations(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get a single designation by ID
export function useDesignation(
  id: string,
  isDemo: boolean = false
): UseQueryResult<DesignationResponse> {
  return useQuery({
    queryKey: designationKeys.detail(id, isDemo),
    queryFn: () => getDesignation(id, isDemo),
    enabled: !!id,
  });
}

// Alias for useDesignation for consistency with other modules
export function useDesignationData(
  designationId: string,
  isDemo: boolean = false
): UseQueryResult<DesignationResponse> {
  return useDesignation(designationId, isDemo);
}

// Alias for useSimpleDesignations for consistency with other modules
export function useDesignationsSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleDesignationResponse> {
  return useSimpleDesignations(isDemo);
}

// Hook to check designation name availability
export function useDesignationNameAvailability(
  name: string,
  isDemo: boolean = false
): UseQueryResult<DesignationNameAvailabilityResponse> {
  return useQuery({
    queryKey: designationKeys.nameAvailability(name, isDemo),
    queryFn: () => checkDesignationNameAvailability(name, isDemo),
    enabled: !!name && name.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Hook to create a designation
export function useCreateDesignation(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation<DesignationIdResponse, Error, CreateDesignationSchema>({
    mutationFn: (data: CreateDesignationSchema) =>
      createDesignation(data, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Designation created successfully",
        });
        // Invalidate and refetch designation lists
        queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
        queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to create designation",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create designation",
      });
    },
  });
}

// Hook to bulk create designations
export function useBulkCreateDesignations(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation<
    BulkDesignationIdsResponse,
    Error,
    BulkCreateDesignationSchema
  >({
    mutationFn: (data: BulkCreateDesignationSchema) =>
      bulkCreateDesignations(data, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Designations created successfully",
        });
        // Invalidate and refetch designation lists
        queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
        queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to create designations",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to create designations",
      });
    },
  });
}

// Hook to bulk import designations with images
export function useBulkImportDesignationsWithImages(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkImportDesignationsWithImagesSchema) =>
      bulkImportDesignationsWithImages(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch designation queries
      queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
    },
  });
}

// Hook to update a designation
export function useUpdateDesignation(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation<
    DesignationIdResponse,
    Error,
    { id: string; data: UpdateDesignationSchema }
  >({
    mutationFn: ({ id, data }: { id: string; data: UpdateDesignationSchema }) =>
      updateDesignation(id, data, isDemo),
    onSuccess: (response, { id }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Designation updated successfully",
        });
        // Invalidate and refetch related queries
        queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
        queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
        queryClient.invalidateQueries({
          queryKey: designationKeys.detail(id, isDemo),
        });
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to update designation",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update designation",
      });
    },
  });
}

// Hook to delete a designation
export function useDeleteDesignation(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<DeleteDesignationResponseDto>, Error, string>({
    mutationFn: (id: string) => deleteDesignation(id, isDemo),
    onSuccess: (response, id) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: response.data?.message || "Designation deleted successfully",
        });
        // Invalidate and refetch designation lists
        queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
        queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
        // Remove the specific designation from cache
        queryClient.removeQueries({
          queryKey: designationKeys.detail(id, isDemo),
        });
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.message || "Failed to delete designation",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to delete designation",
      });
    },
  });
}

// Designations hierarchy hook
export function useDesignationsHierarchyData(isDemo: boolean = false) {
  return useQuery({
    queryKey: designationKeys.hierarchy(isDemo),
    queryFn: () => getDesignationsHierarchy(isDemo),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook to bulk delete designations
export function useBulkDeleteDesignations(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (designationIds: string[]) =>
      bulkDeleteDesignations(designationIds, isDemo),
    onSuccess: () => {
      // Invalidate and refetch designation lists
      queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
    },
  });
}

// Hook to bulk update designation status
export function useBulkUpdateDesignationStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      designationIds,
      status,
    }: {
      designationIds: string[];
      status: string;
    }) => bulkUpdateDesignationStatus(designationIds, status, isDemo),
    onSuccess: () => {
      // Invalidate and refetch designation lists
      queryClient.invalidateQueries({ queryKey: designationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: designationKeys.simple() });
    },
  });
}
