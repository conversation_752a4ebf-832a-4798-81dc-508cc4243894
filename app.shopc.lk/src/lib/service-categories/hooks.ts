import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  ServiceCategoryPaginatedResponse,
  ServiceCategoryIdResponse,
  BulkServiceCategoryIdsResponse,
  BulkDeleteServiceCategoryResponse,
  ServiceCategoryNameAvailabilityResponse,
  ServiceCategorySlugAvailabilityResponse,
  ServiceCategoryShortCodeAvailabilityResponse,
  SimpleServiceCategoryResponse,
  ServiceCategoryResponse,
  CreateServiceCategoryDto,
  UpdateServiceCategoryDto,
  BulkCreateServiceCategoryDto,
  ServiceCategoryStatus,
  ServiceCategoryHierarchyResponse,
} from "@/types/service-category";
import { ApiResponse } from "@/types/common";
import {
  GetServiceCategoriesSchema,
  BulkImportServiceCategoriesWithImagesSchema,
} from "./validations";
import {
  getServiceCategoriesTableData,
  getServiceCategoriesSlim,
  getServiceCategory,
  checkServiceCategoryNameAvailability,
  checkServiceCategorySlugAvailability,
  checkServiceCategoryShortCodeAvailability,
  updateServiceCategory,
  deleteServiceCategory,
  updateServiceCategoryStatus,
  updateServiceCategoryAvailableOnline,
  updateServiceCategoryPositions,
  bulkImportServiceCategoriesWithImages,
  getServiceCategoriesHierarchy,
  getServiceCategoriesHierarchyData,
  createServiceCategory,
  bulkCreateServiceCategories,
  bulkDeleteServiceCategories,
} from "./queries";

// Query keys
export const serviceCategoryKeys = {
  all: ["service-categories"] as const,
  lists: () => [...serviceCategoryKeys.all, "list"] as const,
  list: (filters: string) =>
    [...serviceCategoryKeys.lists(), { filters }] as const,
  details: () => [...serviceCategoryKeys.all, "detail"] as const,
  detail: (id: string) => [...serviceCategoryKeys.details(), id] as const,
  slim: () => [...serviceCategoryKeys.all, "slim"] as const,
  hierarchy: () => [...serviceCategoryKeys.all, "hierarchy"] as const,
  hierarchyData: () => [...serviceCategoryKeys.all, "hierarchy-data"] as const,
  nameAvailability: (name: string) =>
    [...serviceCategoryKeys.all, "name-availability", name] as const,
  slugAvailability: (slug: string) =>
    [...serviceCategoryKeys.all, "slug-availability", slug] as const,
  shortCodeAvailability: (shortCode: string) =>
    [...serviceCategoryKeys.all, "short-code-availability", shortCode] as const,
};

// Get service categories with pagination and filters
export function useServiceCategories(
  params: GetServiceCategoriesSchema,
  isDemo: boolean = false
): UseQueryResult<ServiceCategoryPaginatedResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.list(JSON.stringify(params)),
    queryFn: () => getServiceCategoriesTableData(params, isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get service categories with pagination and filters (alias for consistency with categories)
export function useServiceCategoriesData(
  params: GetServiceCategoriesSchema,
  isDemo: boolean = false
): UseQueryResult<ServiceCategoryPaginatedResponse, Error> {
  return useServiceCategories(params, isDemo);
}

// Get service categories in slim format (for dropdowns/selects)
export function useServiceCategoriesSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleServiceCategoryResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.slim(),
    queryFn: () => getServiceCategoriesSlim(isDemo),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get service categories in hierarchical format
export function useServiceCategoriesHierarchy(
  isDemo: boolean = false
): UseQueryResult<SimpleServiceCategoryResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.hierarchy(),
    queryFn: () => getServiceCategoriesHierarchy(isDemo),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get service categories with nested subcategories for service forms
export function useServiceCategoriesWithNested(
  isDemo: boolean = false
): UseQueryResult<SimpleServiceCategoryResponse, Error> {
  return useQuery({
    queryKey: [...serviceCategoryKeys.hierarchy(), 'with-nested'],
    queryFn: () => getServiceCategoriesHierarchy(isDemo),
    staleTime: 10 * 60 * 1000, // 10 minutes
    select: (data) => {
      if (!data.data) return data;
      
      // Ensure we only return root categories with their nested subcategories
      const rootCategories = data.data.filter(category => !category.parentId);
      
      return {
        ...data,
        data: rootCategories
      };
    }
  });
}

// Get service categories hierarchy data
export function useServiceCategoriesHierarchyData(
  isDemo: boolean = false
): UseQueryResult<ServiceCategoryHierarchyResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.hierarchyData(),
    queryFn: () => getServiceCategoriesHierarchyData(isDemo),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get a single service category by ID
export function useServiceCategory(
  id: string,
  isDemo: boolean = false
): UseQueryResult<ServiceCategoryResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.detail(id),
    queryFn: () => getServiceCategory(id, isDemo),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Check service category name availability
export function useServiceCategoryNameAvailability(
  name: string,
  isDemo: boolean = false,
  excludeId?: string
): UseQueryResult<ServiceCategoryNameAvailabilityResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.nameAvailability(name),
    queryFn: () =>
      checkServiceCategoryNameAvailability(name, isDemo, excludeId),
    enabled: !!name && name.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Check service category slug availability
export function useServiceCategorySlugAvailability(
  slug: string,
  isDemo: boolean = false,
  excludeId?: string
): UseQueryResult<ServiceCategorySlugAvailabilityResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.slugAvailability(slug),
    queryFn: () =>
      checkServiceCategorySlugAvailability(slug, isDemo, excludeId),
    enabled: !!slug && slug.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Check service category short code availability
export function useServiceCategoryShortCodeAvailability(
  shortCode: string,
  isDemo: boolean = false,
  excludeId?: string
): UseQueryResult<ServiceCategoryShortCodeAvailabilityResponse, Error> {
  return useQuery({
    queryKey: serviceCategoryKeys.shortCodeAvailability(shortCode),
    queryFn: () =>
      checkServiceCategoryShortCodeAvailability(shortCode, isDemo, excludeId),
    enabled: !!shortCode && shortCode.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Create service category mutation
export function useCreateServiceCategory(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      data,
      image,
      ogImage,
    }: {
      data: CreateServiceCategoryDto;
      image?: File;
      ogImage?: File;
    }) => createServiceCategory(data, isDemo, image, ogImage),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Bulk create service categories mutation
export function useBulkCreateServiceCategories(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      serviceCategories,
      images,
    }: {
      serviceCategories: BulkCreateServiceCategoryDto[];
      images?: File[];
    }) => bulkCreateServiceCategories(serviceCategories, isDemo, images),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Update service category mutation
export function useUpdateServiceCategory(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
      image,
      ogImage,
    }: {
      id: string;
      data: UpdateServiceCategoryDto;
      image?: File;
      ogImage?: File;
    }) => updateServiceCategory(id, data, isDemo, image, ogImage),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
      queryClient.invalidateQueries({
        queryKey: serviceCategoryKeys.detail(variables.id),
      });
    },
  });
}

// Delete service category mutation
export function useDeleteServiceCategory(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteServiceCategory(id, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Bulk delete service categories mutation
export function useBulkDeleteServiceCategories(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (serviceCategoryIds: string[]) =>
      bulkDeleteServiceCategories(serviceCategoryIds, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Update service category status mutation
export function useUpdateServiceCategoryStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      status,
    }: {
      id: string;
      status: ServiceCategoryStatus;
    }) => updateServiceCategoryStatus(id, status, isDemo),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
      queryClient.invalidateQueries({
        queryKey: serviceCategoryKeys.detail(variables.id),
      });
    },
  });
}

// Update service category available online mutation
export function useUpdateServiceCategoryAvailableOnline(
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      availableOnline,
    }: {
      id: string;
      availableOnline: boolean;
    }) => updateServiceCategoryAvailableOnline(id, availableOnline, isDemo),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
      queryClient.invalidateQueries({
        queryKey: serviceCategoryKeys.detail(variables.id),
      });
    },
  });
}

// Update service category positions mutation
export function useUpdateServiceCategoryPositions(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: { id: string; position: number }[]) =>
      updateServiceCategoryPositions(updates, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Bulk import service categories with images mutation
export function useBulkImportServiceCategoriesWithImages(
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkImportServiceCategoriesWithImagesSchema) =>
      bulkImportServiceCategoriesWithImages(data, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Bulk update service category status mutation
export function useBulkUpdateServiceCategoryStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      serviceCategoryIds,
      status,
    }: {
      serviceCategoryIds: string[];
      status: ServiceCategoryStatus;
    }) => {
      const promises = serviceCategoryIds.map((id) =>
        updateServiceCategoryStatus(id, status, isDemo)
      );
      return Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}

// Bulk update service category availability mutation
export function useBulkUpdateServiceCategoryAvailability(
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      serviceCategoryIds,
      availableOnline,
    }: {
      serviceCategoryIds: string[];
      availableOnline: boolean;
    }) => {
      const promises = serviceCategoryIds.map((id) =>
        updateServiceCategoryAvailableOnline(id, availableOnline, isDemo)
      );
      return Promise.all(promises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    },
  });
}
