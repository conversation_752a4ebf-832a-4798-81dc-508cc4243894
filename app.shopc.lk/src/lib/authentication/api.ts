import { jwtVerify, SignJWT, JWTPayload } from "jose";
import { NextRequest, NextResponse } from "next/server";
import { ApiStatus, Session, ApiResponse } from "@/types/common";
import {
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  UserResponse,
  User,
  UpdateProfileData,
  UsernameCheckResponse,
  TwoFactorResponse,
  TwoFactorVerifyLoginResponse,
  TwoFactorVerifyResponse,
  ForgotPasswordResponse,
  ResetPasswordResponse,
  Role,
  CreateUserRequest,
  RegisterUserData,
} from "@/types/auth";
import axios from "@/utils/axios";
import { default as defaultAxios } from "axios";
import Cookies from "js-cookie";
import { ModuleType } from "@/types/business-modules";
import {
  getSessionMetadata,
  generateSessionId,
  setSessionId,
  clearSessionId,
} from "@/utils/session";

const secretKey = process.env.NEXT_PUBLIC_JWT_SECRET || "secret123";
const key = new TextEncoder().encode(secretKey);

// Utility function to check if a token is close to expiring
function isTokenNearExpiry(expires: Date, bufferMinutes: number = 5): boolean {
  const now = new Date();
  const expiryWithBuffer = new Date(
    expires.getTime() - bufferMinutes * 60 * 1000
  );
  return now >= expiryWithBuffer;
}

export async function encrypt(payload: JWTPayload | undefined) {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("7 days")
    .sign(key);
}

export async function decrypt(input: string): Promise<Session> {
  const { payload } = await jwtVerify(input, key, {
    algorithms: ["HS256"],
  });
  return payload as Session;
}

// Session Management
export async function createSession(tokens: {
  accessToken: string;
  refreshToken: string;
}) {
  const user = {
    token: tokens.accessToken,
    refreshToken: tokens.refreshToken,
  };

  const expires = new Date(Date.now() + 1000 * 60 * 60 * 24 * 1); // 1 day for access token
  const refreshExpires = new Date(Date.now() + 1000 * 60 * 60 * 24 * 7); // 7 days for refresh token
  const createdAt = new Date(Date.now());
  const session = await encrypt({ user, expires, createdAt });

  // Generate a new session ID for this login session
  const sessionId = generateSessionId();
  setSessionId(sessionId);

  // Use js-cookie for client-side
  if (typeof window !== "undefined") {
    Cookies.set("session", session, {
      expires: 7, // 7 days
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });
  } else {
    // Server-side
    const { cookies } = await import("next/headers");
    (await cookies()).set("session", session, {
      expires: refreshExpires,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });
  }
}

// Utility function to validate and refresh session if needed
export async function validateAndRefreshSession(): Promise<{
  isValid: boolean;
  session: Session | null;
  error?: string;
}> {
  try {
    const session = await getSession();

    if (!session) {
      return { isValid: false, session: null, error: "No session found" };
    }

    // Check if user token exists
    if (!session.user?.token) {
      return { isValid: false, session: null, error: "No access token" };
    }

    return { isValid: true, session };
  } catch (error: any) {
    console.error("Session validation error:", error);
    return {
      isValid: false,
      session: null,
      error: error.message || "Session validation failed",
    };
  }
}

export async function getSession(): Promise<Session | null> {
  let sessionCookie;

  // Get cookie based on environment
  if (typeof window !== "undefined") {
    sessionCookie = Cookies.get("session");
  } else {
    const { cookies } = await import("next/headers");
    sessionCookie = (await cookies()).get("session")?.value;
  }

  if (!sessionCookie) return null;

  try {
    const session = await decrypt(sessionCookie);
    const now = new Date();
    const expires = new Date(session.expires);

    // Check if session is expired or close to expiring (proactive refresh)
    if (now > expires || isTokenNearExpiry(expires, 5)) {
      console.log(
        now > expires
          ? "Session expired, attempting refresh..."
          : "Session near expiry, proactively refreshing..."
      );

      if (session.user?.refreshToken) {
        try {
          const refreshResponse = await refreshAccessToken(
            session.user.refreshToken
          );

          if (
            refreshResponse.status === ApiStatus.SUCCESS &&
            refreshResponse.data
          ) {
            // Create new session with refreshed tokens
            const newSession = {
              ...session,
              user: {
                token: refreshResponse.data.accessToken,
                refreshToken: refreshResponse.data.refreshToken,
              },
              expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1), // 1 day for access token
              createdAt: new Date(),
            };

            // Save the new session to cookies
            const newSessionCookie = await encrypt(newSession);
            const cookieExpires = new Date(
              Date.now() + 1000 * 60 * 60 * 24 * 7
            ); // 7 days

            if (typeof window !== "undefined") {
              Cookies.set("session", newSessionCookie, { expires: 7 }); // 7 days
            } else {
              const { cookies } = await import("next/headers");
              (await cookies()).set("session", newSessionCookie, {
                expires: cookieExpires,
              });
            }

            console.log("Session refreshed successfully");
            return newSession;
          } else {
            console.warn("Refresh response failed:", refreshResponse.message);
          }
        } catch (error) {
          console.error("Failed to refresh session:", error);
        }
      }

      console.log("Unable to refresh session, clearing...");
      await clearSession();
      return null;
    }

    return session;
  } catch (error) {
    console.error("Error decrypting session:", error);
    await clearSession();
    return null;
  }
}

export async function clearSession() {
  if (typeof window !== "undefined") {
    Cookies.remove("session");
    clearSessionId(); // Also clear session ID
  } else {
    const { cookies } = await import("next/headers");
    (await cookies()).delete("session");
  }
}

// Authentication Actions
export async function register(
  userData: RegisterUserData
): Promise<ApiResponse<UserResponse>> {
  try {
    const res = await axios.post("/business/register", userData);

    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data.tokens);
    }

    return res.data;
  } catch (error) {
    return error as ApiResponse<UserResponse>;
  }
}

// Send SMS OTP for verification
export async function sendSmsOtp(
  telephone: string
): Promise<ApiResponse<{ otpId: string }>> {
  try {
    const res = await axios.post("/auth/send-otp", { telephone });
    return res.data;
  } catch (error) {
    return error as ApiResponse<{ otpId: string }>;
  }
}

// Verify SMS OTP
export async function verifySmsOtp(
  otpId: string,
  otp: string
): Promise<ApiResponse<{ verified: boolean }>> {
  try {
    const res = await axios.post("/auth/verify-otp", { otpId, otp });
    return res.data;
  } catch (error) {
    return error as ApiResponse<{ verified: boolean }>;
  }
}

// Register with OTP verification
export async function registerWithOtp(
  data: RegisterCredentials,
  otpId: string,
  otp: string
): Promise<ApiResponse<UserResponse>> {
  try {
    const res = await axios.post("/auth/register-with-otp", {
      email: data.email,
      password: data.password,
      firstName: data.firstName,
      lastName: data.lastName,
      telephone: data.telephone,
      businessName: data.businessName,
      businessType: data.businessType,
      pricingPlan: data.pricingPlan,
      otpId,
      otp,
    });

    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data.tokens);
    }

    return res.data;
  } catch (error) {
    return error as ApiResponse<UserResponse>;
  }
}

export async function login(
  data: LoginCredentials
): Promise<ApiResponse<AuthResponse>> {
  try {
    const res = await axios.post("/auth/login", {
      identifier: data.identifier,
      password: data.password,
    });

    if (res.data.status === ApiStatus.SUCCESS) {
      if (res.data.data.requires2FA) {
        return res.data;
      }

      // if (res.data.data.user.role !== Role.Admin) {
      //   return {
      //     status: ApiStatus.FAIL,
      //     message: "Access denied. Admin access only.",
      //     data: res.data.data,
      //   };
      // }

      await createSession(res.data.data.tokens);
    }

    return res.data;
  } catch (error) {
    return error as ApiResponse<AuthResponse>;
  }
}

export async function logout(): Promise<void> {
  try {
    // Get current session ID before clearing
    const sessionMetadata = getSessionMetadata();

    // Call backend logout endpoint with session ID
    try {
      await axios.post("/auth/logout", {
        sessionId: sessionMetadata.sessionId,
      });
    } catch (error) {
      console.error("Backend logout error:", error);
      // Continue with local cleanup even if backend call fails
    }

    await clearSession();
  } catch (error) {
    throw error;
  }
}

export async function getProfile(): Promise<ApiResponse<UserResponse>> {
  try {
    const res = await axios.get("/users/profile");

    res.data.data.business.businessModules = [
      ModuleType.ITEMS,
      ModuleType.BRANDS,
      ModuleType.WARRANCIES,
      ModuleType.MEAL_PERIODS,
      ModuleType.VARIANTS,
      ModuleType.CATEGORIES,
      ModuleType.SERVICES,
      ModuleType.PRODUCTS,
      ModuleType.CUSTOMERS,
      ModuleType.CUSTOMER_GROUPS,
      ModuleType.PAYMENT_ACCOUNTS,
      ModuleType.PAYMENT_ACCOUNT_TYPES,
      ModuleType.EXPENSES,
      ModuleType.EXPENSE_CATEGORIES,
      ModuleType.EMPLOYEES,
      ModuleType.DESIGNATIONS,
      ModuleType.DEPARTMENTS,
      ModuleType.ASSETS_MAINTENANCE,
      ModuleType.ASSETS_ALLOCATED,
      ModuleType.ASSETS_REVOKED,
      ModuleType.ASSETS_CATEGORIES,
      ModuleType.ASSETS,
      ModuleType.APPOINTMENTS,
      ModuleType.VEHICLES,
      ModuleType.VEHICLE_TYPES,
      ModuleType.SUPPLIERS,
      ModuleType.PURCHASES,
      ModuleType.PURCHASE_RETURNS,
      ModuleType.MODIFIERS,
      ModuleType.SERVICE_TYPES,
      ModuleType.SERVICES,
      ModuleType.ACCOUNTS,
      ModuleType.ROOM_TYPES,
      ModuleType.ROOMS,
      ModuleType.ROOM_BOOKINGS,
      ModuleType.TAX,
      ModuleType.PAYMENT_METHODS,
      ModuleType.TASKS,
      ModuleType.PROJECT,
      ModuleType.PROMO_CODES,
      ModuleType.GAMES,
      ModuleType.DISCOUNT_PLANS,
      ModuleType.CAMPAIGN,
      ModuleType.EMAIL_MARKETING,
      ModuleType.SOCIAL_MEDIA,
      ModuleType.SEO,
      ModuleType.VEHICLE_FINES,
      ModuleType.VEHICLE_DAMAGES,
      ModuleType.RESTAURANT_POS,
      ModuleType.RETAIL_POS,
      ModuleType.SMS_PROVIDERS,
      ModuleType.EMAIL_PROVIDERS,
      ModuleType.WHATSAPP_PROVIDERS,
      ModuleType.COD_PROVIDERS,
      ModuleType.PAYMENT_GATEWAY_PROVIDERS,
      ModuleType.AI_PROVIDERS,
      ModuleType.UNITS,
      ModuleType.WORKORDERS,
      ModuleType.WORKORDERS,
      ModuleType.WORKORDERS,
      ModuleType.SERVICE_ORDERS,
    ];

    return res.data;
  } catch (error) {
    return error as ApiResponse<UserResponse>;
  }
}

export async function checkUsername(
  username: string
): Promise<ApiResponse<UsernameCheckResponse>> {
  try {
    const res = await axios.get(`/auth/check-username/${username}`);
    return res.data;
  } catch (error) {
    return error as ApiResponse<UsernameCheckResponse>;
  }
}

export async function forgotPassword(
  email: string
): Promise<ApiResponse<ForgotPasswordResponse>> {
  try {
    const res = await axios.post("/auth/forgot-password", { email });
    return res.data;
  } catch (error) {
    return error as ApiResponse<ForgotPasswordResponse>;
  }
}

export async function resetPassword(
  token: string,
  newPassword: string
): Promise<ApiResponse<ResetPasswordResponse>> {
  try {
    const res = await axios.post("/auth/reset-password", {
      token,
      newPassword,
    });
    return res.data;
  } catch (error) {
    return error as ApiResponse<ResetPasswordResponse>;
  }
}

export async function enable2FA(
  code: string
): Promise<ApiResponse<TwoFactorResponse>> {
  try {
    const res = await axios.post("/auth/2fa/enable", { code });
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorResponse>;
  }
}

export async function verify2FA(
  userId: string,
  code: string
): Promise<ApiResponse<TwoFactorVerifyLoginResponse>> {
  try {
    const res = await axios.post("/auth/2fa/verify", { userId, code });
    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data.tokens);
    }
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorVerifyLoginResponse>;
  }
}

export async function verify2FASetup(
  code: string
): Promise<ApiResponse<TwoFactorVerifyResponse>> {
  try {
    const res = await axios.post("/auth/2fa/verify-setup", { code });
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorVerifyResponse>;
  }
}

export async function disable2FA(): Promise<
  ApiResponse<TwoFactorVerifyResponse>
> {
  try {
    const res = await axios.post("/auth/2fa/disable");
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorVerifyResponse>;
  }
}

export async function generate2FA(): Promise<ApiResponse<TwoFactorResponse>> {
  try {
    const res = await axios.post("/auth/2fa/generate");
    return res.data;
  } catch (error) {
    return error as ApiResponse<TwoFactorResponse>;
  }
}

export async function updateProfile(
  data: UpdateProfileData
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/users/update-profile", data);
    return res.data;
  } catch (error) {
    console.log("error ::: ", error);
    return error as ApiResponse<User>;
  }
}

export async function updateSession(request: NextRequest) {
  const session = request.cookies.get("session")?.value;

  if (!session) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  return NextResponse.next();
}

export async function refreshAccessToken(
  refreshToken: string
): Promise<ApiResponse<{ accessToken: string; refreshToken: string }>> {
  try {
    const res = await defaultAxios.post(
      process.env.NEXT_PUBLIC_API_URL + "/api/v1" + "/auth/refresh",
      { refreshToken },
      {
        headers: {
          "Content-Type": "application/json",
        },
        timeout: 10000, // 10 second timeout
      }
    );

    if (res.data.status === ApiStatus.SUCCESS) {
      await createSession(res.data.data);
    }

    return res.data;
  } catch (error: any) {
    console.error("Refresh token error:", error);

    // Return structured error response
    if (error.response?.data) {
      return error.response.data as ApiResponse<{
        accessToken: string;
        refreshToken: string;
      }>;
    }

    return {
      status: ApiStatus.FAIL,
      message: error.message || "Failed to refresh token",
      data: null,
    };
  }
}

export async function createUser(
  data: CreateUserRequest
): Promise<ApiResponse<User>> {
  try {
    const res = await axios.post("/auth/create-user", {
      username: data.username,
      email: data.email,
      password: data.password,
      accountType: data.accountType,
      role: data.role,
      twoFactorMethod: data.twoFactorMethod,
      status: data.status,
    });

    return res.data;
  } catch (error) {
    return error as ApiResponse<User>;
  }
}
