// Authentication API functions
export {
  encrypt,
  decrypt,
  createSession,
  getSession,
  clearSession,
  validateAndRefreshSession,
  register,
  sendSmsOtp,
  verifySmsOtp,
  registerWithOtp,
  login,
  logout,
  getProfile,
  checkUsername,
  forgotPassword,
  resetPassword,
  enable2FA,
  verify2FA,
  verify2FASetup,
  disable2FA,
  generate2FA,
  updateProfile,
  updateSession,
  refreshAccessToken,
  createUser,
} from "./api";

// Authentication hooks
export {
  authKeys,
  useAuthSession,
  useProfile,
  useAutoLogout,
  useLogin,
  useRegister,
  useLogout,
  useUpdateProfile,
  useUsernameCheck,
  useGenerate2FA,
  useEnable2FA,
  useVerify2FA,
  useVerify2FASetup,
  useDisable2FA,
  useForgotPassword,
  useResetPassword,
  useSendSmsOtp,
  useVerifySmsOtp,
  useRegisterWithOtp,
  useCreateUser,
} from "./hooks";

// Session management hooks
export {
  sessionKeys,
  useUserSessions,
  useSessionStats,
  useSessionDetail,
  useRevokeSession,
  useRevokeAllSessions,
  useCurrentSessionStatus,
  useIsSessionActive,
} from "./session-hooks";

export type {
  SessionInfo,
  SessionStats,
  UserSessionsResponse,
} from "./session-hooks";

// Re-export types for convenience
export type {
  LoginCredentials,
  RegisterCredentials,
  UpdateProfileData,
  CreateUserRequest,
  RegisterUserData,
  AuthResponse,
  UserResponse,
  User,
  UsernameCheckResponse,
  TwoFactorResponse,
  TwoFactorVerifyLoginResponse,
  TwoFactorVerifyResponse,
  ForgotPasswordResponse,
  ResetPasswordResponse,
} from "@/types/auth";

export type { Session, ApiResponse } from "@/types/common";
