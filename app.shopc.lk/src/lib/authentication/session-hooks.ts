import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { ApiStatus } from "@/types/common";
import axios from "@/utils/axios";
import { getSessionMetadata } from "@/utils/session";

// Session types
export interface SessionInfo {
  sessionId: string;
  userId: string;
  source: string;
  deviceType: string;
  userAgent: string | null;
  ipAddress: string | null;
  firstSeenAt: Date;
  lastSeenAt: Date;
  status: "ACTIVE" | "LOGGED_OUT" | "EXPIRED" | "REVOKED";
  requestCount: number;
  country?: string | null;
  city?: string | null;
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
  revokedSessions: number;
  bySource: {
    WEB: number;
    MOBILE: number;
    API: number;
  };
  byDeviceType: {
    DESKTOP: number;
    MOBILE: number;
    TABLET: number;
    API_CLIENT: number;
    UNKNOWN: number;
  };
  uniqueDevices: number;
  uniqueIPs: number;
  averageSessionDuration: number;
}

export interface UserSessionsResponse {
  currentSessionId: string;
  sessions: SessionInfo[];
  totalActiveSessions: number;
}

// Query keys for sessions
export const sessionKeys = {
  all: ["sessions"] as const,
  list: () => [...sessionKeys.all, "list"] as const,
  stats: () => [...sessionKeys.all, "stats"] as const,
  detail: (sessionId: string) => [...sessionKeys.all, "detail", sessionId] as const,
};

// Get user's active sessions
export function useUserSessions() {
  return useQuery({
    queryKey: sessionKeys.list(),
    queryFn: async (): Promise<UserSessionsResponse> => {
      const response = await axios.get("/auth/sessions");
      if (response.data.status !== ApiStatus.SUCCESS) {
        throw new Error(response.data.message || "Failed to fetch sessions");
      }
      return response.data.data;
    },
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });
}

// Get session statistics
export function useSessionStats() {
  return useQuery({
    queryKey: sessionKeys.stats(),
    queryFn: async (): Promise<SessionStats> => {
      const response = await axios.get("/auth/sessions/stats");
      if (response.data.status !== ApiStatus.SUCCESS) {
        throw new Error(response.data.message || "Failed to fetch session stats");
      }
      return response.data.data;
    },
    staleTime: 60 * 1000, // 1 minute
  });
}

// Get specific session details
export function useSessionDetail(sessionId: string) {
  return useQuery({
    queryKey: sessionKeys.detail(sessionId),
    queryFn: async (): Promise<SessionInfo> => {
      const response = await axios.get(`/auth/sessions/${sessionId}`);
      if (response.data.status !== ApiStatus.SUCCESS) {
        throw new Error(response.data.message || "Failed to fetch session details");
      }
      return response.data.data;
    },
    enabled: !!sessionId,
  });
}

// Revoke a specific session
export function useRevokeSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ sessionId, reason }: { sessionId: string; reason?: string }) => {
      const response = await axios.delete(`/auth/sessions/${sessionId}`, {
        data: { reason: reason || "User initiated" }
      });
      if (response.data.status !== ApiStatus.SUCCESS) {
        throw new Error(response.data.message || "Failed to revoke session");
      }
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate sessions list and stats
      queryClient.invalidateQueries({ queryKey: sessionKeys.list() });
      queryClient.invalidateQueries({ queryKey: sessionKeys.stats() });
    },
  });
}

// Revoke all sessions except current
export function useRevokeAllSessions() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ reason }: { reason?: string } = {}) => {
      const metadata = getSessionMetadata();
      const response = await axios.post("/auth/sessions/revoke-all", {
        exceptSessionId: metadata.sessionId,
        reason: reason || "User initiated - revoke all"
      });
      if (response.data.status !== ApiStatus.SUCCESS) {
        throw new Error(response.data.message || "Failed to revoke all sessions");
      }
      return response.data.data;
    },
    onSuccess: () => {
      // Invalidate sessions list and stats
      queryClient.invalidateQueries({ queryKey: sessionKeys.list() });
      queryClient.invalidateQueries({ queryKey: sessionKeys.stats() });
    },
  });
}

// Hook to monitor current session status
export function useCurrentSessionStatus() {
  const metadata = getSessionMetadata();
  
  return useQuery({
    queryKey: sessionKeys.detail(metadata.sessionId),
    queryFn: async () => {
      try {
        const response = await axios.get(`/auth/sessions/${metadata.sessionId}`);
        if (response.data.status === ApiStatus.SUCCESS) {
          return response.data.data;
        }
        return null;
      } catch (error) {
        // Session might be invalid or expired
        console.error("Failed to fetch current session status:", error);
        return null;
      }
    },
    refetchInterval: 5 * 60 * 1000, // Check every 5 minutes
    retry: false, // Don't retry if session is invalid
  });
}

// Utility hook to check if current session is still active
export function useIsSessionActive() {
  const { data: session, isLoading } = useCurrentSessionStatus();
  
  return {
    isActive: session?.status === "ACTIVE",
    isLoading,
    session
  };
}