import { useCallback } from "react";
import { toast } from "@/hooks/use-toast";
import {
  useSmsTemplatesQuery,
  useSmsTemplatesSlimQuery,
  useSmsTemplateQuery,
  useCreateSmsTemplateMutation,
  useUpdateSmsTemplateMutation,
  useDeleteSmsTemplateMutation,
  useWhatsappTemplatesQuery,
  useWhatsappTemplatesSlimQuery,
  useWhatsappTemplateQuery,
  useCreateWhatsappTemplateMutation,
  useUpdateWhatsappTemplateMutation,
  useDeleteWhatsappTemplateMutation,
  useEmailTemplatesQuery,
  useEmailTemplatesSlimQuery,
  useEmailTemplateQuery,
  useCreateEmailTemplateMutation,
  useUpdateEmailTemplateMutation,
  useDeleteEmailTemplateMutation,
  useTemplateNameAvailabilityQuery,
} from "./queries";
import {
  CreateSmsTemplateDto,
  UpdateSmsTemplateDto,
  CreateWhatsappTemplateDto,
  UpdateWhatsappTemplateDto,
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
} from "@/types/template";
import {
  GetSmsTemplatesSchema,
  GetWhatsappTemplatesSchema,
  GetEmailTemplatesSchema,
} from "./validations";

// SMS Template Hooks
export const useSmsTemplates = (
  params?: GetSmsTemplatesSchema,
  isDemo = false
) => {
  const query = useSmsTemplatesQuery(params, isDemo);

  return {
    templates: query.data?.data?.data || [],
    total: query.data?.data?.total || 0,
    page: query.data?.data?.page || 1,
    limit: query.data?.data?.limit || 10,
    totalPages: query.data?.data?.totalPages || 0,
    hasNextPage: query.data?.data?.hasNextPage || false,
    hasPrevPage: query.data?.data?.hasPrevPage || false,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useSmsTemplatesSlim = (isDemo = false) => {
  const query = useSmsTemplatesSlimQuery(isDemo);

  return {
    templates: query.data?.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useSmsTemplate = (id: string, isDemo = false) => {
  const query = useSmsTemplateQuery(id, isDemo);

  return {
    template: query.data?.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useCreateSmsTemplate = (isDemo = false) => {
  const mutation = useCreateSmsTemplateMutation(isDemo);

  const createTemplate = useCallback(
    async (data: CreateSmsTemplateDto) => {
      try {
        const result = await mutation.mutateAsync(data);
        toast({
          title: "Success",
          description: "SMS template created successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to create SMS template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    createTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

export const useUpdateSmsTemplate = (isDemo = false) => {
  const mutation = useUpdateSmsTemplateMutation(isDemo);

  const updateTemplate = useCallback(
    async (id: string, data: UpdateSmsTemplateDto) => {
      try {
        const result = await mutation.mutateAsync({ id, data });
        toast({
          title: "Success",
          description: "SMS template updated successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to update SMS template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    updateTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

export const useDeleteSmsTemplate = (isDemo = false) => {
  const mutation = useDeleteSmsTemplateMutation(isDemo);

  const deleteTemplate = useCallback(
    async (id: string) => {
      try {
        const result = await mutation.mutateAsync(id);
        toast({
          title: "Success",
          description: "SMS template deleted successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to delete SMS template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    deleteTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

// WhatsApp Template Hooks
export const useWhatsappTemplates = (
  params?: GetWhatsappTemplatesSchema,
  isDemo = false
) => {
  const query = useWhatsappTemplatesQuery(params, isDemo);

  return {
    templates: query.data?.data?.data || [],
    total: query.data?.data?.total || 0,
    page: query.data?.data?.page || 1,
    limit: query.data?.data?.limit || 10,
    totalPages: query.data?.data?.totalPages || 0,
    hasNextPage: query.data?.data?.hasNextPage || false,
    hasPrevPage: query.data?.data?.hasPrevPage || false,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useWhatsappTemplatesSlim = (isDemo = false) => {
  const query = useWhatsappTemplatesSlimQuery(isDemo);

  return {
    templates: query.data?.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useWhatsappTemplate = (id: string, isDemo = false) => {
  const query = useWhatsappTemplateQuery(id, isDemo);

  return {
    template: query.data?.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useCreateWhatsappTemplate = (isDemo = false) => {
  const mutation = useCreateWhatsappTemplateMutation(isDemo);

  const createTemplate = useCallback(
    async (data: CreateWhatsappTemplateDto, media?: File) => {
      try {
        const result = await mutation.mutateAsync({ data, media });
        toast({
          title: "Success",
          description: "WhatsApp template created successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to create WhatsApp template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    createTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

export const useUpdateWhatsappTemplate = (isDemo = false) => {
  const mutation = useUpdateWhatsappTemplateMutation(isDemo);

  const updateTemplate = useCallback(
    async (id: string, data: UpdateWhatsappTemplateDto, media?: File) => {
      try {
        const result = await mutation.mutateAsync({ id, data, media });
        toast({
          title: "Success",
          description: "WhatsApp template updated successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to update WhatsApp template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    updateTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

export const useDeleteWhatsappTemplate = (isDemo = false) => {
  const mutation = useDeleteWhatsappTemplateMutation(isDemo);

  const deleteTemplate = useCallback(
    async (id: string) => {
      try {
        const result = await mutation.mutateAsync(id);
        toast({
          title: "Success",
          description: "WhatsApp template deleted successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to delete WhatsApp template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    deleteTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

// Email Template Hooks
export const useEmailTemplates = (
  params?: GetEmailTemplatesSchema,
  isDemo = false
) => {
  const query = useEmailTemplatesQuery(params, isDemo);

  return {
    templates: query.data?.data?.data || [],
    total: query.data?.data?.total || 0,
    page: query.data?.data?.page || 1,
    limit: query.data?.data?.limit || 10,
    totalPages: query.data?.data?.totalPages || 0,
    hasNextPage: query.data?.data?.hasNextPage || false,
    hasPrevPage: query.data?.data?.hasPrevPage || false,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useEmailTemplatesSlim = (isDemo = false) => {
  const query = useEmailTemplatesSlimQuery(isDemo);

  return {
    templates: query.data?.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useEmailTemplate = (id: string, isDemo = false) => {
  const query = useEmailTemplateQuery(id, isDemo);

  return {
    template: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
};

export const useCreateEmailTemplate = (isDemo = false) => {
  const mutation = useCreateEmailTemplateMutation(isDemo);

  const createTemplate = useCallback(
    async (data: CreateEmailTemplateDto, attachments?: File[]) => {
      try {
        const result = await mutation.mutateAsync({ data, attachments });
        toast({
          title: "Success",
          description: "Email template created successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to create Email template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    createTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

export const useUpdateEmailTemplate = (isDemo = false) => {
  const mutation = useUpdateEmailTemplateMutation(isDemo);

  const updateTemplate = useCallback(
    async (id: string, data: UpdateEmailTemplateDto, attachments?: File[]) => {
      try {
        const result = await mutation.mutateAsync({ id, data, attachments });
        toast({
          title: "Success",
          description: "Email template updated successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to update Email template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    updateTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

export const useDeleteEmailTemplate = (isDemo = false) => {
  const mutation = useDeleteEmailTemplateMutation(isDemo);

  const deleteTemplate = useCallback(
    async (id: string) => {
      try {
        const result = await mutation.mutateAsync(id);
        toast({
          title: "Success",
          description: "Email template deleted successfully",
        });
        return result;
      } catch (error: any) {
        toast({
          title: "Error",
          description: error.message || "Failed to delete Email template",
          variant: "destructive",
        });
        throw error;
      }
    },
    [mutation]
  );

  return {
    deleteTemplate,
    isLoading: mutation.isPending,
    isError: mutation.isError,
    error: mutation.error,
  };
};

// Utility Hooks
// export const useTemplateNameAvailability = (
//   name: string,
//   type: "sms" | "whatsapp" | "email",
//   excludeId?: string,
//   isDemo = false
// ) => {
//   const query = useTemplateNameAvailabilityQuery(name, type, excludeId, isDemo);

//   return {
//     isAvailable: query.data?.available,
//     message: query.data?.message,
//     isLoading: query.isLoading,
//     isError: query.isError,
//     error: query.error,
//   };
// };
