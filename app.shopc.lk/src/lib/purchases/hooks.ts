import {
  useQuery,
  useMutation,
  UseQueryResult,
  useQueryClient,
  UseMutationResult,
} from "@tanstack/react-query";
import { ApiResponse, ApiStatus } from "@/types/api";
import {
  Purchase,
  PurchasePaginatedResponse,
  SimplePurchaseResponse,
} from "@/types/purchases";
import {
  getPurchasesTableData,
  getPurchaseById,
  createPurchase,
  updatePurchase,
  deletePurchase,
  getSimplePurchases,
  getAllPurchases,
  getPurchase,
} from "./queries";
import {
  GetPurchasesSchema,
  CreatePurchaseSchema,
  UpdatePurchaseSchema,
} from "./validations";
import * as z from "zod";
import { toast } from "@/hooks/use-toast";

// Query keys for caching and invalidation
export const purchaseKeys = {
  all: ["purchases"] as const,
  lists: () => [...purchaseKeys.all, "list"] as const,
  list: (filters: GetPurchasesSchema) =>
    [...purchaseKeys.lists(), { ...filters }] as const,
  details: () => [...purchaseKeys.all, "detail"] as const,
  detail: (id: string) => [...purchaseKeys.details(), id] as const,
  simple: () => [...purchaseKeys.all, "simple"] as const,
};

/**
 * Hook for fetching paginated purchases data
 */
export function usePurchasesData(
  params: GetPurchasesSchema,
  isDemo: boolean
): UseQueryResult<ApiResponse<PurchasePaginatedResponse | null>> {
  return useQuery({
    queryKey: purchaseKeys.list(params),
    queryFn: () => getAllPurchases(params, isDemo),
    staleTime: 0,
    refetchOnMount: true,
  });
}

/**
 * Hook for fetching a purchase by ID
 */
export function usePurchase(
  id: string | undefined,
  isDemo: boolean
): UseQueryResult<ApiResponse<Purchase | null>> {
  return useQuery({
    queryKey: purchaseKeys.detail(id || "empty"),
    queryFn: () => getPurchase(id!, isDemo),
    enabled: !!id,
    staleTime: 0,
  });
}

/**
 * Hook for fetching simple purchase list (for dropdowns)
 */
export function useSimplePurchases(
  isDemo: boolean
): UseQueryResult<ApiResponse<SimplePurchaseResponse | null>> {
  return useQuery({
    queryKey: purchaseKeys.simple(),
    queryFn: () => getSimplePurchases(isDemo),
    staleTime: 60000, // 1 minute
  });
}

/**
 * Hook for creating a purchase
 */
export function useCreatePurchase(
  isDemo: boolean
): UseMutationResult<
  ApiResponse<Purchase | null>,
  Error,
  CreatePurchaseSchema,
  unknown
> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePurchaseSchema) => createPurchase(data, isDemo),
    onSuccess: () => {
      // Invalidate purchases list queries
      queryClient.invalidateQueries({ queryKey: purchaseKeys.lists(data) });
      queryClient.invalidateQueries({ queryKey: purchaseKeys.simple() });
    },
  });
}

/**
 * Hook for updating a purchase
 */
export function useUpdatePurchase(
  isDemo: boolean
): UseMutationResult<
  ApiResponse<Purchase | null>,
  Error,
  UpdatePurchaseSchema,
  unknown
> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdatePurchaseSchema) => updatePurchase(data, isDemo),
    onSuccess: (_, variables) => {
      // Invalidate the updated purchase query and lists
      queryClient.invalidateQueries({
        queryKey: purchaseKeys.detail(variables._id),
      });
      queryClient.invalidateQueries({ queryKey: purchaseKeys.lists(data) });
      queryClient.invalidateQueries({ queryKey: purchaseKeys.simple() });
    },
  });
}

/**
 * Hook for deleting a purchase
 */
export function useDeletePurchase(
  isDemo: boolean
): UseMutationResult<ApiResponse<null>, Error, string, unknown> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deletePurchase(id, isDemo),
    onSuccess: (_, id) => {
      // Invalidate the deleted purchase query and lists
      queryClient.invalidateQueries({ queryKey: purchaseKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: purchaseKeys.lists({}) });
      queryClient.invalidateQueries({ queryKey: purchaseKeys.simple() });
    },
  });
}

export function useDeletePurchases(isDemo: boolean = false) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (ids: string[]) => {
      const results = await Promise.all(
        ids.map((id) => deletePurchase(id, isDemo))
      );
      const failedResults = results.filter(
        (result) => result.status === ApiStatus.FAIL
      );

      if (failedResults.length > 0) {
        throw new Error(`Failed to delete ${failedResults.length} purchase(s)`);
      }

      return {
        status: ApiStatus.SUCCESS,
        data: null,
        message: `Successfully deleted ${ids.length} purchase(s)`,
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: purchaseKeys.lists({}) });
    },
  });
}

// Validation schema
export function usePurchaseValidationSchema() {
  const purchaseItemSchema = z.object({
    _id: z.string().optional(),
    productId: z.string().min(1, "Product is required"),
    productName: z.string().optional(),
    quantity: z.number().min(1, "Quantity must be at least 1"),
    unitPrice: z.number().min(0, "Unit price must be non-negative"),
    discount: z.number().min(0, "Discount must be non-negative").optional(),
    tax: z.number().min(0, "Tax must be non-negative").optional(),
    total: z.number(),
  });

  return z.object({
    _id: z.string().optional(),
    date: z.date({
      required_error: "Date is required",
    }),
    referenceNo: z.string().optional(),
    locationId: z.string().min(1, "Location is required"),
    supplierId: z.string().min(1, "Supplier is required"),
    purchaseStatus: z.string().min(1, "Purchase status is required"),
    paymentStatus: z.string().min(1, "Payment status is required"),
    grandTotal: z.number(),
    paymentDue: z.number(),
    items: z.array(purchaseItemSchema).min(1, "At least one item is required"),
    notes: z.string().optional(),
  });
}

// Suppliers, Locations, and Products queries
// These would typically be in their own respective files, but for simplicity we'll define them here
export interface Supplier {
  _id: string;
  name: string;
}

export interface Location {
  _id: string;
  name: string;
}

export interface Product {
  _id: string;
  name: string;
  price?: number;
}

// Demo data for suppliers, locations, and products
const demoSuppliers: Supplier[] = [
  { _id: "sup1", name: "Supplier A" },
  { _id: "sup2", name: "Supplier B" },
  { _id: "sup3", name: "Supplier C" },
];

const demoLocations: Location[] = [
  { _id: "loc1", name: "Warehouse A" },
  { _id: "loc2", name: "Warehouse B" },
  { _id: "loc3", name: "Store Front" },
];

const demoProducts: Product[] = [
  { _id: "prod1", name: "Product A", price: 10.99 },
  { _id: "prod2", name: "Product B", price: 24.99 },
  { _id: "prod3", name: "Product C", price: 5.99 },
  { _id: "prod4", name: "Product D", price: 19.99 },
];

// Query keys for related entities
const supplierKeys = {
  all: ["suppliers"] as const,
};

const locationKeys = {
  all: ["locations"] as const,
};

const productKeys = {
  all: ["products"] as const,
};

// Supplier queries
export function useSuppliers(isDemo: boolean = false) {
  return useQuery<ApiResponse<Supplier[]>>({
    queryKey: supplierKeys.all,
    queryFn: async () => {
      if (isDemo) {
        return {
          status: ApiStatus.SUCCESS,
          message: null,
          data: demoSuppliers,
        };
      } else {
        // Implement actual API call here
        // For now, return demo data
        return {
          status: ApiStatus.SUCCESS,
          message: null,
          data: demoSuppliers,
        };
      }
    },
  });
}

// Location queries
export function useLocations(isDemo: boolean = false) {
  return useQuery<ApiResponse<Location[]>>({
    queryKey: locationKeys.all,
    queryFn: async () => {
      if (isDemo) {
        return {
          status: ApiStatus.SUCCESS,
          message: null,
          data: demoLocations,
        };
      } else {
        // Implement actual API call here
        // For now, return demo data
        return {
          status: ApiStatus.SUCCESS,
          message: null,
          data: demoLocations,
        };
      }
    },
  });
}

// Product queries
export function useProducts(isDemo: boolean = false) {
  return useQuery<ApiResponse<Product[]>>({
    queryKey: productKeys.all,
    queryFn: async () => {
      if (isDemo) {
        return {
          status: ApiStatus.SUCCESS,
          message: null,
          data: demoProducts,
        };
      } else {
        // Implement actual API call here
        // For now, return demo data
        return {
          status: ApiStatus.SUCCESS,
          message: null,
          data: demoProducts,
        };
      }
    },
  });
}
