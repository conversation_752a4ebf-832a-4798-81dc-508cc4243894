import { z } from "zod";
import {
  WorkOrderStatus,
  WorkOrderPriority,
  QualityInspectionResult,
} from "@/types/work-order";

// Backend DTO: CreateWorkOrderDto
export const createWorkOrderSchema = z.object({
  workOrderNumber: z.string().min(1, "Work order number is required"),
  productId: z.string().uuid("Invalid product ID"),
  variantId: z.string().uuid("Invalid variant ID").optional(),
  bomId: z.string().uuid("Invalid BOM ID"),
  quantityToProduce: z.string().min(1, "Quantity to produce is required"),
  quantityProduced: z.string().optional(),
  quantityScrapped: z.string().optional(),
  statusId: z.string().uuid("Invalid status ID"),
  priorityId: z.string().uuid("Invalid priority ID"),
  plannedStartDate: z.string().optional(),
  plannedEndDate: z.string().optional(),
  actualStartDate: z.string().optional(),
  actualEndDate: z.string().optional(),
  notes: z.string().optional(),
  qualityInspectionRequired: z.boolean().optional().default(false),
  qualityInspectionCompleted: z.boolean().optional().default(false),
  qualityInspectionResult: z.string().optional(),
  qualityInspectionNotes: z.string().optional(),
  inspectedBy: z.string().uuid("Invalid inspector ID").optional(),
  salesOrderIds: z.array(z.string().uuid("Invalid sales order ID")).optional(),
  staffIds: z.array(z.string().uuid("Invalid staff ID")).optional(),
  // Staff assignments with optional tasks
  staffAssignments: z.array(z.object({
    staffId: z.string().uuid("Invalid staff ID"),
    createTask: z.boolean().optional().default(false),
    taskTitle: z.string().optional(),
    taskDescription: z.string().optional(),
    taskDueDate: z.string().optional(),
    taskPriority: z.string().optional(),
  })).optional(),
  // Task creation fields
  createTask: z.boolean().optional().default(false),
  taskTitle: z.string().optional(),
  taskDescription: z.string().optional(),
  taskDueDate: z.string().optional(),
  taskPriority: z.string().optional(),
  taskAssignedTo: z.string().uuid("Invalid assignee ID").optional(),
});

// Backend DTO: UpdateWorkOrderDto
export const updateWorkOrderSchema = z.object({
  workOrderNumber: z
    .string()
    .min(1, "Work order number is required")
    .optional(),
  productId: z.string().uuid("Invalid product ID").optional(),
  variantId: z.string().uuid("Invalid variant ID").optional(),
  bomId: z.string().uuid("Invalid BOM ID").optional(),
  quantityToProduce: z
    .string()
    .min(1, "Quantity to produce is required")
    .optional(),
  quantityProduced: z.string().optional(),
  quantityScrapped: z.string().optional(),
  statusId: z.string().uuid("Invalid status ID").optional(),
  priorityId: z.string().uuid("Invalid priority ID").optional(),
  plannedStartDate: z.string().optional(),
  plannedEndDate: z.string().optional(),
  actualStartDate: z.string().optional(),
  actualEndDate: z.string().optional(),
  notes: z.string().optional(),
  qualityInspectionRequired: z.boolean().optional(),
  qualityInspectionCompleted: z.boolean().optional(),
  qualityInspectionResult: z.string().optional(),
  qualityInspectionNotes: z.string().optional(),
  inspectedBy: z.string().uuid("Invalid inspector ID").optional(),
  salesOrderIds: z.array(z.string().uuid("Invalid sales order ID")).optional(),
  staffIds: z.array(z.string().uuid("Invalid staff ID")).optional(),
  // Staff assignments with optional tasks
  staffAssignments: z.array(z.object({
    staffId: z.string().uuid("Invalid staff ID"),
    createTask: z.boolean().optional().default(false),
    taskTitle: z.string().optional(),
    taskDescription: z.string().optional(),
    taskDueDate: z.string().optional(),
    taskPriority: z.string().optional(),
  })).optional(),
  // Task update fields
  taskTitle: z.string().optional(),
  taskDescription: z.string().optional(),
  taskDueDate: z.string().optional(),
  taskPriority: z.string().optional(),
  taskAssignedTo: z.string().uuid("Invalid assignee ID").optional(),
});

// Schema for getting work orders with pagination and filters
export const getWorkOrdersSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).default(10),
  from: z.string().optional(),
  to: z.string().optional(),
  workOrderNumber: z.string().optional(),
  statusId: z.string().uuid("Invalid status ID").optional(),
  priorityId: z.string().uuid("Invalid priority ID").optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Bulk create schema for work orders
export const bulkCreateWorkOrdersSchema = z.object({
  workOrders: z.string().min(1, "Work orders data is required"), // JSON string
});

// Schema for bulk delete
export const bulkDeleteWorkOrdersSchema = z.object({
  workOrderIds: z
    .array(z.string().uuid("Invalid work order ID"))
    .min(1, "At least one work order ID is required"),
});

// Work Order Task Schemas
export const createWorkOrderTaskSchema = z.object({
  title: z.string().min(1, "Task title is required"),
  description: z.string().optional(),
  priority: z.string().optional(),
  assignedTo: z.string().uuid("Invalid assignee ID").optional(),
  dueDate: z.string().optional(),
});

export const updateWorkOrderTaskSchema = z.object({
  title: z.string().min(1, "Task title is required").optional(),
  description: z.string().optional(),
  priority: z.string().optional(),
  assignedTo: z.string().uuid("Invalid assignee ID").optional(),
  dueDate: z.string().optional(),
});

// Form schema for UI components (includes additional fields for form handling)
export const workOrderFormSchema = z.object({
  workOrderNumber: z.string().min(1, "Work order number is required"),
  productId: z.string().uuid("Invalid product ID"),
  variantId: z.string().uuid("Invalid variant ID").optional(),
  bomId: z.string().uuid("Invalid BOM ID"),
  quantityToProduce: z.string().min(1, "Quantity to produce is required"),
  quantityProduced: z.string().optional(),
  quantityScrapped: z.string().optional(),
  statusId: z.string().uuid("Invalid status ID"),
  priorityId: z.string().uuid("Invalid priority ID"),
  plannedStartDate: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      const date = new Date(val);
      return !isNaN(date.getTime());
    }, "Invalid planned start date"),
  plannedEndDate: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      const date = new Date(val);
      return !isNaN(date.getTime());
    }, "Invalid planned end date"),
  actualStartDate: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      const date = new Date(val);
      return !isNaN(date.getTime());
    }, "Invalid actual start date"),
  actualEndDate: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      const date = new Date(val);
      return !isNaN(date.getTime());
    }, "Invalid actual end date"),
  notes: z.string().optional(),
  qualityInspectionRequired: z.boolean().optional().default(false),
  qualityInspectionCompleted: z.boolean().optional().default(false),
  qualityInspectionResult: z.string().optional(),
  qualityInspectionNotes: z.string().optional(),
  inspectedBy: z.string().uuid("Invalid inspector ID").optional(),
  salesOrderIds: z.array(z.string().uuid("Invalid sales order ID")).optional(),
  staffIds: z.array(z.string().uuid("Invalid staff ID")).optional(),
  // Staff assignments with optional tasks
  staffAssignments: z.array(z.object({
    staffId: z.string().uuid("Invalid staff ID"),
    createTask: z.boolean().optional().default(false),
    taskTitle: z.string().optional(),
    taskDescription: z.string().optional(),
    taskDueDate: z.string()
      .optional()
      .refine((val) => {
        if (!val) return true; // Optional field
        const date = new Date(val);
        return !isNaN(date.getTime());
      }, "Invalid task due date"),
    taskPriority: z.string().optional(),
  })).optional().default([]),
  // Legacy task fields (for backward compatibility)
  createTask: z.boolean().optional().default(false),
  taskTitle: z.string().optional(),
  taskDescription: z.string().optional(),
  taskDueDate: z.string()
    .optional()
    .refine((val) => {
      if (!val) return true; // Optional field
      const date = new Date(val);
      return !isNaN(date.getTime());
    }, "Invalid task due date"),
  taskPriority: z.string().optional(),
  taskAssignedTo: z.string().uuid("Invalid assignee ID").optional(),
})
.refine((data) => {
  // Validate that planned end date is after planned start date
  if (data.plannedStartDate && data.plannedEndDate) {
    const startDate = new Date(data.plannedStartDate);
    const endDate = new Date(data.plannedEndDate);
    return endDate > startDate;
  }
  return true;
}, {
  message: "Planned end date must be after planned start date",
  path: ["plannedEndDate"],
})
.refine((data) => {
  // Validate that actual end date is after actual start date
  if (data.actualStartDate && data.actualEndDate) {
    const startDate = new Date(data.actualStartDate);
    const endDate = new Date(data.actualEndDate);
    return endDate > startDate;
  }
  return true;
}, {
  message: "Actual end date must be after actual start date",
  path: ["actualEndDate"],
})
.refine((data) => {
  // Validate that actual start date is not before planned start date (with warning)
  if (data.plannedStartDate && data.actualStartDate) {
    const plannedStart = new Date(data.plannedStartDate);
    const actualStart = new Date(data.actualStartDate);
    // Allow some flexibility - actual can be up to 1 day before planned
    const oneDayBefore = new Date(plannedStart.getTime() - 24 * 60 * 60 * 1000);
    return actualStart >= oneDayBefore;
  }
  return true;
}, {
  message: "Actual start date should not be significantly before planned start date",
  path: ["actualStartDate"],
})
.refine((data) => {
  // Validate that task due date is not in the past
  if (data.createTask && data.taskDueDate) {
    const dueDate = new Date(data.taskDueDate);
    const now = new Date();
    // Allow task due dates up to 1 hour in the past to account for timezone differences
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    return dueDate >= oneHourAgo;
  }
  return true;
}, {
  message: "Task due date cannot be in the past",
  path: ["taskDueDate"],
})
.refine((data) => {
  // Validate staff assignment tasks
  if (data.staffAssignments && data.staffAssignments.length > 0) {
    for (let i = 0; i < data.staffAssignments.length; i++) {
      const assignment = data.staffAssignments[i];
      if (assignment.createTask && assignment.taskDueDate) {
        const dueDate = new Date(assignment.taskDueDate);
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        if (dueDate < oneHourAgo) {
          return false;
        }
      }
    }
  }
  return true;
}, {
  message: "Staff assignment task due dates cannot be in the past",
  path: ["staffAssignments"],
});

// Schema for work order number availability check
export const checkWorkOrderNumberSchema = z.object({
  workOrderNumber: z.string().min(1, "Work order number is required"),
});

// Type exports for use in other files
export type CreateWorkOrderSchema = z.infer<typeof createWorkOrderSchema>;
export type UpdateWorkOrderSchema = z.infer<typeof updateWorkOrderSchema>;
export type GetWorkOrdersSchema = z.infer<typeof getWorkOrdersSchema>;
export type BulkCreateWorkOrdersSchema = z.infer<
  typeof bulkCreateWorkOrdersSchema
>;
export type BulkDeleteWorkOrdersSchema = z.infer<
  typeof bulkDeleteWorkOrdersSchema
>;
export type CreateWorkOrderTaskSchema = z.infer<
  typeof createWorkOrderTaskSchema
>;
export type UpdateWorkOrderTaskSchema = z.infer<
  typeof updateWorkOrderTaskSchema
>;
export type WorkOrderFormSchema = z.infer<typeof workOrderFormSchema>;
export type CheckWorkOrderNumberSchema = z.infer<
  typeof checkWorkOrderNumberSchema
>;
