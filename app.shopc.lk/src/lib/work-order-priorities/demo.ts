import {
  WorkOrderPriorityDto,
  WorkOrderPrioritySlimDto,
  WorkOrderPriorityListDto,
  WorkOrderPriorityTableData,
  CreateWorkOrderPriorityDto,
  UpdateWorkOrderPriorityDto,
  BulkCreateWorkOrderPriorityItemDto,
  WorkOrderPriorityIdResponse,
  BulkWorkOrderPriorityIdsResponse,
  BulkDeleteWorkOrderPriorityResponse,
  WorkOrderPriorityCodeAvailabilityResponse,
  WorkOrderPriorityNameAvailabilityResponse,
  WorkOrderPriorityResponse,
  SimpleWorkOrderPriorityResponse,
  WorkOrderPriorityPaginatedResponse,
  UpdateWorkOrderPriorityPositionsResponse,
  UpdateWorkOrderPriorityPositionsDto,
  PaginatedWorkOrderPrioritiesResponseDto,
  BulkUpdateWorkOrderPriorityStatusDto,
  BulkUpdateWorkOrderPriorityStatusResponse,
  BulkDeleteWorkOrderPriorityDto,
} from "@/types/work-order-priority";
import { ApiStatus } from "@/types/common";
import { GetWorkOrderPrioritiesSchema, BulkImportWorkOrderPrioritiesSchema } from "./validations";

// Raw demo work order priorities data
const rawDemoWorkOrderPriorities = [
  {
    id: "wop_1",
    businessId: "biz_1",
    priorityCode: "CRITICAL",
    priorityName: "Critical Priority",
    description: "Critical issues requiring immediate attention within 1 hour",
    colorCode: "#DC2626",
    iconName: "alert-triangle",
    severityLevel: 10,
    position: 1,
    isActive: true,
    isDefault: false,
    escalationHours: 1,
    createdBy: "John Doe",
    createdAt: new Date("2023-01-01T10:00:00Z"),
    updatedAt: new Date("2023-01-01T10:00:00Z"),
  },
  {
    id: "wop_2",
    businessId: "biz_1",
    priorityCode: "HIGH",
    priorityName: "High Priority",
    description: "High priority issues requiring attention within 4 hours",
    colorCode: "#EA580C",
    iconName: "alert-circle",
    severityLevel: 8,
    position: 2,
    isActive: true,
    isDefault: true,
    escalationHours: 4,
    createdBy: "Jane Smith",
    createdAt: new Date("2023-01-02T09:00:00Z"),
    updatedAt: new Date("2023-01-02T09:00:00Z"),
  },
  {
    id: "wop_3",
    businessId: "biz_1",
    priorityCode: "MEDIUM",
    priorityName: "Medium Priority",
    description: "Medium priority issues requiring attention within 24 hours",
    colorCode: "#D97706",
    iconName: "info",
    severityLevel: 5,
    position: 3,
    isActive: true,
    isDefault: false,
    escalationHours: 24,
    createdBy: "Mike Johnson",
    createdAt: new Date("2023-01-03T08:00:00Z"),
    updatedAt: new Date("2023-01-03T08:00:00Z"),
  },
  {
    id: "wop_4",
    businessId: "biz_1",
    priorityCode: "LOW",
    priorityName: "Low Priority",
    description: "Low priority issues requiring attention within 72 hours",
    colorCode: "#16A34A",
    iconName: "minus-circle",
    severityLevel: 2,
    position: 4,
    isActive: true,
    isDefault: false,
    escalationHours: 72,
    createdBy: "Sarah Wilson",
    createdAt: new Date("2023-01-04T07:00:00Z"),
    updatedAt: new Date("2023-01-04T07:00:00Z"),
  },
  {
    id: "wop_5",
    businessId: "biz_1",
    priorityCode: "URGENT",
    priorityName: "Urgent Priority",
    description:
      "Urgent issues requiring immediate attention within 30 minutes",
    colorCode: "#7C2D12",
    iconName: "zap",
    severityLevel: 9,
    position: 5,
    isActive: false,
    isDefault: false,
    escalationHours: 0.5,
    createdBy: "David Brown",
    createdAt: new Date("2023-01-05T06:00:00Z"),
    updatedAt: new Date("2023-01-05T06:00:00Z"),
  },
];

// In-memory storage for demo data
let demoWorkOrderPriorities: WorkOrderPriorityListDto[] = [
  ...rawDemoWorkOrderPriorities,
];

// Helper function to simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Helper function to filter work order priorities based on search parameters
function filterWorkOrderPriorities(
  workOrderPriorities: WorkOrderPriorityListDto[],
  params: GetWorkOrderPrioritiesSchema
): WorkOrderPriorityListDto[] {
  let filtered = [...workOrderPriorities];

  // Filter by priority code
  if (params.priorityCode) {
    filtered = filtered.filter((priority) =>
      priority.priorityCode
        .toLowerCase()
        .includes(params.priorityCode!.toLowerCase())
    );
  }

  // Filter by priority name
  if (params.priorityName) {
    filtered = filtered.filter((priority) =>
      priority.priorityName
        .toLowerCase()
        .includes(params.priorityName!.toLowerCase())
    );
  }

  // Filter by severity level
  if (params.severityLevel) {
    filtered = filtered.filter(
      (priority) => priority.severityLevel.toString() === params.severityLevel
    );
  }

  // Filter by active status
  if (params.isActive !== undefined) {
    const isActive = params.isActive === "true";
    filtered = filtered.filter((priority) => priority.isActive === isActive);
  }

  // Filter by date range
  if (params.from || params.to) {
    const fromDate = params.from ? new Date(params.from) : null;
    const toDate = params.to ? new Date(params.to) : null;

    filtered = filtered.filter((priority) => {
      const createdAt = new Date(priority.createdAt);
      if (fromDate && createdAt < fromDate) return false;
      if (toDate && createdAt > toDate) return false;
      return true;
    });
  }

  return filtered;
}

// Helper function to sort work order priorities
function sortWorkOrderPriorities(
  workOrderPriorities: WorkOrderPriorityListDto[],
  sort?: string
): WorkOrderPriorityListDto[] {
  if (!sort) {
    return workOrderPriorities.sort((a, b) => a.position - b.position);
  }

  const [field, direction] = sort.split(":");
  const isDesc = direction === "desc";

  return workOrderPriorities.sort((a, b) => {
    let aValue: any = a[field as keyof WorkOrderPriorityListDto];
    let bValue: any = b[field as keyof WorkOrderPriorityListDto];

    // Handle date fields
    if (field === "createdAt" || field === "updatedAt") {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // Handle string fields
    if (typeof aValue === "string" && typeof bValue === "string") {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) return isDesc ? 1 : -1;
    if (aValue > bValue) return isDesc ? -1 : 1;
    return 0;
  });
}

// Demo API functions
export async function getDemoWorkOrderPrioritiesTableDataApi(
  params: GetWorkOrderPrioritiesSchema
): Promise<WorkOrderPriorityPaginatedResponse> {
  await delay(300);

  const filtered = filterWorkOrderPriorities(demoWorkOrderPriorities, params);
  const sorted = sortWorkOrderPriorities(filtered, params.sort);

  const page = params.page || 1;
  const limit = params.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  const paginatedData = sorted.slice(startIndex, endIndex);
  const total = sorted.length;
  const totalPages = Math.ceil(total / limit);

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: {
      data: paginatedData,
      meta: {
        total,
        page,
        totalPages,
      },
    },
  };
}

export async function getDemoWorkOrderPrioritiesSlimApi(): Promise<SimpleWorkOrderPriorityResponse> {
  await delay(200);

  const activeWorkOrderPriorities = demoWorkOrderPriorities
    .filter((priority) => priority.isActive)
    .sort((a, b) => a.position - b.position)
    .map((priority) => ({
      id: priority.id,
      priorityName: priority.priorityName,
      priorityCode: priority.priorityCode,
      severityLevel: priority.severityLevel,
      position: priority.position,
      colorCode: priority.colorCode,
      iconName: priority.iconName,
      isActive: priority.isActive,
    }));

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: activeWorkOrderPriorities,
  };
}

export async function getDemoWorkOrderPriorityApi(
  id: string
): Promise<WorkOrderPriorityResponse> {
  await delay(200);

  const workOrderPriority = demoWorkOrderPriorities.find(
    (priority) => priority.id === id
  );

  if (!workOrderPriority) {
    return {
      status: ApiStatus.ERROR,
      message: "Work order priority not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: workOrderPriority,
  };
}

export async function checkDemoWorkOrderPriorityCodeAvailabilityApi(
  priorityCode: string,
  excludeId?: string
): Promise<WorkOrderPriorityCodeAvailabilityResponse> {
  await delay(150);

  const existingPriority = demoWorkOrderPriorities.find(
    (priority) =>
      priority.priorityCode.toLowerCase() === priorityCode.toLowerCase() &&
      priority.id !== excludeId
  );

  const available = !existingPriority;

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: {
      available,
      message: available
        ? "Priority code is available"
        : "Priority code is already in use",
    },
  };
}

export async function checkDemoWorkOrderPriorityNameAvailabilityApi(
  priorityName: string,
  excludeId?: string
): Promise<WorkOrderPriorityNameAvailabilityResponse> {
  await delay(150);

  const existingPriority = demoWorkOrderPriorities.find(
    (priority) =>
      priority.priorityName.toLowerCase() === priorityName.toLowerCase() &&
      priority.id !== excludeId
  );

  const available = !existingPriority;

  return {
    status: ApiStatus.SUCCESS,
    message: null,
    data: {
      available,
      message: available
        ? "Priority name is available"
        : "Priority name is already in use",
    },
  };
}

export async function createDemoWorkOrderPriorityApi(
  data: CreateWorkOrderPriorityDto
): Promise<WorkOrderPriorityIdResponse> {
  await delay(500);

  const newId = `wop_${Date.now()}`;
  const maxPosition = Math.max(
    ...demoWorkOrderPriorities.map((p) => p.position),
    0
  );

  const newWorkOrderPriority: WorkOrderPriorityListDto = {
    id: newId,
    businessId: "biz_1",
    priorityCode: data.priorityCode,
    priorityName: data.priorityName,
    description: data.description,
    colorCode: data.colorCode,
    iconName: data.iconName,
    severityLevel: data.severityLevel,
    position: maxPosition + 1,
    isActive: data.isActive ?? true,
    isDefault: data.isDefault ?? false,
    escalationHours: data.escalationHours,
    createdBy: "Demo User",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  demoWorkOrderPriorities.push(newWorkOrderPriority);

  return {
    status: ApiStatus.SUCCESS,
    message: "Work order priority created successfully",
    data: { id: newId },
  };
}

export async function bulkCreateDemoWorkOrderPrioritiesApi(
  workOrderPriorities: BulkCreateWorkOrderPriorityItemDto[]
): Promise<BulkWorkOrderPriorityIdsResponse> {
  await delay(800);

  const ids: string[] = [];
  let maxPosition = Math.max(
    ...demoWorkOrderPriorities.map((p) => p.position),
    0
  );

  for (const priorityData of workOrderPriorities) {
    const newId = `wop_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    maxPosition += 1;

    const newWorkOrderPriority: WorkOrderPriorityListDto = {
      id: newId,
      businessId: "biz_1",
      priorityCode: priorityData.priorityCode,
      priorityName: priorityData.priorityName,
      description: priorityData.description,
      colorCode: priorityData.colorCode,
      iconName: priorityData.iconName,
      severityLevel: priorityData.severityLevel,
      position: maxPosition,
      isActive: priorityData.isActive ?? true,
      isDefault: priorityData.isDefault ?? false,
      escalationHours: priorityData.escalationHours,
      createdBy: "Demo User",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    demoWorkOrderPriorities.push(newWorkOrderPriority);
    ids.push(newId);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${ids.length} work order priorities created successfully`,
    data: { ids },
  };
}

export async function updateDemoWorkOrderPriorityApi(
  id: string,
  data: UpdateWorkOrderPriorityDto
): Promise<WorkOrderPriorityIdResponse> {
  await delay(400);

  const index = demoWorkOrderPriorities.findIndex(
    (priority) => priority.id === id
  );

  if (index === -1) {
    return {
      status: ApiStatus.ERROR,
      message: "Work order priority not found",
      data: null,
    };
  }

  // Update the work order priority
  demoWorkOrderPriorities[index] = {
    ...demoWorkOrderPriorities[index],
    ...data,
    updatedAt: new Date(),
  };

  return {
    status: ApiStatus.SUCCESS,
    message: "Work order priority updated successfully",
    data: { id },
  };
}

export async function updateDemoWorkOrderPriorityPositionsApi(
  data: UpdateWorkOrderPriorityPositionsDto
): Promise<UpdateWorkOrderPriorityPositionsResponse> {
  await delay(300);

  let updated = 0;

  for (const update of data.updates) {
    const index = demoWorkOrderPriorities.findIndex(
      (priority) => priority.id === update.id
    );
    if (index !== -1) {
      demoWorkOrderPriorities[index].position = update.position;
      demoWorkOrderPriorities[index].updatedAt = new Date();
      updated++;
    }
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${updated} work order priority positions updated successfully`,
    data: {
      updated,
      message: `${updated} work order priority positions updated successfully`,
    },
  };
}

export async function bulkUpdateDemoWorkOrderPriorityStatusApi(
  data: BulkUpdateWorkOrderPriorityStatusDto
): Promise<BulkUpdateWorkOrderPriorityStatusResponse> {
  await delay(400);

  let updated = 0;

  for (const id of data.ids) {
    const index = demoWorkOrderPriorities.findIndex(
      (priority) => priority.id === id
    );
    if (index !== -1) {
      demoWorkOrderPriorities[index].isActive = data.isActive;
      demoWorkOrderPriorities[index].updatedAt = new Date();
      updated++;
    }
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${updated} work order priorities updated successfully`,
    data: {
      updated,
      message: `${updated} work order priorities updated successfully`,
    },
  };
}

export async function deleteDemoWorkOrderPriorityApi(id: string) {
  await delay(300);

  const index = demoWorkOrderPriorities.findIndex(
    (priority) => priority.id === id
  );

  if (index === -1) {
    return {
      status: ApiStatus.ERROR,
      message: "Work order priority not found",
      data: null,
    };
  }

  demoWorkOrderPriorities.splice(index, 1);

  return {
    status: ApiStatus.SUCCESS,
    message: "Work order priority deleted successfully",
    data: null,
  };
}

export async function bulkDeleteDemoWorkOrderPrioritiesApi(
  data: BulkDeleteWorkOrderPriorityDto
): Promise<BulkDeleteWorkOrderPriorityResponse> {
  await delay(500);

  let deleted = 0;

  for (const id of data.ids) {
    const index = demoWorkOrderPriorities.findIndex(
      (priority) => priority.id === id
    );
    if (index !== -1) {
      demoWorkOrderPriorities.splice(index, 1);
      deleted++;
    }
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${deleted} work order priorities deleted successfully`,
    data: {
      deleted,
      message: `${deleted} work order priorities deleted successfully`,
    },
  };
}

// Bulk import work order priorities (demo version)
export async function bulkImportDemoWorkOrderPrioritiesApi(
  data: BulkImportWorkOrderPrioritiesSchema
): Promise<BulkWorkOrderPriorityIdsResponse> {
  await delay(800);

  const ids: string[] = [];
  let maxPosition = Math.max(
    ...demoWorkOrderPriorities.map((p) => p.position),
    0
  );

  for (const priorityData of data.workOrderPriorities) {
    const newId = `wop_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    maxPosition += 1;

    const newWorkOrderPriority: WorkOrderPriorityListDto = {
      id: newId,
      businessId: "biz_1",
      priorityCode: priorityData.priorityCode,
      priorityName: priorityData.priorityName,
      description: priorityData.description,
      colorCode: priorityData.colorCode,
      iconName: priorityData.iconName,
      severityLevel: priorityData.severityLevel,
      position: maxPosition,
      isActive: priorityData.isActive ?? true,
      isDefault: priorityData.isDefault ?? false,
      escalationHours: priorityData.escalationHours,
      createdBy: "Demo User",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    demoWorkOrderPriorities.push(newWorkOrderPriority);
    ids.push(newId);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${ids.length} work order priorities imported successfully`,
    data: { ids },
  };
}
