import {
  WorkOrderPriorityPaginatedResponse,
  WorkOrderPriorityIdResponse,
  BulkWorkOrderPriorityIdsResponse,
  BulkDeleteWorkOrderPriorityResponse,
  WorkOrderPriorityCodeAvailabilityResponse,
  WorkOrderPriorityNameAvailabilityResponse,
  SimpleWorkOrderPriorityResponse,
  WorkOrderPriorityResponse,
  CreateWorkOrderPriorityDto,
  UpdateWorkOrderPriorityDto,
  BulkCreateWorkOrderPriorityItemDto,
  UpdateWorkOrderPriorityPositionsDto,
  UpdateWorkOrderPriorityPositionsResponse,
  BulkUpdateWorkOrderPriorityStatusDto,
  BulkUpdateWorkOrderPriorityStatusResponse,
  WorkOrderPriorityTableData,
} from "@/types/work-order-priority";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  GetWorkOrderPrioritiesSchema,
  CreateWorkOrderPrioritySchema,
  UpdateWorkOrderPrioritySchema,
  BulkCreateWorkOrderPrioritiesSchema,
  ImportWorkOrderPrioritySchema,
  BulkImportWorkOrderPrioritiesSchema,
} from "./validations";

// Real API imports
import {
  getWorkOrderPrioritiesTableDataApi,
  getWorkOrderPrioritiesSlimApi,
  getWorkOrderPriorityApi,
  checkWorkOrderPriorityCodeAvailabilityApi,
  checkWorkOrderPriorityNameAvailabilityApi,
  createWorkOrderPriorityApi,
  bulkCreateWorkOrderPrioritiesApi,
  updateWorkOrderPriorityApi,
  updateWorkOrderPriorityPositionsApi,
  bulkUpdateWorkOrderPriorityStatusApi,
  deleteWorkOrderPriorityApi,
  bulkDeleteWorkOrderPrioritiesApi,
  bulkImportWorkOrderPrioritiesApi,
} from "./api";

// Demo API imports
import {
  getDemoWorkOrderPrioritiesTableDataApi,
  getDemoWorkOrderPrioritiesSlimApi,
  getDemoWorkOrderPriorityApi,
  checkDemoWorkOrderPriorityCodeAvailabilityApi,
  checkDemoWorkOrderPriorityNameAvailabilityApi,
  createDemoWorkOrderPriorityApi,
  bulkCreateDemoWorkOrderPrioritiesApi,
  updateDemoWorkOrderPriorityApi,
  updateDemoWorkOrderPriorityPositionsApi,
  bulkUpdateDemoWorkOrderPriorityStatusApi,
  deleteDemoWorkOrderPriorityApi,
  bulkDeleteDemoWorkOrderPrioritiesApi,
  bulkImportDemoWorkOrderPrioritiesApi,
} from "./demo";

// Get work order priorities table data with pagination and filtering
export async function getWorkOrderPrioritiesTableData(
  params: GetWorkOrderPrioritiesSchema,
  isDemo: boolean = false
): Promise<WorkOrderPriorityPaginatedResponse> {
  if (isDemo) {
    return getDemoWorkOrderPrioritiesTableDataApi(params);
  }
  return getWorkOrderPrioritiesTableDataApi(params);
}

// Get work order priorities slim data (for dropdowns)
export async function getWorkOrderPrioritiesSlim(
  isDemo: boolean = false
): Promise<SimpleWorkOrderPriorityResponse> {
  if (isDemo) {
    return getDemoWorkOrderPrioritiesSlimApi();
  }
  return getWorkOrderPrioritiesSlimApi();
}

// Get a single work order priority by ID
export async function getWorkOrderPriority(
  id: string,
  isDemo: boolean = false
): Promise<WorkOrderPriorityResponse> {
  if (isDemo) {
    return getDemoWorkOrderPriorityApi(id);
  }
  return getWorkOrderPriorityApi(id);
}

// Check work order priority code availability
export async function checkWorkOrderPriorityCodeAvailability(
  priorityCode: string,
  isDemo: boolean = false,
  excludeId?: string
): Promise<WorkOrderPriorityCodeAvailabilityResponse> {
  if (isDemo) {
    return checkDemoWorkOrderPriorityCodeAvailabilityApi(priorityCode, excludeId);
  }
  return checkWorkOrderPriorityCodeAvailabilityApi(priorityCode, excludeId);
}

// Check work order priority name availability
export async function checkWorkOrderPriorityNameAvailability(
  priorityName: string,
  isDemo: boolean = false,
  excludeId?: string
): Promise<WorkOrderPriorityNameAvailabilityResponse> {
  if (isDemo) {
    return checkDemoWorkOrderPriorityNameAvailabilityApi(priorityName, excludeId);
  }
  return checkWorkOrderPriorityNameAvailabilityApi(priorityName, excludeId);
}

// Create a new work order priority
export async function createWorkOrderPriority(
  data: CreateWorkOrderPriorityDto,
  isDemo: boolean = false
): Promise<WorkOrderPriorityIdResponse> {
  if (isDemo) {
    return createDemoWorkOrderPriorityApi(data);
  }
  return createWorkOrderPriorityApi(data);
}

// Bulk create work order priorities
export async function bulkCreateWorkOrderPriorities(
  workOrderPriorities: BulkCreateWorkOrderPriorityItemDto[],
  isDemo: boolean = false,
  images?: File[]
): Promise<BulkWorkOrderPriorityIdsResponse> {
  if (isDemo) {
    return bulkCreateDemoWorkOrderPrioritiesApi(workOrderPriorities, images);
  }
  return bulkCreateWorkOrderPrioritiesApi(workOrderPriorities, images);
}

// Update a work order priority
export async function updateWorkOrderPriority(
  id: string,
  data: UpdateWorkOrderPriorityDto,
  isDemo: boolean = false
): Promise<WorkOrderPriorityIdResponse> {
  if (isDemo) {
    return updateDemoWorkOrderPriorityApi(id, data);
  }
  return updateWorkOrderPriorityApi(id, data);
}

// Update work order priority positions
export async function updateWorkOrderPriorityPositions(
  updates: { id: string; position: number }[],
  isDemo: boolean = false
): Promise<UpdateWorkOrderPriorityPositionsResponse> {
  const data: UpdateWorkOrderPriorityPositionsDto = { updates };
  
  if (isDemo) {
    return updateDemoWorkOrderPriorityPositionsApi(data);
  }
  return updateWorkOrderPriorityPositionsApi(data);
}

// Bulk update work order priority status
export async function bulkUpdateWorkOrderPriorityStatus(
  data: BulkUpdateWorkOrderPriorityStatusDto,
  isDemo: boolean = false
): Promise<BulkUpdateWorkOrderPriorityStatusResponse> {
  if (isDemo) {
    return bulkUpdateDemoWorkOrderPriorityStatusApi(data);
  }
  return bulkUpdateWorkOrderPriorityStatusApi(data);
}

// Delete a work order priority
export async function deleteWorkOrderPriority(
  id: string,
  isDemo: boolean = false
): Promise<ApiResponse<null>> {
  if (isDemo) {
    return deleteDemoWorkOrderPriorityApi(id);
  }
  return deleteWorkOrderPriorityApi(id);
}

// Bulk delete work order priorities
export async function bulkDeleteWorkOrderPriorities(
  workOrderPriorityIds: string[],
  isDemo: boolean = false
): Promise<BulkDeleteWorkOrderPriorityResponse> {
  if (isDemo) {
    return bulkDeleteDemoWorkOrderPrioritiesApi(workOrderPriorityIds);
  }
  return bulkDeleteWorkOrderPrioritiesApi({ ids: workOrderPriorityIds });
}

// Bulk import work order priorities
export async function bulkImportWorkOrderPriorities(
  data: BulkImportWorkOrderPrioritiesSchema,
  isDemo: boolean = false
): Promise<BulkWorkOrderPriorityIdsResponse> {
  if (isDemo) {
    return bulkImportDemoWorkOrderPrioritiesApi(data);
  }
  return bulkImportWorkOrderPrioritiesApi(data);
}