import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  WorkOrderPriorityPaginatedResponse,
  WorkOrderPriorityIdResponse,
  BulkWorkOrderPriorityIdsResponse,
  BulkDeleteWorkOrderPriorityResponse,
  WorkOrderPriorityCodeAvailabilityResponse,
  WorkOrderPriorityNameAvailabilityResponse,
  SimpleWorkOrderPriorityResponse,
  WorkOrderPriorityResponse,
  CreateWorkOrderPriorityDto,
  UpdateWorkOrderPriorityDto,
  BulkCreateWorkOrderPriorityItemDto,
  UpdateWorkOrderPriorityPositionsDto,
  UpdateWorkOrderPriorityPositionsResponse,
  BulkUpdateWorkOrderPriorityStatusDto,
  BulkUpdateWorkOrderPriorityStatusResponse,
} from "@/types/work-order-priority";
import { ApiResponse } from "@/types/common";
import {
  GetWorkOrderPrioritiesSchema,
  BulkImportWorkOrderPrioritiesSchema,
} from "./validations";
import {
  getWorkOrderPrioritiesTableData,
  getWorkOrderPrioritiesSlim,
  getWorkOrderPriority,
  checkWorkOrderPriorityCodeAvailability,
  checkWorkOrderPriorityNameAvailability,
  createWorkOrderPriority,
  bulkCreateWorkOrderPriorities,
  updateWorkOrderPriority,
  updateWorkOrderPriorityPositions,
  bulkUpdateWorkOrderPriorityStatus,
  deleteWorkOrderPriority,
  bulkDeleteWorkOrderPriorities,
  bulkImportWorkOrderPriorities,
} from "./queries";

// Query keys for cache management
export const workOrderPriorityKeys = {
  all: ["work-order-priorities"] as const,
  list: () => [...workOrderPriorityKeys.all, "list"] as const,
  filtered: (params: GetWorkOrderPrioritiesSchema & { isDemo?: boolean }) =>
    [...workOrderPriorityKeys.list(), params] as const,
  slim: () => [...workOrderPriorityKeys.all, "slim"] as const,
  detail: (id: string) => [...workOrderPriorityKeys.all, "detail", id] as const,
  codeAvailability: (code: string) =>
    [...workOrderPriorityKeys.all, "code-availability", code] as const,
  nameAvailability: (name: string) =>
    [...workOrderPriorityKeys.all, "name-availability", name] as const,
};

// Hook to get work order priorities with pagination and filters
export function useWorkOrderPrioritiesData(
  params: GetWorkOrderPrioritiesSchema,
  isDemo: boolean = false
): UseQueryResult<WorkOrderPriorityPaginatedResponse> {
  return useQuery({
    queryKey: workOrderPriorityKeys.filtered({ ...params, isDemo }),
    queryFn: () => getWorkOrderPrioritiesTableData(params, isDemo),
    staleTime: 1000 * 60 * 5, // Always fetch fresh data when params change
    refetchOnMount: true, // Ensure it fetches on mount
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    placeholderData: (previousData) => previousData, // Keep previous data while loading new data
  });
}

// Hook to get work order priorities in slim format (for dropdowns)
export function useWorkOrderPrioritiesSlim(
  isDemo: boolean = false
): UseQueryResult<SimpleWorkOrderPriorityResponse> {
  return useQuery({
    queryKey: [...workOrderPriorityKeys.slim(), { isDemo }],
    queryFn: () => getWorkOrderPrioritiesSlim(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to get a single work order priority by ID
export function useWorkOrderPriorityData(
  id: string,
  isDemo: boolean = false
): UseQueryResult<WorkOrderPriorityResponse> {
  return useQuery({
    queryKey: [...workOrderPriorityKeys.detail(id), { isDemo }],
    queryFn: () => getWorkOrderPriority(id, isDemo),
    enabled: !!id,
  });
}

// Hook to check work order priority code availability
export function useWorkOrderPriorityCodeAvailability(
  priorityCode: string,
  isDemo: boolean = false,
  excludeId?: string
): UseQueryResult<WorkOrderPriorityCodeAvailabilityResponse> {
  return useQuery({
    queryKey: [...workOrderPriorityKeys.codeAvailability(priorityCode), { isDemo, excludeId }],
    queryFn: () => checkWorkOrderPriorityCodeAvailability(priorityCode, isDemo, excludeId),
    enabled: !!priorityCode && priorityCode.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Hook to check work order priority name availability
export function useWorkOrderPriorityNameAvailability(
  priorityName: string,
  isDemo: boolean = false,
  excludeId?: string
): UseQueryResult<WorkOrderPriorityNameAvailabilityResponse> {
  return useQuery({
    queryKey: [...workOrderPriorityKeys.nameAvailability(priorityName), { isDemo, excludeId }],
    queryFn: () => checkWorkOrderPriorityNameAvailability(priorityName, isDemo, excludeId),
    enabled: !!priorityName && priorityName.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Mutation hook to create a work order priority
export function useCreateWorkOrderPriority(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWorkOrderPriorityDto) =>
      createWorkOrderPriority(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch work order priority queries
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    },
  });
}

// Mutation hook to bulk create work order priorities
export function useBulkCreateWorkOrderPriorities(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      workOrderPriorities,
      images,
    }: {
      workOrderPriorities: BulkCreateWorkOrderPriorityItemDto[];
      images?: File[];
    }) => bulkCreateWorkOrderPriorities(workOrderPriorities, isDemo, images),
    onSuccess: () => {
      // Invalidate and refetch work order priority queries
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    },
  });
}

// Mutation hook to bulk import work order priorities
export function useBulkImportWorkOrderPriorities(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkImportWorkOrderPrioritiesSchema) =>
      bulkImportWorkOrderPriorities(data, isDemo),
    onSuccess: () => {
      // Invalidate and refetch work order priority queries
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    },
  });
}

// Mutation hook to update a work order priority
export function useUpdateWorkOrderPriority(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateWorkOrderPriorityDto;
    }) => updateWorkOrderPriority(id, data, isDemo),
    onSuccess: (_, { id }) => {
      // Invalidate specific work order priority and list queries
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.list() });
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.slim() });
    },
  });
}

// Mutation hook to delete a work order priority
export function useDeleteWorkOrderPriority(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteWorkOrderPriority(id, isDemo),
    onSuccess: () => {
      // Invalidate and refetch work order priority queries
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    },
  });
}

// Mutation hook to bulk delete work order priorities
export function useBulkDeleteWorkOrderPriorities(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (workOrderPriorityIds: string[]) =>
      bulkDeleteWorkOrderPriorities(workOrderPriorityIds, isDemo),
    onSuccess: () => {
      // Invalidate and refetch work order priority queries
      queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    },
  });
}

// Custom hook for work order priority table with built-in refresh capability
export function useWorkOrderPrioritiesTable(
  params: GetWorkOrderPrioritiesSchema,
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();
  const query = useWorkOrderPrioritiesData(params, isDemo);

  const refresh = () => {
    queryClient.invalidateQueries({
      queryKey: workOrderPriorityKeys.filtered({ ...params, isDemo }),
    });
  };

  return {
    ...query,
    refresh,
  };
}

// Custom hook for work order priority form
export function useWorkOrderPriorityForm(isDemo: boolean = false) {
  const createMutation = useCreateWorkOrderPriority(isDemo);
  const updateMutation = useUpdateWorkOrderPriority(isDemo);

  return {
    createWorkOrderPriority: createMutation,
    updateWorkOrderPriority: updateMutation,
    isLoading: createMutation.isPending || updateMutation.isPending,
  };
}

// Legacy hooks for backward compatibility
export const useWorkOrderPrioritiesSimple = useWorkOrderPrioritiesSlim;
export const useWorkOrderPriorityDetail = useWorkOrderPriorityData;