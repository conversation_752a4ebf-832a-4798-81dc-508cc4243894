import {
  WorkOrderPriorityResponse,
  WorkOrderPriorityPaginatedResponse,
  WorkOrderPriorityIdResponse,
  BulkWorkOrderPriorityIdsResponse,
  BulkDeleteWorkOrderPriorityDto,
  BulkDeleteWorkOrderPriorityResponse,
  WorkOrderPriorityCodeAvailabilityResponse,
  WorkOrderPriorityNameAvailabilityResponse,
  SimpleWorkOrderPriorityResponse,
  CreateWorkOrderPriorityDto,
  UpdateWorkOrderPriorityDto,
  BulkCreateWorkOrderPriorityItemDto,
  UpdateWorkOrderPriorityPositionsDto,
  UpdateWorkOrderPriorityPositionsResponse,
  BulkUpdateWorkOrderPriorityStatusDto,
  BulkUpdateWorkOrderPriorityStatusResponse,
} from "@/types/work-order-priority";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetWorkOrderPrioritiesSchema, BulkImportWorkOrderPrioritiesSchema } from "./validations";
import axios from "@/utils/axios";

const BASE_URL = "/work-order-priorities";

// Get all work order priorities with pagination and filtering
export async function getWorkOrderPrioritiesTableDataApi(
  params: GetWorkOrderPrioritiesSchema
): Promise<WorkOrderPriorityPaginatedResponse> {
  try {
    const response = await axios.get(BASE_URL, { params });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to fetch work order priorities");
  }
}

// Get all work order priorities (slim version for dropdowns)
export async function getWorkOrderPrioritiesSlimApi(): Promise<SimpleWorkOrderPriorityResponse> {
  try {
    const response = await axios.get(`${BASE_URL}/slim`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to fetch work order priorities");
  }
}

// Get a single work order priority by ID
export async function getWorkOrderPriorityApi(id: string): Promise<WorkOrderPriorityResponse> {
  try {
    const response = await axios.get(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to fetch work order priority");
  }
}

// Check if priority code is available
export async function checkWorkOrderPriorityCodeAvailabilityApi(
  priorityCode: string,
  excludeId?: string
): Promise<WorkOrderPriorityCodeAvailabilityResponse> {
  try {
    const params = excludeId ? { excludeId } : {};
    const response = await axios.get(`${BASE_URL}/check-priority-code/${priorityCode}`, { params });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to check priority code availability");
  }
}

// Check if priority name is available
export async function checkWorkOrderPriorityNameAvailabilityApi(
  priorityName: string,
  excludeId?: string
): Promise<WorkOrderPriorityNameAvailabilityResponse> {
  try {
    const params = excludeId ? { excludeId } : {};
    const response = await axios.get(`${BASE_URL}/check-priority-name/${priorityName}`, { params });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to check priority name availability");
  }
}

// Create a new work order priority
export async function createWorkOrderPriorityApi(
  data: CreateWorkOrderPriorityDto
): Promise<WorkOrderPriorityIdResponse> {
  try {
    const response = await axios.post(BASE_URL, data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to create work order priority");
  }
}

// Bulk create work order priorities
export async function bulkCreateWorkOrderPrioritiesApi(
  workOrderPriorities: BulkCreateWorkOrderPriorityItemDto[],
  images?: File[]
): Promise<BulkWorkOrderPriorityIdsResponse> {
  try {
    const formData = new FormData();
    
    // Add work order priorities data as JSON string
    formData.append("workOrderPriorities", JSON.stringify(workOrderPriorities));

    // Add images if provided
    if (images && images.length > 0) {
      images.forEach((image) => {
        formData.append("images", image);
      });
    }

    const response = await axios.post(`${BASE_URL}/bulk`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to bulk create work order priorities");
  }
}

// Update a work order priority
export async function updateWorkOrderPriorityApi(
  id: string,
  data: UpdateWorkOrderPriorityDto
): Promise<WorkOrderPriorityIdResponse> {
  try {
    const response = await axios.patch(`${BASE_URL}/${id}`, data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to update work order priority");
  }
}

// Update work order priority positions
export async function updateWorkOrderPriorityPositionsApi(
  data: UpdateWorkOrderPriorityPositionsDto
): Promise<UpdateWorkOrderPriorityPositionsResponse> {
  try {
    const response = await axios.patch(`${BASE_URL}/positions`, data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to update work order priority positions");
  }
}

// Bulk update work order priority status
export async function bulkUpdateWorkOrderPriorityStatusApi(
  data: BulkUpdateWorkOrderPriorityStatusDto
): Promise<BulkUpdateWorkOrderPriorityStatusResponse> {
  try {
    const response = await axios.patch(`${BASE_URL}/bulk-status`, data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to bulk update work order priority status");
  }
}

// Delete a work order priority
export async function deleteWorkOrderPriorityApi(id: string): Promise<ApiResponse<null>> {
  try {
    const response = await axios.delete(`${BASE_URL}/${id}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to delete work order priority");
  }
}

// Bulk delete work order priorities
export async function bulkDeleteWorkOrderPrioritiesApi(
  data: BulkDeleteWorkOrderPriorityDto
): Promise<BulkDeleteWorkOrderPriorityResponse> {
  try {
    const response = await axios.delete(`${BASE_URL}/bulk`, { data });
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to bulk delete work order priorities");
  }
}

// Bulk import work order priorities
export async function bulkImportWorkOrderPrioritiesApi(
  data: BulkImportWorkOrderPrioritiesSchema
): Promise<BulkWorkOrderPriorityIdsResponse> {
  try {
    const response = await axios.post(`${BASE_URL}/bulk`, data);
    return response.data;
  } catch (error: any) {
    throw new Error(error.response?.data?.message || "Failed to bulk import work order priorities");
  }
}
