import { z } from "zod";

// Backend DTO: CreateWorkOrderPriorityDto
export const createWorkOrderPrioritySchema = z.object({
  priorityCode: z.string().min(1, "Priority code is required").max(50, "Priority code must be 50 characters or less"),
  priorityName: z.string().min(1, "Priority name is required").max(100, "Priority name must be 100 characters or less"),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color code must be a valid hex color (e.g., #FF0000)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  severityLevel: z
    .number()
    .min(1, "Severity level must be at least 1")
    .max(10, "Severity level must be at most 10"),
  isActive: z.boolean().optional().default(true),
  isDefault: z.boolean().optional().default(false),
  escalationHours: z
    .number()
    .min(1, "Escalation hours must be at least 1")
    .optional(),
  imageIndex: z.number().min(0).optional(),
});

// Backend DTO: UpdateWorkOrderPriorityDto
export const updateWorkOrderPrioritySchema = z.object({
  priorityCode: z.string().min(1, "Priority code is required").max(50, "Priority code must be 50 characters or less").optional(),
  priorityName: z.string().min(1, "Priority name is required").max(100, "Priority name must be 100 characters or less").optional(),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color code must be a valid hex color (e.g., #FF0000)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  severityLevel: z
    .number()
    .min(1, "Severity level must be at least 1")
    .max(10, "Severity level must be at most 10")
    .optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  escalationHours: z
    .number()
    .min(1, "Escalation hours must be at least 1")
    .optional(),
});

// Schema for bulk create operations
export const bulkCreateWorkOrderPrioritiesSchema = z.object({
  workOrderPriorities: z.array(createWorkOrderPrioritySchema).min(1, "At least one work order priority is required"),
});

// Schema for bulk delete operations
export const bulkDeleteWorkOrderPrioritiesSchema = z.object({
  ids: z.array(z.string().uuid("Invalid work order priority ID")).min(1, "At least one work order priority ID is required"),
});

// Schema for bulk status update operations
export const bulkUpdateWorkOrderPriorityStatusSchema = z.object({
  ids: z.array(z.string().uuid("Invalid work order priority ID")).min(1, "At least one work order priority ID is required"),
  isActive: z.boolean(),
});

// Schema for position updates
export const updateWorkOrderPriorityPositionsSchema = z.object({
  updates: z.array(
    z.object({
      id: z.string().uuid("Invalid work order priority ID"),
      position: z.number().min(1, "Position must be at least 1"),
    })
  ).min(1, "At least one position update is required"),
});

// Schema for getting work order priorities with filters
export const getWorkOrderPrioritiesSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  priorityCode: z.string().optional(),
  priorityName: z.string().optional(),
  severityLevel: z.string().optional(),
  isActive: z.string().optional(),
  filters: z.string().optional(),
  joinOperator: z.enum(["and", "or"]).optional(),
  sort: z.string().optional(),
});

// Schema for importing work order priorities
export const importWorkOrderPrioritySchema = z.object({
  priorityCode: z.string().min(1, "Priority code is required").max(50, "Priority code must be 50 characters or less"),
  priorityName: z.string().min(1, "Priority name is required").max(100, "Priority name must be 100 characters or less"),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color code must be a valid hex color (e.g., #FF0000)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  severityLevel: z
    .number()
    .min(1, "Severity level must be at least 1")
    .max(10, "Severity level must be at most 10"),
  isActive: z.boolean().optional().default(true),
  isDefault: z.boolean().optional().default(false),
  escalationHours: z
    .number()
    .min(1, "Escalation hours must be at least 1")
    .optional(),
});

// Schema for bulk import (no images support)
export const bulkImportWorkOrderPrioritiesSchema = z.object({
  workOrderPriorities: z.array(importWorkOrderPrioritySchema).min(1, "At least one work order priority is required"),
});

// Form schema for UI components (includes additional fields for form handling)
export const workOrderPriorityFormSchema = z.object({
  priorityCode: z.string().min(1, "Priority code is required").max(50, "Priority code must be 50 characters or less"),
  priorityName: z.string().min(1, "Priority name is required").max(100, "Priority name must be 100 characters or less"),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color code must be a valid hex color (e.g., #FF0000)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  severityLevel: z
    .number()
    .min(1, "Severity level must be at least 1")
    .max(10, "Severity level must be at most 10"),
  isActive: z.boolean().optional().default(true),
  isDefault: z.boolean().optional().default(false),
  escalationHours: z
    .number()
    .min(1, "Escalation hours must be at least 1")
    .optional(),
});

// Type exports for use in components
export type CreateWorkOrderPrioritySchema = z.infer<typeof createWorkOrderPrioritySchema>;
export type UpdateWorkOrderPrioritySchema = z.infer<typeof updateWorkOrderPrioritySchema>;
export type BulkCreateWorkOrderPrioritiesSchema = z.infer<typeof bulkCreateWorkOrderPrioritiesSchema>;
export type BulkDeleteWorkOrderPrioritiesSchema = z.infer<typeof bulkDeleteWorkOrderPrioritiesSchema>;
export type GetWorkOrderPrioritiesSchema = z.infer<typeof getWorkOrderPrioritiesSchema>;
export type WorkOrderPriorityFormSchema = z.infer<typeof workOrderPriorityFormSchema>;
export type ImportWorkOrderPrioritySchema = z.infer<typeof importWorkOrderPrioritySchema>;
export type BulkImportWorkOrderPrioritiesSchema = z.infer<typeof bulkImportWorkOrderPrioritiesSchema>;
export type BulkUpdateWorkOrderPriorityStatusSchema = z.infer<typeof bulkUpdateWorkOrderPriorityStatusSchema>;
export type UpdateWorkOrderPriorityPositionsSchema = z.infer<typeof updateWorkOrderPriorityPositionsSchema>;
