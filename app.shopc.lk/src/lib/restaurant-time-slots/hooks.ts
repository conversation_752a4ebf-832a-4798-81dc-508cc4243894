import {
  useQuery,
  useMutation,
  useQuery<PERSON>lient,
  UseQueryResult,
  UseMutationResult,
} from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import {
  RestaurantTimeSlotDto,
  RestaurantTimeSlotSlimDto,
  CreateRestaurantTimeSlotDto,
  UpdateRestaurantTimeSlotDto,
  RestaurantTimeSlotsResponse,
  RestaurantTimeSlotSlimResponse,
  RestaurantTimeSlotResponse,
  RestaurantTimeSlotIdResponse,
  DeleteRestaurantTimeSlotResponse,
} from "@/types/restaurant-time-slot";
import {
  getRestaurantTimeSlotsApi,
  getRestaurantTimeSlotsSlimApi,
  getRestaurantTimeSlotByIdApi,
  createRestaurantTimeSlotA<PERSON>,
  updateRestaurantTimeSlotApi,
  deleteRestaurantTimeSlotApi,
} from "./api";

// Query Keys
export const RESTAURANT_TIME_SLOTS_QUERY_KEYS = {
  all: ["restaurant-time-slots"] as const,
  lists: () => [...RESTAURANT_TIME_SLOTS_QUERY_KEYS.all, "list"] as const,
  list: () => [...RESTAURANT_TIME_SLOTS_QUERY_KEYS.lists()] as const,
  slim: () => [...RESTAURANT_TIME_SLOTS_QUERY_KEYS.all, "slim"] as const,
  details: () => [...RESTAURANT_TIME_SLOTS_QUERY_KEYS.all, "detail"] as const,
  detail: (id: string) => [...RESTAURANT_TIME_SLOTS_QUERY_KEYS.details(), id] as const,
};

// Get all restaurant time slots
export function useRestaurantTimeSlots(): UseQueryResult<RestaurantTimeSlotDto[], Error> {
  return useQuery({
    queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.list(),
    queryFn: async () => {
      const response = await getRestaurantTimeSlotsApi();
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get slim restaurant time slots for dropdowns
export function useRestaurantTimeSlotsSlim(): UseQueryResult<RestaurantTimeSlotSlimDto[], Error> {
  return useQuery({
    queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
    queryFn: async () => {
      const response = await getRestaurantTimeSlotsSlimApi();
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Get restaurant time slot by ID
export function useRestaurantTimeSlot(
  id: string,
  enabled = true
): UseQueryResult<RestaurantTimeSlotDto, Error> {
  return useQuery({
    queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.detail(id),
    queryFn: async () => {
      const response = await getRestaurantTimeSlotByIdApi(id);
      return response.data;
    },
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Create restaurant time slot mutation
export function useCreateRestaurantTimeSlot(): UseMutationResult<
  { id: string },
  Error,
  CreateRestaurantTimeSlotDto
> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createRestaurantTimeSlotApi,
    onSuccess: (data) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
      });
      
      toast({
        title: "Success",
        description: "Restaurant time slot created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error creating restaurant time slot:", error);
      const message = error?.response?.data?.message || "Failed to create restaurant time slot";
      toast({
        variant: "destructive",
        title: "Error",
        description: message,
      });
    },
  });
}

// Update restaurant time slot mutation
export function useUpdateRestaurantTimeSlot(): UseMutationResult<
  { id: string },
  Error,
  { id: string; data: UpdateRestaurantTimeSlotDto }
> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }) => updateRestaurantTimeSlotApi(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
      });
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.detail(variables.id),
      });
      
      toast({
        title: "Success",
        description: "Restaurant time slot updated successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error updating restaurant time slot:", error);
      const message = error?.response?.data?.message || "Failed to update restaurant time slot";
      toast({
        variant: "destructive",
        title: "Error",
        description: message,
      });
    },
  });
}

// Delete restaurant time slot mutation
export function useDeleteRestaurantTimeSlot(): UseMutationResult<
  { message: string; id: string },
  Error,
  string
> {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteRestaurantTimeSlotApi,
    onSuccess: (data) => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
      });
      
      // Remove the deleted item from cache
      queryClient.removeQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.detail(data.id),
      });
      
      toast({
        title: "Success",
        description: "Restaurant time slot deleted successfully",
      });
    },
    onError: (error: any) => {
      console.error("Error deleting restaurant time slot:", error);
      const message = error?.response?.data?.message || "Failed to delete restaurant time slot";
      toast({
        variant: "destructive",
        title: "Error",
        description: message,
      });
    },
  });
}

// Prefetch restaurant time slots
export function usePrefetchRestaurantTimeSlots() {
  const queryClient = useQueryClient();

  return {
    prefetchList: () => {
      queryClient.prefetchQuery({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.list(),
        queryFn: async () => {
          const response = await getRestaurantTimeSlotsApi();
          return response.data;
        },
        staleTime: 5 * 60 * 1000,
      });
    },
    prefetchSlim: () => {
      queryClient.prefetchQuery({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
        queryFn: async () => {
          const response = await getRestaurantTimeSlotsSlimApi();
          return response.data;
        },
        staleTime: 10 * 60 * 1000,
      });
    },
    prefetchDetail: (id: string) => {
      queryClient.prefetchQuery({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.detail(id),
        queryFn: async () => {
          const response = await getRestaurantTimeSlotByIdApi(id);
          return response.data;
        },
        staleTime: 5 * 60 * 1000,
      });
    },
  };
}

// Utility hook for optimistic updates
export function useOptimisticRestaurantTimeSlotUpdate() {
  const queryClient = useQueryClient();

  return {
    updateOptimistically: (id: string, updatedData: Partial<RestaurantTimeSlotDto>) => {
      // Update the list cache
      queryClient.setQueryData<RestaurantTimeSlotDto[]>(
        RESTAURANT_TIME_SLOTS_QUERY_KEYS.list(),
        (old) => {
          if (!old) return old;
          return old.map((slot) =>
            slot.id === id ? { ...slot, ...updatedData } : slot
          );
        }
      );

      // Update the detail cache
      queryClient.setQueryData<RestaurantTimeSlotDto>(
        RESTAURANT_TIME_SLOTS_QUERY_KEYS.detail(id),
        (old) => {
          if (!old) return old;
          return { ...old, ...updatedData };
        }
      );

      // Update the slim cache if it exists
      queryClient.setQueryData<RestaurantTimeSlotSlimDto[]>(
        RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
        (old) => {
          if (!old) return old;
          return old.map((slot) =>
            slot.id === id ? { ...slot, ...updatedData } : slot
          );
        }
      );
    },
    
    removeOptimistically: (id: string) => {
      // Remove from list cache
      queryClient.setQueryData<RestaurantTimeSlotDto[]>(
        RESTAURANT_TIME_SLOTS_QUERY_KEYS.list(),
        (old) => {
          if (!old) return old;
          return old.filter((slot) => slot.id !== id);
        }
      );

      // Remove from slim cache
      queryClient.setQueryData<RestaurantTimeSlotSlimDto[]>(
        RESTAURANT_TIME_SLOTS_QUERY_KEYS.slim(),
        (old) => {
          if (!old) return old;
          return old.filter((slot) => slot.id !== id);
        }
      );

      // Remove detail cache
      queryClient.removeQueries({
        queryKey: RESTAURANT_TIME_SLOTS_QUERY_KEYS.detail(id),
      });
    },
  };
}

// Custom hook for filtering and sorting
export function useRestaurantTimeSlotsFiltered(
  filter?: {
    status?: string;
    timeSlotType?: string;
    isActive?: boolean;
    searchTerm?: string;
  }
) {
  const { data: timeSlots = [], ...queryResult } = useRestaurantTimeSlots();

  const filteredTimeSlots = timeSlots.filter((slot) => {
    if (filter?.status && slot.status !== filter.status) return false;
    if (filter?.timeSlotType && slot.timeSlotType !== filter.timeSlotType) return false;
    if (filter?.isActive !== undefined && slot.isActive !== filter.isActive) return false;
    if (filter?.searchTerm) {
      const searchLower = filter.searchTerm.toLowerCase();
      return (
        slot.name.toLowerCase().includes(searchLower) ||
        slot.description?.toLowerCase().includes(searchLower) ||
        slot.timeSlotType.toLowerCase().includes(searchLower)
      );
    }
    return true;
  });

  return {
    ...queryResult,
    data: filteredTimeSlots,
    totalCount: filteredTimeSlots.length,
  };
}