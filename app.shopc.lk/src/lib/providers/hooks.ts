"use client";

import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import { ApiResponse, ApiStatus } from "@/types/common";
import {
  ProviderDto,
  ProviderSlimDto,
  PaginatedProvidersResponseDto,
  ProviderIdResponseDto,
  DeleteProviderResponseDto,
  CreateProviderDto,
  UpdateProviderDto,
} from "@/types/provider";
import { GetProvidersSchema } from "./validations";
import {
  getProvidersTableData,
  getProvidersSlim,
  getProvider,
  createProvider,
  updateProvider,
  deleteProvider,
  bulkDeleteProviders,
  setDefaultProvider,
  testProvider,
  getProvidersByType,
  getDefaultProvider,
} from "./queries";

// Query keys for providers
export const providerKeys = {
  all: ["providers"] as const,
  list: () => [...providerKeys.all, "list"] as const,
  filtered: (params: GetProvidersSchema & { isDemo?: boolean }) =>
    [...providerKeys.list(), params] as const,
  slim: (isDemo?: boolean) =>
    [...providerKeys.all, "slim", { isDemo }] as const,
  detail: (id: string, isDemo?: boolean) =>
    [...providerKeys.all, "detail", id, { isDemo }] as const,
  byType: (type: string, isDemo?: boolean) =>
    [...providerKeys.all, "byType", type, { isDemo }] as const,
  defaultProvider: (type: string, isDemo?: boolean) =>
    [...providerKeys.all, "default", type, { isDemo }] as const,
};

// Hook to fetch providers with pagination and filtering
export function useProvidersData(
  params: GetProvidersSchema,
  isDemo: boolean = false
): UseQueryResult<ApiResponse<PaginatedProvidersResponseDto | null>> {
  return useQuery({
    queryKey: providerKeys.filtered({ ...params, isDemo }),
    queryFn: () => getProvidersTableData(params, isDemo),
    staleTime: 0,
    refetchOnMount: true,
  });
}

// Hook to fetch providers in slim format (for dropdowns)
export function useProvidersSlim(
  isDemo: boolean = false
): UseQueryResult<ApiResponse<ProviderSlimDto[]>> {
  return useQuery({
    queryKey: providerKeys.slim(isDemo),
    queryFn: () => getProvidersSlim(isDemo),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch single provider by ID
export function useProvider(
  id: string,
  isDemo: boolean = false
): UseQueryResult<ApiResponse<ProviderDto | null>> {
  return useQuery({
    queryKey: providerKeys.detail(id, isDemo),
    queryFn: () => getProvider(id, isDemo),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch providers by type
export function useProvidersByType(
  providerType: string,
  isDemo: boolean = false
): UseQueryResult<ApiResponse<ProviderSlimDto[]>> {
  return useQuery({
    queryKey: providerKeys.byType(providerType, isDemo),
    queryFn: () => getProvidersByType(providerType, isDemo),
    enabled: !!providerType,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to fetch default provider for a type
export function useDefaultProvider(
  providerType: string,
  isDemo: boolean = false
): UseQueryResult<ApiResponse<ProviderSlimDto | null>> {
  return useQuery({
    queryKey: providerKeys.defaultProvider(providerType, isDemo),
    queryFn: () => getDefaultProvider(providerType, isDemo),
    enabled: !!providerType,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutation hook for creating providers
export function useCreateProvider(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProviderDto) => createProvider(data, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        // Invalidate and refetch providers
        queryClient.invalidateQueries({ queryKey: providerKeys.all });
        toast({
          title: "Success",
          description: response.message || "Provider created successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create provider",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create provider",
        variant: "destructive",
      });
    },
  });
}

// Mutation hook for updating providers
export function useUpdateProvider(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProviderDto }) =>
      updateProvider(id, data, isDemo),
    onSuccess: (response, variables) => {
      if (response.status === ApiStatus.SUCCESS) {
        // Invalidate and refetch providers
        queryClient.invalidateQueries({ queryKey: providerKeys.all });
        toast({
          title: "Success",
          description: response.message || "Provider updated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update provider",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update provider",
        variant: "destructive",
      });
    },
  });
}

// Mutation hook for deleting providers
export function useDeleteProvider(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteProvider(id, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        // Invalidate and refetch providers
        queryClient.invalidateQueries({ queryKey: providerKeys.all });
        toast({
          title: "Success",
          description: response.message || "Provider deleted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete provider",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete provider",
        variant: "destructive",
      });
    },
  });
}

// Mutation hook for bulk deleting providers
export function useBulkDeleteProviders(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => bulkDeleteProviders(ids, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        // Invalidate and refetch providers
        queryClient.invalidateQueries({ queryKey: providerKeys.all });
        toast({
          title: "Success",
          description: response.message || "Providers deleted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete providers",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete providers",
        variant: "destructive",
      });
    },
  });
}

// Mutation hook for setting default provider
export function useSetDefaultProvider(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => setDefaultProvider(id, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS) {
        // Invalidate and refetch providers
        queryClient.invalidateQueries({ queryKey: providerKeys.all });
        toast({
          title: "Success",
          description: response.message || "Provider set as default successfully",
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to set provider as default",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to set provider as default",
        variant: "destructive",
      });
    },
  });
}

// Mutation hook for testing provider configuration
export function useTestProvider(isDemo: boolean = false) {
  return useMutation({
    mutationFn: (id: string) => testProvider(id, isDemo),
    onSuccess: (response) => {
      if (response.status === ApiStatus.SUCCESS && response.data) {
        if (response.data.success) {
          toast({
            title: "Success",
            description: response.data.message || "Provider test successful",
          });
        } else {
          toast({
            title: "Error",
            description: response.data.message || "Provider test failed",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to test provider",
          variant: "destructive",
        });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to test provider",
        variant: "destructive",
      });
    },
  });
}
