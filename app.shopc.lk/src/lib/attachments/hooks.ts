import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/hooks/use-toast";
import {
  uploadAttachmentApi,
  uploadMultipleAttachmentsApi,
  getAttachmentsByReferenceApi,
  getAttachmentApi,
  updateAttachmentsForReferenceApi,
  deleteAttachmentApi,
  softDeleteAttachmentApi,
  deleteAttachmentsByReferenceApi,
  restoreAttachmentApi,
  generateSignedUrlsApi,
  UploadAttachmentDto,
  UpdateAttachmentsDto,
} from "./api";
import { ApiStatus } from "@/types/common";

// Query keys
export const attachmentKeys = {
  all: ["attachments"] as const,
  byReference: (referenceId: string, folder: string) =>
    [...attachmentKeys.all, "reference", referenceId, folder] as const,
  byId: (id: string, folder: string) =>
    [...attachmentKeys.all, "id", id, folder] as const,
};

// Get attachments by reference ID
export function useAttachmentsByReference(
  referenceId: string,
  folder: string,
  enabled = true
) {
  return useQuery({
    queryKey: attachmentKeys.byReference(referenceId, folder),
    queryFn: () => getAttachmentsByReferenceApi(referenceId, folder),
    enabled: enabled && !!referenceId && !!folder,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get single attachment by ID
export function useAttachment(id: string, folder: string, enabled = true) {
  return useQuery({
    queryKey: attachmentKeys.byId(id, folder),
    queryFn: () => getAttachmentApi(id, folder),
    enabled: enabled && !!id && !!folder,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Upload single attachment
export function useUploadAttachment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, data }: { file: File; data: UploadAttachmentDto }) =>
      uploadAttachmentApi(file, data),
    onSuccess: (response, { data }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Attachment uploaded successfully",
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(data.referenceId, data.folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to upload attachment",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to upload attachment",
        variant: "destructive",
      });
    },
  });
}

// Upload multiple attachments
export function useUploadMultipleAttachments() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ files, data }: { files: File[]; data: UploadAttachmentDto }) =>
      uploadMultipleAttachmentsApi(files, data),
    onSuccess: (response, { data }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: `${response.data?.length || 0} attachments uploaded successfully`,
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(data.referenceId, data.folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to upload attachments",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to upload attachments",
        variant: "destructive",
      });
    },
  });
}

// Update attachments for reference (replace all)
export function useUpdateAttachmentsForReference() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ files, data }: { files: File[]; data: UpdateAttachmentsDto }) =>
      updateAttachmentsForReferenceApi(files, data),
    onSuccess: (response, { data }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Attachments updated successfully",
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(data.referenceId, data.folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update attachments",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update attachments",
        variant: "destructive",
      });
    },
  });
}

// Delete single attachment
export function useDeleteAttachment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, folder, referenceId }: { id: string; folder: string; referenceId: string }) =>
      deleteAttachmentApi(id, folder),
    onSuccess: (response, { referenceId, folder }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Attachment deleted successfully",
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(referenceId, folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete attachment",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete attachment",
        variant: "destructive",
      });
    },
  });
}

// Soft delete attachment
export function useSoftDeleteAttachment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, referenceId, folder }: { id: string; referenceId: string; folder: string }) =>
      softDeleteAttachmentApi(id),
    onSuccess: (response, { referenceId, folder }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Attachment moved to trash",
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(referenceId, folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete attachment",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete attachment",
        variant: "destructive",
      });
    },
  });
}

// Delete all attachments by reference
export function useDeleteAttachmentsByReference() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ referenceId, folder }: { referenceId: string; folder: string }) =>
      deleteAttachmentsByReferenceApi(referenceId, folder),
    onSuccess: (response, { referenceId, folder }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "All attachments deleted successfully",
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(referenceId, folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete attachments",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete attachments",
        variant: "destructive",
      });
    },
  });
}

// Restore attachment
export function useRestoreAttachment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, referenceId, folder }: { id: string; referenceId: string; folder: string }) =>
      restoreAttachmentApi(id),
    onSuccess: (response, { referenceId, folder }) => {
      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: "Attachment restored successfully",
        });
        
        // Invalidate and refetch attachments for this reference
        queryClient.invalidateQueries({
          queryKey: attachmentKeys.byReference(referenceId, folder),
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to restore attachment",
          variant: "destructive",
        });
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to restore attachment",
        variant: "destructive",
      });
    },
  });
}

// Generate signed URLs
export function useGenerateSignedUrls() {
  return useMutation({
    mutationFn: ({ mediaIds, folder, expiration }: { mediaIds: string[]; folder: string; expiration?: number }) =>
      generateSignedUrlsApi(mediaIds, folder, expiration),
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to generate signed URLs",
        variant: "destructive",
      });
    },
  });
}