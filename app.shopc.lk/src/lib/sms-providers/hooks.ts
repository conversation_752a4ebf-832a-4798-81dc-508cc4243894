import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type {
  SMSProviderSettings,
  SMSProviderCreateInput,
  SMSTemplate,
  SMSTemplateInput,
  SMSMessage,
} from "@/types/sms-marketing";
import * as api from "./api";
import * as demo from "./demo";
import { toast } from "@/hooks/use-toast";
import { templateKeys } from "./queries";
import { getTemplate, getTemplates } from "./queries";
import {
  getSMSCampaignsTableData,
  getSMSCampaignById,
  smsCampaignKeys,
} from "./queries";
import { SMSCampaign } from "@/types/sms-campaign";

// Provider hooks
export function useProviders(isDemo = false) {
  return useQuery({
    queryKey: ["sms-marketing", "providers", { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listProviders();
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listProviders();
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}

export function useProvider(id: string, isDemo = false) {
  return useQuery({
    queryKey: ["sms-marketing", "providers", id, { isDemo }],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.getProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.getProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    enabled: !!id, // Only run the query if id is provided
  });
}

export function useCreateProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SMSProviderCreateInput) => {
      if (isDemo) {
        const response = await demo.createProvider(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.createProvider(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["sms-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "SMS provider created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useUpdateProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<SMSProviderCreateInput>;
    }) => {
      if (isDemo) {
        const response = await demo.updateProvider(id, data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.updateProvider(id, data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["sms-marketing", "providers", variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: ["sms-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "SMS provider updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useDeleteProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.deleteProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.deleteProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["sms-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "SMS provider deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useSetDefaultProvider(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.setDefaultProvider(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.setDefaultProvider(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["sms-marketing", "providers"],
      });
      toast({
        title: "Success",
        description: "Default SMS provider updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

// Template hooks
export function useTemplatesQuery(params: {
  search?: string;
  providerId?: string;
  tags?: string[];
  page?: number;
  limit?: number;
  isDemo?: boolean;
}) {
  const { isDemo = false, ...restParams } = params;

  return useQuery({
    queryKey: templateKeys.list(params),
    queryFn: () => getTemplates(params),
  });
}

export function useTemplateQuery(id: string, isDemo = false) {
  return useQuery({
    queryKey: templateKeys.detail(id),
    queryFn: () => getTemplate(id, isDemo),
    enabled: !!id,
  });
}

export function useCreateTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SMSTemplateInput) => {
      if (isDemo) {
        const response = await demo.createTemplate(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.createTemplate(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: templateKeys.lists(),
      });
      toast({
        title: "Success",
        description: "SMS template created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useUpdateTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<SMSTemplateInput>;
    }) => {
      if (isDemo) {
        const response = await demo.updateTemplate(id, data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.updateTemplate(id, data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: templateKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: templateKeys.lists(),
      });
      toast({
        title: "Success",
        description: "SMS template updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useDeleteTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (isDemo) {
        const response = await demo.deleteTemplate(id);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.deleteTemplate(id);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: templateKeys.lists(),
      });
      toast({
        title: "Success",
        description: "SMS template deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

// For UI state management in components
export function useTemplates(isDemo: boolean) {
  const { data, isLoading, refetch } = useTemplatesQuery({
    isDemo,
    limit: 100, // Get all templates for client-side management
  });

  const queryClient = useQueryClient();

  // This function allows UI components to update the template list
  const setTemplates = (templates: SMSTemplate[]) => {
    queryClient.setQueryData(templateKeys.list({ isDemo, limit: 100 }), {
      templates,
      total: templates.length,
      pageCount: 1,
    });
  };

  return {
    templates: data?.templates || [],
    setTemplates,
    isLoading,
    refetch,
  };
}

// Message hooks
export function useSendMessage(isDemo = false) {
  return useMutation({
    mutationFn: async (data: {
      to: string;
      message: string;
      providerId?: string;
      templateId?: string;
    }) => {
      if (isDemo) {
        const response = await demo.sendMessage(data);
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.sendMessage(data);
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "SMS message sent successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message,
      });
    },
  });
}

export function useMessages(params?: {
  status?: string;
  providerId?: string;
  isDemo?: boolean;
}) {
  const isDemo = params?.isDemo || false;

  return useQuery({
    queryKey: [
      "sms-marketing",
      "messages",
      { status: params?.status, providerId: params?.providerId, isDemo },
    ],
    queryFn: async () => {
      if (isDemo) {
        const response = await demo.listMessages({
          status: params?.status,
          providerId: params?.providerId,
        });
        if (!response.success) {
          throw new Error(response.message);
        }
        return response.data;
      }

      const response = await api.listMessages({
        status: params?.status,
        providerId: params?.providerId,
      });
      if (!response.success) {
        throw new Error(response.message);
      }
      return response.data;
    },
  });
}

// Hook to fetch SMS campaigns with filters
export function useSMSCampaignsData(
  filters: Record<string, any>,
  isDemo = false
) {
  return useQuery({
    queryKey: smsCampaignKeys.filtered(filters),
    queryFn: () => getSMSCampaignsTableData(filters, isDemo),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}

// Hook to fetch a single SMS campaign by ID
export function useSMSCampaignData(id: string, isDemo = false) {
  return useQuery({
    queryKey: smsCampaignKeys.detail(id),
    queryFn: () => getSMSCampaignById(id, isDemo),
    staleTime: 1000 * 60 * 5, // 5 minutes
    enabled: !!id, // Only run query if ID is provided
  });
}
