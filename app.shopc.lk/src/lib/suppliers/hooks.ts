import { useState, useCallback, useMemo } from "react";
import { toast } from "sonner";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import {
  useSuppliers,
  useSupplier,
  useSuppliersSlim,
  useCreateSupplier,
  useUpdateSupplier,
  useDeleteSupplier,
  useBulkCreateSuppliers,
  useBulkDeleteSuppliers as useBulkDeleteSuppliersQuery,
  useUpdateSupplierStatus,
  useSupplierDisplayNameAvailability,
  useSupplierEmailAvailability,
  supplierKeys,
  getSuppliersTableData,
} from "./queries";
import { updateSupplierStatusApi, bulkCreateSuppliersApi } from "./api";
import { bulkUpdateSupplierStatus } from "./queries";
import {
  CreateSupplierDto,
  UpdateSupplierDto,
  BulkCreateSupplierDto,
  SupplierStatus,
  SupplierListDto,
  SupplierAutocompleteResponse,
} from "@/types/supplier";
import { GetSuppliersSchema } from "./validations";
import { ApiStatus } from "@/types/common";
import { searchSuppliersAutocomplete } from "./queries";
import { UseQueryResult } from "@tanstack/react-query";

// Hook for managing suppliers list with pagination and filters
export function useSuppliersTable(initialParams?: Partial<GetSuppliersSchema>) {
  const [params, setParams] = useState<GetSuppliersSchema>({
    page: 1,
    perPage: 10,
    filters: [],
    joinOperator: "and",
    sort: [{ id: "createdAt", desc: true }],
    ...initialParams,
  });

  const query = useSuppliers(params);

  const updateParams = useCallback((newParams: Partial<GetSuppliersSchema>) => {
    setParams((prev) => ({ ...prev, ...newParams }));
  }, []);

  const resetParams = useCallback(() => {
    setParams({
      page: 1,
      perPage: 10,
      filters: [],
      joinOperator: "and",
      sort: [{ id: "createdAt", desc: true }],
      ...initialParams,
    });
  }, [initialParams]);

  return {
    ...query,
    params,
    updateParams,
    resetParams,
    suppliers: query.data?.data?.data || [],
    meta: query.data?.data?.meta,
    isSuccess: query.data?.status === ApiStatus.SUCCESS,
    isError: query.data?.status === ApiStatus.FAIL,
    errorMessage: query.data?.message,
  };
}

// Hook for managing supplier form operations
export function useSupplierForm() {
  const { toast } = useToast();
  const createMutation = useCreateSupplier();
  const updateMutation = useUpdateSupplier();

  const createSupplier = useCallback(
    async (data: CreateSupplierDto, profileImage?: File) => {
      try {
        const result = await createMutation.mutateAsync({ data, profileImage });
        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: "Supplier created successfully",
          });
          return result;
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to create supplier",
            variant: "destructive",
          });
          return result;
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
        throw error;
      }
    },
    [createMutation, toast]
  );

  const updateSupplier = useCallback(
    async (id: string, data: UpdateSupplierDto, profileImage?: File) => {
      try {
        const result = await updateMutation.mutateAsync({
          id,
          data,
          profileImage,
        });
        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: "Supplier updated successfully",
          });
          return result;
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to update supplier",
            variant: "destructive",
          });
          return result;
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
        throw error;
      }
    },
    [updateMutation, toast]
  );

  return {
    createSupplier,
    updateSupplier,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isLoading: createMutation.isPending || updateMutation.isPending,
  };
}

// Hook for managing supplier deletion
export function useSupplierDeletion(isDemo: boolean = false) {
  const { toast } = useToast();
  const deleteMutation = useDeleteSupplier();
  const bulkDeleteMutation = useBulkDeleteSuppliersQuery(isDemo);

  const deleteSupplier = useCallback(
    async (id: string) => {
      try {
        const result = await deleteMutation.mutateAsync(id);
        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: "Supplier deleted successfully",
          });
          return result;
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to delete supplier",
            variant: "destructive",
          });
          return result;
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
        throw error;
      }
    },
    [deleteMutation, toast]
  );

  const bulkDeleteSuppliers = useCallback(
    async (supplierIds: string[]) => {
      try {
        const result = await bulkDeleteMutation.mutateAsync(supplierIds);
        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: `${
              result.data?.deletedCount || 0
            } suppliers deleted successfully`,
          });
          return result;
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to delete suppliers",
            variant: "destructive",
          });
          return result;
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
        throw error;
      }
    },
    [bulkDeleteMutation, toast]
  );

  return {
    deleteSupplier,
    bulkDeleteSuppliers,
    isDeleting: deleteMutation.isPending,
    isBulkDeleting: bulkDeleteMutation.isPending,
    isLoading: deleteMutation.isPending || bulkDeleteMutation.isPending,
  };
}

// Direct export of bulk delete hook for component usage
export function useBulkDeleteSuppliers(isDemo: boolean = false) {
  return useBulkDeleteSuppliersQuery(isDemo);
}

// Hook for managing supplier status updates
export function useSupplierStatusUpdate() {
  const { toast } = useToast();
  const statusMutation = useUpdateSupplierStatus();

  const updateStatus = useCallback(
    async (id: string, status: SupplierStatus) => {
      try {
        const result = await statusMutation.mutateAsync({ id, status });
        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: "Supplier status updated successfully",
          });
          return result;
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to update supplier status",
            variant: "destructive",
          });
          return result;
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
        throw error;
      }
    },
    [statusMutation, toast]
  );

  return {
    updateStatus,
    isUpdating: statusMutation.isPending,
  };
}

// Hook for bulk supplier operations
export function useBulkSupplierOperations() {
  const { toast } = useToast();
  const bulkCreateMutation = useBulkCreateSuppliers();

  const bulkCreateSuppliers = useCallback(
    async (suppliers: BulkCreateSupplierDto[], profileImages?: File[]) => {
      try {
        const result = await bulkCreateMutation.mutateAsync({
          suppliers,
          profileImages,
        });
        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: `${
              result.data?.ids.length || 0
            } suppliers created successfully`,
          });
          return result;
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to create suppliers",
            variant: "destructive",
          });
          return result;
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "An unexpected error occurred",
          variant: "destructive",
        });
        throw error;
      }
    },
    [bulkCreateMutation, toast]
  );

  return {
    bulkCreateSuppliers,
    isBulkCreating: bulkCreateMutation.isPending,
  };
}

// Hook for supplier selection management
export function useSupplierSelection(suppliers: SupplierListDto[]) {
  const [selectedSuppliers, setSelectedSuppliers] = useState<string[]>([]);

  const toggleSupplier = useCallback((supplierId: string) => {
    setSelectedSuppliers((prev) =>
      prev.includes(supplierId)
        ? prev.filter((id) => id !== supplierId)
        : [...prev, supplierId]
    );
  }, []);

  const toggleAll = useCallback(() => {
    setSelectedSuppliers((prev) =>
      prev.length === suppliers.length ? [] : suppliers.map((s) => s.id)
    );
  }, [suppliers]);

  const clearSelection = useCallback(() => {
    setSelectedSuppliers([]);
  }, []);

  const isSelected = useCallback(
    (supplierId: string) => selectedSuppliers.includes(supplierId),
    [selectedSuppliers]
  );

  const isAllSelected = useMemo(
    () => suppliers.length > 0 && selectedSuppliers.length === suppliers.length,
    [suppliers.length, selectedSuppliers.length]
  );

  const isIndeterminate = useMemo(
    () =>
      selectedSuppliers.length > 0 &&
      selectedSuppliers.length < suppliers.length,
    [selectedSuppliers.length, suppliers.length]
  );

  return {
    selectedSuppliers,
    toggleSupplier,
    toggleAll,
    clearSelection,
    isSelected,
    isAllSelected,
    isIndeterminate,
    selectedCount: selectedSuppliers.length,
  };
}

// Hook for getting suppliers data (alias for useSuppliersTable)
export function useSuppliersData(
  params: GetSuppliersSchema,
  isDemo: boolean = false
) {
  const query = useQuery({
    queryKey: [...supplierKeys.list(params), { isDemo }],
    queryFn: () => getSuppliersTableData(params, isDemo),
    staleTime: 1000 * 60 * 5, // Always fetch fresh data when params change
    refetchOnMount: true, // Ensure it fetches on mount
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    placeholderData: (previousData) => previousData, // Keep previous data while loading new data
  });

  return {
    ...query,
    suppliers: query.data?.data?.data || [],
    meta: query.data?.data?.meta,
    isSuccess: query.data?.status === ApiStatus.SUCCESS,
    isError: query.data?.status === ApiStatus.FAIL,
    errorMessage: query.data?.message,
  };
}

// Hook for getting supplier data by ID (alias for useSupplier)
export function useSupplierData(id: string, isDemo: boolean = false) {
  return useSupplier(id);
}

// Hook for bulk updating supplier status
export function useBulkUpdateSupplierStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      supplierIds,
      status,
    }: {
      supplierIds: string[];
      status: SupplierStatus;
    }) => {
      return await bulkUpdateSupplierStatus(supplierIds, status, isDemo);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.slim() });
    },
  });
}

// Hook for bulk importing suppliers with images
export function useBulkImportSuppliersWithImages(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      suppliers,
      profileImages,
    }: {
      suppliers: BulkCreateSupplierDto[];
      profileImages?: File[];
    }) => bulkCreateSuppliersApi(suppliers, profileImages),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: supplierKeys.lists() });
      queryClient.invalidateQueries({ queryKey: supplierKeys.slim() });
    },
  });
}

// Hook for supplier availability checks
export function useSupplierAvailability() {
  const [displayNameToCheck, setDisplayNameToCheck] = useState("");
  const [emailToCheck, setEmailToCheck] = useState("");

  const displayNameQuery =
    useSupplierDisplayNameAvailability(displayNameToCheck);
  const emailQuery = useSupplierEmailAvailability(emailToCheck);

  const checkDisplayName = useCallback((displayName: string) => {
    setDisplayNameToCheck(displayName);
  }, []);

  const checkEmail = useCallback((email: string) => {
    setEmailToCheck(email);
  }, []);

  return {
    checkDisplayName,
    checkEmail,
    displayNameAvailable: displayNameQuery.data?.data?.available,
    emailAvailable: emailQuery.data?.data?.available,
    isCheckingDisplayName: displayNameQuery.isFetching,
    isCheckingEmail: emailQuery.isFetching,
  };
}

// Hook for supplier autocomplete search
export function useSuppliersAutocomplete(
  search?: string,
  limit: number = 10,
  isDemo: boolean = false,
  enabled: boolean = true
): UseQueryResult<SupplierAutocompleteResponse, Error> {
  return useQuery({
    queryKey: [...supplierKeys.all, "autocomplete", search, limit],
    queryFn: () => searchSuppliersAutocomplete(search, limit, isDemo),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Export the slim suppliers hook that was imported but not exported
export { useSuppliersSlim };
