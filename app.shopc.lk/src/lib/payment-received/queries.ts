import { useMutation, useQuery } from "@tanstack/react-query";
import {
  createPaymentReceived,
  deletePaymentReceived,
  getPaymentReceived,
  updatePaymentReceived,
} from "./api";
import { CreatePaymentReceivedInput } from "@/types/payment-received";
import { toast } from "@/hooks/use-toast";

export const paymentReceivedKeys = {
  all: ["payment-received"] as const,
  lists: () => [...paymentReceivedKeys.all, "list"] as const,
  list: (filters: any) =>
    [...paymentReceivedKeys.lists(), { filters }] as const,
  details: () => [...paymentReceivedKeys.all, "detail"] as const,
  detail: (id: string) => [...paymentReceivedKeys.details(), id] as const,
};

export const usePaymentReceivedList = (params: {
  page: number;
  perPage: number;
  filters?: any[];
  sort?: { id: string; desc: boolean }[];
}) => {
  return useQuery({
    queryKey: paymentReceivedKeys.list(params),
    queryFn: () => getPaymentReceived(params),
  });
};

export const useCreatePaymentReceived = () => {
  return useMutation({
    mutationFn: (data: CreatePaymentReceivedInput) =>
      createPaymentReceived(data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Payment received created successfully",
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create payment received",
      });
    },
  });
};

export const useUpdatePaymentReceived = () => {
  return useMutation({
    mutationFn: (data: Partial<CreatePaymentReceivedInput> & { id: string }) =>
      updatePaymentReceived(data),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Payment received updated successfully",
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update payment received",
      });
    },
  });
};

export const useDeletePaymentReceived = () => {
  return useMutation({
    mutationFn: (id: string) => deletePaymentReceived(id),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Payment received deleted successfully",
      });
    },
    onError: () => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete payment received",
      });
    },
  });
};
