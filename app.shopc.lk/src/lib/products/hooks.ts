import {
  useQuery,
  useMutation,
  useQueryClient,
  UseQueryResult,
} from "@tanstack/react-query";
import {
  ProductPaginatedResponse,
  ProductNameAvailabilityResponse,
  ProductSkuAvailabilityResponse,
  ProductSlugAvailabilityResponse,
  ProductBarcodeAvailabilityResponse,
  SimpleProductResponse,
  ProductResponse,
  CreateProductDto,
  UpdateProductDto,
  ProductStatus,
  UpdateProductGlobalPositionsDto,
  UpdateProductCategoryPositionsDto,
  UpdateProductSubCategoryPositionsDto,
  BulkDeleteProductDto,
} from "@/types/product";
import { ApiStatus } from "@/types/common";
import { GetProductsSchema } from "./validations";
import {
  getProductsTableData,
  getProductsSlim,
  getProduct,
  checkProductNameAvailability,
  checkProductSkuAvailability,
  checkProductSlugAvailability,
  checkProductBarcodeAvailability,
  createProduct,
  bulkCreateProducts,
  updateProduct,
  deleteProduct,
  bulkDeleteProducts,
  updateProductGlobalPositions,
  updateProductCategoryPositions,
  updateProductSubCategoryPositions,
} from "./queries";

// Query keys for cache management
export const productKeys = {
  all: ["products"] as const,
  list: () => [...productKeys.all, "list"] as const,
  filtered: (params: GetProductsSchema & { isDemo?: boolean }) =>
    [...productKeys.list(), params] as const,
  slim: () => [...productKeys.all, "slim"] as const,
  detail: (id: string) => [...productKeys.all, "detail", id] as const,
  nameAvailability: (name: string) =>
    [...productKeys.all, "name-availability", name] as const,
  skuAvailability: (sku: string) =>
    [...productKeys.all, "sku-availability", sku] as const,
  slugAvailability: (slug: string) =>
    [...productKeys.all, "slug-availability", slug] as const,
  barcodeAvailability: (barcode: string) =>
    [...productKeys.all, "barcode-availability", barcode] as const,
};

// Hook to get products with pagination and filtering
export function useProductsData(
  queryParams: GetProductsSchema,
  isDemo: boolean = false
): UseQueryResult<ProductPaginatedResponse, Error> {
  return useQuery({
    queryKey: productKeys.filtered({ ...queryParams, isDemo }),
    queryFn: () => getProductsTableData(queryParams, isDemo),
    staleTime: 1000 * 60 * 5, // Always fetch fresh data when params change
    refetchOnMount: true, // Ensure it fetches on mount
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    placeholderData: (previousData) => previousData, // Keep previous data while loading new data
  });
}

// Legacy hook for backward compatibility
export const useProducts = useProductsData;

// Hook to get products in slim format
export function useProductsSlim(
  isDemo: boolean,
  enabled: boolean = true
): UseQueryResult<SimpleProductResponse, Error> {
  return useQuery({
    queryKey: productKeys.slim(),
    queryFn: () => getProductsSlim(isDemo),
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook to get a single product
export function useProductData(
  id: string,
  isDemo: boolean = false
): UseQueryResult<ProductResponse> {
  return useQuery({
    queryKey: [...productKeys.detail(id), { isDemo }],
    queryFn: () => getProduct(id, isDemo),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Legacy hook for backward compatibility
export const useProduct = useProductData;

// Hook to check product name availability
export function useProductNameAvailability(
  name: string,
  isDemo: boolean = false,
  enabled: boolean = true
): UseQueryResult<ProductNameAvailabilityResponse, Error> {
  return useQuery({
    queryKey: [...productKeys.nameAvailability(name), { isDemo }],
    queryFn: () => checkProductNameAvailability({ name }, isDemo),
    enabled: Boolean(enabled && !!name && name.length > 0),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook to check product SKU availability
export function useProductSkuAvailability(
  sku: string,
  isDemo: boolean = false,
  enabled: boolean = true
): UseQueryResult<ProductSkuAvailabilityResponse, Error> {
  return useQuery({
    queryKey: [...productKeys.skuAvailability(sku), { isDemo }],
    queryFn: () => checkProductSkuAvailability({ sku }, isDemo),
    enabled: Boolean(enabled && !!sku && sku.length > 0),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook to check product slug availability
export function useProductSlugAvailability(
  slug: string,
  isDemo: boolean = false,
  enabled: boolean = true
): UseQueryResult<ProductSlugAvailabilityResponse, Error> {
  return useQuery({
    queryKey: [...productKeys.slugAvailability(slug), { isDemo }],
    queryFn: () => checkProductSlugAvailability({ slug }, isDemo),
    enabled: Boolean(enabled && !!slug && slug.length > 0),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook to check product barcode availability
export function useProductBarcodeAvailability(
  barcode: string,
  isDemo: boolean = false,
  enabled: boolean = true
): UseQueryResult<ProductBarcodeAvailabilityResponse, Error> {
  return useQuery({
    queryKey: [...productKeys.barcodeAvailability(barcode), { isDemo }],
    queryFn: () => checkProductBarcodeAvailability({ barcode }, isDemo),
    enabled: Boolean(enabled && !!barcode && barcode.length > 0),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook to create a product
export function useCreateProduct(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      data,
      image,
      ogImage,
    }: {
      data: CreateProductDto;
      image?: File;
      ogImage?: File;
    }) => createProduct(data, isDemo, image, ogImage),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Hook to bulk create products
export function useBulkCreateProducts(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      products,
      images,
    }: {
      products: CreateProductDto[];
      images?: File[];
    }) => bulkCreateProducts({ products }, isDemo, images),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Hook to update a product
export function useUpdateProduct(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
      image,
      ogImage,
    }: {
      id: string;
      data: UpdateProductDto;
      image?: File;
      ogImage?: File;
    }) => updateProduct(id, data, isDemo, image, ogImage),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({
        queryKey: productKeys.detail(variables.id),
      });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Hook to delete a product
export function useDeleteProduct(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteProduct(id, isDemo),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.removeQueries({ queryKey: productKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Hook to bulk delete products
export function useBulkDeleteProducts(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkDeleteProductDto) =>
      bulkDeleteProducts(data, isDemo),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
      // Remove individual product queries
      variables.productIds.forEach((id) => {
        queryClient.removeQueries({ queryKey: productKeys.detail(id) });
      });
    },
  });
}

// Hook to update product global positions
export function useUpdateProductGlobalPositions(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateProductGlobalPositionsDto) =>
      updateProductGlobalPositions(data, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
    },
  });
}

// Hook to update product category positions
export function useUpdateProductCategoryPositions(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateProductCategoryPositionsDto) =>
      updateProductCategoryPositions(data, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
    },
  });
}

// Hook to update product sub-category positions
export function useUpdateProductSubCategoryPositions(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateProductSubCategoryPositionsDto) =>
      updateProductSubCategoryPositions(data, isDemo),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
    },
  });
}

// Custom hook for product table with built-in refresh capability
export function useProductsTable(
  params: GetProductsSchema,
  isDemo: boolean = false
) {
  const queryClient = useQueryClient();
  const query = useProductsData(params, isDemo);

  const refresh = () => {
    queryClient.invalidateQueries({
      queryKey: productKeys.filtered({ ...params, isDemo }),
    });
  };

  return {
    ...query,
    refresh,
  };
}

// Custom hook for product form with availability checks
export function useProductForm(isDemo: boolean = false) {
  const createMutation = useCreateProduct(isDemo);
  const updateMutation = useUpdateProduct(isDemo);

  return {
    createProduct: createMutation,
    updateProduct: updateMutation,
    isLoading: createMutation.isPending || updateMutation.isPending,
  };
}

// Hook to bulk update product status
export function useBulkUpdateProductStatus(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      productIds,
      status,
    }: {
      productIds: string[];
      status: ProductStatus;
    }) => {
      // This would need to be implemented in the API
      // For now, return a placeholder
      return Promise.resolve({
        status: ApiStatus.SUCCESS,
        message: "Status updated successfully",
        data: { updated: productIds.length },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Hook to bulk update product availability
export function useBulkUpdateProductAvailability(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      productIds,
      availableOnline,
    }: {
      productIds: string[];
      availableOnline: boolean;
    }) => {
      // This would need to be implemented in the API
      // For now, return a placeholder
      return Promise.resolve({
        status: ApiStatus.SUCCESS,
        message: "Availability updated successfully",
        data: { updated: productIds.length },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Hook to bulk update product featured status
export function useBulkUpdateProductFeatured(isDemo: boolean = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      productIds,
      featured,
    }: {
      productIds: string[];
      featured: boolean;
    }) => {
      // This would need to be implemented in the API
      // For now, return a placeholder
      return Promise.resolve({
        status: ApiStatus.SUCCESS,
        message: "Featured status updated successfully",
        data: { updated: productIds.length },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productKeys.list() });
      queryClient.invalidateQueries({ queryKey: productKeys.slim() });
    },
  });
}

// Legacy hooks for backward compatibility
export const useProductsSimple = useProductsSlim;
export const useProductDetail = useProductData;
