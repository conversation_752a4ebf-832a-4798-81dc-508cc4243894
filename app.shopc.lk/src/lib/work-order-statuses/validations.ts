import { z } from "zod";
import { WorkOrderStatusType } from "@/types/work-order-status";

// Backend DTO: CreateWorkOrderStatusDto
export const createWorkOrderStatusSchema = z.object({
  statusCode: z.string().min(1, "Status code is required").max(50, "Status code must be 50 characters or less"),
  statusName: z.string().min(1, "Status name is required").max(100, "Status name must be 100 characters or less"),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  statusType: z.nativeEnum(WorkOrderStatusType, {
    errorMap: () => ({ message: "Invalid status type" }),
  }),
  isActive: z.boolean().optional().default(true),
  isDefault: z.boolean().optional().default(false),
});

// Backend DTO: UpdateWorkOrderStatusDto
export const updateWorkOrderStatusSchema = z.object({
  statusCode: z.string().min(1, "Status code is required").max(50, "Status code must be 50 characters or less").optional(),
  statusName: z.string().min(1, "Status name is required").max(100, "Status name must be 100 characters or less").optional(),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  statusType: z.nativeEnum(WorkOrderStatusType, {
    errorMap: () => ({ message: "Invalid status type" }),
  }).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
});

// Schema for getting work order statuses with pagination and filters
export const getWorkOrderStatusesSchema = z.object({
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).default(10),
  from: z.string().optional(),
  to: z.string().optional(),
  // Individual filter fields
  statusName: z.string().optional(),
  statusCode: z.string().optional(),
  statusType: z.string().optional(),
  isActive: z.string().optional(),
  isDefault: z.string().optional(),
  description: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([{ id: "createdAt", desc: true }]),
});

// Schema for updating work order status active status
export const updateWorkOrderStatusActiveStatusSchema = z.object({
  id: z.string().min(1, "ID is required"),
  isActive: z.boolean(),
});

// Schema for bulk creation
export const bulkCreateWorkOrderStatusesSchema = z.array(createWorkOrderStatusSchema);

// Schema for bulk deletion
export const bulkDeleteWorkOrderStatusesSchema = z.object({
  workOrderStatusIds: z.array(z.string().uuid("Invalid work order status ID")).min(1, "At least one work order status ID is required"),
});

// Schema for bulk status update
export const bulkUpdateWorkOrderStatusStatusSchema = z.object({
  workOrderStatusIds: z.array(z.string().uuid("Invalid work order status ID")).min(1, "At least one work order status ID is required"),
  isActive: z.boolean(),
});

// Schema for position updates
export const workOrderStatusPositionSchema = z.object({
  id: z.string().uuid("Invalid work order status ID"),
  position: z.number().min(1, "Position must be at least 1"),
});

export const updateWorkOrderStatusPositionsSchema = z.object({
  workOrderStatuses: z.array(workOrderStatusPositionSchema).min(1, "At least one work order status position update is required"),
});

// Form schema for UI components (includes additional fields for form handling)
export const workOrderStatusFormSchema = z.object({
  statusCode: z.string().min(1, "Status code is required").max(50, "Status code must be 50 characters or less"),
  statusName: z.string().min(1, "Status name is required").max(100, "Status name must be 100 characters or less"),
  description: z.string().optional(),
  colorCode: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color (e.g., #3B82F6)")
    .optional(),
  iconName: z.string().max(50, "Icon name must be 50 characters or less").optional(),
  statusType: z.nativeEnum(WorkOrderStatusType, {
    errorMap: () => ({ message: "Invalid status type" }),
  }),
  isActive: z.boolean().optional().default(true),
  isDefault: z.boolean().optional().default(false),
});

// Schema for work order status name availability check
export const checkWorkOrderStatusNameSchema = z.object({
  statusName: z.string().min(1, "Status name is required"),
  excludeId: z.string().uuid("Invalid work order status ID").optional(),
});

export type GetWorkOrderStatusesSchema = z.infer<typeof getWorkOrderStatusesSchema>;
export type CreateWorkOrderStatusSchema = z.infer<typeof createWorkOrderStatusSchema>;
export type UpdateWorkOrderStatusSchema = z.infer<typeof updateWorkOrderStatusSchema>;
export type UpdateWorkOrderStatusActiveStatusSchema = z.infer<typeof updateWorkOrderStatusActiveStatusSchema>;
export type BulkCreateWorkOrderStatusesSchema = z.infer<typeof bulkCreateWorkOrderStatusesSchema>;
export type BulkDeleteWorkOrderStatusesSchema = z.infer<typeof bulkDeleteWorkOrderStatusesSchema>;
export type BulkUpdateWorkOrderStatusStatusSchema = z.infer<typeof bulkUpdateWorkOrderStatusStatusSchema>;
export type WorkOrderStatusPositionSchema = z.infer<typeof workOrderStatusPositionSchema>;
export type UpdateWorkOrderStatusPositionsSchema = z.infer<typeof updateWorkOrderStatusPositionsSchema>;
export type WorkOrderStatusFormSchema = z.infer<typeof workOrderStatusFormSchema>;
export type CheckWorkOrderStatusNameSchema = z.infer<typeof checkWorkOrderStatusNameSchema>;
