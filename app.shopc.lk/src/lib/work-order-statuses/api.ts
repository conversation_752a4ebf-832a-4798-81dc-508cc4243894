import {
  WorkOrderStatusResponse,
  WorkOrderStatusPaginatedResponse,
  WorkOrderStatusIdResponse,
  BulkWorkOrderStatusIdsResponse,
  BulkDeleteWorkOrderStatusDto,
  BulkDeleteWorkOrderStatusResponse,
  WorkOrderStatusNameAvailabilityResponse,
  SimpleWorkOrderStatusResponse,
  WorkOrderStatusListResponse,
  CreateWorkOrderStatusDto,
  UpdateWorkOrderStatusDto,
  BulkCreateWorkOrderStatusDto,
  BulkUpdateWorkOrderStatusStatusDto,
  BulkUpdateWorkOrderStatusStatusResponse,
  UpdateWorkOrderStatusPositionsDto,
  UpdateWorkOrderStatusPositionsResponse,
} from "@/types/work-order-status";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetWorkOrderStatusesSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new work order status
export async function createWorkOrderStatusApi(
  data: CreateWorkOrderStatusDto
): Promise<WorkOrderStatusIdResponse> {
  try {
    const res = await axios.post("/work-order-statuses", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create work order statuses
export async function bulkCreateWorkOrderStatusesApi(
  workOrderStatuses: BulkCreateWorkOrderStatusDto[]
): Promise<BulkWorkOrderStatusIdsResponse> {
  try {
    const res = await axios.post("/work-order-statuses/bulk", {
      workOrderStatuses,
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get work order statuses with pagination and filters
export async function getWorkOrderStatusesApi(
  params: GetWorkOrderStatusesSchema
): Promise<WorkOrderStatusPaginatedResponse> {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append("page", params.page.toString());
    if (params.perPage) queryParams.append("limit", params.perPage.toString());
    if (params.from) queryParams.append("from", params.from);
    if (params.to) queryParams.append("to", params.to);
    
    // Add filter parameters
    if (params.statusName) queryParams.append("statusName", params.statusName);
    if (params.statusCode) queryParams.append("statusCode", params.statusCode);
    if (params.statusType) queryParams.append("statusType", params.statusType);
    if (params.isActive) queryParams.append("isActive", params.isActive);
    if (params.isDefault) queryParams.append("isDefault", params.isDefault);
    if (params.description) queryParams.append("description", params.description);
    
    // Add advanced filters and sorting
    if (params.filters && params.filters.length > 0) {
      queryParams.append("filters", JSON.stringify(params.filters));
    }
    if (params.joinOperator) queryParams.append("joinOperator", params.joinOperator);
    if (params.sort && params.sort.length > 0) {
      queryParams.append("sort", JSON.stringify(params.sort));
    }

    const res = await axios.get(`/work-order-statuses?${queryParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get work order statuses in slim format (for dropdowns/selects)
export async function getWorkOrderStatusesSlimApi(): Promise<SimpleWorkOrderStatusResponse> {
  try {
    const res = await axios.get("/work-order-statuses/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get work order statuses in list format
export async function getWorkOrderStatusesListApi(): Promise<WorkOrderStatusListResponse> {
  try {
    const res = await axios.get("/work-order-statuses/list");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single work order status by ID
export async function getWorkOrderStatusApi(id: string): Promise<WorkOrderStatusResponse> {
  try {
    const res = await axios.get(`/work-order-statuses/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update a work order status
export async function updateWorkOrderStatusApi(
  id: string,
  data: UpdateWorkOrderStatusDto
): Promise<WorkOrderStatusIdResponse> {
  try {
    const res = await axios.patch(`/work-order-statuses/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete a work order status
export async function deleteWorkOrderStatusApi(id: string): Promise<ApiResponse<{ message: string; deletedId: string }>> {
  try {
    const res = await axios.delete(`/work-order-statuses/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete work order statuses
export async function bulkDeleteWorkOrderStatusesApi(
  data: BulkDeleteWorkOrderStatusDto
): Promise<BulkDeleteWorkOrderStatusResponse> {
  try {
    const res = await axios.delete("/work-order-statuses/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk update work order status active status
export async function bulkUpdateWorkOrderStatusStatusApi(
  data: BulkUpdateWorkOrderStatusStatusDto
): Promise<BulkUpdateWorkOrderStatusStatusResponse> {
  try {
    const res = await axios.patch("/work-order-statuses/bulk/status", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update work order status positions
export async function updateWorkOrderStatusPositionsApi(
  data: UpdateWorkOrderStatusPositionsDto
): Promise<UpdateWorkOrderStatusPositionsResponse> {
  try {
    const res = await axios.patch("/work-order-statuses/positions", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check work order status name availability
export async function checkWorkOrderStatusNameAvailabilityApi(
  statusName: string,
  excludeId?: string
): Promise<WorkOrderStatusNameAvailabilityResponse> {
  try {
    const queryParams = new URLSearchParams();
    if (excludeId) queryParams.append("excludeId", excludeId);
    
    const url = `/work-order-statuses/check-name/${encodeURIComponent(statusName)}${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    
    const res = await axios.get(url);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}
