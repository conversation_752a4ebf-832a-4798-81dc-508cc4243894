export type IconName = 
  | 'home'
  | 'menu'
  | 'close'
  | 'search'
  | 'plus'
  | 'minus'
  | 'arrowLeft'
  | 'arrowRight'
  | 'arrowUp'
  | 'arrowDown'
  | 'chevronLeft'
  | 'chevronRight'
  | 'chevronUp'
  | 'chevronDown'
  | 'user'
  | 'users'
  | 'settings'
  | 'check'
  | 'x'
  | 'warning'
  | 'info'
  | 'bell'
  | 'mail'
  | 'calendar'
  | 'clock'
  | 'download'
  | 'upload'
  | 'file'
  | 'folder'
  | 'edit'
  | 'trash'
  | 'heart'
  | 'star'
  | 'bookmark'
  | 'share'
  | 'link'
  | 'eye'
  | 'eyeOff'
  | 'lock'
  | 'unlock'
  | 'key'
  | 'shield'
  | 'globe'
  | 'map'
  | 'phone'
  | 'message'
  | 'send'
  | 'image'
  | 'video'
  | 'music'
  | 'mic'
  | 'volume'
  | 'wifi'
  | 'bluetooth'
  | 'battery'
  | 'power'
  | 'sun'
  | 'moon'
  | 'cloud'
  | 'lightning'
  | 'database'
  | 'server'
  | 'terminal'
  | 'code'
  | 'git'
  | 'github'
  | 'twitter'
  | 'linkedin'
  | 'facebook'
  | 'instagram'
  | 'youtube';

export interface IconPath {
  d: string;
  fill?: string;
  stroke?: string;
  strokeWidth?: number;
  strokeLinecap?: 'butt' | 'round' | 'square';
  strokeLinejoin?: 'miter' | 'round' | 'bevel';
  fillRule?: 'nonzero' | 'evenodd';
  clipRule?: 'nonzero' | 'evenodd';
}

export interface IconDefinition {
  viewBox: string;
  paths: IconPath[];
}

export interface IconProps extends Omit<React.SVGAttributes<SVGElement>, 'onLoad' | 'onError'> {
  name: IconName;
  size?: number;
  color?: string;
  strokeWidth?: number;
  className?: string;
  fallback?: React.ReactNode;
  onLoad?: (name: string) => void;
  onError?: (error: Error) => void;
}

export type IconRegistry = Record<IconName, IconDefinition>;