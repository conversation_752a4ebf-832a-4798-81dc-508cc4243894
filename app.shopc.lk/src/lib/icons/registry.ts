import type { IconName, IconRegistry } from "./types";

export const iconRegistry: IconRegistry = {
  // Navigation Icons
  home: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M9 22V12h6v10", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  menu: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M3 12h18M3 6h18M3 18h18",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  close: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M18 6L6 18M6 6l12 12",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Action Icons
  search: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  plus: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 5v14m-7-7h14",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  minus: {
    viewBox: "0 0 24 24",
    paths: [{ d: "M5 12h14", strokeLinecap: "round", strokeLinejoin: "round" }],
  },

  // Arrow Icons
  arrowLeft: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M19 12H5m0 0l7 7m-7-7l7-7",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  arrowRight: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M5 12h14m0 0l-7-7m7 7l-7 7",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  arrowUp: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 19V5m0 0l-7 7m7-7l7 7",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  arrowDown: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 5v14m0 0l7-7m-7 7l-7-7",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Chevron Icons
  chevronLeft: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M15 18l-6-6 6-6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  chevronRight: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M9 18l6-6-6-6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  chevronUp: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M18 15l-6-6-6 6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  chevronDown: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M6 9l6 6 6-6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },

  // User & Account Icons
  user: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  users: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M9 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M23 21v-2a4 4 0 0 0-3-3.87",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M16 3.13a4 4 0 0 1 0 7.75",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  settings: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 15a3 3 0 100-6 3 3 0 000 6z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Status Icons
  check: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M20 6L9 17l-5-5", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  x: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M18 6L6 18M6 6l12 12",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  warning: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  info: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M12 16v-4m0-4h.01",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Communication Icons
  bell: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M18 8A6 6 0 1 0 6 8c0 7-3 9-3 9h18s-3-2-3-9",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M13.73 21a2 2 0 0 1-3.46 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  mail: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M22 6l-10 7L2 6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  message: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  send: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M22 2L11 13", strokeLinecap: "round", strokeLinejoin: "round" },
      {
        d: "M22 2l-7 20-4-9-9-4 20-7z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Time Icons
  calendar: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM16 1v4M8 1v4M3 9h18",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  clock: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M12 6v6l4 2", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },

  // File Management Icons
  download: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M7 10l5 5 5-5", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M12 15V3", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  upload: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M17 8l-5-5-5 5", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M12 3v12", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  file: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M13 2v7h7", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  folder: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Edit Icons
  edit: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  trash: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M3 6h18", strokeLinecap: "round", strokeLinejoin: "round" },
      {
        d: "M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M10 11v6", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M14 11v6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },

  // Interactive Icons
  heart: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  star: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  bookmark: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  share: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M16 6l-4-4-4 4", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M12 2v13", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  link: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // View Icons
  eye: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  eyeOff: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M1 1l22 22", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },

  // Security Icons
  lock: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M19 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M7 11V7a5 5 0 0 1 10 0v4",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  unlock: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M19 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M7 11V7a5 5 0 0 1 9.9-1",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  key: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  shield: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Location Icons
  globe: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M2 12h20", strokeLinecap: "round", strokeLinejoin: "round" },
      {
        d: "M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  map: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M1 6v16l7-4 8 4 7-4V2l-7 4-8-4-7 4z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M8 2v16", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M16 6v16", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },

  // Device Icons
  phone: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Media Icons
  image: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M8.5 10a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M21 15l-5-5L5 21",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  video: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M23 7l-7 5 7 5V7z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M16 5H3a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h13a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  music: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M9 18V5l12-2v13", strokeLinecap: "round", strokeLinejoin: "round" },
      {
        d: "M6 15a3 3 0 1 0 0 6 3 3 0 0 0 0-6z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M18 13a3 3 0 1 0 0 6 3 3 0 0 0 0-6z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  mic: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 1a4 4 0 0 0-4 4v7a4 4 0 0 0 8 0V5a4 4 0 0 0-4-4z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M19 10v2a7 7 0 0 1-14 0v-2",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M12 19v4", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M8 23h8", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  volume: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M11 5L6 9H2v6h4l5 4V5z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // System Icons
  wifi: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M5 12.55a11 11 0 0 1 14.08 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M1.42 9a16 16 0 0 1 21.16 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M8.53 16.11a6 6 0 0 1 6.95 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M12 20h.01", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  bluetooth: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M6.5 6.5l11 11L12 23V1l5.5 5.5-11 11",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  battery: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M23 13v-2", strokeLinecap: "round", strokeLinejoin: "round" },
      {
        d: "M5 7H3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-2",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  power: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M18.36 6.64a9 9 0 1 1-12.73 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M12 2v10", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },

  // Weather Icons
  sun: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M12 17a5 5 0 1 0 0-10 5 5 0 0 0 0 10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  moon: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  cloud: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M18 10h-1.26A8 8 0 1 0 9 20h9a5 5 0 0 0 0-10z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  lightning: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M13 2L3 14h9l-1 8 10-12h-9l1-8z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Development Icons
  database: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M3 5a9 3 0 0 1 18 0 9 3 0 0 1-18 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M21 5v14a9 3 0 0 1-18 0V5",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M21 12a9 3 0 0 1-18 0",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  server: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M20 2H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2zM20 14H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4a2 2 0 0 0-2-2z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M6 6h.01M6 18h.01",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  terminal: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M4 17l6-6-6-6", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M12 19h8", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  code: {
    viewBox: "0 0 24 24",
    paths: [
      { d: "M16 18l6-6-6-6", strokeLinecap: "round", strokeLinejoin: "round" },
      { d: "M8 6l-6 6 6 6", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  git: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M9 19a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM9 5a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM21 19a3 3 0 1 0 0-6 3 3 0 0 0 0 6zM9 13V5M9 19v-6M21 16v-7a4 4 0 0 0-4-4h-5",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },

  // Social Icons
  github: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  twitter: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  linkedin: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M2 9h4v12H2z", strokeLinecap: "round", strokeLinejoin: "round" },
      {
        d: "M4 6a2 2 0 1 0 0-4 2 2 0 0 0 0 4z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  facebook: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
  instagram: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M22 2H2a5 5 0 0 0-5 5v10a5 5 0 0 0 5 5h10a5 5 0 0 0 5-5V7a5 5 0 0 0-5-5z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M12 16a4 4 0 1 0 0-8 4 4 0 0 0 0 8z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      { d: "M17.5 6.5h.01", strokeLinecap: "round", strokeLinejoin: "round" },
    ],
  },
  youtube: {
    viewBox: "0 0 24 24",
    paths: [
      {
        d: "M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
      {
        d: "M9.75 15.02l5.75-3.27-5.75-3.27v6.54z",
        strokeLinecap: "round",
        strokeLinejoin: "round",
      },
    ],
  },
};

export const getIconNames = (): IconName[] =>
  Object.keys(iconRegistry) as IconName[];
export const hasIcon = (name: string): name is IconName => name in iconRegistry;
