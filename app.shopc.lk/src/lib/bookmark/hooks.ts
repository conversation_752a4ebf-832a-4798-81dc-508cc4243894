import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ApiStatus } from "@/types/common";
import { toast } from "sonner";
import {
  CreateBookmarkRequest,
  UpdateBookmarkRequest,
  CreateBookmarkFolderRequest,
  UpdateBookmarkFolderRequest,
  BookmarkQueryParams,
  BulkBookmarkOperation,
  BookmarkImportData,
} from "@/types/bookmark";
import {
  createBookmark,
  updateBookmark,
  deleteBookmark,
  getBookmark,
  getBookmarks,
  createBookmarkFolder,
  updateBookmarkFolder,
  deleteBookmarkFolder,
  getBookmarkFolder,
  getBookmarkFolders,
  bulkBookmarkOperation,
  searchBookmarks,
  importBookmarks,
  exportBookmarks,
  getBookmarkStats,
  checkBookmarkExists,
  getBookmarkByUrl,
} from "./api";

// Query keys for cache management
export const bookmarkKeys = {
  all: ["bookmarks"] as const,
  lists: () => [...bookmarkKeys.all, "list"] as const,
  list: (params?: BookmarkQueryParams) => [...bookmarkKeys.lists(), params] as const,
  details: () => [...bookmarkKeys.all, "detail"] as const,
  detail: (id: string) => [...bookmarkKeys.details(), id] as const,
  folders: () => [...bookmarkKeys.all, "folders"] as const,
  folder: (id: string) => [...bookmarkKeys.folders(), id] as const,
  search: (query: string) => [...bookmarkKeys.all, "search", query] as const,
  stats: () => [...bookmarkKeys.all, "stats"] as const,
  byUrl: (url: string) => [...bookmarkKeys.all, "byUrl", url] as const,
  exists: (url: string) => [...bookmarkKeys.all, "exists", url] as const,
};

// Bookmark CRUD hooks
export function useCreateBookmark() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBookmarkRequest) => {
      const response = await createBookmark(data);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to create bookmark");
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.stats() });
      
      toast.success(data?.message || "Bookmark has been created successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUpdateBookmark() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateBookmarkRequest }) => {
      const response = await updateBookmark(id, data);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to update bookmark");
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.detail(variables.id) });
      
      toast.success(data?.message || "Bookmark has been updated successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useDeleteBookmark() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await deleteBookmark(id);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to delete bookmark");
      }
      return response.data;
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.stats() });
      queryClient.removeQueries({ queryKey: bookmarkKeys.detail(id) });
      
      toast.success(data?.message || "Bookmark has been deleted successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useBookmark(id: string) {
  return useQuery({
    queryKey: bookmarkKeys.detail(id),
    queryFn: async () => {
      const response = await getBookmark(id);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch bookmark");
      }
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useBookmarks(params?: BookmarkQueryParams) {
  return useQuery({
    queryKey: bookmarkKeys.list(params),
    queryFn: async () => {
      const response = await getBookmarks(params);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch bookmarks");
      }
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Bookmark folder hooks
export function useCreateBookmarkFolder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateBookmarkFolderRequest) => {
      const response = await createBookmarkFolder(data);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to create folder");
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.folders() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      
      toast.success(data?.message || "Folder has been created successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUpdateBookmarkFolder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateBookmarkFolderRequest }) => {
      const response = await updateBookmarkFolder(id, data);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to update folder");
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.folders() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.folder(variables.id) });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      
      toast.success(data?.message || "Folder has been updated successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useDeleteBookmarkFolder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await deleteBookmarkFolder(id);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to delete folder");
      }
      return response.data;
    },
    onSuccess: (data, id) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.folders() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      queryClient.removeQueries({ queryKey: bookmarkKeys.folder(id) });
      
      toast.success(data?.message || "Folder has been deleted successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useBookmarkFolders() {
  return useQuery({
    queryKey: bookmarkKeys.folders(),
    queryFn: async () => {
      const response = await getBookmarkFolders();
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch folders");
      }
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useBookmarkFolder(id: string) {
  return useQuery({
    queryKey: bookmarkKeys.folder(id),
    queryFn: async () => {
      const response = await getBookmarkFolder(id);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch folder");
      }
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Bulk operations hook
export function useBulkBookmarkOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (operation: BulkBookmarkOperation) => {
      const response = await bulkBookmarkOperation(operation);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to perform bulk operation");
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.stats() });
      
      toast.success(data?.message || "Bulk operation has been completed successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Search hook
export function useSearchBookmarks(query: string, params?: Pick<BookmarkQueryParams, 'limit' | 'page'>) {
  return useQuery({
    queryKey: bookmarkKeys.search(query),
    queryFn: async () => {
      const response = await searchBookmarks(query, params);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to search bookmarks");
      }
      return response.data;
    },
    enabled: !!query.trim(),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Import/Export hooks
export function useImportBookmarks() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BookmarkImportData) => {
      const response = await importBookmarks(data);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to import bookmarks");
      }
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.lists() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.folders() });
      queryClient.invalidateQueries({ queryKey: bookmarkKeys.stats() });
      
      toast.success(data?.message || "Bookmarks have been imported successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useExportBookmarks() {

  return useMutation({
    mutationFn: async () => {
      const blob = await exportBookmarks();
      
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `bookmarks-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      return { success: true };
    },
    onSuccess: () => {
      toast.success("Bookmarks have been exported successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

// Statistics hook
export function useBookmarkStats() {
  return useQuery({
    queryKey: bookmarkKeys.stats(),
    queryFn: async () => {
      const response = await getBookmarkStats();
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch bookmark statistics");
      }
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Utility hooks
export function useCheckBookmarkExists(url: string) {
  return useQuery({
    queryKey: bookmarkKeys.exists(url),
    queryFn: () => checkBookmarkExists(url),
    enabled: !!url,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useBookmarkByUrl(url: string) {
  return useQuery({
    queryKey: bookmarkKeys.byUrl(url),
    queryFn: async () => {
      const response = await getBookmarkByUrl(url);
      if (response.status !== ApiStatus.SUCCESS) {
        throw new Error(response.message || "Failed to fetch bookmark by URL");
      }
      return response.data;
    },
    enabled: !!url,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Optimistic update helper
export function useOptimisticBookmarkUpdate() {
  const queryClient = useQueryClient();

  const updateBookmarkOptimistic = (id: string, updates: Partial<UpdateBookmarkRequest>) => {
    queryClient.setQueryData(bookmarkKeys.detail(id), (old: any) => {
      if (!old) return old;
      return { ...old, ...updates };
    });
  };

  const revertBookmarkOptimistic = (id: string) => {
    queryClient.invalidateQueries({ queryKey: bookmarkKeys.detail(id) });
  };

  return { updateBookmarkOptimistic, revertBookmarkOptimistic };
}