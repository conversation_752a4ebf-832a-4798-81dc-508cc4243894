import { useMutation, useQuery, useQueryClient, UseQueryResult } from "@tanstack/react-query";
import {
  WarrantyTemplateDto,
  WarrantyTemplateSlimDto,
  PaginatedWarrantyTemplatesResponseDto,
  CreateWarrantyTemplateDto,
  UpdateWarrantyTemplateDto,
  WarrantyTemplateNameAvailabilityResponseDto,
  WarrantyTemplateIdResponseDto,
  BulkCreateWarrantyTemplateDto,
  BulkWarrantyTemplateIdsResponseDto,
  DeleteWarrantyTemplateResponseDto,
  WarrantyTemplateAutocompleteDto,
  BulkUpdateWarrantyTemplateStatusDto,
  BulkUpdateWarrantyTemplateStatusResponseDto,
  BulkDeleteWarrantyTemplateDto,
  BulkDeleteWarrantyTemplateResponseDto,
  WarrantyTemplateTableData,
} from "@/types/warranty-templates";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetWarrantyTemplatesSchema } from "./validations";
import {
  getWarrantyTemplatesApi,
  getWarrantyTemplateApi,
  getWarrantyTemplatesSlimApi,
  createWarrantyTemplateApi,
  updateWarrantyTemplateApi,
  deleteWarrantyTemplateApi,
  bulkCreateWarrantyTemplatesApi,
  checkWarrantyTemplateNameAvailabilityApi,
  getWarrantyTemplatesAutocompleteApi,
  bulkUpdateWarrantyTemplateStatusApi,
  bulkDeleteWarrantyTemplatesApi,
} from "./api";
import {
  getWarrantyTemplatesDemoApi,
  getWarrantyTemplateDemoApi,
  getWarrantyTemplatesSlimDemoApi,
  createWarrantyTemplateDemoApi,
  updateWarrantyTemplateDemoApi,
  deleteWarrantyTemplateDemoApi,
  bulkCreateWarrantyTemplatesDemoApi,
  checkWarrantyTemplateNameAvailabilityDemoApi,
  getWarrantyTemplatesAutocompleteDemoApi,
  bulkUpdateWarrantyTemplateStatusDemoApi,
  bulkDeleteWarrantyTemplatesDemoApi,
} from "./demo";
import { toast } from "sonner";

// Query keys
export const warrantyTemplateKeys = {
  all: ["warrantyTemplates"] as const,
  lists: () => [...warrantyTemplateKeys.all, "list"] as const,
  list: (filters: string) => [...warrantyTemplateKeys.lists(), { filters }] as const,
  details: () => [...warrantyTemplateKeys.all, "detail"] as const,
  detail: (id: string) => [...warrantyTemplateKeys.details(), id] as const,
  slim: () => [...warrantyTemplateKeys.all, "slim"] as const,
  autocomplete: (query?: string) => [...warrantyTemplateKeys.all, "autocomplete", query] as const,
  nameAvailability: (name: string, excludeId?: string) =>
    [...warrantyTemplateKeys.all, "nameAvailability", name, excludeId] as const,
};

// Get warranty templates with pagination and filtering
export function useWarrantyTemplates(
  params: GetWarrantyTemplatesSchema,
  isDemo = false
) {
  return useQuery({
    queryKey: warrantyTemplateKeys.list(JSON.stringify(params)),
    queryFn: (): Promise<ApiResponse<PaginatedWarrantyTemplatesResponseDto | null>> =>
      isDemo ? getWarrantyTemplatesDemoApi(params) : getWarrantyTemplatesApi(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get warranty templates with pagination and filtering (optimized for table data)
export function useWarrantyTemplatesData(
  params: GetWarrantyTemplatesSchema,
  isDemo: boolean = false
) {
  const query = useQuery({
    queryKey: warrantyTemplateKeys.list(JSON.stringify({ ...params, isDemo })),
    queryFn: (): Promise<ApiResponse<PaginatedWarrantyTemplatesResponseDto | null>> =>
      isDemo ? getWarrantyTemplatesDemoApi(params) : getWarrantyTemplatesApi(params),
    staleTime: 0,
    refetchOnMount: true,
  });

  return {
    ...query,
    warrantyTemplates: query.data?.data?.data || [],
    meta: query.data?.data?.meta,
    isSuccess: query.data?.status === ApiStatus.SUCCESS,
    isError: query.data?.status === ApiStatus.FAIL,
    errorMessage: query.data?.message,
  };
}

// Get warranty template by ID
export function useWarrantyTemplate(id: string, isDemo = false) {
  return useQuery({
    queryKey: warrantyTemplateKeys.detail(id),
    queryFn: (): Promise<ApiResponse<WarrantyTemplateDto | null>> =>
      isDemo ? getWarrantyTemplateDemoApi(id) : getWarrantyTemplateApi(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get warranty templates slim (for dropdowns/autocomplete)
export function useWarrantyTemplatesSlim(isDemo = false) {
  return useQuery({
    queryKey: warrantyTemplateKeys.slim(),
    queryFn: (): Promise<ApiResponse<WarrantyTemplateSlimDto[]>> =>
      isDemo ? getWarrantyTemplatesSlimDemoApi() : getWarrantyTemplatesSlimApi(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get warranty templates for autocomplete
export function useWarrantyTemplatesAutocomplete(query?: string, isDemo = false) {
  return useQuery({
    queryKey: warrantyTemplateKeys.autocomplete(query),
    queryFn: (): Promise<ApiResponse<WarrantyTemplateAutocompleteDto[]>> =>
      isDemo 
        ? getWarrantyTemplatesAutocompleteDemoApi(query) 
        : getWarrantyTemplatesAutocompleteApi(query),
    enabled: query !== undefined,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Check warranty template name availability
export function useWarrantyTemplateNameAvailability(
  name: string,
  excludeId?: string,
  isDemo = false
) {
  return useQuery({
    queryKey: warrantyTemplateKeys.nameAvailability(name, excludeId),
    queryFn: (): Promise<ApiResponse<WarrantyTemplateNameAvailabilityResponseDto | null>> =>
      isDemo 
        ? checkWarrantyTemplateNameAvailabilityDemoApi(name, excludeId)
        : checkWarrantyTemplateNameAvailabilityApi(name, excludeId),
    enabled: !!name && name.length > 0,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Create warranty template mutation
export function useCreateWarrantyTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateWarrantyTemplateDto): Promise<ApiResponse<WarrantyTemplateIdResponseDto | null>> =>
      isDemo ? createWarrantyTemplateDemoApi(data) : createWarrantyTemplateApi(data),
    onSuccess: (data) => {
      if (data.status === "success") {
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
        toast.success("Warranty template created successfully");
      } else {
        toast.error(data.message || "Failed to create warranty template");
      }
    },
    onError: (error) => {
      toast.error(error instanceof Error ? error.message : "Failed to create warranty template");
    },
  });
}

// Update warranty template mutation
export function useUpdateWarrantyTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      id, 
      data 
    }: { 
      id: string; 
      data: UpdateWarrantyTemplateDto 
    }): Promise<ApiResponse<WarrantyTemplateIdResponseDto | null>> =>
      isDemo ? updateWarrantyTemplateDemoApi(id, data) : updateWarrantyTemplateApi(id, data),
    onSuccess: (data, variables) => {
      if (data.status === "success") {
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.detail(variables.id) });
        toast.success("Warranty template updated successfully");
      } else {
        toast.error(data.message || "Failed to update warranty template");
      }
    },
    onError: (error) => {
      toast.error(error instanceof Error ? error.message : "Failed to update warranty template");
    },
  });
}

// Delete warranty template mutation
export function useDeleteWarrantyTemplate(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string): Promise<ApiResponse<DeleteWarrantyTemplateResponseDto | null>> =>
      isDemo ? deleteWarrantyTemplateDemoApi(id) : deleteWarrantyTemplateApi(id),
    onSuccess: (data, id) => {
      if (data.status === "success") {
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
        queryClient.removeQueries({ queryKey: warrantyTemplateKeys.detail(id) });
        toast.success("Warranty template deleted successfully");
      } else {
        toast.error(data.message || "Failed to delete warranty template");
      }
    },
    onError: (error) => {
      toast.error(error instanceof Error ? error.message : "Failed to delete warranty template");
    },
  });
}

// Bulk create warranty templates mutation
export function useBulkCreateWarrantyTemplates(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkCreateWarrantyTemplateDto): Promise<ApiResponse<BulkWarrantyTemplateIdsResponseDto | null>> =>
      isDemo ? bulkCreateWarrantyTemplatesDemoApi(data) : bulkCreateWarrantyTemplatesApi(data),
    onSuccess: (data) => {
      if (data.status === "success") {
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
        toast.success(data.message || "Warranty templates created successfully");
      } else {
        toast.error(data.message || "Failed to create warranty templates");
      }
    },
    onError: (error) => {
      toast.error(error instanceof Error ? error.message : "Failed to create warranty templates");
    },
  });
}

// Bulk update warranty template status mutation
export function useBulkUpdateWarrantyTemplateStatus(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkUpdateWarrantyTemplateStatusDto): Promise<ApiResponse<BulkUpdateWarrantyTemplateStatusResponseDto | null>> =>
      isDemo ? bulkUpdateWarrantyTemplateStatusDemoApi(data) : bulkUpdateWarrantyTemplateStatusApi(data),
    onSuccess: (data) => {
      if (data.status === "success") {
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
        toast.success(data.message || "Warranty template status updated successfully");
      } else {
        toast.error(data.message || "Failed to update warranty template status");
      }
    },
    onError: (error) => {
      toast.error(error instanceof Error ? error.message : "Failed to update warranty template status");
    },
  });
}

// Bulk delete warranty templates mutation
export function useBulkDeleteWarrantyTemplates(isDemo = false) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkDeleteWarrantyTemplateDto): Promise<ApiResponse<BulkDeleteWarrantyTemplateResponseDto | null>> =>
      isDemo ? bulkDeleteWarrantyTemplatesDemoApi(data) : bulkDeleteWarrantyTemplatesApi(data),
    onSuccess: (data) => {
      if (data.status === "success") {
        queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
        toast.success(data.message || "Warranty templates deleted successfully");
      } else {
        toast.error(data.message || "Failed to delete warranty templates");
      }
    },
    onError: (error) => {
      toast.error(error instanceof Error ? error.message : "Failed to delete warranty templates");
    },
  });
}