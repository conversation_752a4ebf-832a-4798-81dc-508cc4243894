import {
  ServiceResponse,
  ServicePaginatedResponse,
  ServiceIdResponse,
  BulkServiceIdsResponse,
  BulkDeleteServiceDto,
  BulkDeleteServiceResponse,
  ServiceNameAvailabilityResponse,
  ServiceSkuAvailabilityResponse,
  SimpleServiceResponse,
  CreateServiceDto,
  UpdateServiceDto,
  BulkCreateServiceDto,
  ServiceStatus,
} from "@/types/service";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetServicesSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new service
export async function createServiceApi(
  data: CreateServiceDto,
  images?: File[],
  ogImage?: File
): Promise<ServiceIdResponse> {
  try {
    const formData = new FormData();
    formData.append("name", data.name);
    if (data.sku) formData.append("sku", data.sku);
    formData.append("serviceCategoryId", data.serviceCategoryId);
    if (data.serviceSubCategoryId) formData.append("serviceSubCategoryId", data.serviceSubCategoryId);
    if (data.description) formData.append("description", data.description);
    formData.append("priceRate", data.priceRate);
    formData.append("incomeAccountId", data.incomeAccountId);
    formData.append("salesTaxId", data.salesTaxId);
    if (data.sellToCustomers !== undefined)
      formData.append("sellToCustomers", String(data.sellToCustomers));
    if (data.purchaseFromSupplier !== undefined)
      formData.append(
        "purchaseFromSupplier",
        String(data.purchaseFromSupplier)
      );
    if (data.purchaseDescription)
      formData.append("purchaseDescription", data.purchaseDescription);
    if (data.purchaseCost) formData.append("purchaseCost", data.purchaseCost);
    if (data.expenseAccountId)
      formData.append("expenseAccountId", data.expenseAccountId);
    if (data.preferredSupplierId)
      formData.append("preferredSupplierId", data.preferredSupplierId);
    if (data.availableOnline !== undefined)
      formData.append("availableOnline", String(data.availableOnline));

    // Location allocation fields
    if (data.isAllocatedToAllLocations !== undefined)
      formData.append(
        "isAllocatedToAllLocations",
        String(data.isAllocatedToAllLocations)
      );
    // locationIds must be an array of strings - send as JSON string for FormData
    if (data.locationIds && data.locationIds.length > 0) {
      formData.append("locationIds", JSON.stringify(data.locationIds));
    }

    // Staff assignment
    if (data.staffMemberIds && data.staffMemberIds.length > 0) {
      formData.append("staffMemberIds", JSON.stringify(data.staffMemberIds));
    }

    // SEO fields
    if (data.seoTitle) formData.append("seoTitle", data.seoTitle);
    if (data.seoDescription)
      formData.append("seoDescription", data.seoDescription);
    if (data.seoKeywords && data.seoKeywords.length > 0) {
      formData.append("seoKeywords", JSON.stringify(data.seoKeywords));
    }

    if (images) {
      images.forEach((image, index) => {
        formData.append("images", image);
      });
    }
    if (ogImage) {
      formData.append("ogImage", ogImage);
    }

    const res = await axios.post("/services", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create services
export async function bulkCreateServicesApi(
  services: BulkCreateServiceDto[],
  images?: File[]
): Promise<BulkServiceIdsResponse> {
  try {
    const formData = new FormData();
    formData.append("services", JSON.stringify(services));

    if (images) {
      images.forEach((image, index) => {
        formData.append("images", image);
      });
    }

    const res = await axios.post("/services/bulk", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get services with pagination and filters
export async function getServicesApi(
  params: GetServicesSchema
): Promise<ServicePaginatedResponse> {
  try {
    // Convert the sort and filters to query params
    const queryParams = new URLSearchParams({
      page: params.page.toString(),
      limit: params.perPage.toString(), // Backend uses 'limit' instead of 'perPage'
      ...(params.name ? { name: params.name } : {}),
      ...(params.sku ? { sku: params.sku } : {}),
      ...(params.serviceCategoryId ? { serviceCategoryId: params.serviceCategoryId } : {}),
      ...(params.status ? { status: params.status } : {}),
      ...(params.availableOnline
        ? { availableOnline: params.availableOnline }
        : {}),
      ...(params.sellToCustomers
        ? { sellToCustomers: params.sellToCustomers }
        : {}),
      ...(params.purchaseFromSupplier
        ? { purchaseFromSupplier: params.purchaseFromSupplier }
        : {}),
      ...(params.from ? { from: params.from } : {}),
      ...(params.to ? { to: params.to } : {}),
      ...(params.filters.length > 0
        ? { filters: JSON.stringify(params.filters) }
        : {}),
      joinOperator: params.joinOperator,
      ...(params.sort ? { sort: JSON.stringify(params.sort) } : {}),
    });

    const response = await axios.get(`/services?${queryParams.toString()}`);

    // Backend returns PaginatedServicesResponseDto directly
    return {
      status: ApiStatus.SUCCESS,
      message: response.data.message,
      data: response.data.data, // This should match PaginatedServicesResponseDto structure
    };
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.message || "Failed to fetch services",
      data: null,
    };
  }
}

// Check service name availability
export async function checkServiceNameAvailabilityApi(
  name: string,
  excludeId?: string
): Promise<ServiceNameAvailabilityResponse> {
  try {
    const queryParams = new URLSearchParams();
    if (excludeId) {
      queryParams.append("excludeId", excludeId);
    }

    const res = await axios.get(
      `/services/check-name/${encodeURIComponent(
        name
      )}?${queryParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check service SKU availability
export async function checkServiceSkuAvailabilityApi(
  sku: string,
  excludeId?: string
): Promise<ServiceSkuAvailabilityResponse> {
  try {
    const queryParams = new URLSearchParams();
    if (excludeId) {
      queryParams.append("excludeId", excludeId);
    }

    const res = await axios.get(
      `/services/check-sku/${encodeURIComponent(sku)}?${queryParams.toString()}`
    );
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get services in slim format (for dropdowns/selects)
export async function getServicesSlimApi(): Promise<SimpleServiceResponse> {
  try {
    const res = await axios.get("/services/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single service by ID
export async function getServiceApi(id: string): Promise<ServiceResponse> {
  try {
    const res = await axios.get(`/services/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update a service
export async function updateServiceApi(
  id: string,
  data: UpdateServiceDto,
  images?: File[],
  ogImage?: File
): Promise<ServiceResponse> {
  try {
    const formData = new FormData();
    if (data.name) formData.append("name", data.name);
    if (data.sku !== undefined) formData.append("sku", data.sku);
    if (data.serviceCategoryId)
      formData.append("serviceCategoryId", data.serviceCategoryId);
    if (data.serviceSubCategoryId)
      formData.append("serviceSubCategoryId", data.serviceSubCategoryId);
    if (data.description !== undefined)
      formData.append("description", data.description);
    if (data.priceRate) formData.append("priceRate", data.priceRate);
    if (data.incomeAccountId)
      formData.append("incomeAccountId", data.incomeAccountId);
    if (data.salesTaxId) formData.append("salesTaxId", data.salesTaxId);
    if (data.sellToCustomers !== undefined)
      formData.append("sellToCustomers", String(data.sellToCustomers));
    if (data.purchaseFromSupplier !== undefined)
      formData.append(
        "purchaseFromSupplier",
        String(data.purchaseFromSupplier)
      );
    if (data.purchaseDescription !== undefined)
      formData.append("purchaseDescription", data.purchaseDescription);
    if (data.purchaseCost !== undefined)
      formData.append("purchaseCost", data.purchaseCost);
    if (data.expenseAccountId !== undefined)
      formData.append("expenseAccountId", data.expenseAccountId);
    if (data.preferredSupplierId !== undefined)
      formData.append("preferredSupplierId", data.preferredSupplierId);
    if (data.availableOnline !== undefined)
      formData.append("availableOnline", String(data.availableOnline));

    // Location allocation fields
    if (data.isAllocatedToAllLocations !== undefined)
      formData.append(
        "isAllocatedToAllLocations",
        String(data.isAllocatedToAllLocations)
      );
    // locationIds must be an array of strings - send as JSON string for FormData
    if (data.locationIds !== undefined) {
      formData.append("locationIds", JSON.stringify(data.locationIds));
    }

    // Staff assignment
    if (data.staffMemberIds !== undefined) {
      formData.append("staffMemberIds", JSON.stringify(data.staffMemberIds));
    }

    // SEO fields
    if (data.seoTitle !== undefined) formData.append("seoTitle", data.seoTitle);
    if (data.seoDescription !== undefined)
      formData.append("seoDescription", data.seoDescription);
    if (data.seoKeywords !== undefined) {
      formData.append("seoKeywords", JSON.stringify(data.seoKeywords));
    }

    if (images) {
      images.forEach((image, index) => {
        formData.append("images", image);
      });
    }
    if (ogImage) {
      formData.append("ogImage", ogImage);
    }

    const res = await axios.patch(`/services/${id}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete a service
export async function deleteServiceApi(
  id: string
): Promise<BulkDeleteServiceResponse> {
  try {
    const res = await axios.delete(`/services/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete services
export async function bulkDeleteServicesApi(
  data: BulkDeleteServiceDto
): Promise<BulkDeleteServiceResponse> {
  try {
    const res = await axios.delete("/services/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update service positions
export async function updateServicePositionsApi(
  updates: { id: string; position: number }[]
): Promise<ApiResponse<{ message: string }>> {
  try {
    const res = await axios.patch("/services/positions", { updates });
    return {
      status: ApiStatus.SUCCESS,
      message: res.data.message,
      data: res.data,
    };
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}
