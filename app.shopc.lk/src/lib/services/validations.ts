import { z } from "zod";
import { ServiceStatus } from "@/types/service";

// Backend DTO: CreateServiceDto
export const createServiceSchema = z.object({
  name: z.string().min(1, "Name is required"),
  sku: z.string().optional(),
  serviceCategoryId: z.string().uuid("Invalid service category ID"),
  serviceSubCategoryId: z.string().uuid("Invalid service sub category ID").optional(),
  description: z.string().optional(),
  priceRate: z.string().min(1, "Price rate is required"),
  incomeAccountId: z.string().uuid("Invalid income account ID"),
  salesTaxId: z.string().uuid("Invalid sales tax ID"),
  sellToCustomers: z.boolean().optional().default(true),
  purchaseFromSupplier: z.boolean().optional().default(false),
  purchaseDescription: z.string().optional(),
  purchaseCost: z.string().optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),
  preferredSupplierId: z.string().uuid("Invalid supplier ID").optional(),
  availableOnline: z.boolean().optional().default(true),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional().default(false),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // Staff assignment
  staffMemberIds: z.array(z.string().uuid("Invalid staff member ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
});

// Backend DTO: UpdateServiceDto
export const updateServiceSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  sku: z.string().optional(),
  serviceCategoryId: z.string().uuid("Invalid service category ID").optional(),
  serviceSubCategoryId: z.string().uuid("Invalid service sub category ID").optional(),
  description: z.string().optional(),
  priceRate: z.string().min(1, "Price rate is required").optional(),
  incomeAccountId: z.string().uuid("Invalid income account ID").optional(),
  salesTaxId: z.string().uuid("Invalid sales tax ID").optional(),
  sellToCustomers: z.boolean().optional(),
  purchaseFromSupplier: z.boolean().optional(),
  purchaseDescription: z.string().optional(),
  purchaseCost: z.string().optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),
  preferredSupplierId: z.string().uuid("Invalid supplier ID").optional(),
  availableOnline: z.boolean().optional(),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional(),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // Staff assignment
  staffMemberIds: z.array(z.string().uuid("Invalid staff member ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
});

// Schema for getting services with pagination and filters
export const getServicesSchema = z.object({
  page: z.number().min(1, "Page must be at least 1").default(1),
  perPage: z.number().min(1, "Per page must be at least 1").max(100, "Per page cannot exceed 100").default(10),
  name: z.string().optional(),
  sku: z.string().optional(),
  serviceCategoryId: z.string().uuid("Invalid service category ID").optional(),
  status: z.enum([ServiceStatus.ACTIVE, ServiceStatus.INACTIVE]).optional(),
  availableOnline: z.string().optional(), // Backend expects string for boolean filters
  sellToCustomers: z.string().optional(), // Backend expects string for boolean filters
  purchaseFromSupplier: z.string().optional(), // Backend expects string for boolean filters
  from: z.string().optional(), // ISO date string
  to: z.string().optional(), // ISO date string
  filters: z.array(z.object({
    field: z.string(),
    operator: z.enum(["eq", "ne", "gt", "gte", "lt", "lte", "like", "in", "nin"]),
    value: z.union([z.string(), z.number(), z.boolean(), z.array(z.union([z.string(), z.number()]))]),
  })).default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z.object({
    field: z.string(),
    direction: z.enum(["asc", "desc"]),
  }).optional(),
});

// Schema for bulk creating services
export const bulkCreateServicesSchema = z.object({
  services: z.array(createServiceSchema.extend({
    imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
  })),
});

// Schema for bulk importing services with images
export const bulkImportServicesWithImagesSchema = z.object({
  services: z.array(createServiceSchema.extend({
    imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
  })),
  images: z.array(z.instanceof(File)).optional(),
});

// Schema for bulk deleting services
export const bulkDeleteServicesSchema = z.object({
  ids: z.array(z.string().uuid("Invalid service ID")),
});

// Schema for updating service status
export const updateServiceStatusSchema = z.object({
  status: z.enum([ServiceStatus.ACTIVE, ServiceStatus.INACTIVE]),
});

// Form schema for UI components (includes additional fields for form handling)
export const serviceFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  sku: z.string().optional(),
  serviceCategoryId: z.string().uuid("Invalid service category ID"),
  serviceSubCategoryId: z.string().uuid("Invalid service sub category ID").optional(),
  description: z.string().optional(),
  priceRate: z.string().min(1, "Price rate is required"),
  incomeAccountId: z.string().uuid("Invalid income account ID"),
  salesTaxId: z.string().uuid("Invalid sales tax ID"),
  sellToCustomers: z.boolean().optional().default(true),
  purchaseFromSupplier: z.boolean().optional().default(false),
  purchaseDescription: z.string().optional(),
  purchaseCost: z.string().optional(),
  expenseAccountId: z.string().uuid("Invalid expense account ID").optional(),
  preferredSupplierId: z.string().uuid("Invalid supplier ID").optional(),
  availableOnline: z.boolean().optional().default(true),
  // Location fields
  isAllocatedToAllLocations: z.boolean().optional().default(false),
  locationIds: z.array(z.string().uuid("Invalid location ID")).optional(),
  // Staff assignment
  staffMemberIds: z.array(z.string().uuid("Invalid staff member ID")).optional(),
  // SEO fields
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  seoKeywords: z.array(z.string()).optional(),
  imageIndex: z.number().min(0, "Image index must be 0 or greater").optional(),
});

// Schema for service name availability check
export const checkServiceNameSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

// Schema for service SKU availability check
export const checkServiceSkuSchema = z.object({
  sku: z.string().min(1, "SKU is required"),
});

// Schema for updating service positions
export const updateServicePositionsSchema = z.object({
  updates: z.array(z.object({
    id: z.string().uuid("Invalid service ID"),
    position: z.number().min(0, "Position must be 0 or greater"),
  })),
});

// Type exports for use in components
export type CreateServiceSchema = z.infer<typeof createServiceSchema>;
export type UpdateServiceSchema = z.infer<typeof updateServiceSchema>;
export type GetServicesSchema = z.infer<typeof getServicesSchema>;
export type BulkCreateServicesSchema = z.infer<typeof bulkCreateServicesSchema>;
export type BulkImportServicesWithImagesSchema = z.infer<typeof bulkImportServicesWithImagesSchema>;
export type BulkDeleteServicesSchema = z.infer<typeof bulkDeleteServicesSchema>;
export type UpdateServiceStatusSchema = z.infer<typeof updateServiceStatusSchema>;
export type ServiceFormSchema = z.infer<typeof serviceFormSchema>;
export type CheckServiceNameSchema = z.infer<typeof checkServiceNameSchema>;
export type CheckServiceSkuSchema = z.infer<typeof checkServiceSkuSchema>;
export type UpdateServicePositionsSchema = z.infer<typeof updateServicePositionsSchema>;
