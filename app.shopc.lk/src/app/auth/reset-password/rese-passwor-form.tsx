"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { resetPassword } from "@/lib/authentication/api";
import { ApiStatus } from "@/types/common";
import { toast } from "sonner";
import { Logo } from "@/components/ui/logo";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { PasswordInput } from "@/components/ui/password-input";

const resetPasswordSchema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export default function ResetPasswordForm({ token }: { token?: string }) {
  const router = useRouter();

  const form = useForm<z.infer<typeof resetPasswordSchema>>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  if (!token) {
    return (
      <div className="p-6 md:p-8 text-center">
        <h2 className="text-lg font-semibold mb-4">Invalid Reset Link</h2>
        <p className="text-muted-foreground mb-4">
          This password reset link is invalid or has expired.
        </p>
        <Link
          href="/forgot-password"
          className="text-primary underline-offset-4 hover:underline"
        >
          Request a new reset link
        </Link>
      </div>
    );
  }

  const onSubmit = async (values: z.infer<typeof resetPasswordSchema>) => {
    try {
      const res = await resetPassword(token, values.password);

      if (res.status === ApiStatus.SUCCESS) {
        toast.success("Your password has been successfully reset.");
        router.push("/login");
      } else {
        toast.error(res.message || "An error occurred.");
      }
    } catch (error) {
      console.error("Reset password error:", error);
      toast.error("Failed to reset password. Please try again later.");
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="p-6 md:p-8">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col items-center text-center">
          <Logo />
          <p className="text-balance text-muted-foreground">
            Enter your new password
          </p>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="password">New Password</Label>
          <PasswordInput id="password" {...form.register("password")} />
          {form.formState.errors.password && (
            <p className="text-sm text-red-500">
              {form.formState.errors.password.message}
            </p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="confirmPassword">Confirm Password</Label>
          <PasswordInput
            id="confirmPassword"
            {...form.register("confirmPassword")}
            disabled={form.formState.isSubmitting}
          />
          {form.formState.errors.confirmPassword && (
            <p className="text-sm text-red-500">
              {form.formState.errors.confirmPassword.message}
            </p>
          )}
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={form.formState.isSubmitting}
        >
          {form.formState.isSubmitting ? (
            <Loader2 className="size-6 animate-spin" />
          ) : (
            "Reset Password"
          )}
        </Button>

        <div className="text-center text-sm">
          <Link
            href="/login"
            className="text-primary underline-offset-4 hover:underline"
          >
            Back to login
          </Link>
        </div>
      </div>
    </form>
  );
}
