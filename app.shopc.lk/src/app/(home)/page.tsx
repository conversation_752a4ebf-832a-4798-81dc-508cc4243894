"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Check, Globe, Users, CreditCard, Shield, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

export default function HomePage() {
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  
  const heroSlides = [
    {
      type: "image",
      src: "https://images.unsplash.com/photo-1497215728101-856f4ea42174?w=1600&h=600&fit=crop",
      alt: "Modern office workspace",
      content: {
        title: "Business Professional",
        description: "Transform your workspace"
      }
    },
    {
      type: "image",
      src: "https://images.unsplash.com/photo-1521737852567-6949f3f9f2b5?w=1600&h=600&fit=crop",
      alt: "Team collaboration",
      content: {
        title: "Collaborative Success",
        description: "Work together seamlessly"
      }
    },
    {
      type: "image",
      src: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=1600&h=600&fit=crop",
      alt: "Professional woman",
      content: {
        title: "Empower Your Team",
        description: "Build stronger connections"
      }
    },
    {
      type: "card",
      bgColor: "bg-yellow-400",
      content: {
        title: "75+",
        subtitle: "Currency Conversion",
        icon: "currency"
      }
    },
    {
      type: "image",
      src: "https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=1600&h=600&fit=crop",
      alt: "Woman with phone",
      content: {
        title: "Mobile First",
        description: "Banking on the go"
      }
    },
    {
      type: "card",
      bgColor: "bg-teal-500",
      content: {
        title: "Business",
        subtitle: "Debit Card",
        image: "/card-mockup.png"
      }
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlideIndex((prev) => (prev + 1) % heroSlides.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  const nextSlide = () => {
    setCurrentSlideIndex((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlideIndex((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-8">
              <Link href="/" className="flex items-center space-x-2">
                <span className="font-bold text-2xl">Wittix</span>
              </Link>
              <div className="hidden md:flex items-center space-x-6">
                <Link href="/company" className="text-sm font-medium hover:text-primary">
                  Company Information
                </Link>
                <Link href="/virtual" className="text-sm font-medium hover:text-primary">
                  Virtual Wallet
                </Link>
                <Link href="/cards" className="text-sm font-medium hover:text-primary">
                  Cards
                </Link>
                <Link href="/pricing" className="text-sm font-medium hover:text-primary">
                  Pricing
                </Link>
                <Link href="/contact" className="text-sm font-medium hover:text-primary">
                  Contact
                </Link>
                <Link href="/help" className="text-sm font-medium hover:text-primary">
                  Help
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                Log In
              </Button>
              <Button size="sm">
                Sign Up
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative bg-black text-white py-20">
        <div className="container mx-auto px-4">
          {/* Hero Content */}
          <div className="text-center max-w-6xl mx-auto mb-16">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 tracking-tight">
              TAKE YOUR BUSINESS TO <span className="text-blue-500">NEW HEIGHTS</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-gray-300 max-w-4xl mx-auto">
              Sign up for your fast, easy international business account today and unlock a world of opportunities.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-6 text-lg group">
                Open Account
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Button>
              <Button size="lg" variant="outline" className="border-blue-600 text-blue-500 hover:bg-blue-600 hover:text-white px-8 py-6 text-lg">
                Contact Sales
              </Button>
            </div>
          </div>

          {/* Full Width Carousel Grid */}
          <div className="relative">
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4 h-[300px]">
              {/* Dynamic Carousel Item */}
              <div className="relative rounded-2xl overflow-hidden group cursor-pointer">
                {heroSlides[currentSlideIndex].type === "image" ? (
                  <>
                    <Image
                      src={heroSlides[currentSlideIndex].src!}
                      alt={heroSlides[currentSlideIndex].alt!}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
                  </>
                ) : (
                  <div className={`w-full h-full ${heroSlides[currentSlideIndex].bgColor} rounded-2xl flex items-center justify-center`}>
                    <div className="text-center text-black">
                      {heroSlides[currentSlideIndex].content.icon === "currency" && (
                        <div className="mb-2">
                          <div className="flex justify-center gap-1 mb-3">
                            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                              <span className="text-sm">💵</span>
                            </div>
                            <div className="w-10 h-10 bg-white/30 rounded-full flex items-center justify-center">
                              <span className="text-lg">€</span>
                            </div>
                            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                              <span className="text-sm">¥</span>
                            </div>
                          </div>
                        </div>
                      )}
                      <h2 className="text-2xl md:text-3xl font-bold mb-1">{heroSlides[currentSlideIndex].content.title}</h2>
                      <p className="text-sm md:text-base">{heroSlides[currentSlideIndex].content.subtitle}</p>
                    </div>
                  </div>
                )}
                
                {/* Carousel Navigation Overlay */}
                <button
                  onClick={prevSlide}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={nextSlide}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>

              {/* Static Item 2 */}
              <div className="relative rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1521737852567-6949f3f9f2b5?w=800&h=600&fit=crop"
                  alt="Team collaboration"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/20" />
              </div>

              {/* Static Item 3 */}
              <div className="relative rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&h=600&fit=crop"
                  alt="Professional woman"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/20" />
              </div>

              {/* Currency Card */}
              <div className="bg-yellow-400 rounded-2xl flex items-center justify-center p-4">
                <div className="text-center text-black">
                  <div className="flex justify-center gap-1 mb-3">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <span className="text-sm">💵</span>
                    </div>
                    <div className="w-10 h-10 bg-white/30 rounded-full flex items-center justify-center">
                      <span className="text-lg">€</span>
                    </div>
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <span className="text-sm">¥</span>
                    </div>
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold mb-1">75+</h2>
                  <p className="text-sm md:text-base">Currency Conversion</p>
                </div>
              </div>

              {/* Static Item 5 */}
              <div className="relative rounded-2xl overflow-hidden">
                <Image
                  src="https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=800&h=600&fit=crop"
                  alt="Woman with phone"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/20" />
              </div>

              {/* Business Card */}
              <div className="bg-teal-500 rounded-2xl flex items-center justify-center p-4">
                <div className="text-center text-black">
                  <h2 className="text-xl md:text-2xl font-bold mb-1">Business</h2>
                  <p className="text-sm md:text-base mb-3">Debit Card</p>
                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg p-3 shadow-lg">
                    <div className="w-24 h-16 bg-gradient-to-br from-blue-600 to-blue-800 rounded-md flex items-end p-2">
                      <div className="text-white text-xs">
                        <p className="font-bold">wittix</p>
                        <p className="text-[10px]">John Smith</p>
                        <p className="text-[8px] mt-1">VISA</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Carousel Indicators */}
            <div className="absolute -bottom-12 left-1/2 -translate-x-1/2 flex gap-2">
              {heroSlides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlideIndex(index)}
                  className={`transition-all ${
                    index === currentSlideIndex 
                      ? 'w-6 h-2 bg-blue-500' 
                      : 'w-2 h-2 bg-white/50 hover:bg-white/70'
                  } rounded-full`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="py-12 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
            <span className="text-xl font-semibold">STEVE MADDEN</span>
            <span className="text-xl font-semibold">Reebok</span>
            <span className="text-xl font-semibold">sodastream</span>
            <span className="text-xl font-semibold">AP</span>
            <span className="text-xl font-semibold">DECATHLON</span>
          </div>
        </div>
      </section>

      {/* Unlock Full Potential Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <Image
                src="https://images.unsplash.com/photo-1556761175-b413da4baf72?w=600&h=400&fit=crop"
                alt="Team working"
                width={600}
                height={400}
                className="rounded-lg shadow-lg"
              />
            </div>
            <div>
              <h2 className="text-4xl font-bold mb-6">
                IT'S TIME TO UNLOCK THE FULL POTENTIAL OF YOUR BUSINESS
              </h2>
              <p className="text-lg text-muted-foreground mb-6">
                It's time to unlock the full potential of your business. With Wittix's
                payment & wallet solutions you can set your company free and get the
                support you need to take your business to new heights.
              </p>
              <p className="text-lg text-muted-foreground mb-8">
                Embrace the possibilities, boost your team's productivity and
                reach new heights like never before.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <Card className="text-center p-4">
                  <CardContent className="p-0">
                    <div className="text-3xl font-bold text-primary mb-2">140+</div>
                    <p className="text-sm text-muted-foreground">Global Coverage Countries</p>
                  </CardContent>
                </Card>
                <Card className="text-center p-4">
                  <CardContent className="p-0">
                    <div className="text-3xl font-bold text-primary mb-2">35</div>
                    <p className="text-sm text-muted-foreground">Popular Currencies</p>
                  </CardContent>
                </Card>
                <Card className="text-center p-4">
                  <CardContent className="p-0">
                    <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                    <p className="text-sm text-muted-foreground">Available Support</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Currency Conversion Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4">Financial Tools</Badge>
              <h2 className="text-4xl font-bold mb-6">CURRENCY CONVERSION</h2>
              <p className="text-lg text-muted-foreground mb-8">
                With Wittix, you can convert funds between different currencies at
                competitive rates, saving you time and money when conducting
                international transactions.
              </p>
              <Button size="lg" className="group">
                Convert Now
                <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
              </Button>
            </div>
            <div className="bg-background rounded-lg shadow-lg p-6">
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Amount</label>
                      <div className="flex gap-2">
                        <Input type="number" placeholder="1,000" className="flex-1" />
                        <Button variant="outline" className="w-24">
                          EUR
                          <ChevronDown className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-center py-2">
                      <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-primary/10">
                        <ArrowRight className="h-5 w-5 text-primary rotate-90" />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">Converted to</label>
                      <div className="flex gap-2">
                        <Input type="number" placeholder="1,052.82" readOnly className="flex-1" />
                        <Button variant="outline" className="w-24">
                          USD
                          <ChevronDown className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      1 EUR = 1.05 USD • Last update
                    </div>
                    <Button className="w-full" size="lg">
                      Convert Currency
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Seamless International Payments */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge className="mb-4">Powered by business globally</Badge>
            <h2 className="text-4xl font-bold">SEAMLESS INTERNATIONAL PAYMENTS</h2>
            <p className="text-lg text-muted-foreground mt-4 max-w-3xl mx-auto">
              With Wittix, you can enjoy seamless international payments,
              eliminating the need for multiple banking relationships and excessive fees.
            </p>
          </div>
          <div className="relative">
            <Image
              src="https://images.unsplash.com/photo-**********-4b87b5e36e44?w=1200&h=600&fit=crop"
              alt="International team collaboration"
              width={1200}
              height={600}
              className="rounded-lg shadow-lg mx-auto"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="grid grid-cols-3 gap-8">
                <div className="bg-background/90 backdrop-blur rounded-lg p-4 shadow-lg">
                  <CreditCard className="h-8 w-8 text-primary mb-2" />
                  <p className="font-semibold">Credit Line</p>
                </div>
                <div className="bg-background/90 backdrop-blur rounded-lg p-4 shadow-lg">
                  <Globe className="h-8 w-8 text-primary mb-2" />
                  <p className="font-semibold">Global Reach</p>
                </div>
                <div className="bg-background/90 backdrop-blur rounded-lg p-4 shadow-lg">
                  <Shield className="h-8 w-8 text-primary mb-2" />
                  <p className="font-semibold">Secure</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Effortless Mass Payments */}
      <section className="relative min-h-screen md:min-h-0">
        <div className="grid md:grid-cols-2 min-h-[600px]">
          {/* Left Side - Blue Background */}
          <div className="bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 py-20 px-8 md:px-16 flex items-center justify-center">
            <div className="relative max-w-sm mx-auto">
              {/* User Avatars around the phone */}
              <div className="absolute -top-10 left-10 w-14 h-14 rounded-full border-4 border-white overflow-hidden shadow-lg z-10">
                <Image
                  src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop"
                  alt="User 1"
                  width={56}
                  height={56}
                  className="w-full h-full object-cover"
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white flex items-center justify-center">
                  <span className="text-xs">🇺🇸</span>
                </div>
              </div>
              
              <div className="absolute -top-6 right-8 w-14 h-14 rounded-full border-4 border-white overflow-hidden shadow-lg z-10">
                <Image
                  src="https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=100&h=100&fit=crop"
                  alt="User 2"
                  width={56}
                  height={56}
                  className="w-full h-full object-cover"
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white flex items-center justify-center">
                  <span className="text-xs">🇩🇪</span>
                </div>
              </div>

              <div className="absolute top-20 -left-10 w-14 h-14 rounded-full border-4 border-white overflow-hidden shadow-lg z-10">
                <Image
                  src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=100&h=100&fit=crop"
                  alt="User 3"
                  width={56}
                  height={56}
                  className="w-full h-full object-cover"
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white flex items-center justify-center">
                  <span className="text-xs">🇨🇳</span>
                </div>
              </div>

              <div className="absolute top-40 -right-6 w-14 h-14 rounded-full border-4 border-white overflow-hidden shadow-lg z-10">
                <Image
                  src="https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop"
                  alt="User 4"
                  width={56}
                  height={56}
                  className="w-full h-full object-cover"
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white flex items-center justify-center">
                  <span className="text-xs">🇬🇧</span>
                </div>
              </div>

              <div className="absolute bottom-20 -left-8 w-14 h-14 rounded-full border-4 border-white overflow-hidden shadow-lg z-10">
                <Image
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop"
                  alt="User 5"
                  width={56}
                  height={56}
                  className="w-full h-full object-cover"
                />
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full border-2 border-white flex items-center justify-center">
                  <span className="text-xs">🇪🇸</span>
                </div>
              </div>

              {/* Phone Mockup */}
              <div className="bg-gray-900 rounded-[2.5rem] p-2 shadow-2xl mx-auto max-w-[280px]">
                <div className="bg-gradient-to-b from-blue-800 to-blue-900 rounded-[2.2rem] p-5 h-[500px] relative">
                  {/* Status Bar */}
                  <div className="flex justify-between items-center mb-6 text-white text-xs font-medium">
                    <span>9:41</span>
                    <span className="text-sm">Dashboard</span>
                    <div className="flex gap-0.5">
                      <div className="w-4 h-4 bg-white/20 rounded-sm"></div>
                    </div>
                  </div>
                  
                  {/* Main Balance */}
                  <div className="text-center mb-8">
                    <p className="text-blue-200 text-sm mb-2">Balance</p>
                    <div className="text-4xl font-bold text-white mb-3">€5,470.25</div>
                    <p className="text-blue-300 text-xs">Shown in the equivalent EUR amount</p>
                  </div>

                  {/* Progress Section */}
                  <div className="space-y-4 mb-8">
                    <div>
                      <div className="flex justify-between text-xs text-blue-200 mb-2">
                        <span>Pending</span>
                        <span>Booked</span>
                      </div>
                      <div className="flex gap-2 text-xs">
                        <span className="text-white">€10,000.00</span>
                        <span className="text-white">€15,000.00</span>
                      </div>
                      <div className="w-full bg-blue-700/50 rounded-full h-3 mt-2">
                        <div className="bg-gradient-to-r from-red-400 to-orange-400 h-3 rounded-full w-4/5"></div>
                      </div>
                    </div>
                  </div>

                  {/* Bottom Stats */}
                  <div className="absolute bottom-5 left-5 right-5">
                    <div className="bg-blue-800/50 rounded-2xl p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="text-blue-300 text-xs">Total Amount</p>
                          <p className="text-white text-2xl font-bold">€37,600.00</p>
                        </div>
                        <div className="text-right">
                          <p className="text-green-400 text-sm">+12.5%</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Right Side - White Background with Image */}
          <div className="relative bg-gray-50 py-20 px-8 md:px-16 flex items-center">
            {/* Background Image */}
            <div className="absolute inset-0">
              <Image
                src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&h=600&fit=crop"
                alt="Professional woman with phone"
                width={800}
                height={600}
                className="w-full h-full object-cover opacity-10"
              />
            </div>
            
            {/* Content */}
            <div className="relative z-10 max-w-xl">
              <Badge className="mb-4 bg-blue-100 text-blue-700 border-blue-200">
                Customizable Payment Templates
              </Badge>
              <h2 className="text-5xl font-bold mb-6 text-gray-900">
                Effortless Mass Payments
              </h2>
              <p className="text-lg mb-8 text-gray-700 leading-relaxed">
                Wittix takes mass payments to a whole new level of simplicity. Our platform
                allows you to initiate multiple payments simultaneously, even to different
                recipients. Whether you need to pay vendors, suppliers, or employees, Wittix's
                mass payment feature ensures that you can execute these payments
                efficiently and accurately.
              </p>
              <p className="text-sm text-gray-600 mb-8">
                Welcome to Wittix! We're here to assist you with any questions or concerns you
                may have. Feel free to reach out, and we'll do our best to provide you with the
                help you need.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white group">
                  Open Account
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </Button>
                <Button size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
                  Contact Sales
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* International Banking */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4">
              SIMPLIFY YOUR INTERNATIONAL BANKING WITH WITTIX VIRTUAL IBANS
            </h2>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              Enjoy the benefits of having a local bank account in multiple countries,
              making it easy to receive payments and manage funds efficiently, all from a
              single platform.
            </p>
          </div>
          <div className="relative max-w-4xl mx-auto">
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-64 h-64 rounded-full bg-primary/10 flex items-center justify-center">
                  <Globe className="h-32 w-32 text-primary" />
                </div>
                {/* Country flags positioned around the globe */}
                <div className="absolute -top-4 left-1/2 -translate-x-1/2">
                  <div className="w-12 h-12 rounded-full bg-background shadow-lg flex items-center justify-center text-2xl">
                    🇺🇸
                  </div>
                </div>
                <div className="absolute top-1/2 -left-16 -translate-y-1/2">
                  <div className="w-12 h-12 rounded-full bg-background shadow-lg flex items-center justify-center text-2xl">
                    🇬🇧
                  </div>
                </div>
                <div className="absolute top-1/2 -right-16 -translate-y-1/2">
                  <div className="w-12 h-12 rounded-full bg-background shadow-lg flex items-center justify-center text-2xl">
                    🇪🇺
                  </div>
                </div>
                <div className="absolute -bottom-4 left-1/2 -translate-x-1/2">
                  <div className="w-12 h-12 rounded-full bg-background shadow-lg flex items-center justify-center text-2xl">
                    🇯🇵
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customized Access Control */}
      <section className="py-20 bg-slate-900 text-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <Badge className="mb-4 text-blue-400 border-blue-400">Full control</Badge>
              <h2 className="text-4xl font-bold mb-6">CUSTOMIZED ACCESS CONTROL</h2>
              <p className="text-lg text-gray-300 mb-6">
                Wittix Bespoke Access allows you to customize access permissions based on
                your team's roles and responsibilities.
              </p>
              <p className="text-lg text-gray-300 mb-8">
                Whether you have different departments, managers, or team members with
                varying levels of authority, you can grant.
              </p>
            </div>
            
            <div className="relative">
              {/* Desktop UI Mockup */}
              <div className="relative">
                {/* Main Desktop Window */}
                <div className="bg-gray-700 rounded-lg p-4 shadow-xl">
                  <div className="bg-gray-600 rounded-md p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium text-white">Custom Ron</h3>
                      <button className="text-gray-400 hover:text-white">
                        <span className="text-lg">×</span>
                      </button>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-white font-medium mb-3 flex items-center gap-2">
                          Transfers
                          <ArrowRight className="h-4 w-4 text-blue-400" />
                        </h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-300 text-sm">View transfers</span>
                            <div className="w-10 h-5 bg-blue-500 rounded-full relative">
                              <div className="w-4 h-4 bg-white rounded-full absolute top-0.5 right-0.5"></div>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-300 text-sm">Make payments</span>
                            <div className="w-10 h-5 bg-gray-400 rounded-full relative">
                              <div className="w-4 h-4 bg-white rounded-full absolute top-0.5 left-0.5"></div>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-300 text-sm">Add approval rule</span>
                            <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-xs px-3 py-1">
                              + Add
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Profile Cards */}
                <div className="absolute -top-4 -right-8 bg-white rounded-lg p-4 shadow-lg text-black max-w-xs">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">Willie Cameron</p>
                      <p className="text-xs text-gray-500"><EMAIL></p>
                    </div>
                  </div>
                  <Button size="sm" className="w-full bg-blue-500 hover:bg-blue-600 text-xs">
                    + Add
                  </Button>
                </div>

                <div className="absolute -bottom-8 -right-4 bg-white rounded-lg p-4 shadow-lg text-black max-w-xs">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-sm">Robert Lamb</p>
                      <p className="text-xs text-gray-500"><EMAIL></p>
                    </div>
                  </div>
                  <Button size="sm" className="w-full bg-blue-500 hover:bg-blue-600 text-xs">
                    + Add
                  </Button>
                </div>

                {/* Credit Card */}
                <div className="absolute -bottom-4 left-8 bg-gradient-to-r from-gray-800 to-gray-900 rounded-lg p-4 shadow-lg text-white w-48">
                  <div className="flex justify-between items-start mb-8">
                    <div className="text-xs">
                      <div className="flex gap-1 mb-2">
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      </div>
                    </div>
                    <span className="text-xs font-bold">VISA</span>
                  </div>
                  <div className="text-xs">
                    <p className="font-medium">John Smith</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">JOIN WITTIX TODAY</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Experience a new level of simplicity and efficiency in your business payments.
            Let us handle the complexities while you focus on driving your business forward
            towards the power of streamlined payments with Wittix.
          </p>
          <Button size="lg" variant="secondary" className="group">
            Open a Free Account
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-background border-t">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-8">
            <div>
              <h3 className="font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><Link href="/about" className="text-sm text-muted-foreground hover:text-primary">About Us</Link></li>
                <li><Link href="/careers" className="text-sm text-muted-foreground hover:text-primary">Careers</Link></li>
                <li><Link href="/press" className="text-sm text-muted-foreground hover:text-primary">Press</Link></li>
                <li><Link href="/contact" className="text-sm text-muted-foreground hover:text-primary">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Products</h3>
              <ul className="space-y-2">
                <li><Link href="/virtual-wallet" className="text-sm text-muted-foreground hover:text-primary">Virtual Wallet</Link></li>
                <li><Link href="/cards" className="text-sm text-muted-foreground hover:text-primary">Cards</Link></li>
                <li><Link href="/payments" className="text-sm text-muted-foreground hover:text-primary">Payments</Link></li>
                <li><Link href="/currency" className="text-sm text-muted-foreground hover:text-primary">Currency Exchange</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li><Link href="/help" className="text-sm text-muted-foreground hover:text-primary">Help Center</Link></li>
                <li><Link href="/blog" className="text-sm text-muted-foreground hover:text-primary">Blog</Link></li>
                <li><Link href="/developers" className="text-sm text-muted-foreground hover:text-primary">Developers</Link></li>
                <li><Link href="/security" className="text-sm text-muted-foreground hover:text-primary">Security</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link href="/terms" className="text-sm text-muted-foreground hover:text-primary">Terms of Service</Link></li>
                <li><Link href="/privacy" className="text-sm text-muted-foreground hover:text-primary">Privacy Policy</Link></li>
                <li><Link href="/cookies" className="text-sm text-muted-foreground hover:text-primary">Cookie Policy</Link></li>
                <li><Link href="/compliance" className="text-sm text-muted-foreground hover:text-primary">Compliance</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-sm text-muted-foreground">
                © 2024 Wittix. All rights reserved.
              </p>
              <div className="flex items-center gap-4 mt-4 md:mt-0">
                <Link href="#" className="text-muted-foreground hover:text-primary">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                  </svg>
                </Link>
                <Link href="#" className="text-muted-foreground hover:text-primary">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </Link>
                <Link href="#" className="text-muted-foreground hover:text-primary">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}