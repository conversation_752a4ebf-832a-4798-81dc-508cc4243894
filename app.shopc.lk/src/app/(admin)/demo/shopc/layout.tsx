"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DemoContentLayout } from "@/components/demo-panel/demo-content-layout";
import { useRouter, usePathname } from "next/navigation";
import { PreviewContainer } from "@/components/shopc/preview-container";
import { useShopcStore } from "@/lib/stores/storefront-store";
import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { Moon, Sun, Monitor } from "lucide-react";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";

const tabs = [
  {
    label: "Storefront",
    path: "/demo/shopc/storefront",
  },
  {
    label: "Checkout",
    path: "/demo/shopc/checkout",
  },
  {
    label: "Appearance",
    path: "/demo/shopc/appearance",
  },
  {
    label: "Menu",
    path: "/demo/shopc/menu",
  },
  {
    label: "Marketing",
    path: "/demo/shopc/marketing",
  },
];

export default function BizCLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { cards } = useShopcStore();
  const [isHydrated, setIsHydrated] = useState(false);
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    useShopcStore.persist.rehydrate();
    setIsHydrated(true);
    setMounted(true);
  }, []);

  // Theme toggle component
  const ThemeToggle = () => {
    if (!mounted) {
      return (
        <Button variant="ghost" size="icon" className="h-9 w-9">
          <Monitor className="h-4 w-4" />
        </Button>
      );
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-9 w-9">
            {theme === "dark" ? (
              <Moon className="h-4 w-4" />
            ) : theme === "light" ? (
              <Sun className="h-4 w-4" />
            ) : (
              <Monitor className="h-4 w-4" />
            )}
            <span className="sr-only">Toggle theme</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setTheme("light")}>
            <Sun className="mr-2 h-4 w-4" />
            <span>Light</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setTheme("dark")}>
            <Moon className="mr-2 h-4 w-4" />
            <span>Dark</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setTheme("system")}>
            <Monitor className="mr-2 h-4 w-4" />
            <span>System</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <DemoContentLayout title="BizC">
      <div className="bg-background min-h-screen transition-colors">
        <div className="container py-6 space-y-6">
          {/* Header with tabs and theme toggle */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-foreground">Design</h2>
              <div className="flex items-center gap-2">
                <ThemeToggle />
              </div>
            </div>
            
            {/* Tab Navigation */}
            <div className="border-b border-border">
              <div className="flex gap-1">
                {tabs.map((tab) => (
                  <Button
                    key={tab.path}
                    variant="ghost"
                    onClick={() => router.push(tab.path)}
                    className={`
                      relative px-4 py-2 rounded-none border-b-2 transition-all duration-200
                      ${pathname === tab.path 
                        ? 'border-orange-500 bg-orange-500 text-white hover:bg-orange-600 dark:border-orange-400 dark:bg-orange-600 dark:hover:bg-orange-700' 
                        : 'border-transparent hover:bg-muted text-foreground'
                      }
                    `}
                  >
                    {tab.label}
                  </Button>
                ))}
              </div>
            </div>
          </div>
          
          {/* Main Content - clean layout with theme support */}
          <div className="grid grid-cols-2 gap-8 pt-6">
            {/* Left Column - Page Content */}
            <div className="space-y-6">
              <div className="bg-card border border-border rounded-lg shadow-sm transition-colors">
                {children}
              </div>
            </div>

            {/* Right Column - Shared Preview */}
            <div className="relative">
              <div className="bg-card border border-border rounded-lg shadow-sm p-6 transition-colors">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg">
                    <Monitor className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-foreground">Live Preview</h3>
                    <p className="text-sm text-muted-foreground">
                      See your changes in real-time
                    </p>
                  </div>
                </div>
                {isHydrated ? (
                  <PreviewContainer isDemo={true} cards={cards} />
                ) : (
                  <div className="flex items-center justify-center h-64 bg-muted/30 rounded-lg border border-dashed border-border/50">
                    <div className="text-center space-y-2">
                      <div className="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto" />
                      <p className="text-sm text-muted-foreground">Loading preview...</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DemoContentLayout>
  );
}
