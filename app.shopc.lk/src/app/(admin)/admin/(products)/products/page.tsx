import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ProductsTableWrapper from "@/components/products/products-table-wrapper";

export const metadata: Metadata = {
  title: "Items",
  description: "Manage Items in demo mode",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ProductsTableWrapper}
      columnCount={7}
      searchableColumnCount={1}
      filterableColumnCount={3}
      cellWidths={[
        "6rem",
        "15rem",
        "12rem",
        "8rem",
        "8rem",
        "8rem",
        "8rem",
        "8rem",
      ]}
      isDemo={false}
    />
  );
}
