import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ServiceOrdersTableWrapper from "@/components/service-orders/service-orders-table-wrapper";

export const metadata: Metadata = {
  title: "Service Orders",
  description: "Manage service orders",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ServiceOrdersTableWrapper}
      columnCount={13}
      searchableColumnCount={3}
      filterableColumnCount={4}
      cellWidths={[
        "12rem",
        "16rem",
        "12rem",
        "12rem",
        "12rem",
        "12rem",
        "10rem",
        "12rem",
        "14rem",
        "12rem",
        "14rem",
        "12rem",
        "8rem",
      ]}
      isDemo={false}
    />
  );
}