import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ServiceOrderPrioritiesTableWrapper from "@/components/service-order-priorities/service-order-priorities-table-wrapper";

export const metadata: Metadata = {
  title: "Service Order Priorities",
  description: "Manage service order priorities",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ServiceOrderPrioritiesTableWrapper}
      columnCount={9}
      searchableColumnCount={2}
      filterableColumnCount={2}
      cellWidths={["12rem", "18rem", "12rem", "10rem", "10rem", "10rem", "10rem", "10rem", "8rem"]}
      isDemo={false}
    />
  );
}