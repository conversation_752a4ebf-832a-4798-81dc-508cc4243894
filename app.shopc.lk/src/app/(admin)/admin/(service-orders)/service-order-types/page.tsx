import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ServiceOrderTypesTableWrapper from "@/components/service-order-types/service-order-types-table-wrapper";

export const metadata: Metadata = {
  title: "Service Order Types",
  description: "Manage service order types",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ServiceOrderTypesTableWrapper}
      columnCount={9}
      searchableColumnCount={2}
      filterableColumnCount={2}
      cellWidths={["12rem", "18rem", "12rem", "10rem", "10rem", "10rem", "10rem", "10rem", "8rem"]}
      isDemo={false}
    />
  );
}