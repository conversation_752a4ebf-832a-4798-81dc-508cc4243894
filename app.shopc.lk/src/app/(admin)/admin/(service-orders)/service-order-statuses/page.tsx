import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ServiceOrderStatusesTableWrapper from "@/components/service-order-statuses/service-order-statuses-table-wrapper";

export const metadata: Metadata = {
  title: "Service Order Statuses",
  description: "Manage service order statuses",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ServiceOrderStatusesTableWrapper}
      columnCount={9}
      searchableColumnCount={2}
      filterableColumnCount={2}
      cellWidths={["12rem", "18rem", "12rem", "10rem", "10rem", "10rem", "10rem", "10rem", "8rem"]}
      isDemo={false}
    />
  );
}