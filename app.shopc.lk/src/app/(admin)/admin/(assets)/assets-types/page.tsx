import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import AssetTypesTableWrapper from "@/components/asset-types/asset-types-table-wrapper";

export const metadata: Metadata = {
  title: "Asset Types",
  description: "Manage asset types in your business",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={AssetTypesTableWrapper}
      columnCount={5}
      searchableColumnCount={1}
      filterableColumnCount={1}
      cellWidths={["15rem", "20rem", "12rem", "12rem", "8rem"]}
      isDemo={false}
    />
  );
}
