import { type Metadata } from "next";
import { DataPage } from "@/components/shared/data-page";
import ServicesTableWrapper from "@/components/services/services-table-wrapper";
import ActivityLogsTableWrapper from "@/components/activity-log/activity-logs-table-wrapper";

export const metadata: Metadata = {
  title: "Services",
  description: "Manage services in demo mode",
};

export default async function IndexPage() {
  return (
    <DataPage
      TableComponent={ActivityLogsTableWrapper}
      columnCount={5}
      searchableColumnCount={1}
      filterableColumnCount={1}
      cellWidths={["15rem", "20rem", "12rem", "12rem", "8rem"]}
      isDemo={false}
    />
  );
}
