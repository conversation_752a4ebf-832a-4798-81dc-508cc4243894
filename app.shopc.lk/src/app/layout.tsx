import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { getTranslations, setRequestLocale, getLocale } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { CustomThemeProvider } from "@/contexts/theme-context";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as SonnerToaster } from "@/components/ui/sonner";
import { siteConfig } from "@/config/site";
import NextTopLoader from "nextjs-toploader";
import { defaultLocale } from "@/config/i18n";
import { AuthProvider } from "@/contexts/auth-contexts";
import { BusinessLocationProvider } from "@/contexts/business-location-context";
// import { ThemeInitScript } from "@/components/theme";
import { QueryProvider } from "@/providers/query-provider";
// Initialize the Inter font
const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  alternates: {
    canonical: "/",
  },
  openGraph: {
    url: "/",
    title: {
      default: siteConfig.name,
      template: `%s | ${siteConfig.name}`,
    },
    description: siteConfig.description,
    type: "website",
  },
};

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: {
    locale: string;
  };
}>) {
  // Set the locale for server components
  setRequestLocale((await params).locale || defaultLocale);

  // Get the locale
  const locale = await getLocale();
  // Use the valid locale or fallback to default

  const t = await getTranslations();

  return (
    <html lang={locale} suppressHydrationWarning>
      <body className={inter.className}>
        <NextTopLoader
          color="hsl(var(--primary))"
          initialPosition={0.08}
          crawlSpeed={200}
          height={3}
          crawl={true}
          showSpinner={true}
          easing="ease"
          speed={200}
          shadow="0 0 10px hsl(var(--primary)),0 0 5px hsl(var(--primary))"
          template='<div class="bar" role="bar"><div class="peg"></div></div> 
  <div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'
          zIndex={1600}
          showAtBottom={false}
        />
        <QueryProvider>
          <AuthProvider>
            {/* <AuthGuard> */}
            <BusinessLocationProvider>
              {/* <ThemeInitScript /> */}
              <ThemeProvider
                attribute="class"
                defaultTheme="system"
                enableSystem
              >
                <CustomThemeProvider>
                  <NextIntlClientProvider locale={locale}>
                    <ThemeProvider
                      attribute="class"
                      defaultTheme="system"
                      enableSystem
                    >
                      {/* We now wrap non-demo pages only, as demo pages have their own providers */}
                      {children}
                    </ThemeProvider>
                    <Toaster />
                    <SonnerToaster />
                  </NextIntlClientProvider>
                </CustomThemeProvider>
              </ThemeProvider>
            </BusinessLocationProvider>
            {/* </AuthGuard> */}
          </AuthProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
