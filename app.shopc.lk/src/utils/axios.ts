import axios, { AxiosRequestConfig, AxiosError } from "axios";

import { type ApiResponse } from "@/types/common";
import { getSession, clearSession } from "@/lib/authentication/api";
import { getSessionId, clearSessionId, detectSessionSource } from "./session";

const axiosService = () => {
  const defaultOptions = {
    baseURL: process.env.NEXT_PUBLIC_API_URL + "/api/v1",
  };

  const instance = axios.create(defaultOptions);

  // Flag to track if we're currently refreshing to prevent multiple refresh attempts
  let isRefreshing = false;
  let failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (error?: any) => void;
  }> = [];

  const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    failedQueue = [];
  };

  // Helper function to check if we should redirect to login
  const shouldRedirectToLogin = () => {
    if (typeof window === "undefined") return false;

    const currentPath = window.location.pathname;
    const authPaths = [
      "/auth/login",
      "/auth/register",
      "/auth/forgot-password",
      "/auth/reset-password",
    ];

    // Don't redirect if we're already on an auth page
    return !authPaths.some((path) => currentPath.startsWith(path));
  };

  instance.interceptors.request.use(async (request) => {
    try {
      // Add session ID to all requests
      const sessionId = getSessionId();
      request.headers["X-Session-Id"] = sessionId;
      
      // Add client source header
      const source = detectSessionSource();
      request.headers["X-Client-Source"] = source;
      
      // Add authorization token if available
      const session = await getSession();
      if (session) {
        request.headers.Authorization = `Bearer ${session.user.token}`;
      }
    } catch (error) {
      console.error("Error in request interceptor:", error);
    }
    return request;
  });

  instance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as AxiosRequestConfig & {
        _retry?: boolean;
      };

      // Check if error is 401 and we haven't already tried to refresh
      if (error.response?.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          // If we're already refreshing, queue this request
          try {
            await new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject });
            });
            // Retry the original request with new token
            return instance(originalRequest);
          } catch (err) {
            return Promise.reject(err);
          }
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          // Attempt to get a fresh session (which will trigger refresh if needed)
          const session = await getSession();

          if (session && session.user.token) {
            // Update the authorization header with the new token
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers.Authorization = `Bearer ${session.user.token}`;

            processQueue(null, session.user.token);

            // Retry the original request with the new token
            return instance(originalRequest);
          } else {
            // Unable to refresh, clear session
            await clearSession();
            processQueue(new Error("Unable to refresh token"), null);

            // Only redirect if we should (not on auth pages)
            if (shouldRedirectToLogin()) {
              console.log("Redirecting to login due to authentication failure");
              clearSessionId(); // Clear session ID on auth failure
              window.location.href = "/auth/login";
            }

            return Promise.reject(error);
          }
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);
          await clearSession();
          clearSessionId(); // Clear session ID on refresh failure
          processQueue(refreshError, null);

          // Only redirect if we should (not on auth pages)
          if (shouldRedirectToLogin()) {
            console.log("Redirecting to login due to refresh failure");
            window.location.href = "/auth/login";
          }

          return Promise.reject(error);
        } finally {
          isRefreshing = false;
        }
      }

      return Promise.reject(error.response?.data as ApiResponse<unknown>);
    }
  );

  return instance;
};

export default axiosService();
