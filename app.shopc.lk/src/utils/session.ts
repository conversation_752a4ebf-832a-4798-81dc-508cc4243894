import Cookies from "js-cookie";

export enum SessionSource {
  WEB = "WEB",
  MOBILE = "MOBILE",
  API = "API",
}

interface SessionIdOptions {
  source?: SessionSource;
  deviceId?: string;
}

/**
 * Generate a unique session ID following the backend format
 * Format: source_timestamp_randomString
 */
export function generateSessionId(options: SessionIdOptions = {}): string {
  const source = options.source || detectSessionSource();
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const deviceId = options.deviceId || getDeviceId();
  
  return `${source.toLowerCase()}_${timestamp}_${randomString}${deviceId ? `_${deviceId}` : ""}`;
}

/**
 * Detect session source based on user agent and platform
 */
export function detectSessionSource(): SessionSource {
  if (typeof window === "undefined") {
    return SessionSource.WEB;
  }

  const userAgent = window.navigator.userAgent.toLowerCase();
  
  // Check if it's a mobile device
  const isMobile = /mobile|android|iphone|ipad|ipod|windows phone|blackberry|webos|opera mini|iemobile/i.test(userAgent);
  
  if (isMobile) {
    return SessionSource.MOBILE;
  }
  
  // Check if it's an API client (unlikely in browser, but check for electron or similar)
  const isApiClient = /electron|postman|insomnia/i.test(userAgent);
  
  if (isApiClient) {
    return SessionSource.API;
  }
  
  return SessionSource.WEB;
}

/**
 * Get or create a persistent device ID for this browser/device
 */
export function getDeviceId(): string {
  const DEVICE_ID_KEY = "shopc_device_id";
  
  if (typeof window === "undefined") {
    return "";
  }
  
  let deviceId = localStorage.getItem(DEVICE_ID_KEY);
  
  if (!deviceId) {
    deviceId = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
    localStorage.setItem(DEVICE_ID_KEY, deviceId);
  }
  
  return deviceId;
}

/**
 * Get the current session ID from cookies or generate a new one
 */
export function getSessionId(): string {
  const SESSION_ID_KEY = "shopc_session_id";
  
  if (typeof window === "undefined") {
    return generateSessionId();
  }
  
  let sessionId = Cookies.get(SESSION_ID_KEY);
  
  if (!sessionId || !isValidSessionId(sessionId)) {
    sessionId = generateSessionId();
    setSessionId(sessionId);
  }
  
  return sessionId;
}

/**
 * Set session ID in cookies
 */
export function setSessionId(sessionId: string): void {
  const SESSION_ID_KEY = "shopc_session_id";
  
  if (typeof window === "undefined") {
    return;
  }
  
  Cookies.set(SESSION_ID_KEY, sessionId, {
    expires: 7, // 7 days
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
  });
}

/**
 * Clear session ID from cookies
 */
export function clearSessionId(): void {
  const SESSION_ID_KEY = "shopc_session_id";
  
  if (typeof window === "undefined") {
    return;
  }
  
  Cookies.remove(SESSION_ID_KEY);
}

/**
 * Validate session ID format
 * Must be 8-128 characters, alphanumeric with -, _, .
 */
export function isValidSessionId(sessionId: string): boolean {
  if (!sessionId || typeof sessionId !== "string") {
    return false;
  }
  
  const trimmed = sessionId.trim();
  
  // Check length
  if (trimmed.length < 8 || trimmed.length > 128) {
    return false;
  }
  
  // Check format
  const sessionIdRegex = /^[a-zA-Z0-9\-_.]+$/;
  if (!sessionIdRegex.test(trimmed)) {
    return false;
  }
  
  // Security checks
  if (
    trimmed.includes("..") ||
    trimmed.includes("--") ||
    trimmed.includes("script") ||
    trimmed.includes("<") ||
    trimmed.includes(">")
  ) {
    return false;
  }
  
  return true;
}

/**
 * Get device information for session tracking
 */
export function getDeviceInfo(): {
  userAgent: string;
  platform: string;
  language: string;
  screenResolution: string;
  timezone: string;
} {
  if (typeof window === "undefined") {
    return {
      userAgent: "",
      platform: "",
      language: "",
      screenResolution: "",
      timezone: "",
    };
  }
  
  return {
    userAgent: window.navigator.userAgent,
    platform: window.navigator.userAgent || "",
    language: window.navigator.language || "",
    screenResolution: window.screen ? `${window.screen.width}x${window.screen.height}` : "",
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || "",
  };
}

/**
 * Extract session metadata for API requests
 */
export function getSessionMetadata() {
  const deviceInfo = getDeviceInfo();
  
  return {
    sessionId: getSessionId(),
    source: detectSessionSource(),
    deviceId: getDeviceId(),
    userAgent: deviceInfo.userAgent,
    timezone: deviceInfo.timezone,
    language: deviceInfo.language,
  };
}