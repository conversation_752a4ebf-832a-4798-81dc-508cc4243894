/**
 * Utility for printing tables and other HTML elements with preserved styling
 */

import { toast } from "sonner";
import { TemplateType } from "@/types/invoice";
import {
  StandardTemplate,
  SpreadsheetTemplate,
  ContinentalTemplate,
  CompactTemplate,
  ThermalTemplate,
  SupermarketTemplate,
  RestaurantTemplate,
  PharmacyTemplate,
  DigitalTemplate,
} from "@/components/invoice/templates";
import {
  SupportedLanguage,
  getTranslationsByLanguage,
} from "@/config/invoice-translations";
import React from "react";
import ReactDOMServer from "react-dom/server";
import { ViewMode } from "@/components/invoice/templates/types";

interface PrintTableOptions {
  /**
   * Title for the print window
   */
  title?: string;

  /**
   * Callback executed before printing starts
   */
  onPrintStart?: () => void;

  /**
   * Callback executed after printing completes or is canceled
   */
  onPrintEnd?: () => void;

  /**
   * Additional styles to apply to the printed document
   */
  additionalStyles?: string;

  /**
   * Template type for invoice/receipt printing (optional)
   */
  templateType?: TemplateType;

  /**
   * Document type - invoice or receipt (optional)
   */
  documentType?: "invoice" | "receipt";

  /**
   * Additional colors for template printing (optional)
   */
  colors?: {
    headingColor?: string;
    textColor?: string;
    backgroundColor?: string;
    accentColor?: string;
    logoColor?: string;
  };

  /**
   * Language for template translations
   */
  language?: SupportedLanguage;

  /**
   * Use direct template rendering instead of element content
   */
  useDirectTemplate?: boolean;

  /**
   * Template view mode - affects how data is displayed
   */
  viewMode?: ViewMode;
}

/**
 * Get the template component based on template type
 */
const getTemplateComponent = (templateType: TemplateType, props: any) => {
  switch (templateType) {
    case TemplateType.SPREADSHEET:
      return React.createElement(SpreadsheetTemplate, props);
    case TemplateType.CONTINENTAL:
      return React.createElement(ContinentalTemplate, props);
    case TemplateType.COMPACT:
      return React.createElement(CompactTemplate, props);
    case TemplateType.THERMAL:
      return React.createElement(ThermalTemplate, props);
    case TemplateType.SUPERMARKET:
      return React.createElement(SupermarketTemplate, props);
    case TemplateType.RESTAURANT:
      return React.createElement(RestaurantTemplate, props);
    case TemplateType.PHARMACY:
      return React.createElement(PharmacyTemplate, props);
    case TemplateType.DIGITAL:
      return React.createElement(DigitalTemplate, props);
    case TemplateType.STANDARD:
    default:
      return React.createElement(StandardTemplate, props);
  }
};

/**
 * Prints the content of any HTML element with all its styles preserved
 *
 * @param elementId - The ID of the element to print
 * @param options - Print options
 * @returns Promise that resolves when printing is complete or rejects on error
 */
export const printTable = (
  elementId: string,
  options: PrintTableOptions = {}
): Promise<void> => {
  const {
    title = "Print Preview",
    onPrintStart,
    onPrintEnd,
    additionalStyles = "",
    templateType,
    documentType,
    colors,
    language,
    useDirectTemplate = false,
    viewMode,
  } = options;

  // Create template-specific title if template information is provided
  let documentTitle = title;
  if (templateType && documentType) {
    const templateName =
      templateType.charAt(0).toUpperCase() + templateType.slice(1);
    documentTitle = `${
      documentType.charAt(0).toUpperCase() + documentType.slice(1)
    } Preview - ${templateName}`;
  }

  // Generate additional style content based on colors if provided
  let colorStyles = "";
  if (colors) {
    colorStyles = `
      /* Template color styles */
      body {
        background-color: ${colors.backgroundColor || "#ffffff"};
        color: ${colors.textColor || "#4b5563"};
      }
      h1, h2, h3, h4, h5, h6 {
        color: ${colors.headingColor || "#111827"};
      }
    `;
  }

  return new Promise((resolve, reject) => {
    try {
      // Call the start callback if provided
      if (onPrintStart) onPrintStart();

      // Get the HTML content either from a rendered element or a direct template
      let contentHtml = "";

      if (useDirectTemplate && templateType) {
        // If using direct template rendering, render the template
        const templateProps: any = {
          headingColor: colors?.headingColor || "#111827",
          textColor: colors?.textColor || "#4b5563",
          backgroundColor: colors?.backgroundColor || "#ffffff",
          accentColor: colors?.accentColor || "#f3f4f6",
          logoColor: colors?.logoColor || "#b4a7f5",
          viewMode: viewMode || "print",
        };

        // Add translations if language is provided
        if (language) {
          templateProps.translations = getTranslationsByLanguage(language);
        }

        // Get the component based on template type
        const templateComponent = getTemplateComponent(
          templateType,
          templateProps
        );

        // Render the template to HTML string
        contentHtml = ReactDOMServer.renderToString(templateComponent);
      } else {
        // Otherwise get the HTML from the element
        const element = document.getElementById(elementId);
        if (!element) {
          const error = `Element with ID "${elementId}" not found`;
          toast.error(error);
          reject(new Error(error));
          if (onPrintEnd) onPrintEnd();
          return;
        }
        contentHtml = element.innerHTML;
      }

      // Get all stylesheets from the document to maintain styling
      const styleSheets = Array.from(document.styleSheets);
      let styleContent = "";

      // Extract CSS rules from all stylesheets
      styleSheets.forEach((sheet) => {
        try {
          if (sheet.cssRules) {
            const cssRules = Array.from(sheet.cssRules);
            cssRules.forEach((rule) => {
              styleContent += rule.cssText + "\n";
            });
          }
        } catch (e) {
          // Skip stylesheets that can't be accessed due to CORS
          console.warn("Could not access stylesheet rules", e);
        }
      });

      // Create a new window for printing
      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        const error =
          "Unable to open print window. Please check your popup settings.";
        toast.error(error);
        reject(new Error(error));
        if (onPrintEnd) onPrintEnd();
        return;
      }

      // Write the content to the new window with all styles
      printWindow.document.write(`
        <html>
          <head>
            <title>${documentTitle}</title>
            <style>
              /* Reset default margins */
              body {
                margin: 0;
                padding: 20px;
                background-color: white;
              }
              
              /* Apply styles to the print wrapper */
              .print-wrapper {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
                box-sizing: border-box;
              }
              
              /* Template color styles */
              ${colorStyles}
              
              /* Additional custom styles */
              ${additionalStyles}
              
              /* Global stylesheet styles */
              ${styleContent}
              
              /* Print-specific styles */
              @media print {
                body {
                  margin: 0;
                  padding: 0;
                }
                .print-wrapper {
                  width: 100%;
                  padding: 0;
                }
              }
            </style>
          </head>
          <body>
            <div class="print-wrapper">
              <div class="print-content">
                ${contentHtml}
              </div>
            </div>
          </body>
        </html>
      `);

      // Wait for content to load before printing
      printWindow.document.close();

      // Track if print dialog was shown
      let printDialogShown = false;

      printWindow.onload = () => {
        setTimeout(() => {
          // Mark that we've shown the print dialog
          printDialogShown = true;

          // Add beforeprint event to detect when print dialog opens
          printWindow.addEventListener("beforeprint", () => {
            printDialogShown = true;
          });

          printWindow.print();

          // Add afterprint event to detect when printing completes or is cancelled
          printWindow.onafterprint = () => {
            printWindow.close();
            if (onPrintEnd) onPrintEnd();
            resolve();
          };

          // Detect if print dialog was cancelled (focus returns to window without afterprint event)
          printWindow.addEventListener("focus", () => {
            // If we get focus back and we showed the dialog, but no afterprint event fired,
            // the user likely cancelled the print
            if (printDialogShown) {
              // Short delay to see if afterprint will fire
              setTimeout(() => {
                if (!printWindow.closed) {
                  if (onPrintEnd) onPrintEnd();
                  resolve();

                  // Keep the window open so user can try again if desired
                  // Add a message to inform the user
                  const messageDiv = printWindow.document.createElement("div");
                  messageDiv.style.cssText =
                    "position: fixed; top: 0; left: 0; right: 0; background: #f8d7da; color: #721c24; padding: 10px; text-align: center; font-family: system-ui;";
                  messageDiv.innerHTML =
                    "Print was cancelled. You can close this window or try printing again.";
                  printWindow.document.body.prepend(messageDiv);

                  // Add a close button
                  const closeButton =
                    printWindow.document.createElement("button");
                  closeButton.textContent = "Close Window";
                  closeButton.style.cssText =
                    "background: #721c24; color: white; border: none; padding: 5px 10px; margin-left: 10px; cursor: pointer; border-radius: 3px;";
                  closeButton.onclick = () => printWindow.close();
                  messageDiv.appendChild(closeButton);

                  // Add a print again button
                  const printAgainButton =
                    printWindow.document.createElement("button");
                  printAgainButton.textContent = "Print Again";
                  printAgainButton.style.cssText =
                    "background: #0d6efd; color: white; border: none; padding: 5px 10px; margin-left: 10px; cursor: pointer; border-radius: 3px;";
                  printAgainButton.onclick = () => printWindow.print();
                  messageDiv.appendChild(printAgainButton);
                }
              }, 500);
            }
          });
        }, 500); // Short delay to ensure styles are applied
      };

      // Fallback in case other events don't fire
      const fallbackTimer = setTimeout(() => {
        if (printWindow && !printWindow.closed) {
          // If window is still open after timeout, assume user abandoned or cancelled
          if (onPrintEnd) onPrintEnd();
          resolve();
        }
      }, 10000);

      // Clean up timer if window closes
      printWindow.onunload = () => {
        clearTimeout(fallbackTimer);
        if (onPrintEnd) onPrintEnd();
        resolve();
      };
    } catch (error) {
      console.error("Print error:", error);
      toast.error("Print Error: " + (error instanceof Error ? error.message : String(error)));
      if (onPrintEnd) onPrintEnd();
      reject(error);
    }
  });
};
