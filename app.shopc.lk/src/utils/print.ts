/**
 * Utility for printing HTML elements with preserved styling
 */

import { toast } from "sonner";

interface PrintElementOptions {
  title?: string;
  onPrintStart?: () => void;
  onPrintEnd?: () => void;
  additionalStyles?: string;
}

/**
 * Prints the content of an HTML element with all its styles preserved
 *
 * @param elementId - The ID of the element to print
 * @param options - Print options
 * @returns Promise that resolves when printing is complete or rejects on error
 */
export const printElement = (
  elementId: string,
  options: PrintElementOptions = {}
): Promise<void> => {
  const {
    title = "Print Preview",
    onPrintStart,
    onPrintEnd,
    additionalStyles = "",
  } = options;

  return new Promise((resolve, reject) => {
    try {
      // Call the start callback if provided
      if (onPrintStart) onPrintStart();

      // Get the element to print
      const element = document.getElementById(elementId);
      if (!element) {
        const error = `Element with ID "${elementId}" not found`;
        toast.error(error);
        reject(new Error(error));
        if (onPrintEnd) onPrintEnd();
        return;
      }

      // Get all stylesheets from the document to maintain styling
      const styleSheets = Array.from(document.styleSheets);
      let styleContent = "";

      // Extract CSS rules from all stylesheets
      styleSheets.forEach((sheet) => {
        try {
          if (sheet.cssRules) {
            const cssRules = Array.from(sheet.cssRules);
            cssRules.forEach((rule) => {
              styleContent += rule.cssText + "\n";
            });
          }
        } catch (e) {
          // Skip stylesheets that can't be accessed due to CORS
          console.warn("Could not access stylesheet rules", e);
        }
      });

      // Get computed styles for the element
      const computedStyle = window.getComputedStyle(element);
      let elementStyle = "";

      // Create a style block for the element to maintain its exact appearance
      elementStyle += ".print-content {";
      for (let i = 0; i < computedStyle.length; i++) {
        const prop = computedStyle[i];
        const value = computedStyle.getPropertyValue(prop);
        if (value) {
          elementStyle += `${prop}: ${value}; `;
        }
      }
      elementStyle += "}";

      // Create a new window for printing
      const printWindow = window.open("", "_blank");
      if (!printWindow) {
        const error =
          "Unable to open print window. Please check your popup settings.";
        toast.error(error);
        reject(new Error(error));
        if (onPrintEnd) onPrintEnd();
        return;
      }

      // Clone the element's HTML content
      const contentHtml = element.innerHTML;

      // Write the content to the new window with all styles
      printWindow.document.write(`
        <html>
          <head>
            <title>${title}</title>
            <style>
              /* Reset default margins */
              body {
                margin: 0;
                padding: 20px;
                background-color: white;
              }
              
              /* Apply styles to the print wrapper */
              .print-wrapper {
                width: 100%;
                max-width: 100%;
                margin: 0 auto;
                box-sizing: border-box;
              }
              
              /* Element-specific styles */
              ${elementStyle}
              
              /* Additional custom styles */
              ${additionalStyles}
              
              /* Global stylesheet styles */
              ${styleContent}
              
              /* Print-specific styles */
              @media print {
                body {
                  margin: 0;
                  padding: 0;
                }
                .print-wrapper {
                  width: 100%;
                  padding: 0;
                }
              }
            </style>
          </head>
          <body>
            <div class="print-wrapper">
              <div class="print-content">
                ${contentHtml}
              </div>
            </div>
          </body>
        </html>
      `);

      // Wait for content to load before printing
      printWindow.document.close();

      // Track if print dialog was shown
      let printDialogShown = false;

      printWindow.onload = () => {
        setTimeout(() => {
          // Mark that we've shown the print dialog
          printDialogShown = true;

          // Add beforeprint event to detect when print dialog opens
          printWindow.addEventListener("beforeprint", () => {
            printDialogShown = true;
          });

          printWindow.print();

          // Add afterprint event to detect when printing completes or is cancelled
          printWindow.onafterprint = () => {
            printWindow.close();
            if (onPrintEnd) onPrintEnd();
            resolve();
          };

          // Detect if print dialog was cancelled (focus returns to window without afterprint event)
          printWindow.addEventListener("focus", () => {
            // If we get focus back and we showed the dialog, but no afterprint event fired,
            // the user likely cancelled the print
            if (printDialogShown) {
              // Short delay to see if afterprint will fire
              setTimeout(() => {
                if (!printWindow.closed) {
                  if (onPrintEnd) onPrintEnd();
                  resolve();

                  // Keep the window open so user can try again if desired
                  // Add a message to inform the user
                  const messageDiv = printWindow.document.createElement("div");
                  messageDiv.style.cssText =
                    "position: fixed; top: 0; left: 0; right: 0; background: #f8d7da; color: #721c24; padding: 10px; text-align: center; font-family: system-ui;";
                  messageDiv.innerHTML =
                    "Print was cancelled. You can close this window or try printing again.";
                  printWindow.document.body.prepend(messageDiv);

                  // Add a close button
                  const closeButton =
                    printWindow.document.createElement("button");
                  closeButton.textContent = "Close Window";
                  closeButton.style.cssText =
                    "background: #721c24; color: white; border: none; padding: 5px 10px; margin-left: 10px; cursor: pointer; border-radius: 3px;";
                  closeButton.onclick = () => printWindow.close();
                  messageDiv.appendChild(closeButton);

                  // Add a print again button
                  const printAgainButton =
                    printWindow.document.createElement("button");
                  printAgainButton.textContent = "Print Again";
                  printAgainButton.style.cssText =
                    "background: #0d6efd; color: white; border: none; padding: 5px 10px; margin-left: 10px; cursor: pointer; border-radius: 3px;";
                  printAgainButton.onclick = () => printWindow.print();
                  messageDiv.appendChild(printAgainButton);
                }
              }, 500);
            }
          });
        }, 500); // Short delay to ensure styles are applied
      };

      // Fallback in case other events don't fire
      const fallbackTimer = setTimeout(() => {
        if (printWindow && !printWindow.closed) {
          // If window is still open after timeout, assume user abandoned or cancelled
          if (onPrintEnd) onPrintEnd();
          resolve();
        }
      }, 10000);

      // Clean up timer if window closes
      printWindow.onunload = () => {
        clearTimeout(fallbackTimer);
        if (onPrintEnd) onPrintEnd();
        resolve();
      };
    } catch (error) {
      console.error("Print error:", error);
      toast.error("Print Error: " + (error instanceof Error ? error.message : String(error)));
      if (onPrintEnd) onPrintEnd();
      reject(error);
    }
  });
};
