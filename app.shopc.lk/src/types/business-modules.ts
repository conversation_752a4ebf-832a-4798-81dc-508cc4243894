export enum ModuleType {
  // Main modules
  MARKETING = "Marketing",
  PAYMENT_ACCOUNT = "PaymentAccount",
  INVENTORY = "Inventory",
  FINANCE = "Finance",
  CRM = "CRM",
  PROJECT = "Project",
  SUPPORT = "Support",

  APPOINTMENTS = "Appointments",

  //   Assets
  // Asset allocated
  // Asset revoked
  // Asset maintenance
  // Asset categories

  ASSETS = "Assets",
  ASSETS_TYPES = "AssetsTypes",
  ASSETS_CATEGORIES = "AssetsCategories",
  ASSETS_ALLOCATED = "AssetsAllocated",
  ASSETS_REVOKED = "AssetsRevoked",
  ASSETS_MAINTENANCE = "AssetsMaintenance",

  // Former submodules now as modules
  ATTENDANCE = "Attendance",
  PAYROLL = "Payroll",
  RECRUITMENT = "Recruitment",
  PERFORMANCE = "Performance",
  LEAVE = "Leave",
  TRAINING = "Training",
  CAMPAIGN = "Campaign",
  SOCIAL_MEDIA = "SocialMedia",
  EMAIL_MARKETING = "EmailMarketing",
  SEO = "SEO",
  FIXED_ASSETS = "FixedAssets",
  DIGITAL_ASSETS = "DigitalAssets",
  MAINTENANCE = "Maintenance",
  BANK_ACCOUNTS = "BankAccounts",
  PAYMENT_GATEWAYS = "PaymentGateways",
  TRANSACTIONS = "Transactions",

  RETAIL_POS = "RetailPOS",
  RESTAURANT_POS = "RestaurantPOS",
  SALES = "Sales",

  WORKORDERS = "WorkOrders",
  SERVICE_ORDERS = "ServiceOrders",

  PRODUCTS = "Products",

  ITEMS = "Items",
  CATEGORIES = "Categories",
  BRANDS = "Brands",
  WARRANCIES = "Warranties",
  MEAL_PERIODS = "MealPeriods",
  VARIANTS = "Variants",

  CUSTOMERS = "Customers",
  CUSTOMER_GROUPS = "CustomerGroups",

  SERVICES = "Services",

  PAYMENT_ACCOUNTS = "PaymentAccounts",
  CASH_FLOW = "CashFlow",
  PAYMENTS = "Payments",
  PAYMENT_ACCOUNT_TYPES = "PaymentAccountTypes",

  EXPENSES = "Expenses",
  EXPENSE_CATEGORIES = "ExpenseCategories",

  HRM = "Hrm",
  EMPLOYEES = "Employees",
  DESIGNATIONS = "Designations",
  DEPARTMENTS = "Departments",

  // Vehicles
  VEHICLE_TYPES = "VehicleTypes",
  VEHICLES = "Vehicles",
  VEHICLE_FINES = "VehicleFines",
  VEHICLE_DAMAGES = "VehicleDamages",

  // purchases
  SUPPLIERS = "Suppliers",
  PURCHASES = "Purchases",
  PURCHASE_RETURNS = "PurchaseReturns",

  MODIFIERS = "Modifiers",

  SERVICE_TYPES = "ServiceTypes",

  // Room and Accommodation modules
  ROOMS = "Rooms",
  ROOM_TYPES = "RoomTypes",
  ROOM_BOOKINGS = "RoomBookings",

  // Account modules
  ACCOUNTS = "Accounts",

  // Tax modules
  TAX = "Tax",
  PAYMENT_METHODS = "PaymentMethods",

  // Task management
  TASKS = "Tasks",

  // Promotional modules
  DISCOUNT_PLANS = "DiscountPlans",
  PROMO_CODES = "PromoCodes",
  GAMES = "Games",

  // Subscription modules
  SUBSCRIPTION_PLANS = "SubscriptionPlans",
  SUBSCRIPTION_PLAN_ASSIGNMENTS = "SubscriptionPlanAssignments",

  // Provider modules
  SMS_PROVIDERS = "SmsProviders",
  EMAIL_PROVIDERS = "EmailProviders",
  WHATSAPP_PROVIDERS = "WhatsappProviders",
  COD_PROVIDERS = "CodProviders",
  PAYMENT_GATEWAY_PROVIDERS = "PaymentGatewayProviders",
  AI_PROVIDERS = "AiProviders",

  // Account modules
  PROFILE = "Profile",
  SECURITY = "Security",

  UNITS = "Units",
}
