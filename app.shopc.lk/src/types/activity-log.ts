import { ApiResponse } from "./common";

export enum ActivityLogType {
  CREATE = "create",
  UPDATE = "update",
  DELETE = "delete",
  VIEW = "view",
  LOGIN = "login",
  LOGOUT = "logout",
  EXPORT = "export",
  IMPORT = "import",
  UPLOAD = "upload",
  DOWNLOAD = "download",
  APPROVE = "approve",
  REJECT = "reject",
  ARCHIVE = "archive",
  RESTORE = "restore",
}

export enum ActivityLogStatus {
  SUCCESS = "success",
  FAILED = "failed",
  PENDING = "pending",
}

export enum ActivityEntityType {
  USER = "user",
  CATEGORY = "category",
  SERVICE_CATEGORY = "service_category",
  PRODUCT = "product",
  SERVICE = "service",
  CUSTOMER = "customer",
  SUPPLIER = "supplier",
  STAFF = "staff",
  LOCATION = "location",
  INVOICE = "invoice",
  ORDER = "order",
  PAYMENT = "payment",
  INVENTORY = "inventory",
  BUSINESS = "business",
  ACCOUNT = "account",
  TASK = "task",
  PROJECT = "project",
  CAMPAIGN = "campaign",
  BRAND = "brand",
  ASSET = "asset",
  VEHICLE = "vehicle",
  EQUIPMENT = "equipment",
}

// Backend DTO: ActivityLogDto
export interface ActivityLogDto {
  id: string;
  businessId: string;
  userId: string;
  userName: string;
  userEmail: string;
  type: ActivityLogType;
  entityType: ActivityEntityType;
  entityId?: string;
  entityName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  status: ActivityLogStatus;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateActivityLogDto
export interface CreateActivityLogDto {
  type: ActivityLogType;
  entityType: ActivityEntityType;
  entityId?: string;
  entityName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  status?: ActivityLogStatus;
}

// Backend DTO: ActivityLogSlimDto
export interface ActivityLogSlimDto {
  id: string;
  type: ActivityLogType;
  entityType: ActivityEntityType;
  entityName?: string;
  description: string;
  status: ActivityLogStatus;
  createdAt: Date;
  userName: string;
}

// Backend DTO: PaginatedActivityLogsResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedActivityLogsResponseDto {
  data: ActivityLogTableData[];
  meta: PaginationMetaDto;
}

// API Response wrappers
export interface ActivityLogResponse extends ApiResponse<ActivityLogDto> {}

export interface ActivityLogTableResponse
  extends ApiResponse<ActivityLogTableData[]> {}

export interface ActivityLogPaginatedResponse
  extends ApiResponse<PaginatedActivityLogsResponseDto | null> {}

export interface SimpleActivityLogResponse
  extends ApiResponse<ActivityLogSlimDto[]> {}

// Table data interface - optimized for table display
export interface ActivityLogTableData {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  type: ActivityLogType;
  entityType: ActivityEntityType;
  entityId?: string;
  entityName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  status: ActivityLogStatus;
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility
export type ActivityLog = ActivityLogDto;
export type SimpleActivityLogData = ActivityLogSlimDto;
export type ActivityLogPaginatedData = PaginatedActivityLogsResponseDto;

// Form values interface for UI components (if needed)
export interface ActivityLogFormValues {
  type: ActivityLogType;
  entityType: ActivityEntityType;
  entityId?: string;
  entityName?: string;
  description: string;
  metadata?: Record<string, any>;
  status?: ActivityLogStatus;
}

// Extended interfaces for frontend use
export interface ActivityLogTableDataExtended extends ActivityLogTableData {
  _id: string; // Legacy field for backward compatibility
  formattedDate: string; // Formatted creation date
  typeLabel: string; // Human-readable type label
  statusLabel: string; // Human-readable status label
  entityTypeLabel: string; // Human-readable entity type label
}

// Statistics data interface - for dashboard/analytics
export interface ActivityLogStatsData {
  totalActivities: number;
  todayActivities: number;
  successfulActivities: number;
  failedActivities: number;
  activitiesByType: Record<ActivityLogType, number>;
  activitiesByEntityType: Record<ActivityEntityType, number>;
  activitiesByStatus: Record<ActivityLogStatus, number>;
  recentActivities: ActivityLogSlimDto[];
}

// API Response wrapper for stats data
export interface ActivityLogStatsResponse
  extends ApiResponse<ActivityLogStatsData> {}

// Filter interface for activity logs
export interface ActivityLogFilterData {
  userId?: string;
  type?: ActivityLogType;
  entityType?: ActivityEntityType;
  status?: ActivityLogStatus;
  entityId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  searchTerm?: string;
}

// Export data interface
export interface ActivityLogExportData {
  format: 'csv' | 'xlsx' | 'json';
  filters?: ActivityLogFilterData;
  columns?: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
}

// Response for export operations
export interface ActivityLogExportResponse extends ApiResponse<{
  downloadUrl: string;
  fileName: string;
  fileSize: number;
}> {}