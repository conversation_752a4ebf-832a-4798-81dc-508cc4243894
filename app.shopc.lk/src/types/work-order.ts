import { ApiResponse } from "./common";

// Work Order Status Enum (matching backend schema)
export enum WorkOrderStatus {
  DRAFT = "draft",
  PLANNED = "planned",
  IN_PROGRESS = "in_progress",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

// Work Order Priority Enum (matching backend schema)
export enum WorkOrderPriority {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  URGENT = "urgent",
}

// BOM Type Enum (matching backend schema)
export enum BomType {
  MANUFACTURING = "manufacturing",
  ASSEMBLY = "assembly",
  DISASSEMBLY = "disassembly",
}

// Quality Inspection Result Enum (matching backend schema)
export enum QualityInspectionResult {
  PASS = "pass",
  FAIL = "fail",
  CONDITIONAL = "conditional",
}

// Backend DTO: WorkOrderDto
export interface WorkOrderDto {
  id: string;
  businessId: string;
  workOrderNumber: string;
  productId: string;
  variantId?: string;
  bomId: string;
  quantityToProduce: string;
  quantityProduced: string;
  quantityScrapped: string;
  statusId: string;
  priorityId: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  notes?: string;
  qualityInspectionRequired: boolean;
  qualityInspectionCompleted: boolean;
  qualityInspectionResult?: string;
  qualityInspectionNotes?: string;
  inspectedBy?: string;
  // Related data
  product?: {
    id: string;
    name: string;
    sku: string;
  };
  variant?: {
    id: string;
    name: string;
    sku: string;
  };
  bom?: {
    id: string;
    bomCode: string;
    bomType: string;
  };
  status?: {
    id: string;
    statusCode: string;
    statusName: string;
    colorCode?: string;
    statusType: string;
  };
  priority?: {
    id: string;
    priorityCode: string;
    priorityName: string;
    colorCode?: string;
    severityLevel: number;
  };
  salesOrders: { id: string; orderNumber: string }[];
  staff: { id: string; name: string; taskId?: string }[];
  task?: {
    id: string;
    title: string;
    status: string;
    priority: string;
    assignedTo?: string;
    dueDate?: string;
  };
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateWorkOrderDto
export interface CreateWorkOrderDto {
  workOrderNumber: string;
  productId: string;
  variantId?: string;
  bomId: string;
  quantityToProduce: string;
  quantityProduced?: string;
  quantityScrapped?: string;
  statusId: string;
  priorityId: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  notes?: string;
  qualityInspectionRequired?: boolean;
  qualityInspectionCompleted?: boolean;
  qualityInspectionResult?: string;
  qualityInspectionNotes?: string;
  inspectedBy?: string;
  salesOrderIds?: string[];
  staffIds?: string[];
  // Staff assignments with optional tasks
  staffAssignments?: {
    staffId: string;
    createTask?: boolean;
    taskTitle?: string;
    taskDescription?: string;
    taskDueDate?: string;
    taskPriority?: string;
  }[];
  // Task creation fields
  createTask?: boolean;
  taskTitle?: string;
  taskDescription?: string;
  taskDueDate?: string;
  taskPriority?: string;
  taskAssignedTo?: string;
}

// Backend DTO: UpdateWorkOrderDto
export interface UpdateWorkOrderDto {
  workOrderNumber?: string;
  productId?: string;
  variantId?: string;
  bomId?: string;
  quantityToProduce?: string;
  quantityProduced?: string;
  quantityScrapped?: string;
  statusId?: string;
  priorityId?: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  notes?: string;
  qualityInspectionRequired?: boolean;
  qualityInspectionCompleted?: boolean;
  qualityInspectionResult?: string;
  qualityInspectionNotes?: string;
  inspectedBy?: string;
  salesOrderIds?: string[];
  staffIds?: string[];
  // Staff assignments with optional tasks
  staffAssignments?: {
    staffId: string;
    createTask?: boolean;
    taskTitle?: string;
    taskDescription?: string;
    taskDueDate?: string;
    taskPriority?: string;
  }[];
  // Task update fields
  taskTitle?: string;
  taskDescription?: string;
  taskDueDate?: string;
  taskPriority?: string;
  taskAssignedTo?: string;
}

// Backend DTO: WorkOrderSlimDto
export interface WorkOrderSlimDto {
  id: string;
  workOrderNumber: string;
  productId: string;
  quantityToProduce: string;
  quantityProduced: string;
  statusId: string;
  priorityId: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
}

// Backend DTO: WorkOrderListDto
export interface WorkOrderListDto {
  id: string;
  workOrderNumber: string;
  productId: string;
  quantityToProduce: string;
  quantityProduced: string;
  statusId: string;
  priorityId: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: BulkCreateWorkOrderDto
export interface BulkCreateWorkOrderDto {
  workOrders: string; // JSON string containing array of work order objects
}

// Backend DTO: BulkDeleteWorkOrderDto
export interface BulkDeleteWorkOrderDto {
  workOrderIds: string[];
}

// Response DTOs
export interface WorkOrderIdResponseDto {
  id: string;
  message: string;
}

export interface BulkWorkOrderIdsResponseDto {
  ids: string[];
  message: string;
}

export interface DeleteWorkOrderResponseDto {
  id: string;
  message: string;
}

export interface BulkDeleteWorkOrderResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
  failed?: string[];
}

export interface WorkOrderNumberAvailabilityResponseDto {
  available: boolean;
}

export interface PaginatedWorkOrdersResponseDto {
  data: WorkOrderListDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Work Order Task DTOs
export interface WorkOrderTaskDto {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  assignedTo?: string;
  dueDate?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateWorkOrderTaskDto {
  title: string;
  description?: string;
  priority?: string;
  assignedTo?: string;
  dueDate?: string;
}

export interface UpdateWorkOrderTaskDto {
  title?: string;
  description?: string;
  priority?: string;
  assignedTo?: string;
  dueDate?: string;
}

export interface WorkOrderTaskIdResponseDto {
  id: string;
  message: string;
}

export interface WorkOrderTaskDeleteResponseDto {
  message: string;
}

// API Response Types
export interface WorkOrderResponse extends ApiResponse<WorkOrderDto> {}
export interface WorkOrderPaginatedResponse
  extends ApiResponse<PaginatedWorkOrdersResponseDto> {}
export interface WorkOrderIdResponse
  extends ApiResponse<WorkOrderIdResponseDto> {}
export interface BulkWorkOrderIdsResponse
  extends ApiResponse<BulkWorkOrderIdsResponseDto> {}
export interface BulkDeleteWorkOrderResponse
  extends ApiResponse<BulkDeleteWorkOrderResponseDto> {}
export interface WorkOrderNumberAvailabilityResponse
  extends ApiResponse<WorkOrderNumberAvailabilityResponseDto> {}
export interface SimpleWorkOrderResponse
  extends ApiResponse<WorkOrderSlimDto[]> {}
export interface WorkOrderListResponse
  extends ApiResponse<WorkOrderListDto[]> {}
export interface WorkOrderTaskResponse extends ApiResponse<WorkOrderTaskDto> {}
export interface WorkOrderTaskIdResponse
  extends ApiResponse<WorkOrderTaskIdResponseDto> {}
export interface WorkOrderTaskDeleteResponse
  extends ApiResponse<WorkOrderTaskDeleteResponseDto> {}

// Table data interface - optimized for table display
export interface WorkOrderTableData {
  id: string;
  workOrderNumber: string;
  productId: string;
  productName?: string;
  productSku?: string;
  quantityToProduce: string;
  quantityProduced: string;
  quantityScrapped: string;
  statusId: string;
  statusName?: string;
  statusColorCode?: string;
  priorityId: string;
  priorityName?: string;
  priorityColorCode?: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  qualityInspectionRequired: boolean;
  qualityInspectionCompleted: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Form values interface for UI components
export interface WorkOrderFormValues {
  workOrderNumber: string;
  productId: string;
  variantId?: string;
  bomId: string;
  quantityToProduce: string;
  quantityProduced?: string;
  quantityScrapped?: string;
  statusId: string;
  priorityId: string;
  plannedStartDate?: string;
  plannedEndDate?: string;
  actualStartDate?: string;
  actualEndDate?: string;
  notes?: string;
  qualityInspectionRequired?: boolean;
  qualityInspectionCompleted?: boolean;
  qualityInspectionResult?: string;
  qualityInspectionNotes?: string;
  inspectedBy?: string;
  salesOrderIds?: string[];
  staffIds?: string[];
  // Task fields
  createTask?: boolean;
  taskTitle?: string;
  taskDescription?: string;
  taskDueDate?: string;
  taskPriority?: string;
  taskAssignedTo?: string;
}

// Bulk create work order interface
export interface BulkCreateWorkOrderItemDto extends CreateWorkOrderDto {
  imageIndex?: number; // For mapping to uploaded images
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type WorkOrder = WorkOrderDto;
export type SimpleWorkOrderData = WorkOrderSlimDto;
export type WorkOrderPaginatedData = PaginatedWorkOrdersResponseDto;
