import { ApiResponse } from "./common";

export enum ServiceStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

// Backend DTO: ServiceDto
export interface ServiceDto {
  id: string;
  businessId: string;
  name: string;
  itemType: string; // Always "service"
  sku: string;
  serviceCategoryId: string;
  serviceCategoryName: string;
  serviceSubCategoryId?: string;
  serviceSubCategoryName?: string;
  description?: string;
  priceRate: string;
  incomeAccountId: string;
  incomeAccountName: string;
  salesTaxId: string;
  salesTaxName: string;
  sellToCustomers: boolean;
  purchaseFromSupplier: boolean;
  purchaseDescription?: string;
  purchaseCost?: string;
  expenseAccountId?: string;
  expenseAccountName?: string;
  preferredSupplierId?: string;
  preferredSupplierName?: string;
  availableOnline: boolean;
  position: number;
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  ogImage?: string;
  images: string[];
  // Location fields
  isAllocatedToAllLocations: boolean;
  locations: { id: string; name: string }[]; // Empty array when allocated to all locations
  // Staff assignment
  staffMembers: { id: string; name: string }[];
  status: ServiceStatus;
  createdBy: string;
  updatedBy?: string;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateServiceDto
export interface CreateServiceDto {
  name: string;
  sku?: string;
  serviceCategoryId: string;
  serviceSubCategoryId?: string;
  description?: string;
  priceRate: string;
  incomeAccountId: string;
  salesTaxId: string;
  sellToCustomers?: boolean;
  purchaseFromSupplier?: boolean;
  purchaseDescription?: string;
  purchaseCost?: string;
  expenseAccountId?: string;
  preferredSupplierId?: string;
  availableOnline?: boolean;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // Staff assignment
  staffMemberIds?: string[];
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Backend DTO: UpdateServiceDto
export interface UpdateServiceDto {
  name?: string;
  sku?: string;
  serviceCategoryId?: string;
  serviceSubCategoryId?: string;
  description?: string;
  priceRate?: string;
  incomeAccountId?: string;
  salesTaxId?: string;
  sellToCustomers?: boolean;
  purchaseFromSupplier?: boolean;
  purchaseDescription?: string;
  purchaseCost?: string;
  expenseAccountId?: string;
  preferredSupplierId?: string;
  availableOnline?: boolean;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // Staff assignment
  staffMemberIds?: string[];
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Backend DTO: ServiceSlimDto
export interface ServiceSlimDto {
  id: string;
  name: string;
  sku: string;
  serviceCategoryId: string;
  serviceCategoryName: string;
  serviceSubCategoryId?: string;
  serviceSubCategoryName?: string;
  priceRate: string;
  position: number;
  status: ServiceStatus;
  media?: {
    id: string;
    publicUrl: string;
    originalName: string;
  };
}

// Backend DTO: PaginatedServicesResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedServicesResponseDto {
  data: ServiceTableData[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeleteServiceResponseDto
export interface DeleteServiceResponseDto {
  message: string;
}

// Backend DTO: ServiceNameAvailabilityResponseDto
export interface ServiceNameAvailabilityResponseDto {
  available: boolean;
  message: string;
}

// Backend DTO: ServiceSkuAvailabilityResponseDto
export interface ServiceSkuAvailabilityResponseDto {
  available: boolean;
  message: string;
}

// Backend DTO: ServiceIdResponseDto
export interface ServiceIdResponseDto {
  id: string;
}

// Backend DTO: BulkServiceIdsResponseDto
export interface BulkServiceIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteServiceDto
export interface BulkDeleteServiceDto {
  ids: string[];
}

// Backend DTO: BulkDeleteServiceResponseDto
export interface BulkDeleteServiceResponseDto {
  message: string;
  deletedCount: number;
}

// API Response wrappers
export interface ServiceResponse extends ApiResponse<ServiceDto> {}

export interface ServiceTableResponse extends ApiResponse<ServiceTableData[]> {}

export interface ServicePaginatedResponse
  extends ApiResponse<PaginatedServicesResponseDto | null> {}

export interface SimpleServiceResponse extends ApiResponse<ServiceSlimDto[]> {}

export interface ServiceNameAvailabilityResponse
  extends ApiResponse<ServiceNameAvailabilityResponseDto> {}

export interface ServiceSkuAvailabilityResponse
  extends ApiResponse<ServiceSkuAvailabilityResponseDto> {}

export interface ServiceIdResponse extends ApiResponse<ServiceIdResponseDto> {}

export interface BulkServiceIdsResponse
  extends ApiResponse<BulkServiceIdsResponseDto> {}

export interface BulkDeleteServiceResponse
  extends ApiResponse<BulkDeleteServiceResponseDto> {}

// Table data interface - optimized for table display (based on ServiceListDto)
export interface ServiceTableData {
  id: string;
  name: string;
  sku: string;
  serviceCategoryId: string;
  serviceCategoryName: string;
  serviceSubCategoryId?: string;
  serviceSubCategoryName?: string;
  description?: string;
  priceRate: string;
  incomeAccountId: string;
  incomeAccountName: string;
  salesTaxId: string;
  salesTaxName: string;
  sellToCustomers: boolean;
  purchaseFromSupplier: boolean;
  purchaseDescription?: string;
  purchaseCost?: string;
  expenseAccountId?: string;
  expenseAccountName?: string;
  preferredSupplierId?: string;
  preferredSupplierName?: string;
  availableOnline: boolean;
  position: number;
  status: ServiceStatus;
  images: string[];
  // Location fields
  isAllocatedToAllLocations: boolean;
  locationIds: string[]; // Legacy field - will be removed after migration
  locations: { id: string; name: string }[]; // Empty array when allocated to all locations
  // Staff assignment
  staffMemberIds: string[];
  staffMembers: { id: string; name: string }[];
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  ogImage?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type Service = ServiceDto;
export type SimpleServiceData = ServiceSlimDto;
export type ServicePaginatedData = PaginatedServicesResponseDto;

// Form values interface for UI components
export interface ServiceFormValues {
  name: string;
  sku?: string;
  serviceCategoryId: string;
  serviceSubCategoryId?: string;
  description?: string;
  priceRate: string;
  incomeAccountId: string;
  salesTaxId: string;
  sellToCustomers?: boolean;
  purchaseFromSupplier?: boolean;
  purchaseDescription?: string;
  purchaseCost?: string;
  expenseAccountId?: string;
  preferredSupplierId?: string;
  availableOnline?: boolean;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // Staff assignment
  staffMemberIds?: string[];
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Extended interfaces for frontend use
export interface ServiceTableDataExtended extends ServiceTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
  code?: string; // Legacy field mapping to sku
  customFields?: Record<string, string>[]; // Legacy field
  availableInOnlineStore: boolean; // Legacy field mapping to availableOnline
}

// Bulk create service interface
export interface BulkCreateServiceDto extends CreateServiceDto {
  imageIndex?: number; // For mapping to uploaded images
}
