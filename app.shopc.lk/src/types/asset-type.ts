import { ApiResponse } from "./common";

export enum AssetTypeStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum AssetTypeCategory {
  PHYSICAL = "PHYSICAL",
  DIGITAL = "DIGITAL",
}

export enum AssetTypeReferenceType {
  RENTAL_ITEM_CATEGORY = "rental-item-category",
  VEHICLE_CATEGORY = "vehicle-category",
}

// Backend DTO: AssetTypeDto
export interface AssetTypeDto {
  id: string;
  businessId: string;
  name: string;
  description?: string;
  parentId?: string;
  category: AssetTypeCategory;
  referenceId?: string;
  referenceType?: AssetTypeReferenceType;
  status: AssetTypeStatus;
  assetsCount: number;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateAssetTypeDto
export interface CreateAssetTypeDto {
  name: string;
  description?: string;
  parentId?: string;
  category: AssetTypeCategory;
  referenceId?: string;
  referenceType?: AssetTypeReferenceType;
  status?: AssetTypeStatus;
}

// Backend DTO: UpdateAssetTypeDto
export interface UpdateAssetTypeDto {
  name?: string;
  description?: string;
  parentId?: string;
  category?: AssetTypeCategory;
  referenceId?: string;
  referenceType?: AssetTypeReferenceType;
  status?: AssetTypeStatus;
}

// Backend DTO: AssetTypeSlimDto
export interface AssetTypeSlimDto {
  id: string;
  name: string;
}

// Backend DTO: AssetTypeListDto (optimized for table display)
export interface AssetTypeListDto {
  id: string;
  name: string;
  description?: string;
  status: AssetTypeStatus;
  parentId?: string;
  parentName?: string;
  subcategoriesCount: number;
  assetsCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: PaginatedAssetTypesResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedAssetTypesResponseDto {
  data: AssetTypeListDto[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeleteAssetTypeResponseDto
export interface DeleteAssetTypeResponseDto {
  success: boolean;
  message: string;
}

// Backend DTO: AssetTypeNameAvailabilityResponseDto
export interface AssetTypeNameAvailabilityResponseDto {
  available: boolean;
}

// Backend DTO: AssetTypeIdResponseDto
export interface AssetTypeIdResponseDto {
  id: string;
}

// Backend DTO: BulkAssetTypeIdsResponseDto
export interface BulkAssetTypeIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteAssetTypeDto
export interface BulkDeleteAssetTypeDto {
  assetTypeIds: string[];
}

// Backend DTO: BulkDeleteAssetTypeResponseDto
export interface BulkDeleteAssetTypeResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
}

// Backend DTO: BulkUpdateAssetTypeHierarchyDto
export interface BulkUpdateAssetTypeHierarchyDto {
  updates: Array<{
    id: string;
    parentId: string | null;
  }>;
}

// Backend DTO: BulkUpdateAssetTypeHierarchyResponseDto
export interface BulkUpdateAssetTypeHierarchyResponseDto {
  updated: number;
  failed: Array<{
    id: string;
    error: string;
  }>;
}

// Backend DTO: BulkUpdateAssetTypeStatusDto
export interface BulkUpdateAssetTypeStatusDto {
  assetTypeIds: string[];
  status: AssetTypeStatus;
}

// Backend DTO: BulkUpdateAssetTypeStatusResponseDto
export interface BulkUpdateAssetTypeStatusResponseDto {
  updated: number;
  message: string;
  updatedIds: string[];
  failed?: Array<{
    assetTypeId: string;
    error: string;
  }>;
}

// Backend DTO: UpdateAssetTypePositionsDto
export interface UpdateAssetTypePositionsDto {
  updates: Array<{
    id: string;
    position: number;
  }>;
}

// Backend DTO: UpdateAssetTypePositionsResponseDto
export interface UpdateAssetTypePositionsResponseDto {
  updated: number;
  message: string;
}

// API Response wrappers
export interface AssetTypeResponse extends ApiResponse<AssetTypeDto> {}

export interface AssetTypeListResponse
  extends ApiResponse<AssetTypeListDto[]> {}

export interface AssetTypePaginatedResponse
  extends ApiResponse<PaginatedAssetTypesResponseDto | null> {}

export interface SimpleAssetTypeResponse
  extends ApiResponse<AssetTypeSlimDto[]> {}

export interface AssetTypeNameAvailabilityResponse
  extends ApiResponse<AssetTypeNameAvailabilityResponseDto> {}

export interface AssetTypeIdResponse
  extends ApiResponse<AssetTypeIdResponseDto> {}

export interface BulkAssetTypeIdsResponse
  extends ApiResponse<BulkAssetTypeIdsResponseDto> {}

export interface BulkDeleteAssetTypeResponse
  extends ApiResponse<BulkDeleteAssetTypeResponseDto> {}

export interface BulkUpdateAssetTypeHierarchyResponse
  extends ApiResponse<BulkUpdateAssetTypeHierarchyResponseDto> {}

export interface BulkUpdateAssetTypeStatusResponse
  extends ApiResponse<BulkUpdateAssetTypeStatusResponseDto> {}

export interface UpdateAssetTypePositionsResponse
  extends ApiResponse<UpdateAssetTypePositionsResponseDto> {}

// Table data interface - optimized for table display
export interface AssetTypeTableData {
  id: string;
  name: string;
  description?: string;
  status: AssetTypeStatus;
  category: AssetTypeCategory;
  parentId?: string;
  parentName?: string;
  subcategoriesCount: number;
  assetsCount: number;
  referenceId?: string;
  referenceType?: AssetTypeReferenceType;
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type AssetType = AssetTypeDto;
export type SimpleAssetTypeData = AssetTypeSlimDto;
export type AssetTypePaginatedData = PaginatedAssetTypesResponseDto;

// Form values interface for UI components
export interface AssetTypeFormValues {
  name: string;
  description?: string;
  parentId?: string;
  category: AssetTypeCategory;
  referenceId?: string;
  referenceType?: AssetTypeReferenceType;
  status?: AssetTypeStatus;
}

// Extended interfaces for frontend use
export interface AssetTypeTableDataExtended extends AssetTypeTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
  numAssets?: number; // Legacy field - use assetsCount instead
  parent?: AssetTypeSlimDto; // Parent asset type details
  subcategories?: AssetTypeSlimDto[]; // Subcategory details
}

// Bulk create asset type interface
export interface BulkCreateAssetTypeDto extends CreateAssetTypeDto {
  // No additional fields needed for asset types currently
}

// Hierarchy data interface - optimized for hierarchy display
export interface AssetTypeHierarchyData {
  id: string;
  name: string;
  parentId?: string;
}

// API Response wrapper for hierarchy data
export interface AssetTypeHierarchyResponse
  extends ApiResponse<AssetTypeHierarchyData[]> {}