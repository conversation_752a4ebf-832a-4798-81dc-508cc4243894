import { ApiResponse } from "./common";

export enum ServiceCategoryStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

// Backend DTO: ServiceCategoryDto
export interface ServiceCategoryDto {
  id: string;
  businessId: string;
  name: string;
  shortCode?: string;
  description?: string;
  slug?: string;
  parentId?: string;
  availableOnline: boolean;
  position: number;
  color?: string;
  status: ServiceCategoryStatus;
  servicesCount: number;
  // Location fields
  isAllocatedToAllLocations: boolean;
  locations: { id: string; name: string }[]; // Empty array when allocated to all locations
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  ogImage?: string;
  image?: string;
  parent?: ServiceCategorySlimDto;
  subServiceCategories?: ServiceCategorySlimDto[];
  createdBy: string;
  updatedBy?: string;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateServiceCategoryDto
export interface CreateServiceCategoryDto {
  name: string;
  shortCode?: string;
  parentId?: string;
  description?: string;
  slug?: string;
  availableOnline?: boolean;
  color?: string;
  status?: ServiceCategoryStatus;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Backend DTO: UpdateServiceCategoryDto
export interface UpdateServiceCategoryDto {
  name?: string;
  shortCode?: string;
  parentId?: string;
  description?: string;
  slug?: string;
  availableOnline?: boolean;
  color?: string;
  status?: ServiceCategoryStatus;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Backend DTO: ServiceCategorySlimDto
export interface ServiceCategorySlimDto {
  id: string;
  name: string;
  position: number;
  parentId?: string;
  media?: {
    id: string;
    publicUrl: string;
    originalName: string;
  };
  subServiceCategories?: ServiceCategorySlimDto[];
}

// Enhanced slim DTO for hierarchical display in service forms
export interface ServiceCategorySlimWithNested extends ServiceCategorySlimDto {
  subServiceCategories: ServiceCategorySlimWithNested[];
}

// Response type for hierarchical service categories
export interface ServiceCategoryHierarchicalResponse
  extends ApiResponse<ServiceCategorySlimWithNested[]> {}

// Backend DTO: PaginatedServiceCategoriesResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedServiceCategoriesResponseDto {
  data: ServiceCategoryTableData[];
  meta: PaginationMetaDto;
}

// Backend DTO: DeleteServiceCategoryResponseDto
export interface DeleteServiceCategoryResponseDto {
  message: string;
}

// Backend DTO: ServiceCategoryNameAvailabilityResponseDto
export interface ServiceCategoryNameAvailabilityResponseDto {
  available: boolean;
  message: string;
}

// Backend DTO: ServiceCategoryIdResponseDto
export interface ServiceCategoryIdResponseDto {
  id: string;
}

// Backend DTO: BulkServiceCategoryIdsResponseDto
export interface BulkServiceCategoryIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteServiceCategoryDto
export interface BulkDeleteServiceCategoryDto {
  ids: string[];
}

// Backend DTO: BulkDeleteServiceCategoryResponseDto
export interface BulkDeleteServiceCategoryResponseDto {
  message: string;
  deletedCount: number;
}

// API Response wrappers
export interface ServiceCategoryResponse extends ApiResponse<ServiceCategoryDto> {}

export interface ServiceCategoryTableResponse
  extends ApiResponse<ServiceCategoryTableData[]> {}

export interface ServiceCategoryPaginatedResponse
  extends ApiResponse<PaginatedServiceCategoriesResponseDto | null> {}

export interface SimpleServiceCategoryResponse
  extends ApiResponse<ServiceCategorySlimDto[]> {}

export interface ServiceCategoryNameAvailabilityResponse
  extends ApiResponse<ServiceCategoryNameAvailabilityResponseDto> {}

export interface ServiceCategorySlugAvailabilityResponse
  extends ApiResponse<ServiceCategoryNameAvailabilityResponseDto> {}

export interface ServiceCategoryShortCodeAvailabilityResponse
  extends ApiResponse<ServiceCategoryNameAvailabilityResponseDto> {}

export interface ServiceCategoryIdResponse
  extends ApiResponse<ServiceCategoryIdResponseDto> {}

export interface BulkServiceCategoryIdsResponse
  extends ApiResponse<BulkServiceCategoryIdsResponseDto> {}

export interface BulkDeleteServiceCategoryResponse
  extends ApiResponse<BulkDeleteServiceCategoryResponseDto> {}

// Table data interface - optimized for table display (based on ServiceCategoryListDto)
export interface ServiceCategoryTableData {
  id: string;
  name: string;
  shortCode?: string;
  slug?: string;
  status: ServiceCategoryStatus;
  availableOnline: boolean;
  position: number;
  color?: string;
  description?: string;
  parentId?: string;
  parentName?: string;
  subServiceCategoriesCount: number;
  servicesCount: number;
  image?: string;
  // Location fields
  isAllocatedToAllLocations: boolean;
  locationIds: string[]; // Legacy field - will be removed after migration
  locations: { id: string; name: string }[]; // Empty array when allocated to all locations
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  ogImage?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type ServiceCategory = ServiceCategoryDto;
export type SimpleServiceCategoryData = ServiceCategorySlimDto;
export type ServiceCategoryPaginatedData = PaginatedServiceCategoriesResponseDto;

// Form values interface for UI components
export interface ServiceCategoryFormValues {
  name: string;
  shortCode?: string;
  parentId?: string;
  description?: string;
  slug?: string;
  availableOnline?: boolean;
  color?: string;
  status?: ServiceCategoryStatus;
  // Location fields
  isAllocatedToAllLocations?: boolean;
  locationIds?: string[]; // For junction table management (ignored if isAllocatedToAllLocations is true)
  // SEO fields
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

// Extended interfaces for frontend use
export interface ServiceCategoryTableDataExtended extends ServiceCategoryTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
  numServices?: number; // Legacy field - use servicesCount instead
  code?: string; // Legacy field mapping to shortCode
  customFields?: Record<string, string>[]; // Legacy field
  availableInOnlineStore: boolean; // Legacy field mapping to availableOnline
  parent?: ServiceCategorySlimDto; // Parent service category details
  subServiceCategories?: ServiceCategorySlimDto[]; // Sub service category details
}

// Bulk create service category interface
export interface BulkCreateServiceCategoryDto extends CreateServiceCategoryDto {
  imageIndex?: number; // For mapping to uploaded images
}

// Hierarchy data interface - optimized for hierarchy display
export interface ServiceCategoryHierarchyData {
  id: string;
  name: string;
  parentId?: string;
  position: number;
}

// API Response wrapper for hierarchy data
export interface ServiceCategoryHierarchyResponse
  extends ApiResponse<ServiceCategoryHierarchyData[]> {}
