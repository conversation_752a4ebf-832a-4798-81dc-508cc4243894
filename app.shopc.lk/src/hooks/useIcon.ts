import { useState, useEffect } from 'react';
import type { IconDefinition, IconName } from '@/lib/icons/types';
import { iconRegistry } from '@/lib/icons/registry';

interface UseIconReturn {
  icon: IconDefinition | null;
  loading: boolean;
  error: Error | null;
}

export const useIcon = (name: IconName): UseIconReturn => {
  const [icon, setIcon] = useState<IconDefinition | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadIcon = async () => {
      try {
        setLoading(true);
        setError(null);
        
        await new Promise(resolve => setTimeout(resolve, 50));
        
        if (iconRegistry[name]) {
          setIcon(iconRegistry[name]);
        } else {
          throw new Error(`Icon "${name}" not found`);
        }
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    loadIcon();
  }, [name]);

  return { icon, loading, error };
};