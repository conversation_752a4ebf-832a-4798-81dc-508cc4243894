"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Calendar,
  CheckSquare,
  Users,
  ClipboardList,
  Factory,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { workOrderFormSchema } from "@/lib/work-orders/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  WorkOrderTableData,
  UpdateWorkOrderDto,
  CreateWorkOrderDto,
} from "@/types/work-order";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useWorkOrderData,
  useWorkOrderNumberAvailability,
  useCreateWorkOrder,
  useUpdateWorkOrder,
} from "@/lib/work-orders/hooks";
import { useWorkOrderStatusesSlim } from "@/lib/work-order-statuses/hooks";
import { useWorkOrderPrioritiesSlim } from "@/lib/work-order-priorities/hooks";
import { useProductsSlim } from "@/lib/products/hooks";
import { useBillOfMaterialsSlim } from "@/lib/bill-of-materials/hooks";
import { useStaffMembersSlim } from "@/lib/staff/hooks";
import { StaffAssignmentInput } from "./staff-assignment-input";
// import { useSalesOrdersSlim } from "@/lib/sales-orders/hooks";

interface WorkOrderSheetProps {
  workOrder: WorkOrderTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (workOrder?: WorkOrderTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof workOrderFormSchema>;

export function WorkOrderSheet({
  workOrder,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: WorkOrderSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [dateValidationErrors, setDateValidationErrors] = React.useState<
    { field: string; message: string }[]
  >([]);

  // Values for availability checking
  const [checkWorkOrderNumber, setCheckWorkOrderNumber] =
    React.useState<string>("");

  // Fetch complete work order data if updating
  const { data: fullWorkOrderResponse, isLoading: isLoadingWorkOrder } =
    useWorkOrderData(workOrder?.id || "", isDemo);
  const fullWorkOrder = fullWorkOrderResponse?.data;

  // Fetch data for dropdowns
  const { data: statusesResponse } = useWorkOrderStatusesSlim(isDemo);
  const statuses = React.useMemo(
    () => statusesResponse?.data || [],
    [statusesResponse?.data]
  );

  const { data: prioritiesResponse } = useWorkOrderPrioritiesSlim(isDemo);
  const priorities = React.useMemo(
    () => prioritiesResponse?.data || [],
    [prioritiesResponse?.data]
  );

  const { data: productsResponse } = useProductsSlim(isDemo);
  const products = React.useMemo(
    () => productsResponse?.data || [],
    [productsResponse?.data]
  );

  const { data: bomsResponse } = useBillOfMaterialsSlim(isDemo);
  const boms = React.useMemo(
    () => bomsResponse?.data || [],
    [bomsResponse?.data]
  );

  const { data: staffResponse } = useStaffMembersSlim(isDemo);
  const staff = React.useMemo(
    () => staffResponse?.data || [],
    [staffResponse?.data]
  );
  // const salesOrders: any[] = []; // Placeholder

  // Mutation hooks for create and update operations
  const createWorkOrderMutation = useCreateWorkOrder(isDemo);
  const updateWorkOrderMutation = useUpdateWorkOrder(isDemo);

  // Availability checks (only check if not updating the same work order)
  const shouldCheckWorkOrderNumberAvailability =
    checkWorkOrderNumber.length > 0 &&
    (!isUpdate ||
      (workOrder &&
        checkWorkOrderNumber.toLowerCase() !==
          workOrder.workOrderNumber.toLowerCase()));

  const {
    data: workOrderNumberAvailabilityResponse,
    isLoading: isCheckingWorkOrderNumber,
  } = useWorkOrderNumberAvailability(checkWorkOrderNumber, isDemo);

  const isWorkOrderNumberAvailable = shouldCheckWorkOrderNumberAvailability
    ? workOrderNumberAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(workOrderFormSchema),
    defaultValues: {
      workOrderNumber: "",
      productId: "",
      variantId: "",
      bomId: "",
      quantityToProduce: "",
      quantityProduced: "",
      quantityScrapped: "",
      statusId: "",
      priorityId: "",
      plannedStartDate: "",
      plannedEndDate: "",
      actualStartDate: "",
      actualEndDate: "",
      notes: "",
      qualityInspectionRequired: false,
      qualityInspectionCompleted: false,
      qualityInspectionResult: "",
      qualityInspectionNotes: "",
      inspectedBy: "",
      salesOrderIds: [],
      staffIds: [],
      staffAssignments: [],
      createTask: false,
      taskTitle: "",
      taskDescription: "",
      taskDueDate: "",
      taskPriority: "",
      taskAssignedTo: "",
    },
  });

  React.useEffect(() => {
    if (workOrder && fullWorkOrder) {
      // Use full work order data for populating the form
      form.reset({
        workOrderNumber: fullWorkOrder.workOrderNumber || "",
        productId: fullWorkOrder.productId || "",
        variantId: fullWorkOrder.variantId || "",
        bomId: fullWorkOrder.bomId || "",
        quantityToProduce: fullWorkOrder.quantityToProduce || "",
        quantityProduced: fullWorkOrder.quantityProduced || "",
        quantityScrapped: fullWorkOrder.quantityScrapped || "",
        statusId: fullWorkOrder.statusId || "",
        priorityId: fullWorkOrder.priorityId || "",
        plannedStartDate: fullWorkOrder.plannedStartDate || "",
        plannedEndDate: fullWorkOrder.plannedEndDate || "",
        actualStartDate: fullWorkOrder.actualStartDate || "",
        actualEndDate: fullWorkOrder.actualEndDate || "",
        notes: fullWorkOrder.notes || "",
        qualityInspectionRequired:
          fullWorkOrder.qualityInspectionRequired || false,
        qualityInspectionCompleted:
          fullWorkOrder.qualityInspectionCompleted || false,
        qualityInspectionResult: fullWorkOrder.qualityInspectionResult || "",
        qualityInspectionNotes: fullWorkOrder.qualityInspectionNotes || "",
        inspectedBy: fullWorkOrder.inspectedBy || "",
        salesOrderIds: fullWorkOrder.salesOrders?.map((so) => so.id) || [],
        staffIds: fullWorkOrder.staff?.map((s) => s.id) || [],
        staffAssignments: fullWorkOrder.staff?.map((s) => ({
          staffId: s.id,
          createTask: !!s.taskId,
          taskTitle: fullWorkOrder.task?.title || "",
          taskDescription: fullWorkOrder.task?.description || "",
          taskDueDate: fullWorkOrder.task?.dueDate || "",
          taskPriority: fullWorkOrder.task?.priority || "",
        })) || [],
        createTask: false,
        taskTitle: fullWorkOrder.task?.title || "",
        taskDescription: fullWorkOrder.task?.title || "", // TODO: Fix when task has description field
        taskDueDate: fullWorkOrder.task?.dueDate || "",
        taskPriority: fullWorkOrder.task?.priority || "",
        taskAssignedTo: fullWorkOrder.task?.assignedTo || "",
      });
    } else if (!workOrder) {
      // Reset form for new work order
      form.reset({
        workOrderNumber: "",
        productId: "",
        variantId: "",
        bomId: "",
        quantityToProduce: "",
        quantityProduced: "",
        quantityScrapped: "",
        statusId: "",
        priorityId: "",
        plannedStartDate: "",
        plannedEndDate: "",
        actualStartDate: "",
        actualEndDate: "",
        notes: "",
        qualityInspectionRequired: false,
        qualityInspectionCompleted: false,
        qualityInspectionResult: "",
        qualityInspectionNotes: "",
        inspectedBy: "",
        salesOrderIds: [],
        staffIds: [],
        staffAssignments: [],
        createTask: false,
        taskTitle: "",
        taskDescription: "",
        taskDueDate: "",
        taskPriority: "",
        taskAssignedTo: "",
      });
    }
  }, [workOrder, fullWorkOrder]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setDateValidationErrors([]);
    }
  }, [open]);

  // Clear date validation errors when relevant fields change
  React.useEffect(() => {
    const subscription = form.watch((_, { name }) => {
      if (
        name &&
        [
          "plannedStartDate",
          "plannedEndDate",
          "actualStartDate",
          "actualEndDate",
          "taskDueDate",
        ].includes(name)
      ) {
        // Clear validation errors for the changed field
        setDateValidationErrors((prev) =>
          prev.filter((error) => error.field !== name)
        );
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const {
    formState: { errors },
  } = form;

  // Helper function to get validation error for a specific field
  const getDateValidationError = (fieldName: string) => {
    return dateValidationErrors.find((error) => error.field === fieldName)
      ?.message;
  };

  // Helper function to check if a field has validation errors
  const hasDateValidationError = (fieldName: string) => {
    return dateValidationErrors.some((error) => error.field === fieldName);
  };

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.workOrderNumber ||
    errors.productId ||
    errors.bomId ||
    errors.quantityToProduce ||
    errors.statusId ||
    errors.priorityId ||
    (shouldCheckWorkOrderNumberAvailability &&
      isWorkOrderNumberAvailable === false)
  );

  const hasProductionInfoErrors = !!(
    errors.quantityProduced ||
    errors.quantityScrapped ||
    errors.variantId
  );

  const hasSchedulingErrors = !!(
    errors.plannedStartDate ||
    errors.plannedEndDate ||
    errors.actualStartDate ||
    errors.actualEndDate ||
    hasDateValidationError("plannedStartDate") ||
    hasDateValidationError("plannedEndDate") ||
    hasDateValidationError("actualStartDate") ||
    hasDateValidationError("actualEndDate")
  );

  const hasQualityInspectionErrors = !!(
    errors.qualityInspectionResult ||
    errors.qualityInspectionNotes ||
    errors.inspectedBy
  );

  const hasAssignmentErrors = !!(
    errors.salesOrderIds || 
    errors.staffIds || 
    errors.staffAssignments
  );


  const hasDetailsErrors = !!errors.notes;

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (
      shouldCheckWorkOrderNumberAvailability &&
      isWorkOrderNumberAvailable === false
    ) {
      toast.error(
        "Work order number is already taken. Please choose a different number."
      );
      return;
    }

    // Additional date validations with user-friendly messages
    const dateValidations = validateDates(data);
    if (dateValidations.length > 0) {
      setDateValidationErrors(dateValidations);
      // Show the first validation error as toast for immediate feedback
      toast.error(dateValidations[0].message);
      return;
    }

    // Clear any previous date validation errors
    setDateValidationErrors([]);

    setIsSubmitting(true);

    try {
      if (workOrder) {
        // Update existing work order
        const updateData: UpdateWorkOrderDto = {
          workOrderNumber: data.workOrderNumber,
          productId: data.productId,
          variantId: data.variantId || undefined,
          bomId: data.bomId,
          quantityToProduce: data.quantityToProduce,
          quantityProduced: data.quantityProduced || undefined,
          quantityScrapped: data.quantityScrapped || undefined,
          statusId: data.statusId,
          priorityId: data.priorityId,
          plannedStartDate: data.plannedStartDate || undefined,
          plannedEndDate: data.plannedEndDate || undefined,
          actualStartDate: data.actualStartDate || undefined,
          actualEndDate: data.actualEndDate || undefined,
          notes: data.notes || undefined,
          qualityInspectionRequired: data.qualityInspectionRequired,
          qualityInspectionCompleted: data.qualityInspectionCompleted,
          qualityInspectionResult: data.qualityInspectionResult || undefined,
          qualityInspectionNotes: data.qualityInspectionNotes || undefined,
          inspectedBy: data.inspectedBy || undefined,
          salesOrderIds: data.salesOrderIds || [],
          staffIds: data.staffIds || [],
          staffAssignments: data.staffAssignments || [],
          taskTitle: data.taskTitle || undefined,
          taskDescription: data.taskDescription || undefined,
          taskDueDate: data.taskDueDate || undefined,
          taskPriority: data.taskPriority || undefined,
          taskAssignedTo: data.taskAssignedTo || undefined,
        };

        const response = await updateWorkOrderMutation.mutateAsync({
          id: workOrder.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Work order updated successfully");
          onOpenChange?.(false);
          onSuccess?.();
          return;
        }
        toast.error(response.message || "Failed to update work order");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new work order
        const createData: CreateWorkOrderDto = {
          workOrderNumber: data.workOrderNumber,
          productId: data.productId,
          variantId: data.variantId || undefined,
          bomId: data.bomId,
          quantityToProduce: data.quantityToProduce,
          quantityProduced: data.quantityProduced || undefined,
          quantityScrapped: data.quantityScrapped || undefined,
          statusId: data.statusId,
          priorityId: data.priorityId,
          plannedStartDate: data.plannedStartDate || undefined,
          plannedEndDate: data.plannedEndDate || undefined,
          actualStartDate: data.actualStartDate || undefined,
          actualEndDate: data.actualEndDate || undefined,
          notes: data.notes || undefined,
          qualityInspectionRequired: data.qualityInspectionRequired,
          qualityInspectionCompleted: data.qualityInspectionCompleted,
          qualityInspectionResult: data.qualityInspectionResult || undefined,
          qualityInspectionNotes: data.qualityInspectionNotes || undefined,
          inspectedBy: data.inspectedBy || undefined,
          salesOrderIds: data.salesOrderIds || [],
          staffIds: data.staffIds || [],
          staffAssignments: data.staffAssignments || [],
          createTask: data.createTask,
          taskTitle: data.taskTitle || undefined,
          taskDescription: data.taskDescription || undefined,
          taskDueDate: data.taskDueDate || undefined,
          taskPriority: data.taskPriority || undefined,
          taskAssignedTo: data.taskAssignedTo || undefined,
        };

        const response = await createWorkOrderMutation.mutateAsync(createData);

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Work order created successfully");
          onOpenChange?.(false);
          onSuccess?.();
          return;
        }
        toast.error(response.message || "Failed to create work order");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save work order:", error);
      toast.error("Failed to save work order");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle availability checks
  const handleCheckWorkOrderNumberAvailability = (value: string) => {
    setCheckWorkOrderNumber(value);
  };

  // Enhanced date validation function
  const validateDates = (data: FormData) => {
    const validations: { field: string; message: string }[] = [];
    const now = new Date();

    // Parse dates
    const plannedStart = data.plannedStartDate
      ? new Date(data.plannedStartDate)
      : null;
    const plannedEnd = data.plannedEndDate
      ? new Date(data.plannedEndDate)
      : null;
    const actualStart = data.actualStartDate
      ? new Date(data.actualStartDate)
      : null;
    const actualEnd = data.actualEndDate ? new Date(data.actualEndDate) : null;
    const taskDue = data.taskDueDate ? new Date(data.taskDueDate) : null;

    // Validate date formats
    if (
      data.plannedStartDate &&
      (plannedStart === null || isNaN(plannedStart.getTime()))
    ) {
      validations.push({
        field: "plannedStartDate",
        message: "Invalid planned start date format",
      });
    }
    if (
      data.plannedEndDate &&
      (plannedEnd === null || isNaN(plannedEnd.getTime()))
    ) {
      validations.push({
        field: "plannedEndDate",
        message: "Invalid planned end date format",
      });
    }
    if (
      data.actualStartDate &&
      (actualStart === null || isNaN(actualStart.getTime()))
    ) {
      validations.push({
        field: "actualStartDate",
        message: "Invalid actual start date format",
      });
    }
    if (
      data.actualEndDate &&
      (actualEnd === null || isNaN(actualEnd.getTime()))
    ) {
      validations.push({
        field: "actualEndDate",
        message: "Invalid actual end date format",
      });
    }
    if (data.taskDueDate && (taskDue === null || isNaN(taskDue.getTime()))) {
      validations.push({
        field: "taskDueDate",
        message: "Invalid task due date format",
      });
    }

    // Validate planned dates logic
    if (plannedStart && plannedEnd && plannedEnd <= plannedStart) {
      validations.push({
        field: "plannedEndDate",
        message: "Planned end date must be after planned start date",
      });
    }

    // Validate actual dates logic
    if (actualStart && actualEnd && actualEnd <= actualStart) {
      validations.push({
        field: "actualEndDate",
        message: "Actual end date must be after actual start date",
      });
    }

    // Validate planned vs actual dates
    if (plannedStart && actualStart) {
      const threeDaysEarlier = new Date(
        plannedStart.getTime() - 3 * 24 * 60 * 60 * 1000
      );
      if (actualStart < threeDaysEarlier) {
        validations.push({
          field: "actualStartDate",
          message:
            "Actual start date is significantly earlier than planned start date. Please verify.",
        });
      }
    }

    if (plannedEnd && actualEnd) {
      const oneWeekLater = new Date(
        plannedEnd.getTime() + 7 * 24 * 60 * 60 * 1000
      );
      if (actualEnd > oneWeekLater) {
        validations.push({
          field: "actualEndDate",
          message:
            "Actual end date is significantly later than planned end date. Please verify.",
        });
      }
    }

    // Validate task due date
    if (data.createTask && taskDue) {
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      if (taskDue < oneDayAgo) {
        validations.push({
          field: "taskDueDate",
          message: "Task due date cannot be more than 1 day in the past",
        });
      }

      // Warn if task due date is too far in the future (more than 1 year)
      const oneYearLater = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
      if (taskDue > oneYearLater) {
        validations.push({
          field: "taskDueDate",
          message:
            "Task due date is more than 1 year in the future. Please verify.",
        });
      }
    }

    // Validate that actual dates are not too far in the future
    if (actualStart) {
      const oneDayLater = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      if (actualStart > oneDayLater) {
        validations.push({
          field: "actualStartDate",
          message: "Actual start date cannot be more than 1 day in the future",
        });
      }
    }

    if (actualEnd) {
      const oneDayLater = new Date(now.getTime() + 24 * 60 * 60 * 1000);
      if (actualEnd > oneDayLater) {
        validations.push({
          field: "actualEndDate",
          message: "Actual end date cannot be more than 1 day in the future",
        });
      }
    }

    return validations;
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="workOrderNumber"
                label="Work Order Number"
                placeholder="Enter work order number (e.g., WO-2024-001)"
                value={form.watch("workOrderNumber") || ""}
                onChange={(value) =>
                  form.setValue("workOrderNumber", value, {
                    shouldValidate: true,
                  })
                }
                onCheckAvailability={handleCheckWorkOrderNumberAvailability}
                isCheckingAvailability={isCheckingWorkOrderNumber}
                isAvailable={isWorkOrderNumberAvailable}
                shouldCheck={shouldCheckWorkOrderNumberAvailability}
                error={errors.workOrderNumber?.message}
                disabled={isSubmitting}
                fieldName="Work order number"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Product *</Label>
                  <Select
                    value={form.watch("productId")}
                    onValueChange={(value) =>
                      form.setValue("productId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.productId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select product" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.productId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.productId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Bill of Materials *</Label>
                  <Select
                    value={form.watch("bomId")}
                    onValueChange={(value) =>
                      form.setValue("bomId", value, { shouldValidate: true })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.bomId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select BOM" />
                    </SelectTrigger>
                    <SelectContent>
                      {boms.map((bom) => (
                        <SelectItem key={bom.id} value={bom.id}>
                          {bom.bomName || bom.id}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.bomId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.bomId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Quantity to Produce *</Label>
                  <Input
                    {...form.register("quantityToProduce")}
                    name="quantityToProduce"
                    type="number"
                    placeholder="Enter quantity to produce"
                    className={cn(
                      errors.quantityToProduce && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.quantityToProduce && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.quantityToProduce.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Product Variant</Label>
                  <Input
                    {...form.register("variantId")}
                    name="variantId"
                    placeholder="Enter variant ID (optional)"
                    className={cn(errors.variantId && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.variantId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.variantId.message}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Optional variant specification
                  </p>
                </div>

                <div>
                  <Label>Status *</Label>
                  <Select
                    value={form.watch("statusId")}
                    onValueChange={(value) =>
                      form.setValue("statusId", value, { shouldValidate: true })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.statusId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {statuses.map((status) => (
                        <SelectItem key={status.id} value={status.id}>
                          {status.statusName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.statusId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.statusId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Priority *</Label>
                  <Select
                    value={form.watch("priorityId")}
                    onValueChange={(value) =>
                      form.setValue("priorityId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.priorityId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      {priorities.map((priority) => (
                        <SelectItem key={priority.id} value={priority.id}>
                          {priority.priorityName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.priorityId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.priorityId.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "production-info",
      title: "Production Information",
      icon: <Factory className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasProductionInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Factory className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Production Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Quantity Produced</Label>
                  <Input
                    {...form.register("quantityProduced")}
                    name="quantityProduced"
                    type="number"
                    placeholder="Enter quantity produced"
                    className={cn(
                      errors.quantityProduced && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.quantityProduced && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.quantityProduced.message}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Actual quantity produced so far
                  </p>
                </div>

                <div>
                  <Label>Quantity Scrapped</Label>
                  <Input
                    {...form.register("quantityScrapped")}
                    name="quantityScrapped"
                    type="number"
                    placeholder="Enter quantity scrapped"
                    className={cn(
                      errors.quantityScrapped && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.quantityScrapped && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.quantityScrapped.message}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Quantity that was scrapped or defective
                  </p>
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "scheduling",
      title: "Scheduling",
      icon: <Calendar className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasSchedulingErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Scheduling</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Planned Start Date</Label>
                  <DateTimePicker
                    name="plannedStartDate"
                    value={form.watch("plannedStartDate")}
                    onChange={(date) =>
                      form.setValue(
                        "plannedStartDate",
                        date ? date.toISOString() : "",
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                    placeholder="Select planned start date and time"
                    disabled={isSubmitting}
                    className={cn(
                      (errors.plannedStartDate ||
                        hasDateValidationError("plannedStartDate")) &&
                        "border-destructive"
                    )}
                  />
                  {errors.plannedStartDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.plannedStartDate.message}
                    </p>
                  )}
                  {getDateValidationError("plannedStartDate") && (
                    <p className="text-sm text-destructive mt-1">
                      {getDateValidationError("plannedStartDate")}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Planned End Date</Label>
                  <DateTimePicker
                    name="plannedEndDate"
                    value={form.watch("plannedEndDate")}
                    onChange={(date) =>
                      form.setValue(
                        "plannedEndDate",
                        date ? date.toISOString() : "",
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                    placeholder="Select planned end date and time"
                    disabled={isSubmitting}
                    className={cn(
                      (errors.plannedEndDate ||
                        hasDateValidationError("plannedEndDate")) &&
                        "border-destructive"
                    )}
                  />
                  {errors.plannedEndDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.plannedEndDate.message}
                    </p>
                  )}
                  {getDateValidationError("plannedEndDate") && (
                    <p className="text-sm text-destructive mt-1">
                      {getDateValidationError("plannedEndDate")}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Actual Start Date</Label>
                  <DateTimePicker
                    name="actualStartDate"
                    value={form.watch("actualStartDate")}
                    onChange={(date) =>
                      form.setValue(
                        "actualStartDate",
                        date ? date.toISOString() : "",
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                    placeholder="Select actual start date and time"
                    disabled={isSubmitting}
                    className={cn(
                      (errors.actualStartDate ||
                        hasDateValidationError("actualStartDate")) &&
                        "border-destructive"
                    )}
                  />
                  {errors.actualStartDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.actualStartDate.message}
                    </p>
                  )}
                  {getDateValidationError("actualStartDate") && (
                    <p className="text-sm text-destructive mt-1">
                      {getDateValidationError("actualStartDate")}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Actual End Date</Label>
                  <DateTimePicker
                    name="actualEndDate"
                    value={form.watch("actualEndDate")}
                    onChange={(date) =>
                      form.setValue(
                        "actualEndDate",
                        date ? date.toISOString() : "",
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                    placeholder="Select actual end date and time"
                    disabled={isSubmitting}
                    className={cn(
                      (errors.actualEndDate ||
                        hasDateValidationError("actualEndDate")) &&
                        "border-destructive"
                    )}
                  />
                  {errors.actualEndDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.actualEndDate.message}
                    </p>
                  )}
                  {getDateValidationError("actualEndDate") && (
                    <p className="text-sm text-destructive mt-1">
                      {getDateValidationError("actualEndDate")}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "quality-inspection",
      title: "Quality Inspection",
      icon: <CheckSquare className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasQualityInspectionErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Quality Inspection</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Quality Inspection Required</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable quality inspection for this work order
                  </p>
                </div>
                <Switch
                  checked={form.watch("qualityInspectionRequired")}
                  onCheckedChange={(checked) =>
                    form.setValue("qualityInspectionRequired", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>

              {form.watch("qualityInspectionRequired") && (
                <>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Quality Inspection Completed</Label>
                      <p className="text-sm text-muted-foreground">
                        Mark as completed when inspection is done
                      </p>
                    </div>
                    <Switch
                      checked={form.watch("qualityInspectionCompleted")}
                      onCheckedChange={(checked) =>
                        form.setValue("qualityInspectionCompleted", checked, {
                          shouldValidate: true,
                        })
                      }
                      disabled={isSubmitting}
                    />
                  </div>

                  <div>
                    <Label>Inspection Result</Label>
                    <Select
                      value={form.watch("qualityInspectionResult") || ""}
                      onValueChange={(value) =>
                        form.setValue("qualityInspectionResult", value, {
                          shouldValidate: true,
                        })
                      }
                      disabled={isSubmitting}
                    >
                      <SelectTrigger
                        className={cn(
                          errors.qualityInspectionResult && "border-destructive"
                        )}
                      >
                        <SelectValue placeholder="Select inspection result" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PASS">Passed</SelectItem>
                        <SelectItem value="FAIL">Failed</SelectItem>
                        <SelectItem value="REWORK">Rework Required</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.qualityInspectionResult && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.qualityInspectionResult.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label>Inspection Notes</Label>
                    <Textarea
                      {...form.register("qualityInspectionNotes")}
                      name="qualityInspectionNotes"
                      placeholder="Enter inspection notes"
                      className={cn(
                        errors.qualityInspectionNotes && "border-destructive"
                      )}
                      disabled={isSubmitting}
                      rows={3}
                    />
                    {errors.qualityInspectionNotes && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.qualityInspectionNotes.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label>Inspected By</Label>
                    <Select
                      value={form.watch("inspectedBy") || ""}
                      onValueChange={(value) =>
                        form.setValue("inspectedBy", value, {
                          shouldValidate: true,
                        })
                      }
                      disabled={isSubmitting}
                    >
                      <SelectTrigger
                        className={cn(
                          errors.inspectedBy && "border-destructive"
                        )}
                      >
                        <SelectValue placeholder="Select inspector" />
                      </SelectTrigger>
                      <SelectContent>
                        {staff.map((staffMember: any, index: number) => (
                          <SelectItem key={index} value={staffMember.id}>
                            {staffMember.firstName} {staffMember.lastName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.inspectedBy && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.inspectedBy.message}
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "assignments",
      title: "Assignments",
      icon: <Users className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasAssignmentErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Assignments</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-6">
              <div>
                <Label>Sales Orders</Label>
                <p className="text-sm text-muted-foreground mb-2">
                  Link this work order to related sales orders
                </p>
                <p className="text-sm text-orange-600">
                  Sales order selection will be implemented in future update
                </p>
              </div>

              <StaffAssignmentInput
                assignments={form.watch("staffAssignments") || []}
                onChange={(assignments) =>
                  form.setValue("staffAssignments", assignments, {
                    shouldValidate: true,
                  })
                }
                staff={staff}
                errors={errors}
                disabled={isSubmitting}
              />
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "additional-details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Notes</Label>
                <Textarea
                  {...form.register("notes")}
                  name="notes"
                  placeholder="Add any additional notes about this work order..."
                  className={cn(errors.notes && "border-destructive")}
                  disabled={isSubmitting}
                  rows={4}
                />
                {errors.notes && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.notes.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional notes and comments about the work order
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<WorkOrderTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={workOrder}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Work Order"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingWorkOrder}
    />
  );
}
