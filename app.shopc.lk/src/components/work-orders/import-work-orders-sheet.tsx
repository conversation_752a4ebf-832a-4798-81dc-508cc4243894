/**
 * ImportWorkOrdersSheet Component
 *
 * This component allows importing work orders with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Field validation and error handling
 * - Integration with bulk import API
 * - Support for work order numbers, products, BOMs, quantities, and other attributes
 */

"use client";

import { useState } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  CreateWorkOrderSchema,
  GetWorkOrdersSchema,
} from "@/lib/work-orders/validations";
import { ApiStatus } from "@/types/common";
import {
  useWorkOrdersData,
  useWorkOrderNumberAvailability,
  useBulkCreateWorkOrders,
} from "@/lib/work-orders/hooks";

// Define the work order field types that can be imported
export type WorkOrderImportFields =
  | "workOrderNumber"
  | "productId"
  | "variantId"
  | "bomId"
  | "quantityToProduce"
  | "quantityProduced"
  | "quantityScrapped"
  | "statusId"
  | "priorityId"
  | "plannedStartDate"
  | "plannedEndDate"
  | "actualStartDate"
  | "actualEndDate"
  | "notes"
  | "qualityInspectionRequired"
  | "qualityInspectionCompleted"
  | "qualityInspectionResult"
  | "qualityInspectionNotes"
  | "inspectedBy"
  | "createTask"
  | "taskTitle"
  | "taskDescription"
  | "taskDueDate"
  | "taskPriority"
  | "taskAssignedTo";

// All possible fields for work order import
const ALL_WORK_ORDER_FIELDS: WorkOrderImportFields[] = [
  "workOrderNumber",
  "productId",
  "variantId",
  "bomId",
  "quantityToProduce",
  "quantityProduced",
  "quantityScrapped",
  "statusId",
  "priorityId",
  "plannedStartDate",
  "plannedEndDate",
  "actualStartDate",
  "actualEndDate",
  "notes",
  "qualityInspectionRequired",
  "qualityInspectionCompleted",
  "qualityInspectionResult",
  "qualityInspectionNotes",
  "inspectedBy",
  "createTask",
  "taskTitle",
  "taskDescription",
  "taskDueDate",
  "taskPriority",
  "taskAssignedTo",
];

export type ImportWorkOrdersSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportWorkOrdersSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportWorkOrdersSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook for bulk importing work orders
  const bulkImportMutation = useBulkCreateWorkOrders(isDemo);

  // Fetch full work orders data for availability checking
  const { data: fullWorkOrdersResponse } = useWorkOrdersData(
    {
      page: 1,
      limit: 1000, // Get all work orders for availability checking
    } as GetWorkOrdersSchema,
    isDemo
  );
  const fullWorkOrders = fullWorkOrdersResponse?.data?.data || [];

  // Field configurations for work orders
  const WORK_ORDER_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "workOrderNumber",
      type: "text",
      defaultValue: "",
      placeholder: "Enter work order number (e.g., WO-001)",
    },
    {
      name: "productId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter product ID (UUID format)",
    },
    {
      name: "variantId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter variant ID (UUID format, optional)",
    },
    {
      name: "bomId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter BOM ID (UUID format)",
    },
    {
      name: "quantityToProduce",
      type: "text",
      defaultValue: "",
      placeholder: "Enter quantity to produce (e.g., 100)",
    },
    {
      name: "quantityProduced",
      type: "text",
      defaultValue: "",
      placeholder: "Enter quantity produced (optional)",
    },
    {
      name: "quantityScrapped",
      type: "text",
      defaultValue: "",
      placeholder: "Enter quantity scrapped (optional)",
    },
    {
      name: "statusId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter status ID (UUID format)",
    },
    {
      name: "priorityId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter priority ID (UUID format)",
    },
    {
      name: "plannedStartDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter planned start date (YYYY-MM-DD)",
    },
    {
      name: "plannedEndDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter planned end date (YYYY-MM-DD)",
    },
    {
      name: "actualStartDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter actual start date (YYYY-MM-DD, optional)",
    },
    {
      name: "actualEndDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter actual end date (YYYY-MM-DD, optional)",
    },
    {
      name: "notes",
      type: "text",
      defaultValue: "",
      placeholder: "Enter work order notes (optional)",
    },
    {
      name: "qualityInspectionRequired",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "false",
    },
    {
      name: "qualityInspectionCompleted",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "false",
    },
    {
      name: "qualityInspectionResult",
      type: "text",
      defaultValue: "",
      placeholder: "Enter quality inspection result (optional)",
    },
    {
      name: "qualityInspectionNotes",
      type: "text",
      defaultValue: "",
      placeholder: "Enter quality inspection notes (optional)",
    },
    {
      name: "inspectedBy",
      type: "text",
      defaultValue: "",
      placeholder: "Enter inspector ID (UUID format, optional)",
    },
    {
      name: "createTask",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "false",
    },
    {
      name: "taskTitle",
      type: "text",
      defaultValue: "",
      placeholder: "Enter task title (optional)",
    },
    {
      name: "taskDescription",
      type: "text",
      defaultValue: "",
      placeholder: "Enter task description (optional)",
    },
    {
      name: "taskDueDate",
      type: "text",
      defaultValue: "",
      placeholder: "Enter task due date (YYYY-MM-DD, optional)",
    },
    {
      name: "taskPriority",
      type: "text",
      defaultValue: "",
      placeholder: "Enter task priority (optional)",
    },
    {
      name: "taskAssignedTo",
      type: "text",
      defaultValue: "",
      placeholder: "Enter assignee ID (UUID format, optional)",
    },
  ];

  // Enhanced validation function for work orders
  const validateWorkOrderRow = (row: Record<string, any>) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (
      !row.workOrderNumber ||
      typeof row.workOrderNumber !== "string" ||
      row.workOrderNumber.trim() === ""
    ) {
      errors.workOrderNumber = "Work order number is required";
      valid = false;
    } else {
      // Check for duplicates in existing data
      const duplicate = fullWorkOrders.find(
        (workOrder) =>
          workOrder.workOrderNumber.toLowerCase() ===
          row.workOrderNumber.toLowerCase()
      );
      if (duplicate) {
        errors.workOrderNumber = "Work order number already exists";
        valid = false;
      }
    }

    if (
      !row.productId ||
      typeof row.productId !== "string" ||
      row.productId.trim() === ""
    ) {
      errors.productId = "Product ID is required";
      valid = false;
    } else {
      // Validate UUID format
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(row.productId)) {
        errors.productId = "Product ID must be a valid UUID";
        valid = false;
      }
    }

    if (
      !row.bomId ||
      typeof row.bomId !== "string" ||
      row.bomId.trim() === ""
    ) {
      errors.bomId = "BOM ID is required";
      valid = false;
    } else {
      // Validate UUID format
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(row.bomId)) {
        errors.bomId = "BOM ID must be a valid UUID";
        valid = false;
      }
    }

    if (
      !row.quantityToProduce ||
      typeof row.quantityToProduce !== "string" ||
      row.quantityToProduce.trim() === ""
    ) {
      errors.quantityToProduce = "Quantity to produce is required";
      valid = false;
    }

    if (
      !row.statusId ||
      typeof row.statusId !== "string" ||
      row.statusId.trim() === ""
    ) {
      errors.statusId = "Status ID is required";
      valid = false;
    } else {
      // Validate UUID format
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(row.statusId)) {
        errors.statusId = "Status ID must be a valid UUID";
        valid = false;
      }
    }

    if (
      !row.priorityId ||
      typeof row.priorityId !== "string" ||
      row.priorityId.trim() === ""
    ) {
      errors.priorityId = "Priority ID is required";
      valid = false;
    } else {
      // Validate UUID format
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(row.priorityId)) {
        errors.priorityId = "Priority ID must be a valid UUID";
        valid = false;
      }
    }

    // Validate optional UUID fields
    const uuidFields = ["variantId", "inspectedBy", "taskAssignedTo"];
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

    uuidFields.forEach((field) => {
      if (row[field] && row[field].trim() !== "" && !uuidRegex.test(row[field])) {
        errors[field] = `${field} must be a valid UUID`;
        valid = false;
      }
    });

    // Validate date fields
    const dateFields = [
      "plannedStartDate",
      "plannedEndDate",
      "actualStartDate",
      "actualEndDate",
      "taskDueDate",
    ];
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

    dateFields.forEach((field) => {
      if (row[field] && row[field].trim() !== "" && !dateRegex.test(row[field])) {
        errors[field] = `${field} must be in YYYY-MM-DD format`;
        valid = false;
      }
    });

    // Validate boolean fields
    const booleanFields = [
      "qualityInspectionRequired",
      "qualityInspectionCompleted",
      "createTask",
    ];

    booleanFields.forEach((field) => {
      if (
        row[field] !== undefined &&
        row[field] !== "" &&
        row[field] !== null
      ) {
        if (typeof row[field] === "string") {
          const normalizedValue = row[field].toLowerCase().trim();
          if (
            normalizedValue !== "true" &&
            normalizedValue !== "false" &&
            normalizedValue !== "yes" &&
            normalizedValue !== "no" &&
            normalizedValue !== "1" &&
            normalizedValue !== "0"
          ) {
            errors[field] = "Must be true/false, yes/no, or 1/0";
            valid = false;
          }
        } else if (typeof row[field] !== "boolean") {
          errors[field] = "Must be a boolean value";
          valid = false;
        }
      }
    });

    return { valid, errors };
  };

  // Transform row data to CreateWorkOrderSchema format
  const transformRowToWorkOrder = (row: Record<string, any>): CreateWorkOrderSchema => {
    // Handle boolean fields
    const transformBoolean = (value: any, defaultValue: boolean = false) => {
      if (value === undefined || value === "" || value === null) {
        return defaultValue;
      }
      if (typeof value === "boolean") {
        return value;
      }
      if (typeof value === "string") {
        const normalizedValue = value.toLowerCase().trim();
        return (
          normalizedValue === "true" ||
          normalizedValue === "yes" ||
          normalizedValue === "1"
        );
      }
      return defaultValue;
    };

    // Build work order data
    const workOrderData: CreateWorkOrderSchema = {
      workOrderNumber: row.workOrderNumber.trim(),
      productId: row.productId.trim(),
      bomId: row.bomId.trim(),
      quantityToProduce: row.quantityToProduce.trim(),
      statusId: row.statusId.trim(),
      priorityId: row.priorityId.trim(),
    };

    // Add optional fields if present
    if (row.variantId && row.variantId.trim()) {
      workOrderData.variantId = row.variantId.trim();
    }

    if (row.quantityProduced && row.quantityProduced.trim()) {
      workOrderData.quantityProduced = row.quantityProduced.trim();
    }

    if (row.quantityScrapped && row.quantityScrapped.trim()) {
      workOrderData.quantityScrapped = row.quantityScrapped.trim();
    }

    if (row.plannedStartDate && row.plannedStartDate.trim()) {
      workOrderData.plannedStartDate = row.plannedStartDate.trim();
    }

    if (row.plannedEndDate && row.plannedEndDate.trim()) {
      workOrderData.plannedEndDate = row.plannedEndDate.trim();
    }

    if (row.actualStartDate && row.actualStartDate.trim()) {
      workOrderData.actualStartDate = row.actualStartDate.trim();
    }

    if (row.actualEndDate && row.actualEndDate.trim()) {
      workOrderData.actualEndDate = row.actualEndDate.trim();
    }

    if (row.notes && row.notes.trim()) {
      workOrderData.notes = row.notes.trim();
    }

    // Boolean fields
    workOrderData.qualityInspectionRequired = transformBoolean(
      row.qualityInspectionRequired,
      false
    );
    workOrderData.qualityInspectionCompleted = transformBoolean(
      row.qualityInspectionCompleted,
      false
    );

    if (row.qualityInspectionResult && row.qualityInspectionResult.trim()) {
      workOrderData.qualityInspectionResult = row.qualityInspectionResult.trim();
    }

    if (row.qualityInspectionNotes && row.qualityInspectionNotes.trim()) {
      workOrderData.qualityInspectionNotes = row.qualityInspectionNotes.trim();
    }

    if (row.inspectedBy && row.inspectedBy.trim()) {
      workOrderData.inspectedBy = row.inspectedBy.trim();
    }

    // Task fields
    workOrderData.createTask = transformBoolean(row.createTask, false);

    if (row.taskTitle && row.taskTitle.trim()) {
      workOrderData.taskTitle = row.taskTitle.trim();
    }

    if (row.taskDescription && row.taskDescription.trim()) {
      workOrderData.taskDescription = row.taskDescription.trim();
    }

    if (row.taskDueDate && row.taskDueDate.trim()) {
      workOrderData.taskDueDate = row.taskDueDate.trim();
    }

    if (row.taskPriority && row.taskPriority.trim()) {
      workOrderData.taskPriority = row.taskPriority.trim();
    }

    if (row.taskAssignedTo && row.taskAssignedTo.trim()) {
      workOrderData.taskAssignedTo = row.taskAssignedTo.trim();
    }

    return workOrderData;
  };

  // Handle submission of work order data
  const handleSubmitWorkOrders = async (data: any[]) => {
    try {
      setIsSubmitting(true);

      // Transform data to CreateWorkOrderSchema format
      const workOrdersData: CreateWorkOrderSchema[] = data.map(
        transformRowToWorkOrder
      );

      // Use the mutation hook to import work orders
      const result = await bulkImportMutation.mutateAsync({
        workOrders: JSON.stringify(workOrdersData),
      });

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        return Promise.resolve();
      } else {
        return Promise.reject(
          new Error(result.message || "Failed to import work orders")
        );
      }
    } catch (error) {
      console.error("Error importing work orders:", error);
      return Promise.reject(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Work Orders"
      description="Import work orders from a CSV or Excel file. Make sure your file includes the required fields: work order number, product ID, BOM ID, quantity to produce, status ID, and priority ID."
      targetFields={ALL_WORK_ORDER_FIELDS}
      fieldConfigs={WORK_ORDER_FIELD_CONFIGS}
      validateRow={validateWorkOrderRow}
      onSubmit={handleSubmitWorkOrders}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}