"use client";

import * as React from "react";
import { Plus, X, Users, CheckSquare } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StaffAssignment {
  staffId: string;
  createTask: boolean;
  taskTitle?: string;
  taskDescription?: string;
  taskDueDate?: string;
  taskPriority?: string;
}

interface StaffMember {
  id: string;
  firstName?: string;
  lastName?: string;
  name?: string;
}

interface StaffAssignmentInputProps {
  assignments: StaffAssignment[];
  onChange: (assignments: StaffAssignment[]) => void;
  staff: StaffMember[];
  errors?: any;
  disabled?: boolean;
}

export function StaffAssignmentInput({
  assignments,
  onChange,
  staff,
  errors,
  disabled = false,
}: StaffAssignmentInputProps) {
  const addAssignment = () => {
    const newAssignment: StaffAssignment = {
      staffId: "",
      createTask: false,
      taskTitle: "",
      taskDescription: "",
      taskDueDate: "",
      taskPriority: "",
    };
    onChange([...assignments, newAssignment]);
  };

  const removeAssignment = (index: number) => {
    const updated = assignments.filter((_, i) => i !== index);
    onChange(updated);
  };

  const updateAssignment = (index: number, updates: Partial<StaffAssignment>) => {
    const updated = assignments.map((assignment, i) =>
      i === index ? { ...assignment, ...updates } : assignment
    );
    onChange(updated);
  };

  const getAvailableStaff = (currentIndex: number) => {
    const assignedStaffIds = assignments
      .map((a, i) => i !== currentIndex ? a.staffId : null)
      .filter(Boolean);
    return staff.filter(s => !assignedStaffIds.includes(s.id));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Label className="text-base font-medium">Staff Assignments</Label>
          <p className="text-sm text-muted-foreground">
            Assign staff members to this work order with optional task creation
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addAssignment}
          disabled={disabled}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Staff
        </Button>
      </div>

      {assignments.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No staff assigned yet</p>
              <p className="text-sm">Click "Add Staff" to assign team members</p>
            </div>
          </CardContent>
        </Card>
      )}

      {assignments.map((assignment, index) => {
        const availableStaff = getAvailableStaff(index);
        const selectedStaff = staff.find(s => s.id === assignment.staffId);
        
        return (
          <Card key={index} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Staff Assignment #{index + 1}
                  {selectedStaff && (
                    <span className="text-muted-foreground">
                      - {selectedStaff.name || `${selectedStaff.firstName} ${selectedStaff.lastName}`}
                    </span>
                  )}
                </CardTitle>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAssignment(index)}
                  disabled={disabled}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Staff Member *</Label>
                <Select
                  value={assignment.staffId}
                  onValueChange={(value) =>
                    updateAssignment(index, { staffId: value })
                  }
                  disabled={disabled}
                >
                  <SelectTrigger
                    className={cn(
                      errors?.staffAssignments?.[index]?.staffId && "border-destructive"
                    )}
                  >
                    <SelectValue placeholder="Select staff member" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableStaff.map((staffMember) => (
                      <SelectItem key={staffMember.id} value={staffMember.id}>
                        {staffMember.name || `${staffMember.firstName} ${staffMember.lastName}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors?.staffAssignments?.[index]?.staffId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.staffAssignments[index].staffId.message}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between p-3 bg-secondary/30 rounded-lg">
                <div className="space-y-0.5">
                  <div className="flex items-center gap-2">
                    <CheckSquare className="h-4 w-4" />
                    <Label className="font-medium">Create Task for This Staff Member</Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Optionally create a task for this staff assignment
                  </p>
                </div>
                <Switch
                  checked={assignment.createTask}
                  onCheckedChange={(checked) =>
                    updateAssignment(index, { createTask: checked })
                  }
                  disabled={disabled}
                />
              </div>

              {assignment.createTask && (
                <div className="space-y-4 p-4 border rounded-lg bg-background">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Task Title</Label>
                      <Input
                        value={assignment.taskTitle}
                        onChange={(e) =>
                          updateAssignment(index, { taskTitle: e.target.value })
                        }
                        placeholder="Enter task title"
                        disabled={disabled}
                        className={cn(
                          errors?.staffAssignments?.[index]?.taskTitle && "border-destructive"
                        )}
                      />
                      {errors?.staffAssignments?.[index]?.taskTitle && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.staffAssignments[index].taskTitle.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label>Task Priority</Label>
                      <Input
                        value={assignment.taskPriority}
                        onChange={(e) =>
                          updateAssignment(index, { taskPriority: e.target.value })
                        }
                        placeholder="e.g., High, Medium, Low"
                        disabled={disabled}
                        className={cn(
                          errors?.staffAssignments?.[index]?.taskPriority && "border-destructive"
                        )}
                      />
                      {errors?.staffAssignments?.[index]?.taskPriority && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.staffAssignments[index].taskPriority.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label>Task Description</Label>
                    <Textarea
                      value={assignment.taskDescription}
                      onChange={(e) =>
                        updateAssignment(index, { taskDescription: e.target.value })
                      }
                      placeholder="Enter task description"
                      disabled={disabled}
                      rows={3}
                      className={cn(
                        errors?.staffAssignments?.[index]?.taskDescription && "border-destructive"
                      )}
                    />
                    {errors?.staffAssignments?.[index]?.taskDescription && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.staffAssignments[index].taskDescription.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label>Task Due Date</Label>
                    <DateTimePicker
                      value={assignment.taskDueDate}
                      onChange={(date) =>
                        updateAssignment(index, {
                          taskDueDate: date ? date.toISOString() : "",
                        })
                      }
                      placeholder="Select task due date and time"
                      disabled={disabled}
                      className={cn(
                        errors?.staffAssignments?.[index]?.taskDueDate && "border-destructive"
                      )}
                    />
                    {errors?.staffAssignments?.[index]?.taskDueDate && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.staffAssignments[index].taskDueDate.message}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}