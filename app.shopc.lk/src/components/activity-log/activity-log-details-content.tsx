"use client";

import * as React from "react";
import {
  Info,
  Activity,
  User,
  Calendar,
  Monitor,
  MapPin,
  FileText,
  Printer,
  Download,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  ActivityLogTableData,
  ActivityLogStatus,
  ActivityLogType,
} from "@/types/activity-log";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface ActivityLogDetailsContentProps {
  activityLog: ActivityLogTableData;
  isDemo?: boolean;
}

export function ActivityLogDetailsContent({
  activityLog,
  isDemo = false,
}: ActivityLogDetailsContentProps) {
  const printRef = React.useRef<HTMLDivElement>(null);

  // Print functionality
  const handlePrint = React.useCallback(() => {
    const printWindow = window.open("", "_blank");
    if (!printWindow || !printRef.current) return;

    const printContent = printRef.current.cloneNode(true) as HTMLElement;

    // Create a complete HTML document for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Activity Log Details - ${activityLog.description}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .print-header h1 { margin: 0; font-size: 24px; color: #333; }
            .print-header p { margin: 5px 0 0 0; color: #666; }
            .section { margin-bottom: 25px; page-break-inside: avoid; }
            .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
            .field { margin-bottom: 10px; }
            .field label { font-weight: bold; color: #555; display: block; margin-bottom: 3px; font-size: 12px; }
            .field div { color: #333; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
            .badge.success { background-color: #d4edda; color: #155724; }
            .badge.failed { background-color: #f8d7da; color: #721c24; }
            .badge.pending { background-color: #fff3cd; color: #856404; }
            .badge.outline { border: 1px solid #ddd; background-color: #f8f9fa; color: #495057; }
            .code { font-family: monospace; background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Activity Log Details</h1>
            <p>Generated on ${format(new Date(), "PPP 'at' p")}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [activityLog.description]);

  // PDF download functionality
  const handleDownloadPDF = React.useCallback(async () => {
    if (!printRef.current) return;

    try {
      toast.loading("Generating PDF...");

      const canvas = await html2canvas(printRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: printRef.current.scrollWidth,
        height: printRef.current.scrollHeight,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const ratio = Math.min(pdfWidth / canvasWidth, pdfHeight / canvasHeight);
      const imgWidth = canvasWidth * ratio;
      const imgHeight = canvasHeight * ratio;

      // Add header
      pdf.setFontSize(20);
      pdf.text("Activity Log Details", pdfWidth / 2, 20, { align: "center" });
      pdf.setFontSize(12);
      pdf.text(
        `Generated on ${format(new Date(), "PPP 'at' p")}`,
        pdfWidth / 2,
        30,
        { align: "center" }
      );

      // Add content
      pdf.addImage(imgData, "PNG", 0, 40, imgWidth, imgHeight);

      // Save the PDF
      const fileName = `activity-log-${activityLog.id}-${format(new Date(), "yyyy-MM-dd")}.pdf`;
      pdf.save(fileName);

      toast.dismiss();
      toast.success("PDF downloaded successfully!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.dismiss();
      toast.error("Failed to generate PDF");
    }
  }, [activityLog.id]);

  // Helper function to get status color and icon
  const getStatusDisplay = (status: ActivityLogStatus) => {
    switch (status) {
      case ActivityLogStatus.SUCCESS:
        return {
          color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
          icon: <CheckCircle className="h-4 w-4" />,
        };
      case ActivityLogStatus.FAILED:
        return {
          color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
          icon: <XCircle className="h-4 w-4" />,
        };
      case ActivityLogStatus.PENDING:
        return {
          color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
          icon: <AlertCircle className="h-4 w-4" />,
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
          icon: <AlertCircle className="h-4 w-4" />,
        };
    }
  };

  // Helper function to get activity type color and icon
  const getActivityTypeDisplay = (type: ActivityLogType) => {
    switch (type) {
      case ActivityLogType.CREATE:
        return {
          color: "bg-green-50 text-green-700 border-green-200",
          icon: <FileText className="h-4 w-4" />,
          label: "Create",
        };
      case ActivityLogType.UPDATE:
        return {
          color: "bg-blue-50 text-blue-700 border-blue-200",
          icon: <FileText className="h-4 w-4" />,
          label: "Update",
        };
      case ActivityLogType.DELETE:
        return {
          color: "bg-red-50 text-red-700 border-red-200",
          icon: <XCircle className="h-4 w-4" />,
          label: "Delete",
        };
      case ActivityLogType.VIEW:
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: <FileText className="h-4 w-4" />,
          label: "View",
        };
      case ActivityLogType.LOGIN:
        return {
          color: "bg-purple-50 text-purple-700 border-purple-200",
          icon: <User className="h-4 w-4" />,
          label: "Login",
        };
      case ActivityLogType.LOGOUT:
        return {
          color: "bg-orange-50 text-orange-700 border-orange-200",
          icon: <User className="h-4 w-4" />,
          label: "Logout",
        };
      default:
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: <Activity className="h-4 w-4" />,
          label: type ? type.charAt(0).toUpperCase() + type.slice(1) : "Unknown",
        };
    }
  };

  const statusDisplay = getStatusDisplay(activityLog.status);
  const typeDisplay = getActivityTypeDisplay(activityLog.type);

  return (
    <ScrollArea className="h-[70vh]">
      <div className="p-6 space-y-6 bg-white dark:bg-background">
        {/* Action Buttons */}
        <div className="flex items-center justify-between border-b pb-4 no-print">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Activity Log Details
          </h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              Print
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadPDF}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </div>

        {/* Printable Content */}
        <div ref={printRef}>
          <Accordion
            type="multiple"
            defaultValue={[
              "basic-info",
              "user-info",
              "entity-info",
              "technical-info",
              "timestamps",
            ]}
            className="space-y-2"
          >
            {/* Basic Information Section */}
            <AccordionItem value="basic-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/40 data-[state=open]:bg-accent/40 rounded-t-lg">
                <div className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-primary" />
                  <span className="font-medium">Activity Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Description
                    </label>
                    <div className="text-base font-medium text-gray-900 dark:text-gray-100">
                      {activityLog.description}
                    </div>
                  </div>

                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Status
                    </label>
                    <div className="flex items-center gap-2">
                      <Badge
                        className={cn(
                          "capitalize text-xs gap-1",
                          statusDisplay.color
                        )}
                      >
                        {statusDisplay.icon}
                        {activityLog.status.toLowerCase()}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Action Type
                    </label>
                    <div className="flex items-center">
                      <Badge
                        className={cn(
                          "text-xs gap-1 border",
                          typeDisplay.color
                        )}
                      >
                        {typeDisplay.icon}
                        {typeDisplay.label}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Activity Log ID
                    </label>
                    <div className="text-base font-mono text-blue-600 dark:text-blue-400">
                      {activityLog.id}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* User Information */}
            <AccordionItem value="user-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/40 data-[state=open]:bg-accent/40 rounded-t-lg">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-primary" />
                  <span className="font-medium">User Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      User Name
                    </label>
                    <div className="text-base font-medium text-gray-900 dark:text-gray-100">
                      {activityLog.userName}
                    </div>
                  </div>

                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Email Address
                    </label>
                    <div className="text-base text-gray-900 dark:text-gray-100">
                      {activityLog.userEmail}
                    </div>
                  </div>

                  <div className="space-y-1 md:col-span-2">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      User ID
                    </label>
                    <div className="text-base font-mono text-blue-600 dark:text-blue-400">
                      {activityLog.userId}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Entity Information */}
            <AccordionItem value="entity-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/40 data-[state=open]:bg-accent/40 rounded-t-lg">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <span className="font-medium">Entity Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Entity Type
                    </label>
                    <div className="text-base font-medium text-gray-900 dark:text-gray-100">
                      {activityLog.entityType.replace('_', ' ')}
                    </div>
                  </div>

                  {activityLog.entityName && (
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Entity Name
                      </label>
                      <div className="text-base text-gray-900 dark:text-gray-100">
                        {activityLog.entityName}
                      </div>
                    </div>
                  )}

                  <div className="space-y-1 md:col-span-2">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Entity ID
                    </label>
                    <div className="text-base font-mono text-blue-600 dark:text-blue-400">
                      {activityLog.entityId}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Technical Information */}
            <AccordionItem value="technical-info" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/40 data-[state=open]:bg-accent/40 rounded-t-lg">
                <div className="flex items-center gap-2">
                  <Monitor className="h-5 w-5 text-primary" />
                  <span className="font-medium">Technical Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                  {activityLog.ipAddress && (
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        IP Address
                      </label>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <code className="text-base bg-muted px-2 py-1 rounded font-mono">
                          {activityLog.ipAddress}
                        </code>
                      </div>
                    </div>
                  )}

                  {activityLog.userAgent && (
                    <div className="space-y-1 md:col-span-2">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        User Agent
                      </label>
                      <div className="text-sm text-gray-700 dark:text-gray-300 bg-muted p-2 rounded break-all">
                        {activityLog.userAgent}
                      </div>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Additional Data */}
            {activityLog.additionalData && (
              <AccordionItem value="additional-data" className="border rounded-lg">
                <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/40 data-[state=open]:bg-accent/40 rounded-t-lg">
                  <div className="flex items-center gap-2">
                    <Info className="h-5 w-5 text-primary" />
                    <span className="font-medium">Additional Data</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Raw Data
                    </label>
                    <pre className="text-xs bg-muted p-3 rounded-lg overflow-x-auto whitespace-pre-wrap">
                      {JSON.stringify(activityLog.additionalData, null, 2)}
                    </pre>
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Timestamps */}
            <AccordionItem value="timestamps" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-accent/40 data-[state=open]:bg-accent/40 rounded-t-lg">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-primary" />
                  <span className="font-medium">Timestamps</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-4 pb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Created At
                    </label>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div className="text-base text-gray-900 dark:text-gray-100">
                        {format(new Date(activityLog.createdAt), "PPP 'at' pp")}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Last Updated
                    </label>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div className="text-base text-gray-900 dark:text-gray-100">
                        {format(new Date(activityLog.updatedAt), "PPP 'at' pp")}
                      </div>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </ScrollArea>
  );
}