"use client";

import * as React from "react";
import {
  Info,
  User,
  Calendar,
  Monitor,
  Activity,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  MapPin,
  FileText,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  ActivityLogTableData,
  ActivityLogStatus,
  ActivityLogType,
} from "@/types/activity-log";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface ActivityLogDetailsProps {
  activityLog: ActivityLogTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isDemo?: boolean;
}

export function ActivityLogDetails({
  activityLog,
  open,
  onOpenChange,
  isDemo = false,
}: ActivityLogDetailsProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Helper function to get status color and icon
  const getStatusDisplay = (status: ActivityLogStatus) => {
    switch (status) {
      case ActivityLogStatus.SUCCESS:
        return {
          color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
          icon: <CheckCircle className="h-4 w-4" />,
        };
      case ActivityLogStatus.FAILED:
        return {
          color: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
          icon: <XCircle className="h-4 w-4" />,
        };
      case ActivityLogStatus.PENDING:
        return {
          color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
          icon: <AlertCircle className="h-4 w-4" />,
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
          icon: <AlertCircle className="h-4 w-4" />,
        };
    }
  };

  // Helper function to get activity type color and icon
  const getActivityTypeDisplay = (type: ActivityLogType) => {
    switch (type) {
      case ActivityLogType.CREATE:
        return {
          color: "bg-green-50 text-green-700 border-green-200",
          icon: <FileText className="h-4 w-4" />,
          label: "Create",
        };
      case ActivityLogType.UPDATE:
        return {
          color: "bg-blue-50 text-blue-700 border-blue-200",
          icon: <FileText className="h-4 w-4" />,
          label: "Update",
        };
      case ActivityLogType.DELETE:
        return {
          color: "bg-red-50 text-red-700 border-red-200",
          icon: <XCircle className="h-4 w-4" />,
          label: "Delete",
        };
      case ActivityLogType.VIEW:
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: <FileText className="h-4 w-4" />,
          label: "View",
        };
      case ActivityLogType.LOGIN:
        return {
          color: "bg-purple-50 text-purple-700 border-purple-200",
          icon: <User className="h-4 w-4" />,
          label: "Login",
        };
      case ActivityLogType.LOGOUT:
        return {
          color: "bg-orange-50 text-orange-700 border-orange-200",
          icon: <User className="h-4 w-4" />,
          label: "Logout",
        };
      default:
        return {
          color: "bg-gray-50 text-gray-700 border-gray-200",
          icon: <Activity className="h-4 w-4" />,
          label: type.charAt(0).toUpperCase() + type.slice(1),
        };
    }
  };

  // Content component
  const Content = () => {
    if (!activityLog) {
      return (
        <div className="p-6 text-center">
          <p className="text-muted-foreground">Activity log not found</p>
        </div>
      );
    }

    const statusDisplay = getStatusDisplay(activityLog.status);
    const typeDisplay = getActivityTypeDisplay(activityLog.type);

    return (
      <ScrollArea className="h-full max-h-[80vh] overflow-y-auto">
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">Activity Log Details</h2>
              <div className="flex items-center gap-2">
                <Badge
                  className={cn(
                    "font-medium border gap-1",
                    typeDisplay.color
                  )}
                >
                  {typeDisplay.icon}
                  {typeDisplay.label}
                </Badge>
                <Badge
                  className={cn(
                    "font-medium gap-1",
                    statusDisplay.color
                  )}
                >
                  {statusDisplay.icon}
                  {activityLog.status}
                </Badge>
              </div>
            </div>
            <p className="text-lg text-muted-foreground">
              {activityLog.description}
            </p>
          </div>

          <Separator />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* User Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-muted-foreground" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      User Name
                    </label>
                    <p className="text-sm font-medium">{activityLog.userName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Email
                    </label>
                    <p className="text-sm">{activityLog.userEmail}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      User ID
                    </label>
                    <p className="text-sm font-mono text-blue-600 dark:text-blue-400">
                      {activityLog.userId}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Activity Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-muted-foreground" />
                  Activity Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Action Type
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge
                        className={cn(
                          "font-medium border gap-1",
                          typeDisplay.color
                        )}
                      >
                        {typeDisplay.icon}
                        {typeDisplay.label}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Status
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge
                        className={cn(
                          "font-medium gap-1",
                          statusDisplay.color
                        )}
                      >
                        {statusDisplay.icon}
                        {activityLog.status}
                      </Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Description
                    </label>
                    <p className="text-sm">{activityLog.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Entity Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  Entity Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Entity Type
                    </label>
                    <p className="text-sm font-medium">
                      {activityLog.entityType.replace('_', ' ')}
                    </p>
                  </div>
                  {activityLog.entityName && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Entity Name
                      </label>
                      <p className="text-sm">{activityLog.entityName}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Entity ID
                    </label>
                    <p className="text-sm font-mono text-blue-600 dark:text-blue-400">
                      {activityLog.entityId}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Technical Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5 text-muted-foreground" />
                  Technical Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  {activityLog.ipAddress && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        IP Address
                      </label>
                      <div className="flex items-center gap-2 mt-1">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                          {activityLog.ipAddress}
                        </code>
                      </div>
                    </div>
                  )}
                  {activityLog.userAgent && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        User Agent
                      </label>
                      <p className="text-xs text-muted-foreground mt-1 break-all">
                        {activityLog.userAgent}
                      </p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Activity Log ID
                    </label>
                    <p className="text-sm font-mono text-blue-600 dark:text-blue-400">
                      {activityLog.id}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Data */}
            {activityLog.additionalData && (
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="h-5 w-5 text-muted-foreground" />
                    Additional Data
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-xs bg-muted p-3 rounded-lg overflow-x-auto">
                    {JSON.stringify(activityLog.additionalData, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Timestamps */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  Timestamps
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created At
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      {format(new Date(activityLog.createdAt), "PPP 'at' pp")}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      {format(new Date(activityLog.updatedAt), "PPP 'at' pp")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </ScrollArea>
    );
  };

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle className="flex items-center justify-between">
              <span>Activity Log Details</span>
              {isDemo && (
                <Badge variant="outline" className="ml-2">
                  Demo Mode
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          <Content />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader className="border-b">
          <DrawerTitle className="flex items-center justify-between">
            <span>Activity Log Details</span>
            {isDemo && (
              <Badge variant="outline" className="ml-2">
                Demo Mode
              </Badge>
            )}
          </DrawerTitle>
        </DrawerHeader>
        <Content />
      </DrawerContent>
    </Drawer>
  );
}