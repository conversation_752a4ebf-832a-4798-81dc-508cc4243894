"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2 } from "lucide-react";

import { type ActivityLogTableData } from "@/types/activity-log";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";

interface ActivityLogsTableFloatingBarProps {
  table: Table<ActivityLogTableData>;
  onRefresh?: (activityLogIds?: string[]) => void;
  isDemo?: boolean;
}

export function ActivityLogsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: ActivityLogsTableFloatingBarProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedActivityLogs = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedActivityLogs.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedActivityLogs.length, selectionCount]);

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No activity logs selected" };

    // For demo mode, just simulate success
    if (isDemo) {
      return {}; // Success
    }

    // In real implementation, this would call the bulk delete API
    // For now, we'll simulate since activity logs are typically read-only
    try {
      // TODO: Implement actual bulk delete API call
      // const activityLogIds = selectedActivityLogs.map((log) => log.id);
      // const result = await bulkDeleteActivityLogs(activityLogIds);
      
      // Simulate success for now
      return {};
    } catch (error: any) {
      console.error("Error deleting activity logs:", error);
      return { error: error.message || "Failed to delete activity logs" };
    }
  }, [selectedActivityLogs, selectionCount, isDemo]);

  const actions = (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="secondary"
          size="icon"
          className="size-7 border"
          onClick={() => setDeleteDialogOpen(true)}
          disabled={selectionCount === 0}
        >
          <Trash2 className="size-3.5" aria-hidden="true" />
        </Button>
      </TooltipTrigger>
      <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
        <p>Delete selected</p>
      </TooltipContent>
    </Tooltip>
  );

  return (
    <>
      <BaseTableFloatingBar<ActivityLogTableData>
        table={table}
        title="Activity Logs"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} activity log${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass IDs since they're deleted
        }}
      />
    </>
  );
}