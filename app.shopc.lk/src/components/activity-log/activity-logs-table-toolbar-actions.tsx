"use client";

import { type Table } from "@tanstack/react-table";
import { BaseTableToolbarActions } from "@/components/shared/base-table-toolbar-actions";
import { ActivityLogTableData } from "@/types/activity-log";

interface ActivityLogsTableToolbarActionsProps {
  table: Table<ActivityLogTableData>;
  onRefresh?: () => Promise<void>;
  rowSelection: boolean;
  onRowSelectionChange: (value: boolean) => void;
  isDemo?: boolean;
}

export function ActivityLogsTableToolbarActions({
  table,
  onRefresh,
  rowSelection,
  onRowSelectionChange,
  isDemo = false,
}: ActivityLogsTableToolbarActionsProps) {
  return (
    <BaseTableToolbarActions<ActivityLogTableData>
      table={table}
      onRefresh={onRefresh}
      rowSelection={rowSelection}
      onRowSelectionChange={onRowSelectionChange}
      isDemo={isDemo}
      title="Activity Logs"
      addButton={null} // Activity logs are system-generated, no manual creation
      additionalActions={null}
    />
  );
}