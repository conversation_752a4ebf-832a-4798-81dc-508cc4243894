"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@radix-ui/react-icons";
import { ActivityLogTableData } from "@/types/activity-log";
import { toast } from "sonner";

interface DeleteActivityLogsDialogProps {
  activityLogs: ActivityLogTableData[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteActivityLogsDialog({
  activityLogs,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteActivityLogsDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);

  const isMultiple = activityLogs.length > 1;

  const handleDelete = async () => {
    if (activityLogs.length === 0) return;

    setIsDeleting(true);
    try {
      // For demo mode, just simulate the deletion
      if (isDemo) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
        toast.success(`Successfully deleted ${activityLogs.length} activity log${isMultiple ? "s" : ""}`);
        onOpenChange(false);
        onSuccess?.();
        return;
      }

      // TODO: Implement actual bulk delete API call
      // const activityLogIds = activityLogs.map((log) => log.id);
      // const result = await bulkDeleteActivityLogs(activityLogIds);
      
      // For now, simulate success since activity logs are typically read-only
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      toast.success(`Successfully deleted ${activityLogs.length} activity log${isMultiple ? "s" : ""}`);
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error deleting activity logs:", error);
      toast.error("Failed to delete activity logs");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete{" "}
            {isMultiple
              ? `${activityLogs.length} activity logs`
              : "the activity log"}{" "}
            {!isMultiple && activityLogs[0] && (
              <span className="font-medium">"{activityLogs[0].description}"</span>
            )}
            . This action cannot be undone.
            {isDemo && (
              <span className="block mt-2 text-amber-600">
                Demo mode: No actual deletion will occur.
              </span>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting
              ? "Deleting..."
              : `Delete ${
                  isMultiple ? `${activityLogs.length} Activity Logs` : "Activity Log"
                }`}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}