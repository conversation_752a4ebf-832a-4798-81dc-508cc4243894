"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Clock, User, FileText, AlertCircle, CheckCircle, XCircle } from "lucide-react";
import { ActivityLogTableData, ActivityLogType, ActivityLogStatus, ActivityEntityType } from "@/types/activity-log";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow } from "date-fns";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  isSortableEnabled?: boolean;
}

const getActivityTypeIcon = (type: ActivityLogType) => {
  switch (type) {
    case ActivityLogType.CREATE:
      return <FileText className="h-3 w-3" />;
    case ActivityLogType.UPDATE:
      return <FileText className="h-3 w-3" />;
    case ActivityLogType.DELETE:
      return <XCircle className="h-3 w-3" />;
    case ActivityLogType.VIEW:
      return <Eye className="h-3 w-3" />;
    case ActivityLogType.LOGIN:
    case ActivityLogType.LOGOUT:
      return <User className="h-3 w-3" />;
    default:
      return <FileText className="h-3 w-3" />;
  }
};

const getActivityTypeColor = (type: ActivityLogType) => {
  switch (type) {
    case ActivityLogType.CREATE:
      return "bg-green-50 text-green-700 border-green-200";
    case ActivityLogType.UPDATE:
      return "bg-blue-50 text-blue-700 border-blue-200";
    case ActivityLogType.DELETE:
      return "bg-red-50 text-red-700 border-red-200";
    case ActivityLogType.VIEW:
      return "bg-gray-50 text-gray-700 border-gray-200";
    case ActivityLogType.LOGIN:
      return "bg-purple-50 text-purple-700 border-purple-200";
    case ActivityLogType.LOGOUT:
      return "bg-orange-50 text-orange-700 border-orange-200";
    default:
      return "bg-gray-50 text-gray-700 border-gray-200";
  }
};

const getStatusIcon = (status: ActivityLogStatus) => {
  switch (status) {
    case ActivityLogStatus.SUCCESS:
      return <CheckCircle className="h-3 w-3" />;
    case ActivityLogStatus.FAILED:
      return <XCircle className="h-3 w-3" />;
    case ActivityLogStatus.PENDING:
      return <AlertCircle className="h-3 w-3" />;
    default:
      return <AlertCircle className="h-3 w-3" />;
  }
};

const getStatusVariant = (status: ActivityLogStatus): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case ActivityLogStatus.SUCCESS:
      return "default";
    case ActivityLogStatus.FAILED:
      return "destructive";
    case ActivityLogStatus.PENDING:
      return "secondary";
    default:
      return "outline";
  }
};

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  isSortableEnabled = false,
}: ColumnsProps) {
  const columns: ColumnDef<ActivityLogTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10,
    },
    {
      accessorKey: "userName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="User" />
      ),
      cell: ({ row }) => {
        const userName = row.getValue("userName") as string;
        const userEmail = row.original.userEmail;
        
        const displayName = userName || "Unknown";
        const initials = displayName.length >= 2 ? displayName.slice(0, 2).toUpperCase() : displayName.charAt(0).toUpperCase() || "U";
        
        return (
          <div className="flex items-center gap-3 pl-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-xs">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <div className="font-medium">{displayName}</div>
              <div className="text-xs text-muted-foreground">{userEmail || "No email"}</div>
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Activity" />
      ),
      cell: ({ row }) => {
        const description = row.getValue("description") as string;
        return (
          <div className="pl-2 max-w-[300px]">
            <div className="font-medium truncate">{description}</div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "type",
      header: () => <div className="font-medium">Action Type</div>,
      cell: ({ row }) => {
        const type = row.getValue("type") as ActivityLogType;
        const safeType = type || ActivityLogType.VIEW;
        const icon = getActivityTypeIcon(safeType);
        const colorClass = getActivityTypeColor(safeType);
        const displayType = safeType.charAt(0).toUpperCase() + safeType.slice(1);
        
        return (
          <div className="pl-2">
            <div className={`inline-flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium border ${colorClass}`}>
              {icon}
              {displayType}
            </div>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "entityType",
      header: () => <div className="font-medium">Entity</div>,
      cell: ({ row }) => {
        const entityType = row.getValue("entityType") as ActivityEntityType;
        const entityName = row.original.entityName;
        
        return (
          <div className="pl-2">
            <div className="flex flex-col">
              <span className="text-sm font-medium">{entityType.replace('_', ' ')}</span>
              {entityName && (
                <span className="text-xs text-muted-foreground truncate max-w-[120px]">
                  {entityName}
                </span>
              )}
            </div>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const status = row.getValue("status") as ActivityLogStatus;
        const safeStatus = status || ActivityLogStatus.PENDING;
        const icon = getStatusIcon(safeStatus);
        const variant = getStatusVariant(safeStatus);
        const displayStatus = safeStatus.charAt(0).toUpperCase() + safeStatus.slice(1);
        
        return (
          <div className="pl-2">
            <Badge variant={variant} className="gap-1">
              {icon}
              {displayStatus}
            </Badge>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "ipAddress",
      header: () => <div className="font-medium">IP Address</div>,
      cell: ({ row }) => {
        const ipAddress = row.original.ipAddress;
        return (
          <div className="pl-2">
            {ipAddress ? (
              <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-xs">
                {ipAddress}
              </code>
            ) : (
              <span className="text-xs text-muted-foreground">N/A</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Time" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as string;
        const date = new Date(createdAt);
        
        return (
          <div className="pl-2">
            <div className="flex items-center gap-1 text-sm">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span>{formatDistanceToNow(date, { addSuffix: true })}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {date.toLocaleString()}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
                className="text-red-600"
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}