"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getActivityLogsTableData } from "@/lib/activity-log/queries";
import { DeleteActivityLogsDialog } from "./delete-activity-logs-dialog";
import { ActivityLogsTableToolbarActions } from "./activity-logs-table-toolbar-actions";
import { ActivityLogsTableFloatingBar } from "./activity-logs-table-floating-bar";
import { ActivityLogDetails } from "./activity-log-details";
import { ActivityLogDetailsContent } from "./activity-log-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./activity-logs-table-columns";
import { ActivityLogTableData, ActivityLogStatus, ActivityLogType, ActivityEntityType } from "@/types/activity-log";
import { useActivityLogsData } from "@/lib/activity-log/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { activityLogKeys } from "@/lib/activity-log/hooks";

interface ActivityLogsTableProps {
  isDemo?: boolean;
}

export function ActivityLogTable({ isDemo = false }: ActivityLogsTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<ActivityLogTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<ActivityLogTableData>[] = React.useMemo(
    () => [
      {
        id: "description",
        label: "Description",
        placeholder: "Filter by description...",
      },
      // type
      {
        id: "type",
        label: "Action Type",
        placeholder: "Filter by action type...",
        type: "select",
        options: [
          { label: "Create", value: ActivityLogType.CREATE },
          { label: "Update", value: ActivityLogType.UPDATE },
          { label: "Delete", value: ActivityLogType.DELETE },
          { label: "View", value: ActivityLogType.VIEW },
          { label: "Login", value: ActivityLogType.LOGIN },
          { label: "Logout", value: ActivityLogType.LOGOUT },
          { label: "Export", value: ActivityLogType.EXPORT },
          { label: "Import", value: ActivityLogType.IMPORT },
          { label: "Upload", value: ActivityLogType.UPLOAD },
          { label: "Download", value: ActivityLogType.DOWNLOAD },
          { label: "Approve", value: ActivityLogType.APPROVE },
          { label: "Reject", value: ActivityLogType.REJECT },
          { label: "Archive", value: ActivityLogType.ARCHIVE },
          { label: "Restore", value: ActivityLogType.RESTORE },
        ],
      },
      // status
      {
        id: "status",
        label: "Status",
        placeholder: "Filter by status...",
        type: "select",
        options: [
          { label: "Success", value: ActivityLogStatus.SUCCESS },
          { label: "Failed", value: ActivityLogStatus.FAILED },
          { label: "Pending", value: ActivityLogStatus.PENDING },
        ],
      },
      // entityType
      {
        id: "entityType",
        label: "Entity Type",
        placeholder: "Filter by entity type...",
        type: "select",
        options: [
          { label: "User", value: ActivityEntityType.USER },
          { label: "Category", value: ActivityEntityType.CATEGORY },
          { label: "Service Category", value: ActivityEntityType.SERVICE_CATEGORY },
          { label: "Product", value: ActivityEntityType.PRODUCT },
          { label: "Service", value: ActivityEntityType.SERVICE },
          { label: "Customer", value: ActivityEntityType.CUSTOMER },
          { label: "Supplier", value: ActivityEntityType.SUPPLIER },
          { label: "Staff", value: ActivityEntityType.STAFF },
          { label: "Location", value: ActivityEntityType.LOCATION },
          { label: "Invoice", value: ActivityEntityType.INVOICE },
          { label: "Order", value: ActivityEntityType.ORDER },
          { label: "Payment", value: ActivityEntityType.PAYMENT },
          { label: "Inventory", value: ActivityEntityType.INVENTORY },
          { label: "Business", value: ActivityEntityType.BUSINESS },
          { label: "Account", value: ActivityEntityType.ACCOUNT },
          { label: "Task", value: ActivityEntityType.TASK },
          { label: "Project", value: ActivityEntityType.PROJECT },
          { label: "Campaign", value: ActivityEntityType.CAMPAIGN },
          { label: "Brand", value: ActivityEntityType.BRAND },
          { label: "Asset", value: ActivityEntityType.ASSET },
          { label: "Vehicle", value: ActivityEntityType.VEHICLE },
          { label: "Equipment", value: ActivityEntityType.EQUIPMENT },
        ],
      },
    ],
    []
  );

  const advancedFilterFields: DataTableAdvancedFilterField<ActivityLogTableData>[] =
    React.useMemo(
      () => [
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        {
          id: "userName",
          label: "User Name",
          type: "text",
        },
        {
          id: "userEmail",
          label: "User Email",
          type: "text",
        },
        {
          id: "entityName",
          label: "Entity Name",
          type: "text",
        },
        {
          id: "ipAddress",
          label: "IP Address",
          type: "text",
        },
        {
          id: "type",
          label: "Action Type",
          type: "select",
          options: [
            { label: "Create", value: ActivityLogType.CREATE },
            { label: "Update", value: ActivityLogType.UPDATE },
            { label: "Delete", value: ActivityLogType.DELETE },
            { label: "View", value: ActivityLogType.VIEW },
            { label: "Login", value: ActivityLogType.LOGIN },
            { label: "Logout", value: ActivityLogType.LOGOUT },
            { label: "Export", value: ActivityLogType.EXPORT },
            { label: "Import", value: ActivityLogType.IMPORT },
            { label: "Upload", value: ActivityLogType.UPLOAD },
            { label: "Download", value: ActivityLogType.DOWNLOAD },
            { label: "Approve", value: ActivityLogType.APPROVE },
            { label: "Reject", value: ActivityLogType.REJECT },
            { label: "Archive", value: ActivityLogType.ARCHIVE },
            { label: "Restore", value: ActivityLogType.RESTORE },
          ],
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Success", value: ActivityLogStatus.SUCCESS },
            { label: "Failed", value: ActivityLogStatus.FAILED },
            { label: "Pending", value: ActivityLogStatus.PENDING },
          ],
        },
        {
          id: "entityType",
          label: "Entity Type",
          type: "select",
          options: [
            { label: "User", value: ActivityEntityType.USER },
            { label: "Category", value: ActivityEntityType.CATEGORY },
            { label: "Service Category", value: ActivityEntityType.SERVICE_CATEGORY },
            { label: "Product", value: ActivityEntityType.PRODUCT },
            { label: "Service", value: ActivityEntityType.SERVICE },
            { label: "Customer", value: ActivityEntityType.CUSTOMER },
            { label: "Supplier", value: ActivityEntityType.SUPPLIER },
            { label: "Staff", value: ActivityEntityType.STAFF },
            { label: "Location", value: ActivityEntityType.LOCATION },
            { label: "Invoice", value: ActivityEntityType.INVOICE },
            { label: "Order", value: ActivityEntityType.ORDER },
            { label: "Payment", value: ActivityEntityType.PAYMENT },
            { label: "Inventory", value: ActivityEntityType.INVENTORY },
            { label: "Business", value: ActivityEntityType.BUSINESS },
            { label: "Account", value: ActivityEntityType.ACCOUNT },
            { label: "Task", value: ActivityEntityType.TASK },
            { label: "Project", value: ActivityEntityType.PROJECT },
            { label: "Campaign", value: ActivityEntityType.CAMPAIGN },
            { label: "Brand", value: ActivityEntityType.BRAND },
            { label: "Asset", value: ActivityEntityType.ASSET },
            { label: "Vehicle", value: ActivityEntityType.VEHICLE },
            { label: "Equipment", value: ActivityEntityType.EQUIPMENT },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<ActivityLogTableData>[] =
    React.useMemo(
      () => [
        {
          id: "userName",
          label: "User Name",
          type: "text",
        },
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        {
          id: "type",
          label: "Action Type",
          type: "text",
        },
        {
          id: "entityType",
          label: "Entity Type",
          type: "text",
        },
        {
          id: "entityName",
          label: "Entity Name",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "text",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<ActivityLogTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      searchTerm: searchParams.get("searchTerm") || "",
      userId: searchParams.get("userId") || "",
      type: searchParams.get("type") || "",
      entityType: searchParams.get("entityType") || "",
      entityId: searchParams.get("entityId") || "",
      status: searchParams.get("status") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [{ id: "createdAt", desc: true }];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: activityLogsData,
    isLoading,
    isFetching,
    isRefetching,
  } = useActivityLogsData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.searchTerm ||
      searchParamsValues.userId ||
      searchParamsValues.type ||
      searchParamsValues.entityType ||
      searchParamsValues.entityId ||
      searchParamsValues.status ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  const handleRefresh = React.useCallback(
    async (activityLogIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: activityLogKeys.list() }),
        queryClient.invalidateQueries({ queryKey: activityLogKeys.simple() }),
      ];

      // If specific activity log IDs are provided, invalidate their detail cache as well
      if (activityLogIds && activityLogIds.length > 0) {
        activityLogIds.forEach((activityLogId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: activityLogKeys.detail(activityLogId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  // Custom dialog content renderer for row clicks
  const renderActivityLogDetails = React.useCallback(
    (activityLog: ActivityLogTableData) => {
      return <ActivityLogDetailsContent activityLog={activityLog} isDemo={isDemo} />;
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        ActivityLogTableData,
        Awaited<ReturnType<typeof getActivityLogsTableData>>
      >
        data={activityLogsData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => false}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          isSortableEnabled: false,
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <ActivityLogsTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <ActivityLogsTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId="createdAt"
        defaultSortingDesc={true}
        onRefresh={handleRefresh}
        renderDialogContent={renderActivityLogDetails}
      />
      <ActivityLogDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        activityLog={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <DeleteActivityLogsDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        activityLogs={rowAction?.row.original ? [rowAction.row.original] : []}
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: activityLogKeys.list() });
          queryClient.invalidateQueries({ queryKey: activityLogKeys.simple() });
        }}
        isDemo={isDemo}
      />
    </>
  );
}