"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Image as ImageIcon,
  <PERSON>lette,
  Check,
  X,
  Loader2,
  <PERSON><PERSON><PERSON><PERSON>gle,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { serviceOrderPriorityFormSchema } from "@/lib/service-order-priorities/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ServiceOrderPriorityTableData,
  UpdateServiceOrderPriorityDto,
} from "@/types/service-order-priority";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useServiceOrderPriority,
  useServiceOrderPriorityCodeAvailability,
  useServiceOrderPriorityNameAvailability,
  useCreateServiceOrderPriority,
  useUpdateServiceOrderPriority,
} from "@/lib/service-order-priorities/hooks";
import { useEffect } from "react";

interface ServiceOrderPrioritySheetProps {
  serviceOrderPriority: ServiceOrderPriorityTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (serviceOrderPriority?: ServiceOrderPriorityTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof serviceOrderPriorityFormSchema>;

export function ServiceOrderPrioritySheet({
  serviceOrderPriority,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ServiceOrderPrioritySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Values for availability checking
  const [checkCode, setCheckCode] = React.useState<string>("");
  const [checkName, setCheckName] = React.useState<string>("");

  // Fetch complete service order priority data if updating
  const { data: fullServiceOrderPriorityResponse, isLoading: isLoadingServiceOrderPriority } =
    useServiceOrderPriority(serviceOrderPriority?.id || "", isDemo);
  const fullServiceOrderPriority = fullServiceOrderPriorityResponse?.data;

  useEffect(() => {
    if (fullServiceOrderPriorityResponse) {
      console.log("fullServiceOrderPriorityResponse", fullServiceOrderPriorityResponse);
    }
  }, [fullServiceOrderPriorityResponse]);

  // Mutation hooks for create and update operations
  const createServiceOrderPriorityMutation = useCreateServiceOrderPriority(isDemo);
  const updateServiceOrderPriorityMutation = useUpdateServiceOrderPriority(isDemo);

  // Availability checks (only check if not updating the same service order priority)
  const shouldCheckCodeAvailability =
    checkCode.length > 0 &&
    (!isUpdate ||
      (serviceOrderPriority && checkCode.toUpperCase() !== serviceOrderPriority.priorityCode.toUpperCase()));
  const shouldCheckNameAvailability =
    checkName.length > 0 &&
    (!isUpdate ||
      (serviceOrderPriority && checkName.toLowerCase() !== serviceOrderPriority.priorityName.toLowerCase()));

  const { data: codeAvailabilityResponse, isLoading: isCheckingCode } =
    useServiceOrderPriorityCodeAvailability(checkCode, isDemo, serviceOrderPriority?.id);
  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useServiceOrderPriorityNameAvailability(checkName, isDemo, serviceOrderPriority?.id);

  const isCodeAvailable = shouldCheckCodeAvailability
    ? codeAvailabilityResponse?.data?.available ?? true
    : true;
  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(serviceOrderPriorityFormSchema),
    defaultValues: {
      priorityCode: "",
      priorityName: "",
      description: "",
      colorCode: "",
      iconName: "",
      severityLevel: 1,
      isActive: true,
      isDefault: false,
      escalationMinutes: undefined,
    },
  });

  React.useEffect(() => {
    if (serviceOrderPriority && fullServiceOrderPriority) {
      // Use full service order priority data for populating the form
      form.reset({
        priorityCode: fullServiceOrderPriority.priorityCode,
        priorityName: fullServiceOrderPriority.priorityName,
        description: fullServiceOrderPriority.description || "",
        colorCode: fullServiceOrderPriority.colorCode || "",
        iconName: fullServiceOrderPriority.iconName || "",
        severityLevel: fullServiceOrderPriority.severityLevel,
        isActive: fullServiceOrderPriority.isActive,
        isDefault: fullServiceOrderPriority.isDefault,
        escalationMinutes: fullServiceOrderPriority.escalationMinutes || undefined,
      });
    } else if (!serviceOrderPriority) {
      // Reset form for new service order priority
      form.reset({
        priorityCode: "",
        priorityName: "",
        description: "",
        colorCode: "",
        iconName: "",
        severityLevel: 1,
        isActive: true,
        isDefault: false,
        escalationMinutes: undefined,
      });
    }
  }, [serviceOrderPriority, fullServiceOrderPriority]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
  }, [open]);

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckCodeAvailability && isCodeAvailable === false) {
      toast.error(
        "Priority code is already taken. Please choose a different code."
      );
      return;
    }

    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Priority name is already taken. Please choose a different name."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (serviceOrderPriority) {
        // Update existing service order priority
        const updateData: UpdateServiceOrderPriorityDto = {
          priorityCode: data.priorityCode,
          priorityName: data.priorityName,
          description: data.description || undefined,
          colorCode: data.colorCode || undefined,
          iconName: data.iconName || undefined,
          severityLevel: data.severityLevel,
          isActive: data.isActive,
          isDefault: data.isDefault,
          escalationMinutes: data.escalationMinutes || undefined,
        };

        const response = await updateServiceOrderPriorityMutation.mutateAsync({
          id: serviceOrderPriority.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service order priority updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ServiceOrderPriorityTableData);
          return;
        }
        toast.error(response.message || "Failed to update service order priority");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new service order priority
        const createData = {
          priorityCode: data.priorityCode,
          priorityName: data.priorityName,
          description: data.description || undefined,
          colorCode: data.colorCode || undefined,
          iconName: data.iconName || undefined,
          severityLevel: data.severityLevel,
          isActive: data.isActive,
          isDefault: data.isDefault,
          escalationMinutes: data.escalationMinutes || undefined,
        };

        const response = await createServiceOrderPriorityMutation.mutateAsync({
          data: createData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service order priority created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ServiceOrderPriorityTableData);
          return;
        }
        toast.error(response.message || "Failed to create service order priority");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save service order priority:", error);
      toast.error("Failed to save service order priority");
    } finally {
      setIsSubmitting(false);
    }
  };

  const {
    formState: { errors },
  } = form;

  // Handle availability checks
  const handleCheckCodeAvailability = (value: string) => {
    setCheckCode(value);
  };

  const handleCheckNameAvailability = (value: string) => {
    setCheckName(value);
  };

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.priorityCode ||
    errors.priorityName ||
    errors.severityLevel ||
    errors.isActive ||
    errors.isDefault ||
    (shouldCheckCodeAvailability && isCodeAvailable === false) ||
    (shouldCheckNameAvailability && isNameAvailable === false)
  );

  const hasDetailsErrors = !!(
    errors.description ||
    errors.colorCode ||
    errors.iconName ||
    errors.escalationMinutes
  );

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="priorityCode"
                label="Priority Code"
                placeholder="Enter priority code (e.g., HIGH, URGENT)"
                value={form.watch("priorityCode") || ""}
                onChange={(value) =>
                  form.setValue("priorityCode", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckCodeAvailability}
                isCheckingAvailability={isCheckingCode}
                isAvailable={isCodeAvailable}
                shouldCheck={shouldCheckCodeAvailability}
                error={errors.priorityCode?.message}
                disabled={isSubmitting}
                fieldName="Priority code"
              />

              <AvailabilityInput
                name="priorityName"
                label="Priority Name"
                placeholder="Enter priority name (e.g., High Priority, Urgent)"
                value={form.watch("priorityName") || ""}
                onChange={(value) =>
                  form.setValue("priorityName", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckNameAvailability}
                isCheckingAvailability={isCheckingName}
                isAvailable={isNameAvailable}
                shouldCheck={shouldCheckNameAvailability}
                error={errors.priorityName?.message}
                disabled={isSubmitting}
                fieldName="Priority name"
              />

              <div>
                <Label>Severity Level</Label>
                <Select
                  value={form.watch("severityLevel")?.toString()}
                  onValueChange={(value) =>
                    form.setValue("severityLevel", parseInt(value), {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.severityLevel && "border-destructive")}
                  >
                    <SelectValue placeholder="Select severity level" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map((level) => (
                      <SelectItem key={level} value={level.toString()}>
                        Level {level} {level <= 3 ? "(Low)" : level <= 6 ? "(Medium)" : level <= 8 ? "(High)" : "(Critical)"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.severityLevel && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.severityLevel.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Severity level from 1 (lowest) to 10 (highest priority)
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Active</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this priority available for selection
                  </p>
                </div>
                <Switch
                  checked={form.watch("isActive")}
                  onCheckedChange={(checked) =>
                    form.setValue("isActive", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.isActive && (
                <p className="text-sm text-destructive mt-1">
                  {errors.isActive.message}
                </p>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Default Priority</Label>
                  <p className="text-sm text-muted-foreground">
                    Set as the default priority for new service orders
                    {form.watch("isDefault") && (
                      <span className="block text-orange-600 font-medium mt-1">
                        ⚠️ Only one priority can be set as default
                      </span>
                    )}
                  </p>
                </div>
                <Switch
                  checked={form.watch("isDefault")}
                  onCheckedChange={(checked) =>
                    form.setValue("isDefault", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.isDefault && (
                <p className="text-sm text-destructive mt-1">
                  {errors.isDefault.message}
                </p>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Palette className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter priority description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the priority level
                </p>
              </div>

              <div>
                <Label>Color Code</Label>
                <Input
                  {...form.register("colorCode")}
                  name="colorCode"
                  type="color"
                  placeholder="#FF0000"
                  className={cn(
                    "h-10 w-full cursor-pointer",
                    errors.colorCode && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.colorCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.colorCode.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Color to represent this priority level
                </p>
              </div>

              <div>
                <Label>Icon Name</Label>
                <Input
                  {...form.register("iconName")}
                  name="iconName"
                  placeholder="e.g., alert-triangle, zap, clock"
                  className={cn(errors.iconName && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.iconName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.iconName.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional icon name from Lucide icons
                </p>
              </div>

              <div>
                <Label>Escalation Minutes</Label>
                <Input
                  {...form.register("escalationMinutes", { valueAsNumber: true })}
                  name="escalationMinutes"
                  type="number"
                  min="1"
                  placeholder="e.g., 60"
                  className={cn(errors.escalationMinutes && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.escalationMinutes && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.escalationMinutes.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Minutes after which this priority should be escalated
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ServiceOrderPriorityTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={serviceOrderPriority}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Service Order Priority"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingServiceOrderPriority}
      // Location props (disabled for service order priorities)
      isLocationEnabled={false}
      // SEO props (disabled for service order priorities)
      isSEOEnabled={false}
    />
  );
}
