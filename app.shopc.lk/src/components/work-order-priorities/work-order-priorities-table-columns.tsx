"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye, Flag, Clock } from "lucide-react";
import { WorkOrderPriorityTableData } from "@/types/work-order-priority";
import { WorkOrderPriorityStatusBadge } from "./work-order-priority-status-badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (workOrderPriorityId: string, newStatus: boolean) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<WorkOrderPriorityTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10, // Set a fixed small width for the checkbox column
    },
    {
      accessorKey: "priorityName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Priority Name" />
      ),
      cell: ({ row }) => {
        const priorityName = row.getValue("priorityName") as string;
        const priorityCode = row.original.priorityCode;
        const colorCode = row.original.colorCode;

        return (
          <div className="flex items-center gap-3 pl-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback
                className="text-xs"
                style={{ backgroundColor: colorCode || "#6B7280" }}
              >
                {priorityName.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <div className="font-medium">{priorityName}</div>
              {priorityCode && (
                <div className="text-xs text-muted-foreground">
                  {priorityCode}
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "severityLevel",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Severity Level" />
      ),
      cell: ({ row }) => {
        const severityLevel = row.getValue("severityLevel") as number;
        const colorCode = row.original.colorCode;

        return (
          <div className="pl-2">
            <Badge
              variant="outline"
              className="font-mono"
              style={{
                borderColor: colorCode || "#6B7280",
                color: colorCode || "#6B7280",
              }}
            >
              Level {severityLevel}
            </Badge>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => {
        const description = row.getValue("description") as string;
        return (
          <div className="pl-2 max-w-[200px]">
            {description ? (
              <span className="text-sm truncate block" title={description}>
                {description}
              </span>
            ) : (
              <span className="text-xs text-muted-foreground">
                No description
              </span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "escalationHours",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Escalation Hours" />
      ),
      cell: ({ row }) => {
        const escalationHours = row.getValue("escalationHours") as number;
        return (
          <div className="pl-2">
            {escalationHours ? (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3 text-orange-600" />
                <span className="text-sm">{escalationHours}h</span>
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">
                No escalation
              </span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "isDefault",
      header: () => <div className="font-medium">Default Priority</div>,
      cell: ({ row }) => {
        const isDefault = row.getValue("isDefault") as boolean;
        return (
          <div className="pl-2">
            {isDefault ? (
              <div className="flex items-center gap-1">
                <Flag className="h-3 w-3 text-blue-600" />
                <span className="text-xs text-blue-600 font-medium">
                  Default
                </span>
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">-</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "isActive",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const isActive = row.getValue("isActive") as boolean;
        return (
          <WorkOrderPriorityStatusBadge
            workOrderPriorityId={row.original.id}
            isActive={isActive}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "position",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Position" />
      ),
      cell: ({ row }) => {
        const position = row.getValue("position") as number;
        return (
          <div className="pl-2">
            <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-700">
              #{position}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as Date;
        return (
          <div className="pl-2">
            <span className="text-sm">
              {new Date(createdAt).toLocaleDateString()}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}
