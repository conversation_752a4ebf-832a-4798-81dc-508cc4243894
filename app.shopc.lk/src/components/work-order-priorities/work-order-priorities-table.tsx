"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getWorkOrderPrioritiesTableData } from "@/lib/work-order-priorities/queries";
import { DeleteWorkOrderPrioritiesDialog } from "./delete-work-order-priorities-dialog";
import { WorkOrderPrioritiesTableToolbarActions } from "./work-order-priorities-table-toolbar-actions";
import { WorkOrderPrioritiesTableFloatingBar } from "./work-order-priorities-table-floating-bar";
import { WorkOrderPrioritySheet } from "./work-order-priority-sheet";
import { WorkOrderPriorityDetails } from "./work-order-priority-details";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./work-order-priorities-table-columns";
import { WorkOrderPriorityTableData } from "@/types/work-order-priority";
import { useWorkOrderPrioritiesData } from "@/lib/work-order-priorities/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { workOrderPriorityKeys } from "@/lib/work-order-priorities/hooks";
import { updateWorkOrderPriorityPositions } from "@/lib/work-order-priorities/queries";
import { ApiStatus } from "@/types/common";

interface WorkOrderPrioritiesTableProps {
  isDemo?: boolean;
}

export function WorkOrderPrioritiesTable({
  isDemo = false,
}: WorkOrderPrioritiesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<WorkOrderPriorityTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<WorkOrderPriorityTableData>[] =
    React.useMemo(
      () => [
        {
          id: "priorityName",
          label: "Priority Name",
          placeholder: "Filter by priority name...",
        },
        // isActive
        {
          id: "isActive",
          label: "Status",
          placeholder: "Filter by status...",
          type: "select",
          options: [
            { label: "Active", value: "true" },
            { label: "Inactive", value: "false" },
          ],
        },
        // isDefault
        {
          id: "isDefault",
          label: "Default Priority",
          placeholder: "Filter by default...",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
      ],
      []
    );

  const advancedFilterFields: DataTableAdvancedFilterField<WorkOrderPriorityTableData>[] =
    React.useMemo(
      () => [
        {
          id: "priorityName",
          label: "Priority Name",
          type: "text",
        },
        {
          id: "priorityCode",
          label: "Priority Code",
          type: "text",
        },
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        {
          id: "severityLevel",
          label: "Severity Level",
          type: "number",
        },
        {
          id: "isActive",
          label: "Status",
          type: "select",
          options: [
            { label: "Active", value: "true" },
            { label: "Inactive", value: "false" },
          ],
        },
        {
          id: "isDefault",
          label: "Default Priority",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
        {
          id: "escalationHours",
          label: "Escalation Hours",
          type: "number",
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<WorkOrderPriorityTableData>[] =
    React.useMemo(
      () => [
        {
          id: "priorityName",
          label: "Priority Name",
          type: "text",
        },
        {
          id: "position",
          label: "Position",
          type: "number",
        },
        {
          id: "severityLevel",
          label: "Severity Level",
          type: "number",
        },
        {
          id: "escalationHours",
          label: "Escalation Hours",
          type: "number",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<WorkOrderPriorityTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      priorityName: searchParams.get("priorityName") || "",
      priorityCode: searchParams.get("priorityCode") || "",
      description: searchParams.get("description") || "",
      severityLevel: searchParams.get("severityLevel") || "",
      isActive: searchParams.get("isActive") || "",
      isDefault: searchParams.get("isDefault") || "",
      escalationHours: searchParams.get("escalationHours") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: workOrderPrioritiesData,
    isLoading,
    isFetching,
    isRefetching,
  } = useWorkOrderPrioritiesData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.priorityName ||
      searchParamsValues.priorityCode ||
      searchParamsValues.description ||
      searchParamsValues.severityLevel ||
      searchParamsValues.isActive ||
      searchParamsValues.isDefault ||
      searchParamsValues.escalationHours ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  // Handle reordering
  const handleReorder = React.useCallback(
    async (updates: { id: string; position: number }[]) => {
      try {
        const result = await updateWorkOrderPriorityPositions(updates, isDemo);
        return {
          status: result.status === ApiStatus.SUCCESS ? "success" : "error",
          message: result.message || undefined,
        };
      } catch (error) {
        return {
          status: "error",
          message: "Failed to reorder work order priorities",
        };
      }
    },
    [isDemo]
  );

  const handleRefresh = React.useCallback(
    async (workOrderPriorityIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({
          queryKey: workOrderPriorityKeys.list(),
        }),
        queryClient.invalidateQueries({
          queryKey: workOrderPriorityKeys.slim(),
        }),
      ];

      // If specific work order priority IDs are provided, invalidate their detail cache as well
      if (workOrderPriorityIds && workOrderPriorityIds.length > 0) {
        workOrderPriorityIds.forEach((workOrderPriorityId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: workOrderPriorityKeys.detail(workOrderPriorityId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  return (
    <>
      <BaseTable<
        WorkOrderPriorityTableData,
        Awaited<ReturnType<typeof getWorkOrderPrioritiesTableData>>
      >
        data={workOrderPrioritiesData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={true}
        onReorder={handleReorder}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => true}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onStatusUpdate: (workOrderPriorityId?: string) => {
            queryClient.invalidateQueries({
              queryKey: workOrderPriorityKeys.list(),
            });
            queryClient.invalidateQueries({
              queryKey: workOrderPriorityKeys.slim(),
            });
            if (workOrderPriorityId) {
              queryClient.invalidateQueries({
                queryKey: workOrderPriorityKeys.detail(workOrderPriorityId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <WorkOrderPrioritiesTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <WorkOrderPrioritiesTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
      />
      <WorkOrderPriorityDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        workOrderPriority={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <WorkOrderPrioritySheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        workOrderPriority={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const workOrderPriorityId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({
            queryKey: workOrderPriorityKeys.list(),
          });
          queryClient.invalidateQueries({
            queryKey: workOrderPriorityKeys.slim(),
          });
          if (workOrderPriorityId) {
            queryClient.invalidateQueries({
              queryKey: workOrderPriorityKeys.detail(workOrderPriorityId),
            });
          }
        }}
        isUpdate={true}
      />
      <DeleteWorkOrderPrioritiesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        workOrderPriorities={
          rowAction?.row.original ? [rowAction.row.original] : []
        }
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: workOrderPriorityKeys.list(),
          });
          queryClient.invalidateQueries({
            queryKey: workOrderPriorityKeys.slim(),
          });
        }}
        isDemo={isDemo}
      />
    </>
  );
}
