"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, <PERSON><PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON>, Star, StarOff } from "lucide-react";
import { toast } from "sonner";

import { type WorkOrderPriorityTableData } from "@/types/work-order-priority";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteWorkOrderPriorities,
  useUpdateWorkOrderPriority,
} from "@/lib/work-order-priorities/hooks";
import { ApiStatus } from "@/types/common";

interface WorkOrderPrioritiesTableFloatingBarProps {
  table: Table<WorkOrderPriorityTableData>;
  onRefresh?: (workOrderPriorityIds?: string[]) => void;
  isDemo?: boolean;
}

export function WorkOrderPrioritiesTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: WorkOrderPrioritiesTableFloatingBarProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "activate" | "deactivate" | "make-default" | "remove-default"
  >();

  const bulkDeleteMutation = useBulkDeleteWorkOrderPriorities(isDemo);
  const updateWorkOrderPriorityMutation = useUpdateWorkOrderPriority(isDemo);

  const isPending = updateWorkOrderPriorityMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedWorkOrderPriorities = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedWorkOrderPriorities.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedWorkOrderPriorities.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (isActive: boolean) => {
      if (selectionCount === 0) return;

      setAction(isActive ? "activate" : "deactivate");

      try {
        const workOrderPriorityIds = selectedWorkOrderPriorities.map((priority) => priority.id);
        
        // Update each priority individually since we don't have bulk status update
        const promises = selectedWorkOrderPriorities.map((priority) =>
          updateWorkOrderPriorityMutation.mutateAsync({
            id: priority.id,
            data: { isActive },
          })
        );

        const results = await Promise.all(promises);
        const failedUpdates = results.filter(
          (result) => result.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
          toast.success(
            `Successfully ${
              isActive ? "activated" : "deactivated"
            } ${selectionCount} work order priority${selectionCount > 1 ? "s" : ""}`
          );
          table.toggleAllRowsSelected(false);
          onRefresh?.(workOrderPriorityIds);
        } else {
          toast.error(
            `Failed to update ${failedUpdates.length} work order priority${
              failedUpdates.length > 1 ? "s" : ""
            }`
          );
        }
      } catch (error) {
        console.error("Error updating work order priorities:", error);
        toast.error("Failed to update work order priorities");
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedWorkOrderPriorities,
      selectionCount,
      updateWorkOrderPriorityMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDefaultUpdate = React.useCallback(
    async (isDefault: boolean) => {
      if (selectionCount === 0) return;

      setAction(isDefault ? "make-default" : "remove-default");

      try {
        const workOrderPriorityIds = selectedWorkOrderPriorities.map((priority) => priority.id);
        
        // Update each priority individually
        const promises = selectedWorkOrderPriorities.map((priority) =>
          updateWorkOrderPriorityMutation.mutateAsync({
            id: priority.id,
            data: { isDefault },
          })
        );

        const results = await Promise.all(promises);
        const failedUpdates = results.filter(
          (result) => result.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
          toast.success(
            `Successfully ${
              isDefault ? "set as default" : "removed as default"
            } ${selectionCount} work order priority${selectionCount > 1 ? "s" : ""}`
          );
          table.toggleAllRowsSelected(false);
          onRefresh?.(workOrderPriorityIds);
        } else {
          toast.error(
            `Failed to update ${failedUpdates.length} work order priority${
              failedUpdates.length > 1 ? "s" : ""
            }`
          );
        }
      } catch (error) {
        console.error("Error updating work order priorities:", error);
        toast.error("Failed to update work order priorities");
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedWorkOrderPriorities,
      selectionCount,
      updateWorkOrderPriorityMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No work order priorities selected" };

    try {
      const workOrderPriorityIds = selectedWorkOrderPriorities.map((priority) => priority.id);
      const result = await bulkDeleteMutation.mutateAsync(workOrderPriorityIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete work order priorities",
        };
      }
    } catch (error: any) {
      console.error("Error deleting work order priorities:", error);
      return { error: error.message || "Failed to delete work order priorities" };
    }
  }, [selectedWorkOrderPriorities, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(true)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(false)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkDefaultUpdate(true)}
            disabled={isPending || selectionCount === 0}
          >
            <Star className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Set selected as default</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkDefaultUpdate(false)}
            disabled={isPending || selectionCount === 0}
          >
            <StarOff className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Remove selected as default</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<WorkOrderPriorityTableData>
        table={table}
        title="Work Order Priorities"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} work order priority${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass IDs since they're deleted
        }}
      />
    </>
  );
}