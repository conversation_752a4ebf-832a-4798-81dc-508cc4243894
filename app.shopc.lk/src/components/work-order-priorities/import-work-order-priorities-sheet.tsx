/**
 * ImportWorkOrderPrioritiesSheet Component
 *
 * This component allows importing work order priorities with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Field validation and error handling
 * - Integration with bulk import API
 * - Support for priority levels, codes, names, and other attributes
 */

"use client";

import { useState } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  ImportWorkOrderPrioritySchema,
  GetWorkOrderPrioritiesSchema,
} from "@/lib/work-order-priorities/validations";
import { ApiStatus } from "@/types/common";
import {
  useWorkOrderPrioritiesData,
  useWorkOrderPriorityNameAvailability,
  useWorkOrderPriorityCodeAvailability,
  useBulkImportWorkOrderPriorities,
} from "@/lib/work-order-priorities/hooks";

// Define the work order priority field types that can be imported
export type WorkOrderPriorityImportFields =
  | "priorityCode"
  | "priorityName"
  | "description"
  | "colorCode"
  | "iconName"
  | "severityLevel"
  | "isActive"
  | "isDefault"
  | "escalationHours";


// All possible fields for work order priority import
const ALL_WORK_ORDER_PRIORITY_FIELDS: WorkOrderPriorityImportFields[] = [
  "priorityCode",
  "priorityName",
  "description",
  "colorCode",
  "iconName",
  "severityLevel",
  "isActive",
  "isDefault",
  "escalationHours",
];

export type ImportWorkOrderPrioritiesSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportWorkOrderPrioritiesSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportWorkOrderPrioritiesSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook for bulk importing work order priorities
  const bulkImportMutation = useBulkImportWorkOrderPriorities(isDemo);

  // Fetch full work order priorities data for availability checking
  const { data: fullWorkOrderPrioritiesResponse } = useWorkOrderPrioritiesData(
    {
      page: 1,
      limit: 1000, // Get all priorities for availability checking
    } as GetWorkOrderPrioritiesSchema,
    isDemo
  );
  const fullWorkOrderPriorities =
    fullWorkOrderPrioritiesResponse?.data?.data || [];

  // Field configurations for work order priorities
  const WORK_ORDER_PRIORITY_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "priorityCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter priority code (e.g., HIGH, MED, LOW)",
    },
    {
      name: "priorityName",
      type: "text",
      defaultValue: "",
      placeholder: "Enter priority name (e.g., High Priority)",
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter priority description",
    },
    {
      name: "colorCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter hex color code (e.g., #FF0000)",
    },
    {
      name: "iconName",
      type: "text",
      defaultValue: "",
      placeholder: "Enter icon name (e.g., alert-circle)",
    },
    {
      name: "severityLevel",
      type: "text",
      defaultValue: "",
      placeholder: "Enter severity level (1-10)",
    },
    {
      name: "isActive",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "true",
    },
    {
      name: "isDefault",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "false",
    },
    {
      name: "escalationHours",
      type: "text",
      defaultValue: "",
      placeholder: "Enter escalation hours (e.g., 24)",
    },
  ];

  // Enhanced validation function for work order priorities
  const validateWorkOrderPriorityRow = (
    row: Record<string, any>
  ) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (
      !row.priorityCode ||
      typeof row.priorityCode !== "string" ||
      row.priorityCode.trim() === ""
    ) {
      errors.priorityCode = "Priority code is required";
      valid = false;
    } else if (row.priorityCode.length > 50) {
      errors.priorityCode = "Priority code must be 50 characters or less";
    } else {
      // Check for duplicates in existing data
      const duplicate = fullWorkOrderPriorities.find(
        (priority) =>
          priority.priorityCode.toLowerCase() === row.priorityCode.toLowerCase()
      );
      if (duplicate) {
        errors.priorityCode = "Priority code already exists";
        valid = false;
      }
    }

    if (
      !row.priorityName ||
      typeof row.priorityName !== "string" ||
      row.priorityName.trim() === ""
    ) {
      errors.priorityName = "Priority name is required";
      valid = false;
    } else if (row.priorityName.length > 100) {
      errors.priorityName = "Priority name must be 100 characters or less";
    } else {
      // Check for duplicates in existing data
      const duplicate = fullWorkOrderPriorities.find(
        (priority) =>
          priority.priorityName.toLowerCase() === row.priorityName.toLowerCase()
      );
      if (duplicate) {
        errors.priorityName = "Priority name already exists";
        valid = false;
      }
    }

    if (!row.severityLevel || row.severityLevel === "") {
      errors.severityLevel = "Severity level is required";
      valid = false;
    } else {
      const level = Number(row.severityLevel);
      if (isNaN(level) || level < 1 || level > 10) {
        errors.severityLevel = "Severity level must be between 1 and 10";
        valid = false;
      }
    }

    // Validate optional fields
    if (row.colorCode && !/^#[0-9A-F]{6}$/i.test(row.colorCode)) {
      errors.colorCode = "Color code must be a valid hex color (e.g., #FF0000)";
      valid = false;
    }

    if (
      row.escalationHours !== undefined &&
      row.escalationHours !== null &&
      row.escalationHours !== ""
    ) {
      const hours = Number(row.escalationHours);
      if (isNaN(hours) || hours < 1) {
        errors.escalationHours = "Escalation hours must be at least 1";
        valid = false;
      }
    }

    // Validate boolean fields
    if (
      row.isActive !== undefined &&
      row.isActive !== "" &&
      row.isActive !== null
    ) {
      if (typeof row.isActive === "string") {
        const normalizedValue = row.isActive.toLowerCase().trim();
        if (
          normalizedValue !== "true" &&
          normalizedValue !== "false" &&
          normalizedValue !== "yes" &&
          normalizedValue !== "no" &&
          normalizedValue !== "1" &&
          normalizedValue !== "0"
        ) {
          errors.isActive = "Must be true/false, yes/no, or 1/0";
          valid = false;
        }
      } else if (typeof row.isActive !== "boolean") {
        errors.isActive = "Must be a boolean value";
        valid = false;
      }
    }

    if (
      row.isDefault !== undefined &&
      row.isDefault !== "" &&
      row.isDefault !== null
    ) {
      if (typeof row.isDefault === "string") {
        const normalizedValue = row.isDefault.toLowerCase().trim();
        if (
          normalizedValue !== "true" &&
          normalizedValue !== "false" &&
          normalizedValue !== "yes" &&
          normalizedValue !== "no" &&
          normalizedValue !== "1" &&
          normalizedValue !== "0"
        ) {
          errors.isDefault = "Must be true/false, yes/no, or 1/0";
          valid = false;
        }
      } else if (typeof row.isDefault !== "boolean") {
        errors.isDefault = "Must be a boolean value";
        valid = false;
      }
    }


    return { valid, errors };
  };

  // Transform row data to ImportWorkOrderPrioritySchema format
  const transformRowToWorkOrderPriority = (
    row: Record<string, any>
  ): ImportWorkOrderPrioritySchema => {
    // Handle boolean fields
    let isActive = true; // Default value
    if (
      row.isActive !== undefined &&
      row.isActive !== "" &&
      row.isActive !== null
    ) {
      if (typeof row.isActive === "boolean") {
        isActive = row.isActive;
      } else if (typeof row.isActive === "string") {
        const normalizedValue = row.isActive.toLowerCase().trim();
        isActive =
          normalizedValue === "true" ||
          normalizedValue === "yes" ||
          normalizedValue === "1";
      }
    }

    let isDefault = false; // Default value
    if (
      row.isDefault !== undefined &&
      row.isDefault !== "" &&
      row.isDefault !== null
    ) {
      if (typeof row.isDefault === "boolean") {
        isDefault = row.isDefault;
      } else if (typeof row.isDefault === "string") {
        const normalizedValue = row.isDefault.toLowerCase().trim();
        isDefault =
          normalizedValue === "true" ||
          normalizedValue === "yes" ||
          normalizedValue === "1";
      }
    }

    // Build priority data
    const priorityData: ImportWorkOrderPrioritySchema = {
      priorityCode: row.priorityCode.trim(),
      priorityName: row.priorityName.trim(),
      severityLevel: Number(row.severityLevel),
      isActive,
      isDefault,
    };

    // Add optional fields if present
    if (row.description && row.description.trim()) {
      priorityData.description = row.description.trim();
    }

    if (row.colorCode && row.colorCode.trim()) {
      priorityData.colorCode = row.colorCode.trim();
    }

    if (row.iconName && row.iconName.trim()) {
      priorityData.iconName = row.iconName.trim();
    }

    if (
      row.escalationHours !== undefined &&
      row.escalationHours !== null &&
      row.escalationHours !== ""
    ) {
      priorityData.escalationHours = Number(row.escalationHours);
    }


    return priorityData;
  };

  // Handle submission of work order priority data
  const handleSubmitWorkOrderPriorities = async (
    data: any[]
  ) => {
    try {
      setIsSubmitting(true);

      // Transform data to ImportWorkOrderPrioritySchema format
      const prioritiesData: ImportWorkOrderPrioritySchema[] = data.map(
        transformRowToWorkOrderPriority
      );

      // Use the mutation hook to import priorities
      const result = await bulkImportMutation.mutateAsync({
        workOrderPriorities: prioritiesData,
      });

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        return Promise.resolve();
      } else {
        return Promise.reject(
          new Error(result.message || "Failed to import work order priorities")
        );
      }
    } catch (error) {
      console.error("Error importing work order priorities:", error);
      return Promise.reject(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Work Order Priorities"
      description="Import work order priorities from a CSV or Excel file. Make sure your file includes the required fields: priority code, priority name, and severity level."
      targetFields={ALL_WORK_ORDER_PRIORITY_FIELDS}
      fieldConfigs={WORK_ORDER_PRIORITY_FIELD_CONFIGS}
      validateRow={validateWorkOrderPriorityRow}
      onSubmit={handleSubmitWorkOrderPriorities}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}
