"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Info, Check, X, Loader2 } from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { workOrderPriorityFormSchema } from "@/lib/work-order-priorities/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  WorkOrderPriorityTableData,
  UpdateWorkOrderPriorityDto,
} from "@/types/work-order-priority";
import { Switch } from "@/components/ui/switch";
import { IconSelector } from "@/components/ui/icon-selector";

import {
  useWorkOrderPriorityData,
  useWorkOrderPriorityNameAvailability,
  useWorkOrderPriorityCodeAvailability,
  useCreateWorkOrderPriority,
  useUpdateWorkOrderPriority,
} from "@/lib/work-order-priorities/hooks";
import { useEffect } from "react";

interface WorkOrderPrioritySheetProps {
  workOrderPriority: WorkOrderPriorityTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (workOrderPriority?: WorkOrderPriorityTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof workOrderPriorityFormSchema>;

export function WorkOrderPrioritySheet({
  workOrderPriority,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: WorkOrderPrioritySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Debounced values for availability checking
  const [debouncedName, setDebouncedName] = React.useState<string>("");
  const [debouncedCode, setDebouncedCode] = React.useState<string>("");

  // Fetch complete work order priority data if updating
  const {
    data: fullWorkOrderPriorityResponse,
    isLoading: isLoadingWorkOrderPriority,
  } = useWorkOrderPriorityData(workOrderPriority?.id || "", isDemo);
  const fullWorkOrderPriority = fullWorkOrderPriorityResponse?.data;

  useEffect(() => {
    if (fullWorkOrderPriorityResponse) {
      console.log(
        "fullWorkOrderPriorityResponse",
        fullWorkOrderPriorityResponse
      );
    }
  }, [fullWorkOrderPriorityResponse]);

  // Mutation hooks for create and update operations
  const createWorkOrderPriorityMutation = useCreateWorkOrderPriority(isDemo);
  const updateWorkOrderPriorityMutation = useUpdateWorkOrderPriority(isDemo);

  // Availability checks (only check if not updating the same work order priority)
  const shouldCheckNameAvailability =
    debouncedName.length > 0 &&
    (!isUpdate ||
      (workOrderPriority &&
        debouncedName.toLowerCase() !==
          workOrderPriority.priorityName.toLowerCase()));
  const shouldCheckCodeAvailability =
    debouncedCode.length > 0 &&
    (!isUpdate ||
      (workOrderPriority &&
        debouncedCode.toLowerCase() !==
          workOrderPriority.priorityCode.toLowerCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useWorkOrderPriorityNameAvailability(debouncedName, isDemo);
  const { data: codeAvailabilityResponse, isLoading: isCheckingCode } =
    useWorkOrderPriorityCodeAvailability(debouncedCode, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isCodeAvailable = shouldCheckCodeAvailability
    ? codeAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(workOrderPriorityFormSchema),
    defaultValues: {
      priorityCode: "",
      priorityName: "",
      description: "",
      colorCode: "",
      iconName: "",
      severityLevel: 1,
      isActive: true,
      isDefault: false,
      escalationHours: undefined,
    },
  });

  // Note: Image uploads are not supported yet for work order priorities

  React.useEffect(() => {
    if (workOrderPriority && fullWorkOrderPriority) {
      // Use full work order priority data for populating the form
      form.reset({
        priorityCode: fullWorkOrderPriority.priorityCode,
        priorityName: fullWorkOrderPriority.priorityName,
        description: fullWorkOrderPriority.description || "",
        colorCode: fullWorkOrderPriority.colorCode || "",
        iconName: fullWorkOrderPriority.iconName || "",
        severityLevel: fullWorkOrderPriority.severityLevel,
        isActive: fullWorkOrderPriority.isActive,
        isDefault: fullWorkOrderPriority.isDefault,
        escalationHours: fullWorkOrderPriority.escalationHours || undefined,
      });

      // Note: Images are not supported yet for work order priorities
    } else if (!workOrderPriority) {
      // Reset form for new work order priority
      form.reset({
        priorityCode: "",
        priorityName: "",
        description: "",
        colorCode: "",
        iconName: "",
        severityLevel: 1,
        isActive: true,
        isDefault: false,
        escalationHours: undefined,
      });
    }
  }, [workOrderPriority, fullWorkOrderPriority]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.priorityName ||
    errors.priorityCode ||
    errors.colorCode ||
    errors.severityLevel ||
    errors.isActive ||
    errors.isDefault ||
    (shouldCheckNameAvailability && isNameAvailable === false) ||
    (shouldCheckCodeAvailability && isCodeAvailable === false)
  );

  const hasDetailsErrors = !!(
    errors.description ||
    errors.iconName ||
    errors.escalationHours
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error("Priority name is already taken. Please choose a different name.");
      return;
    }

    if (shouldCheckCodeAvailability && isCodeAvailable === false) {
      toast.error("Priority code is already taken. Please choose a different code.");
      return;
    }

    setIsSubmitting(true);

    try {
      if (workOrderPriority) {
        // Update existing work order priority
        const updateData: UpdateWorkOrderPriorityDto = {
          priorityCode: data.priorityCode,
          priorityName: data.priorityName,
          description: data.description || undefined,
          colorCode: data.colorCode || undefined,
          iconName: data.iconName || undefined,
          severityLevel: data.severityLevel,
          isActive: data.isActive,
          isDefault: data.isDefault,
          escalationHours: data.escalationHours || undefined,
        };

        const response = await updateWorkOrderPriorityMutation.mutateAsync({
          id: workOrderPriority.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Work order priority updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as WorkOrderPriorityTableData);
          return;
        }
        toast.error(response.message || "Failed to update work order priority");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new work order priority
        const createData = {
          priorityCode: data.priorityCode,
          priorityName: data.priorityName,
          description: data.description || undefined,
          colorCode: data.colorCode || undefined,
          iconName: data.iconName || undefined,
          severityLevel: data.severityLevel,
          isActive: data.isActive,
          isDefault: data.isDefault,
          escalationHours: data.escalationHours || undefined,
        };

        const response = await createWorkOrderPriorityMutation.mutateAsync(createData);

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Work order priority created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as WorkOrderPriorityTableData);
          return;
        }
        toast.error(response.message || "Failed to create work order priority");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save work order priority:", error);
      toast.error("Failed to save work order priority");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debounce form values for availability checking
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      const timer = setTimeout(() => {
        if (value.priorityName !== undefined) {
          setDebouncedName(value.priorityName || "");
        }
        if (value.priorityCode !== undefined) {
          setDebouncedCode(value.priorityCode || "");
        }
      }, 500); // 500ms debounce

      return () => clearTimeout(timer);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Helper component for availability indicators
  const AvailabilityIndicator = ({
    isLoading,
    isAvailable,
    shouldCheck,
    fieldName,
  }: {
    isLoading: boolean;
    isAvailable: boolean | undefined | null;
    shouldCheck: boolean | null;
    fieldName: string;
  }) => {
    if (!shouldCheck) return null;

    if (isLoading) {
      return (
        <div className="flex items-center gap-1 text-muted-foreground text-sm mt-1">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Checking availability...</span>
        </div>
      );
    }

    if (isAvailable === true) {
      return (
        <div className="flex items-center gap-1 text-green-600 text-sm mt-1">
          <Check className="h-3 w-3" />
          <span>{fieldName} is available</span>
        </div>
      );
    }

    if (isAvailable === false) {
      return (
        <div className="flex items-center gap-1 text-red-600 text-sm mt-1">
          <X className="h-3 w-3" />
          <span>{fieldName} is already taken</span>
        </div>
      );
    }

    return null;
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Priority Name</Label>
                <Input
                  {...form.register("priorityName")}
                  name="priorityName"
                  placeholder="Enter priority name (e.g., High, Medium, Low)"
                  className={cn(
                    errors.priorityName && "border-destructive",
                    shouldCheckNameAvailability &&
                      isNameAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.priorityName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.priorityName.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingName}
                  isAvailable={isNameAvailable}
                  shouldCheck={shouldCheckNameAvailability}
                  fieldName="Priority name"
                />
              </div>

              <div>
                <Label>Priority Code</Label>
                <Input
                  {...form.register("priorityCode")}
                  name="priorityCode"
                  placeholder="Enter priority code (e.g., HIGH, MED, LOW)"
                  className={cn(
                    errors.priorityCode && "border-destructive",
                    shouldCheckCodeAvailability &&
                      isCodeAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.priorityCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.priorityCode.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingCode}
                  isAvailable={isCodeAvailable}
                  shouldCheck={shouldCheckCodeAvailability}
                  fieldName="Priority code"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Unique identifier for the priority
                </p>
              </div>

              <div>
                <Label>Severity Level</Label>
                <Input
                  {...form.register("severityLevel", { valueAsNumber: true })}
                  name="severityLevel"
                  type="number"
                  min="1"
                  max="10"
                  placeholder="Enter severity level (1-10)"
                  className={cn(errors.severityLevel && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.severityLevel && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.severityLevel.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Severity level from 1 (lowest) to 10 (highest)
                </p>
              </div>

              <div>
                <Label>Color Code</Label>
                <Input
                  {...form.register("colorCode")}
                  name="colorCode"
                  type="color"
                  placeholder="#FF0000"
                  className={cn(
                    "h-10 w-full cursor-pointer",
                    errors.colorCode && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.colorCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.colorCode.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a color to represent this priority
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Active</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this priority available for use
                  </p>
                </div>
                <Switch
                  checked={form.watch("isActive")}
                  onCheckedChange={(checked) =>
                    form.setValue("isActive", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.isActive && (
                <p className="text-sm text-destructive mt-1">
                  {errors.isActive.message}
                </p>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Default Priority</Label>
                  <p className="text-sm text-muted-foreground">
                    Set as the default priority for new work orders
                  </p>
                </div>
                <Switch
                  checked={form.watch("isDefault")}
                  onCheckedChange={(checked) =>
                    form.setValue("isDefault", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.isDefault && (
                <p className="text-sm text-destructive mt-1">
                  {errors.isDefault.message}
                </p>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter priority description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the priority
                </p>
              </div>

              <div>
                <Label>Icon</Label>
                <IconSelector
                  value={form.watch("iconName")}
                  onChange={(iconName) =>
                    form.setValue("iconName", iconName || "", {
                      shouldValidate: true,
                    })
                  }
                  placeholder="Select an icon (optional)"
                  className={cn(errors.iconName && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.iconName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.iconName.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional icon for visual representation
                </p>
              </div>

              <div>
                <Label>Escalation Hours</Label>
                <Input
                  {...form.register("escalationHours", { valueAsNumber: true })}
                  name="escalationHours"
                  type="number"
                  min="1"
                  placeholder="Enter escalation hours"
                  className={cn(errors.escalationHours && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.escalationHours && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.escalationHours.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Hours after which the work order should be escalated
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<WorkOrderPriorityTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={workOrderPriority}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Work Order Priority"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingWorkOrderPriority}
    />
  );
}
