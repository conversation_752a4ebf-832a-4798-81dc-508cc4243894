"use client";

import { useState } from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useEmployeeById,
  useCreateEmployee,
  useUpdateEmployee,
} from "@/lib/employees/hooks";
import { EmployeeDetails } from "@/types/employee";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2 } from "lucide-react";

interface EmployeeSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employeeId?: string;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function EmployeeSheet({
  open,
  onOpenChange,
  employeeId,
  onSuccess,
  isDemo = false,
}: EmployeeSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("personal");

  // Fetch employee data if editing an existing employee
  const { data: employeeResponse, isLoading: isLoadingEmployee } =
    useEmployeeById(employeeId, isDemo);

  const employee = employeeResponse?.data;

  // Get mutations
  const { mutateAsync: createEmployee } = useCreateEmployee(isDemo);
  const { mutateAsync: updateEmployee } = useUpdateEmployee(isDemo);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // In a real component, you would gather form data here
      // For this example, we'll just show a success message

      if (employeeId) {
        // Update existing employee
        await updateEmployee({
          id: employeeId,
          data: {
            // Include updated data here
          },
        });
        toast.success("Employee updated successfully");
      } else {
        // Create new employee
        await createEmployee({
          // Include new employee data here
        });
        toast.success("Employee created successfully");
      }

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

      // Close the sheet
      onOpenChange(false);
    } catch (error) {
      console.error("Error saving employee:", error);
      toast.error("Failed to save employee. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-2xl overflow-y-auto">
        <SheetHeader>
          <SheetTitle>
            {employeeId ? "Edit Employee" : "Add New Employee"}
          </SheetTitle>
          <SheetDescription>
            {employeeId
              ? "Update the employee details below"
              : "Fill in the employee details below"}
          </SheetDescription>
        </SheetHeader>

        {isLoadingEmployee && employeeId ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading employee data...</span>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6 py-6">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-4">
                <TabsTrigger value="personal">Personal</TabsTrigger>
                <TabsTrigger value="employment">Employment</TabsTrigger>
                <TabsTrigger value="payroll">Payroll</TabsTrigger>
                <TabsTrigger value="skills">Skills</TabsTrigger>
              </TabsList>

              <TabsContent value="personal" className="pt-4">
                <div className="text-sm text-muted-foreground">
                  Personal information form would go here
                </div>
              </TabsContent>

              <TabsContent value="employment" className="pt-4">
                <div className="text-sm text-muted-foreground">
                  Employment information form would go here
                </div>
              </TabsContent>

              <TabsContent value="payroll" className="pt-4">
                <div className="text-sm text-muted-foreground">
                  Payroll information form would go here
                </div>
              </TabsContent>

              <TabsContent value="skills" className="pt-4">
                <div className="text-sm text-muted-foreground">
                  Skills and qualifications form would go here
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex items-center justify-end space-x-2 pt-4">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : employeeId ? (
                  "Update Employee"
                ) : (
                  "Create Employee"
                )}
              </Button>
            </div>
          </form>
        )}
      </SheetContent>
    </Sheet>
  );
}
