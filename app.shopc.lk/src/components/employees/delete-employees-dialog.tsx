"use client";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useDeleteEmployee } from "@/lib/employees/hooks";
import { useState } from "react";

interface DeleteEmployeesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employeesToDelete: string[];
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteEmployeesDialog({
  open,
  onOpenChange,
  employeesToDelete,
  onSuccess,
  isDemo = false,
}: DeleteEmployeesDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const { mutateAsync: deleteEmployee } = useDeleteEmployee(isDemo);

  const handleDelete = async () => {
    if (!employeesToDelete.length) return;

    setIsDeleting(true);

    try {
      // Delete each employee in the array
      for (const employeeId of employeesToDelete) {
        await deleteEmployee(employeeId);
      }

      // Show success toast
      toast.success(`${
          employeesToDelete.length === 1 ? "Employee" : "Employees"
        } deleted successfully.`);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error deleting employee(s):", error);
      toast.error("Failed to delete employee(s). Please try again.");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            Delete Employee{employeesToDelete.length > 1 ? "s" : ""}
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete{" "}
            {employeesToDelete.length === 1
              ? "this employee"
              : `these ${employeesToDelete.length} employees`}
            ? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
