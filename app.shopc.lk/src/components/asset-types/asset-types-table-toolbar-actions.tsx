"use client";

import { type Table } from "@tanstack/react-table";
import { AssetTypeSheet } from "./asset-type-sheet";
import { ImportAssetTypesSheet } from "./import-asset-types-sheet";
import { AssetTypesHierarchySheet } from "./asset-types-hierarchy-sheet";
import { BaseTableToolbarActions } from "@/components/shared/base-table-toolbar-actions";
import { useState } from "react";
import { AssetTypeListDto } from "@/types/asset-type";

interface AssetTypesTableToolbarActionsProps {
  table: Table<AssetTypeListDto>;
  onRefresh?: () => Promise<void>;
  rowSelection: boolean;
  onRowSelectionChange: (value: boolean) => void;
  isDemo?: boolean;
}

export function AssetTypesTableToolbarActions({
  table,
  onRefresh,
  rowSelection,
  onRowSelectionChange,
  isDemo = false,
}: AssetTypesTableToolbarActionsProps) {
  const [open, setOpen] = useState(false);
  const [importOpen, setImportOpen] = useState(false);
  const [hierarchyOpen, setHierarchyOpen] = useState(false);

  return (
    <>
      <BaseTableToolbarActions<AssetTypeListDto>
        table={table}
        onRefresh={onRefresh}
        rowSelection={rowSelection}
        onRowSelectionChange={onRowSelectionChange}
        isDemo={isDemo}
        title="Asset Types"
        addButton={
          <AssetTypeSheet
            onSuccess={() => onRefresh?.()}
            isDemo={isDemo}
            assetType={null}
            open={open}
            onOpenChange={setOpen}
          />
        }
        additionalActions={
          <>
            <ImportAssetTypesSheet
              open={importOpen}
              onOpenChange={setImportOpen}
              onSuccess={() => {
                onRefresh?.();
                setImportOpen(false);
              }}
              isDemo={isDemo}
            />
            <AssetTypesHierarchySheet
              open={hierarchyOpen}
              onOpenChange={setHierarchyOpen}
              isDemo={isDemo}
              onSuccess={() => onRefresh?.()}
            />
          </>
        }
      />
    </>
  );
}