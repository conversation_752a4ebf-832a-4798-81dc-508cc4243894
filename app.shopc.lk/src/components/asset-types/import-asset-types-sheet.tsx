/**
 * ImportAssetTypesSheet Component
 *
 * This component allows importing asset types with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Parent asset type selection for hierarchy
 * - Field validation and error handling
 * - Integration with bulk import API
 * - Support for asset type categories (Physical/Digital)
 * - Reference type and ID tracking for auto-created asset types
 */

"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  ImportAssetTypeSchema,
  GetAssetTypesSchema,
} from "@/lib/asset-types/validations";
import { AssetTypeStatus, AssetTypeCategory } from "@/types/asset-type";
import { ApiStatus } from "@/types/common";
import {
  useAssetTypesSlim,
  useAssetTypesData,
  useAssetTypeNameAvailability,
  useBulkImportAssetTypes,
} from "@/lib/asset-types/hooks";

// Define the asset type field types that can be imported
export type AssetTypeImportFields =
  | "name"
  | "description"
  | "parentId"
  | "category"
  | "referenceId"
  | "referenceType"
  | "status";

// Required fields for asset type import
const REQUIRED_ASSET_TYPE_FIELDS: AssetTypeImportFields[] = ["name"];

// All possible fields for asset type import
const ALL_ASSET_TYPE_FIELDS: AssetTypeImportFields[] = [
  "name",
  "description",
  "parentId",
  "category",
  "referenceId",
  "referenceType",
  "status",
];

export type ImportAssetTypesSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportAssetTypesSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportAssetTypesSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook for bulk importing asset types
  const bulkImportMutation = useBulkImportAssetTypes(isDemo);

  // Fetch asset types for parent selection (slim data)
  const { data: assetTypesResponse } = useAssetTypesSlim(isDemo);
  const assetTypes = assetTypesResponse?.data || [];

  // Fetch full asset types data for availability checking
  const { data: fullAssetTypesResponse } = useAssetTypesData(
    {
      page: 1,
      perPage: 1000, // Get all asset types for availability checking
    } as GetAssetTypesSchema,
    isDemo
  );
  const fullAssetTypes = fullAssetTypesResponse?.data?.data || [];

  // Track validation state for availability checking
  const [currentValidatingName, setCurrentValidatingName] =
    useState<string>("");

  // Debounced availability checking to prevent infinite loops
  const debouncedCheckAvailability = useCallback(
    (field: string, value: string) => {
      if (!value || !value.trim()) return;

      const trimmedValue = value.trim();

      // Only trigger if not already checking this value
      setTimeout(() => {
        switch (field) {
          case "name":
            if (currentValidatingName !== trimmedValue) {
              setCurrentValidatingName(trimmedValue);
            }
            break;
        }
      }, 300); // 300ms debounce
    },
    [currentValidatingName]
  );

  // Use availability hooks for current validating values
  const { data: nameAvailability, isLoading: isLoadingNameCheck } =
    useAssetTypeNameAvailability(currentValidatingName, isDemo);

  // Build a cache of checked availability
  const [availabilityCache, setAvailabilityCache] = useState<{
    names: Map<string, boolean>;
  }>({
    names: new Map(),
  });

  // Update cache when availability results come in
  useEffect(() => {
    if (
      currentValidatingName &&
      nameAvailability?.data?.available !== undefined
    ) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.names.set(
          currentValidatingName.toLowerCase(),
          nameAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingName, nameAvailability]);

  // Helper function to check for duplicates within the dataset
  const checkForInternalDuplicates = (
    data: Record<string, any>[],
    currentIndex: number,
    field: string,
    value: string
  ): boolean => {
    if (!value || !value.trim()) return false;

    const normalizedValue = value.trim().toLowerCase();

    return data.some((row, index) => {
      if (index === currentIndex) return false; // Skip current row
      if (!row[field] || !row[field].trim()) return false;

      const otherValue = row[field].trim().toLowerCase();
      return otherValue === normalizedValue;
    });
  };

  // Helper function to check if a field is currently being validated (loading)
  const isFieldLoading = (field: string, value: string): boolean => {
    if (!value || !value.trim()) return false;

    const trimmedValue = value.trim();
    switch (field) {
      case "name":
        return currentValidatingName === trimmedValue && isLoadingNameCheck;
      default:
        return false;
    }
  };

  // Helper function to check availability against existing data using hooks
  const checkAvailabilityAgainstExisting = (
    field: string,
    value: string
  ): boolean => {
    if (!value || !value.trim()) return true;

    const normalizedValue = value.trim().toLowerCase();

    // First check the availability cache from hooks
    let cachedResult: boolean | undefined;
    switch (field) {
      case "name":
        cachedResult = availabilityCache.names.get(normalizedValue);
        break;
    }

    // If we have a cached result, use it
    if (cachedResult !== undefined) {
      return cachedResult;
    }

    // Check against existing asset types data as fallback
    return fullAssetTypes.every((assetType) => {
      if (!assetType[field as keyof typeof assetType]) return true;

      const existingValue = (
        assetType[field as keyof typeof assetType] as string
      ).toLowerCase();

      return existingValue !== normalizedValue;
    });
  };

  // Field configurations for asset types
  const ASSET_TYPE_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "name",
      type: "text",
      defaultValue: "",
      placeholder: "Enter asset type name (e.g., Laptops, Software Licenses)",
      isLoading: (value: string) => isFieldLoading("name", value),
      onValueChange: (value: string) =>
        debouncedCheckAvailability("name", value),
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter asset type description",
    },
    {
      name: "parentId",
      type: "select",
      options: [
        { value: "none", label: "No Parent (Root Asset Type)" },
        ...assetTypes.map((assetType) => ({
          value: assetType.id,
          label: assetType.name,
        })),
      ],
      defaultValue: "none",
    },
    {
      name: "category",
      type: "select",
      options: [
        { value: "physical", label: "Physical" },
        { value: "digital", label: "Digital" },
      ],
      defaultValue: "physical",
    },
    {
      name: "referenceId",
      type: "text",
      defaultValue: "",
      placeholder: "Reference ID (for auto-created asset types)",
    },
    {
      name: "referenceType",
      type: "select",
      options: [
        { value: "none", label: "No Reference (Manual Creation)" },
        { value: "rental-item-category", label: "Rental Item Category" },
        { value: "vehicle-category", label: "Vehicle Category" },
      ],
      defaultValue: "none",
    },
    {
      name: "status",
      type: "select",
      options: [
        { value: "active", label: "Active" },
        { value: "inactive", label: "Inactive" },
      ],
      defaultValue: "active",
    },
  ];

  // Enhanced validation function for asset types with uniqueness checks
  const validateAssetTypeRow = (
    row: Record<string, any>,
    rowIndex?: number,
    allData?: Record<string, any>[]
  ) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (!row.name || typeof row.name !== "string" || row.name.trim() === "") {
      errors.name = "Name is required";
      valid = false;
    } else {
      // Check uniqueness within dataset
      if (allData && rowIndex !== undefined) {
        if (checkForInternalDuplicates(allData, rowIndex, "name", row.name)) {
          errors.name = "Name must be unique within the import data";
          valid = false;
        }
      }

      // Check availability against existing data
      if (!checkAvailabilityAgainstExisting("name", row.name)) {
        errors.name = "Asset type name already exists";
        valid = false;
      }
    }

    // Validate description if present
    if (
      row.description !== undefined &&
      row.description !== null &&
      row.description !== ""
    ) {
      if (typeof row.description !== "string") {
        errors.description = "Description must be a string";
        valid = false;
      }
    }

    // Validate parentId if present
    if (
      row.parentId !== undefined &&
      row.parentId !== null &&
      row.parentId !== "" &&
      row.parentId !== "none"
    ) {
      if (typeof row.parentId !== "string") {
        errors.parentId = "Parent ID must be a string";
        valid = false;
      } else {
        // Check if parentId exists in asset types
        const parentExists = assetTypes.some((at) => at.id === row.parentId);
        if (!parentExists) {
          errors.parentId = "Parent asset type not found";
          valid = false;
        }
      }
    }

    // Validate category if present
    if (row.category && typeof row.category === "string") {
      const normalizedCategory = row.category.toLowerCase().trim();
      if (normalizedCategory !== "physical" && normalizedCategory !== "digital") {
        errors.category = "Category must be 'physical' or 'digital'";
        valid = false;
      }
    }

    // Validate referenceId if present
    if (
      row.referenceId !== undefined &&
      row.referenceId !== null &&
      row.referenceId !== ""
    ) {
      if (typeof row.referenceId !== "string") {
        errors.referenceId = "Reference ID must be a string";
        valid = false;
      }
    }

    // Validate referenceType if present
    if (
      row.referenceType !== undefined &&
      row.referenceType !== null &&
      row.referenceType !== "" &&
      row.referenceType !== "none"
    ) {
      if (typeof row.referenceType !== "string") {
        errors.referenceType = "Reference type must be a string";
        valid = false;
      } else {
        const validTypes = ["rental-item-category", "vehicle-category"];
        if (!validTypes.includes(row.referenceType)) {
          errors.referenceType = "Invalid reference type";
          valid = false;
        }
      }
    }

    // Validate status if present
    if (row.status && typeof row.status === "string") {
      const normalizedStatus = row.status.toLowerCase().trim();
      if (normalizedStatus !== "active" && normalizedStatus !== "inactive") {
        errors.status = "Status must be 'active' or 'inactive'";
        valid = false;
      }
    }

    return { valid, errors };
  };

  // Transform row data to ImportAssetTypeSchema format
  const transformRowToAssetType = (
    row: Record<string, any>
  ): ImportAssetTypeSchema => {
    // Handle category - could be string
    let category = AssetTypeCategory.PHYSICAL; // Default value

    if (row.category && typeof row.category === "string") {
      const normalizedCategory = row.category.toLowerCase().trim();
      if (normalizedCategory === "digital") {
        category = AssetTypeCategory.DIGITAL;
      }
    }

    // Handle status - could be string
    let status = AssetTypeStatus.ACTIVE; // Default value

    if (row.status && typeof row.status === "string") {
      const normalizedStatus = row.status.toLowerCase().trim();
      if (normalizedStatus === "inactive") {
        status = AssetTypeStatus.INACTIVE;
      }
    }

    // Build asset type data
    const assetTypeData: ImportAssetTypeSchema = {
      name: row.name.trim(),
      category,
      status,
    };

    // Add optional fields if present
    if (row.description && row.description.trim()) {
      assetTypeData.description = row.description.trim();
    }

    if (row.parentId && row.parentId.trim() && row.parentId !== "none") {
      assetTypeData.parentId = row.parentId.trim();
    }

    if (row.referenceId && row.referenceId.trim()) {
      assetTypeData.referenceId = row.referenceId.trim();
    }

    if (
      row.referenceType && 
      row.referenceType.trim() && 
      row.referenceType !== "none"
    ) {
      assetTypeData.referenceType = row.referenceType.trim() as any;
    }

    return assetTypeData;
  };

  // Handle submission of asset type data
  const handleSubmitAssetTypes = async (
    data: any[],
    mappings: FieldMapping[]
  ) => {
    try {
      setIsSubmitting(true);

      // Transform data to ImportAssetTypeSchema format
      const assetTypesData: ImportAssetTypeSchema[] = data.map(
        transformRowToAssetType
      );

      // Use the mutation hook to import asset types
      const result = await bulkImportMutation.mutateAsync({
        assetTypes: assetTypesData,
      });

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        return Promise.resolve();
      } else {
        return Promise.reject(
          new Error(result.message || "Failed to import asset types")
        );
      }
    } catch (error) {
      console.error("Error importing asset types:", error);
      return Promise.reject(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Asset Types"
      description="Import asset types from a CSV or Excel file. Make sure your file includes the required fields. You can create asset type hierarchies by specifying parent asset types."
      targetFields={ALL_ASSET_TYPE_FIELDS}
      fieldConfigs={ASSET_TYPE_FIELD_CONFIGS}
      validateRow={validateAssetTypeRow}
      onSubmit={handleSubmitAssetTypes}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}