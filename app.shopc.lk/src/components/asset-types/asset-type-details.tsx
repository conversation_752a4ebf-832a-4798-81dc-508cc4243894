"use client";

import * as React from "react";
import { BaseSheet } from "@/components/shared/base-sheet";
import { AssetTypeDetailsContent } from "./asset-type-details-content";

interface AssetTypeDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assetTypeId?: string;
  isDemo?: boolean;
}

export function AssetTypeDetails({
  open,
  onOpenChange,
  assetTypeId,
  isDemo = false,
}: AssetTypeDetailsProps) {
  return (
    <BaseSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Asset Type Details"
      description="View detailed information about this asset type."
      className="sm:max-w-2xl"
    >
      {assetTypeId && (
        <AssetTypeDetailsContent assetTypeId={assetTypeId} isDemo={isDemo} />
      )}
    </BaseSheet>
  );
}