"use client";

import * as React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Package, 
  Monitor, 
  FolderTree, 
  Calendar, 
  User, 
  FileText, 
  Activity,
  Hash,
  Link
} from "lucide-react";
import { useAssetTypeData } from "@/lib/asset-types/hooks";
import { AssetTypeCategoryBadge } from "./asset-type-category-badge";
import { AssetTypeStatusBadge } from "./asset-type-status-badge";
import {
  formatAssetTypeReferenceType,
  getAssetTypeDisplayName,
} from "@/lib/asset-types/utils";

interface AssetTypeDetailsContentProps {
  assetTypeId: string;
  isDemo?: boolean;
}

export function AssetTypeDetailsContent({
  assetTypeId,
  isDemo = false,
}: AssetTypeDetailsContentProps) {
  const { data: assetTypeResponse, isLoading, error } = useAssetTypeData(assetTypeId, isDemo);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        <Separator />
        <div className="grid grid-cols-2 gap-4">
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
          <Skeleton className="h-20" />
        </div>
      </div>
    );
  }

  if (error || !assetTypeResponse?.data) {
    return (
      <div className="flex items-center justify-center py-8">
        <p className="text-muted-foreground">
          Failed to load asset type details. Please try again.
        </p>
      </div>
    );
  }

  const assetType = assetTypeResponse.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center h-10 w-10 rounded-full bg-muted">
                {assetType.category === "physical" ? (
                  <Package className="h-5 w-5 text-blue-600" />
                ) : (
                  <Monitor className="h-5 w-5 text-purple-600" />
                )}
              </div>
              <div>
                <h2 className="text-2xl font-semibold">{assetType.name}</h2>
                {assetType.description && (
                  <p className="text-muted-foreground mt-1">
                    {assetType.description}
                  </p>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <AssetTypeCategoryBadge category={assetType.category} />
            <AssetTypeStatusBadge
              assetTypeId={assetType.id}
              status={assetType.status}
              isDemo={isDemo}
              disabled={true}
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Overview Cards */}
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{assetType.assetsCount}</div>
            <p className="text-xs text-muted-foreground">
              Assets using this type
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Subcategories</CardTitle>
            <FolderTree className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {/* This would need to be added to the API response */}
              0
            </div>
            <p className="text-xs text-muted-foreground">
              Child asset types
            </p>
          </CardContent>
        </Card>
      </div>

      <Separator />

      {/* Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Details</h3>
        
        <div className="grid grid-cols-1 gap-4">
          {/* ID */}
          <div className="flex items-center gap-3">
            <Hash className="h-4 w-4 text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm font-medium">ID</p>
              <p className="text-sm text-muted-foreground font-mono">
                {assetType.id}
              </p>
            </div>
          </div>

          {/* Parent Type */}
          {assetType.parentId && (
            <div className="flex items-center gap-3">
              <FolderTree className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <p className="text-sm font-medium">Parent Asset Type</p>
                <p className="text-sm text-muted-foreground">
                  {/* This would need parent name from API */}
                  Parent Type
                </p>
              </div>
            </div>
          )}

          {/* Reference Information */}
          {assetType.referenceId && assetType.referenceType && (
            <>
              <div className="flex items-center gap-3">
                <Link className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Source</p>
                  <p className="text-sm text-muted-foreground">
                    {formatAssetTypeReferenceType(assetType.referenceType)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <p className="text-sm font-medium">Reference ID</p>
                  <p className="text-sm text-muted-foreground font-mono">
                    {assetType.referenceId}
                  </p>
                </div>
              </div>
            </>
          )}

          {/* Created Information */}
          <div className="flex items-center gap-3">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm font-medium">Created</p>
              <p className="text-sm text-muted-foreground">
                {new Date(assetType.createdAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </p>
            </div>
          </div>

          {/* Created By */}
          <div className="flex items-center gap-3">
            <User className="h-4 w-4 text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm font-medium">Created By</p>
              <p className="text-sm text-muted-foreground">
                {assetType.createdBy}
              </p>
            </div>
          </div>

          {/* Last Updated */}
          <div className="flex items-center gap-3">
            <Activity className="h-4 w-4 text-muted-foreground" />
            <div className="flex-1">
              <p className="text-sm font-medium">Last Updated</p>
              <p className="text-sm text-muted-foreground">
                {new Date(assetType.updatedAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </p>
            </div>
          </div>

          {/* Updated By */}
          {assetType.updatedBy && (
            <div className="flex items-center gap-3">
              <User className="h-4 w-4 text-muted-foreground" />
              <div className="flex-1">
                <p className="text-sm font-medium">Updated By</p>
                <p className="text-sm text-muted-foreground">
                  {assetType.updatedBy}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}