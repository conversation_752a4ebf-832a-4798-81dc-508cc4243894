"use client";

import { Badge } from "@/components/ui/badge";
import { AssetTypeCategory } from "@/types/asset-type";
import { 
  formatAssetTypeCategory, 
  getAssetTypeCategoryColor 
} from "@/lib/asset-types/utils";
import { Monitor, Package } from "lucide-react";

interface AssetTypeCategoryBadgeProps {
  category: AssetTypeCategory;
  showIcon?: boolean;
  className?: string;
}

export function AssetTypeCategoryBadge({
  category,
  showIcon = true,
  className,
}: AssetTypeCategoryBadgeProps) {
  const getIcon = () => {
    switch (category) {
      case AssetTypeCategory.PHYSICAL:
        return <Package className="h-3 w-3" />;
      case AssetTypeCategory.DIGITAL:
        return <Monitor className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const getBadgeVariant = () => {
    switch (category) {
      case AssetTypeCategory.PHYSICAL:
        return "secondary" as const;
      case AssetTypeCategory.DIGITAL:
        return "outline" as const;
      default:
        return "secondary" as const;
    }
  };

  const getBadgeClass = () => {
    switch (category) {
      case AssetTypeCategory.PHYSICAL:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case AssetTypeCategory.DIGITAL:
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Badge
      variant={getBadgeVariant()}
      className={`${getBadgeClass()} ${className} flex items-center gap-1`}
    >
      {showIcon && getIcon()}
      <span>{formatAssetTypeCategory(category)}</span>
    </Badge>
  );
}