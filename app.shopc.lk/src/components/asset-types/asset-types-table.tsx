"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getAssetTypesTableData } from "@/lib/asset-types/queries";
import { DeleteAssetTypesDialog } from "./delete-asset-types-dialog";
import { AssetTypesTableToolbarActions } from "./asset-types-table-toolbar-actions";
import { AssetTypesTableFloatingBar } from "./asset-types-table-floating-bar";
import { AssetTypeSheet } from "./asset-type-sheet";
import { AssetTypeDetails } from "./asset-type-details";
import { AssetTypeDetailsContent } from "./asset-type-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./asset-types-table-columns";
import { AssetTypeListDto, AssetTypeStatus } from "@/types/asset-type";
import { useAssetTypesData } from "@/lib/asset-types/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { assetTypeKeys } from "@/lib/asset-types/hooks";
import { updateAssetTypePositions } from "@/lib/asset-types/queries";
import { ApiStatus } from "@/types/common";

interface AssetTypesTableProps {
  isDemo?: boolean;
}

export function AssetTypesTable({ isDemo = false }: AssetTypesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<AssetTypeListDto> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<AssetTypeListDto>[] = React.useMemo(
    () => [
      {
        id: "name",
        label: "Name",
        placeholder: "Filter by name...",
      },
      // status
      {
        id: "status",
        label: "Status",
        placeholder: "Filter by status...",
        type: "select",
        options: [
          { label: "Active", value: AssetTypeStatus.ACTIVE },
          { label: "Inactive", value: AssetTypeStatus.INACTIVE },
        ],
      },
    ],
    []
  );

  const advancedFilterFields: DataTableAdvancedFilterField<AssetTypeListDto>[] =
    React.useMemo(
      () => [
        {
          id: "name",
          label: "Name",
          type: "text",
        },
        // description
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        // status
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Active", value: AssetTypeStatus.ACTIVE },
            { label: "Inactive", value: AssetTypeStatus.INACTIVE },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<AssetTypeListDto>[] =
    React.useMemo(
      () => [
        {
          id: "name",
          label: "Name",
          type: "text",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<AssetTypeListDto>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      name: searchParams.get("name") || "",
      description: searchParams.get("description") || "",
      status: searchParams.get("status") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: assetTypesData,
    isLoading,
    isFetching,
  } = useAssetTypesData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.name ||
      searchParamsValues.description ||
      searchParamsValues.status ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  // Handle reordering
  const handleReorder = React.useCallback(
    async (updates: { id: string; position: number }[]) => {
      try {
        const result = await updateAssetTypePositions(updates, isDemo);
        return {
          status: result.status === ApiStatus.SUCCESS ? "success" : "error",
          message: result.message || undefined,
        };
      } catch (error) {
        return {
          status: "error",
          message: "Failed to reorder asset types",
        };
      }
    },
    [isDemo]
  );

  const handleRefresh = React.useCallback(
    async (assetTypeIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() }),
        queryClient.invalidateQueries({ queryKey: assetTypeKeys.simple() }),
      ];

      // If specific asset type IDs are provided, invalidate their detail cache as well
      if (assetTypeIds && assetTypeIds.length > 0) {
        assetTypeIds.forEach((assetTypeId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: assetTypeKeys.detail(assetTypeId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  // Custom dialog content renderer for row clicks
  const renderAssetTypeDetails = React.useCallback(
    (assetType: AssetTypeListDto) => {
      return <AssetTypeDetailsContent assetTypeId={assetType.id} isDemo={isDemo} />;
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        AssetTypeListDto,
        Awaited<ReturnType<typeof getAssetTypesTableData>>
      >
        data={assetTypesData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={true}
        onReorder={handleReorder}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => true}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          isSortableEnabled: true,
          onStatusUpdate: (assetTypeId?: string) => {
            queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
            queryClient.invalidateQueries({ queryKey: assetTypeKeys.simple() });
            if (assetTypeId) {
              queryClient.invalidateQueries({
                queryKey: assetTypeKeys.detail(assetTypeId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <AssetTypesTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <AssetTypesTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderAssetTypeDetails}
      />
      <AssetTypeDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        assetTypeId={rowAction?.row.original?.id}
        isDemo={isDemo}
      />
      <AssetTypeSheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        assetType={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const assetTypeId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
          queryClient.invalidateQueries({ queryKey: assetTypeKeys.simple() });
          if (assetTypeId) {
            queryClient.invalidateQueries({
              queryKey: assetTypeKeys.detail(assetTypeId),
            });
          }
        }}
        isUpdate={true}
      />
      <DeleteAssetTypesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        assetTypes={rowAction?.row.original ? [rowAction.row.original] : []}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
          queryClient.invalidateQueries({ queryKey: assetTypeKeys.simple() });
        }}
        isDemo={isDemo}
      />
    </>
  );
}