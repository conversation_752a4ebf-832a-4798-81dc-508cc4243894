"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  FolderTree,
  Check,
  X,
  Loader2,
  Package,
  Monitor,
} from "lucide-react";
import { type z } from "zod";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { assetTypeFormSchema } from "@/lib/asset-types/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  AssetTypeListDto,
  AssetTypeStatus,
  AssetTypeCategory,
  UpdateAssetTypeDto,
} from "@/types/asset-type";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useAssetTypesSlim,
  useAssetTypeData,
  useAssetTypeNameAvailability,
  useCreateAssetType,
  useUpdateAssetType,
} from "@/lib/asset-types/hooks";
import { useEffect } from "react";

interface AssetTypeSheetProps {
  assetType: AssetTypeListDto | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (assetType?: AssetTypeListDto) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof assetTypeFormSchema>;

export function AssetTypeSheet({
  assetType,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: AssetTypeSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Values for availability checking
  const [checkName, setCheckName] = React.useState<string>("");

  // Fetch complete asset type data if updating
  const { data: fullAssetTypeResponse, isLoading: isLoadingAssetType } =
    useAssetTypeData(assetType?.id || "", isDemo);
  const fullAssetType = fullAssetTypeResponse?.data;

  useEffect(() => {
    if (fullAssetTypeResponse) {
      console.log("fullAssetTypeResponse", fullAssetTypeResponse);
    }
  }, [fullAssetTypeResponse]);

  // Fetch asset types for parent selection
  const { data: assetTypesResponse } = useAssetTypesSlim(isDemo);
  const assetTypes = React.useMemo(
    () => assetTypesResponse?.data || [],
    [assetTypesResponse?.data]
  );

  // Mutation hooks for create and update operations
  const createAssetTypeMutation = useCreateAssetType(isDemo);
  const updateAssetTypeMutation = useUpdateAssetType(isDemo);

  // Availability checks (only check if not updating the same asset type)
  const shouldCheckNameAvailability =
    checkName.length > 0 &&
    (!isUpdate ||
      (assetType && checkName.toLowerCase() !== assetType.name.toLowerCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useAssetTypeNameAvailability(checkName, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;

  // Filter available parent asset types (exclude current asset type and potential circular references)
  const availableParentAssetTypes = React.useMemo(() => {
    return assetTypes.filter((at) => {
      // In update mode, exclude the current asset type from parent options
      if (isUpdate && assetType && at.id === assetType.id) {
        return false;
      }
      // TODO: Add logic to exclude descendants to prevent circular references
      return true;
    });
  }, [assetTypes, isUpdate, assetType]);

  const form = useForm<FormData>({
    resolver: zodResolver(assetTypeFormSchema),
    defaultValues: {
      name: "",
      description: "",
      parentId: null,
      category: AssetTypeCategory.PHYSICAL,
      status: AssetTypeStatus.ACTIVE,
    },
  });

  React.useEffect(() => {
    if (assetType && fullAssetType) {
      // Use full asset type data for populating the form
      const parentId = fullAssetType.parentId || null;

      form.reset({
        name: fullAssetType.name,
        description: fullAssetType.description || "",
        parentId: parentId,
        category: fullAssetType.category,
        status: fullAssetType.status as AssetTypeStatus,
      });
    } else if (!assetType) {
      // Reset form for new asset type
      form.reset({
        name: "",
        description: "",
        parentId: null,
        category: AssetTypeCategory.PHYSICAL,
        status: AssetTypeStatus.ACTIVE,
      });
    }
  }, [assetType, fullAssetType]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.name ||
    errors.category ||
    errors.status ||
    (shouldCheckNameAvailability && isNameAvailable === false)
  );

  const hasHierarchyErrors = !!errors.parentId;

  const hasDetailsErrors = !!(
    errors.description
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Asset type name is already taken. Please choose a different name."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Helper function to convert null to undefined for parentId
      const getParentId = (
        parentId: string | null | undefined
      ): string | undefined => {
        return parentId === null ? undefined : parentId || undefined;
      };

      if (assetType) {
        // Update existing asset type
        const updateData: UpdateAssetTypeDto = {
          name: data.name,
          description: data.description || undefined,
          parentId: getParentId(data.parentId),
          category: data.category,
          status: data.status,
        };

        const response = await updateAssetTypeMutation.mutateAsync({
          id: assetType.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Asset type updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as AssetTypeListDto);
          return;
        }
        toast.error(response.message || "Failed to update asset type");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new asset type
        const createData = {
          name: data.name,
          description: data.description || undefined,
          parentId: getParentId(data.parentId),
          category: data.category,
          status: data.status,
        };

        const response = await createAssetTypeMutation.mutateAsync(createData);

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Asset type created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as AssetTypeListDto);
          return;
        }
        toast.error(response.message || "Failed to create asset type");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save asset type:", error);
      toast.error("Failed to save asset type");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle availability checks
  const handleCheckNameAvailability = (value: string) => {
    setCheckName(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="name"
                label="Name"
                placeholder="Enter asset type name (e.g., Laptops, Software Licenses)"
                value={form.watch("name") || ""}
                onChange={(value) =>
                  form.setValue("name", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckNameAvailability}
                isCheckingAvailability={isCheckingName}
                isAvailable={isNameAvailable}
                shouldCheck={shouldCheckNameAvailability}
                error={errors.name?.message}
                disabled={isSubmitting}
                fieldName="Name"
              />

              <div>
                <Label>Category</Label>
                <Select
                  value={form.watch("category")}
                  onValueChange={(value) =>
                    form.setValue(
                      "category",
                      value as AssetTypeCategory.PHYSICAL | AssetTypeCategory.DIGITAL,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.category && "border-destructive")}
                  >
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AssetTypeCategory.PHYSICAL}>
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-blue-600" />
                        <span>Physical</span>
                      </div>
                    </SelectItem>
                    <SelectItem value={AssetTypeCategory.DIGITAL}>
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4 text-purple-600" />
                        <span>Digital</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.category.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose whether this represents physical or digital assets
                </p>
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue(
                      "status",
                      value as AssetTypeStatus.ACTIVE | AssetTypeStatus.INACTIVE,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AssetTypeStatus.ACTIVE}>
                      Active
                    </SelectItem>
                    <SelectItem value={AssetTypeStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "hierarchy",
      title: "Asset Type Hierarchy",
      icon: <FolderTree className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasHierarchyErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FolderTree className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Asset Type Hierarchy</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Parent Asset Type</Label>
                <Select
                  value={form.watch("parentId") || "none"}
                  onValueChange={(value) =>
                    form.setValue("parentId", value === "none" ? null : value, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.parentId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select parent asset type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      No Parent (Root Asset Type)
                    </SelectItem>
                    {availableParentAssetTypes
                      .filter((at) => at.id && at.id.trim() !== "") // Filter out asset types with empty IDs
                      .map((at) => (
                        <SelectItem
                          key={at.id}
                          value={at.id}
                          disabled={assetType?.id === at.id} // Don't allow self as parent
                        >
                          {at.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                {errors.parentId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.parentId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a parent asset type to create a hierarchy
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter asset type description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the asset type
                </p>
              </div>

            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<AssetTypeListDto, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={assetType}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Asset Type"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingAssetType}
      // Location props - disabled for asset types
      isLocationEnabled={false}
      // SEO props - disabled for asset types
      isSEOEnabled={false}
    />
  );
}