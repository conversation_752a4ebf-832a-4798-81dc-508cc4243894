"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, Check<PERSON><PERSON>cle2, XCircle } from "lucide-react";
import { toast } from "sonner";

import { type AssetTypeListDto, AssetTypeStatus } from "@/types/asset-type";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteAssetTypes,
  useBulkUpdateAssetTypeStatus,
} from "@/lib/asset-types/hooks";
import { ApiStatus } from "@/types/common";

interface AssetTypesTableFloatingBarProps {
  table: Table<AssetTypeListDto>;
  onRefresh?: (assetTypeIds?: string[]) => void;
  isDemo?: boolean;
}

export function AssetTypesTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: AssetTypesTableFloatingBarProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "activate" | "deactivate"
  >();

  const bulkDeleteMutation = useBulkDeleteAssetTypes(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateAssetTypeStatus(isDemo);

  const isPending =
    bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedAssetTypes = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedAssetTypes.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedAssetTypes.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: AssetTypeStatus) => {
      if (selectionCount === 0) return;

      setAction(status === AssetTypeStatus.ACTIVE ? "activate" : "deactivate");

      try {
        const assetTypeIds = selectedAssetTypes.map((assetType) => assetType.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          assetTypeIds,
          status,
        });

        const failedUpdates = result.filter(
          (res: any) => res.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
          toast.success(
            `Successfully ${
              status === AssetTypeStatus.ACTIVE ? "activated" : "deactivated"
            } ${selectionCount} asset type${selectionCount > 1 ? "s" : ""}`
          );
          table.toggleAllRowsSelected(false);
          onRefresh?.(assetTypeIds);
        } else {
          toast.error(
            `Failed to update ${failedUpdates.length} asset type${
              failedUpdates.length > 1 ? "s" : ""
            }`
          );
        }
      } catch (error) {
        console.error("Error updating asset types:", error);
        toast.error("Failed to update asset types");
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedAssetTypes,
      selectionCount,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No asset types selected" };

    try {
      const assetTypeIds = selectedAssetTypes.map((assetType) => assetType.id);
      const result = await bulkDeleteMutation.mutateAsync(assetTypeIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete asset types",
        };
      }
    } catch (error: any) {
      console.error("Error deleting asset types:", error);
      return { error: error.message || "Failed to delete asset types" };
    }
  }, [selectedAssetTypes, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(AssetTypeStatus.ACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(AssetTypeStatus.INACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<AssetTypeListDto>
        table={table}
        title="Asset Types"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} asset type${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass asset type IDs since they're deleted
        }}
      />
    </>
  );
}