"use client";

import * as React from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Loader2, AlertTriangle, Package, FolderTree } from "lucide-react";
import { AssetTypeListDto } from "@/types/asset-type";
import { useDeleteAssetType, useBulkDeleteAssetTypes } from "@/lib/asset-types/hooks";
import { ApiStatus } from "@/types/common";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { getAssetTypeDeletionWarning } from "@/lib/asset-types/utils";

interface DeleteAssetTypesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assetTypes: AssetTypeListDto[];
  isDemo?: boolean;
  onSuccess?: () => void;
}

export function DeleteAssetTypesDialog({
  open,
  onOpenChange,
  assetTypes,
  isDemo = false,
  onSuccess,
}: DeleteAssetTypesDialogProps) {
  const singleDeleteMutation = useDeleteAssetType(isDemo);
  const bulkDeleteMutation = useBulkDeleteAssetTypes(isDemo);

  const isSingle = assetTypes.length === 1;
  const isLoading = singleDeleteMutation.isPending || bulkDeleteMutation.isPending;

  // Check which asset types can be deleted
  const { canDelete, cannotDelete } = React.useMemo(() => {
    const canDelete = assetTypes.filter(
      (assetType) =>
        assetType.subcategoriesCount === 0 && assetType.assetsCount === 0
    );
    const cannotDelete = assetTypes.filter(
      (assetType) =>
        assetType.subcategoriesCount > 0 || assetType.assetsCount > 0
    );
    return { canDelete, cannotDelete };
  }, [assetTypes]);

  const handleDelete = async () => {
    try {
      if (canDelete.length === 0) {
        toast.error("No asset types can be deleted");
        return;
      }

      if (isSingle) {
        const result = await singleDeleteMutation.mutateAsync(canDelete[0].id);
        
        if (result.status === ApiStatus.SUCCESS) {
          toast.success("Asset type deleted successfully");
          onOpenChange(false);
          onSuccess?.();
        } else {
          toast.error(result.message || "Failed to delete asset type");
        }
      } else {
        const result = await bulkDeleteMutation.mutateAsync(
          canDelete.map((at) => at.id)
        );
        
        if (result.status === ApiStatus.SUCCESS && result.data) {
          toast.success(
            `Successfully deleted ${result.data.deleted} asset types`
          );
          onOpenChange(false);
          onSuccess?.();
        } else {
          toast.error(result.message || "Failed to delete asset types");
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred");
    }
  };

  const getDialogTitle = () => {
    if (isSingle) {
      return `Delete Asset Type`;
    }
    return `Delete ${assetTypes.length} Asset Types`;
  };

  const getDialogDescription = () => {
    if (cannotDelete.length === 0) {
      if (isSingle) {
        return `Are you sure you want to delete "${assetTypes[0].name}"? This action cannot be undone.`;
      }
      return `Are you sure you want to delete ${assetTypes.length} asset types? This action cannot be undone.`;
    } else {
      if (canDelete.length === 0) {
        return "The selected asset types cannot be deleted because they have assets or subcategories.";
      }
      return `${canDelete.length} of ${assetTypes.length} asset types can be deleted. Asset types with assets or subcategories cannot be deleted.`;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Show asset types that can be deleted */}
          {canDelete.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-green-700">
                Can be deleted ({canDelete.length}):
              </h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {canDelete.map((assetType) => (
                  <div
                    key={assetType.id}
                    className="flex items-center justify-between p-2 bg-green-50 rounded-lg"
                  >
                    <span className="text-sm font-medium">{assetType.name}</span>
                    <Badge variant="outline" className="text-green-700 border-green-300">
                      Safe to delete
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Show asset types that cannot be deleted */}
          {cannotDelete.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-destructive">
                Cannot be deleted ({cannotDelete.length}):
              </h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {cannotDelete.map((assetType) => {
                  const warning = getAssetTypeDeletionWarning(assetType);
                  return (
                    <Alert key={assetType.id} variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="space-y-1">
                          <div className="font-medium">{assetType.name}</div>
                          <div className="text-xs">{warning}</div>
                          <div className="flex items-center gap-2 text-xs">
                            {assetType.assetsCount > 0 && (
                              <div className="flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                <span>{assetType.assetsCount} assets</span>
                              </div>
                            )}
                            {assetType.subcategoriesCount > 0 && (
                              <div className="flex items-center gap-1">
                                <FolderTree className="h-3 w-3" />
                                <span>{assetType.subcategoriesCount} subcategories</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </AlertDescription>
                    </Alert>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          {canDelete.length > 0 && (
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete {canDelete.length > 1 ? `${canDelete.length} ` : ""}Asset Type{canDelete.length > 1 ? "s" : ""}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}