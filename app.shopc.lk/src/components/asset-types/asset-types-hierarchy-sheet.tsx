"use client";

import * as React from "react";
import { BaseSheet } from "@/components/shared/base-sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  FolderTree, 
  Package, 
  Monitor, 
  ChevronRight,
  Loader2,
  RotateCcw
} from "lucide-react";
import { 
  useAssetTypesHierarchyData,
  useBulkUpdateAssetTypeHierarchy 
} from "@/lib/asset-types/hooks";
import { AssetTypeHierarchyData, AssetTypeCategory } from "@/types/asset-type";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { buildAssetTypeHierarchy } from "@/lib/asset-types/utils";

interface AssetTypesHierarchySheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function AssetTypesHierarchySheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: AssetTypesHierarchySheetProps) {
  const { data: hierarchyResponse, isLoading, refetch } = useAssetTypesHierarchyData(isDemo);
  const updateHierarchyMutation = useBulkUpdateAssetTypeHierarchy(isDemo);

  const [pendingChanges, setPendingChanges] = React.useState<
    Array<{ id: string; parentId: string | null }>
  >([]);

  const hierarchy = hierarchyResponse?.data || [];

  // Build tree structure
  const treeData = React.useMemo(() => {
    if (!hierarchy.length) return [];
    
    const rootItems: (AssetTypeHierarchyData & { children?: AssetTypeHierarchyData[] })[] = [];
    const itemMap = new Map<string, AssetTypeHierarchyData & { children?: AssetTypeHierarchyData[] }>();

    // First pass: create all items
    hierarchy.forEach(item => {
      itemMap.set(item.id, { ...item, children: [] });
    });

    // Second pass: build tree
    hierarchy.forEach(item => {
      const itemWithChildren = itemMap.get(item.id)!;
      
      if (item.parentId && itemMap.has(item.parentId)) {
        const parent = itemMap.get(item.parentId)!;
        parent.children = parent.children || [];
        parent.children.push(itemWithChildren);
      } else {
        rootItems.push(itemWithChildren);
      }
    });

    return rootItems;
  }, [hierarchy]);

  const handleSaveChanges = async () => {
    if (pendingChanges.length === 0) {
      toast.info("No changes to save");
      return;
    }

    try {
      const result = await updateHierarchyMutation.mutateAsync({
        updates: pendingChanges,
      });

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast.success(
          `Successfully updated ${result.data.updated} asset type hierarchies`
        );
        
        if (result.data.failed && result.data.failed.length > 0) {
          toast.warning(
            `${result.data.failed.length} updates failed`
          );
        }

        setPendingChanges([]);
        onSuccess?.();
        refetch();
      } else {
        toast.error(result.message || "Failed to update hierarchy");
      }
    } catch (error) {
      toast.error("Failed to update asset type hierarchy");
    }
  };

  const handleResetChanges = () => {
    setPendingChanges([]);
    toast.info("Changes reset");
  };

  const renderTreeItem = (
    item: AssetTypeHierarchyData & { children?: AssetTypeHierarchyData[] },
    level: number = 0
  ): React.ReactNode => {
    const hasChildren = item.children && item.children.length > 0;
    
    return (
      <div key={item.id} className="space-y-2">
        <div 
          className="flex items-center gap-2 p-2 rounded-lg border bg-card"
          style={{ marginLeft: `${level * 20}px` }}
        >
          <div className="flex items-center gap-2 flex-1">
            {level > 0 && (
              <div className="flex items-center">
                <ChevronRight className="h-3 w-3 text-muted-foreground" />
              </div>
            )}
            
            <div className="flex items-center justify-center h-6 w-6 rounded bg-muted">
              <Package className="h-3 w-3 text-blue-600" />
            </div>
            
            <span className="font-medium">{item.name}</span>
            
            {level === 0 && (
              <Badge variant="outline" className="text-xs">
                Root
              </Badge>
            )}
          </div>
          
          {hasChildren && (
            <Badge variant="secondary" className="text-xs">
              {item.children!.length} children
            </Badge>
          )}
        </div>
        
        {hasChildren && (
          <div className="space-y-2">
            {item.children!.map(child => renderTreeItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Asset Types Hierarchy"
      description="View and manage the hierarchical structure of your asset types."
      className="sm:max-w-4xl"
    >
      <div className="space-y-6">
        {/* Stats */}
        <div className="grid grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Types</CardTitle>
              <FolderTree className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{hierarchy.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Root Types</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{treeData.length}</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Changes</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pendingChanges.length}</div>
            </CardContent>
          </Card>
        </div>

        <Separator />

        {/* Actions */}
        {pendingChanges.length > 0 && (
          <div className="flex items-center justify-between p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 bg-amber-500 rounded-full animate-pulse" />
              <span className="text-sm font-medium text-amber-800">
                You have {pendingChanges.length} unsaved changes
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetChanges}
                disabled={updateHierarchyMutation.isPending}
              >
                <RotateCcw className="mr-2 h-4 w-4" />
                Reset
              </Button>
              <Button
                size="sm"
                onClick={handleSaveChanges}
                disabled={updateHierarchyMutation.isPending}
              >
                {updateHierarchyMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Save Changes
              </Button>
            </div>
          </div>
        )}

        {/* Hierarchy Tree */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Hierarchy Structure</h3>
          
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : treeData.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <FolderTree className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Asset Types Found</h3>
              <p className="text-muted-foreground">
                Create some asset types to see the hierarchy structure.
              </p>
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {treeData.map(item => renderTreeItem(item))}
            </div>
          )}
        </div>

        {/* Info */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <FolderTree className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Hierarchy Management</p>
              <p>
                This view shows the current hierarchical structure of your asset types. 
                Root asset types are shown at the top level, with their children indented below them.
              </p>
            </div>
          </div>
        </div>
      </div>
    </BaseSheet>
  );
}