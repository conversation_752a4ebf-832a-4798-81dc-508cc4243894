"use client";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AssetTypeStatus } from "@/types/asset-type";
import * as demoApi from "@/lib/asset-types/demo";
import * as api from "@/lib/asset-types/api";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { Loader2, Check } from "lucide-react";
import { useState } from "react";

interface AssetTypeStatusBadgeProps {
  assetTypeId: string;
  status: AssetTypeStatus;
  isDemo?: boolean;
  disabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (assetTypeId: string, newStatus: AssetTypeStatus) => void;
}

export function AssetTypeStatusBadge({
  assetTypeId,
  status,
  isDemo = false,
  disabled = false,
  onRefresh,
  onStatusUpdate,
}: AssetTypeStatusBadgeProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (newStatus: AssetTypeStatus) => {
    // Don't make API call if status is already the same
    if (newStatus === status) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    setIsUpdating(true);

    try {
      const result = isDemo
        ? await demoApi.updateDemoAssetTypeApi(assetTypeId, {
            status: newStatus as
              | AssetTypeStatus.ACTIVE
              | AssetTypeStatus.INACTIVE,
          })
        : await api.updateAssetTypeApi(assetTypeId, {
            status: newStatus as
              | AssetTypeStatus.ACTIVE
              | AssetTypeStatus.INACTIVE,
          });

      if (result.status === ApiStatus.SUCCESS) {
        toast.success(
          `Asset type status updated to ${
            newStatus === AssetTypeStatus.ACTIVE ? "Active" : "Inactive"
          }`
        );
        // Use the new status update handler if available
        if (onStatusUpdate) {
          onStatusUpdate(assetTypeId, newStatus);
        } else {
          // Fallback to onRefresh for backward compatibility
          onRefresh?.();
        }
      } else {
        toast.error(result.message || "Failed to update status");
      }
    } catch (error) {
      toast.error("Failed to update asset type status");
    } finally {
      setIsUpdating(false);
    }
  };

  const statusOptions = [
    {
      value: AssetTypeStatus.ACTIVE,
      label: "Active",
      variant: "default" as const,
    },
    {
      value: AssetTypeStatus.INACTIVE,
      label: "Inactive",
      variant: "outline" as const,
    },
  ];

  return (
    <DropdownMenu open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Badge
          variant={status === AssetTypeStatus.ACTIVE ? "default" : "outline"}
          className={`${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer hover:opacity-80"
          } transition-opacity`}
        >
          {isUpdating ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          ) : (
            <span>
              {status === AssetTypeStatus.ACTIVE ? "Active" : "Inactive"}
            </span>
          )}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[120px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleStatusUpdate(option.value)}
            disabled={isUpdating || disabled}
            className="flex items-center justify-between"
          >
            <span>{option.label}</span>
            {status === option.value && <Check className="h-3 w-3" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}