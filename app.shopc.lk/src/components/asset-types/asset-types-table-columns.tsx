"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, GripVertical, Eye, Package, FolderTree } from "lucide-react";
import { AssetTypeListDto, AssetTypeStatus } from "@/types/asset-type";
import { AssetTypeStatusBadge } from "./asset-type-status-badge";
import { SortableDragHandle } from "@/components/ui/sortable";
import { Badge } from "@/components/ui/badge";
import { formatAssetTypeReferenceType } from "@/lib/asset-types/utils";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  isSortableEnabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (assetTypeId: string, newStatus: AssetTypeStatus) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  isSortableEnabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<AssetTypeListDto>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10, // Set a fixed small width for the checkbox column
    },
    {
      id: "drag-handle",
      header: () => <div className="w-8"></div>,
      cell: ({ row }) => {
        if (!isSortableEnabled) {
          return (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 cursor-not-allowed opacity-50"
              disabled={true}
            >
              <GripVertical className="h-4 w-4 text-muted-foreground" />
              <span className="sr-only">Drag handle (disabled)</span>
            </Button>
          );
        }

        return (
          <SortableDragHandle
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            disabled={isActionsDisabled}
          >
            <GripVertical className="h-4 w-4 text-muted-foreground" />
            <span className="sr-only">Drag to reorder</span>
          </SortableDragHandle>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 32, // Fixed width for drag handle
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Asset Type Name" />
      ),
      cell: ({ row }) => {
        const assetTypeName = row.getValue("name") as string;

        return (
          <div className="flex items-center gap-3 pl-2">
            <div className="flex items-center justify-center h-8 w-8 rounded-full bg-muted">
              <Package className="h-4 w-4 text-blue-600" />
            </div>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <span className="font-medium">{assetTypeName}</span>
              </div>
              {row.original.description && (
                <div className="text-xs text-muted-foreground max-w-[200px] truncate">
                  {row.original.description}
                </div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "parentName",
      header: () => <div className="font-medium">Parent Type</div>,
      cell: ({ row }) => {
        const parentName = row.getValue("parentName") as string;
        return (
          <div className="pl-2">
            {parentName ? (
              <div className="flex items-center gap-1">
                <FolderTree className="h-3 w-3 text-muted-foreground" />
                <span className="text-sm">{parentName}</span>
              </div>
            ) : (
              <span className="text-xs text-muted-foreground">
                Root Type
              </span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "assetsCount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Number of Assets" />
      ),
      cell: ({ row }) => {
        const count = row.getValue("assetsCount") as number;
        return (
          <div className="pl-2">
            <span className="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
              {count} Assets
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "subcategoriesCount",
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="Number of Subtypes"
        />
      ),
      cell: ({ row }) => {
        const count = row.getValue("subcategoriesCount") as number;
        return (
          <div className="pl-2">
            <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
              {count}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "referenceType",
      header: () => <div className="font-medium">Source</div>,
      cell: ({ row }) => {
        const referenceType = row.original.referenceType;
        const referenceId = row.original.referenceId;
        
        return (
          <div className="pl-2">
            {referenceType && referenceId ? (
              <Badge variant="outline" className="text-xs">
                {formatAssetTypeReferenceType(referenceType)}
              </Badge>
            ) : (
              <span className="text-xs text-muted-foreground">Manual</span>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const status = row.getValue("status") as AssetTypeStatus;
        return (
          <AssetTypeStatusBadge
            assetTypeId={row.original.id}
            status={status}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const createdAt = row.getValue("createdAt") as Date;
        return (
          <div className="pl-2">
            <span className="text-sm text-muted-foreground">
              {new Date(createdAt).toLocaleDateString()}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}