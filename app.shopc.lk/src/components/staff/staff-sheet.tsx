"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Image as ImageIcon,
  Check,
  X,
  Loader2,
  User,
  Phone,
  Calendar,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { staffFormSchema } from "@/lib/staff/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  StaffTableData,
  EmploymentStatus,
  EmploymentType,
  UpdateStaffMemberDto,
} from "@/types/staff";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import { FileText, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  useStaffMemberData,
  useStaffDisplayNameAvailability,
  useStaffEmailAvailability,
  useStaffEmployeeIdAvailability,
  useCreateStaffMember,
  useUpdateStaffMember,
} from "@/lib/staff/hooks";
import { useDepartmentsSlim } from "@/lib/departments/hooks";
import { useSimpleDesignations } from "@/lib/designations/hooks";
import { useEffect } from "react";

interface StaffSheetProps {
  staff: StaffTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (staff?: StaffTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof staffFormSchema>;

export function StaffSheet({
  staff,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: StaffSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);

  // Document type for staff documents
  interface StaffDocument {
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
    uploadedAt: Date;
    file?: File; // Optional file property for new uploads
  }

  const [documentFiles, setDocumentFiles] = React.useState<StaffDocument[]>([]);

  // Debounced values for availability checking
  const [debouncedDisplayName, setDebouncedDisplayName] =
    React.useState<string>("");
  const [debouncedEmail, setDebouncedEmail] = React.useState<string>("");
  const [debouncedEmployeeId, setDebouncedEmployeeId] =
    React.useState<string>("");

  // Fetch complete staff data if updating
  const { data: fullStaffResponse, isLoading: isLoadingStaff } =
    useStaffMemberData(staff?.id || "", isDemo);
  const fullStaff = fullStaffResponse?.data;

  // Fetch departments and designations for dropdowns
  const { data: departmentsResponse } = useDepartmentsSlim(isDemo);
  const { data: designationsResponse } = useSimpleDesignations(isDemo);
  const departments = departmentsResponse?.data || [];
  const designations = designationsResponse?.data || [];

  useEffect(() => {
    if (fullStaffResponse) {
      console.log("fullStaffResponse", fullStaffResponse);
    }
  }, [fullStaffResponse]);

  // Mutation hooks for create and update operations
  const createStaffMutation = useCreateStaffMember(isDemo);
  const updateStaffMutation = useUpdateStaffMember(isDemo);

  // Availability checks (only check if not updating the same staff)
  const shouldCheckDisplayNameAvailability =
    debouncedDisplayName.length > 0 &&
    (!isUpdate ||
      (staff &&
        debouncedDisplayName.toLowerCase() !==
          staff.displayName.toLowerCase()));
  const shouldCheckEmailAvailability =
    debouncedEmail.length > 0 &&
    (!isUpdate ||
      (staff && debouncedEmail.toLowerCase() !== staff.email.toLowerCase()));
  const shouldCheckEmployeeIdAvailability =
    debouncedEmployeeId.length > 0 &&
    (!isUpdate ||
      (staff &&
        debouncedEmployeeId.toUpperCase() !== staff.employeeId?.toUpperCase()));

  const {
    data: displayNameAvailabilityResponse,
    isLoading: isCheckingDisplayName,
  } = useStaffDisplayNameAvailability(debouncedDisplayName, isDemo);
  const { data: emailAvailabilityResponse, isLoading: isCheckingEmail } =
    useStaffEmailAvailability(debouncedEmail, isDemo);
  const {
    data: employeeIdAvailabilityResponse,
    isLoading: isCheckingEmployeeId,
  } = useStaffEmployeeIdAvailability(debouncedEmployeeId, isDemo);

  const isDisplayNameAvailable = shouldCheckDisplayNameAvailability
    ? displayNameAvailabilityResponse?.data?.available ?? true
    : true;
  const isEmailAvailable = shouldCheckEmailAvailability
    ? emailAvailabilityResponse?.data?.available ?? true
    : true;
  const isEmployeeIdAvailable = shouldCheckEmployeeIdAvailability
    ? employeeIdAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(staffFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      displayName: "",
      email: "",
      phone: "",
      isUser: false,
      employeeId: "",
      dateOfBirth: "",
      dateOfJoining: "",
      employmentType: EmploymentType.FULL_TIME,
      departmentId: "",
      designationId: "",
      address: {
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "",
      },
      status: EmploymentStatus.ACTIVE,
    },
  });

  // Get the selected profile image file for API calls (only if it's a real file, not the mock one for existing images)
  const selectedProfileImage =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  // Get the selected document files for API calls
  const selectedDocuments = documentFiles
    .filter((file) => !file.id.startsWith("existing-") && file.file)
    .map((file) => file.file!);

  React.useEffect(() => {
    if (staff && fullStaff) {
      // Use full staff data for populating the form
      form.reset({
        firstName: fullStaff.firstName,
        lastName: fullStaff.lastName,
        displayName: fullStaff.displayName,
        email: fullStaff.email,
        phone: fullStaff.phone || "",
        isUser: fullStaff.isUser,
        employeeId: fullStaff.employeeId || "",
        dateOfBirth: fullStaff.dateOfBirth
          ? new Date(fullStaff.dateOfBirth).toISOString().split("T")[0]
          : "",
        dateOfJoining: fullStaff.dateOfJoining
          ? new Date(fullStaff.dateOfJoining).toISOString().split("T")[0]
          : "",
        employmentType: fullStaff.employmentType,
        departmentId: fullStaff.departmentId || "",
        designationId: fullStaff.designationId || "",
        address: {
          street: "",
          city: "",
          state: "",
          zipCode: "",
          country: "",
        },
        status: fullStaff.status,
      });

      // Set existing profile image as uploaded file for preview
      if (fullStaff.profileImageUrl) {
        setUploadedFiles([
          {
            id: `existing-${fullStaff.id}`,
            file: new File([], fullStaff.displayName + "-profile", {
              type: "image/jpeg",
            }),
            preview: fullStaff.profileImageUrl,
          },
        ]);
      } else {
        setUploadedFiles([]);
      }

      // Set existing documents as uploaded files for preview
      if (fullStaff.documents && fullStaff.documents.length > 0) {
        setDocumentFiles(
          fullStaff.documents.map((doc) => ({
            id: `existing-doc-${doc.id}`,
            name: doc.fileName,
            size: doc.fileSize,
            type: doc.mimeType,
            url: doc.fileUrl,
            uploadedAt: new Date(doc.createdAt),
          }))
        );
      } else {
        setDocumentFiles([]);
      }
    } else if (!staff) {
      // Reset form for new staff
      form.reset();
      setUploadedFiles([]);
      setDocumentFiles([]);
    }
  }, [staff, fullStaff]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setDocumentFiles([]);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.firstName ||
    errors.lastName ||
    errors.displayName ||
    errors.email ||
    errors.employmentType ||
    errors.status ||
    (shouldCheckDisplayNameAvailability && isDisplayNameAvailable === false) ||
    (shouldCheckEmailAvailability && isEmailAvailable === false) ||
    (shouldCheckEmployeeIdAvailability && isEmployeeIdAvailable === false)
  );

  const hasEmploymentErrors = !!(
    errors.employeeId ||
    errors.dateOfBirth ||
    errors.dateOfJoining ||
    errors.departmentId ||
    errors.designationId
  );

  const hasContactErrors = !!(errors.phone || errors.address);

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (
      shouldCheckDisplayNameAvailability &&
      isDisplayNameAvailable === false
    ) {
      toast.error(
        "Display name is already taken. Please choose a different name."
      );
      return;
    }

    if (shouldCheckEmailAvailability && isEmailAvailable === false) {
      toast.error("Email is already taken. Please choose a different email.");
      return;
    }

    if (shouldCheckEmployeeIdAvailability && isEmployeeIdAvailable === false) {
      toast.error(
        "Employee ID is already taken. Please choose a different ID."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (staff) {
        // Update existing staff
        const updateData: UpdateStaffMemberDto = {
          firstName: data.firstName,
          lastName: data.lastName,
          displayName: data.displayName,
          email: data.email,
          phone: data.phone || undefined,
          isUser: data.isUser,
          employeeId: data.employeeId || undefined,
          dateOfBirth: data.dateOfBirth || undefined,
          dateOfJoining: data.dateOfJoining || undefined,
          employmentType: data.employmentType,
          departmentId: data.departmentId || undefined,
          designationId: data.designationId || undefined,
          address:
            data.address &&
            data.address.street &&
            data.address.city &&
            data.address.state &&
            data.address.zipCode &&
            data.address.country
              ? data.address
              : undefined,
          status: data.status,
        };

        const response = await updateStaffMutation.mutateAsync({
          id: staff.id,
          data: updateData,
          profileImage: selectedProfileImage || undefined,
          documents:
            selectedDocuments.length > 0 ? selectedDocuments : undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Staff member updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as StaffTableData);
          return;
        }
        toast.error(response.message || "Failed to update staff member");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new staff
        const createData = {
          firstName: data.firstName,
          lastName: data.lastName,
          displayName: data.displayName,
          email: data.email,
          phone: data.phone || undefined,
          isUser: data.isUser,
          employeeId: data.employeeId || undefined,
          dateOfBirth: data.dateOfBirth || undefined,
          dateOfJoining: data.dateOfJoining || undefined,
          employmentType: data.employmentType,
          departmentId: data.departmentId || undefined,
          designationId: data.designationId || undefined,
          address:
            data.address &&
            data.address.street &&
            data.address.city &&
            data.address.state &&
            data.address.zipCode &&
            data.address.country
              ? data.address
              : undefined,
          status: data.status,
        };

        const response = await createStaffMutation.mutateAsync({
          data: createData,
          profileImage: selectedProfileImage || undefined,
          documents:
            selectedDocuments.length > 0 ? selectedDocuments : undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Staff member created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as StaffTableData);
          return;
        }
        toast.error(response.message || "Failed to create staff member");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save staff member:", error);
      toast.error("Failed to save staff member");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-generate display name from first and last name
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (
        (name === "firstName" || name === "lastName") &&
        value.firstName &&
        value.lastName &&
        !form.getValues("displayName")
      ) {
        form.setValue("displayName", `${value.firstName} ${value.lastName}`);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Debounce form values for availability checking
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      const timer = setTimeout(() => {
        if (value.displayName !== undefined) {
          setDebouncedDisplayName(value.displayName || "");
        }
        if (value.email !== undefined) {
          setDebouncedEmail(value.email || "");
        }
        if (value.employeeId !== undefined) {
          setDebouncedEmployeeId(value.employeeId || "");
        }
      }, 500); // 500ms debounce

      return () => clearTimeout(timer);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Helper component for availability indicators
  const AvailabilityIndicator = ({
    isLoading,
    isAvailable,
    shouldCheck,
    fieldName,
  }: {
    isLoading: boolean;
    isAvailable: boolean | undefined | null;
    shouldCheck: boolean | null;
    fieldName: string;
  }) => {
    if (!shouldCheck) return null;

    if (isLoading) {
      return (
        <div className="flex items-center gap-1 text-muted-foreground text-sm mt-1">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Checking availability...</span>
        </div>
      );
    }

    if (isAvailable === true) {
      return (
        <div className="flex items-center gap-1 text-green-600 text-sm mt-1">
          <Check className="h-3 w-3" />
          <span>{fieldName} is available</span>
        </div>
      );
    }

    if (isAvailable === false) {
      return (
        <div className="flex items-center gap-1 text-red-600 text-sm mt-1">
          <X className="h-3 w-3" />
          <span>{fieldName} is already taken</span>
        </div>
      );
    }

    return null;
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <User className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>First Name</Label>
                <Input
                  {...form.register("firstName")}
                  name="firstName"
                  placeholder="Enter first name"
                  className={cn(errors.firstName && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.firstName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.firstName.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Last Name</Label>
                <Input
                  {...form.register("lastName")}
                  name="lastName"
                  placeholder="Enter last name"
                  className={cn(errors.lastName && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.lastName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.lastName.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Display Name</Label>
                <Input
                  {...form.register("displayName")}
                  name="displayName"
                  placeholder="Enter display name (auto-generated from first and last name)"
                  className={cn(
                    errors.displayName && "border-destructive",
                    shouldCheckDisplayNameAvailability &&
                      isDisplayNameAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.displayName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.displayName.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingDisplayName}
                  isAvailable={isDisplayNameAvailable}
                  shouldCheck={shouldCheckDisplayNameAvailability}
                  fieldName="Display name"
                />
              </div>

              <div>
                <Label>Email</Label>
                <Input
                  {...form.register("email")}
                  name="email"
                  type="email"
                  placeholder="Enter email address"
                  className={cn(
                    errors.email && "border-destructive",
                    shouldCheckEmailAvailability &&
                      isEmailAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.email && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.email.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingEmail}
                  isAvailable={isEmailAvailable}
                  shouldCheck={shouldCheckEmailAvailability}
                  fieldName="Email"
                />
              </div>

              <div>
                <Label>Employment Type</Label>
                <Select
                  value={form.watch("employmentType")}
                  onValueChange={(value) =>
                    form.setValue("employmentType", value as EmploymentType, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(
                      errors.employmentType && "border-destructive"
                    )}
                  >
                    <SelectValue placeholder="Select employment type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={EmploymentType.FULL_TIME}>
                      Full Time
                    </SelectItem>
                    <SelectItem value={EmploymentType.PART_TIME}>
                      Part Time
                    </SelectItem>
                    <SelectItem value={EmploymentType.CONTRACT}>
                      Contract
                    </SelectItem>
                    <SelectItem value={EmploymentType.INTERN}>
                      Intern
                    </SelectItem>
                    <SelectItem value={EmploymentType.CONSULTANT}>
                      Consultant
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.employmentType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.employmentType.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue("status", value as EmploymentStatus, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={EmploymentStatus.ACTIVE}>
                      Active
                    </SelectItem>
                    <SelectItem value={EmploymentStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                    <SelectItem value={EmploymentStatus.TERMINATED}>
                      Terminated
                    </SelectItem>
                    <SelectItem value={EmploymentStatus.ON_LEAVE}>
                      On Leave
                    </SelectItem>
                    <SelectItem value={EmploymentStatus.PROBATION}>
                      Probation
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Create User Account</Label>
                  <p className="text-sm text-muted-foreground">
                    Create a user account for this staff member
                  </p>
                </div>
                <Switch
                  checked={form.watch("isUser")}
                  onCheckedChange={(checked) =>
                    form.setValue("isUser", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "employment-details",
      title: "Employment Details",
      icon: <Calendar className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasEmploymentErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Employment Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Employee ID</Label>
                <Input
                  {...form.register("employeeId")}
                  name="employeeId"
                  placeholder="Enter employee ID (optional)"
                  className={cn(
                    errors.employeeId && "border-destructive",
                    shouldCheckEmployeeIdAvailability &&
                      isEmployeeIdAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.employeeId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.employeeId.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingEmployeeId}
                  isAvailable={isEmployeeIdAvailable}
                  shouldCheck={shouldCheckEmployeeIdAvailability}
                  fieldName="Employee ID"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Optional unique identifier for the employee
                </p>
              </div>

              <div>
                <DatePicker
                  name="dateOfBirth"
                  label="Date of Birth"
                  value={form.watch("dateOfBirth") && form.watch("dateOfBirth") !== "" ? new Date(form.watch("dateOfBirth")!) : undefined}
                  onChange={(date) => {
                    const dateString = date ? date.toISOString().split("T")[0] : "";
                    form.setValue("dateOfBirth", dateString, { shouldValidate: true });
                  }}
                  disabled={isSubmitting}
                  error={errors.dateOfBirth?.message}
                  placeholder="Select date of birth"
                />
              </div>

              <div>
                <DatePicker
                  name="dateOfJoining"
                  label="Date of Joining"
                  value={form.watch("dateOfJoining") && form.watch("dateOfJoining") !== "" ? new Date(form.watch("dateOfJoining")!) : undefined}
                  onChange={(date) => {
                    const dateString = date ? date.toISOString().split("T")[0] : "";
                    form.setValue("dateOfJoining", dateString, { shouldValidate: true });
                  }}
                  disabled={isSubmitting}
                  error={errors.dateOfJoining?.message}
                  placeholder="Select date of joining"
                />
              </div>

              <div>
                <Label>Department</Label>
                <Select
                  value={form.watch("departmentId") || "none"}
                  onValueChange={(value) =>
                    form.setValue(
                      "departmentId",
                      value === "none" ? undefined : value,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.departmentId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select department (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Department</SelectItem>
                    {departments.map((department) => (
                      <SelectItem key={department.id} value={department.id}>
                        {department.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.departmentId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.departmentId.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Designation</Label>
                <Select
                  value={form.watch("designationId") || "none"}
                  onValueChange={(value) =>
                    form.setValue(
                      "designationId",
                      value === "none" ? undefined : value,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.designationId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select designation (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Designation</SelectItem>
                    {designations.map((designation) => (
                      <SelectItem key={designation.id} value={designation.id}>
                        {designation.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.designationId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.designationId.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "contact-info",
      title: "Contact Information",
      icon: <Phone className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasContactErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Phone className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Contact Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Phone</Label>
                <Input
                  {...form.register("phone")}
                  name="phone"
                  placeholder="Enter phone number (optional)"
                  className={cn(errors.phone && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.phone && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.phone.message}
                  </p>
                )}
              </div>

              <div className="space-y-4">
                <Label className="text-base font-medium">Address</Label>

                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label>Street Address</Label>
                    <Input
                      {...form.register("address.street")}
                      placeholder="Enter street address"
                      className={cn(
                        errors.address?.street && "border-destructive"
                      )}
                      disabled={isSubmitting}
                    />
                    {errors.address?.street && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.address.street.message}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>City</Label>
                      <Input
                        {...form.register("address.city")}
                        placeholder="Enter city"
                        className={cn(
                          errors.address?.city && "border-destructive"
                        )}
                        disabled={isSubmitting}
                      />
                      {errors.address?.city && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.address.city.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label>State/Province</Label>
                      <Input
                        {...form.register("address.state")}
                        placeholder="Enter state/province"
                        className={cn(
                          errors.address?.state && "border-destructive"
                        )}
                        disabled={isSubmitting}
                      />
                      {errors.address?.state && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.address.state.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>ZIP/Postal Code</Label>
                      <Input
                        {...form.register("address.zipCode")}
                        placeholder="Enter ZIP/postal code"
                        className={cn(
                          errors.address?.zipCode && "border-destructive"
                        )}
                        disabled={isSubmitting}
                      />
                      {errors.address?.zipCode && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.address.zipCode.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label>Country</Label>
                      <Input
                        {...form.register("address.country")}
                        placeholder="Enter country"
                        className={cn(
                          errors.address?.country && "border-destructive"
                        )}
                        disabled={isSubmitting}
                      />
                      {errors.address?.country && (
                        <p className="text-sm text-destructive mt-1">
                          {errors.address.country.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "profile-image",
      title: "Profile Image",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Profile Image</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Profile Image"
              description="Upload a profile image for this staff member. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
    {
      id: "documents",
      title: "Documents",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Documents</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Staff Documents</Label>
                <div>
                  <input
                    type="file"
                    id="document-upload"
                    className="hidden"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      files.forEach(async (file) => {
                        try {
                          // Create a temporary document object for display
                          const fakeUrl = URL.createObjectURL(file);
                          const newDoc: StaffDocument = {
                            id: Math.random().toString(36).substring(2, 11),
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            url: fakeUrl,
                            uploadedAt: new Date(),
                            file: file, // Store the actual file for upload
                          };

                          const currentDocs = documentFiles || [];
                          setDocumentFiles([...currentDocs, newDoc]);

                          toast.success(
                            `Document ${file.name} added successfully`
                          );
                        } catch (error) {
                          console.error("Error adding document:", error);
                          toast.error(`Failed to add ${file.name}`);
                        }
                      });
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const input = document.getElementById(
                        "document-upload"
                      ) as HTMLInputElement;
                      input?.click();
                    }}
                    disabled={isSubmitting}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    Add Document
                  </Button>
                </div>
              </div>

              {/* Document List */}
              {documentFiles && documentFiles.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Attached Documents
                  </Label>
                  <div className="space-y-2">
                    {documentFiles.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-3 border rounded-lg bg-muted/50"
                      >
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm font-medium">{doc.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {(doc.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const updatedDocs = documentFiles.filter(
                              (d) => d.id !== doc.id
                            );
                            setDocumentFiles(updatedDocs);
                            toast.success("Document removed");
                          }}
                          disabled={isSubmitting}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <p className="text-sm text-muted-foreground">
                Upload staff documents such as ID cards, contracts,
                certificates, etc. Supported formats: PDF, DOC, DOCX, JPG, PNG,
                GIF
              </p>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<StaffTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={staff}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Staff Member"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingStaff}
      // Location props - disabled for staff
      isLocationEnabled={false}
      // SEO props - disabled for staff
      isSEOEnabled={false}
    />
  );
}
