"use client";

import { useState } from "react";
import {
  BusinessHours,
  businessHoursSchema,
  defaultBusinessHours,
  TimeSlot,
} from "@/types/business-hours";
import { DaySchedule } from "./day-schedule";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ZodError } from "zod";
import { toast } from "sonner";

export function BusinessHoursForm() {
  const [businessHours, setBusinessHours] =
    useState<BusinessHours>(defaultBusinessHours);
  const [errors, setErrors] = useState<Record<string, string>>({});
  // Removed useToast hook - using Sonner directly

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    try {
      const validatedData = businessHoursSchema.parse(businessHours);

      // Here you would typically send the data to your backend
      console.log("Validated business hours:", validatedData);

      toast.success("Business hours updated", {
        description: "Business hours have been updated successfully.",
      });
    } catch (error) {
      if (error instanceof ZodError) {
        const formattedErrors: Record<string, string> = {};
        error.errors.forEach((err) => {
          const path = err.path.join(".");
          formattedErrors[path] = err.message;
        });
        setErrors(formattedErrors);

        toast.error("Validation error", {
          description: "Please fix the highlighted errors and try again.",
        });
      } else {
        toast.error("Unexpected error", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    }
  };

  const updateDaySchedule = (
    day: keyof BusinessHours,
    updates: Partial<(typeof businessHours)[keyof BusinessHours]>
  ) => {
    setBusinessHours((prev) => ({
      ...prev,
      [day]: { ...prev[day], ...updates },
    }));
    // Clear errors when user makes changes
    setErrors({});
  };

  const handleTimeSlotUpdate = (
    day: keyof BusinessHours,
    index: number,
    updatedSlot: TimeSlot
  ) => {
    const updatedTimeSlots = [...businessHours[day].timeSlots];
    updatedTimeSlots[index] = updatedSlot;
    updateDaySchedule(day, { timeSlots: updatedTimeSlots });
  };

  const handleAddTimeSlot = (day: keyof BusinessHours) => {
    const newTimeSlot: TimeSlot = {
      openTime: "09:00",
      closeTime: "17:00",
    };
    updateDaySchedule(day, {
      timeSlots: [...businessHours[day].timeSlots, newTimeSlot],
    });
  };

  const handleRemoveTimeSlot = (day: keyof BusinessHours, index: number) => {
    const updatedTimeSlots = businessHours[day].timeSlots.filter(
      (_, i) => i !== index
    );
    updateDaySchedule(day, { timeSlots: updatedTimeSlots });
  };

  const getErrorsForDay = (day: keyof BusinessHours) => {
    return Object.entries(errors)
      .filter(([key]) => key.startsWith(day))
      .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});
  };

  return (
    <form onSubmit={handleSubmit} className="max-w-2xl mx-auto p-4 space-y-6">
      <h2 className="text-2xl font-bold mb-6">Business Hours</h2>

      {(Object.keys(businessHours) as Array<keyof BusinessHours>).map((day) => (
        <div key={day} className="relative">
          <DaySchedule
            day={day.charAt(0).toUpperCase() + day.slice(1)}
            isOpen={businessHours[day].isOpen}
            timeSlots={businessHours[day].timeSlots}
            onToggle={(value) => updateDaySchedule(day, { isOpen: value })}
            onUpdateTimeSlot={(index, updatedSlot) =>
              handleTimeSlotUpdate(day, index, updatedSlot)
            }
            onAddTimeSlot={() => handleAddTimeSlot(day)}
            onRemoveTimeSlot={(index) => handleRemoveTimeSlot(day, index)}
          />
          {Object.entries(getErrorsForDay(day)).map(([key, error]) => (
            <p key={key} className="text-sm text-destructive mt-1 ml-28">
              {JSON.stringify(error)}
            </p>
          ))}
        </div>
      ))}

      <Button type="submit" className="w-full">
        Save Business Hours
      </Button>
    </form>
  );
}
