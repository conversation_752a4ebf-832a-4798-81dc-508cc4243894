"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  SheetDescription,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Plus, Loader, MapPin, Search, ImageIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import { useLocationsSlim } from "@/lib/locations/hooks";
import { LocationSlimDto } from "@/types/location";

export interface BaseSheetProps<TData, TFormValues> {
  isDemo?: boolean;
  onSuccess?: () => void;
  data?: TData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isUpdate?: boolean;
  title: string;
  sections: Array<{
    id: string;
    title: string;
    icon: React.ReactNode;
    content: React.ReactNode;
    hasErrors?: boolean;
  }>;
  onSubmit: () => Promise<void>;
  addButtonLabel?: string;
  addButtonIcon?: React.ReactNode;
  formRef?: React.RefObject<HTMLFormElement>;
  isPending?: boolean;
  isSubmitting?: boolean;
  // Location section props
  isLocationEnabled?: boolean;
  isAllocatedToAllLocations?: boolean;
  onAllLocationsChange?: (isAllocated: boolean) => void;
  locationIds?: string[];
  onLocationChange?: (locationIds: string[]) => void;
  locationErrors?: string;
  // SEO section props
  isSEOEnabled?: boolean;
  seoTitle?: string;
  onSeoTitleChange?: (value: string) => void;
  seoTitleError?: string;
  seoDescription?: string;
  onSeoDescriptionChange?: (value: string) => void;
  seoDescriptionError?: string;
  seoKeywords?: string;
  onSeoKeywordsChange?: (value: string) => void;
  seoKeywordsError?: string;
  ogImageFiles?: UploadedFile[];
  onOgImageFilesChange?: (files: UploadedFile[]) => void;
}

export function BaseSheet<TData, TFormValues>({
  isDemo = false,
  onSuccess,
  data,
  open: controlledOpen,
  onOpenChange,
  isUpdate = false,
  title,
  sections,
  onSubmit,
  addButtonLabel = "Add New",
  addButtonIcon = <Plus className="size-4" aria-hidden="true" />,
  formRef,
  isPending = false,
  isSubmitting = false,
  // Location section props
  isLocationEnabled = false,
  isAllocatedToAllLocations = false,
  onAllLocationsChange,
  locationIds = [],
  onLocationChange,
  locationErrors,
  // SEO section props
  isSEOEnabled = false,
  seoTitle = "",
  onSeoTitleChange,
  seoTitleError,
  seoDescription = "",
  onSeoDescriptionChange,
  seoDescriptionError,
  seoKeywords = "",
  onSeoKeywordsChange,
  seoKeywordsError,
  ogImageFiles = [],
  onOgImageFilesChange,
}: BaseSheetProps<TData, TFormValues>) {
  const [uncontrolledOpen, setUncontrolledOpen] = React.useState(false);
  const [internalSubmitting, setInternalSubmitting] = React.useState(false);
  const open = controlledOpen ?? uncontrolledOpen;
  const setOpen = onOpenChange ?? setUncontrolledOpen;

  // Fetch locations if location section is enabled
  const { data: locationsResponse } = useLocationsSlim();
  const locations = locationsResponse?.data || [];

  // Use either external isSubmitting prop or internal state
  const isCurrentlySubmitting = isSubmitting || internalSubmitting;

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isCurrentlySubmitting) return;

    setInternalSubmitting(true);

    try {
      await onSubmit();
      // Note: Don't close the sheet here - let the parent component handle success/failure
      // The parent should call setOpen(false) and onSuccess() on successful submission
    } catch (error) {
      console.error("Failed to submit:", error);
      toast.error("Failed to submit");
    } finally {
      setInternalSubmitting(false);
    }
  };

  // Generate additional sections
  const additionalSections = React.useMemo(() => {
    const sections = [];

    // Locations section
    if (isLocationEnabled) {
      sections.push({
        id: "locations",
        title: "Locations",
        icon: <MapPin className="h-5 w-5 text-muted-foreground" />,
        hasErrors: !!locationErrors,
        content: (
          <>
            <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">Locations</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-6 pt-2">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>All Locations</Label>
                      <p className="text-sm text-muted-foreground">
                        Allocate to all business locations
                      </p>
                    </div>
                    <Switch
                      checked={isAllocatedToAllLocations}
                      onCheckedChange={onAllLocationsChange}
                      disabled={isCurrentlySubmitting}
                    />
                  </div>
                  {locationErrors && (
                    <p className="text-sm text-destructive mt-1">
                      {locationErrors}
                    </p>
                  )}
                </div>

                {!isAllocatedToAllLocations && (
                  <div>
                    <Label>Available Locations</Label>
                    <div className="grid grid-cols-1 gap-3 mt-2 max-h-48 overflow-y-auto">
                      {locations.length === 0 ? (
                        <p className="text-sm text-muted-foreground p-4 text-center border border-dashed rounded-lg">
                          No locations available. Create locations first to
                          assign them to categories.
                        </p>
                      ) : (
                        locations.map((location: LocationSlimDto) => (
                          <div
                            key={location.id}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              id={`location-${location.id}`}
                              checked={
                                locationIds?.includes(location.id) || false
                              }
                              onCheckedChange={(checked) => {
                                const currentLocationIds = locationIds || [];
                                if (checked) {
                                  // Add location ID if checked
                                  onLocationChange?.([
                                    ...currentLocationIds,
                                    location.id,
                                  ]);
                                } else {
                                  // Remove location ID if unchecked
                                  onLocationChange?.(
                                    currentLocationIds.filter(
                                      (id) => id !== location.id
                                    )
                                  );
                                }
                              }}
                              disabled={isCurrentlySubmitting}
                            />
                            <Label
                              htmlFor={`location-${location.id}`}
                              className="text-sm font-normal cursor-pointer flex items-center gap-2"
                            >
                              <span>{location.name}</span>
                              <span className="text-xs text-muted-foreground capitalize">
                                ({location.type})
                              </span>
                            </Label>
                          </div>
                        ))
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      Select the locations where this will be available
                    </p>
                  </div>
                )}
              </div>
            </AccordionContent>
          </>
        ),
      });
    }

    // SEO section
    if (isSEOEnabled) {
      sections.push({
        id: "seo",
        title: "SEO & Social Media",
        icon: <Search className="h-5 w-5 text-muted-foreground" />,
        hasErrors: !!(seoTitleError || seoDescriptionError || seoKeywordsError),
        content: (
          <>
            <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
              <div className="flex items-center gap-2">
                <Search className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">SEO & Social Media</span>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-6 pb-6 pt-2">
              <div className="space-y-4">
                <div>
                  <Label>SEO Title</Label>
                  <Input
                    value={seoTitle}
                    onChange={(e) => onSeoTitleChange?.(e.target.value)}
                    placeholder="Enter SEO title for search engines"
                    className={cn(seoTitleError && "border-destructive")}
                    disabled={isCurrentlySubmitting}
                  />
                  {seoTitleError && (
                    <p className="text-sm text-destructive mt-1">
                      {seoTitleError}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Optimized title for search engines (recommended: 50-60
                    characters)
                  </p>
                </div>

                <div>
                  <Label>SEO Description</Label>
                  <Textarea
                    value={seoDescription}
                    onChange={(e) => onSeoDescriptionChange?.(e.target.value)}
                    placeholder="Enter SEO description for search engines"
                    className={cn(seoDescriptionError && "border-destructive")}
                    disabled={isCurrentlySubmitting}
                    rows={3}
                  />
                  {seoDescriptionError && (
                    <p className="text-sm text-destructive mt-1">
                      {seoDescriptionError}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Meta description for search results (recommended: 150-160
                    characters)
                  </p>
                </div>

                <div>
                  <Label>SEO Keywords</Label>
                  <Input
                    value={seoKeywords}
                    onChange={(e) => onSeoKeywordsChange?.(e.target.value)}
                    placeholder="Enter keywords separated by commas"
                    className={cn(seoKeywordsError && "border-destructive")}
                    disabled={isCurrentlySubmitting}
                  />
                  {seoKeywordsError && (
                    <p className="text-sm text-destructive mt-1">
                      {seoKeywordsError}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Keywords for SEO purposes, separated by commas
                  </p>
                </div>

                <div>
                  <Label>Open Graph Image</Label>
                  <FileUploader
                    files={ogImageFiles}
                    onFilesChange={onOgImageFilesChange || (() => {})}
                    fileType="image"
                    maxFiles={1}
                    maxSize={5 * 1024 * 1024} // 5MB
                    multiple={false}
                    label="OG Image"
                    description="Upload an Open Graph image for social media sharing. Recommended size: 1200x630px. Maximum file size is 5MB."
                    placeholder="Click to upload or drag and drop"
                    disabled={isCurrentlySubmitting}
                    showPreview={true}
                    previewSize="md"
                    onError={(error) => {
                      toast.error(error);
                    }}
                  />
                  <p className="text-sm text-muted-foreground mt-1">
                    This image will be displayed when shared on social media
                    platforms
                  </p>
                </div>
              </div>
            </AccordionContent>
          </>
        ),
      });
    }

    return sections;
  }, [
    isLocationEnabled,
    isAllocatedToAllLocations,
    onAllLocationsChange,
    isSEOEnabled,
    locations,
    locationIds,
    locationErrors,
    seoTitle,
    seoTitleError,
    seoDescription,
    seoDescriptionError,
    seoKeywords,
    seoKeywordsError,
    ogImageFiles,
    onLocationChange,
    onSeoTitleChange,
    onSeoDescriptionChange,
    onSeoKeywordsChange,
    onOgImageFilesChange,
    isCurrentlySubmitting,
  ]);

  // Combine user sections with additional sections
  const allSections = [...(sections || []), ...additionalSections];

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      {/* {!isUpdate && (
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            {addButtonIcon}
            {addButtonLabel}
          </Button>
        </SheetTrigger>
      )} */}
      <SheetContent className="w-full sm:w-[540px] md:w-[720px] lg:w-[920px] p-0 gap-0 dark:border-zinc-800">
        {/* Header */}
        <div className="sticky top-0 z-10 bg-background border-b dark:border-zinc-800">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-xl">
                {data ? `Update ${title}` : `New ${title}`}
              </SheetTitle>
              <div className="hidden sm:flex items-center gap-2">
                {allSections.map((section) => (
                  <Button
                    key={section.id}
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      const element = document.querySelector(
                        `[data-value="${section.id}"]`
                      );
                      element?.scrollIntoView({
                        behavior: "smooth",
                        block: "start",
                      });
                    }}
                    className="bg-secondary hover:bg-secondary/80 dark:bg-secondary/30 dark:hover:bg-secondary/40 relative shadow-sm"
                    title={section.title}
                    disabled={isCurrentlySubmitting}
                  >
                    {section.icon}
                    {section.hasErrors && (
                      <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-destructive" />
                    )}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <ScrollArea className="flex-1 h-[calc(100vh-10rem)] px-4 pt-0">
          {isPending ? (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <Loader className="h-8 w-8 animate-spin text-muted-foreground" />
              <div className="text-center space-y-2">
                <p className="text-sm font-medium">
                  Loading {title.toLowerCase()}...
                </p>
                <p className="text-xs text-muted-foreground">
                  Please wait while we fetch the data
                </p>
              </div>
            </div>
          ) : (
            <form
              ref={formRef}
              id="base-sheet-form"
              onSubmit={handleFormSubmit}
              className="space-y-4 py-4 pb-6"
            >
              <Accordion
                type="multiple"
                defaultValue={allSections.map((section) => section.id)}
                className="w-full space-y-4"
              >
                {allSections.map((section) => (
                  <AccordionItem
                    key={section.id}
                    value={section.id}
                    className={cn(
                      "border rounded-lg shadow-sm dark:border-zinc-800 dark:shadow-none",
                      section.hasErrors &&
                        "border-destructive dark:border-red-600"
                    )}
                    data-value={section.id}
                  >
                    {section.content}
                  </AccordionItem>
                ))}
              </Accordion>
            </form>
          )}
        </ScrollArea>

        {/* Footer */}
        <div className="sticky bottom-0 z-10 bg-background border-t dark:border-zinc-800">
          <div className="flex justify-end space-x-2 py-4 px-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              className="min-w-[80px]"
              disabled={isCurrentlySubmitting || isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              form={formRef?.current?.id || "base-sheet-form"}
              className="min-w-[80px]"
              disabled={isCurrentlySubmitting || isPending}
            >
              {isCurrentlySubmitting && (
                <Loader
                  className="mr-2 size-4 animate-spin"
                  aria-hidden="true"
                />
              )}
              {isCurrentlySubmitting
                ? isUpdate
                  ? "Updating..."
                  : "Creating..."
                : isUpdate
                ? "Update"
                : "Create"}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
