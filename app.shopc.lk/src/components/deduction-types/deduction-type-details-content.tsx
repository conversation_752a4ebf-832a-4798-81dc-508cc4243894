"use client";

import * as React from "react";
import {
  Info,
  Calculator,
  Calendar,
  Printer,
  Download,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Hash,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useDeductionTypeData } from "@/lib/deduction-types/hooks";
import {
  DeductionTypeTableData,
  DeductionCalculationMethod,
  DeductionAppliesTo,
} from "@/types/deduction-type";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface DeductionTypeDetailsContentProps {
  deductionType: DeductionTypeTableData;
  isDemo?: boolean;
}

export function DeductionTypeDetailsContent({
  deductionType,
  isDemo = false,
}: DeductionTypeDetailsContentProps) {
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch complete deduction type data
  const { data: fullDeductionTypeResponse, isLoading: isLoadingDeductionType } =
    useDeductionTypeData(deductionType.id, isDemo);
  const fullDeductionType = fullDeductionTypeResponse?.data;

  // Helper functions for display
  const getCalculationMethodDisplay = (method: DeductionCalculationMethod) => {
    switch (method) {
      case DeductionCalculationMethod.FIXED:
        return "Fixed Amount";
      case DeductionCalculationMethod.PERCENTAGE:
        return "Percentage";
      case DeductionCalculationMethod.PROGRESSIVE:
        return "Progressive";
      case DeductionCalculationMethod.FORMULA:
        return "Formula Based";
      default:
        return method;
    }
  };

  const getAppliesToDisplay = (appliesTo: DeductionAppliesTo) => {
    switch (appliesTo) {
      case DeductionAppliesTo.GROSS:
        return "Gross Salary";
      case DeductionAppliesTo.BASIC:
        return "Basic Salary";
      case DeductionAppliesTo.NET:
        return "Net Salary";
      default:
        return appliesTo;
    }
  };

  // Print functionality
  const handlePrint = React.useCallback(() => {
    const printWindow = window.open("", "_blank");
    if (!printWindow || !printRef.current) return;

    const printContent = printRef.current.cloneNode(true) as HTMLElement;

    // Create a complete HTML document for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Deduction Type Details - ${fullDeductionType?.deductionName || "Deduction Type"}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .print-header h1 { margin: 0; font-size: 24px; color: #333; }
            .print-header p { margin: 5px 0 0 0; color: #666; }
            .section { margin-bottom: 25px; page-break-inside: avoid; }
            .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
            .field { margin-bottom: 10px; }
            .field label { font-weight: bold; color: #555; display: block; margin-bottom: 3px; font-size: 12px; }
            .field div { color: #333; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
            .badge.active { background-color: #d4edda; color: #155724; }
            .badge.inactive { background-color: #f8d7da; color: #721c24; }
            .badge.mandatory { background-color: #fff3cd; color: #856404; }
            .badge.method { background-color: #e2e3e5; color: #383d41; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Deduction Type Details</h1>
            <p>Generated on ${format(new Date(), "PPP 'at' p")}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [fullDeductionType]);

  // PDF export functionality
  const handleExportPDF = React.useCallback(async () => {
    if (!printRef.current || !fullDeductionType) return;

    try {
      toast.info("Generating PDF...");

      const canvas = await html2canvas(printRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`deduction-type-${fullDeductionType.deductionCode || fullDeductionType.id}.pdf`);
      toast.success("PDF exported successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF");
    }
  }, [fullDeductionType]);

  if (isLoadingDeductionType) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingIndicator />
      </div>
    );
  }

  if (!fullDeductionType) {
    return (
      <div className="p-6 text-center">
        <p className="text-muted-foreground">Deduction type not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="flex items-center justify-end gap-2 no-print">
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrint}
          className="flex items-center gap-2"
        >
          <Printer className="h-4 w-4" />
          Print
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleExportPDF}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Export PDF
        </Button>
      </div>

      {/* Content for printing */}
      <div ref={printRef} className="space-y-6">
        {/* Header */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold">{fullDeductionType.deductionName}</h2>
            <div className="flex items-center gap-2">
              {fullDeductionType.isActive ? (
                <Badge className="badge active">
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              ) : (
                <Badge className="badge inactive">
                  <XCircle className="h-3 w-3 mr-1" />
                  Inactive
                </Badge>
              )}
              {fullDeductionType.isSystemDefined && (
                <Badge className="badge system-defined">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  System Defined
                </Badge>
              )}
            </div>
          </div>
          {fullDeductionType.description && (
            <p className="text-muted-foreground">
              {fullDeductionType.description}
            </p>
          )}
          {fullDeductionType.deductionCode && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Hash className="h-4 w-4" />
              <span>Code: {fullDeductionType.deductionCode}</span>
            </div>
          )}
        </div>

        {/* Accordion Sections */}
        <Accordion type="multiple" defaultValue={["basic", "calculation", "metadata"]} className="w-full">
          {/* Basic Information */}
          <AccordionItem value="basic">
            <AccordionTrigger className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              Basic Information
            </AccordionTrigger>
            <AccordionContent>
              <div className="section">
                <div className="grid">
                  <div className="field">
                    <label>Deduction Name</label>
                    <div>{fullDeductionType.deductionName}</div>
                  </div>
                  <div className="field">
                    <label>Deduction Code</label>
                    <div className="font-mono text-blue-600 dark:text-blue-400">
                      {fullDeductionType.deductionCode}
                    </div>
                  </div>
                  {fullDeductionType.amount !== undefined && (
                    <div className="field">
                      <label>Amount</label>
                      <div className="font-mono text-blue-600 dark:text-blue-400">
                        {fullDeductionType.amount.toFixed(2)}
                      </div>
                    </div>
                  )}
                  <div className="field">
                    <label>Status</label>
                    <div className="flex items-center gap-2">
                      {fullDeductionType.isActive ? (
                        <>
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                          <span className="text-green-600 dark:text-green-400">Active</span>
                        </>
                      ) : (
                        <>
                          <XCircle className="h-4 w-4 text-red-600" />
                          <span className="text-red-600 dark:text-red-400">Inactive</span>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="field">
                    <label>System Defined</label>
                    <div className="flex items-center gap-2">
                      {fullDeductionType.isSystemDefined ? (
                        <>
                          <AlertTriangle className="h-4 w-4 text-blue-600" />
                          <span className="text-blue-600 dark:text-blue-400">Yes</span>
                        </>
                      ) : (
                        <>
                          <CheckCircle2 className="h-4 w-4 text-gray-600" />
                          <span className="text-gray-600 dark:text-gray-400">No</span>
                        </>
                      )}
                    </div>
                  </div>
                  {fullDeductionType.description && (
                    <div className="field">
                      <label>Description</label>
                      <div>{fullDeductionType.description}</div>
                    </div>
                  )}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Calculation Information */}
          <AccordionItem value="calculation">
            <AccordionTrigger className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Calculation Details
            </AccordionTrigger>
            <AccordionContent>
              <div className="section">
                <div className="grid">
                  <div className="field">
                    <label>Calculation Method</label>
                    <div>
                      <Badge className="badge method">
                        {getCalculationMethodDisplay(fullDeductionType.calculationMethod)}
                      </Badge>
                    </div>
                  </div>
                  <div className="field">
                    <label>Applies To</label>
                    <div>
                      <Badge className="badge method">
                        {getAppliesToDisplay(fullDeductionType.appliesTo)}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Metadata */}
          <AccordionItem value="metadata">
            <AccordionTrigger className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Metadata
            </AccordionTrigger>
            <AccordionContent>
              <div className="section">
                <div className="grid">
                  <div className="field">
                    <label>Created By</label>
                    <div>{fullDeductionType.createdBy}</div>
                  </div>
                  <div className="field">
                    <label>Created At</label>
                    <div>{format(new Date(fullDeductionType.createdAt), "PPP")}</div>
                  </div>
                  <div className="field">
                    <label>Last Updated</label>
                    <div>{format(new Date(fullDeductionType.updatedAt), "PPP")}</div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  );
}
