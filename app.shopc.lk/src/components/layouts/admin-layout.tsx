"use client";

import { NuqsAdapter } from "nuqs/adapters/next/app";
import { TooltipProvider } from "@/components/ui/tooltip";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { useScreenSize } from "@/hooks/use-screen-size";
import { BottomNavigation } from "@/components/mobile/bottom-navigation";
import { BookmarksProvider } from "@/contexts/bookmarks-context";
import { AuthProvider, useBusiness } from "@/contexts/auth-contexts";
import { AuthGuard } from "@/components/admin-panel/auth-guard";
import { BusinessStatus } from "@/types/auth";
import MeetingScheduler from "@/components/views/meeting-scheduler";

interface AdminLayoutProps {
  children: React.ReactNode;
  isDemo?: boolean;
}

function BusinessStatusWrapper({ children }: { children: React.ReactNode }) {
  const { business, isLoading } = useBusiness();

  if (isLoading && business === null) {
    return <div>Loading...</div>;
  }

  if (business?.businessStatus === BusinessStatus.Pending) {
    return (
      <div>
        <MeetingScheduler />
      </div>
    );
  }

  return <>{children}</>;
}

function AdminLayoutContent({ children, isDemo = false }: AdminLayoutProps) {
  const screenSize = useScreenSize();
  const isMobile = screenSize.lessThanOrEqual("sm");

  return (
    <BookmarksProvider>
      <SidebarProvider>
        <AppSidebar isDemo={isDemo} />
        <SidebarInset>
          <TooltipProvider>
            <NuqsAdapter>
              <div
                className={`h-full w-full ${
                  isDemo ? "bg-slate-50 dark:bg-slate-900" : ""
                } ${isMobile ? "pb-16" : ""}`}
              >
                {children}
                <BottomNavigation isDemo={isDemo} />
              </div>
            </NuqsAdapter>
          </TooltipProvider>
        </SidebarInset>
      </SidebarProvider>
    </BookmarksProvider>
  );
}

function AuthenticatedAdminLayout({
  children,
  isDemo = false,
}: AdminLayoutProps) {
  return (
    <AuthGuard>
      <BusinessStatusWrapper>
        <AdminLayoutContent isDemo={isDemo}>{children}</AdminLayoutContent>
      </BusinessStatusWrapper>
    </AuthGuard>
  );
}

export default function AdminLayout({
  children,
  isDemo = false,
}: AdminLayoutProps) {
  return (
    <AuthProvider>
      <AuthenticatedAdminLayout isDemo={isDemo}>
        {children}
      </AuthenticatedAdminLayout>
    </AuthProvider>
  );
}
