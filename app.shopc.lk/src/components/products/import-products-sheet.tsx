/**
 * ImportProductsSheet Component
 *
 * This component allows importing products with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Support for product images with preview
 * - Category, subcategory, and brand selection
 * - Field validation and error handling
 * - Integration with bulk import API with images
 *
 * Image handling:
 * - Images are stored in uploadedImages array
 * - Each product row stores imageIndex to reference the image
 * - Images are displayed with filename and small preview
 * - Supports selective image assignment (some products can have images, others don't)
 */

"use client";

import { useState, useMemo, useEffect, useCallback } from "react";
import {
  ImportDataSheet,
  FieldMapping,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  CreateProductSchema,
  GetProductsSchema,
} from "@/lib/products/validations";
import { ProductStatus, ProductType, TrackingType, StandardUnitOfMeasure } from "@/types/product";
import { ApiStatus } from "@/types/common";
import {
  useProductsData,
  useProductNameAvailability,
  useProductSlugAvailability,
  useProductSkuAvailability,
  useProductBarcodeAvailability,
  useBulkCreateProducts,
} from "@/lib/products/hooks";
import { useCategoriesSlim } from "@/lib/categories/hooks";
import { useBrandsSlim } from "@/lib/brands/hooks";

// Define the product field types that can be imported
export type ProductImportFields =
  | "name"
  | "shortCode"
  | "sku"
  | "barcode"
  | "description"
  | "shortDescription"
  | "slug"
  | "categoryId"
  | "subCategoryId"
  | "brandId"
  | "productType"
  | "basePrice"
  | "standardCost"
  | "minimumSellingPrice"
  | "maximumRetailPrice"
  | "wholesalePrice"
  | "trackingType"
  | "unitOfMeasure"
  | "reorderLevel"
  | "reorderQuantity"
  | "weight"
  | "dimensions"
  | "color"
  | "size"
  | "material"
  | "model"
  | "availableOnline"
  | "featured"
  | "status"
  | "image";

// Required fields for product import
const REQUIRED_PRODUCT_FIELDS: ProductImportFields[] = ["name", "productType"];

// All possible fields for product import
const ALL_PRODUCT_FIELDS: ProductImportFields[] = [
  "name",
  "shortCode",
  "sku",
  "barcode",
  "description",
  "shortDescription",
  "slug",
  "categoryId",
  "subCategoryId",
  "brandId",
  "productType",
  "basePrice",
  "standardCost",
  "minimumSellingPrice",
  "maximumRetailPrice",
  "wholesalePrice",
  "trackingType",
  "unitOfMeasure",
  "reorderLevel",
  "reorderQuantity",
  "weight",
  "dimensions",
  "color",
  "size",
  "material",
  "model",
  "availableOnline",
  "featured",
  "status",
  "image",
];

export type ImportProductsSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportProductsSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportProductsSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook for bulk importing products with images
  const bulkImportMutation = useBulkCreateProducts(isDemo);

  // Fetch categories for selection (slim data)
  const { data: categoriesResponse } = useCategoriesSlim(isDemo);
  const categories = categoriesResponse?.data || [];

  // Fetch brands for selection (slim data)
  const { data: brandsResponse } = useBrandsSlim(isDemo);
  const brands = brandsResponse?.data || [];

  // Fetch full products data for availability checking
  const { data: fullProductsResponse } = useProductsData(
    {
      page: 1,
      limit: 1000, // Get all products for availability checking
    } as GetProductsSchema,
    isDemo
  );
  const fullProducts = fullProductsResponse?.data?.data || [];

  // Flatten categories for easier lookup
  const flatCategories = useMemo(() => {
    const flat: Array<{ id: string; name: string; parentName?: string }> = [];
    categories.forEach((category) => {
      flat.push({ id: category.id, name: category.name });
      if (category.subcategories) {
        category.subcategories.forEach((sub) => {
          flat.push({
            id: sub.id,
            name: sub.name,
            parentName: category.name,
          });
        });
      }
    });
    return flat;
  }, [categories]);

  // Track validation state for availability checking
  const [currentValidatingName, setCurrentValidatingName] = useState<string>("");
  const [currentValidatingSlug, setCurrentValidatingSlug] = useState<string>("");
  const [currentValidatingSku, setCurrentValidatingSku] = useState<string>("");
  const [currentValidatingBarcode, setCurrentValidatingBarcode] = useState<string>("");

  // Debounced availability checking to prevent infinite loops
  const debouncedCheckAvailability = useCallback(
    (field: string, value: string) => {
      if (!value || !value.trim()) return;

      const trimmedValue = value.trim();

      // Only trigger if not already checking this value
      setTimeout(() => {
        switch (field) {
          case "name":
            if (currentValidatingName !== trimmedValue) {
              setCurrentValidatingName(trimmedValue);
            }
            break;
          case "slug":
            if (currentValidatingSlug !== trimmedValue) {
              setCurrentValidatingSlug(trimmedValue);
            }
            break;
          case "sku":
            if (currentValidatingSku !== trimmedValue) {
              setCurrentValidatingSku(trimmedValue);
            }
            break;
          case "barcode":
            if (currentValidatingBarcode !== trimmedValue) {
              setCurrentValidatingBarcode(trimmedValue);
            }
            break;
        }
      }, 300); // 300ms debounce
    },
    [currentValidatingName, currentValidatingSlug, currentValidatingSku, currentValidatingBarcode]
  );

  // Use availability hooks for current validating values
  const { data: nameAvailability, isLoading: isLoadingNameCheck } =
    useProductNameAvailability(currentValidatingName, isDemo);
  const { data: slugAvailability, isLoading: isLoadingSlugCheck } =
    useProductSlugAvailability(currentValidatingSlug, isDemo);
  const { data: skuAvailability, isLoading: isLoadingSkuCheck } =
    useProductSkuAvailability(currentValidatingSku, isDemo);
  const { data: barcodeAvailability, isLoading: isLoadingBarcodeCheck } =
    useProductBarcodeAvailability(currentValidatingBarcode, isDemo);

  // Build a cache of checked availability
  const [availabilityCache, setAvailabilityCache] = useState<{
    names: Map<string, boolean>;
    slugs: Map<string, boolean>;
    skus: Map<string, boolean>;
    barcodes: Map<string, boolean>;
  }>({
    names: new Map(),
    slugs: new Map(),
    skus: new Map(),
    barcodes: new Map(),
  });

  // Update cache when availability results come in
  useEffect(() => {
    if (currentValidatingName && nameAvailability?.data?.available !== undefined) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.names.set(
          currentValidatingName.toLowerCase(),
          nameAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingName, nameAvailability]);

  useEffect(() => {
    if (currentValidatingSlug && slugAvailability?.data?.available !== undefined) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.slugs.set(
          currentValidatingSlug.toLowerCase(),
          slugAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingSlug, slugAvailability]);

  useEffect(() => {
    if (currentValidatingSku && skuAvailability?.data?.available !== undefined) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.skus.set(
          currentValidatingSku.toUpperCase(),
          skuAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingSku, skuAvailability]);

  useEffect(() => {
    if (currentValidatingBarcode && barcodeAvailability?.data?.available !== undefined) {
      setAvailabilityCache((prev) => {
        const newCache = { ...prev };
        newCache.barcodes.set(
          currentValidatingBarcode,
          barcodeAvailability.data!.available
        );
        return newCache;
      });
    }
  }, [currentValidatingBarcode, barcodeAvailability]);

  // Helper function to check for duplicates within the dataset
  const checkForInternalDuplicates = (
    data: Record<string, any>[],
    currentIndex: number,
    field: string,
    value: string
  ): boolean => {
    if (!value || !value.trim()) return false;

    const normalizedValue =
      field === "sku" || field === "barcode"
        ? value.trim().toUpperCase()
        : value.trim().toLowerCase();

    return data.some((row, index) => {
      if (index === currentIndex) return false; // Skip current row
      if (!row[field] || !row[field].trim()) return false;

      const otherValue =
        field === "sku" || field === "barcode"
          ? row[field].trim().toUpperCase()
          : row[field].trim().toLowerCase();

      return otherValue === normalizedValue;
    });
  };

  // Helper function to check if a field is currently being validated (loading)
  const isFieldLoading = (field: string, value: string): boolean => {
    if (!value || !value.trim()) return false;

    const trimmedValue = value.trim();
    switch (field) {
      case "name":
        return currentValidatingName === trimmedValue && isLoadingNameCheck;
      case "slug":
        return currentValidatingSlug === trimmedValue && isLoadingSlugCheck;
      case "sku":
        return currentValidatingSku === trimmedValue && isLoadingSkuCheck;
      case "barcode":
        return currentValidatingBarcode === trimmedValue && isLoadingBarcodeCheck;
      default:
        return false;
    }
  };

  // Helper function to check availability against existing data using hooks
  const checkAvailabilityAgainstExisting = (
    field: string,
    value: string
  ): boolean => {
    if (!value || !value.trim()) return true;

    const normalizedValue =
      field === "sku" || field === "barcode"
        ? value.trim().toUpperCase()
        : value.trim().toLowerCase();

    // First check the availability cache from hooks
    let cachedResult: boolean | undefined;
    switch (field) {
      case "name":
        cachedResult = availabilityCache.names.get(normalizedValue);
        break;
      case "slug":
        cachedResult = availabilityCache.slugs.get(normalizedValue);
        break;
      case "sku":
        cachedResult = availabilityCache.skus.get(normalizedValue);
        break;
      case "barcode":
        cachedResult = availabilityCache.barcodes.get(normalizedValue);
        break;
    }

    // If we have a cached result, use it
    if (cachedResult !== undefined) {
      return cachedResult;
    }

    // Check against existing products data as fallback
    return fullProducts.every((product) => {
      if (!product[field as keyof typeof product]) return true;

      const existingValue =
        field === "sku" || field === "barcode"
          ? (product[field as keyof typeof product] as string).toUpperCase()
          : (product[field as keyof typeof product] as string).toLowerCase();

      return existingValue !== normalizedValue;
    });
  };

  // Field configurations for products
  const PRODUCT_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "name",
      type: "text",
      defaultValue: "",
      placeholder: "Enter product name (e.g., iPhone 15 Pro, MacBook Air)",
      isLoading: (value: string) => isFieldLoading("name", value),
      onValueChange: (value: string) => debouncedCheckAvailability("name", value),
    },
    {
      name: "shortCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter short code (e.g., IP15P, MBA-M2)",
    },
    {
      name: "sku",
      type: "text",
      defaultValue: "",
      placeholder: "Enter SKU (e.g., IPHONE-15-PRO-128GB)",
      isLoading: (value: string) => isFieldLoading("sku", value),
      onValueChange: (value: string) => debouncedCheckAvailability("sku", value),
    },
    {
      name: "barcode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter barcode (e.g., 1234567890123)",
      isLoading: (value: string) => isFieldLoading("barcode", value),
      onValueChange: (value: string) => debouncedCheckAvailability("barcode", value),
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter detailed product description",
    },
    {
      name: "shortDescription",
      type: "text",
      defaultValue: "",
      placeholder: "Enter brief product summary",
    },
    {
      name: "slug",
      type: "text",
      defaultValue: "",
      placeholder: "URL-friendly version (auto-generated if empty)",
      isLoading: (value: string) => isFieldLoading("slug", value),
      onValueChange: (value: string) => debouncedCheckAvailability("slug", value),
    },
    {
      name: "categoryId",
      type: "select",
      options: [
        { value: "none", label: "No Category" },
        ...flatCategories
          .filter((cat) => !cat.parentName)
          .map((cat) => ({
            value: cat.id,
            label: cat.name,
          })),
      ],
      defaultValue: "none",
    },
    {
      name: "subCategoryId",
      type: "select",
      options: [
        { value: "none", label: "No Subcategory" },
        ...flatCategories
          .filter((cat) => cat.parentName)
          .map((cat) => ({
            value: cat.id,
            label: `${cat.name} (${cat.parentName})`,
          })),
      ],
      defaultValue: "none",
    },
    {
      name: "brandId",
      type: "select",
      options: [
        { value: "none", label: "No Brand" },
        ...brands.map((brand) => ({
          value: brand.id,
          label: brand.name,
        })),
      ],
      defaultValue: "none",
    },
    {
      name: "productType",
      type: "select",
      options: Object.values(ProductType).map((type) => ({
        value: type,
        label: type.charAt(0).toUpperCase() + type.slice(1).toLowerCase(),
      })),
      defaultValue: ProductType.SINGLE,
    },
    {
      name: "basePrice",
      type: "text",
      defaultValue: "",
      placeholder: "Enter base price (e.g., 999.99)",
    },
    {
      name: "standardCost",
      type: "text",
      defaultValue: "",
      placeholder: "Enter standard cost (e.g., 750.00)",
    },
    {
      name: "minimumSellingPrice",
      type: "text",
      defaultValue: "",
      placeholder: "Enter minimum selling price",
    },
    {
      name: "maximumRetailPrice",
      type: "text",
      defaultValue: "",
      placeholder: "Enter maximum retail price",
    },
    {
      name: "wholesalePrice",
      type: "text",
      defaultValue: "",
      placeholder: "Enter wholesale price",
    },
    {
      name: "trackingType",
      type: "select",
      options: Object.values(TrackingType).map((type) => ({
        value: type,
        label: type.charAt(0).toUpperCase() + type.slice(1).toLowerCase(),
      })),
      defaultValue: TrackingType.NONE,
    },
    {
      name: "unitOfMeasure",
      type: "select",
      options: Object.values(StandardUnitOfMeasure).map((unit) => ({
        value: unit,
        label: unit.toUpperCase(),
      })),
      defaultValue: StandardUnitOfMeasure.PIECES,
    },
    {
      name: "reorderLevel",
      type: "text",
      defaultValue: "",
      placeholder: "Enter reorder level (e.g., 10)",
    },
    {
      name: "reorderQuantity",
      type: "text",
      defaultValue: "",
      placeholder: "Enter reorder quantity (e.g., 50)",
    },
    {
      name: "weight",
      type: "text",
      defaultValue: "",
      placeholder: "Enter weight (e.g., 0.5 kg)",
    },
    {
      name: "dimensions",
      type: "text",
      defaultValue: "",
      placeholder: "Enter dimensions (e.g., 15x7x0.8 cm)",
    },
    {
      name: "color",
      type: "text",
      defaultValue: "",
      placeholder: "Enter color (e.g., Space Black)",
    },
    {
      name: "size",
      type: "text",
      defaultValue: "",
      placeholder: "Enter size (e.g., 128GB)",
    },
    {
      name: "material",
      type: "text",
      defaultValue: "",
      placeholder: "Enter material (e.g., Titanium)",
    },
    {
      name: "model",
      type: "text",
      defaultValue: "",
      placeholder: "Enter model (e.g., A2848)",
    },
    {
      name: "availableOnline",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "true",
    },
    {
      name: "featured",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "false",
    },
    {
      name: "status",
      type: "select",
      options: Object.values(ProductStatus).map((status) => ({
        value: status,
        label: status.charAt(0).toUpperCase() + status.slice(1).toLowerCase(),
      })),
      defaultValue: ProductStatus.ACTIVE,
    },
    {
      name: "image",
      type: "image",
      defaultValue: "",
      placeholder: "Select product image",
      accept: "image/*",
    },
  ];

  // Enhanced validation function for products with uniqueness checks
  const validateProductRow = (
    row: Record<string, any>,
    rowIndex?: number,
    allData?: Record<string, any>[]
  ) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (!row.name || typeof row.name !== "string" || row.name.trim() === "") {
      errors.name = "Name is required";
      valid = false;
    } else {
      // Check uniqueness within dataset
      if (allData && rowIndex !== undefined) {
        if (checkForInternalDuplicates(allData, rowIndex, "name", row.name)) {
          errors.name = "Name must be unique within the import data";
          valid = false;
        }
      }

      // Check availability against existing data
      if (!checkAvailabilityAgainstExisting("name", row.name)) {
        errors.name = "Product name already exists";
        valid = false;
      }
    }

    // Validate product type
    if (!row.productType || !Object.values(ProductType).includes(row.productType)) {
      errors.productType = "Valid product type is required";
      valid = false;
    }

    // Validate SKU if present
    if (row.sku !== undefined && row.sku !== null && row.sku !== "") {
      if (typeof row.sku !== "string") {
        errors.sku = "SKU must be a string";
        valid = false;
      } else {
        // Check uniqueness within dataset
        if (allData && rowIndex !== undefined) {
          if (checkForInternalDuplicates(allData, rowIndex, "sku", row.sku)) {
            errors.sku = "SKU must be unique within the import data";
            valid = false;
          }
        }

        // Check availability against existing data
        if (!checkAvailabilityAgainstExisting("sku", row.sku)) {
          errors.sku = "SKU already exists";
          valid = false;
        }
      }
    }

    // Validate barcode if present
    if (row.barcode !== undefined && row.barcode !== null && row.barcode !== "") {
      if (typeof row.barcode !== "string") {
        errors.barcode = "Barcode must be a string";
        valid = false;
      } else {
        // Check uniqueness within dataset
        if (allData && rowIndex !== undefined) {
          if (checkForInternalDuplicates(allData, rowIndex, "barcode", row.barcode)) {
            errors.barcode = "Barcode must be unique within the import data";
            valid = false;
          }
        }

        // Check availability against existing data
        if (!checkAvailabilityAgainstExisting("barcode", row.barcode)) {
          errors.barcode = "Barcode already exists";
          valid = false;
        }
      }
    }

    // Validate slug if present
    if (row.slug !== undefined && row.slug !== null && row.slug !== "") {
      if (typeof row.slug !== "string") {
        errors.slug = "Slug must be a string";
        valid = false;
      } else if (!/^[a-z0-9-]+$/.test(row.slug)) {
        errors.slug = "Slug must contain only lowercase letters, numbers, and hyphens";
        valid = false;
      } else {
        // Check uniqueness within dataset
        if (allData && rowIndex !== undefined) {
          if (checkForInternalDuplicates(allData, rowIndex, "slug", row.slug)) {
            errors.slug = "Slug must be unique within the import data";
            valid = false;
          }
        }

        // Check availability against existing data
        if (!checkAvailabilityAgainstExisting("slug", row.slug)) {
          errors.slug = "Slug already exists";
          valid = false;
        }
      }
    }

    // Validate price fields
    const priceFields = ["basePrice", "standardCost", "minimumSellingPrice", "maximumRetailPrice", "wholesalePrice"];
    priceFields.forEach((field) => {
      if (row[field] !== undefined && row[field] !== null && row[field] !== "") {
        const numericValue = parseFloat(row[field]);
        if (isNaN(numericValue) || numericValue < 0) {
          errors[field] = `${field} must be a valid positive number`;
          valid = false;
        }
      }
    });

    // Validate numeric fields
    const numericFields = ["reorderLevel", "reorderQuantity"];
    numericFields.forEach((field) => {
      if (row[field] !== undefined && row[field] !== null && row[field] !== "") {
        const numericValue = parseInt(row[field]);
        if (isNaN(numericValue) || numericValue < 0) {
          errors[field] = `${field} must be a valid positive integer`;
          valid = false;
        }
      }
    });

    // Validate boolean fields
    const booleanFields = ["availableOnline", "featured"];
    booleanFields.forEach((field) => {
      if (row[field] !== undefined) {
        if (typeof row[field] === "string") {
          const normalizedValue = row[field].toLowerCase().trim();
          if (
            normalizedValue !== "true" &&
            normalizedValue !== "false" &&
            normalizedValue !== "yes" &&
            normalizedValue !== "no" &&
            normalizedValue !== "1" &&
            normalizedValue !== "0"
          ) {
            errors[field] = "Must be true/false, yes/no, or 1/0";
            valid = false;
          }
        } else if (typeof row[field] !== "boolean") {
          errors[field] = "Must be a boolean value";
          valid = false;
        }
      }
    });

    // Validate status if present
    if (row.status && typeof row.status === "string") {
      if (!Object.values(ProductStatus).includes(row.status as ProductStatus)) {
        errors.status = "Status must be a valid product status";
        valid = false;
      }
    }

    // Validate categoryId if present
    if (row.categoryId && row.categoryId !== "none") {
      const categoryExists = flatCategories.some((cat) => cat.id === row.categoryId && !cat.parentName);
      if (!categoryExists) {
        errors.categoryId = "Category not found";
        valid = false;
      }
    }

    // Validate subCategoryId if present
    if (row.subCategoryId && row.subCategoryId !== "none") {
      const subCategoryExists = flatCategories.some((cat) => cat.id === row.subCategoryId && cat.parentName);
      if (!subCategoryExists) {
        errors.subCategoryId = "Subcategory not found";
        valid = false;
      }
    }

    // Validate brandId if present
    if (row.brandId && row.brandId !== "none") {
      const brandExists = brands.some((brand) => brand.id === row.brandId);
      if (!brandExists) {
        errors.brandId = "Brand not found";
        valid = false;
      }
    }

    // Note: Image validation is simplified since CreateProductSchema doesn't support imageIndex
    // Images will be handled as a batch with the entire import

    return { valid, errors };
  };

  // Transform row data to CreateProductSchema format
  const transformRowToProduct = (row: Record<string, any>): CreateProductSchema => {
    // Handle boolean fields - could be boolean or string
    const parseBooleanField = (value: any, defaultValue: boolean = false): boolean => {
      if (value === undefined || value === null || value === "") return defaultValue;
      if (typeof value === "boolean") return value;
      if (typeof value === "string") {
        const normalizedValue = value.toLowerCase().trim();
        return normalizedValue === "true" || normalizedValue === "yes" || normalizedValue === "1";
      }
      return defaultValue;
    };

    // Generate slug from name if not provided
    const generateSlug = (name: string) => {
      return name
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
        .replace(/\s+/g, "-") // Replace spaces with hyphens
        .replace(/-+/g, "-") // Replace multiple hyphens with single
        .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
    };

    // Build product data
    const productData: CreateProductSchema = {
      name: row.name.trim(),
      productType: row.productType || ProductType.SINGLE,
      availableOnline: parseBooleanField(row.availableOnline, true),
      featured: parseBooleanField(row.featured, false),
      status: row.status || ProductStatus.ACTIVE,
      trackingType: row.trackingType || TrackingType.NONE,
      unitOfMeasure: row.unitOfMeasure || StandardUnitOfMeasure.PIECES,
      isAllocatedToAllLocations: false, // Default to false for imports
      isSellable: true, // Default to true for imports
      isIngredient: false, // Default to false for imports
      isManufacturable: false, // Default to false for imports
      isPurchasable: true, // Default to true for imports
    };

    // Add optional string fields if present and not empty
    const stringFields = [
      "shortCode", "sku", "barcode", "description", "shortDescription",
      "weight", "dimensions", "color", "size", "material", "model"
    ];
    stringFields.forEach((field) => {
      if (row[field] && row[field].trim()) {
        (productData as any)[field] = row[field].trim();
      }
    });

    // Handle slug - auto-generate if not provided
    if (row.slug && row.slug.trim()) {
      productData.slug = row.slug.trim();
    } else {
      productData.slug = generateSlug(row.name);
    }

    // Handle price fields
    const priceFields = ["basePrice", "standardCost", "minimumSellingPrice", "maximumRetailPrice", "wholesalePrice"];
    priceFields.forEach((field) => {
      if (row[field] && row[field] !== "") {
        const numericValue = parseFloat(row[field]);
        if (!isNaN(numericValue) && numericValue >= 0) {
          (productData as any)[field] = numericValue.toString();
        }
      }
    });

    // Handle numeric fields
    const numericFields = ["reorderLevel", "reorderQuantity"];
    numericFields.forEach((field) => {
      if (row[field] && row[field] !== "") {
        const numericValue = parseInt(row[field]);
        if (!isNaN(numericValue) && numericValue >= 0) {
          (productData as any)[field] = numericValue.toString();
        }
      }
    });

    // Handle foreign key fields
    if (row.categoryId && row.categoryId !== "none") {
      productData.categoryId = row.categoryId;
    }
    if (row.subCategoryId && row.subCategoryId !== "none") {
      productData.subCategoryId = row.subCategoryId;
    }
    if (row.brandId && row.brandId !== "none") {
      productData.brandId = row.brandId;
    }

    // Note: Image handling will be done separately via the images array parameter
    // The CreateProductSchema doesn't include imageIndex field

    return productData;
  };

  // Handle submission of product data
  const handleSubmitProducts = async (
    data: any[],
    mappings: FieldMapping[],
    images?: File[]
  ) => {
    try {
      setIsSubmitting(true);

      // Transform data to CreateProductSchema format
      const productsData: CreateProductSchema[] = data.map(transformRowToProduct);

      // Use the mutation hook to import products with images
      const result = await bulkImportMutation.mutateAsync({
        products: productsData,
        images: images || [],
      });

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        return Promise.resolve();
      } else {
        return Promise.reject(
          new Error(result.message || "Failed to import products")
        );
      }
    } catch (error) {
      console.error("Error importing products:", error);
      return Promise.reject(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Products"
      description="Import products from a CSV or Excel file. Make sure your file includes the required fields. You can assign categories, brands, and upload product images."
      targetFields={ALL_PRODUCT_FIELDS}
      fieldConfigs={PRODUCT_FIELD_CONFIGS}
      validateRow={validateProductRow}
      onSubmit={handleSubmitProducts}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}