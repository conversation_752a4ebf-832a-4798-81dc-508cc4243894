"use client";

import { type Table } from "@tanstack/react-table";
import { ProductSheet } from "./product-sheet";
import { ImportProductsSheet } from "./import-products-sheet";
import { BaseTableToolbarActions } from "@/components/shared/base-table-toolbar-actions";
import { useState } from "react";
import { ProductTableData } from "@/types/product";

interface ProductsTableToolbarActionsProps {
  table: Table<ProductTableData>;
  onRefresh?: () => Promise<void>;
  rowSelection: boolean;
  onRowSelectionChange: (value: boolean) => void;
  isDemo?: boolean;
}

export function ProductsTableToolbarActions({
  table,
  onRefresh,
  rowSelection,
  onRowSelectionChange,
  isDemo = false,
}: ProductsTableToolbarActionsProps) {
  const [showCreateSheet, setShowCreateSheet] = useState(false);
  const [showImportSheet, setShowImportSheet] = useState(false);

  return (
    <>
      <BaseTableToolbarActions<ProductTableData>
        table={table}
        onRefresh={onRefresh}
        rowSelection={rowSelection}
        onRowSelectionChange={onRowSelectionChange}
        isDemo={isDemo}
        title="Products"
        addButton={
          <ProductSheet
            open={showCreateSheet}
            onOpenChange={setShowCreateSheet}
            product={null}
            isDemo={isDemo}
            onSuccess={() => {
              setShowCreateSheet(false);
              onRefresh?.();
            }}
          />
        }
        additionalActions={
          <ImportProductsSheet
            open={showImportSheet}
            onOpenChange={setShowImportSheet}
            isDemo={isDemo}
            onSuccess={() => {
              setShowImportSheet(false);
              onRefresh?.();
            }}
          />
        }
      />
    </>
  );
}
