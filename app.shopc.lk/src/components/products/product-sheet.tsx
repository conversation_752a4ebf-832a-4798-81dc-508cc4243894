"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Package,
  DollarSign,
  Settings,
  Image as ImageIcon,
  Search,
  Globe,
  Tag,
  Truck,
  BarChart3,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { productFormSchema } from "@/lib/products/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ProductTableData,
  UpdateProductDto,
  CreateProductDto,
  ProductStatus,
  ProductType,
  TrackingType,
  TaxType,
  StandardUnitOfMeasure,
  ExpiryPeriodType,
} from "@/types/product";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useProductData,
  useProductNameAvailability,
  useProductSkuAvailability,
  useProductSlugAvailability,
  useProductBarcodeAvailability,
  useCreateProduct,
  useUpdateProduct,
} from "@/lib/products/hooks";
import { useCategoriesSlim } from "@/lib/categories/hooks";
import { useBrandsSlim } from "@/lib/brands/hooks";
import { useWarrantiesSlim } from "@/lib/warranties/hooks";
import { useSuppliersSlim } from "@/lib/suppliers/hooks";
import { useUnitsSlim } from "@/lib/units/hooks";
import { useTaxesSlim } from "@/lib/taxes/hooks";
import { useEffect } from "react";

interface ProductSheetProps {
  product: ProductTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (product?: ProductTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof productFormSchema>;

export function ProductSheet({
  product,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ProductSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [ogImageFiles, setOgImageFiles] = React.useState<UploadedFile[]>([]);
  const [keywords, setKeywords] = React.useState<string>("");

  // Values for availability checking
  const [checkName, setCheckName] = React.useState<string>("");
  const [checkSku, setCheckSku] = React.useState<string>("");
  const [checkSlug, setCheckSlug] = React.useState<string>("");
  const [checkBarcode, setCheckBarcode] = React.useState<string>("");

  // Fetch complete product data if updating
  const { data: fullProductResponse, isLoading: isLoadingProduct } =
    useProductData(product?.id || "", isDemo);
  const fullProduct = fullProductResponse?.data;

  useEffect(() => {
    if (fullProductResponse) {
      console.log("fullProductResponse", fullProductResponse);
    }
  }, [fullProductResponse]);

  // Fetch related data
  const { data: categoriesResponse } = useCategoriesSlim(isDemo);
  const categories = React.useMemo(
    () => categoriesResponse?.data || [],
    [categoriesResponse?.data]
  );

  const { data: brandsResponse } = useBrandsSlim(isDemo);
  const brands = React.useMemo(
    () => brandsResponse?.data || [],
    [brandsResponse?.data]
  );

  const { data: warrantiesResponse } = useWarrantiesSlim(isDemo);
  const warranties = React.useMemo(
    () => warrantiesResponse?.data || [],
    [warrantiesResponse?.data]
  );

  const { data: suppliersResponse } = useSuppliersSlim(isDemo);
  const suppliers = React.useMemo(
    () => suppliersResponse?.data || [],
    [suppliersResponse?.data]
  );

  const { data: unitsResponse } = useUnitsSlim(isDemo);
  const units = React.useMemo(
    () => unitsResponse?.data || [],
    [unitsResponse?.data]
  );

  const { data: taxesResponse } = useTaxesSlim(isDemo);
  const taxes = React.useMemo(
    () => taxesResponse?.data || [],
    [taxesResponse?.data]
  );

  // Mutation hooks for create and update operations
  const createProductMutation = useCreateProduct(isDemo);
  const updateProductMutation = useUpdateProduct(isDemo);

  // Availability checks (only check if not updating the same product)
  const shouldCheckNameAvailability =
    checkName.length > 0 &&
    (!isUpdate ||
      (product && checkName.toLowerCase() !== product.name.toLowerCase()));
  const shouldCheckSkuAvailability =
    checkSku.length > 0 &&
    (!isUpdate ||
      (product && checkSku.toLowerCase() !== product.sku?.toLowerCase()));
  const shouldCheckSlugAvailability =
    checkSlug.length > 0 &&
    (!isUpdate ||
      (product && checkSlug.toLowerCase() !== product.slug?.toLowerCase()));
  const shouldCheckBarcodeAvailability =
    checkBarcode.length > 0 &&
    (!isUpdate || (product && checkBarcode !== product.barcode));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useProductNameAvailability(checkName, isDemo);
  const { data: skuAvailabilityResponse, isLoading: isCheckingSku } =
    useProductSkuAvailability(checkSku, isDemo);
  const { data: slugAvailabilityResponse, isLoading: isCheckingSlug } =
    useProductSlugAvailability(checkSlug, isDemo);
  const { data: barcodeAvailabilityResponse, isLoading: isCheckingBarcode } =
    useProductBarcodeAvailability(checkBarcode, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isSkuAvailable = shouldCheckSkuAvailability
    ? skuAvailabilityResponse?.data?.available ?? true
    : true;
  const isSlugAvailable = shouldCheckSlugAvailability
    ? slugAvailabilityResponse?.data?.available ?? true
    : true;
  const isBarcodeAvailable = shouldCheckBarcodeAvailability
    ? barcodeAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      name: "",
      productType: ProductType.SINGLE,
      shortCode: "",
      sku: "",
      barcode: "",
      description: "",
      shortDescription: "",
      slug: "",
      categoryId: "",
      subCategoryId: "",
      brandId: "",
      warrantyId: "",
      preferredSupplierId: "",
      isSellable: true,
      isIngredient: false,
      isManufacturable: false,
      isPurchasable: true,
      basePrice: "",
      standardCost: "",
      lastPurchasePrice: "",
      minimumSellingPrice: "",
      maximumRetailPrice: "",
      wholesalePrice: "",
      taxType: TaxType.INCLUSIVE,
      salesTaxId: "",
      purchaseTaxId: "",
      trackingType: TrackingType.QUANTITY,
      unitOfMeasure: StandardUnitOfMeasure.PIECES,
      unitId: "",
      reorderLevel: "",
      reorderQuantity: "",
      expiryPeriod: 0,
      expiryPeriodType: ExpiryPeriodType.DAYS,
      availableOnline: true,
      featured: false,
      weight: "",
      dimensions: "",
      color: "",
      size: "",
      material: "",
      model: "",
      tags: [],
      notes: "",
      status: ProductStatus.ACTIVE,
      isAllocatedToAllLocations: true,
      locationIds: [],
      seoTitle: "",
      seoDescription: "",
      seoKeywords: [],
    },
  });

  // Get the selected image file for API calls (only if it's a real file, not the mock one for existing images)
  const selectedImage =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  // Get the selected OG image file for API calls
  const selectedOgImage =
    ogImageFiles.length > 0 && !ogImageFiles[0].id.startsWith("existing-og-")
      ? ogImageFiles[0].file
      : null;

  // Helper function to parse keywords
  const parseKeywords = (keywordsString: string): string[] => {
    return keywordsString
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k.length > 0);
  };

  // Get subcategories based on selected category
  const subcategories = React.useMemo(() => {
    const categoryId = form.watch("categoryId");
    if (!categoryId) return [];
    const selectedCategory = categories.find((cat) => cat.id === categoryId);
    return selectedCategory?.subcategories || [];
  }, [categories, form.watch("categoryId")]);

  React.useEffect(() => {
    if (product && fullProduct) {
      // Use full product data for populating the form
      form.reset({
        name: fullProduct.name,
        productType: fullProduct.productType,
        shortCode: fullProduct.shortCode || "",
        sku: fullProduct.sku || "",
        barcode: fullProduct.barcode || "",
        description: fullProduct.description || "",
        shortDescription: fullProduct.shortDescription || "",
        slug: fullProduct.slug || "",
        categoryId: fullProduct.categoryId || "",
        subCategoryId: fullProduct.subCategoryId || "",
        brandId: fullProduct.brandId || "",
        warrantyId: fullProduct.warrantyId || "",
        preferredSupplierId: fullProduct.preferredSupplierId || "",
        isSellable: fullProduct.isSellable,
        isIngredient: fullProduct.isIngredient,
        isManufacturable: fullProduct.isManufacturable,
        isPurchasable: fullProduct.isPurchasable,
        basePrice: fullProduct.basePrice || "",
        standardCost: fullProduct.standardCost || "",
        lastPurchasePrice: fullProduct.lastPurchasePrice || "",
        minimumSellingPrice: fullProduct.minimumSellingPrice || "",
        maximumRetailPrice: fullProduct.maximumRetailPrice || "",
        wholesalePrice: fullProduct.wholesalePrice || "",
        taxType: fullProduct.taxType || TaxType.INCLUSIVE,
        salesTaxId: fullProduct.salesTaxId || "",
        purchaseTaxId: fullProduct.purchaseTaxId || "",
        trackingType: fullProduct.trackingType,
        unitOfMeasure:
          fullProduct.unitOfMeasure || StandardUnitOfMeasure.PIECES,
        unitId: fullProduct.unitId || "",
        reorderLevel: fullProduct.reorderLevel || "",
        reorderQuantity: fullProduct.reorderQuantity || "",
        expiryPeriod: fullProduct.expiryPeriod || 0,
        expiryPeriodType: fullProduct.expiryPeriodType || ExpiryPeriodType.DAYS,
        availableOnline: fullProduct.availableOnline,
        featured: fullProduct.featured,
        weight: fullProduct.weight || "",
        dimensions: fullProduct.dimensions || "",
        color: fullProduct.color || "",
        size: fullProduct.size || "",
        material: fullProduct.material || "",
        model: fullProduct.model || "",
        tags: fullProduct.tags || [],
        notes: fullProduct.notes || "",
        status: fullProduct.status,
        isAllocatedToAllLocations: fullProduct.isAllocatedToAllLocations,
        locationIds: fullProduct.locations?.map((loc) => loc.id) || [],
        seoTitle: fullProduct.seoTitle || "",
        seoDescription: fullProduct.seoDescription || "",
        seoKeywords: fullProduct.seoKeywords || [],
      });

      // Set keywords state for UI
      setKeywords(fullProduct.seoKeywords?.join(", ") || "");

      // Trigger validation to show errors for required fields based on availableOnline status
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);

      // Set existing image as uploaded file for preview
      if (fullProduct.image) {
        // Create a mock UploadedFile for existing image
        setUploadedFiles([
          {
            id: `existing-${fullProduct.id}`,
            file: new File([], fullProduct.name + "-image", {
              type: "image/jpeg",
            }),
            preview: fullProduct.image,
          },
        ]);
      } else {
        setUploadedFiles([]);
      }

      // Set existing OG image as uploaded file for preview
      if (fullProduct.ogImage) {
        setOgImageFiles([
          {
            id: `existing-og-${fullProduct.id}`,
            file: new File([], fullProduct.name + "-og-image", {
              type: "image/jpeg",
            }),
            preview: fullProduct.ogImage,
          },
        ]);
      } else {
        setOgImageFiles([]);
      }
    } else if (!product) {
      // Reset form for new product
      form.reset({
        name: "",
        productType: ProductType.SINGLE,
        shortCode: "",
        sku: "",
        barcode: "",
        description: "",
        shortDescription: "",
        slug: "",
        categoryId: "",
        subCategoryId: "",
        brandId: "",
        warrantyId: "",
        preferredSupplierId: "",
        isSellable: true,
        isIngredient: false,
        isManufacturable: false,
        isPurchasable: true,
        basePrice: "",
        standardCost: "",
        lastPurchasePrice: "",
        minimumSellingPrice: "",
        maximumRetailPrice: "",
        wholesalePrice: "",
        taxType: TaxType.INCLUSIVE,
        salesTaxId: "",
        purchaseTaxId: "",
        trackingType: TrackingType.QUANTITY,
        unitOfMeasure: StandardUnitOfMeasure.PIECES,
        unitId: "",
        reorderLevel: "",
        reorderQuantity: "",
        expiryPeriod: 0,
        expiryPeriodType: ExpiryPeriodType.DAYS,
        availableOnline: true,
        featured: false,
        weight: "",
        dimensions: "",
        color: "",
        size: "",
        material: "",
        model: "",
        tags: [],
        notes: "",
        status: ProductStatus.ACTIVE,
        isAllocatedToAllLocations: true,
        locationIds: [],
        seoTitle: "",
        seoDescription: "",
        seoKeywords: [],
      });
      setKeywords("");
      setUploadedFiles([]);
      setOgImageFiles([]);

      // Trigger validation for new product (availableOnline defaults to true)
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);
    }
  }, [product, fullProduct]);

  // Reset form when sheet closes and trigger validation when opens
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setOgImageFiles([]);
      setKeywords("");
    } else {
      // When sheet opens, trigger validation to show required field errors
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 200);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.name ||
    errors.shortCode ||
    errors.sku ||
    errors.barcode ||
    errors.description ||
    errors.shortDescription ||
    errors.slug ||
    errors.productType ||
    errors.categoryId ||
    errors.subCategoryId ||
    errors.brandId ||
    errors.warrantyId ||
    errors.preferredSupplierId ||
    (shouldCheckNameAvailability && isNameAvailable === false) ||
    (shouldCheckSkuAvailability && isSkuAvailable === false) ||
    (shouldCheckSlugAvailability && isSlugAvailable === false) ||
    (shouldCheckBarcodeAvailability && isBarcodeAvailable === false)
  );

  const hasPricingErrors = !!(
    errors.basePrice ||
    errors.standardCost ||
    errors.lastPurchasePrice ||
    errors.minimumSellingPrice ||
    errors.maximumRetailPrice ||
    errors.wholesalePrice ||
    errors.taxType ||
    errors.salesTaxId ||
    errors.purchaseTaxId
  );

  const hasInventoryErrors = !!(
    errors.trackingType ||
    errors.unitOfMeasure ||
    errors.unitId ||
    errors.reorderLevel ||
    errors.reorderQuantity ||
    errors.expiryPeriod ||
    errors.expiryPeriodType
  );

  const hasAttributesErrors = !!(
    errors.weight ||
    errors.dimensions ||
    errors.color ||
    errors.size ||
    errors.material ||
    errors.model ||
    errors.tags ||
    errors.notes
  );

  const hasSEOErrors = !!(
    errors.seoTitle ||
    errors.seoDescription ||
    errors.seoKeywords ||
    errors.slug
  );

  const hasSettingsErrors = !!(
    errors.status ||
    errors.availableOnline ||
    errors.featured ||
    errors.isSellable ||
    errors.isIngredient ||
    errors.isManufacturable ||
    errors.isPurchasable ||
    errors.isAllocatedToAllLocations ||
    errors.locationIds
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Product name is already taken. Please choose a different name."
      );
      return;
    }

    if (shouldCheckSkuAvailability && isSkuAvailable === false) {
      toast.error(
        "Product SKU is already taken. Please choose a different SKU."
      );
      return;
    }

    if (shouldCheckSlugAvailability && isSlugAvailable === false) {
      toast.error(
        "Product slug is already taken. Please choose a different slug."
      );
      return;
    }

    if (shouldCheckBarcodeAvailability && isBarcodeAvailable === false) {
      toast.error(
        "Product barcode is already taken. Please choose a different barcode."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (product) {
        // Update existing product
        const updateData: UpdateProductDto = {
          ...data,
          tags: data.tags?.length ? data.tags : undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await updateProductMutation.mutateAsync({
          id: product.id,
          data: updateData,
          image: selectedImage || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Product updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ProductTableData);
          return;
        }
        toast.error(response.message || "Failed to update product");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new product
        const createData: CreateProductDto = {
          ...data,
          tags: data.tags?.length ? data.tags : undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await createProductMutation.mutateAsync({
          data: createData,
          image: selectedImage || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Product created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ProductTableData);
          return;
        }
        toast.error(response.message || "Failed to create product");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save product:", error);
      toast.error("Failed to save product");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  };

  // Auto-generate slug when name changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (name === "name" && value.name) {
        form.setValue("slug", generateSlug(value.name));
      }
      // Trigger validation when availableOnline changes to show/hide required field errors
      if (name === "availableOnline") {
        // Force validation of SEO fields when availableOnline changes
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Handle availability checks
  const handleCheckNameAvailability = (value: string) => {
    setCheckName(value);
  };

  const handleCheckSkuAvailability = (value: string) => {
    setCheckSku(value);
  };

  const handleCheckSlugAvailability = (value: string) => {
    setCheckSlug(value);
  };

  const handleCheckBarcodeAvailability = (value: string) => {
    setCheckBarcode(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="name"
                label="Product Name"
                placeholder="Enter product name (e.g., iPhone 15 Pro, Nike Air Max)"
                value={form.watch("name") || ""}
                onChange={(value) =>
                  form.setValue("name", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckNameAvailability}
                isCheckingAvailability={isCheckingName}
                isAvailable={isNameAvailable}
                shouldCheck={shouldCheckNameAvailability}
                error={errors.name?.message}
                disabled={isSubmitting}
                fieldName="Product name"
              />

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Product Type</Label>
                  <Select
                    value={form.watch("productType")}
                    onValueChange={(value) =>
                      form.setValue("productType", value as ProductType, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.productType && "border-destructive")}
                    >
                      <SelectValue placeholder="Select product type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ProductType.SINGLE}>
                        Single Product
                      </SelectItem>
                      <SelectItem value={ProductType.VARIABLE}>
                        Variable Product
                      </SelectItem>
                      <SelectItem value={ProductType.COMBO}>
                        Combo Product
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.productType && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.productType.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Short Code</Label>
                  <Input
                    {...form.register("shortCode")}
                    name="shortCode"
                    placeholder="e.g., IP15P, NAM90"
                    className={cn(errors.shortCode && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.shortCode && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.shortCode.message}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Optional short identifier for the product
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <AvailabilityInput
                  name="sku"
                  label="SKU"
                  placeholder="Stock Keeping Unit"
                  value={form.watch("sku") || ""}
                  onChange={(value) =>
                    form.setValue("sku", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckSkuAvailability}
                  isCheckingAvailability={isCheckingSku}
                  isAvailable={isSkuAvailable}
                  shouldCheck={shouldCheckSkuAvailability}
                  error={errors.sku?.message}
                  disabled={isSubmitting}
                  fieldName="SKU"
                />

                <AvailabilityInput
                  name="barcode"
                  label="Barcode"
                  placeholder="Product barcode"
                  value={form.watch("barcode") || ""}
                  onChange={(value) =>
                    form.setValue("barcode", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckBarcodeAvailability}
                  isCheckingAvailability={isCheckingBarcode}
                  isAvailable={isBarcodeAvailable}
                  shouldCheck={shouldCheckBarcodeAvailability}
                  error={errors.barcode?.message}
                  disabled={isSubmitting}
                  fieldName="Barcode"
                />
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Detailed product description..."
                  rows={3}
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Short Description</Label>
                <Textarea
                  {...form.register("shortDescription")}
                  name="shortDescription"
                  placeholder="Brief product summary..."
                  rows={2}
                  className={cn(
                    errors.shortDescription && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.shortDescription && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.shortDescription.message}
                  </p>
                )}
              </div>

              <div>
                <AvailabilityInput
                  name="slug"
                  label={`URL Slug${form.watch("availableOnline") ? " *" : ""}`}
                  placeholder="product-url-slug (auto-generated from name)"
                  value={form.watch("slug") || ""}
                  onChange={(value) =>
                    form.setValue("slug", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckSlugAvailability}
                  isCheckingAvailability={isCheckingSlug}
                  isAvailable={isSlugAvailable}
                  shouldCheck={shouldCheckSlugAvailability}
                  error={errors.slug?.message}
                  disabled={isSubmitting}
                  fieldName="Slug"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Used for SEO-friendly URLs (automatically generated if left
                  empty)
                  {form.watch("availableOnline") && (
                    <span className="block text-orange-600 font-medium">
                      Required when product is available online
                    </span>
                  )}
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Category</Label>
                  <Select
                    value={form.watch("categoryId")}
                    onValueChange={(value) => {
                      form.setValue("categoryId", value, {
                        shouldValidate: true,
                      });
                      form.setValue("subCategoryId", "", {
                        shouldValidate: true,
                      }); // Reset subcategory when category changes
                    }}
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.categoryId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.categoryId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.categoryId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Subcategory</Label>
                  <Select
                    value={form.watch("subCategoryId")}
                    onValueChange={(value) =>
                      form.setValue("subCategoryId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={
                      !form.watch("categoryId") ||
                      subcategories.length === 0 ||
                      isSubmitting
                    }
                  >
                    <SelectTrigger
                      className={cn(
                        errors.subCategoryId && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select subcategory" />
                    </SelectTrigger>
                    <SelectContent>
                      {subcategories.map((subcategory) => (
                        <SelectItem key={subcategory.id} value={subcategory.id}>
                          {subcategory.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.subCategoryId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.subCategoryId.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Brand</Label>
                  <Select
                    value={form.watch("brandId")}
                    onValueChange={(value) =>
                      form.setValue("brandId", value, { shouldValidate: true })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.brandId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select brand" />
                    </SelectTrigger>
                    <SelectContent>
                      {brands.map((brand) => (
                        <SelectItem key={brand.id} value={brand.id}>
                          {brand.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.brandId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.brandId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Warranty</Label>
                  <Select
                    value={form.watch("warrantyId")}
                    onValueChange={(value) =>
                      form.setValue("warrantyId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.warrantyId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select warranty" />
                    </SelectTrigger>
                    <SelectContent>
                      {warranties.map((warranty) => (
                        <SelectItem key={warranty.id} value={warranty.id}>
                          {warranty.name} ({warranty.durationMonths} months)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.warrantyId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.warrantyId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Preferred Supplier</Label>
                  <Select
                    value={form.watch("preferredSupplierId")}
                    onValueChange={(value) =>
                      form.setValue("preferredSupplierId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(
                        errors.preferredSupplierId && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.preferredSupplierId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.preferredSupplierId.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "pricing",
      title: "Pricing & Tax",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPricingErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Pricing & Tax</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Base Price</Label>
                  <Input
                    {...form.register("basePrice")}
                    name="basePrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.basePrice && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.basePrice && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.basePrice.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Standard Cost</Label>
                  <Input
                    {...form.register("standardCost")}
                    name="standardCost"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(errors.standardCost && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.standardCost && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.standardCost.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Minimum Selling Price</Label>
                  <Input
                    {...form.register("minimumSellingPrice")}
                    name="minimumSellingPrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(
                      errors.minimumSellingPrice && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.minimumSellingPrice && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.minimumSellingPrice.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Maximum Retail Price</Label>
                  <Input
                    {...form.register("maximumRetailPrice")}
                    name="maximumRetailPrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(
                      errors.maximumRetailPrice && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.maximumRetailPrice && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.maximumRetailPrice.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Wholesale Price</Label>
                  <Input
                    {...form.register("wholesalePrice")}
                    name="wholesalePrice"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className={cn(
                      errors.wholesalePrice && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.wholesalePrice && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.wholesalePrice.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label>Tax Type</Label>
                <Select
                  value={form.watch("taxType")}
                  onValueChange={(value) =>
                    form.setValue("taxType", value as TaxType, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.taxType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select tax type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaxType.INCLUSIVE}>
                      Tax Inclusive
                    </SelectItem>
                    <SelectItem value={TaxType.EXCLUSIVE}>
                      Tax Exclusive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.taxType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.taxType.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Sales Tax</Label>
                  <Select
                    value={form.watch("salesTaxId")}
                    onValueChange={(value) =>
                      form.setValue("salesTaxId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.salesTaxId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select sales tax" />
                    </SelectTrigger>
                    <SelectContent>
                      {taxes.map((tax) => (
                        <SelectItem key={tax.id} value={tax.id}>
                          {tax.name} ({tax.rate}%)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.salesTaxId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.salesTaxId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Purchase Tax</Label>
                  <Select
                    value={form.watch("purchaseTaxId")}
                    onValueChange={(value) =>
                      form.setValue("purchaseTaxId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(
                        errors.purchaseTaxId && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select purchase tax" />
                    </SelectTrigger>
                    <SelectContent>
                      {taxes.map((tax) => (
                        <SelectItem key={tax.id} value={tax.id}>
                          {tax.name} ({tax.rate}%)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.purchaseTaxId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.purchaseTaxId.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "inventory",
      title: "Inventory & Tracking",
      icon: <Package className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasInventoryErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Inventory & Tracking</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Tracking Type</Label>
                <Select
                  value={form.watch("trackingType")}
                  onValueChange={(value) =>
                    form.setValue("trackingType", value as TrackingType, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.trackingType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select tracking type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TrackingType.NONE}>
                      No Tracking
                    </SelectItem>
                    <SelectItem value={TrackingType.QUANTITY}>
                      Quantity Tracking
                    </SelectItem>
                    <SelectItem value={TrackingType.SERIAL}>
                      Serial Number Tracking
                    </SelectItem>
                    <SelectItem value={TrackingType.BATCH}>
                      Batch/Lot Tracking
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.trackingType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.trackingType.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Unit of Measure</Label>
                  <Select
                    value={form.watch("unitOfMeasure")}
                    onValueChange={(value) =>
                      form.setValue(
                        "unitOfMeasure",
                        value as StandardUnitOfMeasure,
                        { shouldValidate: true }
                      )
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(
                        errors.unitOfMeasure && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(StandardUnitOfMeasure).map((unit) => (
                        <SelectItem key={unit} value={unit}>
                          {unit}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.unitOfMeasure && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.unitOfMeasure.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Custom Unit</Label>
                  <Select
                    value={form.watch("unitId")}
                    onValueChange={(value) =>
                      form.setValue("unitId", value, { shouldValidate: true })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.unitId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select custom unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          {unit.name} ({unit.code})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.unitId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.unitId.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Reorder Level</Label>
                  <Input
                    {...form.register("reorderLevel")}
                    name="reorderLevel"
                    type="number"
                    step="0.01"
                    placeholder="Minimum stock level"
                    className={cn(errors.reorderLevel && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.reorderLevel && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.reorderLevel.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Reorder Quantity</Label>
                  <Input
                    {...form.register("reorderQuantity")}
                    name="reorderQuantity"
                    type="number"
                    step="0.01"
                    placeholder="Quantity to reorder"
                    className={cn(
                      errors.reorderQuantity && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.reorderQuantity && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.reorderQuantity.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Expiry Period</Label>
                  <Input
                    {...form.register("expiryPeriod", { valueAsNumber: true })}
                    name="expiryPeriod"
                    type="number"
                    placeholder="0"
                    className={cn(errors.expiryPeriod && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.expiryPeriod && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.expiryPeriod.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Expiry Period Type</Label>
                  <Select
                    value={form.watch("expiryPeriodType")}
                    onValueChange={(value) =>
                      form.setValue(
                        "expiryPeriodType",
                        value as ExpiryPeriodType,
                        { shouldValidate: true }
                      )
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(
                        errors.expiryPeriodType && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select period type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={ExpiryPeriodType.DAYS}>
                        Days
                      </SelectItem>
                      <SelectItem value={ExpiryPeriodType.MONTHS}>
                        Months
                      </SelectItem>
                      <SelectItem value={ExpiryPeriodType.YEARS}>
                        Years
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.expiryPeriodType && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.expiryPeriodType.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "attributes",
      title: "Product Attributes",
      icon: <Tag className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasAttributesErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Product Attributes</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Weight</Label>
                  <Input
                    {...form.register("weight")}
                    name="weight"
                    placeholder="e.g., 1.5 kg"
                    className={cn(errors.weight && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.weight && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.weight.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Dimensions</Label>
                  <Input
                    {...form.register("dimensions")}
                    name="dimensions"
                    placeholder="e.g., 10x5x3 cm"
                    className={cn(errors.dimensions && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.dimensions && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.dimensions.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Color</Label>
                  <Input
                    {...form.register("color")}
                    name="color"
                    placeholder="e.g., Red, Blue, Black"
                    className={cn(errors.color && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.color && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.color.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Size</Label>
                  <Input
                    {...form.register("size")}
                    name="size"
                    placeholder="e.g., S, M, L, XL"
                    className={cn(errors.size && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.size && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.size.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Material</Label>
                  <Input
                    {...form.register("material")}
                    name="material"
                    placeholder="e.g., Cotton, Leather, Metal"
                    className={cn(errors.material && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.material && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.material.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Model</Label>
                  <Input
                    {...form.register("model")}
                    name="model"
                    placeholder="e.g., Model ABC-123"
                    className={cn(errors.model && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.model && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.model.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label>Notes</Label>
                <Textarea
                  {...form.register("notes")}
                  name="notes"
                  placeholder="Additional notes about the product..."
                  rows={3}
                  className={cn(errors.notes && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.notes && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.notes.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "product-image",
      title: "Product Images",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Product Images</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Product Image"
              description="Upload an image for this product. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ProductTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={product}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Product"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingProduct}
      // Location props
      isLocationEnabled={true}
      isAllocatedToAllLocations={
        form.watch("isAllocatedToAllLocations") || false
      }
      onAllLocationsChange={(isAllocated) => {
        form.setValue("isAllocatedToAllLocations", isAllocated, {
          shouldValidate: true,
        });
        // Clear location IDs when allocating to all locations
        if (isAllocated) {
          form.setValue("locationIds", [], { shouldValidate: true });
        }
      }}
      locationIds={form.watch("locationIds") || []}
      onLocationChange={(locationIds) => {
        form.setValue("locationIds", locationIds, { shouldValidate: true });
      }}
      locationErrors={
        errors.locationIds?.message || errors.isAllocatedToAllLocations?.message
      }
      // SEO props
      isSEOEnabled={true}
      seoTitle={form.watch("seoTitle") || ""}
      onSeoTitleChange={(value) => {
        form.setValue("seoTitle", value, { shouldValidate: true });
      }}
      seoTitleError={errors.seoTitle?.message}
      seoDescription={form.watch("seoDescription") || ""}
      onSeoDescriptionChange={(value) => {
        form.setValue("seoDescription", value, { shouldValidate: true });
      }}
      seoDescriptionError={errors.seoDescription?.message}
      seoKeywords={keywords}
      onSeoKeywordsChange={(value) => {
        setKeywords(value);
        form.setValue("seoKeywords", parseKeywords(value), {
          shouldValidate: true,
        });
      }}
      seoKeywordsError={errors.seoKeywords?.message}
      ogImageFiles={ogImageFiles}
      onOgImageFilesChange={setOgImageFiles}
    />
  );
}
