"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Eye,
  Package,
  Barcode,
  DollarSign,
  GripVertical,
} from "lucide-react";
import { ProductTableData, ProductStatus } from "@/types/product";
import { ProductStatusBadge } from "./product-status-badge";
import { ProductAvailableOnlineBadge } from "./product-available-online-badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SortableDragHandle } from "@/components/ui/sortable";
import { Badge } from "@/components/ui/badge";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  isSortableEnabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (productId: string, newStatus: ProductStatus) => void;
  onAvailableOnlineUpdate?: (
    productId: string,
    newAvailableOnline: boolean
  ) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  isSortableEnabled = false,
  onStatusUpdate,
  onAvailableOnlineUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<ProductTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    // Drag handle column for sortable rows
    ...(isSortableEnabled
      ? [
          {
            id: "drag",
            header: "",
            cell: () => (
              <SortableDragHandle
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                disabled={isActionsDisabled}
              >
                <GripVertical className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">Drag to reorder</span>
              </SortableDragHandle>
            ),
            enableSorting: false,
            enableHiding: false,
            size: 40,
          } as ColumnDef<ProductTableData>,
        ]
      : []),
    {
      accessorKey: "image",
      header: "",
      cell: ({ row }) => {
        const product = row.original;
        return (
          <Avatar className="h-10 w-10 rounded-md">
            <AvatarImage
              src={product.image || ""}
              alt={product.name}
              className="object-cover"
            />
            <AvatarFallback className="rounded-md bg-muted">
              <Package className="h-4 w-4 text-muted-foreground" />
            </AvatarFallback>
          </Avatar>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 60,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Product Name" />
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">{product.name}</span>
            {product.shortCode && (
              <span className="text-xs text-muted-foreground">
                Code: {product.shortCode}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "sku",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="SKU" />
      ),
      cell: ({ row }) => {
        const sku = row.getValue("sku") as string;
        return sku ? (
          <div className="flex items-center gap-2">
            <Barcode className="h-4 w-4 text-muted-foreground" />
            <span className="font-mono text-sm">{sku}</span>
          </div>
        ) : (
          <span className="text-muted-foreground">—</span>
        );
      },
    },
    {
      accessorKey: "category",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Category" />
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <div className="flex flex-col gap-1">
            {product.categoryName && (
              <Badge variant="secondary" className="text-xs">
                {product.categoryName}
              </Badge>
            )}
            {product.subCategoryName && (
              <Badge variant="outline" className="text-xs">
                {product.subCategoryName}
              </Badge>
            )}
            {!product.categoryName && !product.subCategoryName && (
              <span className="text-muted-foreground">—</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "basePrice",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price" />
      ),
      cell: ({ row }) => {
        const basePrice = row.getValue("basePrice") as string;
        return basePrice ? (
          <div className="flex items-center gap-2">
            <DollarSign className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">
              ${parseFloat(basePrice).toFixed(2)}
            </span>
          </div>
        ) : (
          <span className="text-muted-foreground">—</span>
        );
      },
    },
    {
      accessorKey: "productType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Type" />
      ),
      cell: ({ row }) => {
        const productType = row.getValue("productType") as string;
        return (
          <Badge variant="outline" className="text-xs">
            {productType}
          </Badge>
        );
      },
    },
    {
      accessorKey: "trackingType",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Tracking" />
      ),
      cell: ({ row }) => {
        const trackingType = row.getValue("trackingType") as string;
        return (
          <Badge variant="secondary" className="text-xs">
            {trackingType}
          </Badge>
        );
      },
    },
    {
      accessorKey: "availableOnline",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Online" />
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <ProductAvailableOnlineBadge
            availableOnline={product.availableOnline}
            productId={product.id}
            isDemo={isDemo}
            onUpdate={onAvailableOnlineUpdate}
          />
        );
      },
    },
    {
      accessorKey: "featured",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Featured" />
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <Badge
            variant={product.featured ? "default" : "outline"}
            className="text-xs"
          >
            {product.featured ? "Featured" : "Not Featured"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const product = row.original;
        return (
          <ProductStatusBadge
            status={product.status}
            productId={product.id}
            isDemo={isDemo}
            onUpdate={onStatusUpdate}
          />
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() => setRowAction({ type: "view", row })}
              >
                <Eye className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setRowAction({ type: "update", row })}
              >
                Edit
                <DropdownMenuShortcut>⌘⏎</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setRowAction({ type: "delete", row })}
                className="text-destructive focus:text-destructive"
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}
