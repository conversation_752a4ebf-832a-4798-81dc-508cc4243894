"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import {
  Trash2,
  <PERSON>Circle2,
  XCircle,
  Eye,
  EyeOff,
  Star,
  <PERSON>Off,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type ProductTableData, ProductStatus } from "@/types/product";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteProducts,
  useBulkUpdateProductStatus,
  useBulkUpdateProductAvailability,
  useBulkUpdateProductFeatured,
} from "@/lib/products/hooks";
import { ApiStatus } from "@/types/common";

interface ProductsTableFloatingBarProps {
  table: Table<ProductTableData>;
  onRefresh?: () => void;
  isDemo?: boolean;
}

export function ProductsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: ProductsTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    | "delete"
    | "activate"
    | "deactivate"
    | "make-available"
    | "make-unavailable"
    | "feature"
    | "unfeature"
  >();

  const bulkDeleteMutation = useBulkDeleteProducts(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateProductStatus(isDemo);
  const bulkUpdateAvailabilityMutation =
    useBulkUpdateProductAvailability(isDemo);
  const bulkUpdateFeaturedMutation = useBulkUpdateProductFeatured(isDemo);

  const isPending =
    bulkUpdateStatusMutation.isPending ||
    bulkUpdateAvailabilityMutation.isPending ||
    bulkUpdateFeaturedMutation.isPending;

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectionCount = selectedRows.length;

  // Handle bulk delete
  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return;

    try {
      const productIds = selectedRows.map((row) => row.original.id);
      const result = await bulkDeleteMutation.mutateAsync(productIds);

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast({
          title: "Success",
          description: result.data.message,
        });
        table.toggleAllRowsSelected(false);
        onRefresh?.();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to delete products",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting products:", error);
      toast({
        title: "Error",
        description: "Failed to delete products",
        variant: "destructive",
      });
    }
  }, [selectionCount, selectedRows, bulkDeleteMutation, table, onRefresh]);

  // Handle bulk status update
  const handleBulkStatusUpdate = React.useCallback(
    async (status: ProductStatus) => {
      if (selectionCount === 0) return;

      try {
        const productIds = selectedRows.map((row) => row.original.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          productIds,
          status,
        });

        if (result.status === ApiStatus.SUCCESS && result.data) {
          toast({
          title: "Success",
          description: result.data.message,
        });
          table.toggleAllRowsSelected(false);
          onRefresh?.();
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to update product status",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error updating product status:", error);
        toast({
          title: "Error",
          description: "Failed to update product status",
          variant: "destructive"
        });
      }
    },
    [selectionCount, selectedRows, bulkUpdateStatusMutation, table, onRefresh]
  );

  // Handle bulk availability update
  const handleBulkAvailabilityUpdate = React.useCallback(
    async (availableOnline: boolean) => {
      if (selectionCount === 0) return;

      try {
        const productIds = selectedRows.map((row) => row.original.id);
        const result = await bulkUpdateAvailabilityMutation.mutateAsync({
          productIds,
          availableOnline,
        });

        if (result.status === ApiStatus.SUCCESS && result.data) {
          toast({
          title: "Success",
          description: result.data.message,
        });
          table.toggleAllRowsSelected(false);
          onRefresh?.();
        } else {
            toast({
            title: "Error",
            description: result.message || "Failed to update product availability",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error updating product availability:", error);
        toast({
          title: "Error",
          description: "Failed to update product availability",
          variant: "destructive"
        });
      }
    },
    [
      selectionCount,
      selectedRows,
      bulkUpdateAvailabilityMutation,
      table,
      onRefresh,
    ]
  );

  // Handle bulk featured update
  const handleBulkFeaturedUpdate = React.useCallback(
    async (featured: boolean) => {
      if (selectionCount === 0) return;

      try {
        const productIds = selectedRows.map((row) => row.original.id);
        const result = await bulkUpdateFeaturedMutation.mutateAsync({
          productIds,
          featured,
        });

        if (result.status === ApiStatus.SUCCESS && result.data) {
          toast({
          title: "Success",
          description: result.data.message,
        });
          table.toggleAllRowsSelected(false);
          onRefresh?.();
        } else {
            toast({
            title: "Error",
            description: result.message || "Failed to update product featured status",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error updating product featured status:", error);
        toast({
          title: "Error",
          description: "Failed to update product featured status",
          variant: "destructive"
        });
      }
    },
    [selectionCount, selectedRows, bulkUpdateFeaturedMutation, table, onRefresh]
  );

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(ProductStatus.ACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(ProductStatus.INACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkAvailabilityUpdate(true)}
            disabled={isPending || selectionCount === 0}
          >
            <Eye className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Make available online</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkAvailabilityUpdate(false)}
            disabled={isPending || selectionCount === 0}
          >
            <EyeOff className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Make unavailable online</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkFeaturedUpdate(true)}
            disabled={isPending || selectionCount === 0}
          >
            <Star className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Feature selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkFeaturedUpdate(false)}
            disabled={isPending || selectionCount === 0}
          >
            <StarOff className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Unfeature selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={bulkDeleteMutation.isPending || selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<ProductTableData>
        table={table}
        title="Products"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} product${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass product IDs since they're deleted
        }}
      />
    </>
  );
}
