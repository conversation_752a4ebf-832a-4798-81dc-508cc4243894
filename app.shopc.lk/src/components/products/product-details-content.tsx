"use client";

import * as React from "react";
import {
  Info,
  Package,
  DollarSign,
  Settings,
  Image as ImageIcon,
  Search,
  Calendar,
  MapPin,
  Globe,
  Eye,
  EyeOff,
  Printer,
  Download,
  Barcode,
  Hash,
  Star,
  Truck,
  BarChart3,
  Tag,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useProductData } from "@/lib/products/hooks";
import { useCategoriesSlim } from "@/lib/categories/hooks";
import { useBrandsSlim } from "@/lib/brands/hooks";
import {
  ProductTableData,
  ProductStatus,
  ProductType,
  TrackingType,
} from "@/types/product";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { useToast } from "@/hooks/use-toast";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface ProductDetailsContentProps {
  product: ProductTableData;
  isDemo?: boolean;
}

export function ProductDetailsContent({
  product,
  isDemo = false,
}: ProductDetailsContentProps) {
  const { toast } = useToast();
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch complete product data
  const { data: fullProductResponse, isLoading: isLoadingProduct } =
    useProductData(product.id, isDemo);
  const fullProduct = fullProductResponse?.data;

  // Fetch related data for display
  const { data: categoriesResponse } = useCategoriesSlim(isDemo);
  const categories = categoriesResponse?.data || [];

  const { data: brandsResponse } = useBrandsSlim(isDemo);
  const brands = brandsResponse?.data || [];

  // Find related entities
  const category = categories.find((cat) => cat.id === fullProduct?.categoryId);
  const subcategory = category?.subcategories?.find(
    (sub) => sub.id === fullProduct?.subCategoryId
  );
  const brand = brands.find((b) => b.id === fullProduct?.brandId);

  // Print functionality
  const handlePrint = React.useCallback(() => {
    if (!fullProduct) return;

    const printWindow = window.open("", "_blank");
    if (!printWindow) return;

    const printContent = printRef.current;
    if (!printContent) return;

    const htmlContent = `
      <html>
        <head>
          <title>Product Details - ${fullProduct.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 10px; }
            .section { margin-bottom: 20px; }
            .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
            .field { margin-bottom: 8px; }
            .field-label { font-weight: bold; color: #666; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 12px; }
            .no-print { display: none; }
            @media print {
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Product Details</h1>
            <p>Generated on ${format(new Date(), "PPP 'at' p")}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [fullProduct?.name]);

  // PDF download functionality
  const handleDownloadPDF = React.useCallback(async () => {
    if (!printRef.current || !fullProduct) return;

    try {
      const canvas = await html2canvas(printRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");

      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(
        `product-details-${fullProduct.name
          .replace(/[^a-z0-9]/gi, "-")
          .toLowerCase()}.pdf`
      );
      toast({
        title: "Success",
        description: "PDF downloaded successfully"
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Error",
        description: "Failed to generate PDF",
        variant: "destructive"
      });
    }
  }, [fullProduct?.name]);

  // Helper functions for styling
  const getStatusColor = (status: ProductStatus) => {
    switch (status) {
      case ProductStatus.ACTIVE:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case ProductStatus.INACTIVE:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case ProductStatus.DRAFT:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      case ProductStatus.DISCONTINUED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getProductTypeColor = (type: ProductType) => {
    switch (type) {
      case ProductType.SIMPLE:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case ProductType.VARIABLE:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case ProductType.COMBO:
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  const getTrackingTypeColor = (type: TrackingType) => {
    switch (type) {
      case TrackingType.NONE:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case TrackingType.QUANTITY:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case TrackingType.SERIAL:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case TrackingType.BATCH:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (isLoadingProduct) {
    return <LoadingIndicator />;
  }

  if (!fullProduct) {
    return (
      <div className="p-6 text-center text-muted-foreground">
        Failed to load product details
      </div>
    );
  }

  return (
    <ScrollArea className="h-[70vh]">
      <div className="p-6 space-y-6 bg-white dark:bg-background">
        {/* Action Buttons */}
        <div className="flex items-center justify-between border-b pb-4 no-print">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Product Details
          </h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              Print
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadPDF}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </div>

        {/* Printable Content */}
        <div ref={printRef}>
          <Accordion
            type="multiple"
            defaultValue={[
              "basic-info",
              "pricing",
              "inventory",
              "attributes",
              "settings",
              "seo",
              "timestamps",
            ]}
            className="w-full"
          >
            {/* Basic Information */}
            <AccordionItem value="basic-info">
              <AccordionTrigger className="text-left">
                <div className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">Basic Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Product Name
                      </label>
                      <div className="flex items-center gap-2">
                        <p className="text-lg font-semibold">
                          {fullProduct.name}
                        </p>
                        {fullProduct.featured && (
                          <Star className="h-4 w-4 text-yellow-500" />
                        )}
                      </div>
                    </div>

                    {fullProduct.shortCode && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Short Code
                        </label>
                        <div className="flex items-center gap-2">
                          <Hash className="h-4 w-4 text-muted-foreground" />
                          <p className="font-mono">{fullProduct.shortCode}</p>
                        </div>
                      </div>
                    )}

                    {fullProduct.sku && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          SKU
                        </label>
                        <div className="flex items-center gap-2">
                          <Barcode className="h-4 w-4 text-muted-foreground" />
                          <p className="font-mono">{fullProduct.sku}</p>
                        </div>
                      </div>
                    )}

                    {fullProduct.barcode && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Barcode
                        </label>
                        <p className="font-mono">{fullProduct.barcode}</p>
                      </div>
                    )}

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Product Type
                      </label>
                      <Badge
                        className={getProductTypeColor(fullProduct.productType)}
                      >
                        {fullProduct.productType}
                      </Badge>
                    </div>

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Status
                      </label>
                      <Badge className={getStatusColor(fullProduct.status)}>
                        {fullProduct.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {category && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Category
                        </label>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{category.name}</Badge>
                          {subcategory && (
                            <Badge variant="outline">{subcategory.name}</Badge>
                          )}
                        </div>
                      </div>
                    )}

                    {brand && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Brand
                        </label>
                        <p>{brand.name}</p>
                      </div>
                    )}

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Tracking Type
                      </label>
                      <Badge
                        className={getTrackingTypeColor(
                          fullProduct.trackingType
                        )}
                      >
                        {fullProduct.trackingType}
                      </Badge>
                    </div>

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Online Availability
                      </label>
                      <div className="flex items-center gap-2">
                        {fullProduct.availableOnline ? (
                          <>
                            <Globe className="h-4 w-4 text-blue-600" />
                            <span className="text-blue-600">
                              Available Online
                            </span>
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 text-gray-600" />
                            <span className="text-gray-600">
                              Not Available Online
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {fullProduct.description && (
                  <div className="mt-6 space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Description
                    </label>
                    <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                      {fullProduct.description}
                    </p>
                  </div>
                )}

                {fullProduct.shortDescription && (
                  <div className="mt-4 space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Short Description
                    </label>
                    <p className="text-gray-700 dark:text-gray-300">
                      {fullProduct.shortDescription}
                    </p>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            {/* Pricing Information */}
            <AccordionItem value="pricing">
              <AccordionTrigger className="text-left">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">Pricing Information</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {fullProduct.basePrice && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Base Price
                        </label>
                        <p className="text-lg font-semibold text-green-600">
                          ${parseFloat(fullProduct.basePrice).toFixed(2)}
                        </p>
                      </div>
                    )}

                    {fullProduct.standardCost && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Standard Cost
                        </label>
                        <p>
                          ${parseFloat(fullProduct.standardCost).toFixed(2)}
                        </p>
                      </div>
                    )}

                    {fullProduct.lastPurchasePrice && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Last Purchase Price
                        </label>
                        <p>
                          $
                          {parseFloat(fullProduct.lastPurchasePrice).toFixed(2)}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    {fullProduct.minimumSellingPrice && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Minimum Selling Price
                        </label>
                        <p>
                          $
                          {parseFloat(fullProduct.minimumSellingPrice).toFixed(
                            2
                          )}
                        </p>
                      </div>
                    )}

                    {fullProduct.maximumRetailPrice && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Maximum Retail Price
                        </label>
                        <p>
                          $
                          {parseFloat(fullProduct.maximumRetailPrice).toFixed(
                            2
                          )}
                        </p>
                      </div>
                    )}

                    {fullProduct.wholesalePrice && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Wholesale Price
                        </label>
                        <p>
                          ${parseFloat(fullProduct.wholesalePrice).toFixed(2)}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {fullProduct.taxType && (
                  <div className="mt-4 space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Tax Type
                    </label>
                    <Badge variant="outline">{fullProduct.taxType}</Badge>
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            {/* Inventory & Tracking */}
            <AccordionItem value="inventory">
              <AccordionTrigger className="text-left">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">Inventory & Tracking</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Tracking Type
                      </label>
                      <Badge
                        className={getTrackingTypeColor(
                          fullProduct.trackingType
                        )}
                      >
                        {fullProduct.trackingType}
                      </Badge>
                    </div>

                    {fullProduct.unitOfMeasure && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Unit of Measure
                        </label>
                        <p>{fullProduct.unitOfMeasure}</p>
                      </div>
                    )}

                    {fullProduct.reorderLevel && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Reorder Level
                        </label>
                        <p>{fullProduct.reorderLevel}</p>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    {fullProduct.reorderQuantity && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Reorder Quantity
                        </label>
                        <p>{fullProduct.reorderQuantity}</p>
                      </div>
                    )}

                    {fullProduct.expiryPeriod &&
                      fullProduct.expiryPeriod > 0 && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Expiry Period
                          </label>
                          <p>
                            {fullProduct.expiryPeriod}{" "}
                            {fullProduct.expiryPeriodType?.toLowerCase()}
                          </p>
                        </div>
                      )}
                  </div>
                </div>

                {/* Product Capabilities */}
                <div className="mt-6">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3 block">
                    Product Capabilities
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          "w-3 h-3 rounded-full",
                          fullProduct.isSellable
                            ? "bg-green-500"
                            : "bg-gray-300"
                        )}
                      />
                      <span className="text-sm">Sellable</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          "w-3 h-3 rounded-full",
                          fullProduct.isPurchasable
                            ? "bg-green-500"
                            : "bg-gray-300"
                        )}
                      />
                      <span className="text-sm">Purchasable</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          "w-3 h-3 rounded-full",
                          fullProduct.isIngredient
                            ? "bg-green-500"
                            : "bg-gray-300"
                        )}
                      />
                      <span className="text-sm">Ingredient</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div
                        className={cn(
                          "w-3 h-3 rounded-full",
                          fullProduct.isManufacturable
                            ? "bg-green-500"
                            : "bg-gray-300"
                        )}
                      />
                      <span className="text-sm">Manufacturable</span>
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Product Attributes */}
            {(fullProduct.weight ||
              fullProduct.dimensions ||
              fullProduct.color ||
              fullProduct.size ||
              fullProduct.material ||
              fullProduct.model ||
              fullProduct.notes ||
              (fullProduct.tags && fullProduct.tags.length > 0)) && (
              <AccordionItem value="attributes">
                <AccordionTrigger className="text-left">
                  <div className="flex items-center gap-2">
                    <Tag className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">Product Attributes</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      {fullProduct.weight && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Weight
                          </label>
                          <p>{fullProduct.weight}</p>
                        </div>
                      )}

                      {fullProduct.dimensions && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Dimensions
                          </label>
                          <p>{fullProduct.dimensions}</p>
                        </div>
                      )}

                      {fullProduct.color && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Color
                          </label>
                          <p>{fullProduct.color}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      {fullProduct.size && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Size
                          </label>
                          <p>{fullProduct.size}</p>
                        </div>
                      )}

                      {fullProduct.material && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Material
                          </label>
                          <p>{fullProduct.material}</p>
                        </div>
                      )}

                      {fullProduct.model && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Model
                          </label>
                          <p>{fullProduct.model}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {fullProduct.tags && fullProduct.tags.length > 0 && (
                    <div className="mt-6 space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Tags
                      </label>
                      <div className="flex flex-wrap gap-2">
                        {fullProduct.tags.map((tag, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {fullProduct.notes && (
                    <div className="mt-6 space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Notes
                      </label>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {fullProduct.notes}
                      </p>
                    </div>
                  )}
                </AccordionContent>
              </AccordionItem>
            )}

            {/* SEO Information */}
            {(fullProduct.seoTitle ||
              fullProduct.seoDescription ||
              (fullProduct.seoKeywords && fullProduct.seoKeywords.length > 0) ||
              fullProduct.slug) && (
              <AccordionItem value="seo">
                <AccordionTrigger className="text-left">
                  <div className="flex items-center gap-2">
                    <Search className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">SEO Information</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="pt-4">
                  <div className="space-y-4">
                    {fullProduct.slug && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          URL Slug
                        </label>
                        <p className="font-mono text-sm">{fullProduct.slug}</p>
                      </div>
                    )}

                    {fullProduct.seoTitle && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          SEO Title
                        </label>
                        <p>{fullProduct.seoTitle}</p>
                      </div>
                    )}

                    {fullProduct.seoDescription && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          SEO Description
                        </label>
                        <p className="text-gray-700 dark:text-gray-300">
                          {fullProduct.seoDescription}
                        </p>
                      </div>
                    )}

                    {fullProduct.seoKeywords &&
                      fullProduct.seoKeywords.length > 0 && (
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            SEO Keywords
                          </label>
                          <div className="flex flex-wrap gap-2">
                            {fullProduct.seoKeywords.map((keyword, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="text-xs"
                              >
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Timestamps */}
            <AccordionItem value="timestamps">
              <AccordionTrigger className="text-left">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <span className="font-medium">Timestamps</span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Created
                      </label>
                      <p className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {format(new Date(fullProduct.createdAt), "PPP")}
                      </p>
                    </div>

                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Last Updated
                      </label>
                      <p className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {format(new Date(fullProduct.updatedAt), "PPP")}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Global Position
                      </label>
                      <p>{fullProduct.globalPosition}</p>
                    </div>

                    {fullProduct.categoryPosition && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Category Position
                        </label>
                        <p>{fullProduct.categoryPosition}</p>
                      </div>
                    )}

                    {fullProduct.subCategoryPosition && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Subcategory Position
                        </label>
                        <p>{fullProduct.subCategoryPosition}</p>
                      </div>
                    )}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </ScrollArea>
  );
}
