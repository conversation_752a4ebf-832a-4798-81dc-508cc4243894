"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, <PERSON><PERSON><PERSON>cle2, <PERSON>Circle, FileCheck } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type JournalEntryTableData, JournalEntryStatus } from "@/types/journal-entry";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteJournalEntries,
  useBulkUpdateJournalEntryStatus,
} from "@/lib/journal-entries/hooks";
import { ApiStatus } from "@/types/common";

interface JournalEntriesTableFloatingBarProps {
  table: Table<JournalEntryTableData>;
  onRefresh?: (journalEntryIds?: string[]) => void;
  isDemo?: boolean;
}

export function JournalEntriesTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: JournalEntriesTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "post" | "void" | "draft"
  >();

  const bulkDeleteMutation = useBulkDeleteJournalEntries(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateJournalEntryStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedJournalEntries = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedJournalEntries.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedJournalEntries.length, selectionCount]);

  // Check if all selected entries are balanced (required for posting)
  const allSelectedAreBalanced = selectedJournalEntries.every(entry => entry.isBalanced);
  
  // Check if any selected entries are already posted (can't be edited/deleted)
  const hasPostedEntries = selectedJournalEntries.some(entry => entry.status === JournalEntryStatus.POSTED);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: JournalEntryStatus) => {
      if (selectionCount === 0) return;

      // Prevent posting unbalanced entries
      if (status === JournalEntryStatus.POSTED && !allSelectedAreBalanced) {
        toast({
          title: "Error",
          description: "Cannot post unbalanced journal entries",
          variant: "destructive"
        });
        return;
      }

      // Prevent updating posted entries
      if (hasPostedEntries && status !== JournalEntryStatus.VOIDED) {
        toast({
          title: "Error",
          description: "Cannot modify posted journal entries (except to void them)",
          variant: "destructive"
        });
        return;
      }

      setAction(
        status === JournalEntryStatus.POSTED ? "post" : 
        status === JournalEntryStatus.VOIDED ? "void" : "draft"
      );

      try {
        const journalEntryIds = selectedJournalEntries.map((entry) => entry.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          journalEntryIds,
          status,
        });

        const failedUpdates = result.filter(
          (res: any) => res.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
          const statusText = 
            status === JournalEntryStatus.POSTED ? "posted" :
            status === JournalEntryStatus.VOIDED ? "voided" : "set to draft";
          
          toast({
            title: "Success",
            description: `Successfully ${statusText} ${selectionCount} journal entr${selectionCount > 1 ? "ies" : "y"}`
          });
          table.toggleAllRowsSelected(false);
          onRefresh?.(journalEntryIds);
        } else {
          toast({
            title: "Error",
            description: `Failed to update ${failedUpdates.length} journal entr${
              failedUpdates.length > 1 ? "ies" : "y"
            }`,
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error updating journal entries:", error);
        toast({
          title: "Error",
          description: "Failed to update journal entries",
          variant: "destructive"
        });
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedJournalEntries,
      selectionCount,
      allSelectedAreBalanced,
      hasPostedEntries,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No journal entries selected" };

    // Prevent deleting posted entries
    if (hasPostedEntries) {
      return { error: "Cannot delete posted journal entries" };
    }

    try {
      const journalEntryIds = selectedJournalEntries.map((entry) => entry.id);
      const result = await bulkDeleteMutation.mutateAsync(journalEntryIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete journal entries",
        };
      }
    } catch (error: any) {
      console.error("Error deleting journal entries:", error);
      return { error: error.message || "Failed to delete journal entries" };
    }
  }, [selectedJournalEntries, selectionCount, hasPostedEntries, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(JournalEntryStatus.POSTED)}
            disabled={isPending || selectionCount === 0 || !allSelectedAreBalanced || hasPostedEntries}
          >
            <FileCheck className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Post selected entries</p>
          {!allSelectedAreBalanced && <p className="text-xs text-red-500">Some entries are unbalanced</p>}
          {hasPostedEntries && <p className="text-xs text-red-500">Some entries are already posted</p>}
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(JournalEntryStatus.VOIDED)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Void selected entries</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(JournalEntryStatus.DRAFT)}
            disabled={isPending || selectionCount === 0 || hasPostedEntries}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Set selected to draft</p>
          {hasPostedEntries && <p className="text-xs text-red-500">Cannot modify posted entries</p>}
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0 || hasPostedEntries}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
          {hasPostedEntries && <p className="text-xs text-red-500">Cannot delete posted entries</p>}
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<JournalEntryTableData>
        table={table}
        title="Journal Entries"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} journal entr${selectionCount > 1 ? "ies" : "y"}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass journal entry IDs since they're deleted
        }}
      />
    </>
  );
}
