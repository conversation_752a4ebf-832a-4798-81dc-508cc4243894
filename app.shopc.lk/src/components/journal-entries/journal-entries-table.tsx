"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getJournalEntriesTableData } from "@/lib/journal-entries/queries";
import { DeleteJournalEntriesDialog } from "./delete-journal-entries-dialog";
import { JournalEntriesTableToolbarActions } from "./journal-entries-table-toolbar-actions";
import { JournalEntriesTableFloatingBar } from "./journal-entries-table-floating-bar";
import { JournalEntrySheet } from "./journal-entry-sheet";
import { JournalEntryDetails } from "./journal-entry-details";
import { JournalEntryDetailsContent } from "./journal-entry-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./journal-entries-table-columns";
import {
  JournalEntryTableData,
  JournalEntryStatus,
  JournalReferenceType,
} from "@/types/journal-entry";
import {
  useJournalEntriesData,
  useUpdateJournalEntryStatus,
} from "@/lib/journal-entries/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { journalEntryKeys } from "@/lib/journal-entries/hooks";
import { ApiStatus } from "@/types/common";
import { toast } from "sonner";

interface JournalEntriesTableProps {
  isDemo?: boolean;
}

export function JournalEntriesTable({
  isDemo = false,
}: JournalEntriesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<JournalEntryTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Status update mutation
  const updateStatusMutation = useUpdateJournalEntryStatus(isDemo);

  const filterFields: DataTableFilterField<JournalEntryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "journalNumber",
          label: "Journal Number",
          placeholder: "Filter by journal number...",
        },
        {
          id: "description",
          label: "Description",
          placeholder: "Filter by description...",
        },
        {
          id: "status",
          label: "Status",
          placeholder: "Filter by status...",
          type: "select",
          options: [
            { label: "Draft", value: JournalEntryStatus.DRAFT },
            { label: "Posted", value: JournalEntryStatus.POSTED },
            { label: "Voided", value: JournalEntryStatus.VOIDED },
          ],
        },
        {
          id: "referenceType",
          label: "Reference Type",
          placeholder: "Filter by reference type...",
          type: "select",
          options: Object.values(JournalReferenceType).map((type) => ({
            label: type
              .replace(/_/g, " ")
              .replace(/\b\w/g, (l) => l.toUpperCase()),
            value: type,
          })),
        },
      ],
      []
    );

  const advancedFilterFields: DataTableAdvancedFilterField<JournalEntryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "journalNumber",
          label: "Journal Number",
          type: "text",
        },
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        {
          id: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Draft", value: JournalEntryStatus.DRAFT },
            { label: "Posted", value: JournalEntryStatus.POSTED },
            { label: "Voided", value: JournalEntryStatus.VOIDED },
          ],
        },
        {
          id: "referenceType",
          label: "Reference Type",
          type: "select",
          options: Object.values(JournalReferenceType).map((type) => ({
            label: type
              .replace(/_/g, " ")
              .replace(/\b\w/g, (l) => l.toUpperCase()),
            value: type,
          })),
        },
        {
          id: "isBalanced",
          label: "Balanced",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<JournalEntryTableData>[] =
    React.useMemo(
      () => [
        {
          id: "journalNumber",
          label: "Journal Number",
          type: "text",
        },
        {
          id: "journalDate",
          label: "Journal Date",
          type: "text",
        },
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        {
          id: "totalDebitAmount",
          label: "Total Debits",
          type: "number",
        },
        {
          id: "totalCreditAmount",
          label: "Total Credits",
          type: "number",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<JournalEntryTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      journalNumber: searchParams.get("journalNumber") || "",
      description: searchParams.get("description") || "",
      status: searchParams.get("status") || "",
      referenceType: searchParams.get("referenceType") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: journalEntriesData,
    isLoading,
    isFetching,
    isRefetching,
  } = useJournalEntriesData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.journalNumber ||
      searchParamsValues.description ||
      searchParamsValues.status ||
      searchParamsValues.referenceType ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  const handleRefresh = React.useCallback(
    async (journalEntryIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({ queryKey: journalEntryKeys.list() }),
        queryClient.invalidateQueries({ queryKey: journalEntryKeys.simple() }),
        queryClient.invalidateQueries({
          queryKey: journalEntryKeys.withLines(),
        }),
      ];

      // If specific journal entry IDs are provided, invalidate their detail cache as well
      if (journalEntryIds && journalEntryIds.length > 0) {
        journalEntryIds.forEach((journalEntryId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: journalEntryKeys.detail(journalEntryId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  // Handle status updates
  const handleStatusUpdate = React.useCallback(
    async (journalEntryId: string, newStatus: JournalEntryStatus) => {
      try {
        const response = await updateStatusMutation.mutateAsync({
          id: journalEntryId,
          status: newStatus,
        });

        if (response.status === ApiStatus.SUCCESS) {
          toast.success(`Journal entry ${newStatus.toLowerCase()} successfully`);
          handleRefresh([journalEntryId]);
        } else {
          toast.error(response.message || `Failed to ${newStatus.toLowerCase()} journal entry`);
        }
      } catch (error) {
        console.error("Failed to update journal entry status:", error);
        toast.error(`Failed to ${newStatus.toLowerCase()} journal entry`);
      }
    },
    [updateStatusMutation, handleRefresh]
  );

  // Custom dialog content renderer for row clicks
  const renderJournalEntryDetails = React.useCallback(
    (journalEntry: JournalEntryTableData) => {
      return (
        <JournalEntryDetailsContent
          journalEntry={journalEntry}
          isDemo={isDemo}
        />
      );
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        JournalEntryTableData,
        Awaited<ReturnType<typeof getJournalEntriesTableData>>
      >
        data={journalEntriesData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={false} // Journal entries don't need sorting
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => false} // Journal entries don't need drag and drop
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          onStatusUpdate: handleStatusUpdate,
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <JournalEntriesTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <JournalEntriesTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId="createdAt"
        defaultSortingDesc={true}
        onRefresh={handleRefresh}
        renderDialogContent={renderJournalEntryDetails}
      />
      <JournalEntryDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        journalEntry={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <JournalEntrySheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        journalEntry={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const journalEntryId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({ queryKey: journalEntryKeys.list() });
          queryClient.invalidateQueries({
            queryKey: journalEntryKeys.simple(),
          });
          queryClient.invalidateQueries({
            queryKey: journalEntryKeys.withLines(),
          });
          if (journalEntryId) {
            queryClient.invalidateQueries({
              queryKey: journalEntryKeys.detail(journalEntryId),
            });
          }
        }}
        isUpdate={true}
      />
      <DeleteJournalEntriesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        journalEntries={rowAction?.row.original ? [rowAction.row.original] : []}
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: journalEntryKeys.list() });
          queryClient.invalidateQueries({
            queryKey: journalEntryKeys.simple(),
          });
          queryClient.invalidateQueries({
            queryKey: journalEntryKeys.withLines(),
          });
        }}
        isDemo={isDemo}
      />
    </>
  );
}
