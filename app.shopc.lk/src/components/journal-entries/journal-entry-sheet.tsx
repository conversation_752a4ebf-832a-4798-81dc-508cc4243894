"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Plus,
  Trash2,
  Calculator,
  Check,
  X,
  Loader2,
  Calendar,
  FileText,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { journalEntryFormSchema } from "@/lib/journal-entries/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  JournalEntryTableData,
  JournalEntryStatus,
  JournalReferenceType,
  EntityType,
  UpdateJournalEntryDto,
  JournalEntryFormValues,
  JournalEntryLineFormValues,
} from "@/types/journal-entry";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  useJournalEntryData,
  useJournalEntryNumberAvailability,
  useCreateJournalEntry,
  useUpdateJournalEntry,
} from "@/lib/journal-entries/hooks";
import { useAccountsSlim } from "@/lib/accounts/hooks";
import { useTaxesSlim } from "@/lib/taxes/hooks";
import { useEffect } from "react";

interface JournalEntrySheetProps {
  journalEntry: JournalEntryTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (journalEntry?: JournalEntryTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof journalEntryFormSchema>;

export function JournalEntrySheet({
  journalEntry,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: JournalEntrySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Values for availability checking
  const [checkJournalNumber, setCheckJournalNumber] =
    React.useState<string>("");

  // Fetch complete journal entry data if updating
  const { data: fullJournalEntryResponse, isLoading: isLoadingJournalEntry } =
    useJournalEntryData(journalEntry?.id || "", isDemo);
  const fullJournalEntry = fullJournalEntryResponse?.data;

  // Fetch accounts for line selection
  const { data: accountsResponse } = useAccountsSlim(isDemo);
  const accounts = React.useMemo(
    () => accountsResponse?.data || [],
    [accountsResponse?.data]
  );

  // Fetch taxes for line selection
  const { data: taxesResponse } = useTaxesSlim(isDemo);
  const taxes = React.useMemo(
    () => taxesResponse?.data || [],
    [taxesResponse?.data]
  );

  // Mutation hooks for create and update operations
  const createJournalEntryMutation = useCreateJournalEntry(isDemo);
  const updateJournalEntryMutation = useUpdateJournalEntry(isDemo);

  // Availability checks (only check if not updating the same journal entry)
  const shouldCheckJournalNumberAvailability =
    checkJournalNumber.length > 0 &&
    (!isUpdate ||
      (journalEntry && checkJournalNumber !== journalEntry.journalNumber));

  const {
    data: journalNumberAvailabilityResponse,
    isLoading: isCheckingJournalNumber,
  } = useJournalEntryNumberAvailability(
    checkJournalNumber,
    isDemo,
    journalEntry?.id
  );

  const isJournalNumberAvailable = shouldCheckJournalNumberAvailability
    ? journalNumberAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(journalEntryFormSchema),
    defaultValues: {
      journalNumber: "",
      journalDate: new Date().toISOString().split("T")[0],
      description: "",
      memo: "",
      referenceType: JournalReferenceType.MANUAL,
      referenceId: undefined,
      status: JournalEntryStatus.DRAFT,
      journalEntryLines: [
        {
          lineNumber: 1,
          sortOrder: 1,
          accountId: "",
          debitAmount: "0.00",
          creditAmount: "0.00",
          description: "",
          nameReference: "",
          taxId: "",
          entityType: undefined,
          entityId: undefined,
        },
        {
          lineNumber: 2,
          sortOrder: 2,
          accountId: "",
          debitAmount: "0.00",
          creditAmount: "0.00",
          description: "",
          nameReference: "",
          taxId: "",
          entityType: undefined,
          entityId: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "journalEntryLines",
  });

  // Calculate totals
  const watchedLines = form.watch("journalEntryLines");
  const totalDebits = React.useMemo(() => {
    return watchedLines.reduce(
      (sum, line) => sum + parseFloat(line.debitAmount || "0"),
      0
    );
  }, [watchedLines]);

  const totalCredits = React.useMemo(() => {
    return watchedLines.reduce(
      (sum, line) => sum + parseFloat(line.creditAmount || "0"),
      0
    );
  }, [watchedLines]);

  const isBalanced = Math.abs(totalDebits - totalCredits) < 0.01;

  React.useEffect(() => {
    if (journalEntry && fullJournalEntry) {
      form.reset({
        journalNumber: fullJournalEntry.journalNumber,
        journalDate: fullJournalEntry.journalDate,
        description: fullJournalEntry.description,
        memo: fullJournalEntry.memo || "",
        referenceType: fullJournalEntry.referenceType,
        referenceId: fullJournalEntry.referenceId,
        status: fullJournalEntry.status,
        journalEntryLines: fullJournalEntry.journalEntryLines.map((line) => ({
          lineNumber: line.lineNumber,
          sortOrder: line.sortOrder,
          accountId: line.accountId,
          debitAmount: line.debitAmount,
          creditAmount: line.creditAmount,
          description: line.description || "",
          nameReference: line.nameReference || "",
          taxId: line.taxId || "",
          entityType: line.entityType,
          entityId: line.entityId,
        })),
      });
    } else if (!journalEntry) {
      // Reset form for new journal entry
      form.reset({
        journalNumber: "",
        journalDate: new Date().toISOString().split("T")[0],
        description: "",
        memo: "",
        referenceType: JournalReferenceType.MANUAL,
        referenceId: undefined,
        status: JournalEntryStatus.DRAFT,
        journalEntryLines: [
          {
            lineNumber: 1,
            sortOrder: 1,
            accountId: "",
            debitAmount: "0.00",
            creditAmount: "0.00",
            description: "",
            nameReference: "",
            taxId: "",
            entityType: undefined,
            entityId: undefined,
          },
          {
            lineNumber: 2,
            sortOrder: 2,
            accountId: "",
            debitAmount: "0.00",
            creditAmount: "0.00",
            description: "",
            nameReference: "",
            taxId: "",
            entityType: undefined,
            entityId: undefined,
          },
        ],
      });
    }
  }, [journalEntry, fullJournalEntry]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.journalNumber ||
    errors.journalDate ||
    errors.description ||
    errors.referenceType ||
    errors.status ||
    (shouldCheckJournalNumberAvailability && isJournalNumberAvailable === false)
  );

  const hasJournalLinesErrors = !!(errors.journalEntryLines || !isBalanced);

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (
      shouldCheckJournalNumberAvailability &&
      isJournalNumberAvailable === false
    ) {
      toast.error("Journal number is already taken. Please choose a different number.");
      return;
    }

    // Check if journal is balanced
    if (!isBalanced) {
      toast.error("Journal entry must be balanced. Total debits must equal total credits.");
      return;
    }

    setIsSubmitting(true);

    try {
      if (journalEntry) {
        // Update existing journal entry
        const updateData: UpdateJournalEntryDto = {
          journalNumber: data.journalNumber,
          journalDate: data.journalDate,
          description: data.description,
          memo: data.memo || undefined,
          referenceType: data.referenceType,
          referenceId: data.referenceId,
          status: data.status,
          journalEntryLines: data.journalEntryLines.map((line) => ({
            lineNumber: line.lineNumber,
            sortOrder: line.sortOrder,
            accountId: line.accountId,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            description: line.description || undefined,
            nameReference: line.nameReference || undefined,
            taxId: line.taxId || undefined,
            entityType: line.entityType,
            entityId: line.entityId,
          })),
        };

        const response = await updateJournalEntryMutation.mutateAsync({
          id: journalEntry.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Journal entry updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as JournalEntryTableData);
          return;
        }
        toast.error(response.message || "Failed to update journal entry");
        return;
      } else {
        // Create new journal entry
        const createData = {
          journalNumber: data.journalNumber,
          journalDate: data.journalDate,
          description: data.description,
          memo: data.memo || undefined,
          referenceType: data.referenceType,
          referenceId: data.referenceId,
          status: data.status,
          journalEntryLines: data.journalEntryLines.map((line) => ({
            lineNumber: line.lineNumber,
            sortOrder: line.sortOrder,
            accountId: line.accountId,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
            description: line.description || undefined,
            nameReference: line.nameReference || undefined,
            taxId: line.taxId || undefined,
            entityType: line.entityType,
            entityId: line.entityId,
          })),
        };

        const response = await createJournalEntryMutation.mutateAsync(
          createData
        );

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Journal entry created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as JournalEntryTableData);
          return;
        }
        toast.error(response.message || "Failed to create journal entry");
        return;
      }
    } catch (error) {
      console.error("Failed to save journal entry:", error);
      toast.error("Failed to save journal entry");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle availability checks
  const handleCheckJournalNumberAvailability = (value: string) => {
    setCheckJournalNumber(value);
  };

  // Add new journal entry line
  const addJournalEntryLine = () => {
    const newLineNumber = fields.length + 1;
    append({
      lineNumber: newLineNumber,
      sortOrder: newLineNumber,
      accountId: "",
      debitAmount: "0.00",
      creditAmount: "0.00",
      description: "",
      nameReference: "",
      taxId: "",
      entityType: undefined,
      entityId: undefined,
    });
  };

  // Remove journal entry line
  const removeJournalEntryLine = (index: number) => {
    if (fields.length > 2) {
      remove(index);
      // Update line numbers and sort orders
      const currentLines = form.getValues("journalEntryLines");
      currentLines.forEach((line, idx) => {
        if (idx >= index) {
          form.setValue(`journalEntryLines.${idx}.lineNumber`, idx + 1);
          form.setValue(`journalEntryLines.${idx}.sortOrder`, idx + 1);
        }
      });
    }
  };

  // Handle debit/credit amount changes to ensure only one is filled
  const handleAmountChange = (
    index: number,
    type: "debit" | "credit",
    value: string
  ) => {
    if (type === "debit") {
      form.setValue(`journalEntryLines.${index}.debitAmount`, value);
      if (parseFloat(value) > 0) {
        form.setValue(`journalEntryLines.${index}.creditAmount`, "0.00");
      }
    } else {
      form.setValue(`journalEntryLines.${index}.creditAmount`, value);
      if (parseFloat(value) > 0) {
        form.setValue(`journalEntryLines.${index}.debitAmount`, "0.00");
      }
    }
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="journalNumber"
                label="Journal Number"
                placeholder="Enter journal number (e.g., JE-001)"
                value={form.watch("journalNumber") || ""}
                onChange={(value) =>
                  form.setValue("journalNumber", value, {
                    shouldValidate: true,
                  })
                }
                onCheckAvailability={handleCheckJournalNumberAvailability}
                isCheckingAvailability={isCheckingJournalNumber}
                isAvailable={isJournalNumberAvailable}
                shouldCheck={shouldCheckJournalNumberAvailability}
                error={errors.journalNumber?.message}
                disabled={isSubmitting}
                fieldName="Journal number"
              />

              <div>
                <Label>Journal Date</Label>
                <Input
                  {...form.register("journalDate")}
                  name="journalDate"
                  type="date"
                  className={cn(errors.journalDate && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.journalDate && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.journalDate.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter journal entry description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Memo</Label>
                <Textarea
                  {...form.register("memo")}
                  name="memo"
                  placeholder="Enter optional memo"
                  className={cn(errors.memo && "border-destructive")}
                  disabled={isSubmitting}
                  rows={2}
                />
                {errors.memo && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.memo.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional additional notes for this journal entry
                </p>
              </div>

              <div>
                <Label>Reference Type</Label>
                <Select
                  value={form.watch("referenceType")}
                  onValueChange={(value) =>
                    form.setValue(
                      "referenceType",
                      value as JournalReferenceType,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.referenceType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select reference type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(JournalReferenceType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {type
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.referenceType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.referenceType.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Reference ID</Label>
                <Input
                  {...form.register("referenceId", { valueAsNumber: true })}
                  name="referenceId"
                  type="number"
                  placeholder="Enter reference ID (optional)"
                  className={cn(errors.referenceId && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.referenceId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.referenceId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional reference to related document
                </p>
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue("status", value as JournalEntryStatus, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(JournalEntryStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "journal-lines",
      title: "Journal Entry Lines",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasJournalLinesErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Journal Entry Lines</span>
              <div className="ml-auto flex items-center gap-2">
                <Calculator className="h-4 w-4" />
                <span
                  className={cn(
                    "text-sm font-medium",
                    isBalanced ? "text-green-600" : "text-red-600"
                  )}
                >
                  {isBalanced ? "Balanced" : "Unbalanced"}
                </span>
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              {/* Summary Card */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium">Summary</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Total Debits</p>
                      <p className="font-medium">${totalDebits.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Total Credits</p>
                      <p className="font-medium">${totalCredits.toFixed(2)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Difference</p>
                      <p
                        className={cn(
                          "font-medium",
                          isBalanced ? "text-green-600" : "text-red-600"
                        )}
                      >
                        ${Math.abs(totalDebits - totalCredits).toFixed(2)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Journal Entry Lines */}
              <div className="space-y-3">
                {fields.map((field, index) => (
                  <Card key={field.id} className="relative">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm font-medium">
                          Line {index + 1}
                        </CardTitle>
                        {fields.length > 2 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeJournalEntryLine(index)}
                            disabled={isSubmitting}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Account</Label>
                          <Select
                            value={
                              form.watch(
                                `journalEntryLines.${index}.accountId`
                              ) || ""
                            }
                            onValueChange={(value) =>
                              form.setValue(
                                `journalEntryLines.${index}.accountId`,
                                value,
                                {
                                  shouldValidate: true,
                                }
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger
                              className={cn(
                                errors.journalEntryLines?.[index]?.accountId &&
                                  "border-destructive"
                              )}
                            >
                              <SelectValue placeholder="Select account" />
                            </SelectTrigger>
                            <SelectContent>
                              {accounts.map((account) => (
                                <SelectItem key={account.id} value={account.id}>
                                  {account.accountName}
                                  {account.accountNumber &&
                                    ` (${account.accountNumber})`}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.journalEntryLines?.[index]?.accountId && (
                            <p className="text-sm text-destructive mt-1">
                              {
                                errors.journalEntryLines[index]?.accountId
                                  ?.message
                              }
                            </p>
                          )}
                        </div>

                        <div>
                          <Label>Description</Label>
                          <Input
                            {...form.register(
                              `journalEntryLines.${index}.description`
                            )}
                            placeholder="Line description (optional)"
                            className={cn(
                              errors.journalEntryLines?.[index]?.description &&
                                "border-destructive"
                            )}
                            disabled={isSubmitting}
                          />
                          {errors.journalEntryLines?.[index]?.description && (
                            <p className="text-sm text-destructive mt-1">
                              {
                                errors.journalEntryLines[index]?.description
                                  ?.message
                              }
                            </p>
                          )}
                        </div>

                        <div>
                          <Label>Debit Amount</Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            value={
                              form.watch(
                                `journalEntryLines.${index}.debitAmount`
                              ) || "0.00"
                            }
                            onChange={(e) =>
                              handleAmountChange(index, "debit", e.target.value)
                            }
                            className={cn(
                              errors.journalEntryLines?.[index]?.debitAmount &&
                                "border-destructive"
                            )}
                            disabled={isSubmitting}
                          />
                          {errors.journalEntryLines?.[index]?.debitAmount && (
                            <p className="text-sm text-destructive mt-1">
                              {
                                errors.journalEntryLines[index]?.debitAmount
                                  ?.message
                              }
                            </p>
                          )}
                        </div>

                        <div>
                          <Label>Credit Amount</Label>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            value={
                              form.watch(
                                `journalEntryLines.${index}.creditAmount`
                              ) || "0.00"
                            }
                            onChange={(e) =>
                              handleAmountChange(
                                index,
                                "credit",
                                e.target.value
                              )
                            }
                            className={cn(
                              errors.journalEntryLines?.[index]?.creditAmount &&
                                "border-destructive"
                            )}
                            disabled={isSubmitting}
                          />
                          {errors.journalEntryLines?.[index]?.creditAmount && (
                            <p className="text-sm text-destructive mt-1">
                              {
                                errors.journalEntryLines[index]?.creditAmount
                                  ?.message
                              }
                            </p>
                          )}
                        </div>

                        <div>
                          <Label>Name Reference</Label>
                          <Input
                            {...form.register(
                              `journalEntryLines.${index}.nameReference`
                            )}
                            placeholder="Name reference (optional)"
                            className={cn(
                              errors.journalEntryLines?.[index]
                                ?.nameReference && "border-destructive"
                            )}
                            disabled={isSubmitting}
                          />
                          {errors.journalEntryLines?.[index]?.nameReference && (
                            <p className="text-sm text-destructive mt-1">
                              {
                                errors.journalEntryLines[index]?.nameReference
                                  ?.message
                              }
                            </p>
                          )}
                        </div>

                        <div>
                          <Label>Tax</Label>
                          <Select
                            value={
                              form.watch(`journalEntryLines.${index}.taxId`) ||
                              ""
                            }
                            onValueChange={(value) =>
                              form.setValue(
                                `journalEntryLines.${index}.taxId`,
                                value || "",
                                {
                                  shouldValidate: true,
                                }
                              )
                            }
                            disabled={isSubmitting}
                          >
                            <SelectTrigger
                              className={cn(
                                errors.journalEntryLines?.[index]?.taxId &&
                                  "border-destructive"
                              )}
                            >
                              <SelectValue placeholder="Select tax (optional)" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="">No Tax</SelectItem>
                              {taxes.map((tax) => (
                                <SelectItem key={tax.id} value={tax.id}>
                                  {tax.taxName} ({tax.taxRate}%)
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.journalEntryLines?.[index]?.taxId && (
                            <p className="text-sm text-destructive mt-1">
                              {errors.journalEntryLines[index]?.taxId?.message}
                            </p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {/* Add Line Button */}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addJournalEntryLine}
                  disabled={isSubmitting}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Journal Entry Line
                </Button>
              </div>

              {errors.journalEntryLines && (
                <p className="text-sm text-destructive">
                  {errors.journalEntryLines.message}
                </p>
              )}

              {!isBalanced && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-red-800">
                    <X className="h-4 w-4" />
                    <span className="font-medium">
                      Journal Entry Not Balanced
                    </span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    Total debits must equal total credits. Current difference: $
                    {Math.abs(totalDebits - totalCredits).toFixed(2)}
                  </p>
                </div>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<JournalEntryTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={journalEntry}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Journal Entry"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingJournalEntry}
      // Disable location and SEO features for journal entries
      isLocationEnabled={false}
      isSEOEnabled={false}
    />
  );
}
