"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Image as ImageIcon,
  FolderTree,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { serviceCategoryFormSchema } from "@/lib/service-categories/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ServiceCategoryTableData,
  ServiceCategoryStatus,
  UpdateServiceCategoryDto,
  ServiceCategorySlimDto,
} from "@/types/service-category";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useServiceCategoriesSlim,
  useServiceCategoryNameAvailability,
  useServiceCategorySlugAvailability,
  useServiceCategoryShortCodeAvailability,
  useCreateServiceCategory,
  useUpdateServiceCategory,
  useServiceCategory,
} from "@/lib/service-categories/hooks";
import { useEffect } from "react";

interface ServiceCategorySheetProps {
  serviceCategory: ServiceCategoryTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (serviceCategory?: ServiceCategoryTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof serviceCategoryFormSchema>;

export function ServiceCategorySheet({
  serviceCategory,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ServiceCategorySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [ogImageFiles, setOgImageFiles] = React.useState<UploadedFile[]>([]);
  const [keywords, setKeywords] = React.useState<string>("");

  // Values for availability checking
  const [checkName, setCheckName] = React.useState<string>("");
  const [checkSlug, setCheckSlug] = React.useState<string>("");
  const [checkShortCode, setCheckShortCode] = React.useState<string>("");

  // Fetch complete service category data if updating
  const {
    data: fullServiceCategoryResponse,
    isLoading: isLoadingServiceCategory,
  } = useServiceCategory(serviceCategory?.id || "", isDemo);
  const fullServiceCategory = fullServiceCategoryResponse?.data;

  // Fetch service categories for parent selection
  const { data: serviceCategoriesResponse } = useServiceCategoriesSlim(isDemo);
  const serviceCategories = React.useMemo(
    () => serviceCategoriesResponse?.data || [],
    [serviceCategoriesResponse?.data]
  );

  useEffect(() => {
    if (fullServiceCategoryResponse) {
      console.log("fullServiceCategoryResponse", fullServiceCategoryResponse);
    }
  }, [fullServiceCategoryResponse]);

  // Mutation hooks for create and update operations
  const createServiceCategoryMutation = useCreateServiceCategory(isDemo);
  const updateServiceCategoryMutation = useUpdateServiceCategory(isDemo);

  // Availability checks (only check if not updating the same service category)
  const shouldCheckNameAvailability =
    checkName.length > 0 &&
    (!isUpdate ||
      (serviceCategory &&
        checkName.toLowerCase() !== serviceCategory.name.toLowerCase()));
  const shouldCheckSlugAvailability =
    checkSlug.length > 0 &&
    (!isUpdate ||
      (serviceCategory &&
        checkSlug.toLowerCase() !== serviceCategory.slug?.toLowerCase()));
  const shouldCheckShortCodeAvailability =
    checkShortCode.length > 0 &&
    (!isUpdate ||
      (serviceCategory &&
        checkShortCode.toUpperCase() !==
          serviceCategory.shortCode?.toUpperCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useServiceCategoryNameAvailability(checkName, isDemo);
  const { data: slugAvailabilityResponse, isLoading: isCheckingSlug } =
    useServiceCategorySlugAvailability(checkSlug, isDemo);
  const {
    data: shortCodeAvailabilityResponse,
    isLoading: isCheckingShortCode,
  } = useServiceCategoryShortCodeAvailability(checkShortCode, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isSlugAvailable = shouldCheckSlugAvailability
    ? slugAvailabilityResponse?.data?.available ?? true
    : true;
  const isShortCodeAvailable = shouldCheckShortCodeAvailability
    ? shortCodeAvailabilityResponse?.data?.available ?? true
    : true;

  // Helper function to flatten service categories into a single list for parent selection
  const flattenServiceCategories = (
    serviceCategories: ServiceCategorySlimDto[]
  ): ServiceCategorySlimDto[] => {
    const flattened: ServiceCategorySlimDto[] = [];

    const addServiceCategory = (cat: ServiceCategorySlimDto) => {
      flattened.push(cat);
      if (cat.subServiceCategories && cat.subServiceCategories.length > 0) {
        cat.subServiceCategories.forEach(addServiceCategory);
      }
    };

    serviceCategories.forEach(addServiceCategory);
    return flattened;
  };

  const flatServiceCategories = React.useMemo(
    () => flattenServiceCategories(serviceCategories),
    [serviceCategories]
  );

  const form = useForm<FormData>({
    resolver: zodResolver(serviceCategoryFormSchema),
    defaultValues: {
      name: "",
      shortCode: "",
      parentId: null,
      description: "",
      slug: "",
      availableOnline: true,
      color: "",
      status: ServiceCategoryStatus.ACTIVE,
      // Location fields
      isAllocatedToAllLocations: false,
      locationIds: [],
      // SEO fields
      seoTitle: "",
      seoDescription: "",
      seoKeywords: [],
    },
  });

  // Get the selected image file for API calls (only if it's a real file, not the mock one for existing images)
  const selectedImage =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  // Get the selected OG image file for API calls
  const selectedOgImage =
    ogImageFiles.length > 0 && !ogImageFiles[0].id.startsWith("existing-og-")
      ? ogImageFiles[0].file
      : null;

  // Helper function to parse keywords
  const parseKeywords = (keywordsString: string): string[] => {
    return keywordsString
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k.length > 0);
  };

  React.useEffect(() => {
    if (serviceCategory && fullServiceCategory) {
      // Use full service category data for populating the form
      // Find the parent ID if parent exists
      const parentId = fullServiceCategory.parentId || null;

      form.reset({
        name: fullServiceCategory.name,
        shortCode: fullServiceCategory.shortCode || "",
        parentId: parentId,
        description: fullServiceCategory.description || "",
        slug: fullServiceCategory.slug || "",
        availableOnline: fullServiceCategory.availableOnline,
        color: fullServiceCategory.color || "",
        status: fullServiceCategory.status as ServiceCategoryStatus,
        // Location fields
        isAllocatedToAllLocations:
          fullServiceCategory.isAllocatedToAllLocations || false,
        locationIds: fullServiceCategory.locations?.map((loc) => loc.id) || [],
        // SEO fields
        seoTitle: fullServiceCategory.seoTitle || "",
        seoDescription: fullServiceCategory.seoDescription || "",
        seoKeywords: fullServiceCategory.seoKeywords || [],
      });

      // Set keywords state for UI
      setKeywords(fullServiceCategory.seoKeywords?.join(", ") || "");

      // Set existing image as uploaded file for preview
      if (fullServiceCategory.image) {
        // Create a mock UploadedFile for existing image
        setUploadedFiles([
          {
            id: `existing-${fullServiceCategory.id}`,
            file: new File([], fullServiceCategory.name + "-image", {
              type: "image/jpeg",
            }),
            preview: fullServiceCategory.image,
          },
        ]);
      } else {
        setUploadedFiles([]);
      }

      // Set existing OG image as uploaded file for preview
      if (fullServiceCategory.ogImage) {
        setOgImageFiles([
          {
            id: `existing-og-${fullServiceCategory.id}`,
            file: new File([], fullServiceCategory.name + "-og-image", {
              type: "image/jpeg",
            }),
            preview: fullServiceCategory.ogImage,
          },
        ]);
      } else {
        setOgImageFiles([]);
      }
    } else if (!serviceCategory) {
      // Reset form for new service category
      form.reset({
        name: "",
        shortCode: "",
        parentId: null,
        description: "",
        slug: "",
        availableOnline: true,
        color: "",
        status: ServiceCategoryStatus.ACTIVE,
        // Location fields
        isAllocatedToAllLocations: false,
        locationIds: [],
        // SEO fields
        seoTitle: "",
        seoDescription: "",
        seoKeywords: [],
      });
      setKeywords("");
      setUploadedFiles([]);
      setOgImageFiles([]);

      // Trigger validation for new service category (availableOnline defaults to true)
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 100);
    }
  }, [serviceCategory, fullServiceCategory]);

  // Reset form when sheet closes and trigger validation when opens
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setOgImageFiles([]);
      setKeywords("");
    } else {
      // When sheet opens, trigger validation to show required field errors
      setTimeout(() => {
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }, 200);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.name ||
    errors.shortCode ||
    errors.color ||
    errors.availableOnline ||
    errors.status ||
    (shouldCheckNameAvailability && isNameAvailable === false) ||
    (shouldCheckShortCodeAvailability && isShortCodeAvailable === false)
  );

  const hasHierarchyErrors = !!errors.parentId;

  const hasDetailsErrors = !!(
    errors.description ||
    errors.slug ||
    (shouldCheckSlugAvailability && isSlugAvailable === false)
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Service category name is already taken. Please choose a different name."
      );
      return;
    }

    if (shouldCheckSlugAvailability && isSlugAvailable === false) {
      toast.error(
        "Service category slug is already taken. Please choose a different slug."
      );
      return;
    }

    if (shouldCheckShortCodeAvailability && isShortCodeAvailable === false) {
      toast.error(
        "Service category short code is already taken. Please choose a different short code."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      // Helper function to convert null to undefined for parentId
      const getParentId = (
        parentId: string | null | undefined
      ): string | undefined => {
        return parentId === null ? undefined : parentId || undefined;
      };

      if (serviceCategory) {
        // Update existing service category
        const updateData: UpdateServiceCategoryDto = {
          name: data.name,
          shortCode: data.shortCode || undefined,
          parentId: getParentId(data.parentId),
          description: data.description || undefined,
          slug: data.slug || undefined,
          availableOnline: data.availableOnline,
          color: data.color || undefined,
          status: data.status,
          // Location fields
          isAllocatedToAllLocations: data.isAllocatedToAllLocations,
          locationIds:
            data.isAllocatedToAllLocations ||
            !data.locationIds ||
            data.locationIds.length === 0
              ? undefined
              : data.locationIds,
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await updateServiceCategoryMutation.mutateAsync({
          id: serviceCategory.id,
          data: updateData,
          image: selectedImage || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service category updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as unknown as ServiceCategoryTableData);
          return;
        }
        toast.error(response.message || "Failed to update service category");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new service category
        const createData = {
          name: data.name,
          shortCode: data.shortCode || undefined,
          parentId: getParentId(data.parentId),
          description: data.description || undefined,
          slug: data.slug || undefined,
          availableOnline: data.availableOnline,
          color: data.color || undefined,
          status: data.status,
          // Location fields
          isAllocatedToAllLocations: data.isAllocatedToAllLocations,
          locationIds:
            data.isAllocatedToAllLocations ||
            !data.locationIds ||
            data.locationIds.length === 0
              ? undefined
              : data.locationIds,
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await createServiceCategoryMutation.mutateAsync({
          data: createData,
          image: selectedImage || undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service category created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as unknown as ServiceCategoryTableData);
          return;
        }
        toast.error(response.message || "Failed to create service category");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save service category:", error);
      toast.error("Failed to save service category");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "") // Remove special characters
      .replace(/\s+/g, "-") // Replace spaces with hyphens
      .replace(/-+/g, "-") // Replace multiple hyphens with single
      .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
  };

  // Auto-generate slug when name changes
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "name" && value.name) {
        form.setValue("slug", generateSlug(value.name));
      }
      // Trigger validation when availableOnline changes to show/hide required field errors
      if (name === "availableOnline") {
        // Force validation of SEO fields when availableOnline changes
        form.trigger(["slug", "seoTitle", "seoDescription"]);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Handle availability checks
  const handleCheckNameAvailability = (value: string) => {
    setCheckName(value);
  };

  const handleCheckSlugAvailability = (value: string) => {
    setCheckSlug(value);
  };

  const handleCheckShortCodeAvailability = (value: string) => {
    setCheckShortCode(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="name"
                label="Name"
                placeholder="Enter service category name (e.g., Consultation, Repair)"
                value={form.watch("name") || ""}
                onChange={(value) =>
                  form.setValue("name", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckNameAvailability}
                isCheckingAvailability={isCheckingName}
                isAvailable={isNameAvailable}
                shouldCheck={shouldCheckNameAvailability}
                error={errors.name?.message}
                disabled={isSubmitting}
                fieldName="Name"
              />

              <div>
                <AvailabilityInput
                  name="shortCode"
                  label="Short Code"
                  placeholder="Enter short code (e.g., CONS, REP)"
                  value={form.watch("shortCode") || ""}
                  onChange={(value) =>
                    form.setValue("shortCode", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckShortCodeAvailability}
                  isCheckingAvailability={isCheckingShortCode}
                  isAvailable={isShortCodeAvailable}
                  shouldCheck={shouldCheckShortCodeAvailability}
                  error={errors.shortCode?.message}
                  disabled={isSubmitting}
                  fieldName="Short code"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Optional short identifier for the service category
                </p>
              </div>

              <div>
                <Label>Color</Label>
                <Input
                  {...form.register("color")}
                  name="color"
                  type="color"
                  placeholder="#3B82F6"
                  className={cn(
                    "h-10 w-full cursor-pointer",
                    errors.color && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.color && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.color.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a color to represent this service category
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Available Online</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this service category available in the online store
                    {form.watch("availableOnline") && (
                      <span className="block text-orange-600 font-medium mt-1">
                        ⚠️ When enabled, Slug and SEO fields become required
                      </span>
                    )}
                  </p>
                </div>
                <Switch
                  checked={form.watch("availableOnline")}
                  onCheckedChange={(checked) =>
                    form.setValue("availableOnline", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.availableOnline && (
                <p className="text-sm text-destructive mt-1">
                  {errors.availableOnline.message}
                </p>
              )}

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue(
                      "status",
                      value as
                        | ServiceCategoryStatus.ACTIVE
                        | ServiceCategoryStatus.INACTIVE,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ServiceCategoryStatus.ACTIVE}>
                      Active
                    </SelectItem>
                    <SelectItem value={ServiceCategoryStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "hierarchy",
      title: "Service Category Hierarchy",
      icon: <FolderTree className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasHierarchyErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FolderTree className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Service Category Hierarchy</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Parent Service Category</Label>
                <Select
                  value={form.watch("parentId") || "none"}
                  onValueChange={(value) =>
                    form.setValue("parentId", value === "none" ? null : value, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.parentId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select parent service category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      No Parent (Root Service Category)
                    </SelectItem>
                    {flatServiceCategories
                      .filter((cat) => cat.id && cat.id.trim() !== "") // Filter out service categories with empty IDs
                      .map((cat) => (
                        <SelectItem
                          key={cat.id}
                          value={cat.id}
                          disabled={serviceCategory?.id === cat.id} // Don't allow self as parent
                        >
                          {cat.name}
                          {cat.subServiceCategories &&
                            cat.subServiceCategories.length > 0 && (
                              <span className="text-muted-foreground ml-2">
                                ({cat.subServiceCategories.length} subcategories)
                              </span>
                            )}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                {errors.parentId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.parentId.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a parent service category to create a hierarchy
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter service category description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the service category
                </p>
              </div>

              <div>
                <AvailabilityInput
                  name="slug"
                  label={`Slug${form.watch("availableOnline") ? " *" : ""}`}
                  placeholder="URL-friendly version (auto-generated from name)"
                  value={form.watch("slug") || ""}
                  onChange={(value) =>
                    form.setValue("slug", value, { shouldValidate: true })
                  }
                  onCheckAvailability={handleCheckSlugAvailability}
                  isCheckingAvailability={isCheckingSlug}
                  isAvailable={isSlugAvailable}
                  shouldCheck={shouldCheckSlugAvailability}
                  error={errors.slug?.message}
                  disabled={isSubmitting}
                  fieldName="Slug"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Used for SEO-friendly URLs (automatically generated if left
                  empty)
                  {form.watch("availableOnline") && (
                    <span className="block text-orange-600 font-medium">
                      Required when service category is available online
                    </span>
                  )}
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "service-category-image",
      title: "Service Category Image",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Service Category Image</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Image"
              description="Upload an image for this service category. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ServiceCategoryTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={serviceCategory}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Service Category"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingServiceCategory}
      // Location props
      isLocationEnabled={true}
      isAllocatedToAllLocations={
        form.watch("isAllocatedToAllLocations") || false
      }
      onAllLocationsChange={(isAllocated) => {
        form.setValue("isAllocatedToAllLocations", isAllocated, {
          shouldValidate: true,
        });
        // Clear location IDs when allocating to all locations
        if (isAllocated) {
          form.setValue("locationIds", [], { shouldValidate: true });
        }
      }}
      locationIds={form.watch("locationIds") || []}
      onLocationChange={(locationIds) => {
        form.setValue("locationIds", locationIds, { shouldValidate: true });
      }}
      locationErrors={
        errors.locationIds?.message || errors.isAllocatedToAllLocations?.message
      }
      // SEO props
      isSEOEnabled={true}
      seoTitle={form.watch("seoTitle") || ""}
      onSeoTitleChange={(value) => {
        form.setValue("seoTitle", value, { shouldValidate: true });
      }}
      seoTitleError={errors.seoTitle?.message}
      seoDescription={form.watch("seoDescription") || ""}
      onSeoDescriptionChange={(value) => {
        form.setValue("seoDescription", value, { shouldValidate: true });
      }}
      seoDescriptionError={errors.seoDescription?.message}
      seoKeywords={keywords}
      onSeoKeywordsChange={(value) => {
        setKeywords(value);
        form.setValue("seoKeywords", parseKeywords(value), {
          shouldValidate: true,
        });
      }}
      seoKeywordsError={errors.seoKeywords?.message}
      ogImageFiles={ogImageFiles}
      onOgImageFilesChange={setOgImageFiles}
    />
  );
}
