"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  She<PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { SalesReceipt, SalesReceiptFormValues } from "@/types/sales-receipt";
import {
  createSalesReceipt,
  updateSalesReceipt,
} from "@/lib/sales-receipts/actions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { toast } from "sonner";

const salesReceiptFormSchema = z.object({
  reference: z.string().optional(),
  customerId: z.string().min(1, "Customer is required"),
  receiptDate: z.date({
    required_error: "Receipt date is required",
  }),
  paymentMode: z.string().min(1, "Payment mode is required"),
  status: z.enum(["Paid", "Pending", "Cancelled"]),
  amount: z.coerce.number().min(0, "Amount must be a positive number"),
  notes: z.string().optional(),
});

interface SalesReceiptSheetProps {
  salesReceipt: SalesReceipt | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  showTrigger?: boolean;
  isDemo?: boolean;
  isUpdate?: boolean;
}

export function SalesReceiptSheet({
  salesReceipt,
  open,
  onOpenChange,
  onSuccess,
  showTrigger = true,
  isDemo = false,
  isUpdate = false,
}: SalesReceiptSheetProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);

  const defaultValues: SalesReceiptFormValues = {
    reference: salesReceipt?.reference || "",
    customerId: salesReceipt?.customerId || "",
    receiptDate: salesReceipt?.receiptDate
      ? new Date(salesReceipt.receiptDate)
      : new Date(),
    paymentMode: salesReceipt?.paymentMode || "Cash",
    status: salesReceipt?.status || "Paid",
    amount: salesReceipt?.amount || 0,
    notes: salesReceipt?.notes || "",
  };

  const form = useForm<z.infer<typeof salesReceiptFormSchema>>({
    resolver: zodResolver(salesReceiptFormSchema),
    defaultValues,
  });

  async function onSubmit(values: z.infer<typeof salesReceiptFormSchema>) {
    setIsLoading(true);

    try {
      if (isUpdate && salesReceipt) {
        // Update existing sales receipt
        await updateSalesReceipt(
          {
            _id: salesReceipt._id,
            ...values,
          },
          isDemo
        );
        toast.success("Sales Receipt updated successfully");
      } else {
        // Create new sales receipt
        await createSalesReceipt(values, isDemo);
        toast.success("Sales Receipt created successfully");
      }

      if (onOpenChange) {
        onOpenChange(false);
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Failed to save sales receipt:", error);
      toast.error("Failed to save sales receipt");
    } finally {
      setIsLoading(false);
    }
  }

  const title = isUpdate ? "Edit Sales Receipt" : "Add Sales Receipt";
  const description = isUpdate
    ? "Update sales receipt details"
    : "Add a new sales receipt to your system";
  const action = isUpdate ? "Save changes" : "Add Sales Receipt";

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <SheetTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 gap-1">
            <Plus className="h-3.5 w-3.5" />
            <span>Add</span>
          </Button>
        </SheetTrigger>
      )}
      <SheetContent className="w-full sm:max-w-md md:max-w-lg overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="space-y-5 py-6"
          >
            <FormField
              control={form.control}
              name="reference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reference</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter reference" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="customerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select customer" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {/* In a real implementation, these would be dynamically fetched */}
                      <SelectItem value="cust1">John Doe</SelectItem>
                      <SelectItem value="cust2">Jane Smith</SelectItem>
                      <SelectItem value="cust3">Company XYZ</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="receiptDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Receipt Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="paymentMode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Mode</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment mode" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Cash">Cash</SelectItem>
                      <SelectItem value="Credit Card">Credit Card</SelectItem>
                      <SelectItem value="Debit Card">Debit Card</SelectItem>
                      <SelectItem value="Bank Transfer">
                        Bank Transfer
                      </SelectItem>
                      <SelectItem value="Check">Check</SelectItem>
                      <SelectItem value="Digital Wallet">
                        Digital Wallet
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Paid">Paid</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      step="0.01"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter notes"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <SheetFooter>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Saving..." : action}
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
