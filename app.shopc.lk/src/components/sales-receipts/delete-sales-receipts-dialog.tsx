"use client";

import * as React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Trash } from "lucide-react";
import { SalesReceipt } from "@/types/sales-receipt";
import { deleteSalesReceipt } from "@/lib/sales-receipts/actions";
import { toast } from "sonner";

interface DeleteSalesReceiptsDialogProps {
  salesReceipt: SalesReceipt | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  showTrigger?: boolean;
  isDemo?: boolean;
}

export function DeleteSalesReceiptsDialog({
  salesReceipt,
  open,
  onOpenChange,
  onSuccess,
  showTrigger = true,
  isDemo = false,
}: DeleteSalesReceiptsDialogProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);

  async function onDelete() {
    if (!salesReceipt) return;

    setIsLoading(true);

    try {
      await deleteSalesReceipt(salesReceipt._id, isDemo);

      toast.success("Sales receipt deleted successfully");

      if (onOpenChange) {
        onOpenChange(false);
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Failed to delete sales receipt:", error);
      toast.error("Failed to delete sales receipt");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-1 h-8">
            <Trash className="h-3.5 w-3.5" />
            <span>Delete</span>
          </Button>
        </DialogTrigger>
      )}
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Sales Receipt</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this sales receipt?
            <div className="mt-2 p-3 bg-muted rounded-md">
              <p className="font-semibold">{salesReceipt?.receiptNumber}</p>
              <p className="text-sm text-muted-foreground">
                {salesReceipt?.customerName} -{" "}
                {salesReceipt?.receiptDate &&
                  new Date(salesReceipt.receiptDate).toLocaleDateString()}
              </p>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              if (onOpenChange) {
                onOpenChange(false);
              }
            }}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button variant="destructive" onClick={onDelete} disabled={isLoading}>
            {isLoading ? "Deleting..." : "Delete"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
