"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Info, Settings, Check, X } from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { workOrderStatusFormSchema } from "@/lib/work-order-statuses/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  WorkOrderStatusTableData,
  WorkOrderStatusType,
  UpdateWorkOrderStatusDto,
} from "@/types/work-order-status";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ColorPicker } from "@/components/ui/color-picker";
import { IconSelector } from "@/components/ui/icon-selector";
import {
  useWorkOrderStatusData,
  useWorkOrderStatusNameAvailability,
  useCreateWorkOrderStatus,
  useUpdateWorkOrderStatus,
} from "@/lib/work-order-statuses/hooks";

interface WorkOrderStatusSheetProps {
  workOrderStatus: WorkOrderStatusTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (workOrderStatus?: WorkOrderStatusTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof workOrderStatusFormSchema>;

export function WorkOrderStatusSheet({
  workOrderStatus,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: WorkOrderStatusSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Debounced values for availability checking
  const [debouncedStatusName, setDebouncedStatusName] =
    React.useState<string>("");

  // Fetch complete work order status data if updating
  const {
    data: fullWorkOrderStatusResponse,
    isLoading: isLoadingWorkOrderStatus,
  } = useWorkOrderStatusData(workOrderStatus?.id || "", isDemo);
  const fullWorkOrderStatus = fullWorkOrderStatusResponse?.data;

  const form = useForm<FormData>({
    resolver: zodResolver(workOrderStatusFormSchema),
    defaultValues: {
      statusCode: "",
      statusName: "",
      description: "",
      colorCode: "",
      iconName: "",
      statusType: WorkOrderStatusType.INITIAL,
      isActive: true,
      isDefault: false,
    },
  });

  const { errors } = form.formState;

  // Debounce status name for availability checking
  const statusNameValue = form.watch("statusName");
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (statusNameValue && statusNameValue.length > 0) {
        setDebouncedStatusName(statusNameValue);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [statusNameValue]);

  // Check status name availability
  const shouldCheckStatusNameAvailability =
    debouncedStatusName.length > 0 &&
    (!isUpdate || debouncedStatusName !== fullWorkOrderStatus?.statusName);

  const { data: statusNameAvailabilityResponse } =
    useWorkOrderStatusNameAvailability(
      debouncedStatusName,
      isDemo,
      isUpdate ? workOrderStatus?.id : undefined,
      shouldCheckStatusNameAvailability
    );

  const isStatusNameAvailable =
    statusNameAvailabilityResponse?.data?.available ?? true;

  // Populate form when editing
  React.useEffect(() => {
    if (isUpdate && fullWorkOrderStatus) {
      form.reset({
        statusCode: fullWorkOrderStatus.statusCode || "",
        statusName: fullWorkOrderStatus.statusName || "",
        description: fullWorkOrderStatus.description || "",
        colorCode: fullWorkOrderStatus.colorCode || "",
        iconName: fullWorkOrderStatus.iconName || "",
        statusType:
          fullWorkOrderStatus.statusType || WorkOrderStatusType.INITIAL,
        isActive: fullWorkOrderStatus.isActive ?? true,
        isDefault: fullWorkOrderStatus.isDefault ?? false,
      });
    } else if (!isUpdate) {
      form.reset({
        statusCode: "",
        statusName: "",
        description: "",
        colorCode: "",
        iconName: "",
        statusType: WorkOrderStatusType.INITIAL,
        isActive: true,
        isDefault: false,
      });
    }
  }, [isUpdate, fullWorkOrderStatus]);

  // Mutations
  const createMutation = useCreateWorkOrderStatus(isDemo);
  const updateMutation = useUpdateWorkOrderStatus(isDemo);

  const handleSubmit = React.useCallback(
    async (data: FormData) => {
      if (!isStatusNameAvailable && shouldCheckStatusNameAvailability) {
        toast.error("Status name is already taken");
        return;
      }

      setIsSubmitting(true);

      try {
        if (isUpdate && workOrderStatus) {
          const updateData: UpdateWorkOrderStatusDto = {
            statusCode: data.statusCode,
            statusName: data.statusName,
            description: data.description || undefined,
            colorCode: data.colorCode || undefined,
            iconName: data.iconName || undefined,
            statusType: data.statusType,
            isActive: data.isActive,
            isDefault: data.isDefault,
          };

          const result = await updateMutation.mutateAsync({
            id: workOrderStatus.id,
            data: updateData,
          });

          if (result.status === ApiStatus.SUCCESS) {
            toast.success("Work order status updated successfully");
            onSuccess?.(result.data as WorkOrderStatusTableData);
            onOpenChange?.(false);
          } else {
            toast.error(result.message || "Failed to update work order status");
          }
        } else {
          const result = await createMutation.mutateAsync(data);

          if (result.status === ApiStatus.SUCCESS) {
            toast.success("Work order status created successfully");
            onSuccess?.(result.data as WorkOrderStatusTableData);
            onOpenChange?.(false);
          } else {
            toast.error(result.message || "Failed to create work order status");
          }
        }
      } catch (error) {
        console.error("Error submitting work order status:", error);
        toast.error("An unexpected error occurred");
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      isStatusNameAvailable,
      shouldCheckStatusNameAvailability,
      isUpdate,
      workOrderStatus,
      updateMutation,
      createMutation,
      onSuccess,
      onOpenChange,
    ]
  );

  // Check for basic info errors
  const hasBasicInfoErrors = !!(
    errors.statusCode ||
    errors.statusName ||
    errors.description ||
    errors.statusType
  );

  // Check for settings errors
  const hasSettingsErrors = !!(
    errors.colorCode ||
    errors.iconName ||
    errors.isActive ||
    errors.isDefault
  );

  // Wrapper function for BaseSheet onSubmit
  const handleFormSubmit = React.useCallback(async () => {
    const formData = form.getValues();
    await handleSubmit(formData);
  }, [, handleSubmit]);

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Status Code</Label>
                <Input
                  {...form.register("statusCode")}
                  name="statusCode"
                  placeholder="Enter status code (e.g., INIT, PROG, COMP)"
                  className={cn(errors.statusCode && "border-destructive")}
                />
                {errors.statusCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.statusCode.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Status Name</Label>
                <div className="relative">
                  <Input
                    {...form.register("statusName")}
                    name="statusName"
                    placeholder="Enter status name (e.g., Initial, In Progress, Completed)"
                    className={cn(
                      errors.statusName && "border-destructive",
                      shouldCheckStatusNameAvailability &&
                        !isStatusNameAvailable &&
                        "border-destructive"
                    )}
                  />
                  {shouldCheckStatusNameAvailability && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      {isStatusNameAvailable ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <X className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
                {errors.statusName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.statusName.message}
                  </p>
                )}
                {shouldCheckStatusNameAvailability &&
                  !isStatusNameAvailable && (
                    <p className="text-sm text-destructive mt-1">
                      This status name is already taken
                    </p>
                  )}
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter status description (optional)"
                  className={cn(errors.description && "border-destructive")}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Status Type</Label>
                <Select
                  value={form.watch("statusType")}
                  onValueChange={(value) =>
                    form.setValue("statusType", value as WorkOrderStatusType, {
                      shouldValidate: true,
                    })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.statusType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={WorkOrderStatusType.INITIAL}>
                      Initial
                    </SelectItem>
                    <SelectItem value={WorkOrderStatusType.PLANNING}>
                      Planning
                    </SelectItem>
                    <SelectItem value={WorkOrderStatusType.IN_PROGRESS}>
                      In Progress
                    </SelectItem>
                    <SelectItem value={WorkOrderStatusType.FINAL}>
                      Final
                    </SelectItem>
                    <SelectItem value={WorkOrderStatusType.CANCELLED}>
                      Cancelled
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.statusType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.statusType.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "settings",
      title: "Settings",
      icon: <Settings className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasSettingsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Settings</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Color Code</Label>
                <div className="flex items-center gap-2">
                  <ColorPicker
                    value={form.watch("colorCode") || "#3B82F6"}
                    onChange={(color) =>
                      form.setValue("colorCode", color, {
                        shouldValidate: true,
                      })
                    }
                    className={cn(errors.colorCode && "border-destructive")}
                  />
                  <Input
                    {...form.register("colorCode")}
                    name="colorCode"
                    placeholder="Enter hex color code (e.g., #3B82F6)"
                    className={cn("flex-1", errors.colorCode && "border-destructive")}
                  />
                </div>
                {errors.colorCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.colorCode.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Icon</Label>
                <IconSelector
                  value={form.watch("iconName")}
                  onChange={(iconName) =>
                    form.setValue("iconName", iconName || "", {
                      shouldValidate: true,
                    })
                  }
                  placeholder="Select an icon (optional)"
                  className={cn(errors.iconName && "border-destructive")}
                />
                {errors.iconName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.iconName.message}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Active Status</Label>
                  <div className="text-sm text-muted-foreground">
                    Enable or disable this work order status
                  </div>
                </div>
                <Switch
                  checked={form.watch("isActive")}
                  onCheckedChange={(checked) =>
                    form.setValue("isActive", checked, {
                      shouldValidate: true,
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Default Status</Label>
                  <div className="text-sm text-muted-foreground">
                    Set as default work order status
                  </div>
                </div>
                <Switch
                  checked={form.watch("isDefault")}
                  onCheckedChange={(checked) =>
                    form.setValue("isDefault", checked, {
                      shouldValidate: true,
                    })
                  }
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<WorkOrderStatusTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={workOrderStatus}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Work Order Status"
      sections={sections}
      onSubmit={handleFormSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingWorkOrderStatus}
    />
  );
}
