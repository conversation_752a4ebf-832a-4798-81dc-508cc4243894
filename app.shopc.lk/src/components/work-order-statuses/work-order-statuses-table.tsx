"use client";

import * as React from "react";
import type {
  DataTableAdvanced<PERSON>ilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { getWorkOrderStatusesTableData } from "@/lib/work-order-statuses/queries";
import { DeleteWorkOrderStatusesDialog } from "./delete-work-order-statuses-dialog";
import { WorkOrderStatusesTableToolbarActions } from "./work-order-statuses-table-toolbar-actions";
import { WorkOrderStatusesTableFloatingBar } from "./work-order-statuses-table-floating-bar";
import { WorkOrderStatusSheet } from "./work-order-status-sheet";
import { WorkOrderStatusDetails } from "./work-order-status-details";
import { WorkOrderStatusDetailsContent } from "./work-order-status-details-content";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./work-order-statuses-table-columns";
import {
  WorkOrderStatusTableData,
  WorkOrderStatusType,
} from "@/types/work-order-status";
import { useWorkOrderStatusesData } from "@/lib/work-order-statuses/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { workOrderStatusKeys } from "@/lib/work-order-statuses/hooks";
import { updateWorkOrderStatusPositions } from "@/lib/work-order-statuses/queries";
import { ApiStatus } from "@/types/common";

interface WorkOrderStatusesTableProps {
  isDemo?: boolean;
}

export function WorkOrderStatusesTable({
  isDemo = false,
}: WorkOrderStatusesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<WorkOrderStatusTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const filterFields: DataTableFilterField<WorkOrderStatusTableData>[] =
    React.useMemo(
      () => [
        {
          id: "statusName",
          label: "Status Name",
          placeholder: "Filter by status name...",
        },
        {
          id: "statusCode",
          label: "Status Code",
          placeholder: "Filter by status code...",
        },
        // status type
        {
          id: "statusType",
          label: "Status Type",
          placeholder: "Filter by status type...",
          type: "select",
          options: [
            { label: "Initial", value: WorkOrderStatusType.INITIAL },
            { label: "Planning", value: WorkOrderStatusType.PLANNING },
            { label: "In Progress", value: WorkOrderStatusType.IN_PROGRESS },
            { label: "Final", value: WorkOrderStatusType.FINAL },
            { label: "Cancelled", value: WorkOrderStatusType.CANCELLED },
          ],
        },
        // active status
        {
          id: "isActive",
          label: "Active Status",
          placeholder: "Filter by active status...",
          type: "select",
          options: [
            { label: "Active", value: "true" },
            { label: "Inactive", value: "false" },
          ],
        },
        // default status
        {
          id: "isDefault",
          label: "Default Status",
          placeholder: "Filter by default status...",
          type: "select",
          options: [
            { label: "Default", value: "true" },
            { label: "Not Default", value: "false" },
          ],
        },
      ],
      []
    );

  const advancedFilterFields: DataTableAdvancedFilterField<WorkOrderStatusTableData>[] =
    React.useMemo(
      () => [
        {
          id: "statusName",
          label: "Status Name",
          type: "text",
        },
        {
          id: "statusCode",
          label: "Status Code",
          type: "text",
        },
        {
          id: "description",
          label: "Description",
          type: "text",
        },
        {
          id: "statusType",
          label: "Status Type",
          type: "select",
          options: [
            { label: "Initial", value: WorkOrderStatusType.INITIAL },
            { label: "Planning", value: WorkOrderStatusType.PLANNING },
            { label: "In Progress", value: WorkOrderStatusType.IN_PROGRESS },
            { label: "Final", value: WorkOrderStatusType.FINAL },
            { label: "Cancelled", value: WorkOrderStatusType.CANCELLED },
          ],
        },
        {
          id: "isActive",
          label: "Active Status",
          type: "select",
          options: [
            { label: "Active", value: "true" },
            { label: "Inactive", value: "false" },
          ],
        },
        {
          id: "isDefault",
          label: "Default Status",
          type: "select",
          options: [
            { label: "Default", value: "true" },
            { label: "Not Default", value: "false" },
          ],
        },
      ],
      []
    );

  const sortFields: DataTableAdvancedFilterField<WorkOrderStatusTableData>[] =
    React.useMemo(
      () => [
        {
          id: "statusName",
          label: "Status Name",
          type: "text",
        },
        {
          id: "createdAt",
          label: "Created Date",
          type: "text",
        },
        {
          id: "updatedAt",
          label: "Updated Date",
          type: "text",
        },
      ],
      []
    );

  const handleRowAction = React.useCallback(
    (action: DataTableRowAction<WorkOrderStatusTableData>) => {
      setRowAction(action);
    },
    []
  );

  const searchParamsValues = React.useMemo(() => {
    return {
      page: searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1,
      perPage: searchParams.get("perPage")
        ? parseInt(searchParams.get("perPage") as string)
        : 10,
      statusName: searchParams.get("statusName") || "",
      statusCode: searchParams.get("statusCode") || "",
      statusType: searchParams.get("statusType") || "",
      isActive: searchParams.get("isActive") || "",
      isDefault: searchParams.get("isDefault") || "",
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
      filters: (() => {
        const filtersParam = searchParams.get("filters");
        return filtersParam ? JSON.parse(filtersParam) : [];
      })(),
      joinOperator: (searchParams.get("joinOperator") as "and" | "or") || "and",
      sort: (() => {
        const sortParam = searchParams.get("sort");
        return sortParam ? JSON.parse(sortParam) : [];
      })(),
    };
  }, [searchParams]);

  // Use TanStack Query hook
  const {
    data: workOrderStatusesData,
    isLoading,
    isFetching,
  } = useWorkOrderStatusesData(searchParamsValues, isDemo);

  // Only show full loading indicator for initial loading
  // Show header loading for refetch operations
  const isHeaderLoading = isFetching && !isLoading;

  // Determine if actions should be disabled (during any loading/fetching state)
  const isActionsDisabled = isLoading || isFetching;

  // Check if there are active filters or search parameters
  const hasActiveFilters = React.useMemo(() => {
    return !!(
      searchParamsValues.statusName ||
      searchParamsValues.statusCode ||
      searchParamsValues.statusType ||
      searchParamsValues.isActive ||
      searchParamsValues.isDefault ||
      searchParamsValues.from ||
      searchParamsValues.to ||
      (searchParamsValues.filters && searchParamsValues.filters.length > 0)
    );
  }, [searchParamsValues]);

  // Handle reordering
  const handleReorder = React.useCallback(
    async (updates: { id: string; position: number }[]) => {
      try {
        const result = await updateWorkOrderStatusPositions(
          { workOrderStatuses: updates },
          isDemo
        );
        return {
          status: result.status === ApiStatus.SUCCESS ? "success" : "error",
          message: result.message || undefined,
        };
      } catch (error) {
        return {
          status: "error",
          message: "Failed to reorder work order statuses",
        };
      }
    },
    [isDemo]
  );

  const handleRefresh = React.useCallback(
    async (workOrderStatusIds?: string[]) => {
      const invalidatePromises = [
        queryClient.invalidateQueries({
          queryKey: workOrderStatusKeys.lists(),
        }),
        queryClient.invalidateQueries({ queryKey: workOrderStatusKeys.slim() }),
      ];

      // If specific work order status IDs are provided, invalidate their detail cache as well
      if (workOrderStatusIds && workOrderStatusIds.length > 0) {
        workOrderStatusIds.forEach((workOrderStatusId) => {
          invalidatePromises.push(
            queryClient.invalidateQueries({
              queryKey: workOrderStatusKeys.detail(workOrderStatusId),
            })
          );
        });
      }

      await Promise.all(invalidatePromises);
    },
    [queryClient]
  );

  // Custom dialog content renderer for row clicks
  const renderWorkOrderStatusDetails = React.useCallback(
    (workOrderStatus: WorkOrderStatusTableData) => {
      return (
        <WorkOrderStatusDetailsContent
          workOrderStatus={workOrderStatus}
          isDemo={isDemo}
        />
      );
    },
    [isDemo]
  );

  return (
    <>
      <BaseTable<
        WorkOrderStatusTableData,
        Awaited<ReturnType<typeof getWorkOrderStatusesTableData>>
      >
        data={workOrderStatusesData || undefined}
        isLoading={isLoading}
        isHeaderLoading={isHeaderLoading}
        isDemo={isDemo}
        hasActiveFilters={hasActiveFilters}
        enableSortableRows={true}
        onReorder={handleReorder}
        currentPage={searchParamsValues.page}
        itemsPerPage={searchParamsValues.perPage}
        isDragDropEnabled={() => true}
        columns={getColumns({
          setRowAction,
          isDemo,
          isActionsDisabled,
          isSortableEnabled: true,
          onStatusUpdate: (workOrderStatusId?: string) => {
            queryClient.invalidateQueries({
              queryKey: workOrderStatusKeys.lists(),
            });
            queryClient.invalidateQueries({
              queryKey: workOrderStatusKeys.slim(),
            });
            if (workOrderStatusId) {
              queryClient.invalidateQueries({
                queryKey: workOrderStatusKeys.detail(workOrderStatusId),
              });
            }
          },
        })}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        sortFields={sortFields}
        getInitialData={(response) => {
          return (response?.data?.data ?? []) as WorkOrderStatusTableData[];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={false}
        enableFloatingBar={true}
        FloatingBar={(props) => (
          <WorkOrderStatusesTableFloatingBar
            {...props}
            onRefresh={handleRefresh}
            isDemo={isDemo}
          />
        )}
        ToolbarActions={(props) => (
          <WorkOrderStatusesTableToolbarActions {...props} isDemo={isDemo} />
        )}
        onRowAction={handleRowAction}
        defaultSortingId={undefined}
        defaultSortingDesc={false}
        onRefresh={handleRefresh}
        renderDialogContent={renderWorkOrderStatusDetails}
      />

      <WorkOrderStatusDetails
        open={rowAction?.type === "view"}
        onOpenChange={() => setRowAction(null)}
        workOrderStatus={rowAction?.row.original ?? null}
        isDemo={isDemo}
      />
      <WorkOrderStatusSheet
        open={rowAction?.type === "update"}
        onOpenChange={() => setRowAction(null)}
        workOrderStatus={rowAction?.row.original ?? null}
        isDemo={isDemo}
        onSuccess={() => {
          const workOrderStatusId = rowAction?.row.original?.id;
          queryClient.invalidateQueries({
            queryKey: workOrderStatusKeys.lists(),
          });
          queryClient.invalidateQueries({
            queryKey: workOrderStatusKeys.slim(),
          });
          if (workOrderStatusId) {
            queryClient.invalidateQueries({
              queryKey: workOrderStatusKeys.detail(workOrderStatusId),
            });
          }
        }}
        isUpdate={true}
      />
      <DeleteWorkOrderStatusesDialog
        open={rowAction?.type === "delete"}
        onOpenChange={() => setRowAction(null)}
        workOrderStatuses={
          rowAction?.row.original ? [rowAction.row.original] : []
        }
        showTrigger={false}
        onSuccess={() => {
          queryClient.invalidateQueries({
            queryKey: workOrderStatusKeys.lists(),
          });
          queryClient.invalidateQueries({
            queryKey: workOrderStatusKeys.slim(),
          });
        }}
        isDemo={isDemo}
      />
    </>
  );
}
