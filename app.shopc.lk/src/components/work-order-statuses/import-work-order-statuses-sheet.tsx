/**
 * ImportWorkOrderStatusesSheet Component
 *
 * This component allows importing work order statuses with the following features:
 * - Manual data entry or file upload (CSV/Excel)
 * - Field validation and error handling
 * - Integration with bulk import API
 */

"use client";

import {
  ImportDataSheet,
  FieldConfig,
} from "../data-import/import-data-sheet";
import {
  BulkCreateWorkOrderStatusSchema,
} from "@/lib/work-order-statuses/validations";
import { WorkOrderStatusType } from "@/types/work-order-status";
import { ApiStatus } from "@/types/common";
import {
  useBulkCreateWorkOrderStatuses,
} from "@/lib/work-order-statuses/hooks";


// Define the work order status field types that can be imported
export type WorkOrderStatusImportFields =
  | "statusCode"
  | "statusName"
  | "description"
  | "colorCode"
  | "iconName"
  | "statusType"
  | "isActive"
  | "isDefault";


// All possible fields for work order status import
const ALL_WORK_ORDER_STATUS_FIELDS: WorkOrderStatusImportFields[] = [
  "statusCode",
  "statusName",
  "description",
  "colorCode",
  "iconName",
  "statusType",
  "isActive",
  "isDefault",
];

export type ImportWorkOrderStatusesSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportWorkOrderStatusesSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportWorkOrderStatusesSheetProps) {

  // Bulk import mutation
  const bulkImportMutation = useBulkCreateWorkOrderStatuses(isDemo);

  // Field configurations for work order statuses
  const WORK_ORDER_STATUS_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "statusCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter status code (e.g., INIT, PROG)",
    },
    {
      name: "statusName",
      type: "text",
      defaultValue: "",
      placeholder: "Enter status name (e.g., Initial, In Progress)",
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter description (optional)",
    },
    {
      name: "colorCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter hex color (e.g., #3B82F6)",
    },
    {
      name: "iconName",
      type: "text",
      defaultValue: "",
      placeholder: "Enter icon name (optional)",
    },
    {
      name: "statusType",
      type: "select",
      options: [
        { value: WorkOrderStatusType.INITIAL, label: "Initial" },
        { value: WorkOrderStatusType.PLANNING, label: "Planning" },
        { value: WorkOrderStatusType.IN_PROGRESS, label: "In Progress" },
        { value: WorkOrderStatusType.FINAL, label: "Final" },
        { value: WorkOrderStatusType.CANCELLED, label: "Cancelled" },
      ],
      defaultValue: WorkOrderStatusType.INITIAL,
    },
    {
      name: "isActive",
      type: "select",
      options: [
        { value: "true", label: "Active" },
        { value: "false", label: "Inactive" },
      ],
      defaultValue: "true",
    },
    {
      name: "isDefault",
      type: "select",
      options: [
        { value: "true", label: "Default" },
        { value: "false", label: "Not Default" },
      ],
      defaultValue: "false",
    },
  ];

  // Enhanced validation function for work order statuses
  const validateWorkOrderStatusRow = (
    row: Record<string, any>,
    rowIndex?: number,
    allData?: Record<string, any>[]
  ) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (!row.statusCode || typeof row.statusCode !== "string" || row.statusCode.trim() === "") {
      errors.statusCode = "Status code is required";
      valid = false;
    } else if (row.statusCode.length > 50) {
      errors.statusCode = "Status code must be 50 characters or less";
      valid = false;
    }

    if (!row.statusName || typeof row.statusName !== "string" || row.statusName.trim() === "") {
      errors.statusName = "Status name is required";
      valid = false;
    } else if (row.statusName.length > 100) {
      errors.statusName = "Status name must be 100 characters or less";
      valid = false;
    }

    if (!row.statusType) {
      errors.statusType = "Status type is required";
      valid = false;
    } else if (!Object.values(WorkOrderStatusType).includes(row.statusType as WorkOrderStatusType)) {
      errors.statusType = "Invalid status type";
      valid = false;
    }

    // Validate optional fields
    if (row.colorCode && !/^#[0-9A-F]{6}$/i.test(row.colorCode)) {
      errors.colorCode = "Color must be a valid hex color (e.g., #3B82F6)";
      valid = false;
    }

    if (row.iconName && row.iconName.length > 50) {
      errors.iconName = "Icon name must be 50 characters or less";
      valid = false;
    }

    // Check for duplicates within dataset
    if (allData && rowIndex !== undefined) {
      // Check statusCode duplicates
      const statusCodeDuplicate = allData.some((item, index) => 
        index !== rowIndex && 
        item.statusCode && 
        item.statusCode.trim().toUpperCase() === row.statusCode.trim().toUpperCase()
      );
      if (statusCodeDuplicate) {
        errors.statusCode = "Status code must be unique within the import data";
        valid = false;
      }

      // Check statusName duplicates
      const statusNameDuplicate = allData.some((item, index) => 
        index !== rowIndex && 
        item.statusName && 
        item.statusName.trim().toLowerCase() === row.statusName.trim().toLowerCase()
      );
      if (statusNameDuplicate) {
        errors.statusName = "Status name must be unique within the import data";
        valid = false;
      }
    }

    return { valid, errors };
  };

  // Transform row data to BulkCreateWorkOrderStatusSchema format
  const transformRowToWorkOrderStatus = (
    row: Record<string, any>
  ): BulkCreateWorkOrderStatusSchema => {
    return {
      statusCode: row.statusCode.trim(),
      statusName: row.statusName.trim(),
      description: row.description && row.description.trim() ? row.description.trim() : undefined,
      colorCode: row.colorCode && row.colorCode.trim() ? row.colorCode.trim() : undefined,
      iconName: row.iconName && row.iconName.trim() ? row.iconName.trim() : undefined,
      statusType: row.statusType as WorkOrderStatusType,
      isActive: row.isActive === "true" || row.isActive === true,
      isDefault: row.isDefault === "true" || row.isDefault === true,
    };
  };

  // Handle submission of work order status data
  const handleSubmitWorkOrderStatuses = async (
    data: any[]
  ) => {
    try {

      // Transform data to BulkCreateWorkOrderStatusSchema format
      const workOrderStatusesData: BulkCreateWorkOrderStatusSchema[] = data.map(
        transformRowToWorkOrderStatus
      );

      // Use the mutation hook to import work order statuses
      const result = await bulkImportMutation.mutateAsync(workOrderStatusesData);

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        return Promise.resolve();
      } else {
        return Promise.reject(
          new Error(result.message || "Failed to import work order statuses")
        );
      }
    } catch (error) {
      console.error("Error importing work order statuses:", error);
      return Promise.reject(error);
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Work Order Statuses"
      description="Import work order statuses from a CSV or Excel file. Make sure your file includes the required fields: Status Code, Status Name, and Status Type."
      targetFields={ALL_WORK_ORDER_STATUS_FIELDS}
      fieldConfigs={WORK_ORDER_STATUS_FIELD_CONFIGS}
      validateRow={validateWorkOrderStatusRow}
      onSubmit={handleSubmitWorkOrderStatuses}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}
