"use client";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AccountStatus } from "@/types/expense-category";
import * as demoApi from "@/lib/expenses-categories/demo";
import * as api from "@/lib/expenses-categories/api";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { Loader2, Check } from "lucide-react";
import { useState } from "react";

interface ExpenseCategoryStatusBadgeProps {
  expenseCategoryId: string;
  status: AccountStatus;
  isDemo?: boolean;
  disabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (expenseCategoryId: string, newStatus: AccountStatus) => void;
}

export function ExpenseCategoryStatusBadge({
  expenseCategoryId,
  status,
  isDemo = false,
  disabled = false,
  onRefresh,
  onStatusUpdate,
}: ExpenseCategoryStatusBadgeProps) {
  // Removed useToast hook - using Sonner directly
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (newStatus: AccountStatus) => {
    // Don't make API call if status is already the same
    if (newStatus === status) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    setIsUpdating(true);

    try {
      const result = isDemo
        ? await demoApi.updateDemoExpenseCategoryApi(expenseCategoryId, {
            status: newStatus as
              | AccountStatus.ACTIVE
              | AccountStatus.INACTIVE,
          })
        : await api.updateExpenseCategoryApi(expenseCategoryId, {
            status: newStatus as
              | AccountStatus.ACTIVE
              | AccountStatus.INACTIVE,
          });

      if (result.status === ApiStatus.SUCCESS) {
        toast.success("Expense category status updated", {
          description: `Expense category status updated to ${
            newStatus === AccountStatus.ACTIVE ? "Active" : "Inactive"
          }`,
        });
        // Use the new status update handler if available
        if (onStatusUpdate) {
          onStatusUpdate(expenseCategoryId, newStatus);
        } else {
          // Fallback to onRefresh for backward compatibility
          onRefresh?.();
        }
      } else {
        toast.error("Failed to update status", {
          description: result.message || "Failed to update status",
        });
      }
    } catch (error) {
      toast.error("Failed to update expense category status", {
        description: "Failed to update expense category status. Please try again.",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const statusOptions = [
    {
      value: AccountStatus.ACTIVE,
      label: "Active",
      variant: "default" as const,
    },
    {
      value: AccountStatus.INACTIVE,
      label: "Inactive",
      variant: "outline" as const,
    },
  ];

  return (
    <DropdownMenu open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Badge
          variant={status === AccountStatus.ACTIVE ? "default" : "outline"}
          className={`${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer hover:opacity-80"
          } transition-opacity`}
        >
          {isUpdating ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          ) : (
            <span>
              {status === AccountStatus.ACTIVE ? "Active" : "Inactive"}
            </span>
          )}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[120px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleStatusUpdate(option.value)}
            disabled={isUpdating || disabled}
            className="flex items-center justify-between"
          >
            <span>{option.label}</span>
            {status === option.value && <Check className="h-3 w-3" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
