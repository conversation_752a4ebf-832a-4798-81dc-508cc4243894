"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, Check<PERSON>ircle2, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type ExpenseCategoryTableData, AccountStatus } from "@/types/expense-category";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteExpenseCategories,
  useBulkUpdateExpenseCategoryStatus,
} from "@/lib/expenses-categories/hooks";
import { ApiStatus } from "@/types/common";

interface ExpenseCategoriesTableFloatingBarProps {
  table: Table<ExpenseCategoryTableData>;
  onRefresh?: (expenseCategoryIds?: string[]) => void;
  isDemo?: boolean;
}

export function ExpenseCategoriesTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: ExpenseCategoriesTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "activate" | "deactivate"
  >();

  const bulkDeleteMutation = useBulkDeleteExpenseCategories(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateExpenseCategoryStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedExpenseCategories = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedExpenseCategories.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedExpenseCategories.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: AccountStatus) => {
      if (selectionCount === 0) return;

      setAction(status === AccountStatus.ACTIVE ? "activate" : "deactivate");

      try {
        const expenseCategoryIds = selectedExpenseCategories.map(
          (expenseCategory) => expenseCategory.expenseCategoryId
        );
        const result = await bulkUpdateStatusMutation.mutateAsync({
          expenseCategoryIds,
          status,
        });

        const failedUpdates = result.filter(
          (res: any) => res.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
          toast({
            title: "Success",
            description: `Successfully ${
              status === AccountStatus.ACTIVE ? "activated" : "deactivated"
            } ${selectionCount} expense category${selectionCount > 1 ? "s" : ""}`
          });
          table.toggleAllRowsSelected(false);
          onRefresh?.(expenseCategoryIds);
        } else {
          toast({
            title: "Error",
            description: `Failed to update ${failedUpdates.length} expense category${
              failedUpdates.length > 1 ? "s" : ""
            }`,
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error updating expense categories:", error);
        toast({
          title: "Error",
          description: "Failed to update expense categories",
          variant: "destructive"
        });
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedExpenseCategories,
      selectionCount,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No expense categories selected" };

    try {
      const expenseCategoryIds = selectedExpenseCategories.map(
        (expenseCategory) => expenseCategory.expenseCategoryId
      );
      const result = await bulkDeleteMutation.mutateAsync(expenseCategoryIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete expense categories",
        };
      }
    } catch (error: any) {
      console.error("Error deleting expense categories:", error);
      return { error: error.message || "Failed to delete expense categories" };
    }
  }, [selectedExpenseCategories, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(AccountStatus.ACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(AccountStatus.INACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<ExpenseCategoryTableData>
        table={table}
        title="Expense Categories"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} expense category${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass expense category IDs since they're deleted
        }}
      />
    </>
  );
}
