"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Icon } from "@/components/ui/icon";
import { ExpenseCategoryTableData } from "@/types/expense-category";
import { ApiStatus } from "@/types/common";
import { useBulkDeleteExpenseCategories } from "@/lib/expenses-categories/hooks";
import { toast } from "sonner";

interface DeleteExpenseCategoriesDialogProps {
  expenseCategories: ExpenseCategoryTableData[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteExpenseCategoriesDialog({
  expenseCategories,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteExpenseCategoriesDialogProps) {
  const bulkDeleteMutation = useBulkDeleteExpenseCategories(isDemo);

  // Check if any expense categories are system accounts
  const systemExpenseCategories = expenseCategories.filter(
    (expenseCategory) => expenseCategory.isSystemAccount
  );

  const hasSystemAccounts = systemExpenseCategories.length > 0;
  const isMultiple = expenseCategories.length > 1;

  const handleDelete = async () => {
    if (expenseCategories.length === 0 || hasSystemAccounts) return;

    try {
      const expenseCategoryIds = expenseCategories.map(
        (expenseCategory) => expenseCategory.expenseCategoryId
      );
      const result = await bulkDeleteMutation.mutateAsync(expenseCategoryIds);

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast.success(result.data.message);
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error(result.message || "Failed to delete expense categories");
      }
    } catch (error) {
      console.error("Error deleting expense categories:", error);
      toast.error("Failed to delete expense categories");
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <Icon name="trash" size={16} className="mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {hasSystemAccounts
              ? `Cannot Delete ${isMultiple ? "Expense Categories" : "Expense Category"}`
              : "Are you sure?"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {hasSystemAccounts ? (
              <>
                {isMultiple ? (
                  <>
                    The following expense categories cannot be deleted because they are
                    system accounts:
                    <ul className="mt-2 list-disc list-inside">
                      {systemExpenseCategories.map((cat) => (
                        <li key={cat.expenseCategoryId} className="font-medium">
                          {cat.accountName}
                          {cat.accountNumber && (
                            <span className="text-muted-foreground ml-2">
                              ({cat.accountNumber})
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <>
                    The expense category{" "}
                    <span className="font-medium">
                      {systemExpenseCategories[0]?.accountName}
                    </span>{" "}
                    cannot be deleted because it is a system account.
                  </>
                )}
                <span className="block mt-2 text-amber-600">
                  System accounts are protected and cannot be deleted.
                </span>
              </>
            ) : (
              <>
                This will permanently delete{" "}
                {isMultiple
                  ? `${expenseCategories.length} expense categories`
                  : "the expense category"}{" "}
                {!isMultiple && (
                  <span className="font-medium">
                    {expenseCategories[0]?.accountName}
                  </span>
                )}
                . This action cannot be undone.
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            {hasSystemAccounts ? "Close" : "Cancel"}
          </AlertDialogCancel>
          {!hasSystemAccounts && (
            <AlertDialogAction
              onClick={handleDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {bulkDeleteMutation.isPending
                ? "Deleting..."
                : `Delete ${
                    isMultiple
                      ? `${expenseCategories.length} Expense Categories`
                      : "Expense Category"
                  }`}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
