"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import {
  Trash2,
  Check<PERSON><PERSON>cle2,
  <PERSON>Circle,
  AlertTriangle,
  Settings,
  Package,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type AssetListDto, AssetStatus } from "@/types/asset";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteAssets,
  useBulkUpdateAssetStatus,
} from "@/lib/assets/hooks";
import { ApiStatus } from "@/types/common";

interface AssetsTableFloatingBarProps {
  table: Table<AssetListDto>;
  onRefresh?: (assetIds?: string[]) => void;
  isDemo?: boolean;
}

export function AssetsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: AssetsTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = React.useState(false);

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectedAssets = selectedRows.map((row) => row.original);
  const selectedAssetIds = selectedAssets.map((asset) => asset.id);

  // Mutation hooks
  const bulkDeleteMutation = useBulkDeleteAssets(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateAssetStatus(isDemo);

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status: AssetStatus) => {
    if (selectedAssetIds.length === 0) return;

    setIsUpdatingStatus(true);

    try {
      const response = await bulkUpdateStatusMutation.mutateAsync({
        ids: selectedAssetIds,
        status,
      });

      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: response.message ||
            `Successfully updated ${selectedAssetIds.length} asset(s) status`
        });
        table.resetRowSelection();
        onRefresh?.(selectedAssetIds);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to update asset status",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error updating asset status:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating asset status",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedAssetIds.length === 0) return;

    try {
      const response = await bulkDeleteMutation.mutateAsync(selectedAssetIds);

      if (response.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: response.message ||
            `Successfully deleted ${selectedAssetIds.length} asset(s)`
        });
        table.resetRowSelection();
        setDeleteDialogOpen(false);
        onRefresh?.(selectedAssetIds);
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to delete assets",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error deleting assets:", error);
      toast({
        title: "Error",
        description: "An error occurred while deleting assets",
        variant: "destructive"
      });
    }
  };

  const actions = [
    // Quick status updates
    {
      icon: CheckCircle2,
      label: "Mark Available",
      tooltip: "Mark selected assets as available",
      onClick: () => handleBulkStatusUpdate(AssetStatus.AVAILABLE),
      disabled: isUpdatingStatus,
    },
    {
      icon: XCircle,
      label: "Mark Unavailable",
      tooltip: "Mark selected assets as unavailable",
      onClick: () => handleBulkStatusUpdate(AssetStatus.UNAVAILABLE),
      disabled: isUpdatingStatus,
    },
    {
      icon: Settings,
      label: "Mark Maintenance",
      tooltip: "Mark selected assets as under maintenance",
      onClick: () => handleBulkStatusUpdate(AssetStatus.MAINTENANCE),
      disabled: isUpdatingStatus,
    },
    {
      icon: AlertTriangle,
      label: "Mark Damaged",
      tooltip: "Mark selected assets as damaged",
      onClick: () => handleBulkStatusUpdate(AssetStatus.DAMAGED),
      disabled: isUpdatingStatus,
    },
    {
      icon: Package,
      label: "Mark Assigned",
      tooltip: "Mark selected assets as assigned",
      onClick: () => handleBulkStatusUpdate(AssetStatus.ASSIGNED),
      disabled: isUpdatingStatus,
    },
    // Separator
    { separator: true },
    // Delete action
    {
      icon: Trash2,
      label: "Delete",
      tooltip: "Delete selected assets",
      onClick: () => setDeleteDialogOpen(true),
      variant: "destructive" as const,
      disabled: bulkDeleteMutation.isPending,
    },
  ];

  return (
    <>
      <BaseTableFloatingBar
        table={table}
        actions={actions}
        isLoading={isUpdatingStatus || bulkDeleteMutation.isPending}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleBulkDelete}
        title="Delete Assets"
        description={`Are you sure you want to delete ${selectedAssets.length} asset(s)? This action cannot be undone.`}
        confirmText="Delete Assets"
        isLoading={bulkDeleteMutation.isPending}
      />
    </>
  );
}
