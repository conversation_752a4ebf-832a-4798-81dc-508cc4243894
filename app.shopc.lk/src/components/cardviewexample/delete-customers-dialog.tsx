"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteCustomer } from "@/lib/customers/api";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { customerKeys, useCustomerById } from "@/lib/customers/hooks";
import { deleteDemoCustomer } from "@/lib/customers/demo";

interface DeleteCustomersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  customerId: string;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteCustomersDialog({
  open,
  onOpenChange,
  customerId,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteCustomersDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);
  const queryClient = useQueryClient();

  // Optionally fetch customer details for display purposes
  const { data: customerData } = useCustomerById(customerId, isDemo);

  const customerName =
    customerData?.data?.personalInfo?.customerDisplayName || customerId;

  const handleDelete = React.useCallback(async () => {
    if (!customerId) return;

    setIsDeleting(true);

    try {
      if (isDemo) {
        // Simulate API call delay for demo
        await deleteDemoCustomer(customerId);
      } else {
        // Call the actual API to delete the customer
        const result = await deleteCustomer(customerId);

        if (result.status !== "SUCCESS") {
          throw new Error(result.message || "Failed to delete customer");
        }
      }

      // Invalidate queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: customerKeys.all });

      // Show success toast
      toast.success("Customer has been deleted successfully");

      // Call onSuccess callback
      onSuccess?.();

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      toast.error("Error deleting customer: " + (error instanceof Error ? error.message : "An unexpected error occurred"));
    } finally {
      setIsDeleting(false);
    }
  }, [customerId, isDemo, onSuccess, onOpenChange, queryClient]);

  if (!customerId) {
    return null;
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Are you sure you want to delete this customer?
          </AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the customer "{customerName}" and all
            associated data. This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            className="bg-destructive text-destructive-foreground"
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
