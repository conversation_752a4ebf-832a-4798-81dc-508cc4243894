"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import {
  User,
  FileText,
  MapPin,
  CreditCard,
  Info,
  Upload,
  Trash2,
  Download,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  type CreateCustomerRequest,
  type UpdateCustomerRequest,
  CustomerDetails,
  CustomerStatus,
  RegistrationMethod,
} from "@/types/customers";
import React from "react";
import { CustomField } from "@/types/custom-fields";
import { useCustomFields } from "@/lib/custom-fields/hooks";
import { CustomFieldsRenderer } from "@/components/custom-fields/custom-fields-renderer";
import { BaseSheet } from "@/components/shared/base-sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { countries } from "@/config/countries";
import { PhoneInputField } from "@/components/register/phone-input";
import { type E164Number } from "libphonenumber-js";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Button } from "@/components/ui/button";
import { useCustomerById } from "@/lib/customers/hooks";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { createCustomer, updateCustomer } from "@/lib/customers/api";
import { createDemoCustomer, updateDemoCustomer } from "@/lib/customers/demo";

// Define the validation schema
const customerSchema = z.object({
  personalInfo: z.object({
    title: z.string().optional(),
    firstName: z.string().optional(),
    middleName: z.string().optional(),
    lastName: z.string().optional(),
    suffix: z.string().optional(),
    customerDisplayName: z.string().min(1, "Name is required"),
    companyName: z.string().optional(),
    customerType: z.enum(["business", "individual"]).optional(),
    country: z.string().optional(),
  }),
  contactInfo: z.object({
    email: z
      .string()
      .email("Invalid email address")
      .optional()
      .or(z.literal("")),
    phoneNumber: z.string().optional(),
    mobileNumber: z.string().optional(),
    fax: z.string().optional(),
    other: z.string().optional(),
    website: z.string().optional(),
  }),
  additionalInfo: z
    .object({
      businessLocation: z.string().optional(),
      customerGroup: z.string().optional(),
      creditPeriod: z
        .number()
        .optional()
        .or(z.literal("").transform(() => undefined)),
      creditLimit: z.string().optional(),
      openingBalance: z.string().optional(),
      taxNumber: z.string().optional(),
      status: z.nativeEnum(CustomerStatus).default(CustomerStatus.ENABLED),
      registrationMethod: z.nativeEnum(RegistrationMethod).optional(),
    })
    .optional(),
  billingAddress: z
    .object({
      billingAttention: z.string().optional(),
      billingCountry: z.string().optional(),
      billingStreet1: z.string().optional(),
      billingStreet2: z.string().optional(),
      billingCity: z.string().optional(),
      billingState: z.string().optional(),
      billingZip: z.string().optional(),
      billingPhone: z.string().optional(),
    })
    .optional(),
  shippingAddress: z
    .object({
      shippingAttention: z.string().optional(),
      shippingCountry: z.string().optional(),
      shippingStreet1: z.string().optional(),
      shippingStreet2: z.string().optional(),
      shippingCity: z.string().optional(),
      shippingState: z.string().optional(),
      shippingZip: z.string().optional(),
      shippingPhone: z.string().optional(),
    })
    .optional(),
  notes: z.string().optional(),
  customFields: z.record(z.string()).optional(),
  documents: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        size: z.number(),
        type: z.string(),
        url: z.string(),
        uploadedAt: z.date(),
      })
    )
    .default([]),
});

type CustomerFormValues = z.infer<typeof customerSchema>;

interface CustomerSheetProps {
  isDemo?: boolean;
  onSuccess?: () => void;
  customerId?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isUpdate?: boolean;
}

export function CustomerSheet({
  isDemo = false,
  onSuccess,
  customerId,
  open,
  onOpenChange,
  isUpdate = false,
}: CustomerSheetProps) {
  const [customFields, setCustomFields] = React.useState<CustomField[]>([]);
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;

  const [sameAsShipping, setSameAsShipping] = React.useState(false);
  // const { data: customerGroups } = useSimpleCustomerGroups(isDemo);
  const { data: customer } = useCustomerById(customerId, isDemo);
  const { data: customFieldsData } = useCustomFields(
    "customers",
    {
      entityType: "customers",
      page: 1,
      limit: 100,
    },
    isDemo
  );

  const [openingBalanceType, setOpeningBalanceType] = React.useState<
    "receive" | "pay"
  >("receive");

  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      personalInfo: {
        title: "",
        firstName: "",
        middleName: "",
        lastName: "",
        suffix: "",
        customerDisplayName: "",
        companyName: "",
        customerType: undefined,
        country: "LK",
      },
      contactInfo: {
        email: "",
        phoneNumber: "",
        mobileNumber: "",
        fax: "",
        other: "",
        website: "",
      },
      additionalInfo: {
        businessLocation: "",
        customerGroup: "",
        creditPeriod: undefined,
        creditLimit: "",
        openingBalance: "",
        taxNumber: "",
        status: CustomerStatus.ENABLED,
        registrationMethod: undefined,
      },
      billingAddress: {
        billingAttention: "",
        billingCountry: "",
        billingStreet1: "",
        billingStreet2: "",
        billingCity: "",
        billingState: "",
        billingZip: "",
        billingPhone: "",
      },
      shippingAddress: {
        shippingAttention: "",
        shippingCountry: "",
        shippingStreet1: "",
        shippingStreet2: "",
        shippingCity: "",
        shippingState: "",
        shippingZip: "",
        shippingPhone: "",
      },
      notes: "",
      customFields: {},
      documents: [],
    },
  });

  // Update custom fields when data is available
  React.useEffect(() => {
    if (customFieldsData?.fields) {
      setCustomFields(customFieldsData.fields);
    }
  }, [customFieldsData]);

  React.useEffect(() => {
    if (customer) {
      // If the customer has a negative opening balance, set type to "pay"
      if (
        customer.data?.additionalInfo?.openingBalance &&
        customer.data?.additionalInfo?.openingBalance.startsWith("-")
      ) {
        setOpeningBalanceType("pay");
      } else {
        setOpeningBalanceType("receive");
      }

      form.reset({
        personalInfo: {
          title: customer.data?.personalInfo.title || "",
          firstName: customer.data?.personalInfo.firstName || "",
          middleName: customer.data?.personalInfo.middleName || "",
          lastName: customer.data?.personalInfo.lastName || "",
          suffix: customer.data?.personalInfo.suffix || "",
          customerDisplayName: customer.data?.personalInfo.customerDisplayName,
          companyName: customer.data?.personalInfo.companyName || "",
          customerType: customer.data?.personalInfo.customerType,
          country: customer.data?.personalInfo.country || "LK",
        },
        contactInfo: {
          email: customer.data?.contactInfo.email || "",
          phoneNumber: customer.data?.contactInfo.phoneNumber || "",
          mobileNumber: customer.data?.contactInfo.mobileNumber || "",
          fax: customer.data?.contactInfo.fax || "",
          other: customer.data?.contactInfo.other || "",
          website: customer.data?.contactInfo.website || "",
        },
        additionalInfo: customer.data?.additionalInfo || {},
        billingAddress: customer.data?.billingAddress || {},
        shippingAddress: customer.data?.shippingAddress || {},
        notes: customer.data?.notes || "",
        customFields: customer.data?.customFields || {},
        documents: customer.data?.documents || [],
      });
    } else {
      form.reset(form.formState.defaultValues!);
      setOpeningBalanceType("receive");
    }
  }, [customer]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  // const hasNameContactErrors = Object.keys(errors).some((key) =>
  //   [
  //     "personalInfo.title",
  //     "personalInfo.firstName",
  //     "personalInfo.middleName",
  //     "personalInfo.lastName",
  //     "personalInfo.suffix",
  //     "personalInfo.customerDisplayName",
  //     "personalInfo.companyName",
  //     "contactInfo.email",
  //     "contactInfo.phoneNumber",
  //     "contactInfo.mobileNumber",
  //     "contactInfo.fax",
  //     "contactInfo.other",
  //     "contactInfo.website",
  //   ].includes(key)
  // );

  // Function to copy billing address to shipping address
  const copyBillingToShipping = React.useCallback(() => {
    const billingValues = {
      country: form.getValues("billingAddress.billingCountry"),
      street1: form.getValues("billingAddress.billingStreet1"),
      street2: form.getValues("billingAddress.billingStreet2"),
      city: form.getValues("billingAddress.billingCity"),
      state: form.getValues("billingAddress.billingState"),
      zip: form.getValues("billingAddress.billingZip"),
      phone: form.getValues("billingAddress.billingPhone"),
    };

    form.setValue("shippingAddress.shippingCountry", billingValues.country);
    form.setValue("shippingAddress.shippingStreet1", billingValues.street1);
    form.setValue("shippingAddress.shippingStreet2", billingValues.street2);
    form.setValue("shippingAddress.shippingCity", billingValues.city);
    form.setValue("shippingAddress.shippingState", billingValues.state);
    form.setValue("shippingAddress.shippingZip", billingValues.zip);
    form.setValue("shippingAddress.shippingPhone", billingValues.phone);
  }, [form]);

  // Watch billing address changes and update shipping if needed
  React.useEffect(() => {
    if (sameAsShipping) {
      const subscription = form.watch((value, { name }) => {
        if (name?.startsWith("billing")) {
          copyBillingToShipping();
        }
      });

      return () => subscription.unsubscribe();
    }
  }, [sameAsShipping, copyBillingToShipping, form]);

  const handleSubmit = async (data: CustomerFormValues) => {
    try {
      if (customerId) {
        // Update existing customer
        const updateData: UpdateCustomerRequest = {
          id: customerId,
          personalInfo: {
            customerDisplayName: data.personalInfo.customerDisplayName,
            title: data.personalInfo.title,
            firstName: data.personalInfo.firstName,
            lastName: data.personalInfo.lastName,
            companyName: data.personalInfo.companyName,
          },
          contactInfo: {
            email: data.contactInfo.email,
            phoneNumber: data.contactInfo.phoneNumber,
            mobileNumber: data.contactInfo.mobileNumber,
            website: data.contactInfo.website,
          },
          additionalInfo: {
            customerGroupId: data.additionalInfo?.customerGroup,
            businessLocationId: data.additionalInfo?.businessLocation,
            creditPeriod: data.additionalInfo?.creditPeriod,
            creditLimit: data.additionalInfo?.creditLimit,
            openingBalance: data.additionalInfo?.openingBalance,
            taxNumber: data.additionalInfo?.taxNumber,
            status: data.additionalInfo?.status,
            registrationMethod: data.additionalInfo?.registrationMethod,
          },
          billingAddress: {
            billingAttention: data.billingAddress?.billingAttention,
            billingCountry: data.billingAddress?.billingCountry,
            billingStreet1: data.billingAddress?.billingStreet1,
            billingStreet2: data.billingAddress?.billingStreet2,
            billingCity: data.billingAddress?.billingCity,
            billingState: data.billingAddress?.billingState,
            billingZip: data.billingAddress?.billingZip,
            billingPhone: data.billingAddress?.billingPhone,
          },
          shippingAddress: {
            shippingAttention: data.shippingAddress?.shippingAttention,
            shippingCountry: data.shippingAddress?.shippingCountry,
            shippingStreet1: data.shippingAddress?.shippingStreet1,
            shippingStreet2: data.shippingAddress?.shippingStreet2,
            shippingCity: data.shippingAddress?.shippingCity,
            shippingState: data.shippingAddress?.shippingState,
            shippingZip: data.shippingAddress?.shippingZip,
            shippingPhone: data.shippingAddress?.shippingPhone,
          },
          customFields: data.customFields,
          documents: data.documents,
          notes: data.notes,
        };

        const result = isDemo
          ? await updateDemoCustomer(customerId, updateData)
          : await updateCustomer(customerId, updateData);

        console.log("Update result:", result);
        if (result.status === ApiStatus.SUCCESS) {
          onOpenChange?.(false);
          onSuccess?.();
          toast.success("Customer updated successfully");
        } else {
          toast.error("Failed to update customer: " + (result.message || "Unknown error"));
          return; // Don't close the sheet if there was an error
        }
      } else {
        const createData: CreateCustomerRequest = {
          personalInfo: {
            customerDisplayName: data.personalInfo.customerDisplayName,
            title: data.personalInfo.title,
            firstName: data.personalInfo.firstName,
            lastName: data.personalInfo.lastName,
            companyName: data.personalInfo.companyName,
          },
          contactInfo: {
            email: data.contactInfo.email,
            phoneNumber: data.contactInfo.phoneNumber,
            mobileNumber: data.contactInfo.mobileNumber,
            website: data.contactInfo.website,
            fax: data.contactInfo.fax,
            other: data.contactInfo.other,
          },
          additionalInfo: {
            customerGroupId: data.additionalInfo?.customerGroup,
            businessLocationId: data.additionalInfo?.businessLocation,
            creditPeriod: data.additionalInfo?.creditPeriod,
            creditLimit: data.additionalInfo?.creditLimit,
            openingBalance: data.additionalInfo?.openingBalance,
            taxNumber: data.additionalInfo?.taxNumber,
            status: data.additionalInfo?.status,
            registrationMethod: data.additionalInfo?.registrationMethod,
          },
          billingAddress: {
            billingAttention: data.billingAddress?.billingAttention,
            billingCountry: data.billingAddress?.billingCountry,
            billingStreet1: data.billingAddress?.billingStreet1,
            billingStreet2: data.billingAddress?.billingStreet2,
            billingCity: data.billingAddress?.billingCity,
            billingState: data.billingAddress?.billingState,
            billingZip: data.billingAddress?.billingZip,
            billingPhone: data.billingAddress?.billingPhone,
          },
        };

        const result = isDemo
          ? await createDemoCustomer(createData)
          : await createCustomer(createData);

        console.log("Create result:", result);

        if (result.status === ApiStatus.SUCCESS) {
          onOpenChange?.(false);
          onSuccess?.();
          toast.success("Customer created successfully");
        } else {
          toast.error("Failed to create customer: " + (result.message || "Unknown error"));
          return; // Don't close the sheet if there was an error
        }
      }

      // Force reset the form
      form.reset();
    } catch (error) {
      console.error("Failed to save customer:", error);
      toast.error("Failed to save customer: " + (error instanceof Error ? error.message : "Unknown error"));
    }
  };

  const sections = [
    {
      id: "name-contact",
      title: "Name and Contact",
      icon: <User className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!(
        errors.personalInfo?.title ||
        errors.personalInfo?.firstName ||
        errors.personalInfo?.middleName ||
        errors.personalInfo?.lastName ||
        errors.personalInfo?.suffix ||
        errors.personalInfo?.customerDisplayName ||
        errors.personalInfo?.companyName ||
        errors.personalInfo?.customerType || // Added for completeness
        errors.personalInfo?.country || // Added for completeness
        errors.contactInfo?.email ||
        errors.contactInfo?.phoneNumber ||
        errors.contactInfo?.mobileNumber ||
        errors.contactInfo?.fax ||
        errors.contactInfo?.other ||
        errors.contactInfo?.website
      ),
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Name and Contact</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Customer Type</Label>
                <RadioGroup
                  defaultValue={
                    form.watch("personalInfo.customerType") || "business"
                  }
                  onValueChange={(value) =>
                    form.setValue(
                      "personalInfo.customerType",
                      value as "business" | "individual"
                    )
                  }
                  className="flex items-center gap-4 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="business" id="business" />
                    <Label htmlFor="business">Business</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="individual" id="individual" />
                    <Label htmlFor="individual">Individual</Label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>Primary Contact</Label>
                <div className="grid grid-cols-12 gap-3 mt-2">
                  <div className="col-span-2">
                    <Select
                      onValueChange={(value) =>
                        form.setValue("personalInfo.title", value)
                      }
                      value={form.watch("personalInfo.title")}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Salutation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mr">Mr.</SelectItem>
                        <SelectItem value="mrs">Mrs.</SelectItem>
                        <SelectItem value="ms">Ms.</SelectItem>
                        <SelectItem value="dr">Dr.</SelectItem>
                        <SelectItem value="prof">Prof.</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-5">
                    <Input
                      {...form.register("personalInfo.firstName")}
                      placeholder="First Name"
                    />
                  </div>
                  <div className="col-span-5">
                    <Input
                      {...form.register("personalInfo.lastName")}
                      placeholder="Last Name"
                    />
                  </div>
                </div>
              </div>

              <div>
                <Label>Company Name</Label>
                <Input
                  {...form.register("personalInfo.companyName")}
                  placeholder="Company name"
                  className="mt-2"
                />
              </div>

              <div>
                <Label className="flex items-center gap-1">
                  Display Name
                  <span className="text-destructive">*</span>
                </Label>
                <Input
                  {...form.register("personalInfo.customerDisplayName")}
                  placeholder="Select or type to add"
                  className={cn(
                    "mt-2",
                    errors.personalInfo?.customerDisplayName &&
                      "border-destructive"
                  )}
                />
                {errors.personalInfo?.customerDisplayName && (
                  <p className="text-sm text-destructive mt-1">
                    Enter the Display Name of your customer.
                  </p>
                )}
              </div>

              <div>
                <Label>Email Address</Label>
                <Input
                  {...form.register("contactInfo.email")}
                  type="email"
                  placeholder="Email"
                  className={cn(
                    "mt-2",
                    errors.contactInfo?.email && "border-destructive"
                  )}
                />
                {errors.contactInfo?.email && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.contactInfo?.email.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <Label>Work Phone</Label>
                  <div className="mt-2">
                    <PhoneInputField
                      value={
                        form.watch("contactInfo.phoneNumber") as
                          | E164Number
                          | undefined
                      }
                      onChange={(value) =>
                        form.setValue("contactInfo.phoneNumber", value || "")
                      }
                      error={errors.contactInfo?.phoneNumber?.message}
                    />
                  </div>
                </div>
                <div>
                  <Label>Mobile</Label>
                  <div className="mt-2">
                    <PhoneInputField
                      value={
                        form.watch("contactInfo.mobileNumber") as
                          | E164Number
                          | undefined
                      }
                      onChange={(value) =>
                        form.setValue("contactInfo.mobileNumber", value || "")
                      }
                      error={errors.contactInfo?.mobileNumber?.message}
                    />
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "addresses",
      title: "Addresses",
      icon: <MapPin className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!(
        errors.billingAddress?.billingAttention ||
        errors.billingAddress?.billingCountry ||
        errors.billingAddress?.billingStreet1 ||
        errors.billingAddress?.billingStreet2 ||
        errors.billingAddress?.billingCity ||
        errors.billingAddress?.billingState ||
        errors.billingAddress?.billingZip ||
        errors.billingAddress?.billingPhone ||
        errors.shippingAddress?.shippingAttention ||
        errors.shippingAddress?.shippingCountry ||
        errors.shippingAddress?.shippingStreet1 ||
        errors.shippingAddress?.shippingStreet2 ||
        errors.shippingAddress?.shippingCity ||
        errors.shippingAddress?.shippingState ||
        errors.shippingAddress?.shippingZip ||
        errors.shippingAddress?.shippingPhone
      ),
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Addresses</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Billing Address */}
              <div className="space-y-4">
                <div>
                  <Label>Billing Address</Label>
                  <div className="mt-2">
                    <Select
                      onValueChange={(value) =>
                        form.setValue("billingAddress.billingCountry", value)
                      }
                      value={form.watch("billingAddress.billingCountry")}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country.code} value={country.code}>
                            {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Input
                    {...form.register("billingAddress.billingStreet1")}
                    placeholder="Street 1"
                    className="mt-2"
                  />
                  <Input
                    {...form.register("billingAddress.billingStreet2")}
                    placeholder="Street 2"
                    className="mt-2"
                  />
                  <Input
                    {...form.register("billingAddress.billingCity")}
                    placeholder="City"
                    className="mt-2"
                  />
                  <Input
                    {...form.register("billingAddress.billingState")}
                    placeholder="State / Province"
                    className="mt-2"
                  />
                  <Input
                    {...form.register("billingAddress.billingZip")}
                    placeholder="ZIP Code"
                    className="mt-2"
                  />
                  <div className="mt-2">
                    <PhoneInputField
                      value={
                        form.watch("billingAddress.billingPhone") as
                          | E164Number
                          | undefined
                      }
                      onChange={(value) =>
                        form.setValue(
                          "billingAddress.billingPhone",
                          value || ""
                        )
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Shipping Address */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Shipping Address</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="shipping"
                      checked={sameAsShipping}
                      onCheckedChange={(checked) => {
                        setSameAsShipping(checked === true);
                        if (checked) {
                          copyBillingToShipping();
                        }
                      }}
                    />
                    <label
                      htmlFor="shipping"
                      className="text-sm text-muted-foreground"
                    >
                      Copy billing address
                    </label>
                  </div>
                </div>
                <div>
                  <div className="mt-2">
                    <Select
                      onValueChange={(value) =>
                        form.setValue("shippingAddress.shippingCountry", value)
                      }
                      value={form.watch("shippingAddress.shippingCountry")}
                      disabled={sameAsShipping}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country.code} value={country.code}>
                            {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Input
                    {...form.register("shippingAddress.shippingStreet1")}
                    placeholder="Street 1"
                    className="mt-2"
                    disabled={sameAsShipping}
                  />
                  <Input
                    {...form.register("shippingAddress.shippingStreet2")}
                    placeholder="Street 2"
                    className="mt-2"
                    disabled={sameAsShipping}
                  />
                  <Input
                    {...form.register("shippingAddress.shippingCity")}
                    placeholder="City"
                    className="mt-2"
                    disabled={sameAsShipping}
                  />
                  <Input
                    {...form.register("shippingAddress.shippingState")}
                    placeholder="State / Province"
                    className="mt-2"
                    disabled={sameAsShipping}
                  />
                  <Input
                    {...form.register("shippingAddress.shippingZip")}
                    placeholder="ZIP Code"
                    className="mt-2"
                    disabled={sameAsShipping}
                  />
                  <div className="mt-2">
                    <PhoneInputField
                      value={
                        form.watch("shippingAddress.shippingPhone") as
                          | E164Number
                          | undefined
                      }
                      onChange={(value) =>
                        form.setValue(
                          "shippingAddress.shippingPhone",
                          value || ""
                        )
                      }
                      disabled={sameAsShipping}
                    />
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "additional",
      title: "Additional Info",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!(
        errors.additionalInfo?.businessLocation ||
        errors.additionalInfo?.customerGroup ||
        errors.additionalInfo?.status ||
        errors.additionalInfo?.registrationMethod
      ),
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Info</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              {/* <div>
                <Label>Customer Group</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("additionalInfo.customerGroup", value)
                  }
                  value={form.watch("additionalInfo.customerGroup")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select customer group" />
                  </SelectTrigger>
                  <SelectContent>
                    {(customerGroups || []).map((group) => (
                      <SelectItem key={group.id} value={group.id}>
                        {group.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div> */}
              <div>
                <Label>Status</Label>
                <RadioGroup
                  defaultValue={
                    form.watch("additionalInfo.status") ||
                    CustomerStatus.ENABLED
                  }
                  onValueChange={(value) =>
                    form.setValue(
                      "additionalInfo.status",
                      value as CustomerStatus
                    )
                  }
                  className="flex items-center gap-4 mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value={CustomerStatus.ENABLED}
                      id="enabled"
                    />
                    <Label htmlFor="enabled" className="cursor-pointer">
                      Enabled
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value={CustomerStatus.DISABLED}
                      id="disabled"
                    />
                    <Label htmlFor="disabled" className="cursor-pointer">
                      Disabled
                    </Label>
                  </div>
                </RadioGroup>
              </div>
              <div>
                <Label>Registration Method</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue(
                      "additionalInfo.registrationMethod",
                      value as RegistrationMethod
                    )
                  }
                  value={form.watch("additionalInfo.registrationMethod")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select registration method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={RegistrationMethod.IN_STORE}>
                      In Store
                    </SelectItem>
                    <SelectItem value={RegistrationMethod.ONLINE}>
                      Online
                    </SelectItem>
                    <SelectItem value={RegistrationMethod.PHONE}>
                      Phone
                    </SelectItem>
                    <SelectItem value={RegistrationMethod.REFERRAL}>
                      Referral
                    </SelectItem>
                    <SelectItem value={RegistrationMethod.OTHER}>
                      Other
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* <div>
                <Label>Business Location</Label>
                <Select
                  onValueChange={(value) =>
                    form.setValue("additionalInfo.businessLocation", value)
                  }
                  value={form.watch("additionalInfo.businessLocation")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select business location" />
                  </SelectTrigger>
                  <SelectContent>
                    {(businessLocations || []).map((location) => (
                      <SelectItem key={location._id} value={location._id}>
                        {location.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div> */}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "payment-info",
      title: "Payment Information",
      icon: <CreditCard className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!(
        errors.additionalInfo?.creditPeriod ||
        errors.additionalInfo?.creditLimit ||
        errors.additionalInfo?.openingBalance ||
        errors.additionalInfo?.taxNumber
      ),
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Payment Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Credit Period</Label>
                <div className="relative mt-2">
                  <Input
                    {...form.register("additionalInfo.creditPeriod")}
                    placeholder="30"
                    type="number"
                    min="0"
                    className="pr-16"
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                    Day(s)
                  </span>
                </div>
              </div>
              <div>
                <Label>Credit Limit</Label>
                <div className="relative mt-2">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                    LKR
                  </span>
                  <Input
                    {...form.register("additionalInfo.creditLimit")}
                    placeholder="0.00"
                    className="pl-12"
                    type="number"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
              <div>
                <Label>Opening Balance</Label>
                <div className="flex gap-2 mt-2">
                  <div className="relative flex-1">
                    <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                      LKR
                    </span>
                    <Input
                      value={
                        form
                          .watch("additionalInfo.openingBalance")
                          ?.replace(/^-/, "") || ""
                      }
                      onChange={(e) => {
                        const value = e.target.value;
                        if (value === "") {
                          form.setValue("additionalInfo.openingBalance", "");
                        } else {
                          form.setValue(
                            "additionalInfo.openingBalance",
                            openingBalanceType === "pay"
                              ? `-${value.replace(/^-/, "")}`
                              : value.replace(/^-/, "")
                          );
                        }
                      }}
                      placeholder="0.00"
                      className="pl-12"
                      type="number"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div className="w-[120px]">
                    <Select
                      value={openingBalanceType}
                      onValueChange={(value: "receive" | "pay") => {
                        setOpeningBalanceType(value);
                        const currentValue = form.getValues(
                          "additionalInfo.openingBalance"
                        );
                        if (currentValue) {
                          const absValue = currentValue.replace(/^-/, "");
                          form.setValue(
                            "additionalInfo.openingBalance",
                            value === "pay" ? `-${absValue}` : absValue
                          );
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="receive">Receive</SelectItem>
                        <SelectItem value="pay">Pay</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <div>
                <Label>Tax Number</Label>
                <Input
                  {...form.register("additionalInfo.taxNumber")}
                  placeholder="Tax Registration Number"
                  className="mt-2"
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "notes",
      title: "Notes",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!errors.notes,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Notes</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <Textarea
                {...form.register("notes")}
                placeholder="Add notes here..."
              />
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "documents",
      title: "Documents",
      icon: <Upload className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!errors.documents, // This will catch array-level errors if any
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Upload className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Documents</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Attached Documents</Label>
                <div>
                  <input
                    type="file"
                    id="file-upload"
                    className="hidden"
                    multiple
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      // Handle file upload here
                      files.forEach(async (file) => {
                        try {
                          // In a real implementation, you would upload to your server/storage
                          const fakeUrl = URL.createObjectURL(file);
                          const newDoc = {
                            id: Math.random().toString(36).substr(2, 9),
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            url: fakeUrl,
                            uploadedAt: new Date(),
                          };

                          const currentDocs = form.getValues("documents") || [];
                          form.setValue("documents", [...currentDocs, newDoc]);

                          toast.success(`File ${file.name} uploaded successfully`);
                        } catch (error) {
                          console.error("Error uploading file:", error);
                          toast.error(`Failed to upload ${file.name}: ` + (error instanceof Error ? error.message : "Unknown error"));
                        }
                      });
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      document.getElementById("file-upload")?.click();
                    }}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Documents
                  </Button>
                </div>
              </div>

              {/* Display uploaded documents */}
              <div className="space-y-2">
                {form.watch("documents")?.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between p-3 border rounded-md"
                  >
                    <div className="flex items-center space-x-3">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{doc.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {(doc.size / 1024 / 1024).toFixed(2)} MB ·{" "}
                          {new Date(doc.uploadedAt).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="text-primary hover:text-primary hover:bg-primary/10"
                        onClick={() => {
                          const a = document.createElement("a");
                          a.href = doc.url;
                          a.download = doc.name;
                          document.body.appendChild(a);
                          a.click();
                          document.body.removeChild(a);
                          toast.success(`Downloading ${doc.name}`);
                        }}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => {
                          const currentDocs = form.getValues("documents") || [];
                          form.setValue(
                            "documents",
                            currentDocs.filter((d) => d.id !== doc.id)
                          );
                          toast.success(`File ${doc.name} removed`);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    // Only include Custom Fields section if there are custom fields
    ...(customFields.length > 0
      ? [
          {
            id: "custom-fields",
            title: "Custom Fields",
            icon: <FileText className="h-5 w-5 text-muted-foreground" />,
            hasErrors: !!errors.customFields, // This will catch errors if the customFields object or any of its properties have errors
            content: (
              <>
                <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">Custom Fields</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-6 pt-2">
                  <CustomFieldsRenderer fields={customFields} form={form} />
                </AccordionContent>
              </>
            ),
          },
        ]
      : []),
  ];

  return (
    <BaseSheet<CustomerDetails, CustomerFormValues>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={customer?.data}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Customer"
      sections={sections}
      onSubmit={form.handleSubmit(handleSubmit)}
      formRef={formRef}
    />
  );
}
