"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, GripVertical, Eye, Globe, MapPin, Users } from "lucide-react";
import { ServiceTableData, ServiceStatus } from "@/types/service";
import { ServiceStatusBadge } from "./service-status-badge";
import { ServiceAvailableOnlineBadge } from "./service-available-online-badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SortableDragHandle } from "@/components/ui/sortable";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  isSortableEnabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (serviceId: string, newStatus: ServiceStatus) => void;
  onAvailableOnlineUpdate?: (
    serviceId: string,
    newAvailableOnline: boolean
  ) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  isSortableEnabled = false,
  onRefresh,
  onStatusUpdate,
  onAvailableOnlineUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<ServiceTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10, // Set a fixed small width for the checkbox column
    },
    {
      id: "drag-handle",
      header: () => <div className="w-8"></div>,
      cell: ({ row }) => {
        if (!isSortableEnabled) {
          return (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 cursor-not-allowed opacity-50"
              disabled={true}
            >
              <GripVertical className="h-4 w-4 text-muted-foreground" />
              <span className="sr-only">Drag handle (disabled)</span>
            </Button>
          );
        }

        return (
          <SortableDragHandle
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            disabled={isActionsDisabled}
          >
            <GripVertical className="h-4 w-4 text-muted-foreground" />
            <span className="sr-only">Drag to reorder</span>
          </SortableDragHandle>
        );
      },
      enableSorting: false,
      enableHiding: false,
      size: 32, // Fixed width for drag handle
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Service Name" />
      ),
      cell: ({ row }) => {
        const imageUrl = row.original.images?.[0];
        const serviceName = row.getValue("name") as string;
        const sku = row.original.sku;

        return (
          <div className="flex items-center gap-3 pl-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={imageUrl} alt={serviceName} />
              <AvatarFallback className="text-xs">
                {serviceName.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <div className="font-medium">{serviceName}</div>
              {sku && (
                <div className="text-xs text-muted-foreground">{sku}</div>
              )}
            </div>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "serviceCategoryName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Service Category" />
      ),
      cell: ({ row }) => {
        const serviceCategoryName = row.getValue("serviceCategoryName") as string;
        const serviceSubCategoryName = row.original.serviceSubCategoryName;
        return (
          <div className="pl-2">
            <span className="text-sm">{serviceCategoryName}</span>
            {serviceSubCategoryName && (
              <span className="text-xs text-muted-foreground block">
                {serviceSubCategoryName}
              </span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "priceRate",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price Rate" />
      ),
      cell: ({ row }) => {
        const priceRate = row.getValue("priceRate") as string;
        return (
          <div className="pl-2">
            <span className="font-medium">${priceRate}</span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "status",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const status = row.getValue("status") as ServiceStatus;
        return (
          <ServiceStatusBadge
            serviceId={row.original.id}
            status={status}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "availableOnline",
      header: () => <div className="font-medium">Available Online</div>,
      cell: ({ row }) => {
        return (
          <ServiceAvailableOnlineBadge
            serviceId={row.original.id}
            availableOnline={row.original.availableOnline}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onAvailableOnlineUpdate={onAvailableOnlineUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "isAllocatedToAllLocations",
      header: () => <div className="font-medium">Location Allocation</div>,
      cell: ({ row }) => {
        const isAllocatedToAllLocations = row.getValue(
          "isAllocatedToAllLocations"
        ) as boolean;
        const locations = row.original.locations || [];

        return (
          <div className="pl-2">
            {isAllocatedToAllLocations ? (
              <div className="flex items-center gap-1">
                <Globe className="h-3 w-3 text-blue-600" />
                <span className="text-xs text-blue-600 font-medium">
                  All Locations
                </span>
              </div>
            ) : (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3 text-orange-600" />
                <span className="text-xs text-orange-600 font-medium">
                  {locations.length} Location{locations.length !== 1 ? "s" : ""}
                </span>
              </div>
            )}
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      accessorKey: "staffMembers",
      header: () => <div className="font-medium">Staff Assignment</div>,
      cell: ({ row }) => {
        const staffMembers = row.original.staffMembers || [];

        return (
          <div className="pl-2">
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3 text-purple-600" />
              <span className="text-xs text-purple-600 font-medium">
                {staffMembers.length} Staff{staffMembers.length !== 1 ? "" : ""}
              </span>
            </div>
          </div>
        );
      },
      enableSorting: false,
      enableHiding: true,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}
