"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Image as ImageIcon,
  DollarSign,
  Check,
  X,
  Loader2,
  Users,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { serviceFormSchema } from "@/lib/services/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ServiceTableData,
  ServiceStatus,
  UpdateServiceDto,
} from "@/types/service";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useService,
  useServiceNameAvailability,
  useServiceSkuAvailability,
  useCreateService,
  useUpdateService,
} from "@/lib/services/hooks";
import { useStaffMembersSlim } from "@/lib/staff/hooks";
import { useServiceCategoriesWithNested } from "@/lib/service-categories/hooks";
import { ServiceCategorySlimDto } from "@/types/service-category";
import { useEffect } from "react";

interface ServiceSheetProps {
  service: ServiceTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (service?: ServiceTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof serviceFormSchema>;

export function ServiceSheet({
  service,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ServiceSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [ogImageFiles, setOgImageFiles] = React.useState<UploadedFile[]>([]);
  const [keywords, setKeywords] = React.useState<string>("");

  // Debounced values for availability checking
  const [debouncedName, setDebouncedName] = React.useState<string>("");
  const [debouncedSku, setDebouncedSku] = React.useState<string>("");

  // Fetch complete service data if updating
  const { data: fullServiceResponse, isLoading: isLoadingService } = useService(
    service?.id || "",
    isDemo
  );
  const fullService = fullServiceResponse?.data;

  useEffect(() => {
    if (fullServiceResponse) {
      console.log("fullServiceResponse", fullServiceResponse);
    }
  }, [fullServiceResponse]);

  // Mutation hooks for create and update operations
  const createServiceMutation = useCreateService(isDemo);
  const updateServiceMutation = useUpdateService(isDemo);

  // Fetch staff members for dropdown
  const { data: staffMembersResponse } = useStaffMembersSlim(isDemo);
  const staffMembers = staffMembersResponse?.data || [];

  // Fetch service categories for dropdown with nested structure
  const { data: serviceCategoriesResponse } = useServiceCategoriesWithNested(isDemo);
  const serviceCategories = React.useMemo(
    () => serviceCategoriesResponse?.data || [],
    [serviceCategoriesResponse?.data]
  );

  // Helper function to flatten service categories into a single list for selection
  const flattenServiceCategories = (
    categories: ServiceCategorySlimDto[]
  ): ServiceCategorySlimDto[] => {
    const flattened: ServiceCategorySlimDto[] = [];

    const addCategory = (cat: ServiceCategorySlimDto) => {
      flattened.push(cat);
      if (cat.subServiceCategories && cat.subServiceCategories.length > 0) {
        cat.subServiceCategories.forEach(addCategory);
      }
    };

    categories.forEach(addCategory);
    return flattened;
  };

  const flatServiceCategories = React.useMemo(
    () => flattenServiceCategories(serviceCategories),
    [serviceCategories]
  );

  // Get parent categories (root categories only) for the main category dropdown
  const parentServiceCategories = React.useMemo(
    () => serviceCategories.filter(cat => cat.id && !cat.parentId),
    [serviceCategories]
  );

  // Availability checks (only check if not updating the same service)
  const shouldCheckNameAvailability =
    debouncedName.length > 0 &&
    (!isUpdate ||
      (service && debouncedName.toLowerCase() !== service.name.toLowerCase()));
  const shouldCheckSkuAvailability =
    debouncedSku.length > 0 &&
    (!isUpdate ||
      (service && debouncedSku.toLowerCase() !== service.sku?.toLowerCase()));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useServiceNameAvailability(debouncedName, isDemo);
  const { data: skuAvailabilityResponse, isLoading: isCheckingSku } =
    useServiceSkuAvailability(debouncedSku, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isSkuAvailable = shouldCheckSkuAvailability
    ? skuAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(serviceFormSchema),
    defaultValues: {
      name: "",
      sku: "",
      serviceCategoryId: "",
      serviceSubCategoryId: "",
      description: "",
      priceRate: "",
      incomeAccountId: "",
      salesTaxId: "",
      sellToCustomers: true,
      purchaseFromSupplier: false,
      purchaseDescription: "",
      purchaseCost: "",
      expenseAccountId: "",
      preferredSupplierId: "",
      availableOnline: true,
      // Location fields
      isAllocatedToAllLocations: false,
      locationIds: [],
      // Staff assignment
      staffMemberIds: [],
      // SEO fields
      seoTitle: "",
      seoDescription: "",
      seoKeywords: [],
    },
  });

  // Get subcategories based on selected category (after form is initialized)
  const watchedCategoryId = form.watch("serviceCategoryId");
  const selectedCategory = React.useMemo(() => {
    return serviceCategories.find(cat => cat.id === watchedCategoryId);
  }, [serviceCategories, watchedCategoryId]);

  const availableSubCategories = React.useMemo(() => {
    return selectedCategory?.subServiceCategories || [];
  }, [selectedCategory]);

  // Get the selected image files for API calls (only if they're real files, not mock ones for existing images)
  const selectedImages =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? (uploadedFiles.map((file) => file.file).filter(Boolean) as File[])
      : [];

  // Get the selected OG image file for API calls
  const selectedOgImage =
    ogImageFiles.length > 0 && !ogImageFiles[0].id.startsWith("existing-og-")
      ? ogImageFiles[0].file
      : null;

  // Helper function to parse keywords
  const parseKeywords = (keywordsString: string): string[] => {
    return keywordsString
      .split(",")
      .map((k) => k.trim())
      .filter((k) => k.length > 0);
  };

  // Helper function to get staff member name by ID
  const getStaffMemberName = (staffId: string): string => {
    const staffMember = staffMembers.find((staff) => staff.id === staffId);
    return staffMember ? staffMember.displayName : `Staff Member ${staffId.slice(-8)}`;
  };

  React.useEffect(() => {
    if (service && fullService) {
      // Use full service data for populating the form
      form.reset({
        name: fullService.name,
        sku: fullService.sku || "",
        serviceCategoryId: fullService.serviceCategoryId,
        serviceSubCategoryId: fullService.serviceSubCategoryId || "",
        description: fullService.description || "",
        priceRate: fullService.priceRate,
        incomeAccountId: fullService.incomeAccountId,
        salesTaxId: fullService.salesTaxId,
        sellToCustomers: fullService.sellToCustomers,
        purchaseFromSupplier: fullService.purchaseFromSupplier,
        purchaseDescription: fullService.purchaseDescription || "",
        purchaseCost: fullService.purchaseCost || "",
        expenseAccountId: fullService.expenseAccountId || "",
        preferredSupplierId: fullService.preferredSupplierId || "",
        availableOnline: fullService.availableOnline,
        // Location fields
        isAllocatedToAllLocations:
          fullService.isAllocatedToAllLocations || false,
        locationIds: fullService.locations?.map((loc) => loc.id) || [],
        // Staff assignment
        staffMemberIds:
          fullService.staffMembers?.map((staff) => staff.id) || [],
        // SEO fields
        seoTitle: fullService.seoTitle || "",
        seoDescription: fullService.seoDescription || "",
        seoKeywords: fullService.seoKeywords || [],
      });

      // Set keywords state for UI
      setKeywords(fullService.seoKeywords?.join(", ") || "");

      // Set existing images as uploaded files for preview
      if (fullService.images && fullService.images.length > 0) {
        const existingImages = fullService.images.map((imageUrl, index) => ({
          id: `existing-${fullService.id}-${index}`,
          file: new File([], fullService.name + `-image-${index}`, {
            type: "image/jpeg",
          }),
          preview: imageUrl,
        }));
        setUploadedFiles(existingImages);
      } else {
        setUploadedFiles([]);
      }

      // Set existing OG image as uploaded file for preview
      if (fullService.ogImage) {
        setOgImageFiles([
          {
            id: `existing-og-${fullService.id}`,
            file: new File([], fullService.name + "-og-image", {
              type: "image/jpeg",
            }),
            preview: fullService.ogImage,
          },
        ]);
      } else {
        setOgImageFiles([]);
      }
    } else if (!service) {
      // Reset form for new service
      form.reset({
        name: "",
        sku: "",
        serviceCategoryId: "",
        serviceSubCategoryId: "",
        description: "",
        priceRate: "",
        incomeAccountId: "",
        salesTaxId: "",
        sellToCustomers: true,
        purchaseFromSupplier: false,
        purchaseDescription: "",
        purchaseCost: "",
        expenseAccountId: "",
        preferredSupplierId: "",
        availableOnline: true,
        // Location fields
        isAllocatedToAllLocations: false,
        locationIds: [],
        // Staff assignment
        staffMemberIds: [],
        // SEO fields
        seoTitle: "",
        seoDescription: "",
        seoKeywords: [],
      });
      setKeywords("");
      setUploadedFiles([]);
      setOgImageFiles([]);
    }
  }, [service, fullService]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setOgImageFiles([]);
      setKeywords("");
    }
  }, [open]);

  // Reset subcategory when category changes
  React.useEffect(() => {
    const currentSubCategoryId = form.getValues("serviceSubCategoryId");
    
    if (watchedCategoryId && currentSubCategoryId) {
      const category = serviceCategories.find(cat => cat.id === watchedCategoryId);
      const hasSubCategory = category?.subServiceCategories?.some(sub => sub.id === currentSubCategoryId);
      
      if (!hasSubCategory) {
        form.setValue("serviceSubCategoryId", "", { shouldValidate: true });
      }
    }
  }, [watchedCategoryId, serviceCategories, form]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.name ||
    errors.sku ||
    errors.serviceCategoryId ||
    errors.serviceSubCategoryId ||
    errors.priceRate ||
    errors.incomeAccountId ||
    errors.salesTaxId ||
    errors.availableOnline ||
    (shouldCheckNameAvailability && isNameAvailable === false) ||
    (shouldCheckSkuAvailability && isSkuAvailable === false)
  );

  const hasPurchaseInfoErrors = !!(
    errors.sellToCustomers ||
    errors.purchaseFromSupplier ||
    errors.purchaseDescription ||
    errors.purchaseCost ||
    errors.expenseAccountId ||
    errors.preferredSupplierId
  );

  const hasDetailsErrors = !!errors.description;

  const hasStaffErrors = !!errors.staffMemberIds;

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error(
        "Service name is already taken. Please choose a different name."
      );
      return;
    }

    if (shouldCheckSkuAvailability && isSkuAvailable === false) {
      toast.error(
        "Service SKU is already taken. Please choose a different SKU."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (service) {
        // Update existing service
        const updateData: UpdateServiceDto = {
          name: data.name,
          sku: data.sku || undefined,
          serviceCategoryId: data.serviceCategoryId,
          serviceSubCategoryId: data.serviceSubCategoryId || undefined,
          description: data.description || undefined,
          priceRate: data.priceRate,
          incomeAccountId: data.incomeAccountId,
          salesTaxId: data.salesTaxId,
          sellToCustomers: data.sellToCustomers,
          purchaseFromSupplier: data.purchaseFromSupplier,
          purchaseDescription: data.purchaseDescription || undefined,
          purchaseCost: data.purchaseCost || undefined,
          expenseAccountId: data.expenseAccountId || undefined,
          preferredSupplierId: data.preferredSupplierId || undefined,
          availableOnline: data.availableOnline,
          // Location fields
          isAllocatedToAllLocations: data.isAllocatedToAllLocations,
          locationIds:
            data.isAllocatedToAllLocations ||
            !data.locationIds ||
            data.locationIds.length === 0
              ? undefined
              : data.locationIds,
          // Staff assignment
          staffMemberIds: data.staffMemberIds || [],
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await updateServiceMutation.mutateAsync({
          id: service.id,
          data: updateData,
          images: selectedImages.length > 0 ? selectedImages : undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ServiceTableData);
          return;
        }
        toast.error(response.message || "Failed to update service");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new service
        const createData = {
          name: data.name,
          sku: data.sku || undefined,
          serviceCategoryId: data.serviceCategoryId,
          serviceSubCategoryId: data.serviceSubCategoryId || undefined,
          description: data.description || undefined,
          priceRate: data.priceRate,
          incomeAccountId: data.incomeAccountId,
          salesTaxId: data.salesTaxId,
          sellToCustomers: data.sellToCustomers,
          purchaseFromSupplier: data.purchaseFromSupplier,
          purchaseDescription: data.purchaseDescription || undefined,
          purchaseCost: data.purchaseCost || undefined,
          expenseAccountId: data.expenseAccountId || undefined,
          preferredSupplierId: data.preferredSupplierId || undefined,
          availableOnline: data.availableOnline,
          // Location fields
          isAllocatedToAllLocations: data.isAllocatedToAllLocations,
          locationIds:
            data.isAllocatedToAllLocations ||
            !data.locationIds ||
            data.locationIds.length === 0
              ? undefined
              : data.locationIds,
          // Staff assignment
          staffMemberIds: data.staffMemberIds || [],
          // SEO fields
          seoTitle: data.seoTitle || undefined,
          seoDescription: data.seoDescription || undefined,
          seoKeywords: parseKeywords(keywords),
        };

        const response = await createServiceMutation.mutateAsync({
          data: createData,
          images: selectedImages.length > 0 ? selectedImages : undefined,
          ogImage: selectedOgImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as ServiceTableData);
          return;
        }
        toast.error(response.message || "Failed to create service");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save service:", error);
      toast.error("Failed to save service");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debounce form values for availability checking
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      const timer = setTimeout(() => {
        if (value.name !== undefined) {
          setDebouncedName(value.name || "");
        }
        if (value.sku !== undefined) {
          setDebouncedSku(value.sku || "");
        }
      }, 500); // 500ms debounce

      return () => clearTimeout(timer);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Helper component for availability indicators
  const AvailabilityIndicator = ({
    isLoading,
    isAvailable,
    shouldCheck,
    fieldName,
  }: {
    isLoading: boolean;
    isAvailable: boolean | undefined | null;
    shouldCheck: boolean | null;
    fieldName: string;
  }) => {
    if (!shouldCheck) return null;

    if (isLoading) {
      return (
        <div className="flex items-center gap-1 text-muted-foreground text-sm mt-1">
          <Loader2 className="h-3 w-3 animate-spin" />
          <span>Checking availability...</span>
        </div>
      );
    }

    if (isAvailable === true) {
      return (
        <div className="flex items-center gap-1 text-green-600 text-sm mt-1">
          <Check className="h-3 w-3" />
          <span>{fieldName} is available</span>
        </div>
      );
    }

    if (isAvailable === false) {
      return (
        <div className="flex items-center gap-1 text-red-600 text-sm mt-1">
          <X className="h-3 w-3" />
          <span>{fieldName} is already taken</span>
        </div>
      );
    }

    return null;
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Name</Label>
                <Input
                  {...form.register("name")}
                  name="name"
                  placeholder="Enter service name (e.g., Web Development, Consulting)"
                  className={cn(
                    errors.name && "border-destructive",
                    shouldCheckNameAvailability &&
                      isNameAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.name && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.name.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingName}
                  isAvailable={isNameAvailable}
                  shouldCheck={shouldCheckNameAvailability}
                  fieldName="Name"
                />
              </div>

              <div>
                <Label>SKU</Label>
                <Input
                  {...form.register("sku")}
                  name="sku"
                  placeholder="Enter SKU (e.g., WEB-DEV, CONSULT)"
                  className={cn(
                    errors.sku && "border-destructive",
                    shouldCheckSkuAvailability &&
                      isSkuAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.sku && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.sku.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingSku}
                  isAvailable={isSkuAvailable}
                  shouldCheck={shouldCheckSkuAvailability}
                  fieldName="SKU"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Optional unique identifier for the service
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Service Category</Label>
                  <Select
                    value={form.watch("serviceCategoryId")}
                    onValueChange={(value) =>
                      form.setValue("serviceCategoryId", value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting}
                  >
                    <SelectTrigger
                      className={cn(errors.serviceCategoryId && "border-destructive")}
                    >
                      <SelectValue placeholder="Select service category" />
                    </SelectTrigger>
                    <SelectContent>
                      {parentServiceCategories.length === 0 ? (
                        <SelectItem value="no-categories" disabled>
                          No service categories available
                        </SelectItem>
                      ) : (
                        parentServiceCategories.map((category) => (
                          <SelectItem key={category.id} value={category.id!}>
                            {category.name}
                            {category.subServiceCategories &&
                              category.subServiceCategories.length > 0 && (
                                <span className="text-muted-foreground ml-2">
                                  ({category.subServiceCategories.length} subcategories)
                                </span>
                              )}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.serviceCategoryId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.serviceCategoryId.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Service Sub Category</Label>
                  <Select
                    value={form.watch("serviceSubCategoryId")}
                    onValueChange={(value) =>
                      form.setValue("serviceSubCategoryId", value === "none" ? "" : value, {
                        shouldValidate: true,
                      })
                    }
                    disabled={isSubmitting || !form.watch("serviceCategoryId")}
                  >
                    <SelectTrigger
                      className={cn(errors.serviceSubCategoryId && "border-destructive")}
                    >
                      <SelectValue placeholder={
                        !form.watch("serviceCategoryId") 
                          ? "Select category first" 
                          : "Select service sub category"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">
                        No Sub Category
                      </SelectItem>
                      {availableSubCategories.length === 0 ? (
                        <SelectItem value="no-subcategories" disabled>
                          No subcategories available
                        </SelectItem>
                      ) : (
                        availableSubCategories.map((subCategory) => (
                          <SelectItem key={subCategory.id} value={subCategory.id!}>
                            {subCategory.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {errors.serviceSubCategoryId && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.serviceSubCategoryId.message}
                    </p>
                  )}
                  <p className="text-sm text-muted-foreground mt-1">
                    Optional: Choose a subcategory to further classify the service
                  </p>
                </div>
              </div>

              <div>
                <Label>Price Rate</Label>
                <Input
                  {...form.register("priceRate")}
                  name="priceRate"
                  type="number"
                  step="0.01"
                  placeholder="Enter price rate (e.g., 100.00)"
                  className={cn(errors.priceRate && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.priceRate && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.priceRate.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  The rate charged for this service
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Available Online</Label>
                  <p className="text-sm text-muted-foreground">
                    Make this service available in the online store
                  </p>
                </div>
                <Switch
                  checked={form.watch("availableOnline")}
                  onCheckedChange={(checked) =>
                    form.setValue("availableOnline", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.availableOnline && (
                <p className="text-sm text-destructive mt-1">
                  {errors.availableOnline.message}
                </p>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "financial-info",
      title: "Financial Information",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPurchaseInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Financial Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Income Account</Label>
                <Select
                  value={form.watch("incomeAccountId")}
                  onValueChange={(value) =>
                    form.setValue("incomeAccountId", value, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(
                      errors.incomeAccountId && "border-destructive"
                    )}
                  >
                    <SelectValue placeholder="Select income account" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* TODO: Add accounts from API */}
                    <SelectItem value="placeholder">
                      Select an income account
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.incomeAccountId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.incomeAccountId.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Sales Tax</Label>
                <Select
                  value={form.watch("salesTaxId")}
                  onValueChange={(value) =>
                    form.setValue("salesTaxId", value, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.salesTaxId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select sales tax" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* TODO: Add taxes from API */}
                    <SelectItem value="placeholder">
                      Select a sales tax
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.salesTaxId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.salesTaxId.message}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Sell to Customers</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow this service to be sold to customers
                  </p>
                </div>
                <Switch
                  checked={form.watch("sellToCustomers")}
                  onCheckedChange={(checked) =>
                    form.setValue("sellToCustomers", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Purchase from Supplier</Label>
                  <p className="text-sm text-muted-foreground">
                    This service can be purchased from suppliers
                  </p>
                </div>
                <Switch
                  checked={form.watch("purchaseFromSupplier")}
                  onCheckedChange={(checked) =>
                    form.setValue("purchaseFromSupplier", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>

              {form.watch("purchaseFromSupplier") && (
                <>
                  <div>
                    <Label>Purchase Description</Label>
                    <Textarea
                      {...form.register("purchaseDescription")}
                      name="purchaseDescription"
                      placeholder="Enter purchase description"
                      className={cn(
                        errors.purchaseDescription && "border-destructive"
                      )}
                      disabled={isSubmitting}
                      rows={2}
                    />
                    {errors.purchaseDescription && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.purchaseDescription.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label>Purchase Cost</Label>
                    <Input
                      {...form.register("purchaseCost")}
                      name="purchaseCost"
                      type="number"
                      step="0.01"
                      placeholder="Enter purchase cost"
                      className={cn(
                        errors.purchaseCost && "border-destructive"
                      )}
                      disabled={isSubmitting}
                    />
                    {errors.purchaseCost && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.purchaseCost.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label>Expense Account</Label>
                    <Select
                      value={form.watch("expenseAccountId") || ""}
                      onValueChange={(value) =>
                        form.setValue("expenseAccountId", value || undefined, {
                          shouldValidate: true,
                        })
                      }
                      disabled={isSubmitting}
                    >
                      <SelectTrigger
                        className={cn(
                          errors.expenseAccountId && "border-destructive"
                        )}
                      >
                        <SelectValue placeholder="Select expense account" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* TODO: Add accounts from API */}
                        <SelectItem value="placeholder">
                          Select an expense account
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.expenseAccountId && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.expenseAccountId.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label>Preferred Supplier</Label>
                    <Select
                      value={form.watch("preferredSupplierId") || ""}
                      onValueChange={(value) =>
                        form.setValue(
                          "preferredSupplierId",
                          value || undefined,
                          {
                            shouldValidate: true,
                          }
                        )
                      }
                      disabled={isSubmitting}
                    >
                      <SelectTrigger
                        className={cn(
                          errors.preferredSupplierId && "border-destructive"
                        )}
                      >
                        <SelectValue placeholder="Select preferred supplier" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* TODO: Add suppliers from API */}
                        <SelectItem value="placeholder">
                          Select a supplier
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.preferredSupplierId && (
                      <p className="text-sm text-destructive mt-1">
                        {errors.preferredSupplierId.message}
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter service description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the service
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "staff-assignment",
      title: "Staff Assignment",
      icon: <Users className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasStaffErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Staff Assignment</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Assigned Staff Members</Label>
                <Select
                  value=""
                  onValueChange={(value) => {
                    const currentStaffIds = form.watch("staffMemberIds") || [];
                    if (!currentStaffIds.includes(value)) {
                      form.setValue(
                        "staffMemberIds",
                        [...currentStaffIds, value],
                        {
                          shouldValidate: true,
                        }
                      );
                    }
                  }}
                  disabled={isSubmitting}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select staff members to assign" />
                  </SelectTrigger>
                  <SelectContent>
                    {staffMembers.length > 0 ? (
                      staffMembers
                        .filter((staff) => {
                          const currentStaffIds = form.watch("staffMemberIds") || [];
                          return !currentStaffIds.includes(staff.id);
                        })
                        .map((staff) => (
                          <SelectItem key={staff.id} value={staff.id}>
                            {staff.displayName}
                          </SelectItem>
                        ))
                    ) : (
                      <SelectItem value="no-staff" disabled>
                        No staff members available
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                
                {/* Display selected staff members as badges */}
                {form.watch("staffMemberIds") && form.watch("staffMemberIds")!.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {form.watch("staffMemberIds")!.map((staffId) => {
                      const staffName = getStaffMemberName(staffId);
                      return (
                        <div
                          key={staffId}
                          className="inline-flex items-center gap-1 px-2 py-1 bg-secondary text-secondary-foreground rounded-md text-sm"
                        >
                          <span>{staffName}</span>
                          <button
                            type="button"
                            onClick={() => {
                              const currentStaffIds = form.watch("staffMemberIds") || [];
                              form.setValue(
                                "staffMemberIds",
                                currentStaffIds.filter((id) => id !== staffId),
                                {
                                  shouldValidate: true,
                                }
                              );
                            }}
                            className="ml-1 hover:bg-destructive hover:text-destructive-foreground rounded-sm p-0.5 transition-colors"
                            disabled={isSubmitting}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      );
                    })}
                  </div>
                )}
                
                {errors.staffMemberIds && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.staffMemberIds.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Assign multiple staff members who can provide this service
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "service-images",
      title: "Service Images",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Service Images</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={5}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={true}
              label="Images"
              description="Upload images for this service. Maximum file size is 5MB per image."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ServiceTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={service}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Service"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingService}
      // Location props
      isLocationEnabled={true}
      isAllocatedToAllLocations={
        form.watch("isAllocatedToAllLocations") || false
      }
      onAllLocationsChange={(isAllocated) => {
        form.setValue("isAllocatedToAllLocations", isAllocated, {
          shouldValidate: true,
        });
        // Clear location IDs when allocating to all locations
        if (isAllocated) {
          form.setValue("locationIds", [], { shouldValidate: true });
        }
      }}
      locationIds={form.watch("locationIds") || []}
      onLocationChange={(locationIds) => {
        form.setValue("locationIds", locationIds, { shouldValidate: true });
      }}
      locationErrors={
        errors.locationIds?.message || errors.isAllocatedToAllLocations?.message
      }
      // SEO props
      isSEOEnabled={true}
      seoTitle={form.watch("seoTitle") || ""}
      onSeoTitleChange={(value) => {
        form.setValue("seoTitle", value, { shouldValidate: true });
      }}
      seoTitleError={errors.seoTitle?.message}
      seoDescription={form.watch("seoDescription") || ""}
      onSeoDescriptionChange={(value) => {
        form.setValue("seoDescription", value, { shouldValidate: true });
      }}
      seoDescriptionError={errors.seoDescription?.message}
      seoKeywords={keywords}
      onSeoKeywordsChange={(value) => {
        setKeywords(value);
        form.setValue("seoKeywords", parseKeywords(value), {
          shouldValidate: true,
        });
      }}
      seoKeywordsError={errors.seoKeywords?.message}
      ogImageFiles={ogImageFiles}
      onOgImageFilesChange={setOgImageFiles}
    />
  );
}
