"use client";

import * as React from "react";
import {
  Info,
  Image as ImageIcon,
  DollarSign,
  Search,
  Calendar,
  User,
  MapPin,
  Globe,
  Eye,
  EyeOff,
  Hash,
  Users,
  Building,
  Printer,
  Download,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useService } from "@/lib/services/hooks";
import { ServiceTableData, ServiceStatus } from "@/types/service";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import Image from "next/image";

interface ServiceDetailsContentProps {
  service: ServiceTableData;
  isDemo?: boolean;
}

export function ServiceDetailsContent({
  service,
  isDemo = false,
}: ServiceDetailsContentProps) {
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch complete service data
  const { data: fullServiceResponse, isLoading: isLoadingService } = useService(
    service.id,
    isDemo
  );
  const fullService = fullServiceResponse?.data;

  // Print functionality
  const handlePrint = React.useCallback(() => {
    const printWindow = window.open("", "_blank");
    if (!printWindow || !printRef.current) return;

    const printContent = printRef.current.cloneNode(true) as HTMLElement;

    // Create a complete HTML document for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Service Details - ${fullService?.name || "Service"}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .print-header h1 { margin: 0; font-size: 24px; color: #333; }
            .print-header p { margin: 5px 0 0 0; color: #666; }
            .section { margin-bottom: 25px; page-break-inside: avoid; }
            .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
            .field { margin-bottom: 10px; }
            .field label { font-weight: bold; color: #555; display: block; margin-bottom: 3px; font-size: 12px; }
            .field div { color: #333; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
            .badge.active { background-color: #d4edda; color: #155724; }
            .badge.inactive { background-color: #fff3cd; color: #856404; }
            .badge.outline { border: 1px solid #ddd; background-color: #f8f9fa; color: #495057; }
            .image-container { margin-top: 10px; }
            .image-container img { max-width: 200px; height: 120px; object-fit: cover; border: 1px solid #ddd; }
            .keywords { display: flex; flex-wrap: wrap; gap: 5px; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Service Details</h1>
            <p>Generated on ${format(new Date(), "PPP 'at' p")}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [fullService?.name]);

  // PDF download functionality
  const handleDownloadPDF = React.useCallback(async () => {
    if (!printRef.current || !fullService) return;

    try {
      toast.loading("Generating PDF...");

      const canvas = await html2canvas(printRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: "#ffffff",
        width: printRef.current.scrollWidth,
        height: printRef.current.scrollHeight,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      const ratio = Math.min(pdfWidth / canvasWidth, pdfHeight / canvasHeight);
      const imgWidth = canvasWidth * ratio;
      const imgHeight = canvasHeight * ratio;

      // Add header
      pdf.setFontSize(20);
      pdf.text("Service Details", pdfWidth / 2, 20, { align: "center" });
      pdf.setFontSize(12);
      pdf.text(
        `Generated on ${format(new Date(), "PPP 'at' p")}`,
        pdfWidth / 2,
        30,
        { align: "center" }
      );

      // Add content
      pdf.addImage(imgData, "PNG", 0, 40, imgWidth, imgHeight);

      // Save the PDF
      const fileName = `service-${fullService.name
        .toLowerCase()
        .replace(/[^a-z0-9]/g, "-")}-${format(new Date(), "yyyy-MM-dd")}.pdf`;
      pdf.save(fileName);

      toast.dismiss();
      toast.success("PDF downloaded successfully!");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.dismiss();
      toast.error("Failed to generate PDF");
    }
  }, [fullService]);

  // Helper function to get status color
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.ACTIVE:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case ServiceStatus.INACTIVE:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (isLoadingService) {
    return <LoadingIndicator />;
  }

  if (!fullService) {
    return (
      <div className="p-6 text-center text-muted-foreground">
        Failed to load service details
      </div>
    );
  }

  return (
    <ScrollArea className="h-[70vh]">
      <div className="p-6 space-y-6 bg-white dark:bg-background">
        {/* Action Buttons */}
        <div className="flex items-center justify-between border-b pb-4 no-print">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Service Details
          </h2>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              Print
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleDownloadPDF}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download PDF
            </Button>
          </div>
        </div>
        {/* Printable Content */}
        <div ref={printRef}>
          {/* Basic Information Section */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Service Name
                </label>
                <div className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {fullService.name}
                </div>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Status
                </label>
                <div className="flex items-center">
                  <Badge
                    className={cn(
                      "capitalize text-xs",
                      getStatusColor(fullService.status)
                    )}
                  >
                    {fullService.status.toLowerCase()}
                  </Badge>
                </div>
              </div>

              {fullService.sku && (
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    SKU
                  </label>
                  <div className="text-base text-gray-900 dark:text-gray-100">
                    {fullService.sku}
                  </div>
                </div>
              )}

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Service Category
                </label>
                <div className="text-base text-gray-900 dark:text-gray-100">
                  {fullService.serviceCategoryName}
                  {fullService.serviceSubCategoryName && (
                    <span className="text-sm text-gray-500 ml-2">
                      / {fullService.serviceSubCategoryName}
                    </span>
                  )}
                </div>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Price Rate
                </label>
                <div className="text-base font-semibold text-green-600 dark:text-green-400">
                  ${fullService.priceRate}
                </div>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Position
                </label>
                <div className="text-base text-gray-900 dark:text-gray-100">
                  {fullService.position}
                </div>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Available Online
                </label>
                <div className="flex items-center gap-2">
                  {fullService.availableOnline ? (
                    <>
                      <Eye className="h-4 w-4 text-green-600" />
                      <span className="text-base text-green-600 dark:text-green-400">
                        Yes
                      </span>
                    </>
                  ) : (
                    <>
                      <EyeOff className="h-4 w-4 text-red-600" />
                      <span className="text-base text-red-600 dark:text-red-400">
                        No
                      </span>
                    </>
                  )}
                </div>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Sell to Customers
                </label>
                <div className="text-base text-gray-900 dark:text-gray-100">
                  {fullService.sellToCustomers ? "Yes" : "No"}
                </div>
              </div>

              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Purchase from Supplier
                </label>
                <div className="text-base text-gray-900 dark:text-gray-100">
                  {fullService.purchaseFromSupplier ? "Yes" : "No"}
                </div>
              </div>
            </div>

            {fullService.description && (
              <div className="space-y-1 pt-2">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Description
                </label>
                <div className="text-base text-gray-900 dark:text-gray-100">
                  {fullService.description}
                </div>
              </div>
            )}
          </div>

          {/* Financial Information */}
          {(fullService.incomeAccountName ||
            fullService.salesTaxName ||
            fullService.purchaseFromSupplier) && (
            <div className="border-t pt-6 space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-gray-500" />
                Financial Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                {fullService.incomeAccountName && (
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Income Account
                    </label>
                    <div className="text-base text-gray-900 dark:text-gray-100">
                      {fullService.incomeAccountName}
                    </div>
                  </div>
                )}

                {fullService.salesTaxName && (
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Sales Tax
                    </label>
                    <div className="text-base text-gray-900 dark:text-gray-100">
                      {fullService.salesTaxName}
                    </div>
                  </div>
                )}

                {fullService.purchaseFromSupplier && (
                  <>
                    {fullService.purchaseDescription && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Purchase Description
                        </label>
                        <div className="text-base text-gray-900 dark:text-gray-100">
                          {fullService.purchaseDescription}
                        </div>
                      </div>
                    )}

                    {fullService.purchaseCost && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Purchase Cost
                        </label>
                        <div className="text-base font-semibold text-gray-900 dark:text-gray-100">
                          ${fullService.purchaseCost}
                        </div>
                      </div>
                    )}

                    {fullService.expenseAccountName && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Expense Account
                        </label>
                        <div className="text-base text-gray-900 dark:text-gray-100">
                          {fullService.expenseAccountName}
                        </div>
                      </div>
                    )}

                    {fullService.preferredSupplierName && (
                      <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Preferred Supplier
                        </label>
                        <div className="text-base text-gray-900 dark:text-gray-100">
                          {fullService.preferredSupplierName}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}

          {/* Location & Staff Allocation */}
          <div className="border-t pt-6 space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <MapPin className="h-5 w-5 text-gray-500" />
              Location & Staff Allocation
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Location Allocation
                </label>
                <div className="flex items-center gap-2">
                  {fullService.isAllocatedToAllLocations ? (
                    <>
                      <Globe className="h-4 w-4 text-blue-600" />
                      <span className="text-base text-blue-600 dark:text-blue-400">
                        All Locations
                      </span>
                    </>
                  ) : (
                    <>
                      <MapPin className="h-4 w-4 text-orange-600" />
                      <span className="text-base text-orange-600 dark:text-orange-400">
                        Specific Locations
                      </span>
                    </>
                  )}
                </div>
              </div>

              {!fullService.isAllocatedToAllLocations &&
                fullService.locations &&
                fullService.locations.length > 0 && (
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Assigned Locations
                    </label>
                    <div className="flex flex-wrap gap-1">
                      {fullService.locations.map((location) => (
                        <Badge
                          key={location.id}
                          variant="outline"
                          className="text-xs"
                        >
                          <Building className="h-3 w-3 mr-1" />
                          {location.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

              {fullService.staffMembers &&
                fullService.staffMembers.length > 0 && (
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Assigned Staff
                    </label>
                    <div className="flex flex-wrap gap-1">
                      {fullService.staffMembers.map((staff) => (
                        <Badge
                          key={staff.id}
                          variant="outline"
                          className="text-xs"
                        >
                          <Users className="h-3 w-3 mr-1" />
                          {staff.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
            </div>
          </div>

          {/* SEO Information */}
          {(fullService.seoTitle ||
            fullService.seoDescription ||
            (fullService.seoKeywords &&
              fullService.seoKeywords.length > 0)) && (
            <div className="border-t pt-6 space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <Search className="h-5 w-5 text-gray-500" />
                SEO & Social Media
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                {fullService.seoTitle && (
                  <div className="space-y-1">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      SEO Title
                    </label>
                    <div className="text-base text-gray-900 dark:text-gray-100">
                      {fullService.seoTitle}
                    </div>
                  </div>
                )}

                {fullService.seoKeywords &&
                  fullService.seoKeywords.length > 0 && (
                    <div className="space-y-1">
                      <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                        Keywords
                      </label>
                      <div className="flex flex-wrap gap-1">
                        {fullService.seoKeywords.map((keyword, index) => (
                          <Badge
                            key={index}
                            variant="outline"
                            className="text-xs"
                          >
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
              </div>

              {fullService.seoDescription && (
                <div className="space-y-1 pt-2">
                  <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    SEO Description
                  </label>
                  <div className="text-base text-gray-900 dark:text-gray-100">
                    {fullService.seoDescription}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Images */}
          {((fullService.images && fullService.images.length > 0) ||
            fullService.ogImage) && (
            <div className="border-t pt-6 space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-gray-500" />
                Images
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                {fullService.images && fullService.images.length > 0 && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Service Images
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {fullService.images.map((image, index) => (
                        <div key={index} className="space-y-1">
                          <Image
                            src={image}
                            alt={`${fullService.name} ${index + 1}`}
                            width={100}
                            height={100}
                            className="w-full h-24 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
                          />
                          <p className="text-xs text-gray-500 text-center">
                            Image {index + 1}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {fullService.ogImage && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Open Graph Image
                    </label>
                    <div className="space-y-2">
                      <Image
                        src={fullService.ogImage}
                        alt={`${fullService.name} OG`}
                        width={100}
                        height={100}
                        className="w-full max-w-sm h-40 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="border-t pt-6 space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Calendar className="h-5 w-5 text-gray-500" />
              Metadata
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-4">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Created By
                </label>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-base text-gray-900 dark:text-gray-100">
                    {fullService.createdBy}
                  </span>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Created At
                </label>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-base text-gray-900 dark:text-gray-100">
                    {format(new Date(fullService.createdAt), "PPP")}
                  </span>
                </div>
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Last Updated
                </label>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-base text-gray-900 dark:text-gray-100">
                    {format(new Date(fullService.updatedAt), "PPP")}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>{" "}
        {/* Close printable content div */}
      </div>
    </ScrollArea>
  );
}
