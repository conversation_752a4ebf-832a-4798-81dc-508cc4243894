"use client";

import * as React from "react";
import {
  Info,
  Image as ImageIcon,
  DollarSign,
  Search,
  Calendar,
  User,
  MapPin,
  Tag,
  Globe,
  Eye,
  EyeOff,
  Hash,
  Users,
  Building,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useService } from "@/lib/services/hooks";
import { ServiceTableData, ServiceStatus } from "@/types/service";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface ServiceDetailsProps {
  service: ServiceTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isDemo?: boolean;
}

export function ServiceDetails({
  service,
  open,
  onOpenChange,
  isDemo = false,
}: ServiceDetailsProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  // Fetch complete service data
  const { data: fullServiceResponse, isLoading: isLoadingService } = useService(
    service?.id || "",
    isDemo
  );
  const fullService = fullServiceResponse?.data;

  // Helper function to get status color
  const getStatusColor = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.ACTIVE:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case ServiceStatus.INACTIVE:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  // Content component
  const Content = () => {
    if (isLoadingService) {
      return (
        <div className="space-y-6 p-6">
          <div className="space-y-3">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-1/3" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    if (!fullService) {
      return (
        <div className="p-6 text-center">
          <p className="text-muted-foreground">Service not found</p>
        </div>
      );
    }

    return (
      <ScrollArea className="h-full max-h-[80vh] overflow-y-auto">
        <div className="space-y-6 p-6">
          {/* Header */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">{fullService.name}</h2>
              <Badge
                className={cn(
                  "font-medium",
                  getStatusColor(fullService.status)
                )}
              >
                {fullService.status}
              </Badge>
            </div>
            {fullService.description && (
              <p className="text-muted-foreground">{fullService.description}</p>
            )}
            {fullService.sku && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Hash className="h-4 w-4" />
                <span>SKU: {fullService.sku}</span>
              </div>
            )}
            <div className="flex items-center gap-2 text-lg font-semibold text-green-600">
              <DollarSign className="h-5 w-5" />
              <span>${fullService.priceRate}</span>
            </div>
          </div>

          <Separator />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="h-5 w-5 text-muted-foreground" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Name
                    </label>
                    <p className="text-sm">{fullService.name}</p>
                  </div>
                  {fullService.sku && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        SKU
                      </label>
                      <p className="text-sm">{fullService.sku}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Service Category
                    </label>
                    <p className="text-sm">
                      {fullService.serviceCategoryName}
                      {fullService.serviceSubCategoryName && (
                        <span className="text-muted-foreground"> / {fullService.serviceSubCategoryName}</span>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Price Rate
                    </label>
                    <p className="text-sm font-semibold text-green-600">
                      ${fullService.priceRate}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Available Online
                    </label>
                    <div className="flex items-center gap-2">
                      {fullService.availableOnline ? (
                        <>
                          <Eye className="h-4 w-4 text-green-600" />
                          <span className="text-sm text-green-600 dark:text-green-400">
                            Yes
                          </span>
                        </>
                      ) : (
                        <>
                          <EyeOff className="h-4 w-4 text-red-600" />
                          <span className="text-sm text-red-600 dark:text-red-400">
                            No
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Position
                    </label>
                    <p className="text-sm">{fullService.position}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-muted-foreground" />
                  Financial Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Income Account
                  </label>
                  <p className="text-sm">{fullService.incomeAccountName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Sales Tax
                  </label>
                  <p className="text-sm">{fullService.salesTaxName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Sell to Customers
                  </label>
                  <p className="text-sm">
                    {fullService.sellToCustomers ? "Yes" : "No"}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Purchase from Supplier
                  </label>
                  <p className="text-sm">
                    {fullService.purchaseFromSupplier ? "Yes" : "No"}
                  </p>
                </div>
                {fullService.purchaseFromSupplier && (
                  <>
                    {fullService.purchaseDescription && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Purchase Description
                        </label>
                        <p className="text-sm">
                          {fullService.purchaseDescription}
                        </p>
                      </div>
                    )}
                    {fullService.purchaseCost && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Purchase Cost
                        </label>
                        <p className="text-sm">${fullService.purchaseCost}</p>
                      </div>
                    )}
                    {fullService.expenseAccountName && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Expense Account
                        </label>
                        <p className="text-sm">
                          {fullService.expenseAccountName}
                        </p>
                      </div>
                    )}
                    {fullService.preferredSupplierName && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Preferred Supplier
                        </label>
                        <p className="text-sm">
                          {fullService.preferredSupplierName}
                        </p>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>

            {/* Location & Staff Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  Location & Staff
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Location Allocation
                  </label>
                  {fullService.isAllocatedToAllLocations ? (
                    <div className="flex items-center gap-2 mt-1">
                      <Globe className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-600 dark:text-blue-400">
                        All Locations
                      </span>
                    </div>
                  ) : (
                    <div className="mt-1">
                      {fullService.locations &&
                      fullService.locations.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {fullService.locations.map((location) => (
                            <Badge
                              key={location.id}
                              variant="outline"
                              className="text-xs"
                            >
                              <Building className="h-3 w-3 mr-1" />
                              {location.name}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          No specific locations assigned
                        </p>
                      )}
                    </div>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Assigned Staff
                  </label>
                  {fullService.staffMembers &&
                  fullService.staffMembers.length > 0 ? (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {fullService.staffMembers.map((staff) => (
                        <Badge
                          key={staff.id}
                          variant="outline"
                          className="text-xs"
                        >
                          <Users className="h-3 w-3 mr-1" />
                          {staff.name}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">
                      No staff assigned
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* SEO Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5 text-muted-foreground" />
                  SEO & Social Media
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    SEO Title
                  </label>
                  <p className="text-sm">
                    {fullService.seoTitle || (
                      <span className="text-muted-foreground italic">
                        Not set
                      </span>
                    )}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    SEO Description
                  </label>
                  <p className="text-sm">
                    {fullService.seoDescription || (
                      <span className="text-muted-foreground italic">
                        Not set
                      </span>
                    )}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Keywords
                  </label>
                  {fullService.seoKeywords &&
                  fullService.seoKeywords.length > 0 ? (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {fullService.seoKeywords.map((keyword, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">
                      No keywords set
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Images */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5 text-muted-foreground" />
                  Images
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Service Images
                  </label>
                  {fullService.images && fullService.images.length > 0 ? (
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {fullService.images.map((image, index) => (
                        <div key={index}>
                          <img
                            src={image}
                            alt={`${fullService.name} ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg border"
                          />
                          <p className="text-xs text-muted-foreground mt-1 truncate">
                            Image {index + 1}
                          </p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">
                      No images uploaded
                    </p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    Open Graph Image
                  </label>
                  {fullService.ogImage ? (
                    <div className="mt-2">
                      <img
                        src={fullService.ogImage}
                        alt={`${fullService.name} OG`}
                        className="w-full max-w-xs h-32 object-cover rounded-lg border"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {fullService.ogImage}
                      </p>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-1">
                      No OG image uploaded
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Metadata */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  Metadata
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created By
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {fullService.createdBy}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Created At
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(new Date(fullService.createdAt), "PPP")}
                    </p>
                  </div>
                  <div>
                    <label className="font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {format(new Date(fullService.updatedAt), "PPP")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </ScrollArea>
    );
  };

  if (isDesktop) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-4xl max-h-[90vh] p-0">
          <DialogHeader className="px-6 py-4 border-b">
            <DialogTitle className="flex items-center justify-between">
              <span>Service Details</span>
              {isDemo && (
                <Badge variant="outline" className="ml-2">
                  Demo Mode
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>
          <Content />
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent className="max-h-[90vh]">
        <DrawerHeader className="border-b">
          <DrawerTitle className="flex items-center justify-between">
            <span>Service Details</span>
            {isDemo && (
              <Badge variant="outline" className="ml-2">
                Demo Mode
              </Badge>
            )}
          </DrawerTitle>
        </DrawerHeader>
        <Content />
      </DrawerContent>
    </Drawer>
  );
}
