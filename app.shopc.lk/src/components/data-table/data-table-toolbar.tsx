"use client";

import * as React from "react";
import type { DataTableFilterField } from "@/types";
import type { Table } from "@tanstack/react-table";
import { Icon } from "@/components/ui/icon";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTableFacetedFilter } from "@/components/data-table/data-table-faceted-filter";
import { DataTableViewOptions } from "@/components/data-table/data-table-view-options";
import { useMediaQuery } from "@/hooks/use-media-query";
import {
  She<PERSON>,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

interface DataTableToolbarProps<TData>
  extends React.HTMLAttributes<HTMLDivElement> {
  table: Table<TData>;
  /**
   * An array of filter field configurations for the data table.
   * When options are provided, a faceted filter is rendered.
   * Otherwise, a search filter is rendered.
   *
   * @example
   * const filterFields = [
   *   {
   *     id: 'name',
   *     label: 'Name',
   *     placeholder: 'Filter by name...'
   *   },
   *   {
   *     id: 'status',
   *     label: 'Status',
   *     options: [
   *       { label: 'Active', value: 'active', icon: ActiveIcon, count: 10 },
   *       { label: 'Inactive', value: 'inactive', icon: InactiveIcon, count: 5 }
   *     ]
   *   }
   * ]
   */
  filterFields?: DataTableFilterField<TData>[];
}

export function DataTableToolbar<TData>({
  table,
  filterFields = [],
  children,
  className,
  ...props
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0;
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [open, setOpen] = React.useState(false);

  // Memoize computation of searchableColumns and filterableColumns
  const { searchableColumns, filterableColumns } = React.useMemo(() => {
    return {
      searchableColumns: filterFields.filter((field) => !field.options),
      filterableColumns: filterFields.filter((field) => field.options),
    };
  }, [filterFields]);

  const FilterContent = () => (
    <div>
      {filterableColumns.length > 0 ? (
        <div className="space-y-4">
          {filterableColumns.map((column) => {
            const tableColumn = table.getColumn(
              column.id ? String(column.id) : ""
            );
            return tableColumn ? (
              <div
                key={String(column.id)}
                className="pb-4 border-b last:border-0"
              >
                <DataTableFacetedFilter
                  column={tableColumn}
                  title={column.label}
                  options={column.options ?? []}
                />
              </div>
            ) : null;
          })}
        </div>
      ) : (
        <div className="text-center py-4 text-muted-foreground">
          No filters available
        </div>
      )}
      {isFiltered && (
        <Button
          aria-label="Reset filters"
          variant="outline"
          className="mt-2"
          onClick={() => {
            table.resetColumnFilters();
            if (isMobile) setOpen(false);
          }}
        >
          Reset filters
          <Icon name="x" size={16} className="ml-2" aria-hidden="true" />
        </Button>
      )}
    </div>
  );

  return (
    <div
      className={cn(
        "flex w-full items-center justify-between gap-2 overflow-auto p-1",
        className
      )}
      {...props}
    >
      <div className="flex flex-1 items-center gap-2">
        {isMobile ? (
          <Sheet open={open} onOpenChange={setOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1">
                <Icon name="search" size={16} />
                Filters
                {isFiltered && (
                  <span className="ml-1 rounded-full bg-primary text-primary-foreground px-1.5 py-0.5 text-xs font-medium">
                    {table.getState().columnFilters.length}
                  </span>
                )}
              </Button>
            </SheetTrigger>
            <SheetContent
              side="left"
              className="w-[300px] sm:w-[400px] overflow-y-auto"
            >
              <SheetHeader>
                <SheetTitle>Filters</SheetTitle>
              </SheetHeader>
              <FilterContent />
            </SheetContent>
          </Sheet>
        ) : (
          <>
            {searchableColumns.length > 0 &&
              searchableColumns.map(
                (column) =>
                  table.getColumn(column.id ? String(column.id) : "") && (
                    <Input
                      key={String(column.id)}
                      placeholder={column.placeholder}
                      value={
                        (table
                          .getColumn(String(column.id))
                          ?.getFilterValue() as string) ?? ""
                      }
                      onChange={(event) =>
                        table
                          .getColumn(String(column.id))
                          ?.setFilterValue(event.target.value)
                      }
                      className="h-8 w-40 lg:w-64 bg-background"
                    />
                  )
              )}
            {filterableColumns.length > 0 &&
              filterableColumns.map((column) => {
                const tableColumn = table.getColumn(
                  column.id ? String(column.id) : ""
                );
                return tableColumn ? (
                  <DataTableFacetedFilter
                    key={String(column.id)}
                    column={tableColumn}
                    title={column.label}
                    options={column.options ?? []}
                  />
                ) : null;
              })}
            {isFiltered && (
              <Button
                aria-label="Reset filters"
                variant="ghost"
                className="h-8 px-2 lg:px-3"
                onClick={() => table.resetColumnFilters()}
              >
                Reset
                <Icon name="x" size={16} className="ml-2" aria-hidden="true" />
              </Button>
            )}
          </>
        )}
      </div>
      <div className="flex items-center gap-2">
        {children}
        <DataTableViewOptions table={table} />
      </div>
    </div>
  );
}
