"use client";

// DataTableRowActions component for handling row actions in data tables
import * as React from "react";
import { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Icon } from "@/components/ui/icon";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
  actions: {
    type: string;
    label: string;
    onClick: (row: Row<TData>) => void;
    icon?: React.ReactNode;
    className?: string;
    disabled?: boolean;
  }[];
}

export function DataTableRowActions<TData>({
  row,
  actions,
}: DataTableRowActionsProps<TData>) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
        >
          <Icon name="menu" size={16} />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[160px]">
        {actions.map((action) => (
          <DropdownMenuItem
            key={action.type}
            onClick={() => action.onClick(row)}
            className={action.className}
            disabled={action.disabled}
          >
            {action.icon ||
              (action.type === "update" ? (
                <Icon name="edit" size={16} className="mr-2" />
              ) : action.type === "delete" ? (
                <Icon name="trash" size={16} className="mr-2" />
              ) : null)}
            {action.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
