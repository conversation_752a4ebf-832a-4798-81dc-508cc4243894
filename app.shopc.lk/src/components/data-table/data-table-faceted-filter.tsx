import type { Option } from "@/types";
import type { Column } from "@tanstack/react-table";
import { Icon } from "@/components/ui/icon";

import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";

interface DataTableFacetedFilterProps<TData, TValue> {
  column?: Column<TData, TValue>;
  title?: string;
  options: Option[];
}

export function DataTableFacetedFilter<TData, TValue>({
  column,
  title,
  options,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const unknownValue = column?.getFilterValue();
  const selectedValues = new Set(
    Array.isArray(unknownValue) ? unknownValue : []
  );

  const handleSelect = (value: string) => {
    if (selectedValues.has(value)) {
      selectedValues.delete(value);
    } else {
      selectedValues.add(value);
    }
    const filterValues = Array.from(selectedValues);
    column?.setFilterValue(filterValues.length ? filterValues : undefined);
  };

  const clearFilters = () => column?.setFilterValue(undefined);

  // Desktop filter options with search
  const DesktopFilterOptions = () => (
    <Command>
      <CommandInput placeholder={title} />
      <CommandList className="max-h-full">
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup className="max-h-[18.75rem] overflow-y-auto overflow-x-hidden">
          {options.map((option) => {
            const isSelected = selectedValues.has(option.value);

            return (
              <CommandItem
                key={option.value}
                onSelect={() => handleSelect(option.value)}
              >
                <div
                  className={cn(
                    "mr-2 flex size-4 items-center justify-center rounded-sm border border-primary",
                    isSelected
                      ? "bg-primary text-primary-foreground"
                      : "opacity-50 [&_svg]:invisible"
                  )}
                >
                  <Icon name="check" size={16} aria-hidden="true" />
                </div>
                {option.icon && (
                  <option.icon
                    className="mr-2 size-4 text-muted-foreground"
                    aria-hidden="true"
                  />
                )}
                <span>{option.label}</span>
                {option.count && (
                  <span className="ml-auto flex size-4 items-center justify-center font-mono text-xs">
                    {option.count}
                  </span>
                )}
              </CommandItem>
            );
          })}
        </CommandGroup>
        {selectedValues.size > 0 && (
          <>
            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                onSelect={clearFilters}
                className="justify-center text-center"
              >
                Clear filters
              </CommandItem>
            </CommandGroup>
          </>
        )}
      </CommandList>
    </Command>
  );

  // Compact mobile filter options
  const MobileFilterOptions = () => (
    <div className="space-y-0.5">
      {options.map((option) => {
        const isSelected = selectedValues.has(option.value);
        return (
          <div
            key={option.value}
            className="flex items-center px-2 py-1 hover:bg-accent cursor-pointer"
            onClick={() => handleSelect(option.value)}
          >
            <div
              className={cn(
                "mr-2 flex size-4 items-center justify-center rounded-sm border border-primary",
                isSelected
                  ? "bg-primary text-primary-foreground"
                  : "opacity-50 [&_svg]:invisible"
              )}
            >
              <Icon name="check" size={12} aria-hidden="true" />
            </div>
            <span className="text-xs">{option.label}</span>
            {option.count && (
              <span className="ml-auto text-xs text-muted-foreground">
                {option.count}
              </span>
            )}
          </div>
        );
      })}
      {selectedValues.size > 0 && (
        <div
          className="px-2 py-1 text-center text-xs hover:bg-accent cursor-pointer text-primary"
          onClick={clearFilters}
        >
          Clear filters
        </div>
      )}
    </div>
  );

  const FilterHeader = () => (
    <div className="flex items-center justify-between px-2 py-1">
      <div className="flex items-center">
        <Icon name="plus" size={12} className="mr-1" />
        <span className="text-xs font-medium">{title}</span>
      </div>
      {selectedValues?.size > 0 && (
        <Badge
          variant="secondary"
          className="h-4 rounded-sm px-1 text-xs font-normal"
        >
          {selectedValues.size}
        </Badge>
      )}
    </div>
  );

  return (
    <div className="inline-block mr-2 mb-2">
      {/* Desktop view with Popover */}
      <div className="hidden sm:block">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 border-dashed">
              <Icon name="plus" size={16} className="mr-2" />
              {title}
              {selectedValues?.size > 0 && (
                <>
                  <Separator orientation="vertical" className="mx-2 h-4" />
                  <Badge
                    variant="secondary"
                    className="rounded-sm px-1 font-normal lg:hidden"
                  >
                    {selectedValues.size}
                  </Badge>
                  <div className="hidden space-x-1 lg:flex">
                    {selectedValues.size > 2 ? (
                      <Badge
                        variant="secondary"
                        className="rounded-sm px-1 font-normal"
                      >
                        {selectedValues.size} selected
                      </Badge>
                    ) : (
                      options
                        .filter((option) => selectedValues.has(option.value))
                        .map((option) => (
                          <Badge
                            variant="secondary"
                            key={option.value}
                            className="rounded-sm px-1 font-normal"
                          >
                            {option.label}
                          </Badge>
                        ))
                    )}
                  </div>
                </>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="p-0" align="start">
            <DesktopFilterOptions />
          </PopoverContent>
        </Popover>
      </div>

      {/* Compact mobile view */}
      <div className="sm:hidden mb-2">
        <FilterHeader />
        <MobileFilterOptions />
      </div>
    </div>
  );
}
