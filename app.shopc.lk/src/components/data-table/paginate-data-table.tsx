import * as React from "react";
import {
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
  type ColumnDef,
  type Table as TanstackTable,
} from "@tanstack/react-table";

import { cn } from "@/lib/utils";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Icon } from "@/components/ui/icon";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { TableToolbar } from "./table-toolbar";

interface PaginateDataTableProps<TData, TValue> {
  /**
   * The data to display in the table
   */
  data: TData[];

  /**
   * The columns configuration for the table
   */
  columns: ColumnDef<TData, TValue>[];

  /**
   * The number of rows to display per page
   * @default 5
   */
  pageSize?: number;

  /**
   * Available page size options for the user to select
   * @default [5, 10, 20, 50]
   */
  pageSizeOptions?: number[];

  /**
   * Additional className for the container
   */
  className?: string;

  /**
   * Optional callback when row is clicked
   */
  onRowClick?: (row: TData) => void;

  /**
   * Message to display when no results are found
   * @default "No results."
   */
  noResultsMessage?: string;

  /**
   * Total number of items (for server-side pagination)
   */
  totalItems?: number;

  /**
   * Total number of pages (for server-side pagination)
   */
  pageCount?: number;

  /**
   * Current page (for server-side pagination)
   * @default 1
   */
  currentPage?: number;

  /**
   * Callback when page is changed (for server-side pagination)
   */
  onPageChange?: (page: number) => void;

  /**
   * Callback when page size is changed (for server-side pagination)
   */
  onPageSizeChange?: (pageSize: number) => void;

  /**
   * Whether the table is in a loading state
   * @default false
   */
  isLoading?: boolean;

  /**
   * Number of skeleton rows to show when loading
   * @default 5
   */
  skeletonRows?: number;

  /**
   * Table title for exports
   */
  title?: string;

  /**
   * Whether to enable row selection
   * @default false
   */
  enableRowSelection?: boolean;

  /**
   * Whether to enable export functionality
   * @default true
   */
  enableExport?: boolean;

  /**
   * Whether to show the toolbar
   * @default true
   */
  showToolbar?: boolean;

  /**
   * Whether it's a demo mode
   * @default false
   */
  isDemo?: boolean;

  /**
   * Custom add button
   */
  addButton?: React.ReactNode;

  /**
   * Additional actions for the toolbar
   */
  additionalActions?: React.ReactNode;

  /**
   * Refresh function
   */
  onRefresh?: () => Promise<void>;
}

export function PaginateDataTable<TData, TValue>({
  data,
  columns,
  pageSize = 5,
  pageSizeOptions = [5, 10, 20, 50],
  className,
  onRowClick,
  noResultsMessage = "No results.",
  totalItems,
  pageCount: externalPageCount,
  currentPage = 1,
  onPageChange,
  onPageSizeChange,
  isLoading = false,
  skeletonRows = 5,
  title = "Table Data",
  enableRowSelection = false,
  enableExport = true,
  showToolbar = true,
  isDemo = false,
  addButton,
  additionalActions,
  onRefresh,
}: PaginateDataTableProps<TData, TValue>) {
  // Determine if we're using external pagination
  const isExternalPagination = !!onPageChange;

  // Local state to track selected page size
  const [selectedPageSize, setSelectedPageSize] = React.useState(pageSize);

  // Row selection state
  const [rowSelection, setRowSelection] = React.useState(false);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Only use internal pagination when not using external pagination
    getPaginationRowModel: isExternalPagination
      ? undefined
      : getPaginationRowModel(),
    manualPagination: isExternalPagination,
    pageCount: externalPageCount,
    enableRowSelection: enableRowSelection && rowSelection,
    state: {
      pagination: {
        pageSize: selectedPageSize,
        pageIndex: isExternalPagination ? currentPage - 1 : 0,
      },
      rowSelection: {},
    },
  });

  // Handle page change with external pagination
  const handlePageChange = (newPage: number) => {
    if (isExternalPagination && onPageChange) {
      onPageChange(newPage);
    } else {
      table.setPageIndex(newPage - 1);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newSize: number) => {
    setSelectedPageSize(newSize);

    if (isExternalPagination && onPageSizeChange) {
      onPageSizeChange(newSize);
    } else {
      table.setPageSize(newSize);
    }
  };

  // Render skeleton loading state
  const renderSkeletonRows = () => {
    return Array(skeletonRows)
      .fill(0)
      .map((_, rowIndex) => (
        <TableRow key={`skeleton-row-${rowIndex}`} className="animate-pulse">
          {Array(columns.length)
            .fill(0)
            .map((_, colIndex) => (
              <TableCell
                key={`skeleton-cell-${rowIndex}-${colIndex}`}
                className="px-4 py-3"
              >
                <Skeleton className="h-5 w-full" />
              </TableCell>
            ))}
        </TableRow>
      ));
  };

  return (
    <div className={cn("w-full space-y-2.5", className)}>
      {showToolbar && enableExport && (
        <TableToolbar
          table={table}
          rowSelection={rowSelection}
          onRowSelectionChange={setRowSelection}
          title={title}
          isDemo={isDemo}
          addButton={addButton}
          additionalActions={additionalActions}
          onRefresh={onRefresh}
        />
      )}

      <div className="overflow-hidden rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className="bg-muted border-y hover:bg-muted"
              >
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className="px-4 py-3 text-xs font-medium text-muted-foreground uppercase tracking-wider"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              renderSkeletonRows()
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  className={cn(
                    "hover:bg-muted/50",
                    onRowClick && "cursor-pointer",
                    row.getIsSelected() && "bg-muted"
                  )}
                  onClick={() => onRowClick && onRowClick(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="px-4 py-3">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  {noResultsMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="p-4 border-t flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Select
            value={`${selectedPageSize}`}
            onValueChange={(value) => handlePageSizeChange(Number(value))}
            disabled={isLoading}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={selectedPageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={`${size}`}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-sm text-foreground/80">
            Page{" "}
            {isExternalPagination
              ? currentPage
              : table.getState().pagination.pageIndex + 1}{" "}
            of {isExternalPagination ? externalPageCount : table.getPageCount()}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              isExternalPagination
                ? handlePageChange(currentPage - 1)
                : table.previousPage()
            }
            disabled={
              isLoading ||
              (isExternalPagination
                ? currentPage <= 1
                : !table.getCanPreviousPage())
            }
          >
            <Icon name="chevronLeft" size={16} className="mr-1" />
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              isExternalPagination
                ? handlePageChange(currentPage + 1)
                : table.nextPage()
            }
            disabled={
              isLoading ||
              (isExternalPagination
                ? currentPage >= (externalPageCount || 1)
                : !table.getCanNextPage())
            }
          >
            Next
            <Icon name="chevronRight" size={16} className="ml-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}
