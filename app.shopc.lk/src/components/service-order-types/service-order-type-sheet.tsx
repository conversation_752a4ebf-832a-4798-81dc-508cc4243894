"use client";

import * as React from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useToast } from "@/hooks/use-toast";
import { Info, Settings } from "lucide-react";
import { cn } from "@/lib/utils";
import { BaseSheet } from "@/components/shared/base-sheet";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionContent, AccordionTrigger } from "@/components/ui/accordion";
import { ApiStatus } from "@/types/common";
import {
  ServiceOrderTypeTableData,
  ServiceOrderTypeStatus,
  ServiceCategory,
  CreateServiceOrderTypeDto,
  UpdateServiceOrderTypeDto,
} from "@/types/service-order-type";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useServiceOrderType,
  useCreateServiceOrderType,
  useUpdateServiceOrderType,
} from "@/lib/service-order-types/hooks";
import { serviceOrderTypeFormSchema } from "@/lib/service-order-types/validations";
import { useEffect } from "react";

interface ServiceOrderTypeSheetProps {
  serviceOrderType: ServiceOrderTypeTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (serviceOrderType?: ServiceOrderTypeTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof serviceOrderTypeFormSchema>;

export function ServiceOrderTypeSheet({
  serviceOrderType,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ServiceOrderTypeSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const { toast } = useToast();

  // Hooks for API operations
  const createServiceOrderTypeMutation = useCreateServiceOrderType(isDemo);
  const updateServiceOrderTypeMutation = useUpdateServiceOrderType(isDemo);

  // Fetch service order type data for editing
  const {
    data: fullServiceOrderTypeResponse,
    isLoading: isLoadingServiceOrderType,
  } = useServiceOrderType(serviceOrderType?.id || "", isDemo);
  const fullServiceOrderType = fullServiceOrderTypeResponse?.data;

  const form = useForm<FormData>({
    resolver: zodResolver(serviceOrderTypeFormSchema),
    defaultValues: {
      typeCode: "",
      typeName: "",
      category: ServiceCategory.REPAIR,
      requiresItems: false,
      requiresDiagnostics: false,
      workflowTemplate: "",
      status: ServiceOrderTypeStatus.ACTIVE,
    },
  });

  // Populate form when editing
  useEffect(() => {
    if (fullServiceOrderType && serviceOrderType) {
      form.reset({
        typeCode: fullServiceOrderType.typeCode || "",
        typeName: fullServiceOrderType.typeName || "",
        category: fullServiceOrderType.category || ServiceCategory.REPAIR,
        requiresItems: fullServiceOrderType.requiresItems || false,
        requiresDiagnostics: fullServiceOrderType.requiresDiagnostics || false,
        workflowTemplate: fullServiceOrderType.workflowTemplate || "",
        status: fullServiceOrderType.status || ServiceOrderTypeStatus.ACTIVE,
      });
    } else if (!serviceOrderType) {
      // Reset form for new service order type
      form.reset({
        typeCode: "",
        typeName: "",
        category: ServiceCategory.REPAIR,
        requiresItems: false,
        requiresDiagnostics: false,
        workflowTemplate: "",
        status: ServiceOrderTypeStatus.ACTIVE,
      });
    }
  }, [serviceOrderType, fullServiceOrderType]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check for errors in different sections
  const hasBasicInfoErrors = !!(
    errors.typeCode ||
    errors.typeName ||
    errors.category ||
    errors.status
  );

  const hasConfigurationErrors = !!(
    errors.requiresItems ||
    errors.requiresDiagnostics ||
    errors.workflowTemplate
  );

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const formData = form.getValues();

      if (serviceOrderType) {
        // Update existing service order type
        const updateData: UpdateServiceOrderTypeDto = {
          typeCode: formData.typeCode,
          typeName: formData.typeName,
          category: formData.category,
          requiresItems: formData.requiresItems,
          requiresDiagnostics: formData.requiresDiagnostics,
          workflowTemplate: formData.workflowTemplate || undefined,
          status: formData.status,
        };

        const response = await updateServiceOrderTypeMutation.mutateAsync({
          id: serviceOrderType.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast({
            title: "Success",
            description: "Service order type updated successfully"
          });
          onOpenChange?.(false);
          onSuccess?.(response.data as ServiceOrderTypeTableData);
          return;
        }
        toast({
          title: "Error",
          description: response.message || "Failed to update service order type",
          variant: "destructive"
        });
        return; // Don't close the sheet if there was an error
      } else {
        // Create new service order type
        const createData: CreateServiceOrderTypeDto = {
          typeCode: formData.typeCode,
          typeName: formData.typeName,
          category: formData.category,
          requiresItems: formData.requiresItems,
          requiresDiagnostics: formData.requiresDiagnostics,
          workflowTemplate: formData.workflowTemplate || undefined,
          status: formData.status,
        };

        const response = await createServiceOrderTypeMutation.mutateAsync(
          createData
        );

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast({
            title: "Success",
            description: "Service order type created successfully"
          });
          onOpenChange?.(false);
          onSuccess?.(response.data as ServiceOrderTypeTableData);
          return;
        }
        toast({
          title: "Error",
          description: response.message || "Failed to create service order type",
          variant: "destructive"
        });
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save service order type:", error);
      toast({
        title: "Error",
        description: "Failed to save service order type",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Type Code</Label>
                <Input
                  {...form.register("typeCode")}
                  name="typeCode"
                  placeholder="Enter type code (e.g., REP001, MAINT001)"
                  className={cn(errors.typeCode && "border-destructive")}
                />
                {errors.typeCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.typeCode.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Type Name</Label>
                <Input
                  {...form.register("typeName")}
                  name="typeName"
                  placeholder="Enter type name (e.g., Standard Repair, Preventive Maintenance)"
                  className={cn(errors.typeName && "border-destructive")}
                />
                {errors.typeName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.typeName.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Category</Label>
                <Select
                  value={form.watch("category")}
                  onValueChange={(value: ServiceCategory) =>
                    form.setValue("category", value, { shouldValidate: true })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.category && "border-destructive")}
                  >
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ServiceCategory.REPAIR}>
                      Repair
                    </SelectItem>
                    <SelectItem value={ServiceCategory.MAINTENANCE}>
                      Maintenance
                    </SelectItem>
                    <SelectItem value={ServiceCategory.INSTALLATION}>
                      Installation
                    </SelectItem>
                    <SelectItem value={ServiceCategory.CONSULTATION}>
                      Consultation
                    </SelectItem>
                    <SelectItem value={ServiceCategory.WARRANTY}>
                      Warranty
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.category && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.category.message}
                  </p>
                )}
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value: ServiceOrderTypeStatus) =>
                    form.setValue("status", value, { shouldValidate: true })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ServiceOrderTypeStatus.ACTIVE}>
                      Active
                    </SelectItem>
                    <SelectItem value={ServiceOrderTypeStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "configuration",
      title: "Configuration",
      icon: <Settings className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasConfigurationErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Configuration</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Requires Items</Label>
                  <p className="text-sm text-muted-foreground">
                    Whether this service order type requires items/parts
                  </p>
                </div>
                <Switch
                  checked={form.watch("requiresItems")}
                  onCheckedChange={(checked) =>
                    form.setValue("requiresItems", checked, {
                      shouldValidate: true,
                    })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Requires Diagnostics</Label>
                  <p className="text-sm text-muted-foreground">
                    Whether this service order type requires diagnostic
                    procedures
                  </p>
                </div>
                <Switch
                  checked={form.watch("requiresDiagnostics")}
                  onCheckedChange={(checked) =>
                    form.setValue("requiresDiagnostics", checked, {
                      shouldValidate: true,
                    })
                  }
                />
              </div>

              <div>
                <Label>Workflow Template</Label>
                <Textarea
                  {...form.register("workflowTemplate")}
                  name="workflowTemplate"
                  placeholder="Enter workflow template description (optional)"
                  className={cn(
                    errors.workflowTemplate && "border-destructive"
                  )}
                  rows={3}
                />
                {errors.workflowTemplate && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.workflowTemplate.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ServiceOrderTypeTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={serviceOrderType}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Service Order Type"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingServiceOrderType}
    />
  );
}
