"use client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Copy,
  Facebook,
  Instagram,
  Link2,
  Share2,
  Twitter,
} from "lucide-react";
import Image from "next/image";

interface MarketingPageProps {
  isDemo?: boolean;
}

export function MarketingPage({ isDemo = false }: MarketingPageProps) {
  const { toast } = useToast();
  const storeLink = isDemo
    ? "https://take.app/demo-store"
    : "https://take.app/a9stores";

  const handleCopyLink = () => {
    navigator.clipboard.writeText(storeLink);
    toast({
      description: "Store link copied to clipboard",
    });
  };

  const handleCopyWidget = () => {
    const widgetCode = `<script async src="https://take.app/static/launcher.js" id="ta-widget" data-id="${
      isDemo ? "demo-store" : "a9stores"
    }" data-message="Visit our Take App store"></script>`;
    navigator.clipboard.writeText(widgetCode);
    toast({
      description: "Widget code copied to clipboard",
    });
  };

  return (
    <Card className="p-6">
        <div className="space-y-8">
          {/* Share store link section */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">
                Share your {isDemo ? "demo" : ""} store link
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Make your store visible on your social media and website
              </p>

              <div className="flex gap-2 mb-6">
                <Input value={storeLink} readOnly className="flex-1" />
                <Button variant="outline" onClick={handleCopyLink}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Button
                  className="w-full flex items-center gap-2"
                  variant="outline"
                >
                  <Instagram className="h-5 w-5" />
                  Add link in bio
                </Button>
                <Button
                  className="w-full flex items-center gap-2"
                  variant="outline"
                >
                  <Facebook className="h-5 w-5" />
                  Share on Facebook
                </Button>
                <Button
                  className="w-full flex items-center gap-2"
                  variant="outline"
                >
                  <Twitter className="h-5 w-5" />
                  Share on Twitter
                </Button>
              </div>
            </div>

            {/* QR Code section */}
            <div>
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-medium">Download QR code</h3>
                  <p className="text-sm text-muted-foreground">
                    Get your QR code ready for your physical stores
                  </p>
                </div>
                <Button variant="outline">Download</Button>
              </div>

              <div className="flex gap-6">
                {/* Simple QR */}
                <div className="bg-background p-6 rounded-lg border border-border">
                  <Image
                    src="/qr-placeholder.svg"
                    alt="Store QR Code"
                    width={200}
                    height={200}
                    className="mb-2"
                  />
                </div>

                {/* Branded QR */}
                <div className="bg-black text-white p-6 rounded-lg">
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-bold mb-1">SCAN ME</h3>
                    <p className="text-sm">TO VISIT OUR WEBSITE</p>
                  </div>
                  <Image
                    src="/qr-placeholder.svg"
                    alt="Store QR Code"
                    width={200}
                    height={200}
                    className="mb-2 invert"
                  />
                  <p className="text-center text-sm mt-2">
                    {isDemo ? "Demo Store" : "A9 Stores"}
                  </p>
                </div>
              </div>
            </div>

            {/* Embed Widget section */}
            <div>
              <h3 className="text-lg font-medium mb-2">Embed widget</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Copy the below code snippet and paste it into your HTML code,
                preferably before the closing head tag.
              </p>

              <div className="relative">
                <Input
                  value={`<script async src="https://take.app/static/launcher.js" id="ta-widget" data-id="${
                    isDemo ? "demo-store" : "a9stores"
                  }" data-message="Visit our Take App store"></script>`}
                  readOnly
                  className="font-mono text-sm pr-16"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2"
                  onClick={handleCopyWidget}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Social Media Integration */}
            <div>
              <h3 className="text-lg font-medium mb-4">
                Social Media Integration
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <Instagram className="h-8 w-8" />
                    <div>
                      <h4 className="font-medium">Instagram</h4>
                      <p className="text-sm text-muted-foreground">
                        Connect your Instagram account
                      </p>
                    </div>
                  </div>
                  <Button className="w-full" variant="outline">
                    Connect Account
                  </Button>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <Facebook className="h-8 w-8" />
                    <div>
                      <h4 className="font-medium">Facebook</h4>
                      <p className="text-sm text-muted-foreground">
                        Connect your Facebook page
                      </p>
                    </div>
                  </div>
                  <Button className="w-full" variant="outline">
                    Connect Page
                  </Button>
                </Card>
              </div>
            </div>

            {/* Promotional Tools */}
            <div>
              <h3 className="text-lg font-medium mb-4">Promotional Tools</h3>
              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <Share2 className="h-8 w-8" />
                    <div>
                      <h4 className="font-medium">Referral Program</h4>
                      <p className="text-sm text-muted-foreground">
                        Create a referral program for your customers
                      </p>
                    </div>
                  </div>
                  <Button className="w-full" variant="outline">
                    Set Up Program
                  </Button>
                </Card>
                <Card className="p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <Link2 className="h-8 w-8" />
                    <div>
                      <h4 className="font-medium">Affiliate Marketing</h4>
                      <p className="text-sm text-muted-foreground">
                        Start an affiliate marketing program
                      </p>
                    </div>
                  </div>
                  <Button className="w-full" variant="outline">
                    Create Program
                  </Button>
                </Card>
              </div>
            </div>
      </div>
    </div>
  </Card>
  );
}
