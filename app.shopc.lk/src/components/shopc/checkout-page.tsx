"use client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useShopcStore } from "@/lib/stores/storefront-store";

interface CheckoutPageProps {
  isDemo?: boolean;
}

export function CheckoutPage({ isDemo = false }: CheckoutPageProps) {
  const { 
    paymentMethods, 
    shippingOptions, 
    orderSettings,
    updatePaymentMethods,
    updateShippingOptions,
    updateOrderSettings
  } = useShopcStore();
  return (
    <Card className="p-6">
        <div className="space-y-8">
          {/* Payment Methods */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Payment Methods</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Cash on Delivery</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to pay when they receive their order
                  </p>
                </div>
                <Switch 
                  checked={paymentMethods.cashOnDelivery}
                  onCheckedChange={(checked) => updatePaymentMethods({ cashOnDelivery: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Bank Transfer</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to pay via bank transfer
                  </p>
                </div>
                <Switch 
                  checked={paymentMethods.bankTransfer}
                  onCheckedChange={(checked) => updatePaymentMethods({ bankTransfer: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Online Payment</Label>
                  <p className="text-sm text-muted-foreground">
                    Accept credit cards and other online payment methods
                  </p>
                </div>
                <Switch 
                  checked={paymentMethods.onlinePayment}
                  onCheckedChange={(checked) => updatePaymentMethods({ onlinePayment: checked })}
                />
              </div>
            </div>
          </div>

          {/* Shipping Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Shipping Options</h3>
            <div className="space-y-4">
              <div>
                <Label>Default Shipping Method</Label>
                <Select 
                  value={shippingOptions.defaultMethod}
                  onValueChange={(value) => updateShippingOptions({ defaultMethod: value })}
                >
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue placeholder="Select shipping method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard Shipping</SelectItem>
                    <SelectItem value="express">Express Shipping</SelectItem>
                    <SelectItem value="pickup">Store Pickup</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Shipping Cost</Label>
                <Input 
                  type="number" 
                  placeholder="0.00" 
                  className="mt-1"
                  value={shippingOptions.shippingCost}
                  onChange={(e) => updateShippingOptions({ shippingCost: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Free Shipping</Label>
                  <p className="text-sm text-muted-foreground">
                    Offer free shipping for orders above a certain amount
                  </p>
                </div>
                <Switch 
                  checked={shippingOptions.freeShipping}
                  onCheckedChange={(checked) => updateShippingOptions({ freeShipping: checked })}
                />
              </div>
              <div>
                <Label>Free Shipping Minimum Order Amount</Label>
                <Input 
                  type="number" 
                  placeholder="0.00" 
                  className="mt-1"
                  value={shippingOptions.freeShippingMinimum}
                  onChange={(e) => updateShippingOptions({ freeShippingMinimum: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>
          </div>

          {/* Order Confirmation */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Order Confirmation</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Send Order Confirmation Email</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically send an email when an order is placed
                  </p>
                </div>
                <Switch 
                  checked={orderSettings.sendEmailConfirmation}
                  onCheckedChange={(checked) => updateOrderSettings({ sendEmailConfirmation: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Send SMS Notification</Label>
                  <p className="text-sm text-muted-foreground">
                    Send SMS updates about order status
                  </p>
                </div>
                <Switch 
                  checked={orderSettings.sendSMSNotification}
                  onCheckedChange={(checked) => updateOrderSettings({ sendSMSNotification: checked })}
                />
              </div>
            </div>
          </div>

          {/* Additional Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Additional Settings</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Guest Checkout</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to check out without creating an account
                  </p>
                </div>
                <Switch 
                  checked={orderSettings.guestCheckout}
                  onCheckedChange={(checked) => updateOrderSettings({ guestCheckout: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Order Notes</Label>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to add notes to their order
                  </p>
                </div>
                <Switch 
                  checked={orderSettings.orderNotes}
                  onCheckedChange={(checked) => updateOrderSettings({ orderNotes: checked })}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label>Terms and Conditions</Label>
                  <p className="text-sm text-muted-foreground">
                    Require customers to accept terms before checkout
                  </p>
                </div>
                <Switch 
                  checked={orderSettings.termsAndConditions}
                  onCheckedChange={(checked) => updateOrderSettings({ termsAndConditions: checked })}
                />
              </div>
            </div>
          </div>

      <Button className="w-full">Save Changes</Button>
    </div>
  </Card>
  );
}
