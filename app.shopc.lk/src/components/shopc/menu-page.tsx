"use client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { GripVertical, Plus, Trash2 } from "lucide-react";
import { useShopcStore } from "@/lib/stores/storefront-store";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import {
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

interface MenuPageProps {
  isDemo?: boolean;
}

interface SortableItemProps {
  id: string;
  item: {
    id: string;
    label: string;
    link: string;
  };
  onUpdate: (id: string, field: string, value: string) => void;
  onRemove: (id: string) => void;
}

function SortableItem({ id, item, onUpdate, onRemove }: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-4 p-4 bg-card border border-border rounded-lg"
    >
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-2 hover:bg-muted rounded"
      >
        <GripVertical className="h-5 w-5 text-muted-foreground" />
      </div>
      <div className="flex-1 space-y-3">
        <Input
          placeholder="Name"
          value={item.label}
          onChange={(e) => onUpdate(item.id, "label", e.target.value)}
        />
        <Input
          placeholder="URL"
          value={item.link}
          onChange={(e) => onUpdate(item.id, "link", e.target.value)}
        />
      </div>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onRemove(item.id)}
        className="text-muted-foreground hover:text-destructive"
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
}

export function MenuPage({ isDemo = false }: MenuPageProps) {
  const { 
    menuItems, 
    addMenuItem, 
    updateMenuItem, 
    removeMenuItem,
    setMenuItems
  } = useShopcStore();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = menuItems.findIndex((item) => item.id === active.id);
      const newIndex = menuItems.findIndex((item) => item.id === over?.id);

      const newMenuItems = arrayMove(menuItems, oldIndex, newIndex);
      setMenuItems(newMenuItems);
    }
  };

  const handleAddMenuItem = () => {
    const newItem = {
      id: Date.now().toString(),
      label: "",
      link: "",
    };
    addMenuItem(newItem);
  };

  const handleRemoveMenuItem = (id: string) => {
    removeMenuItem(id);
  };

  const handleUpdateMenuItem = (
    id: string,
    field: string,
    value: string
  ) => {
    updateMenuItem(id, { [field]: value });
  };

  return (
    <Card className="p-6">
      <div className="space-y-6">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={menuItems.map(item => item.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {menuItems.map((item) => (
                <SortableItem
                  key={item.id}
                  id={item.id}
                  item={item}
                  onUpdate={handleUpdateMenuItem}
                  onRemove={handleRemoveMenuItem}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        <Button
          onClick={handleAddMenuItem}
          variant="outline"
          className="w-full border-dashed border-2 h-12 text-muted-foreground hover:text-foreground"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add menu
        </Button>

        <Button className="w-full bg-foreground text-background hover:bg-foreground/90">
          Save
        </Button>
      </div>
    </Card>
  );
}
