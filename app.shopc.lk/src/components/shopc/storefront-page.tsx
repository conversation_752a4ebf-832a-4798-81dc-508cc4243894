"use client";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Package,
  Clock,
  Link2,
  Type,
  Image as ImageIcon,
  Video,
  Phone,
  MapPin,
  Eye,
} from "lucide-react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useStorefrontStore } from "@/lib/stores/storefront-store";

interface Card {
  type:
    | "product-category"
    | "countdown"
    | "link"
    | "text"
    | "image"
    | "video"
    | "connect"
    | "map";
  title?: string;
  description?: string;
  link?: string;
  imageUrl?: string;
  videoUrl?: string;
  location?: string;
  countdownDate?: string;
}

interface StorefrontPageProps {
  isDemo?: boolean;
}

export function StorefrontPage({ isDemo = false }: StorefrontPageProps) {
  const { cards, addCard, updateCard, removeCard } = useStorefrontStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    useStorefrontStore.persist.rehydrate();
    setIsHydrated(true);
  }, []);

  const handleAddCard = () => {
    addCard({ type: "product-category" });
  };

  if (!isHydrated) {
    return null; // or a loading spinner
  }

  return (
    <Card className="p-6">
      <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Button
                onClick={handleAddCard}
                className="flex-1"
                variant="default"
              >
                Add card
              </Button>
            </div>

            <div className="space-y-4">
              {cards.map((card, index) => (
                <Card key={index} className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Select
                        value={card.type}
                        onValueChange={(value: Card["type"]) => {
                          updateCard(index, { ...card, type: value });
                        }}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select card type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="product-category">
                            <div className="flex items-center gap-2">
                              <Package className="h-4 w-4" />
                              Product category
                            </div>
                          </SelectItem>
                          <SelectItem value="countdown">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              Countdown
                            </div>
                          </SelectItem>
                          <SelectItem value="link">
                            <div className="flex items-center gap-2">
                              <Link2 className="h-4 w-4" />
                              Link
                            </div>
                          </SelectItem>
                          <SelectItem value="text">
                            <div className="flex items-center gap-2">
                              <Type className="h-4 w-4" />
                              Text
                            </div>
                          </SelectItem>
                          <SelectItem value="image">
                            <div className="flex items-center gap-2">
                              <ImageIcon className="h-4 w-4" />
                              Image
                            </div>
                          </SelectItem>
                          <SelectItem value="video">
                            <div className="flex items-center gap-2">
                              <Video className="h-4 w-4" />
                              Video
                            </div>
                          </SelectItem>
                          <SelectItem value="connect">
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              Connect
                            </div>
                          </SelectItem>
                          <SelectItem value="map">
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              Map
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeCard(index)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Card Type Specific Fields */}
                    {card.type === "countdown" && (
                      <div className="space-y-4">
                        <div>
                          <Label>Title</Label>
                          <Input
                            placeholder="Enter title"
                            value={card.title || ""}
                            onChange={(e) => {
                              updateCard(index, {
                                ...card,
                                title: e.target.value,
                              });
                            }}
                          />
                        </div>
                        <div>
                          <Label>Description</Label>
                          <Textarea
                            placeholder="Enter description"
                            value={card.description || ""}
                            onChange={(e) => {
                              updateCard(index, {
                                ...card,
                                description: e.target.value,
                              });
                            }}
                          />
                        </div>
                        <div>
                          <Label>Countdown to date</Label>
                          <Input
                            type="datetime-local"
                            value={card.countdownDate || ""}
                            onChange={(e) => {
                              updateCard(index, {
                                ...card,
                                countdownDate: e.target.value,
                              });
                            }}
                          />
                        </div>
                      </div>
                    )}

                    {card.type === "image" && (
                      <div className="border-2 border-dashed border-border rounded-lg p-6">
                        <div className="text-center">
                          <ImageIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground mb-2">
                            Drag a file here or click to select one
                          </p>
                          <p className="text-xs text-muted-foreground">
                            File should not exceed 10mb. Recommended ratio is
                            1:1
                          </p>
                        </div>
                      </div>
                    )}

                    {card.type === "video" && (
                      <div className="space-y-4">
                        <div>
                          <Label>Title</Label>
                          <Input placeholder="Enter title" />
                        </div>
                        <div>
                          <Label>Video link</Label>
                          <Input placeholder="YouTube or TikTok video link" />
                        </div>
                      </div>
                    )}

                    {card.type === "map" && (
                      <div>
                        <Label>Location</Label>
                        <Input placeholder="Enter a location" />
                      </div>
                    )}

                    {card.type === "product-category" && (
                      <div className="space-y-4">
                        <div>
                          <Label>Category</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="test">
                                Test Category
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" className="flex-1">
                            List
                          </Button>
                          <Button variant="outline" className="flex-1">
                            Grid
                          </Button>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch />
                          <Label>Feature up to 4 products</Label>
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
        </div>
      </div>
    </Card>
  );
}
