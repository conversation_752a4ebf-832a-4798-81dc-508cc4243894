import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Package,
  Clock,
  Link2,
  Type,
  Image as ImageIcon,
  Video,
  Phone,
  MapPin,
} from "lucide-react";
import Image from "next/image";

interface PreviewCardProps {
  type:
    | "product-category"
    | "countdown"
    | "link"
    | "text"
    | "image"
    | "video"
    | "connect"
    | "map";
  title?: string;
  description?: string;
  link?: string;
  imageUrl?: string;
  videoUrl?: string;
  location?: string;
  countdownDate?: string;
}

const getCardIcon = (type: PreviewCardProps["type"]) => {
  switch (type) {
    case "product-category":
      return <Package className="h-4 w-4" />;
    case "countdown":
      return <Clock className="h-4 w-4" />;
    case "link":
      return <Link2 className="h-4 w-4" />;
    case "text":
      return <Type className="h-4 w-4" />;
    case "image":
      return <ImageIcon className="h-4 w-4" />;
    case "video":
      return <Video className="h-4 w-4" />;
    case "connect":
      return <Phone className="h-4 w-4" />;
    case "map":
      return <MapPin className="h-4 w-4" />;
    default:
      return null;
  }
};

export function PreviewCard({
  type,
  title,
  description,
  link,
  imageUrl,
  videoUrl,
  location,
  countdownDate,
}: PreviewCardProps) {
  const renderContent = () => {
    switch (type) {
      case "product-category":
        return (
          <div className="space-y-4">
            <h4 className="font-medium">Featured Products</h4>
            <div className="grid grid-cols-2 gap-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="aspect-square bg-muted rounded-md border border-border" />
              ))}
            </div>
          </div>
        );
      case "countdown":
        if (!countdownDate) return null;
        const targetDate = new Date(countdownDate);
        const now = new Date();
        const timeLeft = targetDate.getTime() - now.getTime();

        if (timeLeft <= 0) return <p>Countdown ended</p>;

        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

        return (
          <div className="text-center space-y-2">
            <div className="flex justify-center gap-4">
              <div>
                <div className="bg-primary text-primary-foreground px-3 py-2 rounded-md">
                  {days}
                </div>
                <p className="text-xs mt-1">Days</p>
              </div>
              <div>
                <div className="bg-primary text-primary-foreground px-3 py-2 rounded-md">
                  {hours}
                </div>
                <p className="text-xs mt-1">Hours</p>
              </div>
              <div>
                <div className="bg-primary text-primary-foreground px-3 py-2 rounded-md">
                  {minutes}
                </div>
                <p className="text-xs mt-1">Minutes</p>
              </div>
            </div>
          </div>
        );
      case "image":
        return imageUrl ? (
          <div className="aspect-video relative overflow-hidden rounded-lg">
            <Image
              src={imageUrl}
              alt={title || "Preview image"}
              fill
              className="object-cover"
            />
          </div>
        ) : (
          <div className="aspect-video bg-muted rounded-lg flex items-center justify-center border border-border">
            <ImageIcon className="h-8 w-8 text-muted-foreground" />
          </div>
        );
      case "video":
        return videoUrl ? (
          <div className="aspect-video relative overflow-hidden rounded-lg">
            <iframe
              src={videoUrl}
              className="w-full h-full"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        ) : (
          <div className="aspect-video bg-muted rounded-lg flex items-center justify-center border border-border">
            <Video className="h-8 w-8 text-muted-foreground" />
          </div>
        );
      case "connect":
        return (
          <div className="grid grid-cols-2 gap-2">
            <Button variant="outline" className="w-full">
              <Phone className="h-4 w-4 mr-2" />
              Call
            </Button>
            <Button variant="outline" className="w-full">
              <Link2 className="h-4 w-4 mr-2" />
              Website
            </Button>
          </div>
        );
      case "map":
        return location ? (
          <div className="aspect-video relative overflow-hidden rounded-lg">
            <iframe
              src={`https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${encodeURIComponent(
                location
              )}`}
              className="w-full h-full"
              allowFullScreen
            />
          </div>
        ) : (
          <div className="aspect-video bg-muted rounded-lg flex items-center justify-center border border-border">
            <MapPin className="h-8 w-8 text-muted-foreground" />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="overflow-hidden">
      <div className="p-4 space-y-4">
        <div className="flex items-center gap-2">
          {getCardIcon(type)}
          <span className="capitalize font-medium">
            {type.replace("-", " ")}
          </span>
        </div>
        {title && <h3 className="font-medium">{title}</h3>}
        {description && <p className="text-sm text-muted-foreground">{description}</p>}
        {renderContent()}
      </div>
    </Card>
  );
}
