import { PreviewCard } from "./preview-card";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Monitor, Smartphone } from "lucide-react";
import { useState } from "react";

interface PreviewContainerProps {
  isDemo?: boolean;
  cards: any[];
}

export function PreviewContainer({ isDemo, cards }: PreviewContainerProps) {
  const [viewMode, setViewMode] = useState<"mobile" | "desktop">("mobile");

  const content = (
    <div className="p-4">
      <div className="w-20 h-20 rounded-full bg-muted mx-auto mb-4 border-2 border-border" />
      <h3 className="text-xl font-bold text-center mb-4 text-foreground">
        {isDemo ? "Demo Store" : "A9 Stores"}
      </h3>
      <div className="space-y-4">
        {cards.map((card, index) => (
          <PreviewCard
            key={index}
            type={card.type}
            title={card.title}
            description={card.description}
            link={card.link}
            imageUrl={card.imageUrl}
            videoUrl={card.videoUrl}
            location={card.location}
            countdownDate={card.countdownDate}
          />
        ))}
      </div>
    </div>
  );

  return (
    <div className="sticky top-6">
      {/* Preview Toggle */}
      <div className="flex justify-center mb-6">
        <div className="bg-secondary p-1 rounded-lg inline-flex">
          <Button
            variant={viewMode === "mobile" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("mobile")}
            className="flex items-center gap-2"
          >
            <Smartphone className="h-4 w-4" />
            Mobile
          </Button>
          <Button
            variant={viewMode === "desktop" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("desktop")}
            className="flex items-center gap-2"
          >
            <Monitor className="h-4 w-4" />
            Desktop
          </Button>
        </div>
      </div>

      {viewMode === "mobile" ? (
        <div className="w-[300px] h-[600px] mx-auto border-8 border-foreground/20 dark:border-foreground/40 rounded-[3rem] relative overflow-hidden bg-foreground/5">
          <div className="absolute top-0 left-1/2 -translate-x-1/2 w-32 h-6 bg-foreground/80 rounded-b-xl" />
          <ScrollArea className="w-full h-full bg-background">
            {content}
          </ScrollArea>
        </div>
      ) : (
        <div className="w-full max-w-[800px] mx-auto">
          <div className="border border-border rounded-lg overflow-hidden shadow-lg">
            {/* Browser Header */}
            <div className="bg-muted/50 px-4 py-2 border-b border-border flex items-center gap-2">
              <div className="flex gap-1.5">
                <div className="w-3 h-3 rounded-full bg-red-500" />
                <div className="w-3 h-3 rounded-full bg-yellow-500" />
                <div className="w-3 h-3 rounded-full bg-green-500" />
              </div>
              <div className="flex-1 ml-4">
                <div className="bg-background rounded-md px-3 py-1 text-sm text-muted-foreground flex items-center justify-between border border-border">
                  <span>
                    {isDemo
                      ? "https://demo-store.take.app"
                      : "https://a9stores.take.app"}
                  </span>
                  <span>🔒</span>
                </div>
              </div>
            </div>
            {/* Browser Content */}
            <ScrollArea className="w-full h-[500px] bg-background">
              <div className="max-w-3xl mx-auto">{content}</div>
            </ScrollArea>
          </div>
        </div>
      )}
    </div>
  );
}
