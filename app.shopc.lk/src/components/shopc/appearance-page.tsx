"use client";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ColorPicker } from "@/components/ui/color-picker";
import { useState } from "react";
import Image from "next/image";
import { useShopcStore } from "@/lib/stores/storefront-store";

interface AppearancePageProps {
  isDemo?: boolean;
}

export function AppearancePage({ isDemo = false }: AppearancePageProps) {
  const { appearance, updateAppearance } = useShopcStore();

  return (
    <Card className="p-6">
      <div className="space-y-8">
            {/* Profile Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Profile</h3>
              <div className="flex items-center justify-center border-2 border-dashed border-border rounded-lg p-6">
                <div className="text-center">
                  <div className="w-24 h-24 bg-muted rounded-full mx-auto mb-4 overflow-hidden border border-border">
                    <Image
                      src="/placeholder-profile.svg"
                      alt="Profile"
                      width={96}
                      height={96}
                      className="object-cover"
                    />
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    Drag a file here or click to select one
                  </p>
                  <p className="text-xs text-muted-foreground">
                    File should not exceed 10mb. Recommended ratio is 1:1
                  </p>
                </div>
              </div>
              <Input 
                placeholder="Store Name" 
                className="mb-4"
                value={appearance.storeName}
                onChange={(e) => updateAppearance({ storeName: e.target.value })}
              />
              <Input 
                placeholder="Description (optional)"
                value={appearance.description}
                onChange={(e) => updateAppearance({ description: e.target.value })}
              />
            </div>

            {/* Colors Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Colors</h3>
              <div className="space-y-4">
                <div>
                  <Label>Primary Color</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <ColorPicker
                      value={appearance.primaryColor}
                      onChange={(color) => updateAppearance({ primaryColor: color })}
                      className="w-10 h-10"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    This color reflects your brand. It will be used for buttons
                    and highlights.
                  </p>
                </div>
                <div>
                  <Label>Background Color</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <ColorPicker
                      value={appearance.backgroundColor}
                      onChange={(color) => updateAppearance({ backgroundColor: color })}
                      className="w-10 h-10"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Banner Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Banner</h3>
              <div className="space-y-4">
                <div>
                  <Label>Text</Label>
                  <Input 
                    placeholder="Enter banner text" 
                    className="mt-1"
                    value={appearance.bannerText}
                    onChange={(e) => updateAppearance({ bannerText: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {appearance.bannerText.length}/45 characters
                  </p>
                </div>
                <div>
                  <Label>Link (optional)</Label>
                  <Input 
                    placeholder="https://" 
                    className="mt-1"
                    value={appearance.bannerLink}
                    onChange={(e) => updateAppearance({ bannerLink: e.target.value })}
                  />
                </div>
              </div>
            </div>

        <Button className="w-full">Save Changes</Button>
      </div>
    </Card>
  );
}
