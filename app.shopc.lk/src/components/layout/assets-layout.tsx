"use client";

import { usePathname, useRouter } from "next/navigation";
import {
  Box,
  Package,
  UserCheck,
  ArrowLeftRight,
  Wrench,
} from "lucide-react";
import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";

import DashboardLayout, {
  SectionCategory,
} from "@/components/layout/dashboard-layout";

// Import asset sheet components
import { AssetTypeSheet, ImportAssetTypesSheet } from "@/components/asset-types";
import { AssetSheet, ImportAssetsSheet } from "../assets";
import { assetTypeKeys } from "@/lib/asset-types/hooks";

// Define query keys for assets modules
export const assetKeys = {
  all: ["assets"] as const,
  lists: () => [...assetKeys.all, "list"] as const,
  list: (filters: any) => [...assetKeys.lists(), filters] as const,
  details: () => [...assetKeys.all, "detail"] as const,
  detail: (id: string) => [...assetKeys.details(), id] as const,
};



// Asset categories for the tabs with their routes
const ALL_ASSET_CATEGORIES: SectionCategory[] = [
  {
    name: "assets",
    path: "assets",
    icon: Box,
    module: ModuleType.ASSETS,
    label: "Assets",
  },
  {
    name: "asset-types",
    path: "assets-types",
    icon: Package,
    module: ModuleType.ASSETS_TYPES,
    label: "Asset Types",
  },
  {
    name: "asset-allocations",
    path: "allocations",
    icon: UserCheck,
    module: ModuleType.ASSETS_ALLOCATED,
    label: "Asset Allocations",
  },
  {
    name: "asset-revoked",
    path: "revoked",
    icon: ArrowLeftRight,
    module: ModuleType.ASSETS_REVOKED,
    label: "Asset Revoked",
  },
  {
    name: "asset-maintenance",
    path: "maintenance",
    icon: Wrench,
    module: ModuleType.ASSETS_MAINTENANCE,
    label: "Asset Maintenance",
  },
];

export default function AssetsLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  const [openAssetSheet, setOpenAssetSheet] = useState(false);
  const [openAssetTypeSheet, setOpenAssetTypeSheet] = useState(false);
  const [openImportAssetSheet, setOpenImportAssetSheet] = useState(false);
  const [openImportAssetTypesSheet, setOpenImportAssetTypesSheet] =
    useState(false);

  // Handle success for asset operations - invalidate queries and refresh
  const handleAssetSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetKeys.lists() });
    router.refresh();
  }, [queryClient, router]);


  const handleAssetTypeSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ["asset-types"] });
    router.refresh();
  }, [router, queryClient]);

  const handleImportAssetSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetKeys.lists() });
    router.refresh();
  }, [router, queryClient]);

  const handleImportAssetTypesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: assetTypeKeys.list() });
    router.refresh();
  }, [router, queryClient]);

  const handleAddNew = () => {
    // For other routes, open the appropriate sheet
    if (pathname.includes("/assets-types")) {
      setOpenAssetTypeSheet(true);
    } else {
      // Default to assets sheet
      setOpenAssetSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    // Placeholder for import functionality
    // Can be extended for specific asset import handlers
    if (pathname.includes("/assets-types")) {
      setOpenImportAssetTypesSheet(true);
    } else {
      setOpenImportAssetSheet(true);
    }
  }, [pathname]);

  const getAddButtonText = () => {
    if (pathname.includes("/assets-types")) return "Add New Asset Type";
    return "Add New Asset";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/assets-types")) return "Import Asset Types";
    return "Import Assets";
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Assets"
      categories={ALL_ASSET_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={{
        text: getImportButtonText(),
        onClick: handleImport,
      }}
      sheets={
        <>
          <AssetTypeSheet
            assetType={null}
            open={openAssetTypeSheet}
            onOpenChange={setOpenAssetTypeSheet}
            isDemo={isDemo}
            onSuccess={handleAssetTypeSuccess}
          />
          <AssetSheet
            asset={null}
            open={openAssetSheet}
            onOpenChange={setOpenAssetSheet}
            isDemo={isDemo}
            onSuccess={handleAssetSuccess}
          />
          <ImportAssetsSheet
            open={openImportAssetSheet}
            onOpenChange={setOpenImportAssetSheet}
            isDemo={isDemo}
            onSuccess={handleImportAssetSuccess}
          />
          <ImportAssetTypesSheet
            open={openImportAssetTypesSheet}
            onOpenChange={setOpenImportAssetTypesSheet}
            isDemo={isDemo}
            onSuccess={handleImportAssetTypesSuccess}
          />
          {/* <AssetAllocationSheet
            allocation={null}
            open={openAssetAllocationSheet}
            onOpenChange={setOpenAssetAllocationSheet}
            isDemo={isDemo}
            onSuccess={handleAssetAllocationSuccess}
          />
          <AssetMaintenanceSheet
            maintenance={null}
            open={openAssetMaintenanceSheet}
            onOpenChange={setOpenAssetMaintenanceSheet}
            isDemo={isDemo}
            onSuccess={handleAssetMaintenanceSuccess}
          />
          <AssetRevokedSheet
            assetRevoked={null}
            open={openAssetRevokedSheet}
            onOpenChange={setOpenAssetRevokedSheet}
            isDemo={isDemo}
            onSuccess={handleAssetRevokedSuccess}
          /> */}
        </>
      }
    >
      {children}
    </DashboardLayout>
  );
}
