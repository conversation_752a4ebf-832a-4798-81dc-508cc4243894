"use client";

import { usePathname, useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Setting<PERSON> } from "lucide-react";
import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";

import DashboardLayout, {
  SectionCategory,
} from "@/components/layout/dashboard-layout";

// Service Order Priority imports
import { ServiceOrderPrioritySheet } from "@/components/service-order-priorities/service-order-priority-sheet";
import { ImportServiceOrderPrioritiesSheet } from "@/components/service-order-priorities/import-service-order-priorities-sheet";
import { serviceOrderPriorityKeys } from "@/lib/service-order-priorities/hooks";

// Service Order Status imports
import { ServiceOrderStatusSheet } from "@/components/service-order-statuses/service-order-status-sheet";
import { serviceOrderStatusKeys } from "@/lib/service-order-statuses/hooks";

// Service Order Type imports
import { ServiceOrderTypeSheet } from "@/components/service-order-types/service-order-type-sheet";
import { ImportServiceOrderTypesSheet } from "@/components/service-order-types/import-service-order-types-sheet";
import { serviceOrderTypeKeys } from "@/lib/service-order-types/hooks";

// Service Order imports
import { ServiceOrderSheet } from "@/components/service-orders/service-order-sheet";
import { ImportServiceOrdersSheet } from "@/components/service-orders/import-service-orders-sheet";
import { serviceOrderKeys } from "@/lib/service-orders/hooks";

// Service Order categories for the tabs with their routes
const ALL_SERVICE_ORDER_CATEGORIES: SectionCategory[] = [
  {
    name: "service-orders",
    path: "service-orders",
    icon: Wrench,
    module: ModuleType.SERVICE_ORDERS,
    label: "Service Orders",
  },
  {
    name: "service-order-statuses",
    path: "service-order-statuses",
    icon: CheckCircle,
    module: ModuleType.SERVICE_ORDERS,
    label: "Statuses",
  },
  {
    name: "service-order-priorities",
    path: "service-order-priorities",
    icon: AlertTriangle,
    module: ModuleType.SERVICE_ORDERS,
    label: "Priorities",
  },
  {
    name: "service-order-types",
    path: "service-order-types",
    icon: Settings,
    module: ModuleType.SERVICE_ORDERS,
    label: "Types",
  },
];

export default function ServiceOrderLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  // Sheet states
  const [openServiceOrderSheet, setOpenServiceOrderSheet] = useState(false);
  const [openServiceOrderPrioritySheet, setOpenServiceOrderPrioritySheet] =
    useState(false);
  const [openServiceOrderStatusSheet, setOpenServiceOrderStatusSheet] =
    useState(false);
  const [openServiceOrderTypeSheet, setOpenServiceOrderTypeSheet] =
    useState(false);

  // Import sheet states
  const [openImportServiceOrdersSheet, setOpenImportServiceOrdersSheet] =
    useState(false);
  const [
    openImportServiceOrderPrioritiesSheet,
    setOpenImportServiceOrderPrioritiesSheet,
  ] = useState(false);
  const [
    openImportServiceOrderTypesSheet,
    setOpenImportServiceOrderTypesSheet,
  ] = useState(false);

  // Success handlers for query invalidation
  const handleServiceOrderSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleServiceOrderPrioritySuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderPriorityKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleServiceOrderStatusSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderStatusKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleServiceOrderTypeSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderTypeKeys.all });
    router.refresh();
  }, [queryClient, router]);

  // Import success handlers
  const handleImportServiceOrdersSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleImportServiceOrderPrioritiesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderPriorityKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleImportServiceOrderTypesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceOrderTypeKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleAddNew = () => {
    // Open the appropriate sheet for each route
    if (
      pathname.includes("/service-orders") &&
      !pathname.includes("/service-order-")
    ) {
      setOpenServiceOrderSheet(true);
    } else if (pathname.includes("/service-order-priorities")) {
      setOpenServiceOrderPrioritySheet(true);
    } else if (pathname.includes("/service-order-statuses")) {
      setOpenServiceOrderStatusSheet(true);
    } else if (pathname.includes("/service-order-types")) {
      setOpenServiceOrderTypeSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    if (
      pathname.includes("/service-orders") &&
      !pathname.includes("/service-order-")
    ) {
      setOpenImportServiceOrdersSheet(true);
    } else if (pathname.includes("/service-order-priorities")) {
      setOpenImportServiceOrderPrioritiesSheet(true);
    } else if (pathname.includes("/service-order-types")) {
      setOpenImportServiceOrderTypesSheet(true);
    }
  }, [pathname]);

  const getAddButtonText = () => {
    if (pathname.includes("/service-order-priorities")) return "Add New Priority";
    if (pathname.includes("/service-order-statuses")) return "Add New Status";
    if (pathname.includes("/service-order-types")) return "Add New Type";
    return "Add New Service Order";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/service-order-priorities")) return "Import Priorities";
    if (pathname.includes("/service-order-types")) return "Import Types";
    return "Import Service Orders";
  };

  const getCurrentRouteSheets = () => {
    if (pathname.includes("/service-order-priorities")) {
      return (
        <>
          <ServiceOrderPrioritySheet
            serviceOrderPriority={null}
            open={openServiceOrderPrioritySheet}
            onOpenChange={setOpenServiceOrderPrioritySheet}
            isDemo={isDemo}
            onSuccess={handleServiceOrderPrioritySuccess}
          />
          <ImportServiceOrderPrioritiesSheet
            open={openImportServiceOrderPrioritiesSheet}
            onOpenChange={setOpenImportServiceOrderPrioritiesSheet}
            isDemo={isDemo}
            onSuccess={handleImportServiceOrderPrioritiesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/service-order-statuses")) {
      return (
        <>
          <ServiceOrderStatusSheet
            serviceOrderStatus={null}
            open={openServiceOrderStatusSheet}
            onOpenChange={setOpenServiceOrderStatusSheet}
            isDemo={isDemo}
            onSuccess={handleServiceOrderStatusSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/service-order-types")) {
      return (
        <>
          <ServiceOrderTypeSheet
            serviceOrderType={null}
            open={openServiceOrderTypeSheet}
            onOpenChange={setOpenServiceOrderTypeSheet}
            isDemo={isDemo}
            onSuccess={handleServiceOrderTypeSuccess}
          />
          <ImportServiceOrderTypesSheet
            open={openImportServiceOrderTypesSheet}
            onOpenChange={setOpenImportServiceOrderTypesSheet}
            isDemo={isDemo}
            onSuccess={handleImportServiceOrderTypesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/service-orders")) {
      return (
        <>
          <ServiceOrderSheet
            serviceOrder={null}
            open={openServiceOrderSheet}
            onOpenChange={setOpenServiceOrderSheet}
            isDemo={isDemo}
            onSuccess={handleServiceOrderSuccess}
          />
          <ImportServiceOrdersSheet
            open={openImportServiceOrdersSheet}
            onOpenChange={setOpenImportServiceOrdersSheet}
            isDemo={isDemo}
            onSuccess={handleImportServiceOrdersSuccess}
          />
        </>
      );
    }

    return null;
  };

  const shouldShowImport = () => {
    return (
      pathname.includes("/service-order-priorities") ||
      pathname.includes("/service-order-types") ||
      (pathname.includes("/service-orders") && !pathname.includes("/service-order-"))
    );
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Service Orders"
      categories={ALL_SERVICE_ORDER_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={shouldShowImport() ? {
        text: getImportButtonText(),
        onClick: handleImport,
      } : undefined}
      sheets={getCurrentRouteSheets()}
    >
      {children}
    </DashboardLayout>
  );
}