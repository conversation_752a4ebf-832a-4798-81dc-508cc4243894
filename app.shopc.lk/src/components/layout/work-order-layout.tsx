"use client";

import { usePathname, useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle, Check<PERSON>ir<PERSON> } from "lucide-react";
import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";

import DashboardLayout, {
  SectionCategory,
  NavigationViewType,
} from "@/components/layout/dashboard-layout";

// Work Order Priority imports
import { WorkOrderPrioritySheet } from "@/components/work-order-priorities/work-order-priority-sheet";
import { ImportWorkOrderPrioritiesSheet } from "@/components/work-order-priorities/import-work-order-priorities-sheet";
import { workOrderPriorityKeys } from "@/lib/work-order-priorities/hooks";

// Work Order Status imports
import { WorkOrderStatusSheet } from "@/components/work-order-statuses/work-order-status-sheet";
import { ImportWorkOrderStatusesSheet } from "@/components/work-order-statuses/import-work-order-statuses-sheet";
import { workOrderStatusKeys } from "@/lib/work-order-statuses/hooks";

// Work Order imports
import { WorkOrderSheet } from "@/components/work-orders/work-order-sheet";
import { ImportWorkOrdersSheet } from "@/components/work-orders/import-work-orders-sheet";
import { workOrderKeys } from "@/lib/work-orders/hooks";

// Work Order categories for the tabs with their routes
const ALL_WORK_ORDER_CATEGORIES: SectionCategory[] = [
  {
    name: "work-orders",
    path: "work-orders",
    icon: Wrench,
    module: ModuleType.WORKORDERS,
    label: "Work Orders",
  },
  {
    name: "work-order-priorities",
    path: "work-order-priorities",
    icon: AlertTriangle,
    module: ModuleType.WORKORDERS,
    label: "Priorities",
  },
  {
    name: "work-order-statuses",
    path: "work-order-statuses",
    icon: CheckCircle,
    module: ModuleType.WORKORDERS,
    label: "Statuses",
  },
];

export default function WorkOrderLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  // Sheet states
  const [openWorkOrderSheet, setOpenWorkOrderSheet] = useState(false);
  const [openWorkOrderPrioritySheet, setOpenWorkOrderPrioritySheet] =
    useState(false);
  const [openWorkOrderStatusSheet, setOpenWorkOrderStatusSheet] =
    useState(false);

  // Import sheet states
  const [openImportWorkOrdersSheet, setOpenImportWorkOrdersSheet] =
    useState(false);
  const [
    openImportWorkOrderPrioritiesSheet,
    setOpenImportWorkOrderPrioritiesSheet,
  ] = useState(false);
  const [
    openImportWorkOrderStatusesSheet,
    setOpenImportWorkOrderStatusesSheet,
  ] = useState(false);

  // Success handlers for query invalidation
  const handleWorkOrderSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: workOrderKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleWorkOrderPrioritySuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleWorkOrderStatusSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: workOrderStatusKeys.all });
    router.refresh();
  }, [queryClient, router]);

  // Import success handlers
  const handleImportWorkOrdersSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: workOrderKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleImportWorkOrderPrioritiesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: workOrderPriorityKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleImportWorkOrderStatusesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: workOrderStatusKeys.all });
    router.refresh();
  }, [queryClient, router]);

  const handleAddNew = () => {
    // Open the appropriate sheet for each route
    if (
      pathname.includes("/work-orders") &&
      !pathname.includes("/work-order-")
    ) {
      setOpenWorkOrderSheet(true);
    } else if (pathname.includes("/work-order-priorities")) {
      setOpenWorkOrderPrioritySheet(true);
    } else if (pathname.includes("/work-order-statuses")) {
      setOpenWorkOrderStatusSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    if (
      pathname.includes("/work-orders") &&
      !pathname.includes("/work-order-")
    ) {
      setOpenImportWorkOrdersSheet(true);
    } else if (pathname.includes("/work-order-priorities")) {
      setOpenImportWorkOrderPrioritiesSheet(true);
    } else if (pathname.includes("/work-order-statuses")) {
      setOpenImportWorkOrderStatusesSheet(true);
    }
  }, [pathname]);

  const getAddButtonText = () => {
    if (pathname.includes("/work-order-priorities")) return "Add New Priority";
    if (pathname.includes("/work-order-statuses")) return "Add New Status";
    return "Add New Work Order";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/work-order-priorities")) return "Import Priorities";
    if (pathname.includes("/work-order-statuses")) return "Import Statuses";
    return "Import Work Orders";
  };

  const getCurrentRouteSheets = () => {
    if (pathname.includes("/work-order-priorities")) {
      return (
        <>
          <WorkOrderPrioritySheet
            workOrderPriority={null}
            open={openWorkOrderPrioritySheet}
            onOpenChange={setOpenWorkOrderPrioritySheet}
            isDemo={isDemo}
            onSuccess={handleWorkOrderPrioritySuccess}
          />
          <ImportWorkOrderPrioritiesSheet
            open={openImportWorkOrderPrioritiesSheet}
            onOpenChange={setOpenImportWorkOrderPrioritiesSheet}
            isDemo={isDemo}
            onSuccess={handleImportWorkOrderPrioritiesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/work-order-statuses")) {
      return (
        <>
          <WorkOrderStatusSheet
            workOrderStatus={null}
            open={openWorkOrderStatusSheet}
            onOpenChange={setOpenWorkOrderStatusSheet}
            isDemo={isDemo}
            onSuccess={handleWorkOrderStatusSuccess}
          />
          <ImportWorkOrderStatusesSheet
            open={openImportWorkOrderStatusesSheet}
            onOpenChange={setOpenImportWorkOrderStatusesSheet}
            isDemo={isDemo}
            onSuccess={handleImportWorkOrderStatusesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/work-orders")) {
      return (
        <>
          <WorkOrderSheet
            workOrder={null}
            open={openWorkOrderSheet}
            onOpenChange={setOpenWorkOrderSheet}
            isDemo={isDemo}
            onSuccess={handleWorkOrderSuccess}
          />
          <ImportWorkOrdersSheet
            open={openImportWorkOrdersSheet}
            onOpenChange={setOpenImportWorkOrdersSheet}
            isDemo={isDemo}
            onSuccess={handleImportWorkOrdersSuccess}
          />
        </>
      );
    }

    return null;
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Work Orders"
      categories={ALL_WORK_ORDER_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={{
        text: getImportButtonText(),
        onClick: handleImport,
      }}
      sheets={getCurrentRouteSheets()}
    >
      {children}
    </DashboardLayout>
  );
}
