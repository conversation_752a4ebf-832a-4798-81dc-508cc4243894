"use client";

import { usePathname, useRouter } from "next/navigation";
import {
  Package,
  Tags,
  Award,
  Clock,
  Wrench,
  Palette,
  <PERSON><PERSON>heck,
  Ruler,
} from "lucide-react";
import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { BrandSheet } from "@/components/brands/brand-sheet";
import { WarrantyTemplateSheet } from "@/components/warranty-templates/warranty-template-sheet";
import { VariationSheet } from "@/components/variations/variation-sheet";
import { warrantyTemplateKeys } from "@/lib/warranty-templates/hooks";
import { brandKeys } from "@/lib/brands/hooks";
import { variationKeys } from "@/lib/variations/hooks";
import { useModules } from "@/contexts/auth-contexts";
import { ModuleType } from "@/types/business-modules";

import DashboardLayout, {
  SectionCategory,
  NavigationViewType,
} from "@/components/layout/dashboard-layout";
import { ImportVariationsSheet } from "../variations/import-variations-sheet";
import { ImportBrandsSheet } from "../brands/import-brands-sheet";
import { categoryKeys } from "@/lib/categories";
import { CategorySheet, ImportCategoriesSheet } from "../categories";
import { MealPeriodSheet } from "../meal-periods/meal-period-sheet";
import { mealPeriodKeys } from "@/lib/meal-periods/hooks";
import { ModifierSheet } from "../modifiers/modifier-sheet";
import { ImportModifiersSheet } from "../modifiers/import-modifiers-sheet";
import { modifierGroupKeys } from "@/lib/modifiers/hooks";
import { ServiceCategorySheet } from "../service-categories/service-category-sheet";
import { ImportServiceCategoriesSheet } from "../service-categories/import-service-categories-sheet";
import { ImportServicesSheet } from "../services/import-services-sheet";
import { ServiceSheet } from "../services/service-sheet";
import { serviceKeys } from "@/lib/services/hooks";
import { serviceCategoryKeys } from "@/lib/service-categories/hooks";
import { UnitSheet } from "../units/unit-sheet";
import { ImportUnitsSheet } from "../units/import-units-sheet";
import { unitKeys } from "@/lib/units/hooks";
import { ProductSheet } from "@/components/products/product-sheet";
import { ImportProductsSheet } from "@/components/products/import-products-sheet";
import { productKeys } from "@/lib/products/hooks";

// Item categories for the tabs with their routes
const ALL_ITEM_CATEGORIES: SectionCategory[] = [
  {
    name: "products",
    path: "products",
    icon: Package,
    module: ModuleType.PRODUCTS,
    label: "Products",
  },
  {
    name: "services",
    path: "services",
    icon: Wrench,
    module: ModuleType.SERVICES,
    label: "Services",
  },
  {
    name: "categories",
    path: "categories",
    icon: Tags,
    module: ModuleType.CATEGORIES,
    label: "Categories",
  },
  {
    name: "brands",
    path: "brands",
    icon: Award,
    module: ModuleType.BRANDS,
    label: "Brands",
  },
  {
    name: "warranties",
    path: "warranties",
    icon: ShieldCheck,
    module: ModuleType.WARRANCIES,
    label: "Warranties",
  },
  {
    name: "meal-periods",
    path: "meal-periods",
    icon: Clock,
    module: ModuleType.MEAL_PERIODS,
    label: "Meal Periods",
  },
  {
    name: "variations",
    path: "variations",
    icon: Palette,
    module: ModuleType.VARIANTS,
    label: "Variants",
  },
  {
    name: "modifiers",
    path: "modifiers",
    icon: ShieldCheck,
    module: ModuleType.MODIFIERS,
    label: "Modifiers",
  },
  {
    name: "units",
    path: "units",
    icon: Ruler,
    module: ModuleType.UNITS,
    label: "Units",
  },
  {
    name: "service-categories",
    path: "service-categories",
    icon: ShieldCheck,
    module: ModuleType.SERVICE_TYPES,
    label: "Service Categories",
  },
  {
    name: "services",
    path: "services",
    icon: ShieldCheck,
    module: ModuleType.SERVICES,
    label: "Services",
  },
];

export default function ItemsLayout({
  children,
  isDemo = false,
}: {
  children: React.ReactNode;
  isDemo?: boolean;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { hasAccess } = useModules();

  const [openCategorySheet, setOpenCategorySheet] = useState(false);
  const [openBrandSheet, setOpenBrandSheet] = useState(false);
  const [openWarrantySheet, setOpenWarrantySheet] = useState(false);
  const [openMealPeriodSheet, setOpenMealPeriodSheet] = useState(false);
  const [openVariantSheet, setOpenVariantSheet] = useState(false);
  const [openImportVariationsSheet, setOpenImportVariationsSheet] =
    useState(false);
  const [openImportBrandsSheet, setOpenImportBrandsSheet] = useState(false);
  const [openImportCategoriesSheet, setOpenImportCategoriesSheet] =
    useState(false);
  const [openModifierSheet, setOpenModifierSheet] = useState(false);
  const [openImportModifiersSheet, setOpenImportModifiersSheet] =
    useState(false);

  // Handle success for category operations - invalidate queries and refresh
  const handleCategorySuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: categoryKeys.list() });
    router.refresh();
  }, [queryClient, router]);

  const handleBrandSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: brandKeys.list() });
    router.refresh();
  }, [router, queryClient]);

  const handleWarrantySuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: warrantyTemplateKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleModifierSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: modifierGroupKeys.all });
    router.refresh();
  }, [router, queryClient]);

  // const handleMealPeriodSuccess = useCallback(() => {
  //   queryClient.invalidateQueries({ queryKey: mealPeriodKeys.list() });
  //   router.refresh();
  // }, [router, queryClient]);

  const handleImportVariationsSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: variationKeys.list() });
    router.refresh();
  }, [router, queryClient]);

  const handleImportBrandsSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: brandKeys.list() });
    router.refresh();
  }, [router, queryClient]);

  const handleImportCategoriesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: categoryKeys.list() });
    router.refresh();
  }, [router, queryClient]);

  const handleImportModifiersSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: modifierGroupKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const [openServiceTypeSheet, setOpenServiceTypeSheet] = useState(false);
  const [openImportServiceTypesSheet, setOpenImportServiceTypesSheet] =
    useState(false);
  const [openServiceSheet, setOpenServiceSheet] = useState(false);
  const [openImportServicesSheet, setOpenImportServicesSheet] = useState(false);
  const [openUnitSheet, setOpenUnitSheet] = useState(false);
  const [openImportUnitsSheet, setOpenImportUnitsSheet] = useState(false);
  const [openProductSheet, setOpenProductSheet] = useState(false);
  const [openImportProductsSheet, setOpenImportProductsSheet] = useState(false);

  const handleServiceTypeSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleServiceSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleImportServiceTypesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceCategoryKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleImportServicesSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: serviceKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleUnitSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: unitKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleImportUnitsSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: unitKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleProductSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: productKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleImportProductsSuccess = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: productKeys.all });
    router.refresh();
  }, [router, queryClient]);

  const handleAddNew = () => {
    // For items, navigate to new page
    const basePath = isDemo ? "/demo" : "/admin";
    if (pathname.includes("/items") || pathname === basePath) {
      router.push(`${basePath}/items/new`);
      return;
    }

    // For products, open the product sheet
    if (pathname.includes("/products")) {
      setOpenProductSheet(true);
      return;
    }

    // For other routes, open the appropriate sheet
    if (pathname.includes("/categories")) {
      setOpenCategorySheet(true);
    } else if (pathname.includes("/brands")) {
      setOpenBrandSheet(true);
    } else if (pathname.includes("/warranty-templates")) {
      setOpenWarrantySheet(true);
    } else if (pathname.includes("/meal-periods")) {
      setOpenMealPeriodSheet(true);
    } else if (pathname.includes("/variations")) {
      setOpenVariantSheet(true);
    } else if (pathname.includes("/modifiers")) {
      setOpenModifierSheet(true);
    } else if (pathname.includes("/service-types")) {
      setOpenServiceTypeSheet(true);
    } else if (pathname.includes("/services")) {
      setOpenServiceSheet(true);
    } else if (pathname.includes("/units")) {
      setOpenUnitSheet(true);
    }
  };

  const handleImport = useCallback(() => {
    if (pathname.includes("/variations")) {
      setOpenImportVariationsSheet(true);
    } else if (pathname.includes("/brands")) {
      setOpenImportBrandsSheet(true);
    } else if (pathname.includes("/categories")) {
      setOpenImportCategoriesSheet(true);
    } else if (pathname.includes("/modifiers")) {
      setOpenImportModifiersSheet(true);
    } else if (pathname.includes("/service-types")) {
      setOpenImportServiceTypesSheet(true);
    } else if (pathname.includes("/services")) {
      setOpenImportServicesSheet(true);
    } else if (pathname.includes("/units")) {
      setOpenImportUnitsSheet(true);
    } else if (pathname.includes("/products")) {
      setOpenImportProductsSheet(true);
    }
    // Add other import handlers for categories, warranties, etc. as needed
  }, [pathname]);

  const getAddButtonText = () => {
    if (pathname.includes("/categories")) return "Add New Category";
    if (pathname.includes("/brands")) return "Add New Brand";
    if (pathname.includes("/warranty-templates")) return "Add New Warranty";
    if (pathname.includes("/meal-periods")) return "Add New Meal Period";
    if (pathname.includes("/variations")) return "Add New Variant";
    if (pathname.includes("/modifiers")) return "Add New Modifier";
    if (pathname.includes("/service-categories"))
      return "Import Service Categories";
    if (pathname.includes("/services")) return "Add New Service";
    if (pathname.includes("/units")) return "Add New Unit";
    if (pathname.includes("/products")) return "Add New Product";
    return "Add New Item";
  };

  const getImportButtonText = () => {
    if (pathname.includes("/categories")) return "Import Categories";
    if (pathname.includes("/brands")) return "Import Brands";
    if (pathname.includes("/warranties")) return "Import Warranties";
    if (pathname.includes("/meal-periods")) return "Import Meal Periods";
    if (pathname.includes("/variations")) return "Import Variations";
    if (pathname.includes("/modifiers")) return "Import Modifiers";
    if (pathname.includes("/service-categories"))
      return "Import Service Categories";
    if (pathname.includes("/services")) return "Import Services";
    if (pathname.includes("/units")) return "Import Units";
    if (pathname.includes("/products")) return "Import Products";
    return "Import Items";
  };

  const getCurrentRouteSheets = () => {
    if (pathname.includes("/categories")) {
      return (
        <>
          <CategorySheet
            category={null}
            open={openCategorySheet}
            onOpenChange={setOpenCategorySheet}
            isDemo={isDemo}
            onSuccess={handleCategorySuccess}
          />
          <ImportCategoriesSheet
            open={openImportCategoriesSheet}
            onOpenChange={setOpenImportCategoriesSheet}
            isDemo={isDemo}
            onSuccess={handleImportCategoriesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/brands")) {
      return (
        <>
          <BrandSheet
            brand={null}
            open={openBrandSheet}
            onOpenChange={setOpenBrandSheet}
            isDemo={isDemo}
            onSuccess={handleBrandSuccess}
          />
          <ImportBrandsSheet
            open={openImportBrandsSheet}
            onOpenChange={setOpenImportBrandsSheet}
            isDemo={isDemo}
            onSuccess={handleImportBrandsSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/warranty-templates")) {
      return (
        <WarrantyTemplateSheet
          warrantyTemplate={null}
          open={openWarrantySheet}
          onOpenChange={setOpenWarrantySheet}
          isDemo={isDemo}
          onSuccess={handleWarrantySuccess}
        />
      );
    }

    if (pathname.includes("/meal-periods")) {
      return (
        <MealPeriodSheet
          mealPeriod={null}
          open={openMealPeriodSheet}
          onOpenChange={setOpenMealPeriodSheet}
          isDemo={isDemo}
          onSuccess={() => {
            queryClient.invalidateQueries({
              queryKey: mealPeriodKeys.all,
            });
          }}
        />
      );
    }

    if (pathname.includes("/variations")) {
      return (
        <>
          <VariationSheet
            variation={null}
            open={openVariantSheet}
            onOpenChange={setOpenVariantSheet}
            isDemo={isDemo}
            onSuccess={() => {
              queryClient.invalidateQueries({ queryKey: variationKeys.all });
            }}
          />
          <ImportVariationsSheet
            open={openImportVariationsSheet}
            onOpenChange={setOpenImportVariationsSheet}
            isDemo={isDemo}
            onSuccess={handleImportVariationsSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/modifiers")) {
      return (
        <>
          <ModifierSheet
            modifierGroup={null}
            open={openModifierSheet}
            onOpenChange={setOpenModifierSheet}
            isDemo={isDemo}
            onSuccess={handleModifierSuccess}
          />
          <ImportModifiersSheet
            open={openImportModifiersSheet}
            onOpenChange={setOpenImportModifiersSheet}
            isDemo={isDemo}
            onSuccess={handleImportModifiersSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/service-categories")) {
      return (
        <>
          <ServiceCategorySheet
            serviceCategory={null}
            open={openServiceTypeSheet}
            onOpenChange={setOpenServiceTypeSheet}
            isDemo={isDemo}
            onSuccess={handleServiceTypeSuccess}
          />
          <ImportServiceCategoriesSheet
            open={openImportServiceTypesSheet}
            onOpenChange={setOpenImportServiceTypesSheet}
            isDemo={isDemo}
            onSuccess={handleImportServiceTypesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/services")) {
      return (
        <>
          <ServiceSheet
            service={null}
            open={openServiceSheet}
            onOpenChange={setOpenServiceSheet}
            isDemo={isDemo}
            onSuccess={handleServiceSuccess}
          />
          <ImportServicesSheet
            open={openImportServicesSheet}
            onOpenChange={setOpenImportServicesSheet}
            isDemo={isDemo}
            onSuccess={handleImportServicesSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/units")) {
      return (
        <>
          <UnitSheet
            unit={null}
            open={openUnitSheet}
            onOpenChange={setOpenUnitSheet}
            isDemo={isDemo}
            onSuccess={handleUnitSuccess}
          />
          <ImportUnitsSheet
            open={openImportUnitsSheet}
            onOpenChange={setOpenImportUnitsSheet}
            isDemo={isDemo}
            onSuccess={handleImportUnitsSuccess}
          />
        </>
      );
    }

    if (pathname.includes("/products")) {
      return (
        <>
          <ProductSheet
            product={null}
            open={openProductSheet}
            onOpenChange={setOpenProductSheet}
            isDemo={isDemo}
            onSuccess={handleProductSuccess}
          />
          <ImportProductsSheet
            open={openImportProductsSheet}
            onOpenChange={setOpenImportProductsSheet}
            isDemo={isDemo}
            onSuccess={handleImportProductsSuccess}
          />
        </>
      );
    }

    return null;
  };

  return (
    <DashboardLayout
      isDemo={isDemo}
      title="Items"
      categories={ALL_ITEM_CATEGORIES}
      hasAccess={hasAccess}
      addAction={{
        text: getAddButtonText(),
        onClick: handleAddNew,
      }}
      importAction={{
        text: getImportButtonText(),
        onClick: handleImport,
      }}
      sheets={getCurrentRouteSheets()}
    >
      {children}
    </DashboardLayout>
  );
}
