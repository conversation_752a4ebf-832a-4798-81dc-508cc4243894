"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Info, Check, X, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import {
  accountFormSchema,
  type AccountFormSchema,
} from "@/lib/accounts/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  AccountStatus,
  AccountTableData,
  UpdateAccountDto,
} from "@/types/account";
import {
  AccountCategory,
  ChartAccountType,
  AccountDetailType,
  ACCOUNT_LABELS,
} from "@/types/account.enum";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useAccountData,
  useAccountNameAvailability,
  useAccountNumberAvailability,
  useCreateAccount,
  useUpdateAccount,
  useAccountsSlim,
} from "@/lib/accounts/hooks";
import { useTaxesSlim } from "@/lib/taxes/hooks";
import { TaxStatus } from "@/types/tax";
import { AccountLabelUtils, ChartOfAccountsUtils } from "@/types/account.enum";
import { useEffect } from "react";

interface AccountSheetProps {
  account: AccountTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (account?: AccountTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

export function AccountSheet({
  account,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: AccountSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Debounced values for availability checking
  const [debouncedName, setDebouncedName] = React.useState<string>("");
  const [debouncedNumber, setDebouncedNumber] = React.useState<string>("");

  // Fetch complete account data if updating
  const { data: fullAccountResponse, isLoading: isLoadingAccount } =
    useAccountData(account?.id || "", isDemo);
  const fullAccount = fullAccountResponse?.data;

  // Fetch slim data for dropdowns
  const { data: taxesSlimResponse } = useTaxesSlim(isDemo);
  const { data: accountsSlimResponse } = useAccountsSlim(isDemo);
  const taxesSlim = taxesSlimResponse?.data || [];
  const accountsSlim = accountsSlimResponse?.data || [];

  useEffect(() => {
    if (fullAccountResponse) {
      console.log("fullAccountResponse", fullAccountResponse);
    }
  }, [fullAccountResponse]);

  // Mutation hooks for create and update operations
  const createAccountMutation = useCreateAccount(isDemo);
  const updateAccountMutation = useUpdateAccount(isDemo);

  // Availability checks (only check if not updating the same account)
  const shouldCheckNameAvailability =
    debouncedName.length > 0 &&
    (!isUpdate ||
      (account &&
        debouncedName.toLowerCase() !== account.accountName.toLowerCase()));
  const shouldCheckNumberAvailability =
    debouncedNumber.length > 0 &&
    (!isUpdate || (account && debouncedNumber !== account.accountNumber));

  const { data: nameAvailabilityResponse, isLoading: isCheckingName } =
    useAccountNameAvailability(debouncedName, isDemo);
  const { data: numberAvailabilityResponse, isLoading: isCheckingNumber } =
    useAccountNumberAvailability(debouncedNumber, isDemo);

  const isNameAvailable = shouldCheckNameAvailability
    ? nameAvailabilityResponse?.data?.available ?? true
    : true;
  const isNumberAvailable = shouldCheckNumberAvailability
    ? numberAvailabilityResponse?.data?.available ?? true
    : true;

  // Helper functions for filtering based on account hierarchy
  const getFilteredAccountTypes = (category: AccountCategory) => {
    return AccountLabelUtils.getAccountTypesForCategory(category);
  };

  const getFilteredDetailTypes = (accountType: ChartAccountType) => {
    return AccountLabelUtils.getDetailTypesForAccountType(accountType);
  };

  const getFilteredParentAccounts = (
    category: AccountCategory,
    accountType: ChartAccountType
  ) => {
    // Filter parent accounts to only show accounts of the same category and compatible types
    return accountsSlim.filter(
      (acc) => acc.accountCategory === category && acc.id !== account?.id // Exclude self from parent options
    );
  };

  const getFilteredTaxes = (
    accountCategory: AccountCategory,
    accountDetailType: AccountDetailType
  ) => {
    // Filter taxes based on account type - this is a simplified filter
    // In a real implementation, you might have more complex business rules
    return taxesSlim.filter((tax) => tax.status === TaxStatus.ACTIVE);
  };

  const getFilteredIncomeAccounts = () => {
    // Filter to only show income accounts for the incomeAccountId field
    return accountsSlim.filter(
      (acc) => acc.accountCategory === AccountCategory.INCOME
    );
  };

  const form = useForm<AccountFormSchema>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      accountName: "",
      accountNumber: "",
      accountCategory: AccountCategory.ASSETS,
      accountType: ChartAccountType.CURRENT_ASSETS,
      accountDetailType: AccountDetailType.OTHER_CURRENT_ASSETS,
      parentAccountId: undefined,
      openingBalance: "",
      openingBalanceDate: "",
      description: "",
      defaultTaxId: undefined,
      useForBillableExpenses: false,
      incomeAccountId: undefined,
      isSystemAccount: false,
      status: AccountStatus.ACTIVE,
    },
  });

  React.useEffect(() => {
    if (account && fullAccount) {
      // Use full account data for populating the form
      form.reset({
        accountName: fullAccount.accountName,
        accountNumber: fullAccount.accountNumber || "",
        accountCategory: fullAccount.accountCategory,
        accountType: fullAccount.accountType,
        accountDetailType: fullAccount.accountDetailType,
        parentAccountId: fullAccount.parentAccountId || undefined,
        openingBalance: fullAccount.openingBalance || "",
        openingBalanceDate: fullAccount.openingBalanceDate || "",
        description: fullAccount.description || "",
        defaultTaxId: fullAccount.defaultTaxId || "",
        useForBillableExpenses: fullAccount.useForBillableExpenses,
        incomeAccountId: fullAccount.incomeAccountId || "",
        isSystemAccount: fullAccount.isSystemAccount,
        status: fullAccount.status as AccountStatus,
      });
    } else if (!account) {
      // Reset form for new account
      form.reset({
        accountName: "",
        accountNumber: "",
        accountCategory: AccountCategory.ASSETS,
        accountType: ChartAccountType.CURRENT_ASSETS,
        accountDetailType: AccountDetailType.OTHER_CURRENT_ASSETS,
        parentAccountId: undefined,
          openingBalance: "",
        openingBalanceDate: "",
        description: "",
        defaultTaxId: undefined,
        useForBillableExpenses: false,
        incomeAccountId: undefined,
        isSystemAccount: false,
        status: AccountStatus.ACTIVE,
      });
    }
  }, [account, fullAccount]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.accountName ||
    errors.accountNumber ||
    errors.accountCategory ||
    errors.accountType ||
    errors.accountDetailType ||
    errors.status ||
    (shouldCheckNameAvailability && isNameAvailable === false) ||
    (shouldCheckNumberAvailability && isNumberAvailable === false)
  );

  const hasHierarchyErrors = !!errors.parentAccountId;

  const hasFinancialErrors = !!(
    errors.openingBalance ||
    errors.openingBalanceDate ||
    errors.defaultTaxId
  );

  const hasAdditionalErrors = !!(
    errors.description ||
    errors.useForBillableExpenses ||
    errors.incomeAccountId ||
    errors.isSystemAccount
  );

  const handleSubmit = async (data: AccountFormSchema) => {
    // Check availability before submitting
    if (shouldCheckNameAvailability && isNameAvailable === false) {
      toast.error("Account name is already taken. Please choose a different name.");
      return;
    }

    if (shouldCheckNumberAvailability && isNumberAvailable === false) {
      toast.error("Account number is already taken. Please choose a different number.");
      return;
    }

    setIsSubmitting(true);

    try {
      if (account) {
        // Update existing account
        const updateData: UpdateAccountDto = {
          accountName: data.accountName,
          accountNumber: data.accountNumber || undefined,
          accountCategory: data.accountCategory,
          accountType: data.accountType,
          accountDetailType: data.accountDetailType,
          parentAccountId: data.parentAccountId || undefined,
          openingBalance: data.openingBalance || undefined,
          openingBalanceDate: data.openingBalanceDate || undefined,
          description: data.description || undefined,
          defaultTaxId: data.defaultTaxId || undefined,
          useForBillableExpenses: data.useForBillableExpenses,
          incomeAccountId: data.incomeAccountId || undefined,
          isSystemAccount: data.isSystemAccount,
          status: data.status,
        };

        const response = await updateAccountMutation.mutateAsync({
          id: account.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Account updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as AccountTableData);
          return;
        }
        toast.error(response.message || "Failed to update account");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new account
        const createData = {
          accountName: data.accountName,
          accountNumber: data.accountNumber || undefined,
          accountCategory: data.accountCategory,
          accountType: data.accountType,
          accountDetailType: data.accountDetailType,
          parentAccountId: data.parentAccountId || undefined,
          openingBalance: data.openingBalance || undefined,
          openingBalanceDate: data.openingBalanceDate || undefined,
          description: data.description || undefined,
          defaultTaxId: data.defaultTaxId || undefined,
          useForBillableExpenses: data.useForBillableExpenses,
          incomeAccountId: data.incomeAccountId || undefined,
          isSystemAccount: data.isSystemAccount,
          status: data.status,
        };

        const response = await createAccountMutation.mutateAsync(createData);

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Account created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as AccountTableData);
          return;
        }
        toast.error(response.message || "Failed to create account");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save account:", error);
      toast.error("Failed to save account");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debounce form values for availability checking
  React.useEffect(() => {
    const subscription = form.watch((value: Partial<AccountFormSchema>) => {
      const timer = setTimeout(() => {
        if (
          value.accountName !== undefined &&
          typeof value.accountName === "string"
        ) {
          setDebouncedName(value.accountName);
        }
        if (
          value.accountNumber !== undefined &&
          typeof value.accountNumber === "string"
        ) {
          setDebouncedNumber(value.accountNumber);
        }
      }, 500); // 500ms debounce

      return () => clearTimeout(timer);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Helper component for availability indicators
  const AvailabilityIndicator = ({
    isLoading,
    isAvailable,
    shouldCheck,
    fieldName,
  }: {
    isLoading: boolean;
    isAvailable: boolean | undefined | null;
    shouldCheck: boolean | null;
    fieldName: string;
  }) => {
    if (!shouldCheck) return null;

    if (isLoading) {
      return (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Loader2 className="h-3 w-3 animate-spin" />
          Checking availability...
        </div>
      );
    }

    if (isAvailable === false) {
      return (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <X className="h-3 w-3" />
          {fieldName} is already taken
        </div>
      );
    }

    if (isAvailable === true) {
      return (
        <div className="flex items-center gap-1 text-xs text-green-600">
          <Check className="h-3 w-3" />
          {fieldName} is available
        </div>
      );
    }

    return null;
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Account Name</Label>
                <Input
                  {...form.register("accountName")}
                  name="accountName"
                  placeholder="Enter account name (e.g., Cash, Accounts Receivable)"
                  className={cn(
                    errors.accountName && "border-destructive",
                    shouldCheckNameAvailability &&
                      isNameAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.accountName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.accountName.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingName}
                  isAvailable={isNameAvailable}
                  shouldCheck={shouldCheckNameAvailability}
                  fieldName="Account name"
                />
              </div>

              <div>
                <Label>Account Number</Label>
                <Input
                  {...form.register("accountNumber")}
                  name="accountNumber"
                  placeholder="Enter account number (e.g., 1000, 1200)"
                  className={cn(
                    errors.accountNumber && "border-destructive",
                    shouldCheckNumberAvailability &&
                      isNumberAvailable === false &&
                      "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.accountNumber && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.accountNumber.message}
                  </p>
                )}
                <AvailabilityIndicator
                  isLoading={isCheckingNumber}
                  isAvailable={isNumberAvailable}
                  shouldCheck={shouldCheckNumberAvailability}
                  fieldName="Account number"
                />
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                  <Label>Account Category</Label>
                  <Select
                    value={form.watch("accountCategory")}
                    onValueChange={(value) => {
                      form.setValue(
                        "accountCategory",
                        value as AccountCategory,
                        {
                          shouldValidate: true,
                        }
                      );
                      // Reset dependent fields when category changes
                      form.setValue("accountType", "" as ChartAccountType);
                      form.setValue(
                        "accountDetailType",
                        "" as AccountDetailType
                      );
                    }}
                  >
                    <SelectTrigger
                      className={cn(
                        errors.accountCategory && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(AccountCategory).map((category) => (
                        <SelectItem key={category} value={category}>
                          {ACCOUNT_LABELS.categories[category]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.accountCategory && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.accountCategory.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Account Type</Label>
                  <Select
                    value={form.watch("accountType")}
                    onValueChange={(value) => {
                      form.setValue("accountType", value as ChartAccountType, {
                        shouldValidate: true,
                      });
                      // Reset detail type when account type changes
                      form.setValue(
                        "accountDetailType",
                        "" as AccountDetailType
                      );
                    }}
                  >
                    <SelectTrigger
                      className={cn(errors.accountType && "border-destructive")}
                    >
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getFilteredAccountTypes(
                        form.watch("accountCategory")
                      ).map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.accountType && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.accountType.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Detail Type</Label>
                  <Select
                    value={form.watch("accountDetailType")}
                    onValueChange={(value) =>
                      form.setValue(
                        "accountDetailType",
                        value as AccountDetailType,
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                  >
                    <SelectTrigger
                      className={cn(
                        errors.accountDetailType && "border-destructive"
                      )}
                    >
                      <SelectValue placeholder="Select detail type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getFilteredDetailTypes(form.watch("accountType")).map(
                        (detailType) => (
                          <SelectItem
                            key={detailType.value}
                            value={detailType.value}
                          >
                            {detailType.label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                  {errors.accountDetailType && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.accountDetailType.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue("status", value as AccountStatus, {
                      shouldValidate: true,
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AccountStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "hierarchy",
      title: "Account Hierarchy",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasHierarchyErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Account Hierarchy</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "financial",
      title: "Financial Details",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasFinancialErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Financial Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <Label>Opening Balance</Label>
                  <Input
                    {...form.register("openingBalance")}
                    placeholder="0.00"
                    type="number"
                    step="0.01"
                    className={cn(
                      errors.openingBalance && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.openingBalance && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.openingBalance.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label>Opening Balance Date</Label>
                  <Input
                    {...form.register("openingBalanceDate")}
                    type="date"
                    className={cn(
                      errors.openingBalanceDate && "border-destructive"
                    )}
                    disabled={isSubmitting}
                  />
                  {errors.openingBalanceDate && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.openingBalanceDate.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label>Default Tax</Label>
                <Select
                  value={form.watch("defaultTaxId") || undefined}
                  onValueChange={(value) =>
                    form.setValue("defaultTaxId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                >
                  <SelectTrigger
                    className={cn(errors.defaultTaxId && "border-destructive")}
                  >
                    <SelectValue placeholder="Select default tax (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {getFilteredTaxes(
                      form.watch("accountCategory"),
                      form.watch("accountDetailType")
                    ).map((tax) => (
                      <SelectItem key={tax.id} value={tax.id}>
                        {tax.taxName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.defaultTaxId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.defaultTaxId.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "additional",
      title: "Additional Settings",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasAdditionalErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Settings</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <textarea
                  {...form.register("description")}
                  placeholder="Enter account description (optional)"
                  className={cn(
                    "w-full min-h-[80px] px-3 py-2 border border-input bg-background text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 rounded-md",
                    errors.description && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="useForBillableExpenses"
                    checked={form.watch("useForBillableExpenses") || false}
                    onCheckedChange={(checked) =>
                      form.setValue(
                        "useForBillableExpenses",
                        checked as boolean,
                        {
                          shouldValidate: true,
                        }
                      )
                    }
                  />
                  <Label htmlFor="useForBillableExpenses">
                    Use for billable expenses
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isSystemAccount"
                    checked={form.watch("isSystemAccount") || false}
                    onCheckedChange={(checked) =>
                      form.setValue("isSystemAccount", checked as boolean, {
                        shouldValidate: true,
                      })
                    }
                  />
                  <Label htmlFor="isSystemAccount">System account</Label>
                </div>
              </div>

              <div>
                <Label>Income Account</Label>
                <Select
                  value={form.watch("incomeAccountId") || undefined}
                  onValueChange={(value) =>
                    form.setValue("incomeAccountId", value || undefined, {
                      shouldValidate: true,
                    })
                  }
                >
                  <SelectTrigger
                    className={cn(
                      errors.incomeAccountId && "border-destructive"
                    )}
                  >
                    <SelectValue placeholder="Select income account (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {getFilteredIncomeAccounts().map((incomeAccount) => (
                      <SelectItem
                        key={incomeAccount.id}
                        value={incomeAccount.id}
                      >
                        {incomeAccount.accountName}
                        {incomeAccount.accountNumber &&
                          ` (${incomeAccount.accountNumber})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.incomeAccountId && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.incomeAccountId.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<AccountTableData, AccountFormSchema>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={account}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Account"
      sections={sections}
      onSubmit={form.handleSubmit(handleSubmit)}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingAccount}
    />
  );
}
