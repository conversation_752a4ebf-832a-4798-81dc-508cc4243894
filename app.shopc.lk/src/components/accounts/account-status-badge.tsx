"use client";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AccountStatus } from "@/types/account";
import * as demoApi from "@/lib/accounts/demo";
import * as api from "@/lib/accounts/api";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { Loader2, Check } from "lucide-react";
import { useState } from "react";

interface AccountStatusBadgeProps {
  accountId: string;
  status: AccountStatus;
  isDemo?: boolean;
  disabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (accountId: string, newStatus: AccountStatus) => void;
}

export function AccountStatusBadge({
  accountId,
  status,
  isDemo = false,
  disabled = false,
  onRefresh,
  onStatusUpdate,
}: AccountStatusBadgeProps) {
  // Removed useToast hook - using Son<PERSON> directly
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (newStatus: AccountStatus) => {
    // Don't make API call if status is already the same
    if (newStatus === status) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    setIsUpdating(true);

    try {
      const result = isDemo
        ? await demoApi.updateDemoAccountApi(accountId, {
            status: newStatus,
          })
        : await api.updateAccountApi(accountId, {
            status: newStatus,
          });

      if (result.status === ApiStatus.SUCCESS) {
        toast.success("Account status updated", {
          description: `Account status updated to ${getStatusLabel(newStatus)}`
        });
        // Use the new status update handler if available
        if (onStatusUpdate) {
          onStatusUpdate(accountId, newStatus);
        } else {
          // Fallback to onRefresh for backward compatibility
          onRefresh?.();
        }
      } else {
        toast.error("Failed to update status", {
          description: result.message || "Failed to update status",
        });
      }
    } catch (error) {
      toast.error("Failed to update account status", {
        description: "Failed to update account status. Please try again.",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusLabel = (status: AccountStatus) => {
    switch (status) {
      case AccountStatus.ACTIVE:
        return "Active";
      case AccountStatus.INACTIVE:
        return "Inactive";
      default:
        return status;
    }
  };

  const getStatusVariant = (status: AccountStatus) => {
    switch (status) {
      case AccountStatus.ACTIVE:
        return "default" as const;
      case AccountStatus.INACTIVE:
        return "outline" as const;
      default:
        return "outline" as const;
    }
  };

  const statusOptions = [
    {
      value: AccountStatus.ACTIVE,
      label: "Active",
      variant: "default" as const,
    },
    {
      value: AccountStatus.INACTIVE,
      label: "Inactive",
      variant: "outline" as const,
    },
    // Note: We typically don't allow users to manually set status to DELETED
    // as deletion is usually handled through delete operations
  ];

  return (
    <DropdownMenu open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Badge
          variant={getStatusVariant(status)}
          className={`${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer hover:opacity-80"
          } transition-opacity`}
        >
          {isUpdating ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          ) : (
            <span>{getStatusLabel(status)}</span>
          )}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[120px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleStatusUpdate(option.value)}
            disabled={isUpdating || disabled}
            className="flex items-center justify-between"
          >
            <span>{option.label}</span>
            {status === option.value && <Check className="h-3 w-3" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
