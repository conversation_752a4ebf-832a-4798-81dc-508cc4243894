"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@radix-ui/react-icons";
import { AccountTableData } from "@/types/account";
import { ApiStatus } from "@/types/common";
import { useBulkDeleteAccounts } from "@/lib/accounts/hooks";
import { toast } from "sonner";

interface DeleteAccountsDialogProps {
  accounts: AccountTableData[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteAccountsDialog({
  accounts,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteAccountsDialogProps) {
  const { toast } = useToast();
  const bulkDeleteMutation = useBulkDeleteAccounts(isDemo);

  // Check if any accounts are system accounts (cannot be deleted)
  const systemAccounts = accounts.filter(
    (account) => account.isSystemAccount
  );

  const hasSystemAccounts = systemAccounts.length > 0;
  const isMultiple = accounts.length > 1;

  const handleDelete = async () => {
    if (accounts.length === 0 || hasSystemAccounts) return;

    try {
      const accountIds = accounts.map((account) => account.id);
      const result = await bulkDeleteMutation.mutateAsync(accountIds);

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast({
          title: "Success",
          description: `Successfully deleted ${result.data.deletedCount} account${
            result.data.deletedCount > 1 ? "s" : ""
          }`,
        });
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to delete accounts",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting accounts:", error);
      toast({
        title: "Error",
        description: "Failed to delete accounts",
        variant: "destructive",
      });
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {hasSystemAccounts
              ? `Cannot Delete ${isMultiple ? "Accounts" : "Account"}`
              : "Are you sure?"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {hasSystemAccounts ? (
              <>
                {isMultiple ? (
                  <>
                    The following accounts cannot be deleted because they are
                    system accounts:
                    <ul className="mt-2 list-disc list-inside">
                      {systemAccounts.map((account) => (
                        <li key={account.id} className="font-medium">
                          {account.accountName}
                          {account.accountNumber && (
                            <span className="text-muted-foreground">
                              {" "}
                              ({account.accountNumber})
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <>
                    The account{" "}
                    <span className="font-medium">
                      {systemAccounts[0]?.accountName}
                    </span>{" "}
                    cannot be deleted because it is a system account.
                  </>
                )}
                <span className="block mt-2 text-amber-600">
                  System accounts are protected and cannot be deleted to maintain
                  data integrity.
                </span>
              </>
            ) : (
              <>
                This will permanently delete{" "}
                {isMultiple
                  ? `${accounts.length} accounts`
                  : "the account"}{" "}
                {!isMultiple && (
                  <span className="font-medium">{accounts[0]?.accountName}</span>
                )}
                . This action cannot be undone.
                <span className="block mt-2 text-amber-600">
                  Warning: Deleting accounts may affect financial records and
                  reporting. Please ensure this account is no longer needed.
                </span>
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            {hasSystemAccounts ? "Close" : "Cancel"}
          </AlertDialogCancel>
          {!hasSystemAccounts && (
            <AlertDialogAction
              onClick={handleDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {bulkDeleteMutation.isPending
                ? "Deleting..."
                : `Delete ${
                    isMultiple ? `${accounts.length} Accounts` : "Account"
                  }`}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
