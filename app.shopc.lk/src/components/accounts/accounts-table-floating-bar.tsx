"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, CheckCircle2, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type AccountTableData, AccountStatus } from "@/types/account";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteAccounts,
  useBulkUpdateAccountStatus,
} from "@/lib/accounts/hooks";
import { ApiStatus } from "@/types/common";

interface AccountsTableFloatingBarProps {
  table: Table<AccountTableData>;
  onRefresh?: (accountIds?: string[]) => void;
  isDemo?: boolean;
}

export function AccountsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: AccountsTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "activate" | "deactivate"
  >();

  const bulkDeleteMutation = useBulkDeleteAccounts(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateAccountStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedAccounts = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedAccounts.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedAccounts.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: AccountStatus) => {
      if (selectionCount === 0) return;

      setAction(status === AccountStatus.ACTIVE ? "activate" : "deactivate");

      try {
        const accountIds = selectedAccounts.map((account) => account.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          accountIds,
          status,
        });

        const failedUpdates = result.filter(
          (res: any) => res.status !== ApiStatus.SUCCESS
        );

        if (failedUpdates.length === 0) {
            toast({
            title: "Success",
            description: `Successfully ${
              status === AccountStatus.ACTIVE ? "activated" : "deactivated"
            } ${selectionCount} account${selectionCount > 1 ? "s" : ""}`
          });
          table.toggleAllRowsSelected(false);
          onRefresh?.(accountIds);
        } else {
            toast({
            title: "Error",
            description: `Failed to update ${failedUpdates.length} account${
              failedUpdates.length > 1 ? "s" : ""
            }`,
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error updating accounts:", error);
        toast({
          title: "Error",
          description: "Failed to update accounts",
          variant: "destructive"
        });
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedAccounts,
      selectionCount,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No accounts selected" };

    try {
      const accountIds = selectedAccounts.map((account) => account.id);
      const result = await bulkDeleteMutation.mutateAsync(accountIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete accounts",
        };
      }
    } catch (error: any) {
      console.error("Error deleting accounts:", error);
      return { error: error.message || "Failed to delete accounts" };
    }
  }, [selectedAccounts, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(AccountStatus.ACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(AccountStatus.INACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<AccountTableData>
        table={table}
        title="Accounts"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} account${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass account IDs since they're deleted
        }}
      />
    </>
  );
}