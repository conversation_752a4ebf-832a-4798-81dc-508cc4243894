"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { type E164Number } from "libphonenumber-js";
import { ApiStatus } from "@/types/common";
import {
  type RegisterUserData,
  type UserInformation,
  type BusinessInformation,
  UserTitle,
  FormStep,
} from "@/types/auth";
import { BusinessType } from "@/types/business-types";
import { BusinessTypeSelection } from "./business-type-selection";
import { BusinessInformationForm } from "./business-information-form";
import { PersonalInformationForm } from "./personal-information-form";
import { toast } from "sonner";
import { register } from "@/lib/authentication/api";

// Form data interface
interface RegistrationFormData {
  businessName: string;
  businessPhone: string;
  businessLogo: File | null;
  businessLogoPreview: string;
  streetAddress: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  title: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  agreedToTerms: boolean;
}

// Default form data
const defaultFormData: RegistrationFormData = {
  businessName: "",
  businessPhone: "",
  businessLogo: null,
  businessLogoPreview: "",
  streetAddress: "",
  city: "",
  state: "",
  postalCode: "",
  country: "LK",
  title: "",
  firstName: "",
  lastName: "",
  email: "",
  phoneNumber: "",
  password: "",
  confirmPassword: "",
  agreedToTerms: false,
};

export default function RegularRegistrationForm() {
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);

  // State hooks
  const [currentStep, setCurrentStep] = useState<FormStep>(FormStep.Type);
  const [selectedBusinessType, setSelectedBusinessType] =
    useState<BusinessType | null>(null);
  const [isRegistering, setIsRegistering] = useState(false);
  const [formData, setFormData] =
    useState<RegistrationFormData>(defaultFormData);

  // Navigation handlers
  const handleBusinessTypeSelect = (businessType: BusinessType) => {
    setSelectedBusinessType(businessType);

    // If staff registration, go directly to personal information
    if (businessType === BusinessType.Staff) {
      setCurrentStep(FormStep.Personal);
    } else {
      setCurrentStep(FormStep.Business);
    }
  };

  const handleBack = () => {
    if (currentStep === FormStep.Personal) {
      // For staff registration, go back to type selection
      if (selectedBusinessType === BusinessType.Staff) {
        setCurrentStep(FormStep.Type);
      } else {
        setCurrentStep(FormStep.Business);
      }
    } else if (currentStep === FormStep.Business) {
      setCurrentStep(FormStep.Type);
    }
  };

  // Form submission handlers
  const handleBusinessInfoSubmit = (businessFormData: any) => {
    setFormData((prev) => ({
      ...prev,
      ...businessFormData,
    }));

    // After business info, always go to personal information
    setCurrentStep(FormStep.Personal);
  };

  const handleFormSubmit = async (personalFormData: any) => {
    // Combine form data
    const updatedFormData = {
      ...formData,
      ...personalFormData,
    };

    // Check if passwords match
    if (updatedFormData.password !== updatedFormData.confirmPassword) {
      toast.error("Passwords do not match", {
        description: "Please make sure your passwords match and try again.",
      });
      return;
    }

    // Proceed with registration
    await registerUser(updatedFormData);
  };

  // Registration helper function
  const registerUser = async (userData: RegistrationFormData) => {
    // Create user information object
    const userInfo: UserInformation = {
      email: userData.email,
      password: userData.password,
      title: userData.title as UserTitle,
      firstName: userData.firstName,
      lastName: userData.lastName,
      phoneNumber: userData.phoneNumber as unknown as E164Number,
    };

    // Create minimal business info for staff registration
    const staffBusinessInfo = null;

    // Create registration data
    const registrationData: RegisterUserData = {
      user: userInfo,
      business:
        selectedBusinessType === BusinessType.Staff
          ? staffBusinessInfo
          : ({} as BusinessInformation),
    };

    // Only include business info if not registering as staff
    if (selectedBusinessType !== BusinessType.Staff) {
      const businessInfo: BusinessInformation = {
        businessName: userData.businessName,
        businessType: selectedBusinessType || BusinessType.Other,
        businessPhone: userData.businessPhone as unknown as E164Number,
        businessLogo: userData.businessLogo,
        streetAddress: userData.streetAddress,
        city: userData.city,
        state: userData.state,
        postalCode: userData.postalCode,
        country: userData.country,
      };

      registrationData.business = businessInfo;
    }

    // Submit registration
    setIsRegistering(true);
    try {
      const response = await register(registrationData);

      if (response.status === ApiStatus.SUCCESS) {
        toast.success("Account created successfully!", {
          description: "Welcome to your new business dashboard!",
        });

        // Redirect to the app home page
        router.push("/admin/home");
      } else {
        toast.error("Registration failed", {
          description: response.message || "Please try again later.",
        });
      }
    } catch (error) {
      toast.error("An error occurred", {
        description: "Failed to create account. Please try again later.",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  // Progress calculation
  const getProgress = () => {
    // For staff registration, only 2 steps
    if (selectedBusinessType === BusinessType.Staff) {
      switch (currentStep) {
        case FormStep.Type:
          return 50;
        case FormStep.Personal:
          return 100;
        default:
          return 0;
      }
    }

    // For all other business types, 3 steps
    switch (currentStep) {
      case FormStep.Type:
        return 33;
      case FormStep.Business:
        return 66;
      case FormStep.Personal:
        return 100;
      default:
        return 0;
    }
  };

  // Get the current step name for display
  const getStepName = () => {
    switch (currentStep) {
      case FormStep.Type:
        return "Business Type";
      case FormStep.Business:
        return "Business Details";
      case FormStep.Personal:
        return "Personal Information";
      default:
        return "";
    }
  };

  // Render current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case FormStep.Personal:
        return (
          <div className="animate-fadeIn">
            <PersonalInformationForm
              formRef={formRef}
              formData={formData}
              isRegistering={isRegistering}
              handleFormSubmit={handleFormSubmit}
              handleBack={handleBack}
            />
          </div>
        );

      case FormStep.Business:
        return (
          <div className="animate-fadeIn">
            <BusinessInformationForm
              formRef={formRef}
              formData={formData}
              setFormData={setFormData}
              handleBusinessInfoSubmit={handleBusinessInfoSubmit}
              handleBack={handleBack}
            />
          </div>
        );

      default:
        return (
          <div className="animate-fadeIn">
            <BusinessTypeSelection
              handleBusinessTypeSelect={handleBusinessTypeSelect}
              selectedBusinessType={selectedBusinessType}
            />
          </div>
        );
    }
  };

  // Progress header component with step indicators
  const ProgressHeader = () => {
    const totalSteps = selectedBusinessType === BusinessType.Staff ? 2 : 3;
    const steps = Array.from({ length: totalSteps }, (_, i) => i);

    return (
      <div className="mb-8 max-w-3xl mx-auto">
        {/* Step indicators */}
        <div className="flex justify-between items-center mb-2">
          {steps.map((step) => {
            // Convert step index to FormStep enum value
            const stepEnum =
              step === 0
                ? FormStep.Type
                : step === 1
                ? totalSteps === 2
                  ? FormStep.Personal
                  : FormStep.Business
                : FormStep.Personal;

            return (
              <div
                key={step}
                className="flex flex-col items-center"
                style={{ width: `${100 / totalSteps}%` }}
              >
                <div
                  className={`h-8 w-8 rounded-full flex items-center justify-center mb-1 
                    ${
                      stepEnum < currentStep
                        ? "bg-primary text-white"
                        : stepEnum === currentStep
                        ? "border-2 border-primary text-primary"
                        : "border-2 border-muted-foreground text-muted-foreground"
                    }`}
                >
                  {step + 1}
                </div>
                <span
                  className={`text-xs text-center hidden sm:block 
                    ${
                      stepEnum <= currentStep
                        ? "text-primary font-medium"
                        : "text-muted-foreground"
                    }`}
                >
                  {step === 0
                    ? "Business Type"
                    : step === 1
                    ? totalSteps === 2
                      ? "Personal Info"
                      : "Business Details"
                    : "Personal Info"}
                </span>
              </div>
            );
          })}
        </div>

        {/* Progress bar */}
        <div className="h-2 bg-muted rounded-full overflow-hidden mt-2">
          <div
            className="h-full bg-primary transition-all duration-500 ease-in-out"
            style={{ width: `${getProgress()}%` }}
          ></div>
        </div>

        {/* Current step name - mobile only */}
        <div className="sm:hidden text-center mt-2">
          <span className="text-sm font-medium text-primary">
            {getStepName()}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full max-w-5xl mx-auto p-2 flex items-center justify-center">
      <div className="w-full mx-auto p-4 md:p-6 h-auto min-h-[80vh] bg-white dark:bg-slate-950 rounded-lg shadow-sm border border-border">
        <ProgressHeader />
        {renderStepContent()}
      </div>
    </div>
  );
}
