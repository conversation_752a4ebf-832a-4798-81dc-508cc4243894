"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, Check<PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON>, Clock, Calculator } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type PayrollRunTableData, PayrollRunStatus } from "@/types/payroll-run";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeletePayrollRuns,
  useBulkUpdatePayrollRunStatus,
} from "@/lib/payroll-runs/hooks";
import { ApiStatus } from "@/types/common";

interface PayrollRunsTableFloatingBarProps {
  table: Table<PayrollRunTableData>;
  onRefresh?: () => Promise<void>;
  isDemo?: boolean;
}

export function PayrollRunsTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: PayrollRunsTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "approve" | "cancel" | "process" | "calculate"
  >();

  const bulkDeleteMutation = useBulkDeletePayrollRuns(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdatePayrollRunStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  const selectedRows = table.getFilteredSelectedRowModel().rows;
  const selectionCount = selectedRows.length;

  // Handle bulk status update
  const handleBulkStatusUpdate = React.useCallback(
    async (newStatus: PayrollRunStatus) => {
      if (selectionCount === 0) return;

      const payrollRunIds = selectedRows.map((row) => row.original.id);

      try {
        const result = await bulkUpdateStatusMutation.mutateAsync({
          payrollRunIds,
          runStatus: newStatus,
        });

        if (result.status === ApiStatus.SUCCESS) {
          toast({
            title: "Success",
            description: `Successfully updated ${selectionCount} payroll run${
              selectionCount > 1 ? "s" : ""
            } to ${newStatus}`
          });
          table.toggleAllRowsSelected(false);
          await onRefresh?.();
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to update payroll runs",
            variant: "destructive"
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to update payroll runs",
          variant: "destructive"
        });
      }
    },
    [
      selectionCount,
      selectedRows,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  // Handle bulk delete
  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return;

    const payrollRunIds = selectedRows.map((row) => row.original.id);

    try {
      const result = await bulkDeleteMutation.mutateAsync(payrollRunIds);

      if (result.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: `Successfully deleted ${selectionCount} payroll run${
            selectionCount > 1 ? "s" : ""
          }`
        });
        table.toggleAllRowsSelected(false);
        setDeleteDialogOpen(false);
        await onRefresh?.();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to delete payroll runs",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete payroll runs",
        variant: "destructive"
      });
    }
  }, [selectionCount, selectedRows, bulkDeleteMutation, table, onRefresh]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(PayrollRunStatus.PROCESSING)}
            disabled={isPending || selectionCount === 0}
          >
            <Clock className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Mark as processing</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(PayrollRunStatus.CALCULATED)}
            disabled={isPending || selectionCount === 0}
          >
            <Calculator className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Mark as calculated</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(PayrollRunStatus.APPROVED)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Approve selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(PayrollRunStatus.CANCELLED)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Cancel selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={isPending || selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<PayrollRunTableData>
        table={table}
        title="Payroll Runs"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} payroll run${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass payroll run IDs since they're deleted
        }}
      />
    </>
  );
}
