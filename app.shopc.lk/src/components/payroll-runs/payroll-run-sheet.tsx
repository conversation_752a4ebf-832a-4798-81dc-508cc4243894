"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { toast } from "sonner";
import { Info, Calendar, DollarSign, Users, FileText } from "lucide-react";

import { BaseSheet } from "@/components/shared/base-sheet";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import {
  PayrollRunTableData,
  PayrollRunType,
  PayrollRunStatus,
} from "@/types/payroll-run";
import { ApiStatus } from "@/types/common";
import {
  useCreatePayrollRun,
  useUpdatePayrollRun,
  usePayrollRunCodeAvailability,
  usePayrollRunData,
} from "@/lib/payroll-runs/hooks";
import { payrollRunFormSchema } from "@/lib/payroll-runs/validations";
import { AvailabilityInput } from "@/components/shared/availability-input";

interface PayrollRunSheetProps {
  payrollRun: PayrollRunTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (payrollRun?: PayrollRunTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof payrollRunFormSchema>;

export function PayrollRunSheet({
  payrollRun,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: PayrollRunSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Values for availability checking
  const [checkRunCode, setCheckRunCode] = React.useState<string>("");

  // Fetch complete payroll run data for editing
  const { data: payrollRunResponse, isLoading: isLoadingPayrollRun } =
    usePayrollRunData(payrollRun?.id || "", isDemo);
  const fullPayrollRun = payrollRunResponse?.data;

  // Availability checks
  const { data: runCodeAvailability } = usePayrollRunCodeAvailability(
    checkRunCode,
    payrollRun?.id,
    isDemo
  );

  // Mutations
  const createMutation = useCreatePayrollRun(isDemo);
  const updateMutation = useUpdatePayrollRun(isDemo);

  // Form setup
  const form = useForm<FormData>({
    resolver: zodResolver(payrollRunFormSchema),
    defaultValues: {
      runCode: "",
      runType: PayrollRunType.REGULAR,
      payrollMonth: "",
      payrollYear: new Date().getFullYear(),
      payPeriodStart: "",
      payPeriodEnd: "",
      paymentDate: "",
      totalEmployees: 0,
      totalGrossPay: "0.00",
      totalDeductions: "0.00",
      totalNetPay: "0.00",
      runStatus: PayrollRunStatus.DRAFT,
      notes: "",
    },
  });

  // Reset form when payroll run changes
  React.useEffect(() => {
    if (fullPayrollRun && isUpdate) {
      form.reset({
        runCode: fullPayrollRun.runCode || "",
        runType: fullPayrollRun.runType || PayrollRunType.REGULAR,
        payrollMonth: fullPayrollRun.payrollMonth || "",
        payrollYear: fullPayrollRun.payrollYear || new Date().getFullYear(),
        payPeriodStart: fullPayrollRun.payPeriodStart || "",
        payPeriodEnd: fullPayrollRun.payPeriodEnd || "",
        paymentDate: fullPayrollRun.paymentDate || "",
        totalEmployees: fullPayrollRun.totalEmployees || 0,
        totalGrossPay: fullPayrollRun.totalGrossPay || "0.00",
        totalDeductions: fullPayrollRun.totalDeductions || "0.00",
        totalNetPay: fullPayrollRun.totalNetPay || "0.00",
        runStatus: fullPayrollRun.runStatus || PayrollRunStatus.DRAFT,
        notes: fullPayrollRun.notes || "",
      });
    } else if (!isUpdate) {
      form.reset({
        runCode: "",
        runType: PayrollRunType.REGULAR,
        payrollMonth: "",
        payrollYear: new Date().getFullYear(),
        payPeriodStart: "",
        payPeriodEnd: "",
        paymentDate: "",
        totalEmployees: 0,
        totalGrossPay: "0.00",
        totalDeductions: "0.00",
        totalNetPay: "0.00",
        runStatus: PayrollRunStatus.DRAFT,
        notes: "",
      });
    }
  }, [fullPayrollRun, isUpdate, form]);

  // Handle form submission
  const handleSubmit = React.useCallback(
    async (data: FormData) => {
      setIsSubmitting(true);

      try {
        let result;

        if (isUpdate && payrollRun?.id) {
          result = await updateMutation.mutateAsync({
            id: payrollRun.id,
            data,
          });
        } else {
          result = await createMutation.mutateAsync(data);
        }

        if (result.status === ApiStatus.SUCCESS) {
          toast.success(`Payroll run ${isUpdate ? "updated" : "created"} successfully`);
          onSuccess?.(result.data);
          onOpenChange?.(false);
        } else {
          toast.error(result.message || "Something went wrong");
        }
      } catch (error) {
        toast.error("Something went wrong");
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      isUpdate,
      payrollRun?.id,
      updateMutation,
      createMutation,
      onSuccess,
      onOpenChange,
    ]
  );

  // Check for form errors in different sections
  const hasBasicInfoErrors = !!(
    form.formState.errors.runCode ||
    form.formState.errors.runType ||
    form.formState.errors.payrollMonth ||
    form.formState.errors.payrollYear
  );

  const hasPeriodErrors = !!(
    form.formState.errors.payPeriodStart ||
    form.formState.errors.payPeriodEnd ||
    form.formState.errors.paymentDate
  );

  const hasFinancialErrors = !!(
    form.formState.errors.totalEmployees ||
    form.formState.errors.totalGrossPay ||
    form.formState.errors.totalDeductions ||
    form.formState.errors.totalNetPay
  );

  const hasStatusErrors = !!(
    form.formState.errors.runStatus || form.formState.errors.notes
  );

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="runCode"
                label="Run Code"
                placeholder="Enter payroll run code (e.g., PR-2024-01)"
                value={form.watch("runCode") || ""}
                onChange={(value) =>
                  form.setValue("runCode", value, { shouldValidate: true })
                }
                onBlur={() => setCheckRunCode(form.watch("runCode") || "")}
                availability={runCodeAvailability}
                error={form.formState.errors.runCode?.message}
                isDemo={isDemo}
              />

              <FormField
                control={form.control}
                name="runType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Run Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select run type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={PayrollRunType.REGULAR}>
                          Regular
                        </SelectItem>
                        <SelectItem value={PayrollRunType.BONUS}>
                          Bonus
                        </SelectItem>
                        <SelectItem value={PayrollRunType.CORRECTION}>
                          Correction
                        </SelectItem>
                        <SelectItem value={PayrollRunType.SPECIAL}>
                          Special
                        </SelectItem>
                        <SelectItem value={PayrollRunType.FINAL}>
                          Final
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="payrollMonth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payroll Month</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., January" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="payrollYear"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payroll Year</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="e.g., 2024"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseInt(e.target.value) || 0)
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "pay-period",
      title: "Pay Period & Payment",
      icon: <Calendar className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasPeriodErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Pay Period & Payment</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="payPeriodStart"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pay Period Start</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="payPeriodEnd"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pay Period End</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="paymentDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "financial-info",
      title: "Financial Information",
      icon: <DollarSign className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasFinancialErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Financial Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="totalEmployees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Employees</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) =>
                          field.onChange(parseInt(e.target.value) || 0)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="totalGrossPay"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Total Gross Pay</FormLabel>
                      <FormControl>
                        <Input placeholder="0.00" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="totalDeductions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Total Deductions</FormLabel>
                      <FormControl>
                        <Input placeholder="0.00" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="totalNetPay"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Total Net Pay</FormLabel>
                      <FormControl>
                        <Input placeholder="0.00" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "status-notes",
      title: "Status & Notes",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasStatusErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Status & Notes</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="runStatus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Run Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={PayrollRunStatus.DRAFT}>
                          Draft
                        </SelectItem>
                        <SelectItem value={PayrollRunStatus.PROCESSING}>
                          Processing
                        </SelectItem>
                        <SelectItem value={PayrollRunStatus.CALCULATED}>
                          Calculated
                        </SelectItem>
                        <SelectItem value={PayrollRunStatus.APPROVED}>
                          Approved
                        </SelectItem>
                        <SelectItem value={PayrollRunStatus.POSTED}>
                          Posted
                        </SelectItem>
                        <SelectItem value={PayrollRunStatus.CANCELLED}>
                          Cancelled
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes about this payroll run..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<PayrollRunTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={payrollRun}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Payroll Run"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingPayrollRun}
    />
  );
}
