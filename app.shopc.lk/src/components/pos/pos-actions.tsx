"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { HoldOrdersDialog } from "./hold-orders-dialog";
import { Calculator } from "./calculator";
import { DiscountDialog } from "./discount-dialog";
import { BalancePaymentDialog } from "./balance-payment-dialog";
import { CustomerDialog } from "./customer-dialog";
import { AddCustomerDialog } from "./add-customer-dialog";
import { CardSelectionDialog } from "./card-selection-dialog";
import { ChequeDetailsDialog } from "./cheque-details-dialog";
import { MultiPaymentDialog } from "./multi-payment-dialog";
import { OrderCreatedDialog } from "./order-created-dialog";
import { PromotionDialog } from "./promotion-dialog";
import { GiftVoucherDialog } from "./gift-voucher-dialog";
import { useState } from "react";
import { usePOSStore } from "./pos-store";
import * as api from "@/lib/pos/api";
import * as demo from "@/lib/pos/demo";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";

type PaymentMethod = "CASH" | "CHEQUE" | "CARD" | "CREDIT" | "MULTI";

interface ExtendedCustomer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  creditLimit: number;
  usedCredit: number;
}

export interface POSActionsProps {
  isDemo: boolean;
  onOrderComplete?: () => void;
}

export function POSActions({ isDemo, onOrderComplete }: POSActionsProps) {
  const items = usePOSStore((state) => state.items);
  const clearCart = usePOSStore((state) => state.clearCart);
  const createOrder = usePOSStore((state) => state.createOrder);
  const holdCurrentOrder = usePOSStore((state) => state.holdCurrentOrder);
  const handlePrint = usePOSStore((state) => state.handlePrint);
  const finalTotal = usePOSStore((state) => state.finalTotal());
  const selectedCustomer = usePOSStore((state) => state.selectedCustomer);
  const setSelectedCustomer = usePOSStore((state) => state.setSelectedCustomer);
  const applyDiscount = usePOSStore((state) => state.applyDiscount);
  const discount = usePOSStore((state) => state.discount);
  const promotionCode = usePOSStore((state) => state.promotionCode);
  const giftVoucherCode = usePOSStore((state) => state.giftVoucherCode);
  const isHoldOrdersDialogOpen = usePOSStore(
    (state) => state.isHoldOrdersDialogOpen
  );
  const setIsHoldOrdersDialogOpen = usePOSStore(
    (state) => state.setIsHoldOrdersDialogOpen
  );

  const [showCalculator, setShowCalculator] = useState(false);
  const [isDiscountDialogOpen, setIsDiscountDialogOpen] = useState(false);
  const [showBalancePayment, setShowBalancePayment] = useState(false);
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showAddCustomerDialog, setShowAddCustomerDialog] = useState(false);
  const [showCardSelection, setShowCardSelection] = useState(false);
  const [showChequeDetails, setShowChequeDetails] = useState(false);
  const [showMultiPayment, setShowMultiPayment] = useState(false);
  const [showOrderCreated, setShowOrderCreated] = useState(false);
  const [showPromotionDialog, setShowPromotionDialog] = useState(false);
  const [showGiftVoucherDialog, setShowGiftVoucherDialog] = useState(false);

  const buttonVisibility = usePOSStore((state) => state.buttonVisibility);

  const handlePaymentMethodClick = async (method: PaymentMethod) => {
    // For multi-payment
    if (method === "MULTI") {
      setShowMultiPayment(true);
      return;
    }

    // For cheque payments
    if (method === "CHEQUE") {
      if (!selectedCustomer) {
        toast.error("Please select a customer for cheque payment");
        setShowCustomerDialog(true);
        return;
      }
      setShowChequeDetails(true);
      return;
    }

    // For credit payments
    if (method === "CREDIT") {
      if (!selectedCustomer) {
        toast.error("Please select a customer for credit payment");
        setShowCustomerDialog(true);
        return;
      }

      const availableCredit =
        selectedCustomer.creditLimit - selectedCustomer.usedCredit;
      if (availableCredit <= 0) {
        toast.error("Customer has no available credit");
        return;
      }
    }

    if (method === "CARD") {
      setShowCardSelection(true);
    }

    // For cash payments, create order directly
    if (method === "CASH") {
      try {
        // Create order items from cart
        const orderItems = items.map((item) => ({
          productId: item.id,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.price,
          subtotal: item.price * item.quantity,
        }));

        // Create the order using either demo or real API
        if (isDemo) {
          const demoResponse = await demo.createDemoOrder({
            items: orderItems,
            payment: {
              amountPaid: finalTotal,
              totalAmount: finalTotal,
              change: 0,
            },
            customerId: selectedCustomer?.id,
            promotionCode: promotionCode ?? undefined,
            giftVoucherCode: giftVoucherCode ?? undefined,
            discount: discount
              ? {
                  type: discount.type,
                  value: discount.value,
                }
              : undefined,
          });

          if (!demoResponse.success || !demoResponse.order) {
            throw new Error(
              demoResponse.error?.message || "Failed to create order"
            );
          }
        } else {
          const apiResponse = await api.createOrder({
            items: orderItems,
            payment: {
              amountPaid: finalTotal,
              totalAmount: finalTotal,
              change: 0,
            },
            customerId: selectedCustomer?.id,
            promotionCode: promotionCode ?? undefined,
            giftVoucherCode: giftVoucherCode ?? undefined,
            discount: discount
              ? {
                  type: discount.type,
                  value: discount.value,
                }
              : undefined,
          });

          if (apiResponse.status !== ApiStatus.SUCCESS) {
            throw new Error(apiResponse.message || "Failed to create order");
          }
        }

        // Print the order receipt
        handlePrint();

        toast.success("Order completed successfully");
        clearCart();
        setShowOrderCreated(true);
        onOrderComplete?.();
      } catch (error) {
        toast.error(
          error instanceof Error ? error.message : "Failed to process order"
        );
      }
    }
  };

  const handleHoldOrder = async () => {
    if (items.length === 0) {
      toast.error("No items in cart to hold");
      return;
    }

    try {
      await holdCurrentOrder(isDemo);
      toast.success("Order held successfully");
    } catch (error) {
      toast.error("Failed to hold order");
    }
  };

  return (
    <>
      <div className="border-t bg-background p-4">
        <div className="flex justify-end gap-2">
          {buttonVisibility.clear && (
            <Button
              variant="outline"
              onClick={clearCart}
              disabled={items.length === 0}
            >
              Clear
            </Button>
          )}
          {buttonVisibility.discount && (
            <Button
              variant="outline"
              onClick={() => setIsDiscountDialogOpen(true)}
              disabled={items.length === 0}
            >
              Dis
            </Button>
          )}
          {buttonVisibility.hold && (
            <Button
              variant="outline"
              onClick={handleHoldOrder}
              disabled={items.length === 0}
            >
              Hold
            </Button>
          )}
          {buttonVisibility.holdOrders && (
            <Button
              variant="outline"
              onClick={() => setIsHoldOrdersDialogOpen(true)}
            >
              Hold Orders
            </Button>
          )}
          {buttonVisibility.print && (
            <Button
              variant="outline"
              onClick={handlePrint}
              disabled={items.length === 0}
            >
              Print
            </Button>
          )}
          {buttonVisibility.cash && (
            <Button
              variant="default"
              onClick={() => handlePaymentMethodClick("CASH")}
              disabled={items.length === 0}
            >
              Cash
            </Button>
          )}
          {buttonVisibility.cheque && (
            <Button
              variant="default"
              onClick={() => handlePaymentMethodClick("CHEQUE")}
              disabled={items.length === 0 || !selectedCustomer}
            >
              Cheque
            </Button>
          )}
          {buttonVisibility.card && (
            <Button
              variant="default"
              onClick={() => handlePaymentMethodClick("CARD")}
              disabled={items.length === 0}
            >
              Card
            </Button>
          )}
          {buttonVisibility.credit && (
            <Button
              variant="default"
              onClick={() => handlePaymentMethodClick("CREDIT")}
              disabled={items.length === 0 || !selectedCustomer}
            >
              Credit
            </Button>
          )}
          {buttonVisibility.multi && (
            <Button
              variant="default"
              onClick={() => handlePaymentMethodClick("MULTI")}
              disabled={items.length === 0}
            >
              Multi
            </Button>
          )}
          {buttonVisibility.balance && (
            <Button
              variant="secondary"
              onClick={() => setShowBalancePayment(true)}
              disabled={items.length === 0}
            >
              Balance
            </Button>
          )}
          {buttonVisibility.promotion && (
            <Button
              variant="secondary"
              onClick={() => setShowPromotionDialog(true)}
            >
              Promotion
            </Button>
          )}
          {buttonVisibility.giftVoucher && (
            <Button
              variant="secondary"
              onClick={() => setShowGiftVoucherDialog(true)}
            >
              Gift Voucher
            </Button>
          )}
        </div>
      </div>

      <HoldOrdersDialog
        open={isHoldOrdersDialogOpen}
        onOpenChange={setIsHoldOrdersDialogOpen}
        isDemo={isDemo}
      />
      {showCalculator && (
        <Calculator onClose={() => setShowCalculator(false)} />
      )}
      <DiscountDialog
        open={isDiscountDialogOpen}
        onOpenChange={setIsDiscountDialogOpen}
        onApplyDiscount={(amount, isPercentage, reason) =>
          applyDiscount({
            type: isPercentage ? "percentage" : "amount",
            value: amount,
            reason,
          })
        }
      />
      <BalancePaymentDialog
        open={showBalancePayment}
        onOpenChange={setShowBalancePayment}
        totalDue={finalTotal}
        onAccept={() => handlePaymentMethodClick("CASH")}
      />
      <CustomerDialog
        open={showCustomerDialog}
        onOpenChange={setShowCustomerDialog}
        onSelectCustomer={setSelectedCustomer}
      />
      <AddCustomerDialog
        open={showAddCustomerDialog}
        onOpenChange={setShowAddCustomerDialog}
        onAddCustomer={(customer) => {
          const newCustomer = {
            ...customer,
            id: Math.random().toString(36).substr(2, 9),
            creditLimit: 0,
            usedCredit: 0,
          };
          setSelectedCustomer(newCustomer);
        }}
      />
      <CardSelectionDialog
        open={showCardSelection}
        onOpenChange={setShowCardSelection}
        onSelectCard={(cardType) => {
          createOrder(finalTotal);
          handlePrint();
          clearCart();
          setShowOrderCreated(true);
        }}
      />
      <ChequeDetailsDialog
        open={showChequeDetails}
        onOpenChange={setShowChequeDetails}
        onSave={(details) => {
          createOrder(details.reduce((sum, detail) => sum + detail.amount, 0));
          handlePrint();
          clearCart();
          setShowOrderCreated(true);
        }}
        totalDue={finalTotal}
      />
      <MultiPaymentDialog
        open={showMultiPayment}
        onOpenChange={setShowMultiPayment}
        onSave={(payments) => {
          createOrder(
            payments.reduce((sum, payment) => sum + payment.amount, 0)
          );
          handlePrint();
          clearCart();
          setShowOrderCreated(true);
        }}
        totalDue={finalTotal}
        selectedCustomer={selectedCustomer}
      />
      <OrderCreatedDialog
        open={showOrderCreated}
        onOpenChange={(open) => {
          setShowOrderCreated(open);
        }}
      />
      <PromotionDialog
        open={showPromotionDialog}
        onOpenChange={setShowPromotionDialog}
        isDemo={isDemo}
      />
      <GiftVoucherDialog
        open={showGiftVoucherDialog}
        onOpenChange={setShowGiftVoucherDialog}
        isDemo={isDemo}
      />
    </>
  );
}
