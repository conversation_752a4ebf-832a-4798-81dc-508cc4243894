"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { usePOSStore } from "./pos-store";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useEffect } from "react";
import { toast } from "sonner";
import { HoldOrder } from "@/types/pos";

interface HoldOrdersDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isDemo: boolean;
}

export function HoldOrdersDialog({
  open,
  onOpenChange,
  isDemo,
}: HoldOrdersDialogProps) {
  const holdOrders = usePOSStore((state) => state.holdOrders);
  const selectHoldOrder = usePOSStore((state) => state.selectHoldOrder);
  const deleteHoldOrder = usePOSStore((state) => state.deleteHoldOrder);
  const fetchHoldOrders = usePOSStore((state) => state.fetchHoldOrders);

  // Fetch hold orders when dialog opens
  useEffect(() => {
    if (open) {
      fetchHoldOrders(isDemo).catch((error) => {
        toast.error("Failed to fetch hold orders");
      });
    }
  }, [open, isDemo, fetchHoldOrders, toast]);

  const handleSelect = async (order: HoldOrder) => {
    try {
      await selectHoldOrder(order, isDemo);
      onOpenChange(false);
      toast.success("Hold order selected successfully");
    } catch (error) {
      toast.error("Failed to select hold order");
    }
  };

  const handleDelete = async (orderId: string) => {
    try {
      await deleteHoldOrder(orderId, isDemo);
      toast.success("Hold order deleted successfully");
    } catch (error) {
      toast.error("Failed to delete hold order");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[1200px]">
        <DialogHeader>
          <DialogTitle>Hold Order View ({holdOrders.length})</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[400px] w-full">
          <div className="space-y-4">
            <div className="grid grid-cols-6 gap-6 px-4 py-2 bg-muted text-sm font-medium">
              <div>Date</div>
              <div>User</div>
              <div>Hold Order ID</div>
              <div>Customer ID</div>
              <div>Net Amount (Rs)</div>
              <div>Actions</div>
            </div>
            {holdOrders.length === 0 ? (
              <div className="px-4 py-8 text-center text-muted-foreground">
                No hold orders found
              </div>
            ) : (
              holdOrders.map((order) => (
                <div
                  key={order.id}
                  className="grid grid-cols-6 gap-6 px-4 py-2 text-sm items-center hover:bg-accent"
                >
                  <div>{order.date}</div>
                  <div>{order.user}</div>
                  <div>{order.id}</div>
                  <div>{order.customerId || "-"}</div>
                  <div>{order.netAmount.toLocaleString()}</div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleSelect(order)}
                    >
                      SELECT
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(order.id)}
                    >
                      CANCEL
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
