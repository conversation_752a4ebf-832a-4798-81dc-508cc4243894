"use client";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { usePOSStore } from "./pos-store";
import { useState } from "react";
import { OrderCreatedDialog } from "./order-created-dialog";
import { CardSelectionDialog } from "./card-selection-dialog";
import { CustomerDialog } from "./customer-dialog";
import {
  ChequeDetailsDialog,
  type ChequeDetails,
} from "./cheque-details-dialog";
import type { Customer } from "./customer-dialog";
import { MultiPaymentDialog, type PaymentDetail } from "./multi-payment-dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, CheckCircle, Printer, Send, Camera } from "lucide-react";
import { Input } from "@/components/ui/input";

interface PaymentViewProps {
  onCancel: () => void;
}

type PaymentMethod = "CASH" | "CHEQUE" | "CARD" | "CREDIT" | "MULTI";

interface PaymentEntry {
  method: PaymentMethod;
  amount: number;
}

interface ReceiptItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  description?: string;
}

export function PaymentView({ onCancel }: PaymentViewProps) {
  const { total: getTotal, finalTotal: getFinalTotal } = usePOSStore();
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>("CASH");
  const [amountInput, setAmountInput] = useState<string>("");
  const [payments, setPayments] = useState<PaymentEntry[]>([
    { method: "CASH", amount: 2771.5 },
  ]);
  const [paymentComplete, setPaymentComplete] = useState(false);
  const [emailInput, setEmailInput] = useState("<EMAIL>");

  // Sample receipt items for the demo
  const receiptItems: ReceiptItem[] = [
    { id: "1", name: "Drawer Black", quantity: 1, price: 28.75 },
    {
      id: "2",
      name: "Acoustic Bloc Screens",
      quantity: 1,
      price: 339.25,
      description: "Wood",
    },
    { id: "3", name: "Flipover", quantity: 1, price: 2242.5 },
    { id: "4", name: "Cabinet with Doors", quantity: 1, price: 161.0 },
  ];

  // Calculate subtotal and tax for the receipt
  const subtotal = receiptItems.reduce((sum, item) => sum + item.price, 0);
  const taxRate = 0.15; // 15%
  const taxAmount = subtotal * taxRate;
  const receiptTotal = subtotal + taxAmount;

  const total = getFinalTotal();
  const formattedTotal = `${total.toLocaleString()} Rs`;

  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
  const remaining = total - totalPaid;
  const hasChange = remaining < 0;
  const changeAmount = Math.abs(remaining);
  const formattedRemaining = hasChange
    ? "0.00 Rs"
    : `${Math.max(0, remaining).toLocaleString()} Rs`;
  const formattedChange = hasChange
    ? `${changeAmount.toLocaleString()} Rs`
    : "0.00 Rs";

  const handlePaymentMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    // For cash payments, we might want to pre-fill with the total amount
    if (method === "CASH") {
      setAmountInput(Math.max(0, remaining).toString());
    } else {
      setAmountInput("");
    }
  };

  const handleNumberInput = (value: string) => {
    if (value === "." && amountInput.includes(".")) {
      return; // Prevent multiple decimal points
    }

    setAmountInput((prev) => {
      // If starting with decimal, add a leading zero
      if (prev === "" && value === ".") {
        return "0.";
      }
      return prev + value;
    });
  };

  const handleBackspace = () => {
    setAmountInput((prev) => prev.slice(0, -1));
  };

  const handleAddPayment = () => {
    if (!selectedMethod) {
      toast.error("Please select a payment method");
      return;
    }

    const amount = parseFloat(amountInput);
    if (isNaN(amount) || amount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    // Add the payment to the list
    setPayments((prev) => [...prev, { method: selectedMethod, amount }]);

    // Reset input and selection for next payment
    setAmountInput("");
  };

  const handleRemovePayment = (index: number) => {
    setPayments((prev) => prev.filter((_, i) => i !== index));
  };

  const handleValidate = () => {
    if (payments.length === 0) {
      toast.error("Please add at least one payment method");
      return;
    }

    if (remaining > 0) {
      toast.error("The total amount has not been fully paid");
      return;
    }

    // Mark payment as complete
    setPaymentComplete(true);

    // Here you would process the payment based on the payment entries
    toast.success(`Payment of ${total} processed with ${payments.length} payment methods`);
  };

  const handleEditPayment = () => {
    setPaymentComplete(false);
  };

  const handlePrintReceipt = () => {
    toast.success("Receipt is being printed");
  };

  const handleSendEmail = () => {
    if (!emailInput || !emailInput.includes("@")) {
      toast.error("Please enter a valid email address");
      return;
    }

    toast.success(`Receipt has been sent to ${emailInput}`);
    setEmailInput("");
  };

  const handleNewOrder = () => {
    // Reset the payment view and go back to the register
    setPaymentComplete(false);
    setPayments([]);
    setAmountInput("");
    onCancel();
  };

  const getMethodLabel = (method: PaymentMethod) => {
    switch (method) {
      case "CASH":
        return "Cash";
      case "CARD":
        return "Card";
      case "CHEQUE":
        return "Cheque";
      case "CREDIT":
        return "Customer Account";
      case "MULTI":
        return "Multiple";
      default:
        return method;
    }
  };

  // Payment success view
  if (paymentComplete) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex flex-1">
          {/* Left side - Success message and actions */}
          <div className="w-1/2 p-4 flex flex-col">
            {/* Success message */}
            <div className="w-full bg-green-100 rounded-md p-6 mb-6">
              <div className="flex flex-col items-center text-center">
                <CheckCircle className="text-green-600 h-12 w-12 mb-2" />
                <h2 className="text-xl font-semibold text-green-800 mb-1">
                  Payment Successful
                </h2>
                <p className="text-2xl font-bold mb-2">{formattedTotal}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-green-700 border-green-300 hover:bg-green-50"
                  onClick={handleEditPayment}
                >
                  Edit Payment
                </Button>
              </div>
            </div>

            {/* Print receipt button */}
            <Button
              variant="outline"
              className="w-full mb-6 py-6 flex items-center justify-center"
              onClick={handlePrintReceipt}
            >
              <Printer className="mr-2 h-5 w-5" />
              Print Full Receipt
            </Button>

            {/* Email receipt */}
            <div className="w-full flex mb-6">
              <Input
                type="email"
                placeholder="e.g. <EMAIL>"
                className="flex-1 rounded-r-none"
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
              />
              <Button
                variant="secondary"
                className="rounded-l-none bg-gray-200"
                onClick={handleSendEmail}
              >
                <Send className="h-5 w-5" />
              </Button>
            </div>

            {/* New order button */}
            <Button
              className="w-full py-6 mt-auto bg-purple-700 hover:bg-purple-800 text-white"
              onClick={handleNewOrder}
            >
              New Order
            </Button>
          </div>

          {/* Right side - Receipt preview */}
          <div className="w-1/2 p-4 border-l">
            <div className="bg-white rounded-md border shadow-sm h-full overflow-auto">
              <div className="p-6 flex flex-col">
                {/* Receipt header */}
                <div className="flex flex-col items-center mb-4">
                  <div className="flex items-center text-purple-800 mb-2">
                    <Camera className="h-6 w-6 mr-2" />
                    <span className="text-xl font-semibold">Your logo</span>
                  </div>
                  <p className="text-sm text-gray-600">{emailInput}</p>
                  <div className="w-full border-t border-dashed my-2"></div>
                </div>

                {/* Receipt items */}
                <div className="mb-4">
                  {receiptItems.map((item) => (
                    <div key={item.id} className="flex mb-2">
                      <div className="w-8 text-center">{item.quantity}</div>
                      <div className="flex-1">
                        <div>{item.name}</div>
                        {item.description && (
                          <div className="text-sm text-gray-500 italic">
                            {item.description}
                          </div>
                        )}
                      </div>
                      <div className="text-right">
                        {item.price.toFixed(2)} Rs
                      </div>
                    </div>
                  ))}
                </div>

                {/* Receipt divider */}
                <div className="w-full border-t border-dashed my-2"></div>

                {/* Receipt totals */}
                <div className="mb-4">
                  <div className="flex justify-between">
                    <div>Untaxed Amount</div>
                    <div>{subtotal.toFixed(2)} Rs</div>
                  </div>
                  <div className="flex justify-between">
                    <div>Tax 15%</div>
                    <div>{taxAmount.toFixed(2)} Rs</div>
                  </div>
                </div>

                {/* Receipt divider */}
                <div className="w-full border-t border-dashed my-2"></div>

                {/* Receipt final total */}
                <div className="mb-4">
                  <div className="flex justify-between font-bold">
                    <div>TOTAL</div>
                    <div>{receiptTotal.toFixed(2)} Rs</div>
                  </div>
                  <div className="flex justify-between">
                    <div>Cash</div>
                    <div>{totalPaid.toFixed(2)} Rs</div>
                  </div>
                  <div className="flex justify-between">
                    <div>CHANGE</div>
                    <div>{changeAmount.toFixed(2)} Rs</div>
                  </div>
                </div>

                {/* Receipt footer */}
                <div className="mt-auto text-center text-xs text-gray-500">
                  <p>Powered by ShopC</p>
                  <p>2501-003-00001</p>
                  <p>
                    {new Date().toLocaleDateString()}{" "}
                    {new Date().toLocaleTimeString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="flex flex-1">
        {/* Left side - Payment methods */}
        <div className="w-1/3 border-r">
          <div className="flex flex-col h-full">
            <Button
              variant="ghost"
              className={cn(
                "flex items-center justify-start py-6 px-4 border-b rounded-none",
                selectedMethod === "CASH" ? "bg-blue-50" : "hover:bg-gray-50"
              )}
              onClick={() => handlePaymentMethodSelect("CASH")}
            >
              <span className="mr-2">💵</span> Cash
            </Button>

            <Button
              variant="ghost"
              className={cn(
                "flex items-center justify-start py-6 px-4 border-b rounded-none",
                selectedMethod === "CARD" ? "bg-blue-50" : "hover:bg-gray-50"
              )}
              onClick={() => handlePaymentMethodSelect("CARD")}
            >
              <span className="mr-2">💳</span> Card
            </Button>

            <Button
              variant="ghost"
              className={cn(
                "flex items-center justify-start py-6 px-4 border-b rounded-none",
                selectedMethod === "CREDIT" ? "bg-blue-50" : "hover:bg-gray-50"
              )}
              onClick={() => handlePaymentMethodSelect("CREDIT")}
            >
              <span className="mr-2">👤</span> Customer Account
            </Button>
          </div>
        </div>

        {/* Right side - Total and payment entries */}
        <div className="w-2/3 p-4">
          <div className="text-right mb-8">
            <div className="text-4xl font-bold text-green-700">
              {formattedTotal}
            </div>
          </div>

          {/* Payment entries */}
          <div className="space-y-2">
            {payments.map((payment, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-3 rounded-md border bg-gray-50"
              >
                <div>{getMethodLabel(payment.method)}</div>
                <div className="flex items-center">
                  <span className="mr-2">
                    {payment.amount.toLocaleString()} Rs
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-red-500"
                    onClick={() => handleRemovePayment(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Remaining amount */}
          <div className="flex justify-between items-center p-2 mt-4">
            <div className="font-medium text-green-700">Remaining</div>
            <div className="font-medium">{formattedRemaining}</div>
          </div>

          {/* Change amount - only shown when there's change to give back */}
          {hasChange && (
            <div className="flex justify-between items-center p-2 mt-1 bg-green-50 rounded-md border border-green-200">
              <div className="font-medium text-green-700">Change</div>
              <div className="font-medium text-green-700">
                {formattedChange}
              </div>
            </div>
          )}

          {/* Amount input display */}
          <div className="mt-4 p-3 bg-gray-50 border rounded-md">
            <p className="text-right text-xl font-mono">
              {amountInput || "0.00"}
            </p>
          </div>
        </div>
      </div>

      {/* Bottom section - Numeric keypad */}
      <div className="border-t">
        <div className="grid grid-cols-4 gap-px bg-gray-200">
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("1")}
          >
            1
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("2")}
          >
            2
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("3")}
          >
            3
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100 text-green-600 font-medium"
            onClick={() =>
              setAmountInput((Math.max(0, remaining) + 10).toString())
            }
          >
            +10
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("4")}
          >
            4
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("5")}
          >
            5
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("6")}
          >
            6
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100 text-green-600 font-medium"
            onClick={() =>
              setAmountInput((Math.max(0, remaining) + 20).toString())
            }
          >
            +20
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("7")}
          >
            7
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("8")}
          >
            8
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("9")}
          >
            9
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100 text-green-600 font-medium"
            onClick={() =>
              setAmountInput((Math.max(0, remaining) + 50).toString())
            }
          >
            +50
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100 text-yellow-600"
            onClick={() =>
              setAmountInput((prev) => {
                const num = parseFloat(prev);
                return isNaN(num) ? "0" : (-num).toString();
              })
            }
          >
            +/-
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput("0")}
          >
            0
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={() => handleNumberInput(".")}
          >
            .
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100 text-red-600"
            onClick={handleBackspace}
          >
            ⌫
          </Button>
        </div>

        {/* Action buttons */}
        <div className="grid grid-cols-2 gap-px bg-gray-200">
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-white hover:bg-gray-100"
            onClick={onCancel}
          >
            Back
          </Button>
          <Button
            variant="ghost"
            className="h-16 rounded-none bg-purple-700 text-white hover:bg-purple-800"
            onClick={
              selectedMethod && amountInput ? handleAddPayment : handleValidate
            }
          >
            {selectedMethod && amountInput ? "Add Payment" : "Validate"}
          </Button>
        </div>
      </div>
    </div>
  );
}
