import { create } from "zustand";
import { toast } from "sonner";
import {
  POSProduct,
  ProductVariant,
  HoldOrder,
  GetHoldOrdersResponse,
  CreateHoldOrderResponse,
  DeleteHoldOrderResponse,
  type POSSettings as BasePOSSettings,
  type GiftVoucher as BaseGiftVoucher,
} from "@/types/pos";
import * as api from "@/lib/pos/api";
import * as demo from "@/lib/pos/demo";
import { ApiStatus } from "@/types/common";

type PaymentMethod = "CASH" | "CHEQUE" | "CARD" | "CREDIT" | "MULTI";

interface CartItem extends POSProduct {
  quantity: number;
  notes?: string;
  selectedVariant?: ProductVariant;
  itemDiscount?: {
    type: "percentage" | "amount";
    value: number;
    reason?: string;
  };
  discount?: {
    type: "percentage" | "amount";
    value: number;
    reason?: string;
  };
}

interface Discount {
  type: "percentage" | "amount";
  value: number;
  reason?: string;
}

interface PaymentDetails {
  orderId: string;
  orderTotal: number;
  cashReceived: number;
  balance: number;
}

interface RecentProduct {
  id: string;
  code: string;
  name: string;
  price: number;
  category?: string;
  subCategory?: string;
  timestamp: number;
}

interface FavoriteProduct {
  id: string;
  code: string;
  name: string;
  price: number;
  category?: string;
  subCategory?: string;
  imageUrl?: string;
}

interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  creditLimit: number;
  usedCredit: number;
}

interface PromotionCode {
  id: string;
  code: string;
  type: "PERCENTAGE" | "FIXED";
  value: number;
  minPurchase?: number;
  maxDiscount?: number;
  validFrom: string;
  validTo: string;
  isActive: boolean;
  usageLimit?: number;
  usageCount: number;
  applicableCategories?: string[];
}

type ButtonVisibilityKeys =
  | "clear"
  | "discount"
  | "hold"
  | "holdOrders"
  | "print"
  | "cash"
  | "cheque"
  | "card"
  | "credit"
  | "multi"
  | "balance"
  | "promotion"
  | "giftVoucher";

type ToolbarButtonVisibilityKeys =
  | "barcode"
  | "settings"
  | "holdOrders"
  | "favorites"
  | "recent"
  | "gridView"
  | "images"
  | "calculator";

type ButtonVisibility = Record<ButtonVisibilityKeys | string, boolean>;
type ToolbarButtonVisibility = Record<
  ToolbarButtonVisibilityKeys | string,
  boolean
>;

interface BillInvoiceSettings {
  template: "standard" | "detailed" | "compact";
  showLogo: boolean;
  showBarcode: boolean;
  showFooter: boolean;
}

interface ExtendedPOSSettings
  extends Omit<
    BasePOSSettings,
    "buttonVisibility" | "toolbarButtonVisibility"
  > {
  buttonVisibility: Record<string, boolean>;
  toolbarButtonVisibility: Record<string, boolean>;
  billInvoiceSettings: BillInvoiceSettings;
}

const DEFAULT_BUTTON_VISIBILITY: ButtonVisibility = {
  clear: true,
  discount: true,
  hold: true,
  holdOrders: true,
  print: true,
  cash: true,
  cheque: true,
  card: true,
  credit: true,
  multi: true,
  balance: true,
  promotion: true,
  giftVoucher: true,
};

const DEFAULT_TOOLBAR_VISIBILITY: ToolbarButtonVisibility = {
  barcode: true,
  settings: true,
  holdOrders: true,
  favorites: true,
  recent: true,
  gridView: true,
  images: true,
  calculator: true,
};

const DEFAULT_BILL_INVOICE_SETTINGS: BillInvoiceSettings = {
  template: "standard" as const,
  showLogo: true,
  showBarcode: true,
  showFooter: true,
};

const DEFAULT_SETTINGS: ExtendedPOSSettings = {
  buttonVisibility: DEFAULT_BUTTON_VISIBILITY,
  toolbarButtonVisibility: DEFAULT_TOOLBAR_VISIBILITY,
  billInvoiceSettings: DEFAULT_BILL_INVOICE_SETTINGS,
};

interface POSStore {
  // Cart state
  items: CartItem[];
  discount: Discount | null;
  showPayment: boolean;
  holdOrders: HoldOrder[];
  lastPaymentDetails: PaymentDetails | null;

  // Search state
  searchQuery: string;
  showSearch: boolean;

  // Category state
  selectedCategory: string | null;
  selectedSubCategory: string | null;
  isGridView: boolean;
  showRecentProducts: boolean;
  recentProducts: RecentProduct[];
  showFavorites: boolean;
  favoriteProducts: FavoriteProduct[];

  // Customer state
  selectedCustomer: Customer | null;

  // Dialog states
  showOrderCreated: boolean;
  showCustomerDialog: boolean;
  showChequeDetails: boolean;
  showCardSelection: boolean;
  showMultiPayment: boolean;

  // Promotion and Gift Voucher state
  appliedPromotion: PromotionCode | null;
  appliedGiftVoucher: BaseGiftVoucher | null;

  // UI state
  showImages: boolean;
  buttonVisibility: ButtonVisibility;
  toolbarButtonVisibility: ToolbarButtonVisibility;
  billInvoiceSettings: BillInvoiceSettings;

  // Cash register state
  isRegisterOpen: boolean;
  openingCash: number;
  openingNote: string;
  registerOpenedAt: Date | null;

  // Actions
  addItem: (item: Omit<CartItem, "quantity">) => void;
  removeItem: (id: string) => void;
  updateCartItem: (id: string, updates: Partial<CartItem>) => void;
  clearCart: () => void;
  applyDiscount: (discount: Discount) => void;
  clearDiscount: () => void;
  setShowPayment: (show: boolean) => void;
  setButtonVisibility: (settings: ButtonVisibility) => void;
  setToolbarButtonVisibility: (settings: ToolbarButtonVisibility) => void;
  resetButtonVisibility: () => void;
  resetToolbarButtonVisibility: () => void;
  setBillInvoiceSettings: (settings: BillInvoiceSettings) => void;
  resetBillInvoiceSettings: () => void;

  // Dialog actions
  setShowOrderCreated: (show: boolean) => void;
  setShowCustomerDialog: (show: boolean) => void;
  setShowChequeDetails: (show: boolean) => void;
  setShowCardSelection: (show: boolean) => void;
  setShowMultiPayment: (show: boolean) => void;

  // Payment actions
  handlePaymentMethodClick: (method: PaymentMethod) => void;
  handlePrint: () => void;
  createOrder: (amount: number) => void;
  clearPaymentDetails: () => void;

  // Category actions
  setSelectedCategory: (category: string | null) => void;
  setSelectedSubCategory: (subCategory: string | null) => void;
  setIsGridView: (isGrid: boolean) => void;
  setShowRecentProducts: (show: boolean) => void;
  setShowFavorites: (show: boolean) => void;
  addToRecentProducts: (product: Omit<RecentProduct, "timestamp">) => void;

  // Hold order actions
  holdCurrentOrder: (isDemo: boolean) => Promise<void>;
  selectHoldOrder: (holdOrder: HoldOrder, isDemo: boolean) => Promise<void>;
  cancelHoldOrder: (id: string) => void;

  // Customer actions
  setSelectedCustomer: (customer: Customer | null) => void;

  // Search actions
  setSearchQuery: (query: string) => void;
  setShowSearch: (show: boolean) => void;

  // Promotion and Gift Voucher actions
  applyPromotionCode: (code: string, isDemo: boolean) => Promise<boolean>;
  applyGiftVoucher: (code: string, isDemo: boolean) => Promise<boolean>;
  clearPromotionCode: () => void;
  clearGiftVoucher: () => void;

  // UI actions
  setShowImages: (show: boolean) => void;

  // Computed values
  subtotal: () => number;
  discountAmount: () => number;
  total: () => number;
  serviceCharge: () => number;
  finalTotal: () => number;

  // New hold order state and actions
  isHoldOrdersDialogOpen: boolean;
  setIsHoldOrdersDialogOpen: (isOpen: boolean) => void;
  fetchHoldOrders: (isDemo: boolean) => Promise<void>;
  deleteHoldOrder: (holdOrderId: string, isDemo: boolean) => Promise<void>;

  calculateDiscount: (price: number, discount: Discount | null) => number;

  showBalancePayment: boolean;
  setShowBalancePayment: (show: boolean) => void;

  updateCartItemDiscount: (
    itemId: string,
    itemDiscount: { type: "percentage" | "amount"; value: number } | null
  ) => void;

  // Favorite products actions
  fetchFavoriteProducts: (isDemo: boolean) => Promise<void>;
  addToFavorites: (product: POSProduct, isDemo: boolean) => Promise<void>;
  removeFromFavorites: (productId: string, isDemo: boolean) => Promise<void>;

  promotionCode: string | null;
  giftVoucherCode: string | null;
  setPromotionCode: (code: string | null) => void;
  setGiftVoucherCode: (code: string | null) => void;

  toggleButtonVisibility: (buttonId: string, visible: boolean) => void;
  toggleToolbarButtonVisibility: (buttonId: string, visible: boolean) => void;
  resetVisibilitySettings: () => void;
  saveSettings: (isDemo: boolean) => Promise<void>;
  loadSettings: (isDemo: boolean) => Promise<void>;

  // Cash register actions
  openRegister: (amount: number, note: string) => void;
  closeRegister: () => void;
}

export const usePOSStore = create<POSStore>((set, get) => ({
  // Initial state
  items: [],
  discount: null,
  showPayment: false,
  holdOrders: [],
  selectedCategory: null,
  selectedSubCategory: null,
  lastPaymentDetails: null,
  isGridView: false,
  showRecentProducts: false,
  recentProducts: [],
  showFavorites: false,
  favoriteProducts: [],
  selectedCustomer: null,
  searchQuery: "",
  showSearch: false,

  // Dialog states
  showOrderCreated: false,
  showCustomerDialog: false,
  showChequeDetails: false,
  showCardSelection: false,
  showMultiPayment: false,

  // Promotion and Gift Voucher initial state
  appliedPromotion: null,
  appliedGiftVoucher: null,

  // UI initial state
  showImages: true,
  buttonVisibility: DEFAULT_BUTTON_VISIBILITY,
  toolbarButtonVisibility: DEFAULT_TOOLBAR_VISIBILITY,
  billInvoiceSettings: DEFAULT_BILL_INVOICE_SETTINGS,

  // Cash register state
  isRegisterOpen: false,
  openingCash: 0,
  openingNote: "",
  registerOpenedAt: null,

  // Actions
  addItem: (item) =>
    set((state) => {
      const existingItem = state.items.find((i) => i.id === item.id);
      if (existingItem) {
        return {
          items: state.items.map((i) =>
            i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
          ),
        };
      }
      return {
        items: [...state.items, { ...item, quantity: 1 }],
      };
    }),

  removeItem: (id) =>
    set((state) => ({
      items: state.items.filter((item) => item.id !== id),
    })),

  updateCartItem: (id, updates) =>
    set((state) => ({
      items: state.items.map((item) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    })),

  clearCart: () =>
    set({
      items: [],
      discount: null,
    }),

  applyDiscount: (discount: Discount) =>
    set({
      discount,
    }),

  clearDiscount: () =>
    set({
      discount: null,
    }),

  setShowPayment: (show) =>
    set({
      showPayment: show,
    }),

  setIsGridView: (isGrid) =>
    set({
      isGridView: isGrid,
    }),

  setShowRecentProducts: (show) =>
    set({
      showRecentProducts: show,
      showFavorites: false, // Hide favorites when showing recent
      // Reset category selection when showing recent products
      ...(show ? { selectedCategory: null, selectedSubCategory: null } : {}),
    }),

  setShowFavorites: (show) =>
    set({
      showFavorites: show,
      showRecentProducts: false, // Hide recent when showing favorites
      // Reset category selection when showing favorites
      ...(show ? { selectedCategory: null, selectedSubCategory: null } : {}),
    }),

  addToRecentProducts: (product) =>
    set((state) => {
      const newProduct = { ...product, timestamp: Date.now() };
      const existingProducts = state.recentProducts.filter(
        (p) => p.id !== product.id
      );
      return {
        recentProducts: [newProduct, ...existingProducts].slice(0, 20), // Keep only last 20 products
      };
    }),

  // Dialog actions
  setShowOrderCreated: (show) => set({ showOrderCreated: show }),
  setShowCustomerDialog: (show) => set({ showCustomerDialog: show }),
  setShowChequeDetails: (show) => set({ showChequeDetails: show }),
  setShowCardSelection: (show) => set({ showCardSelection: show }),
  setShowMultiPayment: (show) => set({ showMultiPayment: show }),

  handlePaymentMethodClick: (method: PaymentMethod) => {
    const { items, discount, selectedCustomer, createOrder } = get();
    const total = get().total();

    if (items.length === 0) {
      toast.error("No items in cart");
      return;
    }

    if (method === "CREDIT" && !selectedCustomer) {
      toast.error("Please select a customer for credit payment");
      return;
    }

    if (method === "CHEQUE" && !selectedCustomer) {
      toast.error("Please select a customer for cheque payment");
      return;
    }

    switch (method) {
      case "CASH":
        set({ showBalancePayment: true });
        break;
      case "CARD":
        set({ showCardSelection: true });
        break;
      case "CHEQUE":
        set({ showChequeDetails: true });
        break;
      case "CREDIT":
        if (selectedCustomer) {
          const availableCredit =
            selectedCustomer.creditLimit - selectedCustomer.usedCredit;
          if (total > availableCredit) {
            toast.error("Insufficient credit limit");
            return;
          }
          createOrder(total);
        }
        break;
      case "MULTI":
        set({ showMultiPayment: true });
        break;
    }
  },

  handlePrint: () => {
    const state = get();
    const {
      items,
      selectedCustomer,
      discount,
      appliedPromotion,
      appliedGiftVoucher,
      billInvoiceSettings,
    } = state;

    const subtotal = state.subtotal();
    const discountAmount = state.discountAmount();
    const total = state.total();
    const serviceCharge = state.serviceCharge();
    const finalTotal = state.finalTotal();

    const getTemplateStyles = () => {
      switch (billInvoiceSettings.template) {
        case "detailed":
          return `
            .receipt {
              font-family: 'Arial', sans-serif;
              width: 80mm;
              padding: 10mm;
            }
            .header {
              text-align: center;
              margin-bottom: 10mm;
            }
            .logo {
              max-width: 60mm;
              margin-bottom: 5mm;
            }
            .business-info {
              margin-bottom: 5mm;
              font-size: 12px;
            }
            .customer-info {
              margin-bottom: 5mm;
              padding: 5px;
              border: 1px solid #000;
              font-size: 12px;
            }
            .items {
              margin: 5mm 0;
              border-top: 1px solid #000;
              border-bottom: 1px solid #000;
              padding: 5mm 0;
            }
            .item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 2mm;
              font-size: 12px;
            }
            .item-discount {
              padding-left: 10mm;
              color: #666;
              font-size: 11px;
            }
            .total {
              margin-top: 5mm;
            }
            .footer {
              margin-top: 10mm;
              text-align: center;
              font-size: 11px;
            }
            .barcode {
              margin-top: 5mm;
              text-align: center;
            }
          `;
        case "compact":
          return `
            .receipt {
              font-family: 'Courier', monospace;
              width: 58mm;
              padding: 5mm;
              font-size: 10px;
            }
            .header {
              text-align: center;
              margin-bottom: 5mm;
            }
            .logo {
              display: none;
            }
            .business-info {
              margin-bottom: 3mm;
            }
            .customer-info {
              margin-bottom: 3mm;
            }
            .items {
              margin: 3mm 0;
            }
            .item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 1mm;
            }
            .item-discount {
              padding-left: 5mm;
            }
            .total {
              margin-top: 3mm;
            }
            .footer {
              margin-top: 5mm;
              text-align: center;
              font-size: 9px;
            }
            .barcode {
              display: none;
            }
          `;
        default: // standard template
          return `
            .receipt {
              font-family: 'Arial', sans-serif;
              width: 72mm;
              padding: 8mm;
            }
            .header {
              text-align: center;
              margin-bottom: 8mm;
            }
            .logo {
              max-width: 50mm;
              margin-bottom: 4mm;
            }
            .business-info {
              margin-bottom: 4mm;
              font-size: 11px;
            }
            .customer-info {
              margin-bottom: 4mm;
              font-size: 11px;
            }
            .items {
              margin: 4mm 0;
            }
            .item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 2mm;
              font-size: 11px;
            }
            .item-discount {
              padding-left: 8mm;
              color: #666;
              font-size: 10px;
            }
            .total {
              margin-top: 4mm;
            }
            .footer {
              margin-top: 8mm;
              text-align: center;
              font-size: 10px;
            }
            .barcode {
              margin-top: 4mm;
              text-align: center;
            }
          `;
      }
    };

    const receiptHTML = `
      <style>
        ${getTemplateStyles()}
      </style>
      <div class="receipt">
        <div class="header">
          ${
            billInvoiceSettings.showLogo
              ? '<img src="/logo.png" alt="Business Logo" class="logo" />'
              : ""
          }
          <div class="business-info">
            <div>Your Business Name</div>
            <div>123 Business Street</div>
            <div>City, Country</div>
            <div>Tel: +1234567890</div>
          </div>
        </div>

        ${
          selectedCustomer
            ? `
          <div class="customer-info">
            <div>Customer: ${selectedCustomer.name}</div>
            ${
              selectedCustomer.phone
                ? `<div>Phone: ${selectedCustomer.phone}</div>`
                : ""
            }
            ${
              selectedCustomer.email
                ? `<div>Email: ${selectedCustomer.email}</div>`
                : ""
            }
          </div>
        `
            : ""
        }
        
        <div class="items">
          ${items
            .map(
              (item) => `
            <div class="item">
              <span>${item.name} x${item.quantity}</span>
              <span>Rs ${(item.price * item.quantity).toLocaleString()}</span>
            </div>
            ${
              item.itemDiscount
                ? `
              <div class="item-discount">
                Discount: ${
                  item.itemDiscount.type === "percentage"
                    ? `${item.itemDiscount.value}%`
                    : `Rs ${item.itemDiscount.value}`
                }
              </div>
            `
                : ""
            }
          `
            )
            .join("")}
        </div>

        <div class="total">
          <div class="item">
            <strong>Subtotal:</strong>
            <span>Rs ${subtotal.toLocaleString()}</span>
          </div>
          ${
            discount
              ? `
            <div class="item">
              <span>Discount ${
                discount.type === "percentage" ? `(${discount.value}%)` : ""
              }:</span>
              <span>Rs ${discountAmount.toLocaleString()}</span>
            </div>
          `
              : ""
          }
          ${
            appliedPromotion
              ? `
            <div class="item">
              <span>Promotion (${appliedPromotion.code}) ${
                  appliedPromotion.type === "PERCENTAGE"
                    ? `(${appliedPromotion.value}%)`
                    : ""
                }:</span>
              <span>Rs ${(
                (appliedPromotion.type === "PERCENTAGE"
                  ? (subtotal * appliedPromotion.value) / 100
                  : appliedPromotion.value) * -1
              ).toLocaleString()}</span>
            </div>
          `
              : ""
          }
          ${
            appliedGiftVoucher
              ? `
            <div class="item">
              <span>Gift Voucher (${appliedGiftVoucher.code}):</span>
              <span>Rs ${(
                appliedGiftVoucher.originalValue * -1
              ).toLocaleString()}</span>
            </div>
          `
              : ""
          }
          <div class="item">
            <span>Service Charge (18%):</span>
            <span>Rs ${serviceCharge.toLocaleString()}</span>
          </div>
          <div class="item" style="margin-top: 2mm; font-weight: bold;">
            <span>Final Total:</span>
            <span>Rs ${finalTotal.toLocaleString()}</span>
          </div>
        </div>

        ${
          billInvoiceSettings.showFooter
            ? `
          <div class="footer">
            <div>Thank you for your business!</div>
            <div>Please come again</div>
          </div>
        `
            : ""
        }

        ${
          billInvoiceSettings.showBarcode
            ? `
          <div class="barcode">
            <svg id="barcode"></svg>
          </div>
        `
            : ""
        }
      </div>
    `;

    // Create a new window for printing
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(receiptHTML);
      printWindow.document.close();

      // If barcode is enabled, generate it
      if (billInvoiceSettings.showBarcode) {
        const script = printWindow.document.createElement("script");
        script.src =
          "https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js";
        script.onload = () => {
          // @ts-ignore
          printWindow.JsBarcode("#barcode", new Date().getTime().toString(), {
            format: "CODE128",
            width: 2,
            height: 50,
            displayValue: true,
          });
          // Print after barcode is generated
          printWindow.print();
          printWindow.close();
        };
        printWindow.document.head.appendChild(script);
      } else {
        // Print immediately if no barcode
        printWindow.print();
        printWindow.close();
      }
    }
  },

  createOrder: (amount) => {
    // Implementation of order creation
    set({ showOrderCreated: true });
  },

  clearPaymentDetails: () =>
    set({
      lastPaymentDetails: null,
    }),

  // Category actions
  setSelectedCategory: (category) =>
    set({
      selectedCategory: category,
      selectedSubCategory: null, // Reset subcategory when category changes
      showRecentProducts: false, // Hide recent products when category changes
      showFavorites: false, // Hide favorites when category changes
    }),

  setSelectedSubCategory: (subCategory) =>
    set({
      selectedSubCategory: subCategory,
      showRecentProducts: false, // Hide recent products when subcategory changes
      showFavorites: false, // Hide favorites when subcategory changes
    }),

  // Hold order actions
  holdCurrentOrder: async (isDemo) => {
    const { items, discount, selectedCustomer } = get();
    if (items.length === 0) return;

    const productsFromCart: POSProduct[] = items.map((item) => ({
      id: item.id,
      code: item.code,
      name: item.name,
      quantity: item.quantity,
      price: item.price,
      variants: item.variants,
      bid: item.bid,
      category: item.category,
      subCategory: item.subCategory,
      maxDiscount: item.maxDiscount,
      defaultDiscount: item.defaultDiscount,
      trackInventory: item.trackInventory,
      reorderQuantity: item.reorderQuantity,
      expiryDate: item.expiryDate,
      uom: item.uom,
      wholesalePrice: item.wholesalePrice,
      imageUrl: item.imageUrl,
    }));

    const discountForApi = discount
      ? {
          type: discount.type,
          value: discount.value,
        }
      : null;

    try {
      if (isDemo) {
        const demoResponse = await demo.createDemoHoldOrder(
          productsFromCart,
          discountForApi,
          selectedCustomer?.id
        );
        if (demoResponse.success) {
          const holdOrdersResponse = await demo.getDemoHoldOrders();
          set((state) => ({
            holdOrders: holdOrdersResponse.holdOrders,
            items: [],
            discount: null,
          }));
        }
      } else {
        const apiResponse = await api.createHoldOrder(
          productsFromCart,
          discountForApi,
          selectedCustomer?.id
        );
        if (apiResponse.status === ApiStatus.SUCCESS) {
          const holdOrdersResponse = await api.getHoldOrders();
          if (holdOrdersResponse.status === ApiStatus.SUCCESS) {
            set((state) => ({
              holdOrders: holdOrdersResponse.data.holdOrders,
              items: [],
              discount: null,
            }));
          }
        }
      }
    } catch (error) {
      console.error("Failed to hold order:", error);
      throw error;
    }
  },

  selectHoldOrder: async (holdOrder: HoldOrder, isDemo: boolean) => {
    try {
      if (isDemo) {
        const demoResponse = await demo.removeDemoSelectedHoldOrder(
          holdOrder.id
        );
        if (demoResponse.success) {
          set((state) => ({
            items: holdOrder.items,
            discount: holdOrder.discount,
            isHoldOrdersDialogOpen: false,
            holdOrders: state.holdOrders.filter(
              (order) => order.id !== holdOrder.id
            ),
          }));
        }
      } else {
        const apiResponse = await api.removeSelectedHoldOrder(holdOrder.id);
        if (apiResponse.status === ApiStatus.SUCCESS) {
          set((state) => ({
            items: holdOrder.items,
            discount: holdOrder.discount,
            isHoldOrdersDialogOpen: false,
            holdOrders: state.holdOrders.filter(
              (order) => order.id !== holdOrder.id
            ),
          }));
        }
      }
    } catch (error) {
      console.error("Failed to select hold order:", error);
      throw error;
    }
  },

  cancelHoldOrder: (id) =>
    set((state) => ({
      holdOrders: state.holdOrders.filter((order) => order.id !== id),
    })),

  // Customer actions
  setSelectedCustomer: (customer) =>
    set({
      selectedCustomer: customer,
    }),

  // Search actions
  setSearchQuery: (query) =>
    set({
      searchQuery: query,
    }),

  setShowSearch: (show) =>
    set({
      showSearch: show,
    }),

  // Promotion and Gift Voucher actions
  applyPromotionCode: async (code: string, isDemo: boolean) => {
    const { items, subtotal } = get();
    const categories = Array.from(
      new Set(items.map((item) => item.category).filter(Boolean) as string[])
    );

    try {
      if (isDemo) {
        const demoResponse = await demo.validateDemoPromotion(
          code,
          subtotal(),
          categories
        );
        if (!demoResponse.success) {
          toast.error(demoResponse.error?.message || "Failed to apply promotion code");
          return false;
        }

        if (demoResponse.promotion) {
          set({ appliedPromotion: demoResponse.promotion });
          toast.success(`Promotion code applied: ${
            demoResponse.promotion.type === "PERCENTAGE"
              ? `${demoResponse.promotion.value}% off`
              : `Rs ${demoResponse.promotion.value.toLocaleString()} off`
          }`);
          return true;
        }
      } else {
        const apiResponse = await api.validatePromotion(
          code,
          subtotal(),
          categories
        );
        if (apiResponse.status !== ApiStatus.SUCCESS) {
          toast.error(apiResponse.message || "Failed to apply promotion code");
          return false;
        }

        if (apiResponse.data.promotion) {
          set({ appliedPromotion: apiResponse.data.promotion });
          toast.success(`Promotion code applied: ${
            apiResponse.data.promotion.type === "PERCENTAGE"
              ? `${apiResponse.data.promotion.value}% off`
              : `Rs ${apiResponse.data.promotion.value.toLocaleString()} off`
          }`);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Failed to apply promotion code:", error);
      toast.error("Failed to apply promotion code");
      return false;
    }
  },

  applyGiftVoucher: async (code: string, isDemo: boolean) => {
    const { finalTotal } = get();
    const total = finalTotal();

    try {
      if (isDemo) {
        const demoResponse = await demo.validateDemoGiftVoucher(code, total);
        if (!demoResponse.success) {
          toast.error(demoResponse.error?.message || "Failed to apply gift voucher");
          return false;
        }

        if (demoResponse.voucher) {
          set({ appliedGiftVoucher: demoResponse.voucher });
          const appliedAmount = Math.min(
            demoResponse.voucher.remainingValue,
            total
          );
          const isPartial = appliedAmount < total;

          toast.success(isPartial
            ? `Gift voucher applied: Rs ${appliedAmount.toLocaleString()} from total Rs ${total.toLocaleString()}`
            : `Gift voucher applied: Full amount Rs ${appliedAmount.toLocaleString()}`);
          return true;
        }
      } else {
        const apiResponse = await api.validateGiftVoucher(code, total);
        if (apiResponse.status !== ApiStatus.SUCCESS) {
          toast.error(apiResponse.message || "Failed to apply gift voucher");
          return false;
        }

        if (apiResponse.data.voucher) {
          set({ appliedGiftVoucher: apiResponse.data.voucher });
          const appliedAmount = Math.min(
            apiResponse.data.voucher.remainingValue,
            total
          );
          const isPartial = appliedAmount < total;

          toast.success(isPartial
            ? `Gift voucher applied: Rs ${appliedAmount.toLocaleString()} from total Rs ${total.toLocaleString()}`
            : `Gift voucher applied: Full amount Rs ${appliedAmount.toLocaleString()}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error("Failed to apply gift voucher:", error);
      toast.error("Failed to apply gift voucher");
      return false;
    }
  },

  clearPromotionCode: () => {
    set({ appliedPromotion: null });
    toast.success("Promotion code removed");
  },

  clearGiftVoucher: () => {
    set({ appliedGiftVoucher: null });
    toast.success("Gift voucher removed");
  },

  // UI actions
  setShowImages: (show) => set({ showImages: show }),

  // Computed values
  subtotal: () => {
    const { items } = get();
    return items.reduce((total, item) => {
      const itemTotal = item.price * item.quantity;
      const itemDiscountAmount = item.itemDiscount
        ? item.itemDiscount.type === "percentage"
          ? (itemTotal * item.itemDiscount.value) / 100
          : item.itemDiscount.value
        : 0;
      return total + itemTotal - itemDiscountAmount;
    }, 0);
  },

  discountAmount: () => {
    const { items, discount } = get();
    if (!discount) return 0;

    const subtotal = items.reduce(
      (acc, item) => acc + item.price * item.quantity,
      0
    );

    return discount.type === "percentage"
      ? (subtotal * discount.value) / 100
      : discount.value;
  },

  total: () => {
    const { items, discount, appliedPromotion, appliedGiftVoucher } = get();
    let total = items.reduce(
      (acc, item) => acc + item.price * item.quantity,
      0
    );

    // Apply cart discount
    if (discount) {
      const discountAmount =
        discount.type === "percentage"
          ? (total * discount.value) / 100
          : discount.value;
      total -= discountAmount;
    }

    // Apply promotion discount
    if (appliedPromotion) {
      const promotionDiscount =
        appliedPromotion.type === "PERCENTAGE"
          ? (total * appliedPromotion.value) / 100
          : appliedPromotion.value;

      // Apply max discount limit if set
      if (appliedPromotion.maxDiscount) {
        total -= Math.min(promotionDiscount, appliedPromotion.maxDiscount);
      } else {
        total -= promotionDiscount;
      }
    }

    // Apply gift voucher
    if (appliedGiftVoucher) {
      total = Math.max(0, total - appliedGiftVoucher.remainingValue);
    }

    return total;
  },

  serviceCharge: () => {
    const total = get().total();
    return Math.round(total * 0.1); // 10% service charge
  },

  finalTotal: () => {
    const total = get().total();
    const serviceCharge = get().serviceCharge();
    return total + serviceCharge;
  },

  // New hold order state and actions
  isHoldOrdersDialogOpen: false,
  setIsHoldOrdersDialogOpen: (isOpen) =>
    set({ isHoldOrdersDialogOpen: isOpen }),
  fetchHoldOrders: async (isDemo) => {
    try {
      if (isDemo) {
        const demoResponse = await demo.getDemoHoldOrders();
        set({ holdOrders: demoResponse.holdOrders });
      } else {
        const apiResponse = await api.getHoldOrders();
        if (apiResponse.status === ApiStatus.SUCCESS) {
          set({ holdOrders: apiResponse.data.holdOrders });
        }
      }
    } catch (error) {
      console.error("Failed to fetch hold orders:", error);
      throw error;
    }
  },
  deleteHoldOrder: async (holdOrderId: string, isDemo: boolean) => {
    try {
      if (isDemo) {
        const demoResponse = await demo.deleteDemoHoldOrder(holdOrderId);
        if (demoResponse.success) {
          const holdOrdersResponse = await demo.getDemoHoldOrders();
          set({
            holdOrders: holdOrdersResponse.holdOrders,
          });
        }
      } else {
        const apiResponse = await api.deleteHoldOrder(holdOrderId);
        if (apiResponse.status === ApiStatus.SUCCESS) {
          const holdOrdersResponse = await api.getHoldOrders();
          if (holdOrdersResponse.status === ApiStatus.SUCCESS) {
            set({
              holdOrders: holdOrdersResponse.data.holdOrders,
            });
          }
        }
      }
    } catch (error) {
      console.error("Failed to delete hold order:", error);
      throw error;
    }
  },

  calculateDiscount: (price: number, discount: Discount | null) => {
    if (!discount) return 0;
    return discount.type === "percentage"
      ? (price * discount.value) / 100
      : discount.value;
  },

  showBalancePayment: false,
  setShowBalancePayment: (show) => set({ showBalancePayment: show }),

  updateCartItemDiscount: (itemId, itemDiscount) => {
    set((state) => ({
      items: state.items.map((item) =>
        item.id === itemId
          ? { ...item, itemDiscount: itemDiscount || undefined }
          : item
      ),
    }));
  },

  // Favorite products actions
  fetchFavoriteProducts: async (isDemo: boolean) => {
    try {
      let productIds: string[] = [];
      if (isDemo) {
        const demoResponse = await demo.getDemoFavoriteProducts();
        productIds = demoResponse.productIds;
      } else {
        const apiResponse = await api.getFavoriteProducts();
        if (apiResponse.status === ApiStatus.SUCCESS) {
          productIds = apiResponse.data.productIds;
        }
      }

      const favoriteProducts: FavoriteProduct[] = [];
      for (const id of productIds) {
        if (isDemo) {
          const demoResponse = await demo.getDemoProductDetails(id);
          favoriteProducts.push({
            id: demoResponse.product.id,
            code: demoResponse.product.code,
            name: demoResponse.product.name,
            price: demoResponse.product.price,
            category: demoResponse.product.category,
            subCategory: demoResponse.product.subCategory,
            imageUrl: demoResponse.product.imageUrl,
          });
        } else {
          const apiResponse = await api.getProductDetails(id);
          if (apiResponse.status === ApiStatus.SUCCESS) {
            favoriteProducts.push({
              id: apiResponse.data.product.id,
              code: apiResponse.data.product.code,
              name: apiResponse.data.product.name,
              price: apiResponse.data.product.price,
              category: apiResponse.data.product.category,
              subCategory: apiResponse.data.product.subCategory,
              imageUrl: apiResponse.data.product.imageUrl,
            });
          }
        }
      }

      set({ favoriteProducts });
    } catch (error) {
      console.error("Failed to fetch favorite products:", error);
      toast.error("Failed to fetch favorite products");
    }
  },

  addToFavorites: async (product: POSProduct, isDemo: boolean) => {
    try {
      const success = isDemo
        ? await demo.addDemoFavoriteProduct(product.id)
        : await api.addFavoriteProduct(product.id);

      if (success) {
        const favoriteProduct: FavoriteProduct = {
          id: product.id,
          code: product.code,
          name: product.name,
          price: product.price,
          category: product.category,
          subCategory: product.subCategory,
          imageUrl: product.imageUrl,
        };

        set((state) => ({
          favoriteProducts: [...state.favoriteProducts, favoriteProduct],
        }));

        toast.success("Product added to favorites");
      }
    } catch (error) {
      console.error("Failed to add product to favorites:", error);
      toast.error("Failed to add product to favorites");
    }
  },

  removeFromFavorites: async (productId: string, isDemo: boolean) => {
    try {
      const success = isDemo
        ? await demo.removeDemoFavoriteProduct(productId)
        : await api.removeFavoriteProduct(productId);

      if (success) {
        set((state) => ({
          favoriteProducts: state.favoriteProducts.filter(
            (product) => product.id !== productId
          ),
        }));

        toast.success("Product removed from favorites");
      }
    } catch (error) {
      console.error("Failed to remove product from favorites:", error);
      toast.error("Failed to remove product from favorites");
    }
  },

  promotionCode: null,
  giftVoucherCode: null,
  setPromotionCode: (code) => set({ promotionCode: code }),
  setGiftVoucherCode: (code) => set({ giftVoucherCode: code }),

  toggleButtonVisibility: (buttonId, visible) =>
    set((state) => ({
      buttonVisibility: {
        ...state.buttonVisibility,
        [buttonId]: visible,
      },
    })),

  toggleToolbarButtonVisibility: (buttonId, visible) =>
    set((state) => ({
      toolbarButtonVisibility: {
        ...state.toolbarButtonVisibility,
        [buttonId]: visible,
      },
    })),

  resetVisibilitySettings: () =>
    set({
      buttonVisibility: DEFAULT_BUTTON_VISIBILITY,
      toolbarButtonVisibility: DEFAULT_TOOLBAR_VISIBILITY,
    }),

  saveSettings: async (isDemo: boolean) => {
    try {
      const { buttonVisibility, toolbarButtonVisibility } = get();
      const settings = {
        buttonVisibility,
        toolbarButtonVisibility,
      };

      if (isDemo) {
        const demoResponse = await demo.saveDemoSettings(settings);
        if (!demoResponse.success) {
          throw new Error(
            demoResponse.error?.message || "Failed to save settings"
          );
        }
      } else {
        const apiResponse = await api.saveSettings(settings);
        if (apiResponse.status !== ApiStatus.SUCCESS) {
          throw new Error(apiResponse.message || "Failed to save settings");
        }
      }

      toast.success("Settings saved successfully");
    } catch (error) {
      console.error("Failed to save settings:", error);
      toast.error("Failed to save settings");
    }
  },

  loadSettings: async (isDemo: boolean) => {
    try {
      if (isDemo) {
        const demoResponse = await demo.getDemoSettings();
        if (!demoResponse.success || !demoResponse.settings) {
          throw new Error(
            demoResponse.error?.message || "Failed to load settings"
          );
        }
        set({
          buttonVisibility: demoResponse.settings.buttonVisibility,
          toolbarButtonVisibility:
            demoResponse.settings.toolbarButtonVisibility,
        });
      } else {
        const apiResponse = await api.getSettings();
        if (
          apiResponse.status !== ApiStatus.SUCCESS ||
          !apiResponse.data.settings
        ) {
          throw new Error(apiResponse.message || "Failed to load settings");
        }
        set({
          buttonVisibility: apiResponse.data.settings.buttonVisibility,
          toolbarButtonVisibility:
            apiResponse.data.settings.toolbarButtonVisibility,
        });
      }
    } catch (error) {
      console.error("Failed to load settings:", error);
      // On error, reset to defaults
      set({
        buttonVisibility: DEFAULT_BUTTON_VISIBILITY,
        toolbarButtonVisibility: DEFAULT_TOOLBAR_VISIBILITY,
      });
      toast.error("Failed to load settings, using defaults");
    }
  },

  setButtonVisibility: (settings) => set({ buttonVisibility: settings }),
  setToolbarButtonVisibility: (settings) =>
    set({ toolbarButtonVisibility: settings }),
  resetButtonVisibility: () =>
    set({ buttonVisibility: DEFAULT_BUTTON_VISIBILITY }),
  resetToolbarButtonVisibility: () =>
    set({
      toolbarButtonVisibility: DEFAULT_TOOLBAR_VISIBILITY,
    }),
  setBillInvoiceSettings: (settings) => set({ billInvoiceSettings: settings }),
  resetBillInvoiceSettings: () =>
    set({ billInvoiceSettings: DEFAULT_BILL_INVOICE_SETTINGS }),

  // Cash register actions
  openRegister: (amount: number, note: string) => {
    set({
      isRegisterOpen: true,
      openingCash: amount,
      openingNote: note,
      registerOpenedAt: new Date(),
    });

    // Save to localStorage for persistence
    try {
      localStorage.setItem(
        "pos_register_status",
        JSON.stringify({
          isOpen: true,
          openingCash: amount,
          openingNote: note,
          openedAt: new Date().toISOString(),
        })
      );
    } catch (error) {
      console.error("Failed to save register status to localStorage:", error);
    }
  },

  closeRegister: () => {
    set({
      isRegisterOpen: false,
      openingCash: 0,
      openingNote: "",
      registerOpenedAt: null,
    });

    // Clear from localStorage
    try {
      localStorage.removeItem("pos_register_status");
    } catch (error) {
      console.error(
        "Failed to clear register status from localStorage:",
        error
      );
    }
  },
}));
