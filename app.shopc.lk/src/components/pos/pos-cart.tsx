"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { X, ArrowLeft, Plus, User, Tag, Gift } from "lucide-react";
import { useState, useEffect, useRef, useCallback } from "react";
import { DiscountDialog } from "./discount-dialog";
import { ModifyCartItemDialog } from "./modify-cart-item-dialog";
import { usePOSStore } from "./pos-store";
import { CustomerDialog } from "./customer-dialog";
import { AddCustomerDialog } from "./add-customer-dialog";
import { CardSelectionDialog } from "./card-selection-dialog";
import {
  ChequeDetailsDialog,
  type ChequeDetails,
} from "./cheque-details-dialog";
import { MultiPaymentDialog, type PaymentDetail } from "./multi-payment-dialog";
import { toast } from "sonner";
import { OrderCreatedDialog } from "./order-created-dialog";
import { BalancePaymentDialog } from "./balance-payment-dialog";
import { PromotionDialog } from "./promotion-dialog";
import { GiftVoucherDialog } from "./gift-voucher-dialog";
import { POSCategory, POSProduct, ProductVariant } from "@/types/pos";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

type PaymentMethod = "CASH" | "CHEQUE" | "CARD" | "CREDIT" | "MULTI";

interface ExtendedCustomer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  creditLimit: number;
  usedCredit: number;
}

interface CartItem extends POSProduct {
  quantity: number;
  notes?: string;
  selectedVariant?: ProductVariant;
  itemDiscount?: {
    type: "percentage" | "amount";
    value: number;
    reason?: string;
  };
  discount?: {
    type: "percentage" | "amount";
    value: number;
    reason?: string;
  };
}

interface POSCartProps {
  isDemo: boolean;
  showPayment: boolean;
  onPayClick: () => void;
  categories: POSCategory[];
}

export function POSCart({
  isDemo,
  showPayment,
  onPayClick,
  categories,
}: POSCartProps) {
  const items = usePOSStore((state) => state.items);
  const discount = usePOSStore((state) => state.discount);
  const removeItem = usePOSStore((state) => state.removeItem);
  const clearCart = usePOSStore((state) => state.clearCart);
  const applyDiscount = usePOSStore((state) => state.applyDiscount);
  const holdCurrentOrder = usePOSStore((state) => state.holdCurrentOrder);
  const createOrder = usePOSStore((state) => state.createOrder);
  const subtotal = usePOSStore((state) => state.subtotal());
  const discountAmount = usePOSStore((state) => state.discountAmount());
  const total = usePOSStore((state) => state.total());
  const serviceCharge = usePOSStore((state) => state.serviceCharge());
  const finalTotal = usePOSStore((state) => state.finalTotal());
  const selectedCustomer = usePOSStore(
    (state) => state.selectedCustomer
  ) as ExtendedCustomer | null;
  const setSelectedCustomer = usePOSStore((state) => state.setSelectedCustomer);
  const appliedPromotion = usePOSStore((state) => state.appliedPromotion);
  const appliedGiftVoucher = usePOSStore((state) => state.appliedGiftVoucher);
  const [showPromotionDialog, setShowPromotionDialog] = useState(false);
  const [showGiftVoucherDialog, setShowGiftVoucherDialog] = useState(false);
  const [customerNote, setCustomerNote] = useState("");
  const [showNoteDialog, setShowNoteDialog] = useState(false);

  const [isDiscountDialogOpen, setIsDiscountDialogOpen] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [showCustomerDialog, setShowCustomerDialog] = useState(false);
  const [showAddCustomerDialog, setShowAddCustomerDialog] = useState(false);
  const [showCardSelection, setShowCardSelection] = useState(false);
  const [showChequeDetails, setShowChequeDetails] = useState(false);
  const [showMultiPayment, setShowMultiPayment] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<PaymentMethod>("CASH");
  const [selectedCardType, setSelectedCardType] = useState<string | null>(null);
  const [chequeDetails, setChequeDetails] = useState<ChequeDetails[]>([]);
  const [multiPayments, setMultiPayments] = useState<PaymentDetail[]>([]);
  const [showOrderCreated, setShowOrderCreated] = useState(false);
  const [showBalancePayment, setShowBalancePayment] = useState(false);
  const [showActionsDialog, setShowActionsDialog] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [activeItemId, setActiveItemId] = useState<string | null>(null);
  const prevItemsRef = useRef<typeof items>([]);

  // Function to scroll to specific item
  const scrollToItem = useCallback((itemId: string) => {
    if (scrollContainerRef.current) {
      const scrollContainer = scrollContainerRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      const itemElement = scrollContainerRef.current.querySelector(
        `[data-item-id="${itemId}"]`
      );

      if (scrollContainer && itemElement) {
        const scrollRect = scrollContainer.getBoundingClientRect();
        const itemRect = itemElement.getBoundingClientRect();
        const relativeTop =
          itemRect.top - scrollRect.top + scrollContainer.scrollTop;

        scrollContainer.scrollTo({
          top: relativeTop - 60,
          behavior: "smooth",
        });
      }
    }
  }, []);

  // Watch for changes in items
  useEffect(() => {
    if (items.length === 0) {
      prevItemsRef.current = [];
      setActiveItemId(null);
      return;
    }

    const prevItems = prevItemsRef.current;

    // Find changed item by comparing with previous items
    let changedItemId: string | null = null;

    if (items.length > prevItems.length) {
      // New item added
      changedItemId = items[items.length - 1].id;
    } else {
      // Check for quantity updates
      for (let i = 0; i < items.length; i++) {
        const currentItem = items[i];
        const prevItem = prevItems.find((item) => item.id === currentItem.id);

        if (prevItem && prevItem.quantity !== currentItem.quantity) {
          changedItemId = currentItem.id;
          break;
        }
      }
    }

    if (changedItemId) {
      setActiveItemId(changedItemId);
      scrollToItem(changedItemId);

      // Clear active item after a short delay
      setTimeout(() => {
        setActiveItemId(null);
      }, 1000);
    }

    prevItemsRef.current = items;
  }, [items, scrollToItem]);

  const handlePrint = () => {
    // Create a hidden iframe for printing
    const iframe = document.createElement("iframe");
    iframe.style.display = "none";
    document.body.appendChild(iframe);

    // Generate the receipt HTML
    const receiptHTML = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>POS Receipt</title>
          <style>
            body {
              font-family: 'Courier New', monospace;
              padding: 20px;
              max-width: 300px;
              margin: 0 auto;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
            }
            .customer-info {
              margin-bottom: 20px;
              padding: 10px 0;
              border-bottom: 1px dashed #000;
            }
            .item {
              display: flex;
              justify-content: space-between;
              margin-bottom: 8px;
            }
            .item-discount {
              padding-left: 20px;
              color: #16a34a;
              font-size: 12px;
            }
            .total {
              border-top: 1px dashed #000;
              margin-top: 10px;
              padding-top: 10px;
            }
            .footer {
              text-align: center;
              margin-top: 20px;
              font-size: 12px;
            }
            @media print {
              body { margin: 0; padding: 10px; }
              @page { margin: 0; size: 80mm auto; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>ShopC POS</h2>
            <p>${new Date().toLocaleString()}</p>
          </div>

          ${
            selectedCustomer
              ? `
            <div class="customer-info">
              <div>Customer: ${selectedCustomer.name}</div>
              ${
                selectedCustomer.phone
                  ? `<div>Phone: ${selectedCustomer.phone}</div>`
                  : ""
              }
              ${
                selectedCustomer.email
                  ? `<div>Email: ${selectedCustomer.email}</div>`
                  : ""
              }
            </div>
          `
              : ""
          }
          
          <div class="items">
            ${items
              .map(
                (item) => `
              <div class="item">
                <span>${item.name} x${item.quantity}</span>
                <span>Rs ${(item.price * item.quantity).toLocaleString()}</span>
              </div>
              ${
                item.itemDiscount
                  ? `
                <div class="item-discount">
                  Discount: ${
                    item.itemDiscount.type === "percentage"
                      ? `${item.itemDiscount.value}%`
                      : `Rs ${item.itemDiscount.value}`
                  }
                </div>
              `
                  : ""
              }
            `
              )
              .join("")}
          </div>

          <div class="total">
            <div class="item">
              <strong>Subtotal:</strong>
              <span>Rs ${subtotal.toLocaleString()}</span>
            </div>
            ${
              discount
                ? `
              <div class="item">
                <span>Discount ${
                  discount.type === "percentage" ? `(${discount.value}%)` : ""
                }:</span>
                <span>-Rs ${discountAmount.toLocaleString()}</span>
              </div>
            `
                : ""
            }
            ${
              appliedPromotion
                ? `
              <div class="item text-green-600">
                <span>Promotion (${appliedPromotion.code}) ${
                    appliedPromotion.type === "PERCENTAGE"
                      ? `(${appliedPromotion.value}%)`
                      : ""
                  }</span>
                <span>-Rs ${(appliedPromotion.type === "PERCENTAGE"
                  ? (subtotal * appliedPromotion.value) / 100
                  : appliedPromotion.value
                ).toLocaleString()}</span>
              </div>
            `
                : ""
            }
            <div class="item">
              <span>Service Charge (18%):</span>
              <span>Rs ${serviceCharge.toLocaleString()}</span>
            </div>
            ${
              appliedGiftVoucher
                ? `
              <div class="item text-green-600">
                <span>Gift Voucher (${appliedGiftVoucher.code})</span>
                <span>-Rs ${appliedGiftVoucher.remainingValue.toLocaleString()}</span>
              </div>
            `
                : ""
            }
            <div class="item" style="margin-top: 10px; font-weight: bold;">
              <strong>Total:</strong>
              <span>Rs ${finalTotal.toLocaleString()}</span>
            </div>
          </div>

          <div class="footer">
            <p>Thank you for your business!</p>
            <p>www.ShopC.lk</p>
          </div>
        </body>
      </html>
    `;

    // Write the receipt to the iframe and print it
    if (iframe.contentWindow) {
      iframe.contentWindow.document.open();
      iframe.contentWindow.document.write(receiptHTML);
      iframe.contentWindow.document.close();

      // Wait for content to load then print
      iframe.onload = () => {
        try {
          iframe.contentWindow?.print();
        } catch (error) {
          console.error("Printing failed:", error);
        } finally {
          // Remove the iframe after printing
          document.body.removeChild(iframe);
        }
      };
    }
  };

  const handlePaymentMethodClick = (method: PaymentMethod) => {
    // For multi-payment
    if (method === "MULTI") {
      setSelectedPaymentMethod(method);
      setShowMultiPayment(true);
      return;
    }

    // For cheque payments
    if (method === "CHEQUE") {
      if (!selectedCustomer) {
        toast.error("Please select a customer for cheque payment");
        setShowCustomerDialog(true);
        return;
      }
      setSelectedPaymentMethod(method);
      setShowChequeDetails(true);
      return;
    }

    // For credit payments
    if (method === "CREDIT") {
      if (!selectedCustomer) {
        toast.error("Please select a customer for credit payment");
        setShowCustomerDialog(true);
        return;
      }

      const availableCredit =
        selectedCustomer.creditLimit - selectedCustomer.usedCredit;
      if (availableCredit <= 0) {
        toast.error("Customer has no available credit");
        return;
      }
    }

    setSelectedPaymentMethod(method);
    if (method === "CARD") {
      setShowCardSelection(true);
    }

    // For cash payments, create order directly
    if (method === "CASH") {
      createOrder(finalTotal);
      setShowOrderCreated(true);
    }
  };

  return (
    <>
      <Card className="flex h-full flex-col">
        <div className="border-b p-4">
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex-1 justify-start gap-2"
              onClick={() => setShowCustomerDialog(true)}
            >
              <User className="h-4 w-4" />
              <span className="truncate">
                {selectedCustomer ? selectedCustomer.name : "Walking Customer"}
              </span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowAddCustomerDialog(true)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <ScrollArea className="flex-1 p-4" ref={scrollContainerRef}>
          {items.map((item, index) => {
            // Find category index for color
            const categoryIndex = categories.findIndex(
              (cat) => cat.id === item.category
            );
            const categoryColor =
              categories[Math.max(0, categoryIndex) % categories.length].color;

            return (
              <div
                key={item.id}
                data-item-id={item.id}
                className={`group cursor-pointer hover:bg-accent/10 rounded-md px-2 ${
                  items.length > 5 ? "py-1" : "py-2"
                } ${item.id === activeItemId ? "bg-accent/20" : ""}`}
                onClick={() => setSelectedItemId(item.id)}
                style={{
                  borderLeft: `4px solid ${categoryColor}`,
                  backgroundColor: `${categoryColor}10`,
                }}
              >
                {items.length <= 5 ? (
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center justify-between flex-1 gap-2">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-muted-foreground min-w-[20px]">
                            {index + 1}.
                          </span>
                          <span className="font-medium truncate">
                            {item.name}
                          </span>
                        </div>
                        <span className="text-muted-foreground whitespace-nowrap">
                          Rs {(item.quantity * item.price).toLocaleString()}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 ml-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeItem(item.id);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {item.quantity} x Rs {item.price.toLocaleString()}
                    </div>
                    {item.itemDiscount && (
                      <div className="text-sm text-green-600">
                        Discount:{" "}
                        {item.itemDiscount.type === "percentage"
                          ? `${item.itemDiscount.value}%`
                          : `Rs ${item.itemDiscount.value}`}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-between gap-2">
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <span className="text-sm font-medium text-muted-foreground min-w-[20px]">
                        {index + 1}.
                      </span>
                      <span className="text-sm font-medium truncate flex-1">
                        {item.name}
                      </span>
                      <span className="text-sm text-muted-foreground whitespace-nowrap">
                        {item.quantity}x
                      </span>
                      <span className="text-sm whitespace-nowrap">
                        Rs {(item.quantity * item.price).toLocaleString()}
                      </span>
                      {item.itemDiscount && (
                        <span className="text-xs text-green-600 whitespace-nowrap">
                          (-
                          {item.itemDiscount.type === "percentage"
                            ? `${item.itemDiscount.value}%`
                            : `Rs ${item.itemDiscount.value}`}
                          )
                        </span>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeItem(item.id);
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </ScrollArea>
        <div className="border-t p-4">
          <div className="space-y-1.5">
            <div className="flex items-center justify-between">
              <span className="text-sm">Sub Total</span>
              <span>Rs {subtotal.toLocaleString()}</span>
            </div>
            {discount && (
              <div className="flex items-center justify-between text-green-600">
                <span className="text-sm">
                  Discount{" "}
                  {discount.type === "percentage" ? `(${discount.value}%)` : ""}
                  {discount.reason ? ` - ${discount.reason}` : ""}
                </span>
                <span>-Rs {discountAmount.toLocaleString()}</span>
              </div>
            )}
            {appliedPromotion && (
              <div className="flex items-center justify-between text-green-600">
                <span className="text-sm">
                  Promotion ({appliedPromotion.code}){" "}
                  {appliedPromotion.type === "PERCENTAGE"
                    ? `(${appliedPromotion.value}%)`
                    : ""}
                </span>
                <span>
                  -Rs{" "}
                  {(appliedPromotion.type === "PERCENTAGE"
                    ? (subtotal * appliedPromotion.value) / 100
                    : appliedPromotion.value
                  ).toLocaleString()}
                </span>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-sm">Service Charge (18%)</span>
              <span>Rs {serviceCharge.toLocaleString()}</span>
            </div>
            {appliedGiftVoucher && (
              <div className="flex items-center justify-between text-green-600">
                <span className="text-sm">
                  Gift Voucher ({appliedGiftVoucher.code})
                </span>
                <span>
                  -Rs {appliedGiftVoucher.remainingValue.toLocaleString()}
                </span>
              </div>
            )}
            <Separator />
            <div className="flex items-center justify-between font-medium">
              <span>Total</span>
              <span>Rs {finalTotal.toLocaleString()}</span>
            </div>

            {/* Customer, Note, and Action Buttons */}
            <div className="mt-4 flex items-center justify-between gap-2">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-gray-100"
                  onClick={() => setShowCustomerDialog(true)}
                >
                  Customer
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="bg-gray-100"
                  onClick={() => {
                    setShowNoteDialog(true);
                  }}
                >
                  Note
                </Button>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setShowActionsDialog(true)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="12" cy="5" r="1" />
                  <circle cx="12" cy="19" r="1" />
                </svg>
              </Button>
            </div>

            {/* Payment Button */}
            <Button
              className="w-full mt-4 bg-[#5D4037] hover:bg-[#4E342E] text-white"
              size="lg"
              onClick={onPayClick}
            >
              Payment
            </Button>
          </div>
        </div>
      </Card>
      <DiscountDialog
        open={isDiscountDialogOpen}
        onOpenChange={setIsDiscountDialogOpen}
        onApplyDiscount={(amount, isPercentage, reason) =>
          applyDiscount({
            type: isPercentage ? "percentage" : "amount",
            value: amount,
            reason,
          })
        }
      />
      {selectedItemId && (
        <ModifyCartItemDialog
          open={!!selectedItemId}
          onOpenChange={(open) => !open && setSelectedItemId(null)}
          itemId={selectedItemId}
        />
      )}
      <CustomerDialog
        open={showCustomerDialog}
        onOpenChange={setShowCustomerDialog}
        onSelectCustomer={setSelectedCustomer}
      />
      <AddCustomerDialog
        open={showAddCustomerDialog}
        onOpenChange={setShowAddCustomerDialog}
        onAddCustomer={(customer) => {
          const newCustomer = {
            ...customer,
            id: Math.random().toString(36).substr(2, 9),
            creditLimit: 0,
            usedCredit: 0,
          };
          setSelectedCustomer(newCustomer);
        }}
      />
      <CardSelectionDialog
        open={showCardSelection}
        onOpenChange={setShowCardSelection}
        onSelectCard={(cardType) => {
          setSelectedCardType(cardType);
          createOrder(finalTotal);
          setShowOrderCreated(true);
        }}
      />
      <ChequeDetailsDialog
        open={showChequeDetails}
        onOpenChange={setShowChequeDetails}
        onSave={(details: ChequeDetails[]) => {
          setChequeDetails(details);
          createOrder(
            details.reduce(
              (sum: number, detail: ChequeDetails) => sum + detail.amount,
              0
            )
          );
          setShowOrderCreated(true);
        }}
        totalDue={finalTotal}
      />
      <MultiPaymentDialog
        open={showMultiPayment}
        onOpenChange={setShowMultiPayment}
        onSave={(payments: PaymentDetail[]) => {
          setMultiPayments(payments);
          createOrder(
            payments.reduce(
              (sum: number, payment: PaymentDetail) => sum + payment.amount,
              0
            )
          );
          setShowOrderCreated(true);
        }}
        totalDue={finalTotal}
        selectedCustomer={selectedCustomer}
      />
      <OrderCreatedDialog
        open={showOrderCreated}
        onOpenChange={(open) => {
          setShowOrderCreated(open);
          if (!open) clearCart();
        }}
      />
      <BalancePaymentDialog
        open={showBalancePayment}
        onOpenChange={setShowBalancePayment}
        totalDue={finalTotal}
        onAccept={() => handlePaymentMethodClick("CASH")}
      />
      <PromotionDialog
        open={showPromotionDialog}
        onOpenChange={setShowPromotionDialog}
        isDemo={isDemo}
      />
      <GiftVoucherDialog
        open={showGiftVoucherDialog}
        onOpenChange={setShowGiftVoucherDialog}
        isDemo={isDemo}
      />

      {/* Actions Dialog */}
      <Dialog open={showActionsDialog} onOpenChange={setShowActionsDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Actions</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button
              variant="outline"
              className="h-24 flex flex-col items-center justify-center gap-2 bg-gray-50"
              onClick={() => {
                setShowActionsDialog(false);
                setShowNoteDialog(true);
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 20h9" />
                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
              </svg>
              <span>Customer Note</span>
            </Button>
            <Button
              variant="outline"
              className="h-24 flex flex-col items-center justify-center gap-2 bg-gray-50"
              onClick={() => {
                setShowActionsDialog(false);
                // TODO: Implement pricelist functionality
                toast.info("Pricelist functionality will be available soon");
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="12" y1="1" x2="12" y2="23" />
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
              </svg>
              <span>Pricelist</span>
            </Button>
            <Button
              variant="outline"
              className="h-24 flex flex-col items-center justify-center gap-2 bg-gray-50"
              onClick={() => {
                setShowActionsDialog(false);
                // TODO: Implement refund functionality
                toast.info("Refund functionality will be available soon");
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                <path d="m3 9 2.45-4.9A2 2 0 0 1 7.24 3h9.52a2 2 0 0 1 1.8 1.1L21 9" />
                <path d="M12 3v6" />
              </svg>
              <span>Refund</span>
            </Button>
            <Button
              variant="outline"
              className="h-24 flex flex-col items-center justify-center gap-2 bg-gray-50"
              onClick={() => {
                setShowActionsDialog(false);
                // TODO: Implement cancel order functionality
                toast.info("Cancel order functionality will be available soon");
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M3 6h18" />
                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6" />
                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2" />
              </svg>
              <span>Cancel Order</span>
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Note Dialog */}
      <Dialog open={showNoteDialog} onOpenChange={setShowNoteDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Note</DialogTitle>
            <DialogDescription>Add a note to this order</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="note">Note</Label>
              <Textarea
                id="note"
                value={customerNote}
                onChange={(e) => setCustomerNote(e.target.value)}
                placeholder="Enter note here..."
                className="h-32"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNoteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowNoteDialog(false)}>Save Note</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
