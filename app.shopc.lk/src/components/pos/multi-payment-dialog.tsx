import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { CustomerDialog } from "./customer-dialog";
import type { Customer } from "./customer-dialog";
import { usePOSStore } from "./pos-store";
import { toast } from "sonner";

interface MultiPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  totalDue: number;
  selectedCustomer: Customer | null;
  onSave: (payments: PaymentDetail[]) => void;
}

export interface PaymentDetail {
  method: "CASH" | "CARD" | "CHEQUE" | "CREDIT";
  amount: number;
  cardType?: string;
  chequeDetails?: {
    chequeNumber: string;
    bankName: string;
    branchName: string;
    chequeDate: string;
    amount: number;
  };
}

const CARD_TYPES = [
  "VISA",
  "MASTER",
  "AMEX",
  "DISCOVER",
  "UNION PAY",
  "JCB",
  "DINERS CLUB",
] as const;

export function MultiPaymentDialog({
  open,
  onOpenChange,
  totalDue,
  selectedCustomer,
  onSave,
}: MultiPaymentDialogProps) {
  const [addedPayments, setAddedPayments] = useState<PaymentDetail[]>([]);
  const [showCustomerSelection, setShowCustomerSelection] = useState(false);
  const [currentAmount, setCurrentAmount] = useState("");
  const [selectedMethod, setSelectedMethod] = useState<
    PaymentDetail["method"] | null
  >(null);
  const [selectedCardType, setSelectedCardType] = useState<string | null>(null);

  // Cheque details state
  const [chequeNumber, setChequeNumber] = useState("");
  const [bankName, setBankName] = useState("");
  const [branchName, setBranchName] = useState("");
  const [chequeDate, setChequeDate] = useState("");

  // Reset state when dialog opens
  const handleDialogChange = (open: boolean) => {
    if (!open) {
      setAddedPayments([]);
      setCurrentAmount("");
      setSelectedMethod(null);
      setSelectedCardType(null);
      resetChequeFields();
    }
    onOpenChange(open);
  };

  const resetChequeFields = () => {
    setChequeNumber("");
    setBankName("");
    setBranchName("");
    setChequeDate("");
  };

  // Calculate totals
  const totalReceived = addedPayments.reduce(
    (sum, payment) => sum + payment.amount,
    0
  );
  const remainingAmount = totalDue - totalReceived;
  const returnAmount = totalReceived > totalDue ? totalReceived - totalDue : 0;
  const currentNumericAmount = parseFloat(currentAmount || "0");

  const handleAddPayment = () => {
    if (!selectedMethod) {
      toast.error("Please select a payment method");
      return;
    }

    if (!currentAmount || currentNumericAmount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    let finalAmount = currentNumericAmount;

    // Validate card type selection
    if (selectedMethod === "CARD" && !selectedCardType) {
      toast.error("Please select a card type");
      return;
    }

    // For credit payment, check credit limit
    if (selectedMethod === "CREDIT" && selectedCustomer) {
      const availableCredit =
        selectedCustomer.creditLimit - selectedCustomer.usedCredit;
      if (finalAmount > availableCredit) {
        toast.error(`Insufficient credit limit. Available: Rs ${availableCredit.toLocaleString()}`);
        return;
      }
    }

    // For cheque payment, validate all fields
    if (selectedMethod === "CHEQUE") {
      if (!chequeNumber || !bankName || !branchName || !chequeDate) {
        toast.error("Please fill in all cheque details");
        return;
      }
    }

    const newPayment: PaymentDetail = {
      method: selectedMethod,
      amount: finalAmount,
    };

    // Add card type if it's a card payment
    if (selectedMethod === "CARD") {
      newPayment.cardType = selectedCardType || undefined;
    }

    // Add cheque details if it's a cheque payment
    if (selectedMethod === "CHEQUE") {
      newPayment.chequeDetails = {
        chequeNumber,
        bankName,
        branchName,
        chequeDate,
        amount: finalAmount,
      };
    }

    setAddedPayments([...addedPayments, newPayment]);
    setCurrentAmount("");
    setSelectedMethod(null);
    setSelectedCardType(null);
    resetChequeFields();
  };

  const handleMethodClick = async (method: PaymentDetail["method"]) => {
    if (method === "CHEQUE" || method === "CREDIT") {
      if (!selectedCustomer) {
        toast.error(`Please select a customer for ${method.toLowerCase()} payment`);
        setShowCustomerSelection(true);
        return;
      }

      if (method === "CREDIT") {
        const availableCredit =
          selectedCustomer.creditLimit - selectedCustomer.usedCredit;
        if (availableCredit <= 0) {
          toast.error("Customer has no available credit");
          return;
        }
      }
    }

    setSelectedMethod(method);
  };

  const handleRemovePayment = (index: number) => {
    setAddedPayments(addedPayments.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    if (totalReceived < totalDue) {
      toast.error("Total amount must be equal or greater than the total due");
      return;
    }
    onSave(addedPayments);
    onOpenChange(false);
  };

  const formatPaymentLabel = (payment: PaymentDetail) => {
    let label = `${payment.method} - Rs ${payment.amount.toLocaleString()}`;
    if (payment.cardType) {
      label += ` (${payment.cardType})`;
    }
    if (payment.chequeDetails) {
      label += ` (${payment.chequeDetails.chequeNumber} - ${payment.chequeDetails.bankName})`;
    }
    return label;
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Multi Payment</DialogTitle>
        </DialogHeader>

        {/* Added Payments */}
        {addedPayments.length > 0 && (
          <div className="flex flex-wrap gap-2 pb-4">
            {addedPayments.map((payment, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="flex items-center gap-2 py-1.5"
              >
                <span>{formatPaymentLabel(payment)}</span>
                <button
                  onClick={() => handleRemovePayment(index)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        {/* Amount Summary */}
        <div className="flex justify-between items-start py-2">
          <div className="text-sm">
            <div className="font-medium">
              Total Due: Rs {totalDue.toLocaleString()}
            </div>
            <div className="text-blue-600">
              Paid: Rs {totalReceived.toLocaleString()}
            </div>
            {returnAmount > 0 && (
              <div className="text-green-600 font-medium">
                Return: Rs {returnAmount.toLocaleString()}
              </div>
            )}
          </div>
          {remainingAmount > 0 && (
            <div className="text-sm font-medium text-red-600">
              Remaining: Rs {remainingAmount.toLocaleString()}
            </div>
          )}
        </div>

        {/* Payment Methods */}
        {remainingAmount > 0 && (
          <>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={selectedMethod === "CASH" ? "default" : "outline"}
                onClick={() => handleMethodClick("CASH")}
              >
                CASH
              </Button>
              <Button
                variant={selectedMethod === "CARD" ? "default" : "outline"}
                onClick={() => handleMethodClick("CARD")}
              >
                CARD
              </Button>
              <Button
                variant={selectedMethod === "CHEQUE" ? "default" : "outline"}
                onClick={() => handleMethodClick("CHEQUE")}
              >
                CHEQUE
              </Button>
              <Button
                variant={selectedMethod === "CREDIT" ? "default" : "outline"}
                onClick={() => handleMethodClick("CREDIT")}
              >
                CREDIT
              </Button>
            </div>

            {/* Card Type Selection */}
            {selectedMethod === "CARD" && (
              <div className="space-y-2 pt-2">
                <div className="grid grid-cols-2 gap-2">
                  {CARD_TYPES.map((cardType) => (
                    <Button
                      key={cardType}
                      variant={
                        selectedCardType === cardType ? "default" : "outline"
                      }
                      onClick={() => setSelectedCardType(cardType)}
                      className="h-auto py-1.5"
                    >
                      {cardType}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Cheque Details */}
            {selectedMethod === "CHEQUE" && (
              <div className="space-y-2 pt-2">
                <Input
                  placeholder="Cheque Number"
                  value={chequeNumber}
                  onChange={(e) => setChequeNumber(e.target.value)}
                />
                <Input
                  placeholder="Bank Name"
                  value={bankName}
                  onChange={(e) => setBankName(e.target.value)}
                />
                <Input
                  placeholder="Branch Name"
                  value={branchName}
                  onChange={(e) => setBranchName(e.target.value)}
                />
                <Input
                  type="date"
                  value={chequeDate}
                  onChange={(e) => setChequeDate(e.target.value)}
                />
              </div>
            )}

            {/* Amount Input */}
            {selectedMethod && (
              <div className="space-y-2 pt-2">
                <div className="flex justify-between">
                  <label className="text-sm font-medium">Amount</label>
                </div>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    value={currentAmount}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (value === "" || parseFloat(value) >= 0) {
                        setCurrentAmount(value);
                      }
                    }}
                    placeholder="Enter amount"
                    className="text-right"
                  />
                  <Button
                    variant="outline"
                    onClick={() => {
                      setCurrentAmount(remainingAmount.toFixed(2));
                    }}
                    className="whitespace-nowrap"
                  >
                    Exact
                  </Button>
                  <Button onClick={() => handleAddPayment()}>Add</Button>
                </div>
              </div>
            )}
          </>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button variant="default" onClick={handleSave}>
            Save
          </Button>
        </div>

        {/* Sub-dialogs */}
        <CustomerDialog
          open={showCustomerSelection}
          onOpenChange={setShowCustomerSelection}
          onSelectCustomer={(customer) => {
            usePOSStore.getState().setSelectedCustomer(customer);
            setShowCustomerSelection(false);
          }}
        />
      </DialogContent>
    </Dialog>
  );
}
