"use client";

import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Search,
  Plus,
  Minus,
  ChevronLeft,
  X,
  MoreVertical,
} from "lucide-react";
import { useState, useRef, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import React from "react";
import {
  getOrderByTableNumber,
  createEmptyOrder,
  updateOrder,
  completeOrder,
} from "./order-data";
import { toast } from "sonner";

interface ProductAttribute {
  id: string;
  name: string;
  price: number;
}

interface ComboChoice {
  name: string;
  options: {
    id: string;
    name: string;
    price: number;
    image?: string;
    description?: string;
  }[];
}

interface AttributeGroup {
  name: string;
  type: "single" | "multiple";
  options: ProductAttribute[];
}

interface MenuItem {
  id: string;
  name: string;
  price: number;
  description?: string;
  category: "Food" | "Drinks";
  image?: string;
  attributes?: AttributeGroup[];
  isCombo?: boolean;
  comboChoices?: ComboChoice[];
  discount?: number;
}

interface Customer {
  id: string;
  name: string;
  email?: string;
  company?: string;
}

// Add category color mapping
const categoryColors = {
  Food: "border-green-500",
  Drinks: "border-blue-500",
} as const;

const menuItems: MenuItem[] = [
  {
    id: "1",
    name: "Bacon Burger",
    price: 17.83,
    description: "Sweet potato fries",
    category: "Food",
    attributes: [
      {
        name: "Sides",
        type: "single",
        options: [
          { id: "s1", name: "Belgian fresh homemade fries", price: 0 },
          { id: "s2", name: "Sweet potato fries", price: 0 },
          { id: "s3", name: "Smashed sweet potatoes", price: 0 },
          { id: "s4", name: "Potatoes with thyme", price: 0 },
          { id: "s5", name: "Grilled vegetables", price: 0 },
        ],
      },
    ],
  },
  {
    id: "2",
    name: "Burger Menu Combo",
    price: 0,
    category: "Food",
    isCombo: true,
    discount: 13,
    description: "Select your burger and drink with 10% discount",
    comboChoices: [
      {
        name: "Burgers Choice",
        options: [
          {
            id: "cb1",
            name: "Cheese Burger",
            price: 0,
            image: "/images/cheese-burger.jpg",
            description: "Classic cheese burger with fresh vegetables",
          },
          {
            id: "cb2",
            name: "Bacon Burger",
            price: 0,
            image: "/images/bacon-burger.jpg",
            description: "Burger with crispy bacon",
          },
        ],
      },
      {
        name: "Drinks choice",
        options: [
          {
            id: "cd1",
            name: "Coca-Cola",
            price: 0,
            image: "/images/coca-cola.jpg",
          },
          {
            id: "cd2",
            name: "Water",
            price: 0,
            image: "/images/water.jpg",
          },
          {
            id: "cd3",
            name: "Minute Maid",
            price: 0,
            image: "/images/minute-maid.jpg",
          },
          {
            id: "cd4",
            name: "Milkshake Banana",
            price: 0,
            image: "/images/milkshake.jpg",
          },
        ],
      },
    ],
  },
  {
    id: "3",
    name: "Cheese Burger",
    price: 14.95,
    category: "Food",
  },
  {
    id: "4",
    name: "Chicken Curry Sandwich",
    price: 13.5,
    category: "Food",
  },
  {
    id: "5",
    name: "Club Sandwich",
    price: 15.0,
    category: "Food",
  },
  {
    id: "6",
    name: "Funghi",
    price: 16.5,
    category: "Food",
  },
  {
    id: "7",
    name: "Lunch Maki 18pc",
    price: 12.0,
    category: "Food",
  },
  {
    id: "8",
    name: "Lunch Salmon 20pc",
    price: 13.8,
    category: "Food",
  },
  {
    id: "9",
    name: "Lunch Temaki mix 3pc",
    price: 14.0,
    category: "Food",
  },
  {
    id: "10",
    name: "Mozzarella Sandwich",
    price: 12.5,
    category: "Food",
  },
  {
    id: "11",
    name: "Pasta 4 Formaggi",
    price: 18.0,
    category: "Food",
  },
  {
    id: "12",
    name: "Pasta Bolognese",
    price: 16.5,
    category: "Food",
  },
  {
    id: "13",
    name: "Pizza Margherita",
    price: 13.23,
    category: "Food",
    description: "Classic Italian pizza with tomato sauce and mozzarella",
    attributes: [
      {
        name: "Extras",
        type: "multiple",
        options: [
          { id: "p1", name: "Pepperoni", price: 3.0 },
          { id: "p2", name: "Mushroom", price: 2.0 },
          { id: "p3", name: "Black olives", price: 1.5 },
          { id: "p4", name: "Anchovy", price: 1.5 },
          { id: "p5", name: "Extra cheese", price: 1.5 },
        ],
      },
    ],
  },
  {
    id: "14",
    name: "Pizza Vegetarian",
    price: 16.5,
    category: "Food",
  },
  {
    id: "15",
    name: "Salmon and Avocado",
    price: 18.5,
    category: "Food",
  },
  {
    id: "16",
    name: "Spicy Tuna Sandwich",
    price: 3.45,
    category: "Food",
  },
  {
    id: "17",
    name: "Coca-Cola",
    price: 2.53,
    category: "Drinks",
  },
  {
    id: "18",
    name: "Espresso",
    price: 5.41,
    category: "Drinks",
  },
  {
    id: "19",
    name: "Schweppes",
    price: 2.53,
    category: "Drinks",
  },
];

interface CartItem extends MenuItem {
  quantity: number;
  selectedAttributes?: { id: string; name: string; price: number }[];
  comboSelections?: { [key: string]: { id: string; name: string } };
  discount?: number;
}

interface AttributeSelectionState {
  item: MenuItem | null;
  selectedAttributes: ProductAttribute[];
  comboSelections?: { [key: string]: { id: string; name: string } };
}

interface RegisterViewProps {
  selectedTable: string | null;
  onTableChange: (table: string | null) => void;
}

// Add interface for payment entry
interface PaymentEntry {
  method: "Cash" | "Card" | "Customer Account";
  amount: number;
}

// Add a new PaymentView component
interface PaymentViewProps {
  total: number;
  onCancel: () => void;
  onComplete: (payments: PaymentEntry[]) => void;
}

function PaymentView({ total, onCancel, onComplete }: PaymentViewProps) {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<
    "Cash" | "Card" | "Customer Account" | null
  >(null);
  const [paymentAmount, setPaymentAmount] = useState<string>("");
  const [payments, setPayments] = useState<PaymentEntry[]>([]);
  const [showChange, setShowChange] = useState(false);

  // Calculate total paid amount
  const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
  // Calculate change amount (if paid more than total)
  const changeAmount = Math.max(0, totalPaid - total);
  // Calculate remaining amount to be paid
  const remainingAmount = Math.max(0, total - totalPaid);

  // Handle payment amount input
  const handlePaymentAmountInput = (value: string) => {
    if (value === "." && paymentAmount.includes(".")) {
      return;
    }

    if (value === "⌫") {
      setPaymentAmount((prev) => prev.slice(0, -1));
      return;
    }

    if (value === "+/-") {
      setPaymentAmount((prev) =>
        prev.startsWith("-") ? prev.substring(1) : "-" + prev
      );
      return;
    }

    // Only allow numbers and one decimal point
    if (/^\d$/.test(value) || (value === "." && !paymentAmount.includes("."))) {
      setPaymentAmount((prev) => prev + value);
    }
  };

  // Handle quick amount additions
  const handleQuickAmount = (amount: number) => {
    const currentAmount = parseFloat(paymentAmount || "0");
    setPaymentAmount((currentAmount + amount).toString());
  };

  // Add a payment method
  const addPayment = () => {
    if (!selectedPaymentMethod) {
      toast.error("Please select a payment method");
      return;
    }

    if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
      toast.error("Please enter a valid payment amount");
      return;
    }

    const amount = parseFloat(paymentAmount);

    setPayments([...payments, { method: selectedPaymentMethod, amount }]);
    setPaymentAmount("");
    setSelectedPaymentMethod(null);

    // Check if payment is complete
    if (totalPaid + amount >= total) {
      setShowChange(true);
      toast.success(`Payment complete. Change: ${(totalPaid + amount - total).toFixed(2)} Rs`);
    }
  };

  // Remove a payment
  const removePayment = (index: number) => {
    setPayments(payments.filter((_, i) => i !== index));
    setShowChange(false);
  };

  // Process payment
  const processPayment = () => {
    if (payments.length === 0) {
      toast.error("Please add at least one payment method");
      return;
    }

    if (totalPaid < total) {
      toast.error(`Insufficient payment. Remaining: ${(total - totalPaid).toFixed(2)} Rs`);
      return;
    }

    onComplete(payments);
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex h-full">
        {/* Left side - Payment methods */}
        <div className="w-1/3 border-r p-6 flex flex-col space-y-6">
          <button
            className={`flex items-center space-x-3 p-4 rounded-md transition-colors ${
              selectedPaymentMethod === "Cash"
                ? "bg-green-50 border border-green-200"
                : "hover:bg-gray-50 border border-gray-200"
            }`}
            onClick={() => setSelectedPaymentMethod("Cash")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect width="20" height="12" x="2" y="6" rx="2" />
              <circle cx="12" cy="12" r="2" />
              <path d="M6 12h.01M18 12h.01" />
            </svg>
            <span className="font-medium">Cash</span>
          </button>

          <button
            className={`flex items-center space-x-3 p-4 rounded-md transition-colors ${
              selectedPaymentMethod === "Card"
                ? "bg-green-50 border border-green-200"
                : "hover:bg-gray-50 border border-gray-200"
            }`}
            onClick={() => setSelectedPaymentMethod("Card")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <line x1="2" x2="22" y1="10" y2="10" />
            </svg>
            <span className="font-medium">Card</span>
          </button>

          <button
            className={`flex items-center space-x-3 p-4 rounded-md transition-colors ${
              selectedPaymentMethod === "Customer Account"
                ? "bg-green-50 border border-green-200"
                : "hover:bg-gray-50 border border-gray-200"
            }`}
            onClick={() => setSelectedPaymentMethod("Customer Account")}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M18 21a8 8 0 0 0-16 0" />
              <circle cx="10" cy="8" r="5" />
              <path d="M22 20c0-3.37-2-6.5-4-8a5 5 0 0 0-.45-8.3" />
            </svg>
            <span className="font-medium">Customer Account</span>
          </button>

          <div>
            {/* Keypad */}
            <div className="flex-1 p-4">
              <div className="grid grid-cols-4 gap-2 h-full">
                {/* Customer and Invoice buttons */}
                <button className="col-span-2 bg-gray-100 rounded-md flex items-center justify-center gap-2 p-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 21a8 8 0 0 0-16 0" />
                    <circle cx="10" cy="8" r="5" />
                  </svg>
                  <span>Customer</span>
                </button>
                <button className="col-span-2 bg-gray-100 rounded-md flex items-center justify-center gap-2 p-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <path d="M14 2v6h6" />
                    <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" />
                    <line x1="10" y1="9" x2="8" y2="9" />
                  </svg>
                  <span>Invoice</span>
                </button>

                {/* Number buttons */}
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("1")}
                >
                  1
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("2")}
                >
                  2
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("3")}
                >
                  3
                </button>
                <button
                  className="bg-green-100 border rounded-md p-4 text-center text-xl"
                  onClick={() => handleQuickAmount(10)}
                >
                  +10
                </button>

                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("4")}
                >
                  4
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("5")}
                >
                  5
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("6")}
                >
                  6
                </button>
                <button
                  className="bg-green-100 border rounded-md p-4 text-center text-xl"
                  onClick={() => handleQuickAmount(20)}
                >
                  +20
                </button>

                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("7")}
                >
                  7
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("8")}
                >
                  8
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("9")}
                >
                  9
                </button>
                <button
                  className="bg-green-100 border rounded-md p-4 text-center text-xl"
                  onClick={() => handleQuickAmount(50)}
                >
                  +50
                </button>

                <button
                  className="bg-yellow-100 border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("+/-")}
                >
                  +/-
                </button>
                <button
                  className="bg-white border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("0")}
                >
                  0
                </button>
                <button
                  className="bg-orange-100 border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput(".")}
                >
                  .
                </button>
                <button
                  className="bg-red-100 border rounded-md p-4 text-center text-xl"
                  onClick={() => handlePaymentAmountInput("⌫")}
                >
                  ⌫
                </button>

                {/* Action buttons */}
                <button
                  className="col-span-2 bg-gray-200 rounded-md p-4 text-center text-xl"
                  onClick={onCancel}
                >
                  Back
                </button>
                <button
                  className="col-span-2 bg-blue-500 text-white rounded-md p-4 text-center text-xl"
                  onClick={addPayment}
                  disabled={!selectedPaymentMethod || !paymentAmount}
                >
                  Add Payment
                </button>

                {/* Validate button (only shown when payment is complete) */}
                {totalPaid >= total && (
                  <button
                    className="col-span-4 bg-purple-500 text-white rounded-md p-4 text-center text-xl mt-2"
                    onClick={processPayment}
                  >
                    Validate
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Amount and keypad */}
        <div className="w-2/3 flex flex-col">
          {/* Amount display and payment list */}
          <div className="p-6 bg-gray-50 flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <div className="text-gray-600">
                {remainingAmount > 0
                  ? `Remaining: ${remainingAmount.toFixed(2)} Rs`
                  : "Payment complete"}
              </div>
              <div className="text-4xl font-bold text-green-600">
                {total.toFixed(2)} Rs
              </div>
            </div>

            {/* Current payment input display */}
            {selectedPaymentMethod && (
              <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-200 flex justify-between items-center">
                <span>{selectedPaymentMethod}</span>
                <span className="font-bold">
                  {paymentAmount ? `${paymentAmount} Rs` : "0.00 Rs"}
                </span>
              </div>
            )}

            {/* Payment list */}
            <div className="space-y-2 max-h-[150px] overflow-y-auto">
              {payments.map((payment, index) => (
                <div
                  key={index}
                  className="flex justify-between items-center p-3 bg-white rounded-md border"
                >
                  <span>{payment.method}</span>
                  <div className="flex items-center">
                    <span className="mr-2">{payment.amount.toFixed(2)} Rs</span>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={() => removePayment(index)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Change amount */}
            {showChange && changeAmount > 0 && (
              <div className="mt-4 flex justify-between items-center p-3 bg-green-50 rounded-md border border-green-200">
                <span className="font-medium text-green-700">Change</span>
                <span className="font-bold text-green-700">
                  {changeAmount.toFixed(2)} Rs
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export function RegisterView({
  selectedTable,
  onTableChange,
}: RegisterViewProps) {
  const [selectedCategory, setSelectedCategory] = useState<"Food" | "Drinks">(
    "Food"
  );
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCartItem, setSelectedCartItem] = useState<{
    item: CartItem;
    index: number;
  } | null>(null);
  const [attributeSelection, setAttributeSelection] =
    useState<AttributeSelectionState>({
      item: null,
      selectedAttributes: [],
      comboSelections: {},
    });
  const [currentInput, setCurrentInput] = useState<string>("");
  const [inputMode, setInputMode] = useState<
    "qty" | "price" | "percent" | "discount" | "table"
  >("qty");
  const [discountMode, setDiscountMode] = useState<"percent" | "discount">(
    "percent"
  );
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [isNoteDialogOpen, setIsNoteDialogOpen] = useState(false);
  const [orderNote, setOrderNote] = useState("");
  const lastItemRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const [showPaymentView, setShowPaymentView] = useState(false);
  const [isOrderTypeDialogOpen, setIsOrderTypeDialogOpen] = useState(false);
  const [orderType, setOrderType] = useState<"Eat In" | "Takeout" | "Delivery">(
    "Eat In"
  );

  // Load order data when selectedTable changes
  useEffect(() => {
    if (selectedTable) {
      // Try to find an existing order for this table
      const existingOrder = getOrderByTableNumber(selectedTable);

      if (existingOrder) {
        // Load existing order data
        setCartItems(existingOrder.items);
        setOrderNote(existingOrder.note || "");
        setOrderType(existingOrder.orderType);

        // If there's a customer associated with the order
        if (existingOrder.customerId && existingOrder.customerName) {
          setSelectedCustomer({
            id: existingOrder.customerId,
            name: existingOrder.customerName,
          });
        } else {
          setSelectedCustomer(null);
        }

        // Show toast notification for loading existing order
        toast.success(`Table ${selectedTable} order loaded: ${existingOrder.items.length} items with total ${existingOrder.total.toFixed(2)} Rs`);
      } else {
        // Create a new empty order for this table
        setCartItems([]);
        setOrderNote("");
        setOrderType("Eat In");
        setSelectedCustomer(null);

        // Show toast notification for new table
        toast.success(`Table ${selectedTable} selected - Ready for a new order`);
      }
    } else {
      // No table selected (direct sale)
      setCartItems([]);
      setOrderNote("");
      setOrderType("Takeout");
      setSelectedCustomer(null);

      // Show toast notification for direct sale
      toast.info("Direct Sale Mode - No table selected");
    }

    // Reset any selected cart item
    setSelectedCartItem(null);
    setCurrentInput("");
    setInputMode("qty");
  }, [selectedTable, toast]);

  // Save order when cart changes
  useEffect(() => {
    if (selectedTable && cartItems.length > 0) {
      const customer = selectedCustomer
        ? { id: selectedCustomer.id, name: selectedCustomer.name }
        : undefined;

      updateOrder(selectedTable, cartItems, orderNote, customer, orderType);
    }
  }, [cartItems, orderNote, selectedCustomer, selectedTable, orderType]);

  const filteredItems = menuItems.filter(
    (item) =>
      item.category === selectedCategory &&
      (searchQuery
        ? item.name.toLowerCase().includes(searchQuery.toLowerCase())
        : true)
  );

  const handleItemClick = (item: MenuItem) => {
    if ((item.attributes && item.attributes.length > 0) || item.isCombo) {
      setAttributeSelection({
        item,
        selectedAttributes: [],
        comboSelections: {},
      });
    } else {
      addToCart(item, []);
    }
  };

  const toggleAttribute = (
    attribute: ProductAttribute,
    groupType: "single" | "multiple"
  ) => {
    setAttributeSelection((prev) => {
      if (groupType === "single") {
        // For single-select, replace all attributes from the same group
        const otherGroupAttributes = prev.selectedAttributes.filter(
          (a) =>
            !attributeSelection.item?.attributes?.find(
              (group) =>
                group.type === "single" &&
                group.options.some((opt) => opt.id === a.id)
            )
        );
        return {
          ...prev,
          selectedAttributes: [...otherGroupAttributes, attribute],
        };
      } else {
        // For multi-select, toggle the attribute
        const exists = prev.selectedAttributes.some(
          (a) => a.id === attribute.id
        );
        return {
          ...prev,
          selectedAttributes: exists
            ? prev.selectedAttributes.filter((a) => a.id !== attribute.id)
            : [...prev.selectedAttributes, attribute],
        };
      }
    });
  };

  const handleComboSelection = (
    choiceName: string,
    option: { id: string; name: string }
  ) => {
    // Find the original menu item if it has attributes
    const menuItem = menuItems.find(
      (item) =>
        item.name === option.name &&
        item.attributes &&
        item.attributes.length > 0
    );

    if (menuItem?.attributes) {
      // If the selected item has attributes, open attribute selection for it
      setAttributeSelection((prev) => ({
        ...prev,
        item: {
          ...menuItem,
          isComboItem: true, // Mark as combo item to handle differently
          comboChoiceName: choiceName, // Store the choice name to restore later
          originalComboItem: prev.item, // Store the original combo item
        },
        selectedAttributes: [],
      }));
    } else {
      // If no attributes, proceed with normal combo selection
      setAttributeSelection((prev) => ({
        ...prev,
        comboSelections: {
          ...prev.comboSelections,
          [choiceName]: option,
        },
      }));
    }
  };

  const isComboComplete = () => {
    if (!attributeSelection.item?.comboChoices) return true;
    return attributeSelection.item.comboChoices.every(
      (choice) => attributeSelection.comboSelections?.[choice.name]
    );
  };

  const addToCart = (
    item: MenuItem,
    selectedAttributes: ProductAttribute[] = []
  ) => {
    if ((item as any).isComboItem) {
      // If this is a combo item's attribute selection, restore the combo selection
      const choiceName = (item as any).comboChoiceName;
      const originalComboItem = (item as any).originalComboItem;

      setAttributeSelection({
        item: originalComboItem,
        selectedAttributes: [],
        comboSelections: {
          ...attributeSelection.comboSelections,
          [choiceName]: {
            id: item.id,
            name: item.name,
            attributes: selectedAttributes,
          },
        },
      });
      return;
    }

    let totalPrice = item.price;

    // For combo items, calculate price based on selections
    if (item.isCombo) {
      totalPrice = 0; // Reset base price for combos

      // Calculate price from each combo selection
      Object.entries(attributeSelection.comboSelections || {}).forEach(
        ([choiceName, selection]) => {
          // Find the original menu item to get its base price
          const selectedMenuItem = menuItems.find(
            (menuItem) => menuItem.name === selection.name
          );
          if (selectedMenuItem) {
            totalPrice += selectedMenuItem.price;
          }

          // Add prices from attributes if any
          if ((selection as any).attributes) {
            totalPrice += (selection as any).attributes.reduce(
              (sum: number, attr: ProductAttribute) => sum + attr.price,
              0
            );
          }
        }
      );

      // Apply combo discount using the dynamic discount value
      const discountMultiplier = 1 - (item.discount || 0) / 100;
      totalPrice = totalPrice * discountMultiplier;
    } else {
      // For regular items, add attribute prices
      totalPrice += selectedAttributes.reduce(
        (sum, attr) => sum + attr.price,
        0
      );
    }

    setCartItems((prev) => {
      // Check if identical item exists
      const existingItemIndex = prev.findIndex((cartItem) => {
        // Check basic item properties
        const basicMatch =
          cartItem.id === item.id && cartItem.price === totalPrice;

        // For combo items, check combo selections
        if (item.isCombo) {
          if (!cartItem.isCombo) return false;

          // Compare combo selections
          const currentSelections = attributeSelection.comboSelections || {};
          const existingSelections = cartItem.comboSelections || {};

          return (
            basicMatch &&
            JSON.stringify(currentSelections) ===
              JSON.stringify(existingSelections)
          );
        }

        // For regular items, check attributes
        const currentAttrs = selectedAttributes || [];
        const existingAttrs = cartItem.selectedAttributes || [];

        return (
          basicMatch &&
          JSON.stringify(currentAttrs) === JSON.stringify(existingAttrs)
        );
      });

      let updatedItems;

      if (existingItemIndex !== -1) {
        // Update quantity of existing item
        updatedItems = prev.map((cartItem, index) =>
          index === existingItemIndex
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
        // Scroll to the updated item after render
        setTimeout(() => {
          lastItemRef.current?.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
          });
        }, 100);
      } else {
        // Add new item
        const newItem = {
          ...item,
          quantity: 1,
          selectedAttributes,
          price: totalPrice,
          comboSelections: item.isCombo
            ? attributeSelection.comboSelections
            : undefined,
        };
        // Scroll to the new item after render
        setTimeout(() => {
          lastItemRef.current?.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
          });
        }, 100);
        updatedItems = [...prev, newItem];
      }

      // Show toast notification for adding item
      const isNewItem = existingItemIndex === -1;
      if (isNewItem) {
        toast.success(`${item.name} has been added to the order.`);
      } else {
        toast.success(`${item.name} quantity has been updated.`);
      }

      return updatedItems;
    });

    setAttributeSelection({
      item: null,
      selectedAttributes: [],
      comboSelections: {},
    });
  };

  const updateQuantity = (itemId: string, delta: number) => {
    // Get the item name before it might be removed
    const itemToUpdate = cartItems.find((item) => item.id === itemId);
    const itemName = itemToUpdate?.name || "Item";
    const willBeRemoved = itemToUpdate && itemToUpdate.quantity + delta <= 0;

    setCartItems((prev) => {
      const updatedItems = prev
        .map((item) =>
          item.id === itemId
            ? { ...item, quantity: Math.max(0, item.quantity + delta) }
            : item
        )
        .filter((item) => item.quantity > 0);

      // Show toast notification if an item was removed
      if (willBeRemoved) {
        toast.success(`${itemName} has been removed from the order.`);
      }

      return updatedItems;
    });
  };

  const subtotal = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );
  const taxes = subtotal * 0.13; // 13% tax
  const total = subtotal + taxes;

  // Update the cart item selection handler to toggle selection
  const handleCartItemClick = (item: CartItem, index: number) => {
    if (selectedCartItem?.index === index) {
      // If clicking the same item, unselect it
      setSelectedCartItem(null);
      setCurrentInput("");
      setInputMode("qty");
    } else {
      // If clicking a different item, select it and set the current input based on mode
      setSelectedCartItem({ item, index });
      setInputMode("qty"); // Default to qty mode
      // Set the current input based on the item's current values
      setCurrentInput(item.quantity.toString());
    }
  };

  const getDisplayValue = () => {
    // Handle table mode first
    if (inputMode === "table") {
      return currentInput ? `Table ${currentInput}` : "Enter table number";
    }

    // Return empty string if no selected cart item or item is undefined
    if (!selectedCartItem?.item) return "";

    if (currentInput) {
      if (inputMode === "percent") {
        const percentValue = Math.min(
          100,
          Math.max(0, parseFloat(currentInput))
        );
        if (!isNaN(percentValue)) {
          const originalPrice =
            selectedCartItem.item.price * selectedCartItem.item.quantity;
          const discountAmount = (originalPrice * percentValue) / 100;
          return `${percentValue.toFixed(1)}% (-${discountAmount.toFixed(
            2
          )} Rs)`;
        }
      } else if (inputMode === "discount") {
        const discountValue = parseFloat(currentInput);
        if (!isNaN(discountValue)) {
          const originalPrice =
            selectedCartItem.item.price * selectedCartItem.item.quantity;
          const percentValue = Math.min(
            100,
            (discountValue / originalPrice) * 100
          );
          return `-${discountValue.toFixed(2)} Rs (${percentValue.toFixed(
            1
          )}%)`;
        }
      }
      return (
        currentInput +
        (inputMode === "percent"
          ? "%"
          : inputMode === "price"
          ? " Rs"
          : inputMode === "discount"
          ? " Rs"
          : "")
      );
    }

    // When no current input, show the existing values
    switch (inputMode) {
      case "qty":
        return selectedCartItem.item.quantity?.toString() || "";
      case "percent":
      case "discount":
        if (selectedCartItem.item.discount) {
          const originalPrice =
            (selectedCartItem.item.price * selectedCartItem.item.quantity) /
            (1 - selectedCartItem.item.discount / 100);
          const discountAmount =
            originalPrice -
            selectedCartItem.item.price * selectedCartItem.item.quantity;
          return `${selectedCartItem.item.discount.toFixed(
            1
          )}% (-${discountAmount.toFixed(2)} Rs)`;
        }
        return "0%";
      case "price":
        return selectedCartItem.item.price?.toFixed(2) + " Rs";
      default:
        return "";
    }
  };

  // Update the numpad input handler
  const handleNumpadInput = (value: string) => {
    if (!selectedCartItem) return;

    switch (value) {
      case "Qty":
        setInputMode("qty");
        setCurrentInput("");
        break;
      case "Discount":
        setInputMode(discountMode);
        setDiscountMode(discountMode === "percent" ? "discount" : "percent");
        setCurrentInput("");
        break;
      case "Price":
        setInputMode("price");
        setCurrentInput("");
        break;
      case "+/-":
        if (currentInput && inputMode !== "qty") {
          setCurrentInput(
            currentInput.startsWith("-")
              ? currentInput.substring(1)
              : "-" + currentInput
          );
        }
        break;
      case ".":
        if (!currentInput.includes(".")) {
          setCurrentInput(currentInput + ".");
        }
        break;
      default:
        // Handle number input
        const newInput = currentInput + value;
        setCurrentInput(newInput);

        const numericValue = parseFloat(newInput);
        if (!isNaN(numericValue)) {
          setCartItems((prev) =>
            prev.map((item, idx) => {
              if (idx !== selectedCartItem.index) return item;

              switch (inputMode) {
                case "qty":
                  return {
                    ...item,
                    quantity: Math.max(1, Math.round(numericValue)),
                  };
                case "percent":
                  const percentDiscount = Math.min(
                    100,
                    Math.max(0, numericValue)
                  );
                  const originalPrice = item.price * item.quantity;
                  let newPrice;

                  if (percentDiscount === 100) {
                    newPrice = 0;
                  } else {
                    const percentDiscountAmount =
                      (originalPrice * percentDiscount) / 100;
                    newPrice =
                      (originalPrice - percentDiscountAmount) / item.quantity;
                  }

                  return {
                    ...item,
                    discount: percentDiscount,
                    price: newPrice,
                  };
                case "discount":
                  const discountValue = Math.min(
                    item.price * item.quantity,
                    Math.max(0, numericValue)
                  );
                  const discountPercent = Math.min(
                    100,
                    (discountValue / (item.price * item.quantity)) * 100
                  );
                  const discountedPrice =
                    (item.price * item.quantity - discountValue) /
                    item.quantity;
                  return {
                    ...item,
                    discount: discountPercent,
                    price: discountedPrice,
                  };
                case "price":
                  return { ...item, price: Math.max(0, numericValue) };
                default:
                  return item;
              }
            })
          );
        }
    }
  };

  // Add demo customers data
  const demoCustomers: Customer[] = [
    { id: "1", name: "asanka", email: "<EMAIL>" },
    { id: "2", name: "John Doe" },
    {
      id: "3",
      name: "marketing54",
      email: "<EMAIL>",
      company: "marketing54 Sri Lanka",
    },
  ];

  // Add the filtered customers function
  const filteredCustomers = demoCustomers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(customerSearchQuery.toLowerCase()) ||
      customer.email
        ?.toLowerCase()
        .includes(customerSearchQuery.toLowerCase()) ||
      customer.company
        ?.toLowerCase()
        .includes(customerSearchQuery.toLowerCase())
  );

  // Quick note handler
  const handleQuickNote = (note: string) => {
    setOrderNote((prev) => {
      const notes = prev.split(", ").filter(Boolean);
      const exists = notes.includes(note);

      if (exists) {
        return notes.filter((n) => n !== note).join(", ");
      } else {
        return [...notes, note].join(", ");
      }
    });
  };

  // Add this new function to handle table number input
  const handleTableInput = (value: string) => {
    if (value === "." || value === "+/-") return; // Ignore these buttons for table input

    if (value === "X" || value === "⌫") {
      if (currentInput.length > 0) {
        setCurrentInput(currentInput.slice(0, -1));
      } else {
        setInputMode("qty"); // Exit table input mode
        setCurrentInput("");
      }
      return;
    }

    // Only allow numbers
    if (/^\d$/.test(value)) {
      setCurrentInput((prev) => prev + value);
    }
  };

  // Handle order type selection
  const handleOrderTypeSelect = (type: "Eat In" | "Takeout" | "Delivery") => {
    setOrderType(type);
    setIsOrderTypeDialogOpen(false);

    toast.success(`Order type set to ${type}`);

    // Update the order with the new type
    if (selectedTable && cartItems.length > 0) {
      const customer = selectedCustomer
        ? { id: selectedCustomer.id, name: selectedCustomer.name }
        : undefined;

      updateOrder(selectedTable, cartItems, orderNote, customer, type);
    }
  };

  // Add a function to handle payment button click
  const handlePayment = () => {
    if (cartItems.length === 0) {
      toast.error("Please add items to the order before payment");
      return;
    }

    setShowPaymentView(true);
  };

  // Handle payment completion
  const handlePaymentComplete = (completedPayments: PaymentEntry[]) => {
    if (selectedTable) {
      // Complete the order
      const completedOrder = completeOrder(selectedTable);

      if (completedOrder) {
        toast.success(`Payment of ${total.toFixed(2)} Rs processed with ${completedPayments.length} payment methods`);

        // Reset cart and related states
        setCartItems([]);
        setOrderNote("");
        setSelectedCustomer(null);

        // If this was a table order, clear the table selection
        onTableChange(null);
      }
    } else {
      // Direct sale
      toast.success(`Direct sale of ${total.toFixed(2)} Rs processed with ${completedPayments.length} payment methods`);

      // Reset cart and related states
      setCartItems([]);
      setOrderNote("");
      setSelectedCustomer(null);
    }

    setShowPaymentView(false);
  };

  // Handle payment cancellation
  const handlePaymentCancel = () => {
    setShowPaymentView(false);
  };

  // Render the payment view if showPaymentView is true
  if (showPaymentView) {
    return (
      <PaymentView
        total={total}
        onCancel={handlePaymentCancel}
        onComplete={handlePaymentComplete}
      />
    );
  }

  return (
    <div className="flex flex-1 gap-4 p-4">
      {/* Left side - Cart */}
      <Card className="w-[400px] flex flex-col h-[calc(100vh-2rem)]">
        <div className="p-4 border-b flex-none">
          <div className="flex items-center justify-between mb-4">
            <h2 className="font-semibold text-lg">Current Order</h2>
            {selectedTable ? (
              <Badge
                variant="outline"
                className="bg-purple-100 text-purple-800 border-purple-200"
              >
                Table {selectedTable}
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="bg-gray-100 text-gray-800 border-gray-200"
              >
                Direct Sale
              </Badge>
            )}
          </div>
        </div>

        <ScrollArea className="flex-1 h-0">
          <div className="p-4 space-y-4">
            {cartItems.map((item, index) => (
              <div
                key={`${item.id}-${index}`}
                ref={index === cartItems.length - 1 ? lastItemRef : null}
                className={`flex items-center gap-4 p-2 cursor-pointer rounded-md border-b-2 ${
                  categoryColors[item.category]
                } ${
                  selectedCartItem?.index === index
                    ? "bg-blue-50"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => handleCartItemClick(item, index)}
              >
                <div className="w-8 text-center text-sm">{item.quantity}</div>
                <div className="flex-1">
                  <div className="text-sm font-medium">{item.name}</div>
                  {/* Show combo selections */}
                  {item.comboSelections && (
                    <div className="mt-1 space-y-1">
                      {Object.entries(item.comboSelections).map(
                        ([choiceName, selection]) => (
                          <div key={choiceName} className="space-y-1">
                            <div className="text-xs text-gray-600">
                              {choiceName}:
                            </div>
                            <div className="flex flex-wrap gap-1">
                              <Badge variant="outline" className="text-xs">
                                {selection.name}
                              </Badge>
                              {/* Show attributes of combo items if any */}
                              {(selection as any).attributes?.map(
                                (attr: ProductAttribute) => (
                                  <Badge
                                    key={attr.id}
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {attr.name}{" "}
                                    {attr.price > 0 &&
                                      `+${attr.price.toFixed(2)} Rs`}
                                  </Badge>
                                )
                              )}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  )}
                  {/* Show regular item attributes */}
                  {item.selectedAttributes &&
                    item.selectedAttributes.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {item.selectedAttributes.map((attr) => (
                          <Badge
                            key={attr.id}
                            variant="secondary"
                            className="text-xs"
                          >
                            {attr.name} +{attr.price.toFixed(2)} Rs
                          </Badge>
                        ))}
                      </div>
                    )}
                  {item.description && (
                    <div className="text-xs text-gray-500 italic">
                      {item.description}
                    </div>
                  )}
                  {/* Add discount information */}
                  {item.discount && item.discount > 0 && (
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="destructive" className="text-xs">
                        {item.discount}% OFF
                      </Badge>
                      <span className="text-xs text-gray-500 line-through">
                        {item.discount === 100
                          ? (item.price * item.quantity * 2).toFixed(2)
                          : (
                              (item.price * item.quantity * 100) /
                              (100 - item.discount)
                            ).toFixed(2)}{" "}
                        Rs
                      </span>
                    </div>
                  )}
                </div>
                <div className="w-20 text-right text-sm font-medium">
                  {(item.price * item.quantity).toFixed(2)} Rs
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        {selectedCartItem ? (
          // Calculator/Numpad View
          <div className="p-4 border-t flex-none">
            <div className="flex flex-col gap-2 mb-4">
              <div className="w-full p-4 bg-gray-100 rounded-md">
                <span className="text-xl font-mono">{getDisplayValue()}</span>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-2">
              {/* First row */}
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("1")
                    : handleNumpadInput("1")
                }
              >
                1
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("2")
                    : handleNumpadInput("2")
                }
              >
                2
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("3")
                    : handleNumpadInput("3")
                }
              >
                3
              </Button>
              <Button
                variant={inputMode === "qty" ? "default" : "outline"}
                size="sm"
                onClick={() => handleNumpadInput("Qty")}
                disabled={inputMode === "table"}
                className={
                  inputMode === "qty"
                    ? "bg-blue-100 hover:bg-blue-200 text-blue-800"
                    : ""
                }
              >
                Qty
              </Button>

              {/* Second row */}
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("4")
                    : handleNumpadInput("4")
                }
              >
                4
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("5")
                    : handleNumpadInput("5")
                }
              >
                5
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("6")
                    : handleNumpadInput("6")
                }
              >
                6
              </Button>
              <Button
                variant={inputMode === "percent" ? "default" : "outline"}
                size="sm"
                onClick={() => handleNumpadInput("Discount")}
                disabled={inputMode === "table"}
              >
                %
              </Button>

              {/* Third row */}
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("7")
                    : handleNumpadInput("7")
                }
              >
                7
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("8")
                    : handleNumpadInput("8")
                }
              >
                8
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("9")
                    : handleNumpadInput("9")
                }
              >
                9
              </Button>
              <Button
                variant={inputMode === "price" ? "default" : "outline"}
                size="sm"
                onClick={() => handleNumpadInput("Price")}
                disabled={inputMode === "table"}
              >
                Price
              </Button>

              {/* Fourth row */}
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("+/-")
                    : handleNumpadInput("+/-")
                }
                disabled={inputMode === "table"}
              >
                +/-
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput("0")
                    : handleNumpadInput("0")
                }
              >
                0
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  inputMode === "table"
                    ? handleTableInput(".")
                    : handleNumpadInput(".")
                }
                disabled={inputMode === "table"}
              >
                .
              </Button>
              <Button
                className="bg-red-500 hover:bg-red-600 text-white"
                size="sm"
                onClick={() => {
                  if (inputMode === "table") {
                    if (currentInput) {
                      handleTableInput("⌫");
                    } else {
                      setInputMode("qty");
                      setCurrentInput("");
                    }
                  } else {
                    if (currentInput.length > 0) {
                      setCurrentInput(currentInput.slice(0, -1));
                    } else {
                      setSelectedCartItem(null);
                    }
                  }
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            {inputMode === "table" && currentInput && (
              <Button
                className="w-full mt-4 bg-purple-700 hover:bg-purple-800"
                onClick={() => {
                  onTableChange(currentInput);
                  setInputMode("qty");
                  setCurrentInput("");
                  setSelectedCartItem(null);
                }}
              >
                Assign
              </Button>
            )}
          </div>
        ) : (
          // Normal Cart Footer
          <div className="p-4 border-t space-y-4 flex-none">
            {/* Show order note if exists */}
            {orderNote && (
              <div className="flex items-center gap-2 mb-2">
                <Badge
                  variant="secondary"
                  className="bg-yellow-100 text-yellow-800 border-yellow-200"
                >
                  Note: {orderNote}
                </Badge>
              </div>
            )}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Taxes</span>
                <span>{taxes.toFixed(2)} Rs</span>
              </div>
              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>{total.toFixed(2)} Rs</span>
              </div>
            </div>
            <div className="grid grid-cols-3 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsCustomerDialogOpen(true)}
              >
                {selectedCustomer ? selectedCustomer.name : "Customer"}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsNoteDialogOpen(true);
                }}
              >
                Note
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsOrderTypeDialogOpen(true)}
              >
                {orderType}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="col-span-2"
                onClick={() => {
                  setInputMode("table");
                  setCurrentInput(selectedTable || "");
                  setSelectedCartItem({ item: {} as CartItem, index: -1 });
                }}
              >
                {selectedTable ? `Table ${selectedTable}` : "Set Table"}
              </Button>
              <Button variant="outline" size="sm">
                Tab
              </Button>
              <Button
                className="col-span-3 bg-purple-700 hover:bg-purple-800"
                onClick={handlePayment}
              >
                Payment
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Right side - Menu */}
      <div className="flex flex-col flex-1">
        {/* Search and Categories */}
        <div className="space-y-4 mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search menu items..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button
              variant={selectedCategory === "Food" ? "default" : "outline"}
              onClick={() => setSelectedCategory("Food")}
              className={`flex-1 ${
                selectedCategory === "Food"
                  ? "bg-green-100 hover:bg-green-200 text-green-800 border-green-200"
                  : "hover:bg-green-50 hover:text-green-800"
              }`}
            >
              Food
            </Button>
            <Button
              variant={selectedCategory === "Drinks" ? "default" : "outline"}
              onClick={() => setSelectedCategory("Drinks")}
              className={`flex-1 ${
                selectedCategory === "Drinks"
                  ? "bg-blue-100 hover:bg-blue-200 text-blue-800 border-blue-200"
                  : "hover:bg-blue-50 hover:text-blue-800"
              }`}
            >
              Drinks
            </Button>
          </div>
        </div>

        {/* Menu Grid */}
        <ScrollArea className="flex-1">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
            {filteredItems.map((item) => (
              <Card
                key={item.id}
                className={`p-4 cursor-pointer hover:shadow-lg transition-shadow relative border-b-2 ${
                  categoryColors[item.category]
                }`}
                onClick={() => handleItemClick(item)}
              >
                <div className="absolute top-2 right-2 bg-gray-100 rounded-full w-6 h-6 flex items-center justify-center text-xs">
                  1
                </div>
                <h3 className="font-medium text-sm mb-1">{item.name}</h3>
                {item.description && (
                  <p className="text-xs text-gray-500 italic mb-2">
                    {item.description}
                  </p>
                )}
                <p className="text-sm text-gray-600">
                  {item.isCombo && item.discount && item.discount > 0 ? (
                    <span className="text-blue-600">
                      Combo with {item.discount}% discount
                    </span>
                  ) : (
                    `${item.price.toFixed(2)} Rs`
                  )}
                </p>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Attribute Selection Dialog */}
      <Dialog
        open={attributeSelection.item !== null}
        onOpenChange={() =>
          setAttributeSelection({
            item: null,
            selectedAttributes: [],
            comboSelections: {},
          })
        }
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {attributeSelection.item?.isCombo
                ? attributeSelection.item.name
                : "Attribute selection"}
            </DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-[calc(100vh-8rem)]">
            <div className="space-y-4">
              {attributeSelection.item && (
                <>
                  {!attributeSelection.item.isCombo ? (
                    // Regular product attributes
                    <>
                      <div className="space-y-2">
                        <h3 className="text-lg font-medium">
                          {attributeSelection.item.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {attributeSelection.item.price.toFixed(2)} Rs{" "}
                          <span className="text-xs">
                            VAT: 15% (={" "}
                            {(attributeSelection.item.price * 0.15).toFixed(2)}{" "}
                            Rs)
                          </span>
                        </p>
                      </div>
                      {attributeSelection.item.attributes?.map((attrGroup) => (
                        <div key={attrGroup.name} className="space-y-2">
                          <h4 className="font-medium">{attrGroup.name}</h4>
                          <div className="flex flex-wrap gap-2">
                            {attrGroup.options.map((option) => {
                              const isSelected =
                                attributeSelection.selectedAttributes.some(
                                  (a) => a.id === option.id
                                );
                              return (
                                <Button
                                  key={option.id}
                                  variant={isSelected ? "default" : "outline"}
                                  className={`text-sm ${
                                    attrGroup.type === "single" && isSelected
                                      ? "bg-blue-100 hover:bg-blue-200 text-blue-800 border-blue-200"
                                      : ""
                                  }`}
                                  onClick={() =>
                                    toggleAttribute(option, attrGroup.type)
                                  }
                                >
                                  {option.name}
                                  {option.price > 0 &&
                                    ` +${option.price.toFixed(2)} Rs`}
                                </Button>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </>
                  ) : (
                    // Combo product selections
                    <div className="space-y-6">
                      {attributeSelection.item.comboChoices?.map((choice) => (
                        <div key={choice.name} className="space-y-3">
                          <h3 className="font-medium text-lg">{choice.name}</h3>
                          <div className="grid grid-cols-2 gap-4">
                            {choice.options.map((option) => {
                              const isSelected =
                                attributeSelection.comboSelections?.[
                                  choice.name
                                ]?.id === option.id;
                              return (
                                <Card
                                  key={option.id}
                                  className={`p-4 cursor-pointer transition-all ${
                                    isSelected
                                      ? "ring-2 ring-blue-500 bg-blue-50"
                                      : "hover:bg-gray-50"
                                  }`}
                                  onClick={() =>
                                    handleComboSelection(choice.name, option)
                                  }
                                >
                                  <div className="aspect-square relative mb-3">
                                    {option.image && (
                                      <Image
                                        src={option.image}
                                        alt={option.name}
                                        fill
                                        className="object-cover rounded-lg"
                                      />
                                    )}
                                  </div>
                                  <h4 className="font-medium">{option.name}</h4>
                                  {option.description && (
                                    <p className="text-sm text-gray-500">
                                      {option.description}
                                    </p>
                                  )}
                                </Card>
                              );
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  <div className="flex justify-end gap-2 pt-4">
                    <Button
                      variant="outline"
                      onClick={() =>
                        setAttributeSelection({
                          item: null,
                          selectedAttributes: [],
                          comboSelections: {},
                        })
                      }
                    >
                      Discard
                    </Button>
                    <Button
                      onClick={() =>
                        addToCart(
                          attributeSelection.item!,
                          attributeSelection.selectedAttributes
                        )
                      }
                      disabled={
                        attributeSelection.item.isCombo && !isComboComplete()
                      }
                    >
                      Add
                    </Button>
                  </div>
                </>
              )}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Customer Selection Dialog */}
      <Dialog
        open={isCustomerDialogOpen}
        onOpenChange={setIsCustomerDialogOpen}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setIsCustomerDialogOpen(false)}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <DialogTitle>Choose customer</DialogTitle>
            </div>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex gap-2">
              <Button variant="secondary" className="flex-1">
                Create
              </Button>
              <div className="relative flex-[2]">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search Customers..."
                  className="pl-10"
                  value={customerSearchQuery}
                  onChange={(e) => setCustomerSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <ScrollArea className="h-[400px] pr-4">
              <div className="space-y-2">
                {filteredCustomers.map((customer) => (
                  <div
                    key={customer.id}
                    className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md cursor-pointer"
                    onClick={() => {
                      setSelectedCustomer(customer);
                      setIsCustomerDialogOpen(false);
                    }}
                  >
                    <div className="space-y-1">
                      <div className="font-medium">{customer.name}</div>
                      {customer.email && (
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <span>{customer.email}</span>
                        </div>
                      )}
                      {customer.company && (
                        <div className="text-sm text-gray-500">
                          {customer.company}
                        </div>
                      )}
                    </div>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>

            <Button
              variant="outline"
              className="w-full"
              onClick={() => setIsCustomerDialogOpen(false)}
            >
              Discard
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Note Dialog */}
      <Dialog open={isNoteDialogOpen} onOpenChange={setIsNoteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Note</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              {["Wait", "To Serve", "Emergency", "No Dressing"].map((note) => (
                <Button
                  key={note}
                  variant={orderNote.includes(note) ? "default" : "outline"}
                  onClick={() => handleQuickNote(note)}
                  className={
                    orderNote.includes(note)
                      ? "bg-blue-100 hover:bg-blue-200 text-blue-800"
                      : ""
                  }
                >
                  {note}
                </Button>
              ))}
            </div>
            <textarea
              value={orderNote}
              onChange={(e) => setOrderNote(e.target.value)}
              className="w-full min-h-[100px] p-3 border rounded-md"
              placeholder="Add a note..."
            />
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsNoteDialogOpen(false);
                }}
              >
                Discard
              </Button>
              <Button onClick={() => setIsNoteDialogOpen(false)}>Apply</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Order Type Dialog */}
      <Dialog
        open={isOrderTypeDialogOpen}
        onOpenChange={setIsOrderTypeDialogOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Select preset</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col space-y-4 py-4">
            <button
              className={`flex items-center justify-between p-4 rounded-md border ${
                orderType === "Eat In" ? "bg-blue-50 border-blue-200" : ""
              }`}
              onClick={() => handleOrderTypeSelect("Eat In")}
            >
              <span>Eat In</span>
              {orderType === "Eat In" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-500"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              )}
            </button>

            <button
              className={`flex items-center justify-between p-4 rounded-md border ${
                orderType === "Takeout" ? "bg-blue-50 border-blue-200" : ""
              }`}
              onClick={() => handleOrderTypeSelect("Takeout")}
            >
              <span>Takeout</span>
              {orderType === "Takeout" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-500"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              )}
            </button>

            <button
              className={`flex items-center justify-between p-4 rounded-md border ${
                orderType === "Delivery" ? "bg-blue-50 border-blue-200" : ""
              }`}
              onClick={() => handleOrderTypeSelect("Delivery")}
            >
              <span>Delivery</span>
              {orderType === "Delivery" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-500"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              )}
            </button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
