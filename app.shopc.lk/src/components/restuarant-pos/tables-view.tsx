"use client";

import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { Card } from "@/components/ui/card";
import {
  Users,
  Grid2X2,
  RotateCcw,
  Plus,
  Minus,
  PlusCircle,
  Trash2,
  Square,
  RectangleHorizontal,
  Edit2,
  Check,
  X,
  Eye,
  EyeOff,
  Settings,
  Table as TableIcon,
  Circle,
  Building,
  Building2,
  MoreHorizontal,
  Pencil,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FloorPlanSettings } from "./floor-plan-settings";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import {
  useFloorPlansData,
  useCreateFloorPlan,
  useUpdateFloorPlan,
  useDeleteFloorPlan,
  useRestaurantTablesData,
  useCreateRestaurantTable,
  useUpdateRestaurantTable,
  useDeleteRestaurantTable,
  useUpdateRestaurantTableStatus,
  useFloorPlanStatus,
  useUpdateFloorPlanStatus,
} from "@/lib/restaurant-tables";
import type {
  FloorPlanDto,
  RestaurantTableDto,
  CreateFloorPlanDto,
  UpdateFloorPlanDto,
  CreateRestaurantTableDto,
  UpdateRestaurantTableDto,
} from "@/types/restaurant-table";
import { RestaurantTableStatus as APIRestaurantTableStatus } from "@/types/restaurant-table";

// Types and Interfaces
export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface TableShape {
  type: "square" | "rectangle" | "circle";
  seats: number;
  size: Size;
}

export interface TableData {
  id: number;
  number: string;
  position: Position;
  shape: TableShape;
  status: "available" | "occupied" | "reserved";
  orders?: number;
}

export interface KitchenArea {
  position: Position;
  size: Size;
}

export interface ReceptionArea {
  position: Position;
  size: Size;
  isVisible: boolean;
}

export interface FloorPlan {
  tables: TableData[];
  kitchen: KitchenArea;
  reception: ReceptionArea;
  dimensions: Size;
}

export interface Floor {
  id: string;
  name: string;
  floorPlan: FloorPlan;
}

// Sample floor plan data
const initialFloorPlan: FloorPlan = {
  tables: [],
  kitchen: {
    position: { x: 0, y: 0 },
    size: { width: 300, height: 300 },
  },
  reception: {
    position: { x: 700, y: 50 },
    size: { width: 200, height: 100 },
    isVisible: false,
  },
  dimensions: { width: 1000, height: 800 },
};

// Initial floors
const initialFloors: Floor[] = [];

const getStatusColor = (status: TableData["status"]) => {
  switch (status) {
    case "available":
      return "bg-green-500";
    case "occupied":
      return "bg-red-500";
    case "reserved":
      return "bg-yellow-500";
    default:
      return "bg-gray-500";
  }
};

// Grid configuration
const GRID_SIZE = 20; // Size of grid cells in pixels
const MIN_TABLE_SIZE = 60; // Minimum table size in pixels

function snapToGrid(value: number, gridSize: number = GRID_SIZE): number {
  return Math.round(value / gridSize) * gridSize;
}

type ResizeHandle = "e" | "s" | "se" | null;

interface ResizeState {
  tableId: number;
  handle: ResizeHandle;
  initialSize: Size;
  initialMousePos: Position;
}

// View mode type
type ViewMode = "edit" | "table";

interface TablesViewProps {
  isDemo?: boolean;
  onTableSelect?: (tableNumber: string) => void;
  setActiveTab?: (tab: string) => void;
}

export function TablesView({
  isDemo = false,
  onTableSelect,
  setActiveTab,
}: TablesViewProps) {
  // API hooks for floor plans
  const { data: floorPlansResponse, isLoading: floorsLoading } =
    useFloorPlansData(isDemo);
  const createFloorPlanMutation = useCreateFloorPlan(isDemo);
  const updateFloorPlanMutation = useUpdateFloorPlan(isDemo);
  const deleteFloorPlanMutation = useDeleteFloorPlan(isDemo);

  // API hooks for restaurant tables
  const createTableMutation = useCreateRestaurantTable(isDemo);
  const updateTableMutation = useUpdateRestaurantTable(isDemo);
  const deleteTableMutation = useDeleteRestaurantTable(isDemo);
  const updateTableStatusMutation = useUpdateRestaurantTableStatus(isDemo);

  // Local state
  const [activeFloorId, setActiveFloorId] = useState<string>("");
  const [floorPlan, setFloorPlan] = useState<FloorPlan>(initialFloorPlan);

  // Get floors from API response with fallback to initial data
  const floors: Floor[] = React.useMemo(() => {
    console.log("floorPlansResponse. data ::: ", floorPlansResponse?.data);
    if (floorPlansResponse?.data && Array.isArray(floorPlansResponse.data)) {
      return floorPlansResponse.data.map((floorPlan: FloorPlanDto) => ({
        id: floorPlan.id,
        name: floorPlan.name,
        floorPlan: {
          tables: [], // Will be populated separately
          kitchen: floorPlan.kitchen,
          reception: floorPlan.reception,
          dimensions: floorPlan.dimensions,
        },
      }));
    }
    return initialFloors;
  }, [floorPlansResponse]);

  // Set initial active floor when floors are loaded
  React.useEffect(() => {
    if (floors.length > 0 && !activeFloorId) {
      setActiveFloorId(floors[0].id);
    }
  }, [floors, activeFloorId]);

  // Get restaurant tables for the active floor
  const { data: tablesResponse, isLoading: tablesLoading } =
    useRestaurantTablesData(
      {
        page: 1,
        perPage: 100,
        floorId: activeFloorId,
      },
      isDemo
    );

  // Update floor plan when data changes
  React.useEffect(() => {
    const currentFloor = floors.find((f) => f.id === activeFloorId);
    if (!currentFloor) {
      setFloorPlan(initialFloorPlan);
      return;
    }

    const apiTables = tablesResponse?.data?.data || [];
    const tables: TableData[] = apiTables.map((apiTable) => ({
      id: parseInt(apiTable.id.replace(/\D/g, "") || "0"), // Convert string ID to number for compatibility
      number: apiTable.tableNumber,
      position: apiTable.position,
      shape: apiTable.shape,
      status: apiTable.status as any, // Type compatibility
      orders: apiTable.orders,
    }));

    setFloorPlan({
      ...currentFloor.floorPlan,
      tables,
    });
  }, [floors, activeFloorId, tablesResponse]);
  const [isDragging, setIsDragging] = useState(false);
  const [draggedTable, setDraggedTable] = useState<TableData | null>(null);
  const [showGrid, setShowGrid] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const [resizeState, setResizeState] = useState<ResizeState | null>(null);
  const [dimensions, setDimensions] = useState<string>("");
  const [tableToDelete, setTableToDelete] = useState<number | null>(null);
  const [editingTableId, setEditingTableId] = useState<number | null>(null);
  const [editingTableName, setEditingTableName] = useState<string>("");
  const tableNameInputRef = useRef<HTMLInputElement>(null);
  const [selectedTableId, setSelectedTableId] = useState<number | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>("table");
  const [showTableNumbers, setShowTableNumbers] = useState(true);
  const [showTableStatus, setShowTableStatus] = useState(true);
  const [showTableSeats, setShowTableSeats] = useState(true);
  const [showTableOrders, setShowTableOrders] = useState(true);
  const [isAddingFloor, setIsAddingFloor] = useState(false);
  const [newFloorName, setNewFloorName] = useState("");
  const [editingFloorId, setEditingFloorId] = useState<string | null>(null);
  const [editingFloorName, setEditingFloorName] = useState("");
  const [floorToDelete, setFloorToDelete] = useState<string | null>(null);
  const floorNameInputRef = useRef<HTMLInputElement>(null);

  // Function to add a new floor
  const addFloor = async () => {
    if (!newFloorName.trim()) return;

    const newFloorData: CreateFloorPlanDto = {
      name: newFloorName.trim(),
      dimensions: { width: 1000, height: 800 },
      kitchen: {
        position: { x: 0, y: 0 },
        size: { width: 300, height: 300 },
      },
      reception: {
        position: { x: 700, y: 50 },
        size: { width: 200, height: 100 },
        isVisible: false,
      },
    };

    try {
      const result = await createFloorPlanMutation.mutateAsync(newFloorData);

      // Show success toast
      toast.success(`"${newFloorName.trim()}" has been added successfully.`);

      // Reset input state
      setNewFloorName("");
      setIsAddingFloor(false);

      // Switch to edit mode for the new floor
      setViewMode("edit");

      // Set the active floor ID
      if (result.data!.id) {
        setActiveFloorId(result.data!.id);
      }
    } catch (error) {
      toast.error("Failed to add floor. Please try again.");
    }
  };

  // Function to start editing a floor name
  const startEditingFloorName = (floor: Floor) => {
    setEditingFloorId(floor.id);
    setEditingFloorName(floor.name);

    // Focus the input after it renders
    setTimeout(() => {
      if (floorNameInputRef.current) {
        floorNameInputRef.current.focus();
        floorNameInputRef.current.select();
      }
    }, 10);
  };

  // Function to save the edited floor name
  const saveFloorName = async () => {
    if (!editingFloorId) return;

    // Trim the name and ensure it's not empty
    const trimmedName = editingFloorName.trim();
    if (!trimmedName) return;

    try {
      await updateFloorPlanMutation.mutateAsync({
        id: editingFloorId,
        data: { name: trimmedName },
      });

      // Show success toast
      toast.success(`Floor has been renamed to "${trimmedName}".`);

      // Exit editing mode
      setEditingFloorId(null);
      setEditingFloorName("");
    } catch (error) {
      toast.error("Failed to rename floor. Please try again.");
    }
  };

  // Function to cancel editing floor name
  const cancelEditingFloorName = () => {
    setEditingFloorId(null);
    setEditingFloorName("");
  };

  // Handle keyboard events for the floor name input
  const handleFloorNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      saveFloorName();
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEditingFloorName();
    }
  };

  // Function to delete a floor
  const deleteFloor = async (floorId: string) => {
    // Don't allow deleting the last floor
    if (floors.length <= 1) return;

    // Get the floor name for the toast message
    const floorName =
      floors.find((floor) => floor.id === floorId)?.name || "Floor";

    try {
      await deleteFloorPlanMutation.mutateAsync(floorId);

      // If the active floor is being deleted, switch to another floor
      if (activeFloorId === floorId) {
        const remainingFloors = floors.filter((floor) => floor.id !== floorId);
        if (remainingFloors.length > 0) {
          setActiveFloorId(remainingFloors[0].id);
        }
      }

      // Show success toast
      toast.success(`"${floorName}" has been deleted successfully.`);

      setFloorToDelete(null);
    } catch (error) {
      toast.error("Failed to delete floor. Please try again.");
    }
  };

  // Drag handlers
  const handleDragStart = (e: React.DragEvent, table: TableData) => {
    setIsDragging(true);
    setDraggedTable(table);

    // Calculate offset from mouse position to table corner
    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });

    // Set drag image to be transparent
    const img = new Image();
    img.src =
      "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
    e.dataTransfer.setDragImage(img, 0, 0);
  };

  const handleDragEnd = async () => {
    if (draggedTable) {
      // Persist the position change to the API
      const updatedTable = floorPlan.tables.find(
        (t) => t.id === draggedTable.id
      );
      if (updatedTable) {
        await updateTableInAPI(updatedTable, {
          position: updatedTable.position,
        });
      }
    }
    setIsDragging(false);
    setDraggedTable(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isDragging || !draggedTable || !containerRef.current) return;

    const container = containerRef.current.getBoundingClientRect();

    // Calculate new position with offset
    let x = e.clientX - container.left - dragOffset.x;
    let y = e.clientY - container.top - dragOffset.y;

    // Snap to grid if enabled
    if (showGrid) {
      x = snapToGrid(x);
      y = snapToGrid(y);
    }

    // Ensure table stays within bounds
    x = Math.max(
      0,
      Math.min(x, container.width - draggedTable.shape.size.width)
    );
    y = Math.max(
      0,
      Math.min(y, container.height - draggedTable.shape.size.height)
    );

    const updatedTables = floorPlan.tables.map((table) =>
      table.id === draggedTable.id
        ? {
            ...table,
            position: { x, y },
          }
        : table
    );

    setFloorPlan((prev) => ({
      ...prev,
      tables: updatedTables,
    }));
  };

  // Function to persist table updates to API
  const updateTableInAPI = async (
    table: TableData,
    updates: UpdateRestaurantTableDto
  ) => {
    // Find the corresponding API table to get the string ID
    const apiTables = tablesResponse?.data?.data || [];
    const apiTable = apiTables.find(
      (apiTable) =>
        apiTable.tableNumber === table.number &&
        apiTable.floorId === activeFloorId
    );

    if (!apiTable) return;

    try {
      await updateTableMutation.mutateAsync({
        id: apiTable.id,
        data: updates,
      });
    } catch (error) {
      toast.error("Failed to update table. Please try again.");
    }
  };

  // Resize handlers
  const startResize = (
    e: React.MouseEvent,
    tableId: number,
    handle: ResizeHandle
  ) => {
    e.stopPropagation();
    e.preventDefault();

    const table = floorPlan.tables.find((t) => t.id === tableId);
    if (!table) return;

    setResizeState({
      tableId,
      handle,
      initialSize: { ...table.shape.size },
      initialMousePos: { x: e.clientX, y: e.clientY },
    });
  };

  // Use useCallback to ensure the function reference is stable
  const handleResize = useCallback(
    (e: MouseEvent) => {
      if (!resizeState || !containerRef.current) return;

      const { tableId, handle, initialSize, initialMousePos } = resizeState;

      const table = floorPlan.tables.find((t) => t.id === tableId);
      if (!table) return;

      const deltaX = e.clientX - initialMousePos.x;
      const deltaY = e.clientY - initialMousePos.y;

      let newWidth = initialSize.width;
      let newHeight = initialSize.height;

      // Update dimensions based on which handle is being dragged
      if (handle === "e" || handle === "se") {
        newWidth = Math.max(MIN_TABLE_SIZE, initialSize.width + deltaX);
        if (showGrid) newWidth = snapToGrid(newWidth);
      }

      if (handle === "s" || handle === "se") {
        newHeight = Math.max(MIN_TABLE_SIZE, initialSize.height + deltaY);
        if (showGrid) newHeight = snapToGrid(newHeight);
      }

      // Update dimensions display
      setDimensions(
        `W: ${Math.round(newWidth)}px, H: ${Math.round(newHeight)}px`
      );

      // Update table size
      setFloorPlan((prevPlan) => {
        const updatedTables = prevPlan.tables.map((t) =>
          t.id === tableId
            ? {
                ...t,
                shape: {
                  ...t.shape,
                  size: { width: newWidth, height: newHeight },
                },
              }
            : t
        );

        return {
          ...prevPlan,
          tables: updatedTables,
        };
      });
    },
    [floorPlan.tables, resizeState, showGrid]
  );

  const endResize = useCallback(async () => {
    if (resizeState) {
      // Persist the size change to the API
      const table = floorPlan.tables.find((t) => t.id === resizeState.tableId);
      if (table) {
        await updateTableInAPI(table, { shape: table.shape });
      }
    }
    setResizeState(null);
    setDimensions("");
  }, [resizeState, floorPlan.tables, updateTableInAPI]);

  // Set up and clean up resize event listeners
  useEffect(() => {
    if (resizeState) {
      window.addEventListener("mousemove", handleResize);
      window.addEventListener("mouseup", endResize);

      return () => {
        window.removeEventListener("mousemove", handleResize);
        window.removeEventListener("mouseup", endResize);
      };
    }
  }, [resizeState, handleResize, endResize]);

  const resetLayout = () => {
    // Reset only the current floor to its initial state
    const activeFloor = floors.find((floor) => floor.id === activeFloorId);
    if (activeFloor && activeFloor.id === "main") {
      setFloorPlan(initialFloorPlan);
    } else if (activeFloor && activeFloor.id === "patio") {
      setFloorPlan({
        ...initialFloorPlan,
        tables: initialFloorPlan.tables.slice(0, 4),
      });
    } else if (activeFloor) {
      // For custom floors, just clear the tables
      setFloorPlan((prevPlan) => ({
        ...prevPlan,
        tables: [],
      }));
    }
  };

  // Seat adjustment handler
  const adjustSeats = async (tableId: number, change: number) => {
    // Find the table to update
    const tableToUpdate = floorPlan.tables.find((table) => table.id === tableId);
    if (!tableToUpdate) return;

    // Calculate new seat count with min of 2 and max of 12
    const newSeats = Math.max(
      2,
      Math.min(12, tableToUpdate.shape.seats + change)
    );

    // If no change needed, return early
    if (newSeats === tableToUpdate.shape.seats) return;

    // Update local state first for immediate UI feedback
    setFloorPlan((prevPlan) => {
      const updatedTables = prevPlan.tables.map((table) => {
        if (table.id === tableId) {
          return {
            ...table,
            shape: {
              ...table.shape,
              seats: newSeats,
            },
          };
        }
        return table;
      });

      return {
        ...prevPlan,
        tables: updatedTables,
      };
    });

    // Persist to backend API
    const updatedTable = {
      ...tableToUpdate,
      shape: {
        ...tableToUpdate.shape,
        seats: newSeats,
      },
    };

    await updateTableInAPI(updatedTable, {
      shape: updatedTable.shape,
    });
  };

  // Function to add a new table
  const addTable = async (type: "square" | "rectangle" | "circle") => {
    if (!activeFloorId) return;

    // Generate a unique table number
    let newNumber = "";
    let counter = 1;

    // Keep incrementing until we find a number that doesn't exist on this floor
    while (true) {
      const candidateNumber = counter.toString();

      // Check if this number exists on the current floor
      const isNumberTaken = floorPlan.tables.some(
        (table) => table.number === candidateNumber
      );

      if (!isNumberTaken) {
        newNumber = candidateNumber;
        break;
      }
      counter++;
    }

    // Default position in the center of the viewport
    let newPosition = { x: 400, y: 300 };

    // If container ref is available, position in the center of the visible area
    if (containerRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const scrollTop = containerRef.current.scrollTop;

      newPosition = {
        x: containerRect.width / 2 - 50, // Half of default width
        y: containerRect.height / 2 - 50 + scrollTop, // Half of default height + scroll offset
      };

      // Snap to grid if enabled
      if (showGrid) {
        newPosition.x = snapToGrid(newPosition.x);
        newPosition.y = snapToGrid(newPosition.y);
      }
    }

    // Create the new table data
    const newTableData: CreateRestaurantTableDto = {
      floorId: activeFloorId,
      tableNumber: newNumber,
      position: newPosition,
      shape: {
        type: type,
        seats: type === "circle" ? 4 : type === "square" ? 4 : 6,
        size:
          type === "circle"
            ? { width: 120, height: 120 }
            : type === "square"
            ? { width: 100, height: 100 }
            : { width: 160, height: 100 },
      },
      status: APIRestaurantTableStatus.AVAILABLE,
    };

    try {
      await createTableMutation.mutateAsync(newTableData);

      // Show success toast
      toast.success(`Table ${newNumber} has been added successfully.`);
    } catch (error) {
      toast.error("Failed to add table. Please try again.");
    }
  };

  // Function to delete a table
  const deleteTable = async (tableId: number) => {
    // Get the table data for the API call
    const table = floorPlan.tables.find((table) => table.id === tableId);
    if (!table) return;

    // Find the corresponding API table to get the string ID
    const apiTables = tablesResponse?.data?.data || [];
    const apiTable = apiTables.find(
      (apiTable) =>
        apiTable.tableNumber === table.number &&
        apiTable.floorId === activeFloorId
    );

    if (!apiTable) return;

    try {
      await deleteTableMutation.mutateAsync(apiTable.id);

      // Show success toast
      toast.success(`Table ${table.number} has been deleted successfully.`);

      setTableToDelete(null);
    } catch (error) {
      toast.error("Failed to delete table. Please try again.");
    }
  };

  // Function to start editing a table name
  const startEditingTableName = (table: TableData) => {
    // Don't allow editing if we're resizing or dragging
    if (resizeState || isDragging) return;

    setEditingTableId(table.id);
    setEditingTableName(table.number);

    // Focus the input after it renders
    setTimeout(() => {
      if (tableNameInputRef.current) {
        tableNameInputRef.current.focus();
        tableNameInputRef.current.select();
      }
    }, 10);
  };

  // Function to save the edited table name
  const saveTableName = async () => {
    if (!editingTableId) return;

    // Trim the name and ensure it's not empty
    const trimmedName = editingTableName.trim();
    if (!trimmedName) return;

    // Find the table being edited
    const tableToUpdate = floorPlan.tables.find((table) => table.id === editingTableId);
    if (!tableToUpdate) return;

    // If name hasn't changed, just exit editing mode
    if (tableToUpdate.number === trimmedName) {
      setEditingTableId(null);
      setEditingTableName("");
      return;
    }

    // Check if the new name is already used by any table in ANY floor
    const isNameTaken = floors.some((floor) =>
      floor.floorPlan.tables.some(
        (table) =>
          // Skip the current table being edited
          !(floor.id === activeFloorId && table.id === editingTableId) &&
          table.number === trimmedName
      )
    );

    if (isNameTaken) {
      // Show a toast notification instead of an alert
      toast.error(`Table number "${trimmedName}" is already in use on one of your floors. Please choose a different number.`);
      return;
    }

    // Update local state first for immediate UI feedback
    setFloorPlan((prevPlan) => ({
      ...prevPlan,
      tables: prevPlan.tables.map((table) =>
        table.id === editingTableId ? { ...table, number: trimmedName } : table
      ),
    }));

    try {
      // Persist to backend API
      await updateTableInAPI(tableToUpdate, {
        tableNumber: trimmedName,
      });

      // Show success toast
      toast.success(`Table number has been updated to "${trimmedName}".`);
    } catch (error) {
      // Revert local state if API call fails
      setFloorPlan((prevPlan) => ({
        ...prevPlan,
        tables: prevPlan.tables.map((table) =>
          table.id === editingTableId ? { ...table, number: tableToUpdate.number } : table
        ),
      }));

      toast.error("Failed to update table name. Please try again.");
    }

    // Exit editing mode
    setEditingTableId(null);
    setEditingTableName("");
  };

  // Function to cancel editing
  const cancelEditingTableName = () => {
    setEditingTableId(null);
    setEditingTableName("");
  };

  // Handle keyboard events for the table name input
  const handleTableNameKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      saveTableName();
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEditingTableName();
    }
  };

  // Function to toggle reception area visibility
  const toggleReceptionArea = () => {
    setFloorPlan((prevPlan) => ({
      ...prevPlan,
      reception: {
        ...prevPlan.reception,
        isVisible: !prevPlan.reception.isVisible,
      },
    }));
  };

  // Function to move reception area
  const handleReceptionDragStart = (e: React.DragEvent) => {
    if (!containerRef.current) return;

    // Calculate offset from mouse position to reception corner
    const rect = e.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    });

    // Set drag image to be transparent
    const img = new Image();
    img.src =
      "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
    e.dataTransfer.setDragImage(img, 0, 0);

    // Set a special flag to indicate we're dragging the reception
    setIsDragging(true);
    setDraggedTable(null);
  };

  const handleReceptionDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isDragging || draggedTable !== null || !containerRef.current) return;

    const container = containerRef.current.getBoundingClientRect();

    // Calculate new position with offset
    let x = e.clientX - container.left - dragOffset.x;
    let y = e.clientY - container.top - dragOffset.y;

    // Snap to grid if enabled
    if (showGrid) {
      x = snapToGrid(x);
      y = snapToGrid(y);
    }

    // Ensure reception stays within bounds
    x = Math.max(
      0,
      Math.min(x, container.width - floorPlan.reception.size.width)
    );
    y = Math.max(
      0,
      Math.min(y, container.height - floorPlan.reception.size.height)
    );

    setFloorPlan((prev) => ({
      ...prev,
      reception: {
        ...prev.reception,
        position: { x, y },
      },
    }));
  };

  const handleReceptionDragEnd = () => {
    setIsDragging(false);
  };

  // Function to select a table
  const selectTable = (tableId: number) => {
    // Don't allow selection if we're resizing, dragging, or editing
    if (resizeState || isDragging || editingTableId !== null) return;

    // If in table view mode and onTableSelect is provided, navigate to register
    if (viewMode === "table" && onTableSelect && setActiveTab) {
      const selectedTable = floorPlan.tables.find(
        (table) => table.id === tableId
      );
      if (selectedTable) {
        onTableSelect(selectedTable.number);
        setActiveTab("Register");
        return;
      }
    }

    setSelectedTableId(tableId === selectedTableId ? null : tableId);
  };

  // Function to move selected table with arrow keys
  const moveSelectedTable = (
    direction: "up" | "down" | "left" | "right",
    amount: number = 10
  ) => {
    if (selectedTableId === null) return;

    setFloorPlan((prevPlan) => {
      const updatedTables = prevPlan.tables.map((table) => {
        if (table.id === selectedTableId) {
          let newX = table.position.x;
          let newY = table.position.y;

          // Calculate new position based on direction
          switch (direction) {
            case "up":
              newY = Math.max(0, newY - amount);
              break;
            case "down":
              newY = Math.min(
                prevPlan.dimensions.height - table.shape.size.height,
                newY + amount
              );
              break;
            case "left":
              newX = Math.max(0, newX - amount);
              break;
            case "right":
              newX = Math.min(
                prevPlan.dimensions.width - table.shape.size.width,
                newX + amount
              );
              break;
          }

          // Snap to grid if enabled
          if (showGrid) {
            newX = snapToGrid(newX);
            newY = snapToGrid(newY);
          }

          return {
            ...table,
            position: { x: newX, y: newY },
          };
        }
        return table;
      });

      return {
        ...prevPlan,
        tables: updatedTables,
      };
    });
  };

  // Handle keyboard events for table movement
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      // Skip if we're editing a table name or no table is selected
      if (editingTableId !== null || selectedTableId === null) return;

      // Determine movement amount (smaller with Shift key for fine adjustments)
      const moveAmount = e.shiftKey ? 1 : showGrid ? GRID_SIZE : 10;

      switch (e.key) {
        case "ArrowUp":
          e.preventDefault();
          moveSelectedTable("up", moveAmount);
          break;
        case "ArrowDown":
          e.preventDefault();
          moveSelectedTable("down", moveAmount);
          break;
        case "ArrowLeft":
          e.preventDefault();
          moveSelectedTable("left", moveAmount);
          break;
        case "ArrowRight":
          e.preventDefault();
          moveSelectedTable("right", moveAmount);
          break;
        case "Escape":
          // Deselect table when Escape is pressed
          setSelectedTableId(null);
          break;
      }
    },
    [selectedTableId, editingTableId, showGrid, moveSelectedTable]
  );

  // Set up keyboard event listeners
  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  // Function to toggle view mode
  const toggleViewMode = () => {
    // If switching to table view, deselect any selected table
    if (viewMode === "edit") {
      setSelectedTableId(null);
    }
    setViewMode(viewMode === "edit" ? "table" : "edit");
  };

  // Show loading state while data is being fetched
  if (floorsLoading || tablesLoading) {
    return (
      <div className="flex flex-col flex-1 items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-gray-100 mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Loading floor plans...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-1">
      {/* Floor Selection and Settings */}
      <div className="p-4 border-b dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex gap-2 items-center overflow-x-auto pb-2 max-w-[60%]">
            {/* Floor Buttons */}
            {floors.map((floor) => (
              <div key={floor.id} className="flex-shrink-0 relative">
                {editingFloorId === floor.id ? (
                  <div className="flex items-center gap-1">
                    <Input
                      ref={floorNameInputRef}
                      value={editingFloorName}
                      onChange={(e) => setEditingFloorName(e.target.value)}
                      onKeyDown={handleFloorNameKeyDown}
                      className="h-9 w-32 text-sm font-medium"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <div className="flex gap-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          saveFloorName();
                        }}
                        className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                      >
                        <Check className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          cancelEditingFloorName();
                        }}
                        className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    className={cn(activeFloorId === floor.id && "bg-blue-50 dark:bg-blue-900")}
                    onClick={() => setActiveFloorId(floor.id)}
                  >
                    <Building2 className="h-4 w-4 mr-2" />
                    {floor.name}
                  </Button>
                )}

                {/* Floor Options - Only in edit mode */}
                {viewMode === "edit" && activeFloorId === floor.id && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 absolute -right-3 -top-2 bg-white dark:bg-gray-800 rounded-full shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="start">
                      <DropdownMenuItem
                        onClick={() => startEditingFloorName(floor)}
                      >
                        <Pencil className="h-4 w-4 mr-2" />
                        Rename Floor
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => setFloorToDelete(floor.id)}
                        className="text-red-600 hover:text-red-700 focus:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        disabled={floors.length <= 1}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Floor
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            ))}

            {/* Add Floor Button - Only in edit mode */}
            {viewMode === "edit" && (
              <div className="flex-shrink-0">
                {isAddingFloor ? (
                  <div className="flex items-center gap-1">
                    <Input
                      value={newFloorName}
                      onChange={(e) => setNewFloorName(e.target.value)}
                      placeholder="Floor name"
                      className="h-9 w-32 text-sm font-medium"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addFloor();
                        } else if (e.key === "Escape") {
                          e.preventDefault();
                          setIsAddingFloor(false);
                          setNewFloorName("");
                        }
                      }}
                      autoFocus
                    />
                    <div className="flex gap-1">
                      <button
                        onClick={addFloor}
                        className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        disabled={!newFloorName.trim()}
                      >
                        <Check className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setIsAddingFloor(false);
                          setNewFloorName("");
                        }}
                        className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    onClick={() => setIsAddingFloor(true)}
                    className="border-dashed"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Floor
                  </Button>
                )}
              </div>
            )}

            {dimensions && (
              <span className="text-xs bg-blue-50 dark:bg-blue-900 px-2 py-1 rounded ml-2">
                {dimensions}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            {/* View Mode Toggle */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleViewMode}
                    className={cn(viewMode === "table" && "bg-blue-50 dark:bg-blue-900")}
                  >
                    {viewMode === "edit" ? (
                      <TableIcon className="h-4 w-4" />
                    ) : (
                      <Settings className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>
                    {viewMode === "edit"
                      ? "Switch to Table View"
                      : "Switch to Edit Mode"}
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Table View Settings Dropdown - Only visible in table view mode */}
            {viewMode === "table" && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    title="Table View Settings"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={() => setShowTableNumbers(!showTableNumbers)}
                    className="flex items-center justify-between"
                  >
                    <span>Show Table Numbers</span>
                    {showTableNumbers ? (
                      <Check className="h-4 w-4 ml-2" />
                    ) : null}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setShowTableStatus(!showTableStatus)}
                    className="flex items-center justify-between"
                  >
                    <span>Show Table Status</span>
                    {showTableStatus ? (
                      <Check className="h-4 w-4 ml-2" />
                    ) : null}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setShowTableSeats(!showTableSeats)}
                    className="flex items-center justify-between"
                  >
                    <span>Show Seat Count</span>
                    {showTableSeats ? <Check className="h-4 w-4 ml-2" /> : null}
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setShowTableOrders(!showTableOrders)}
                    className="flex items-center justify-between"
                  >
                    <span>Show Order Count</span>
                    {showTableOrders ? (
                      <Check className="h-4 w-4 ml-2" />
                    ) : null}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Only show these controls in edit mode */}
            {viewMode === "edit" && (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowGrid(!showGrid)}
                  className={cn(showGrid && "bg-blue-50 dark:bg-blue-900")}
                  title="Toggle Grid"
                >
                  <Grid2X2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={resetLayout}
                  title="Reset Layout"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={toggleReceptionArea}
                  className={cn(floorPlan.reception.isVisible && "bg-blue-50 dark:bg-blue-900")}
                  title={
                    floorPlan.reception.isVisible
                      ? "Hide Reception"
                      : "Show Reception"
                  }
                >
                  <Users className="h-4 w-4" />
                </Button>
                <FloorPlanSettings
                  floorPlan={floorPlan}
                  onUpdate={setFloorPlan}
                />

                {/* Add Table Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon" title="Add Table">
                      <PlusCircle className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => addTable("square")}>
                      <Square className="h-4 w-4 mr-2" />
                      Square Table
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => addTable("rectangle")}>
                      <RectangleHorizontal className="h-4 w-4 mr-2" />
                      Rectangular Table
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => addTable("circle")}>
                      <Circle className="h-4 w-4 mr-2" />
                      Circular Table
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Floor Plan View */}
      <div className="flex-1 p-4 overflow-auto">
        <div
          ref={containerRef}
          className={cn(
            "relative w-full h-[800px] bg-gray-100 dark:bg-gray-800 rounded-lg transition-all",
            showGrid && viewMode === "edit" && "bg-grid-pattern dark:bg-grid-pattern-dark"
          )}
          onDragOver={(e) => {
            if (viewMode === "edit") {
              handleDragOver(e);
              handleReceptionDragOver(e);
            }
          }}
          tabIndex={0} // Make the container focusable for keyboard events
        >
          {/* Reception Area - Only shown when toggled and in edit mode */}
          {floorPlan.reception.isVisible && viewMode === "edit" && (
            <div
              className="absolute bg-blue-100 dark:bg-blue-900 rounded-lg p-4 cursor-move hover:shadow-lg transition-all duration-150"
              style={{
                left: `${floorPlan.reception.position.x}px`,
                top: `${floorPlan.reception.position.y}px`,
                width: `${floorPlan.reception.size.width}px`,
                height: `${floorPlan.reception.size.height}px`,
              }}
              draggable={!resizeState}
              onDragStart={handleReceptionDragStart}
              onDragEnd={handleReceptionDragEnd}
            >
              <div className="text-center font-medium">Reception</div>
            </div>
          )}

          {/* Reception Area - Only shown when toggled and in table view mode */}
          {floorPlan.reception.isVisible && viewMode === "table" && (
            <div
              className="absolute bg-blue-100 dark:bg-blue-900 rounded-lg p-4 transition-all duration-150"
              style={{
                left: `${floorPlan.reception.position.x}px`,
                top: `${floorPlan.reception.position.y}px`,
                width: `${floorPlan.reception.size.width}px`,
                height: `${floorPlan.reception.size.height}px`,
              }}
            >
              <div className="text-center font-medium">Reception</div>
            </div>
          )}

          {/* Tables */}
          {floorPlan.tables.map((table) => (
            <div
              key={table.id}
              className={cn(
                "absolute transition-all duration-150 group",
                viewMode === "edit" && "cursor-move",
                viewMode === "table" && "cursor-pointer",
                isDragging &&
                  draggedTable?.id === table.id &&
                  viewMode === "edit"
                  ? "opacity-70 ring-2 ring-blue-500 shadow-xl scale-105 z-10"
                  : "hover:shadow-lg",
                resizeState?.tableId === table.id && "z-10",
                editingTableId === table.id && "z-20",
                selectedTableId === table.id &&
                  viewMode === "edit" &&
                  "ring-2 ring-blue-500 z-10"
              )}
              style={{
                left: `${table.position.x}px`,
                top: `${table.position.y}px`,
                width: `${table.shape.size.width}px`,
                height: `${table.shape.size.height}px`,
              }}
              draggable={
                viewMode === "edit" &&
                !resizeState &&
                editingTableId !== table.id
              }
              onDragStart={(e) =>
                viewMode === "edit" && handleDragStart(e, table)
              }
              onDragEnd={handleDragEnd}
              onClick={() => selectTable(table.id)}
            >
              <Card
                className={cn(
                  "w-full h-full p-2 relative",
                  table.shape.type === "circle" && "rounded-full",
                  table.status === "occupied" && "bg-gray-50 dark:bg-gray-700",
                  selectedTableId === table.id &&
                    viewMode === "edit" &&
                    "bg-blue-50 dark:bg-blue-900",
                  table.status === "available" &&
                    viewMode === "table" &&
                    "bg-green-50 dark:bg-green-900",
                  table.status === "occupied" &&
                    viewMode === "table" &&
                    "bg-red-50 dark:bg-red-900",
                  table.status === "reserved" &&
                    viewMode === "table" &&
                    "bg-yellow-50 dark:bg-yellow-900"
                )}
              >
                {/* Status Indicator - Always visible in edit mode, optional in table view */}
                {(viewMode === "edit" ||
                  (viewMode === "table" && showTableStatus)) && (
                  <div
                    className={`absolute top-2 right-2 w-3 h-3 rounded-full ${getStatusColor(
                      table.status
                    )}`}
                  />
                )}

                {/* Order Count Badge - Always visible in edit mode, optional in table view */}
                {table.orders &&
                  (viewMode === "edit" ||
                    (viewMode === "table" && showTableOrders)) && (
                    <div className="absolute top-2 right-6 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {table.orders}
                    </div>
                  )}

                {/* Delete Table Button - Only in edit mode */}
                {viewMode === "edit" && (
                  <button
                    className="absolute top-2 left-2 text-red-500 dark:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity hover:text-red-700 dark:hover:text-red-300"
                    onClick={(e) => {
                      e.stopPropagation();
                      setTableToDelete(table.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}

                <div className="flex flex-col items-center justify-center h-full gap-1">
                  {/* Table Name - Editable in edit mode, optional in table view */}
                  {editingTableId === table.id && viewMode === "edit" ? (
                    <div className="flex items-center gap-1">
                      <Input
                        ref={tableNameInputRef}
                        value={editingTableName}
                        onChange={(e) => setEditingTableName(e.target.value)}
                        onKeyDown={handleTableNameKeyDown}
                        className="h-7 w-20 text-center text-sm font-semibold"
                        onClick={(e) => e.stopPropagation()}
                      />
                      <div className="flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            saveTableName();
                          }}
                          className="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            cancelEditingTableName();
                          }}
                          className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ) : (
                    (viewMode === "edit" ||
                      (viewMode === "table" && showTableNumbers)) && (
                      <div className="flex items-center gap-1 relative">
                        <h3
                          className={cn(
                            "text-sm font-semibold",
                            viewMode === "table" && "text-lg"
                          )}
                        >
                          {viewMode === "table"
                            ? table.number
                            : `Table ${table.number}`}
                        </h3>
                        {viewMode === "edit" && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <button
                                  className="text-gray-400 dark:text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity hover:text-blue-600 dark:hover:text-blue-400"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    startEditingTableName(table);
                                  }}
                                >
                                  <Edit2 className="h-3 w-3" />
                                </button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit table name</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                    )
                  )}

                  {/* Seat Controls - Only in edit mode */}
                  {viewMode === "edit" && (
                    <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400 text-xs">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => adjustSeats(table.id, -1)}
                              disabled={table.shape.seats <= 2}
                            >
                              <Minus className="h-3 w-3" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Decrease seats</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>

                      <div className="flex items-center gap-1 min-w-[40px] justify-center">
                        <Users className="h-3 w-3" />
                        <span>{table.shape.seats}</span>
                      </div>

                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <button
                              className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity"
                              onClick={() => adjustSeats(table.id, 1)}
                              disabled={table.shape.seats >= 12}
                            >
                              <Plus className="h-3 w-3" />
                            </button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Increase seats</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  )}

                  {/* Seat Display - Only in table view mode */}
                  {viewMode === "table" && showTableSeats && (
                    <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                      <Users className="h-4 w-4" />
                      <span>{table.shape.seats}</span>
                    </div>
                  )}

                  {resizeState?.tableId === table.id && viewMode === "edit" && (
                    <div className="text-xs text-blue-600 dark:text-blue-400 font-medium mt-1 bg-blue-50 dark:bg-blue-900 px-2 py-0.5 rounded-full">
                      {Math.round(table.shape.size.width)} ×{" "}
                      {Math.round(table.shape.size.height)}
                    </div>
                  )}
                </div>

                {/* Resize Handles - Only in edit mode */}
                {viewMode === "edit" && (
                  <>
                    <div
                      className="absolute right-0 top-0 bottom-0 w-6 cursor-e-resize opacity-0 group-hover:opacity-100 hover:bg-blue-300/50 dark:hover:bg-blue-600/50"
                      onMouseDown={(e) => startResize(e, table.id, "e")}
                    />
                    <div
                      className="absolute bottom-0 left-0 right-0 h-6 cursor-s-resize opacity-0 group-hover:opacity-100 hover:bg-blue-300/50 dark:hover:bg-blue-600/50"
                      onMouseDown={(e) => startResize(e, table.id, "s")}
                    />
                    <div
                      className="absolute bottom-0 right-0 w-8 h-8 cursor-se-resize opacity-0 group-hover:opacity-100 hover:bg-blue-300/50 dark:hover:bg-blue-600/50"
                      onMouseDown={(e) => startResize(e, table.id, "se")}
                    />
                  </>
                )}
              </Card>
            </div>
          ))}

          {/* Selected Table Instructions - Only in edit mode */}
          {selectedTableId !== null && viewMode === "edit" && (
            <div className="absolute left-4 bottom-4 bg-white dark:bg-gray-800 p-2 rounded shadow-md text-xs">
              <p className="font-medium">
                Table{" "}
                {floorPlan.tables.find((t) => t.id === selectedTableId)?.number}{" "}
                selected
              </p>
              <p>Use arrow keys to move table</p>
              <p>Hold Shift for fine adjustments</p>
              <p>Press Esc to deselect</p>
            </div>
          )}

          {/* Floating Add Table Button - Only in edit mode */}
          {viewMode === "edit" && (
            <div className="absolute right-6 bottom-6">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="icon"
                    className="h-12 w-12 rounded-full shadow-lg"
                  >
                    <PlusCircle className="h-6 w-6" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => addTable("square")}>
                    <Square className="h-4 w-4 mr-2" />
                    Square Table
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => addTable("rectangle")}>
                    <RectangleHorizontal className="h-4 w-4 mr-2" />
                    Rectangular Table
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => addTable("circle")}>
                    <Circle className="h-4 w-4 mr-2" />
                    Circular Table
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}

          {/* View Mode Indicator */}
          <div className="absolute left-4 top-4 bg-white dark:bg-gray-800 px-3 py-1 rounded-full shadow-sm text-xs font-medium">
            {viewMode === "edit" ? "Edit Mode" : "Table View"}
          </div>
        </div>
      </div>

      {/* Delete Table Confirmation Dialog */}
      <AlertDialog
        open={tableToDelete !== null}
        onOpenChange={(open) => !open && setTableToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Table</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this table? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                tableToDelete !== null && deleteTable(tableToDelete)
              }
              className="bg-red-500 hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Floor Confirmation Dialog */}
      <AlertDialog
        open={floorToDelete !== null}
        onOpenChange={(open) => !open && setFloorToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Floor</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this floor? This action cannot be
              undone and will remove all tables on this floor.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                floorToDelete !== null && deleteFloor(floorToDelete)
              }
              className="bg-red-500 hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
