"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@radix-ui/react-icons";
import { SupplierTableData, SupplierListDto } from "@/types/supplier";
import { ApiStatus } from "@/types/common";
import { useBulkDeleteSuppliers } from "@/lib/suppliers/hooks";
import { toast } from "sonner";

interface DeleteSuppliersDialogProps {
  suppliers: SupplierTableData[] | SupplierListDto[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteSuppliersDialog({
  suppliers,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteSuppliersDialogProps) {
  const { toast } = useToast();
  const bulkDeleteMutation = useBulkDeleteSuppliers(isDemo);

  const isMultiple = suppliers.length > 1;

  const handleDelete = async () => {
    if (suppliers.length === 0) return;

    try {
      const supplierIds = suppliers.map((supplier) => supplier.id);
      const result = await bulkDeleteMutation.mutateAsync(supplierIds);

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast({
          title: "Success",
          description: result.message ||
            `${result.data.deletedCount} suppliers deleted successfully`,
        });
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to delete suppliers",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error deleting suppliers:", error);
      toast({
        title: "Error",
        description: "Failed to delete suppliers",
        variant: "destructive",
      });
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete{" "}
            {isMultiple ? `${suppliers.length} suppliers` : "the supplier"}{" "}
            {!isMultiple && (
              <span className="font-medium">{suppliers[0]?.displayName}</span>
            )}. This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={bulkDeleteMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {bulkDeleteMutation.isPending
              ? "Deleting..."
              : `Delete ${
                  isMultiple ? `${suppliers.length} Suppliers` : "Supplier"
                }`}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
