"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Image as ImageIcon,
  User,
  CreditCard,
  FileText,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { DatePicker } from "@/components/ui/date-picker";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { supplierFormSchema } from "@/lib/suppliers/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  SupplierTableData,
  SupplierStatus,
  UpdateSupplierDto,
  CreateSupplierDto,
} from "@/types/supplier";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FileUploader,
  type UploadedFile,
} from "@/components/shared/file-uploader";
import {
  useSupplier,
  useSupplierDisplayNameAvailability,
  useSupplierEmailAvailability,
  useCreateSupplier,
  useUpdateSupplier,
} from "@/lib/suppliers/queries";
import { useEffect } from "react";

interface SupplierSheetProps {
  supplier: SupplierTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (supplier?: SupplierTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof supplierFormSchema>;

export function SupplierSheet({
  supplier,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: SupplierSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [netDaysOption, setNetDaysOption] = React.useState<string>("30");

  // Values for availability checking
  const [checkDisplayName, setCheckDisplayName] = React.useState<string>("");
  const [checkEmail, setCheckEmail] = React.useState<string>("");

  // Fetch complete supplier data if updating
  const { data: fullSupplierResponse, isLoading: isLoadingSupplier } =
    useSupplier(supplier?.id || "", isDemo);
  const fullSupplier = fullSupplierResponse?.data;

  useEffect(() => {
    if (fullSupplierResponse) {
      console.log("fullSupplierResponse", fullSupplierResponse);
    }
  }, [fullSupplierResponse]);

  // Mutation hooks for create and update operations
  const createSupplierMutation = useCreateSupplier(isDemo);
  const updateSupplierMutation = useUpdateSupplier(isDemo);

  // Availability checks (only check if not updating the same supplier)
  const shouldCheckDisplayNameAvailability =
    checkDisplayName.length > 0 &&
    (!isUpdate ||
      (supplier &&
        checkDisplayName.toLowerCase() !== supplier.displayName.toLowerCase()));
  const shouldCheckEmailAvailability =
    checkEmail.length > 0 &&
    (!isUpdate ||
      (supplier && checkEmail.toLowerCase() !== supplier.email.toLowerCase()));

  const {
    data: displayNameAvailabilityResponse,
    isLoading: isCheckingDisplayName,
  } = useSupplierDisplayNameAvailability(checkDisplayName);
  const { data: emailAvailabilityResponse, isLoading: isCheckingEmail } =
    useSupplierEmailAvailability(checkEmail);

  const isDisplayNameAvailable = shouldCheckDisplayNameAvailability
    ? displayNameAvailabilityResponse?.data?.available ?? true
    : true;
  const isEmailAvailable = shouldCheckEmailAvailability
    ? emailAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(supplierFormSchema),
    defaultValues: {
      title: "",
      firstName: "",
      lastName: "",
      companyName: "",
      displayName: "",
      email: "",
      website: "",
      fax: "",
      phoneNumber: "",
      mobileNumber: "",
      openingBalance: "",
      openingBalanceDate: "",
      netDays: 0,
      creditLimit: "",
      taxNumber: "",
      notes: "",
      status: SupplierStatus.ACTIVE,
    },
  });

  // Get the selected image file for API calls (only if it's a real file, not the mock one for existing images)
  const selectedImage =
    uploadedFiles.length > 0 && !uploadedFiles[0].id.startsWith("existing-")
      ? uploadedFiles[0].file
      : null;

  React.useEffect(() => {
    if (supplier && fullSupplier) {
      // Use full supplier data for populating the form
      form.reset({
        title: fullSupplier.title || "",
        firstName: fullSupplier.firstName,
        lastName: fullSupplier.lastName,
        companyName: fullSupplier.companyName,
        displayName: fullSupplier.displayName,
        email: fullSupplier.email,
        website: fullSupplier.website || "",
        fax: fullSupplier.fax || "",
        phoneNumber: fullSupplier.phoneNumber,
        mobileNumber: fullSupplier.mobileNumber || "",
        openingBalance: fullSupplier.openingBalance,
        openingBalanceDate: fullSupplier.openingBalanceDate,
        netDays: fullSupplier.netDays,
        creditLimit: fullSupplier.creditLimit || "",
        taxNumber: fullSupplier.taxNumber || "",
        notes: fullSupplier.notes || "",
        status: fullSupplier.status,
      });

      // Set the net days option based on the value
      if (fullSupplier.netDays === 30) {
        setNetDaysOption("30");
      } else if (fullSupplier.netDays === 60) {
        setNetDaysOption("60");
      } else if (fullSupplier.netDays === 90) {
        setNetDaysOption("90");
      } else if (fullSupplier.netDays === 0) {
        setNetDaysOption("0");
      } else {
        setNetDaysOption("custom");
      }

      // Set existing image as uploaded file for preview
      if (fullSupplier.profileImage) {
        // Create a mock UploadedFile for existing image
        setUploadedFiles([
          {
            id: `existing-${fullSupplier.id}`,
            file: new File([], fullSupplier.displayName + "-image", {
              type: "image/jpeg",
            }),
            preview: fullSupplier.profileImage,
          },
        ]);
      } else {
        setUploadedFiles([]);
      }
    } else if (!supplier) {
      // Reset form for new supplier
      form.reset({
        title: "",
        firstName: "",
        lastName: "",
        companyName: "",
        displayName: "",
        email: "",
        website: "",
        fax: "",
        phoneNumber: "",
        mobileNumber: "",
        openingBalance: "",
        openingBalanceDate: "",
        netDays: 30,
        creditLimit: "",
        taxNumber: "",
        notes: "",
        status: SupplierStatus.ACTIVE,
      });
      setNetDaysOption("30");
      setUploadedFiles([]);
    }
  }, [supplier, fullSupplier]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
      setUploadedFiles([]);
      setNetDaysOption("30");
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Handle net days option change
  const handleNetDaysOptionChange = (value: string) => {
    setNetDaysOption(value);
    if (value === "custom") {
      // Don't update the form value, let user enter custom value
      return;
    }
    // Update form value based on selection
    const numValue = parseInt(value);
    form.setValue("netDays", numValue, { shouldValidate: true });
  };

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.firstName ||
    errors.lastName ||
    errors.companyName ||
    errors.displayName ||
    errors.status ||
    (shouldCheckDisplayNameAvailability && isDisplayNameAvailable === false)
  );

  const hasContactInfoErrors = !!(
    errors.email ||
    errors.phoneNumber ||
    errors.mobileNumber ||
    errors.website ||
    errors.fax ||
    (shouldCheckEmailAvailability && isEmailAvailable === false)
  );

  const hasFinancialInfoErrors = !!(
    errors.openingBalance ||
    errors.openingBalanceDate ||
    errors.netDays ||
    errors.creditLimit ||
    errors.taxNumber
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (
      shouldCheckDisplayNameAvailability &&
      isDisplayNameAvailable === false
    ) {
      toast.error("Display name is already taken. Please choose a different display name.");
      return;
    }

    if (shouldCheckEmailAvailability && isEmailAvailable === false) {
      toast.error("Email is already taken. Please choose a different email.");
      return;
    }

    setIsSubmitting(true);

    try {
      if (supplier) {
        // Update existing supplier
        const updateData: UpdateSupplierDto = {
          title: data.title || undefined,
          firstName: data.firstName,
          lastName: data.lastName,
          companyName: data.companyName,
          displayName: data.displayName,
          email: data.email,
          website: data.website || undefined,
          fax: data.fax || undefined,
          phoneNumber: data.phoneNumber,
          mobileNumber: data.mobileNumber || undefined,
          openingBalance:
            data.openingBalance && data.openingBalance.trim() !== ""
              ? data.openingBalance
              : undefined,
          openingBalanceDate:
            data.openingBalanceDate && data.openingBalanceDate.trim() !== ""
              ? data.openingBalanceDate
              : undefined,
          netDays: data.netDays,
          creditLimit: data.creditLimit || undefined,
          taxNumber: data.taxNumber || undefined,
          notes: data.notes || undefined,
          status: data.status,
        };

        const response = await updateSupplierMutation.mutateAsync({
          id: supplier.id,
          data: updateData,
          profileImage: selectedImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Supplier updated successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as SupplierTableData);
          return;
        }
        toast.error(response.message || "Failed to update supplier");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new supplier
        const createData: CreateSupplierDto = {
          title: data.title || undefined,
          firstName: data.firstName,
          lastName: data.lastName,
          companyName: data.companyName,
          displayName: data.displayName,
          email: data.email,
          website: data.website || undefined,
          fax: data.fax || undefined,
          phoneNumber: data.phoneNumber,
          mobileNumber: data.mobileNumber || undefined,
          openingBalance:
            data.openingBalance && data.openingBalance.trim() !== ""
              ? data.openingBalance
              : undefined,
          openingBalanceDate:
            data.openingBalanceDate && data.openingBalanceDate.trim() !== ""
              ? data.openingBalanceDate
              : undefined,
          netDays: data.netDays,
          creditLimit: data.creditLimit || undefined,
          taxNumber: data.taxNumber || undefined,
          notes: data.notes || undefined,
          status: data.status,
        };

        const response = await createSupplierMutation.mutateAsync({
          data: createData,
          profileImage: selectedImage || undefined,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Supplier created successfully");
          onOpenChange?.(false);
          onSuccess?.(response.data as SupplierTableData);
          return;
        }
        toast.error(response.message || "Failed to create supplier");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save supplier:", error);
      toast.error("Failed to save supplier");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-generate display name from first and last name
  React.useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (
        (name === "firstName" || name === "lastName") &&
        value.firstName &&
        value.lastName &&
        !form.getValues("displayName")
      ) {
        form.setValue("displayName", `${value.firstName} ${value.lastName}`);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Handle availability checks
  const handleCheckDisplayNameAvailability = (value: string) => {
    setCheckDisplayName(value);
  };

  const handleCheckEmailAvailability = (value: string) => {
    setCheckEmail(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Title</Label>
                <Input
                  {...form.register("title")}
                  name="title"
                  placeholder="Mr., Mrs., Dr., etc."
                  className={cn(errors.title && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.title && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.title.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <Label>First Name</Label>
                  <Input
                    {...form.register("firstName")}
                    name="firstName"
                    placeholder="Enter first name"
                    className={cn(errors.firstName && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.firstName && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Last Name</Label>
                  <Input
                    {...form.register("lastName")}
                    name="lastName"
                    placeholder="Enter last name"
                    className={cn(errors.lastName && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.lastName && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label>Company Name</Label>
                <Input
                  {...form.register("companyName")}
                  name="companyName"
                  placeholder="Enter company name"
                  className={cn(errors.companyName && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.companyName && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.companyName.message}
                  </p>
                )}
              </div>

              <AvailabilityInput
                name="displayName"
                label="Display Name"
                placeholder="Display name (auto-generated from first and last name)"
                value={form.watch("displayName") || ""}
                onChange={(value) =>
                  form.setValue("displayName", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckDisplayNameAvailability}
                isCheckingAvailability={isCheckingDisplayName}
                isAvailable={isDisplayNameAvailable}
                shouldCheck={shouldCheckDisplayNameAvailability}
                error={errors.displayName?.message}
                disabled={isSubmitting}
                fieldName="Display name"
              />

              <div>
                <Label>Status</Label>
                <Select
                  value={form.watch("status")}
                  onValueChange={(value) =>
                    form.setValue(
                      "status",
                      value as SupplierStatus.ACTIVE | SupplierStatus.INACTIVE,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.status && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={SupplierStatus.ACTIVE}>
                      Active
                    </SelectItem>
                    <SelectItem value={SupplierStatus.INACTIVE}>
                      Inactive
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "contact-info",
      title: "Contact Information",
      icon: <User className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasContactInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Contact Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <AvailabilityInput
                name="email"
                label="Email Address"
                placeholder="Enter email address"
                value={form.watch("email") || ""}
                onChange={(value) =>
                  form.setValue("email", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckEmailAvailability}
                isCheckingAvailability={isCheckingEmail}
                isAvailable={isEmailAvailable}
                shouldCheck={shouldCheckEmailAvailability}
                error={errors.email?.message}
                disabled={isSubmitting}
                type="email"
                fieldName="Email"
              />

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <Label>Phone Number</Label>
                  <Input
                    {...form.register("phoneNumber")}
                    name="phoneNumber"
                    placeholder="Enter phone number"
                    className={cn(errors.phoneNumber && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.phoneNumber && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.phoneNumber.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Mobile Number</Label>
                  <Input
                    {...form.register("mobileNumber")}
                    name="mobileNumber"
                    placeholder="Enter mobile number"
                    className={cn(errors.mobileNumber && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.mobileNumber && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.mobileNumber.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div>
                  <Label>Website</Label>
                  <Input
                    {...form.register("website")}
                    name="website"
                    placeholder="Enter website URL"
                    className={cn(errors.website && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.website && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.website.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label>Fax</Label>
                  <Input
                    {...form.register("fax")}
                    name="fax"
                    placeholder="Enter fax number"
                    className={cn(errors.fax && "border-destructive")}
                    disabled={isSubmitting}
                  />
                  {errors.fax && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.fax.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "financial-info",
      title: "Financial Information",
      icon: <CreditCard className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasFinancialInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Financial Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Opening Balance</Label>
                <Input
                  {...form.register("openingBalance")}
                  name="openingBalance"
                  placeholder="Enter opening balance"
                  className={cn(errors.openingBalance && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.openingBalance && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.openingBalance.message}
                  </p>
                )}
              </div>

              <DatePicker
                name="openingBalanceDate"
                label="Opening Balance Date"
                value={form.watch("openingBalanceDate") || undefined}
                onChange={(date) => {
                  form.setValue(
                    "openingBalanceDate",
                    date ? date.toISOString().split('T')[0] : "",
                    { shouldValidate: true }
                  );
                }}
                placeholder="Select opening balance date"
                disabled={isSubmitting}
                error={errors.openingBalanceDate?.message}
              />

              <div>
                <Label>Net Days</Label>
                <Select
                  value={netDaysOption}
                  onValueChange={handleNetDaysOptionChange}
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.netDays && "border-destructive")}
                  >
                    <SelectValue placeholder="Select payment terms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Due on Receipt</SelectItem>
                    <SelectItem value="30">Net 30</SelectItem>
                    <SelectItem value="60">Net 60</SelectItem>
                    <SelectItem value="90">Net 90</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
                {netDaysOption === "custom" && (
                  <Input
                    {...form.register("netDays", { valueAsNumber: true })}
                    name="netDays"
                    type="number"
                    min="0"
                    placeholder="Enter custom net days"
                    className={cn(
                      errors.netDays && "border-destructive",
                      "mt-2"
                    )}
                    disabled={isSubmitting}
                  />
                )}
                {errors.netDays && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.netDays.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Number of days for payment terms
                </p>
              </div>

              <div>
                <Label>Credit Limit</Label>
                <Input
                  {...form.register("creditLimit")}
                  name="creditLimit"
                  placeholder="Enter credit limit"
                  className={cn(errors.creditLimit && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.creditLimit && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.creditLimit.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional credit limit for this supplier
                </p>
              </div>

              <div>
                <Label>Tax Number</Label>
                <Input
                  {...form.register("taxNumber")}
                  name="taxNumber"
                  placeholder="Enter tax registration number"
                  className={cn(errors.taxNumber && "border-destructive")}
                  disabled={isSubmitting}
                />
                {errors.taxNumber && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.taxNumber.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional tax registration number
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "notes",
      title: "Notes",
      icon: <FileText className="h-5 w-5 text-muted-foreground" />,
      hasErrors: !!errors.notes,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Notes</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <Textarea
                {...form.register("notes")}
                name="notes"
                placeholder="Add notes about this supplier..."
                className={cn(
                  "min-h-[120px]",
                  errors.notes && "border-destructive"
                )}
                disabled={isSubmitting}
                rows={4}
              />
              {errors.notes && (
                <p className="text-sm text-destructive mt-1">
                  {errors.notes.message}
                </p>
              )}
              <p className="text-sm text-muted-foreground mt-1">
                Optional notes about this supplier
              </p>
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "supplier-image",
      title: "Supplier Image",
      icon: <ImageIcon className="h-5 w-5 text-muted-foreground" />,
      hasErrors: false,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Supplier Image</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <FileUploader
              files={uploadedFiles}
              onFilesChange={setUploadedFiles}
              fileType="image"
              maxFiles={1}
              maxSize={5 * 1024 * 1024} // 5MB
              multiple={false}
              label="Profile Image"
              description="Upload a profile image for this supplier. Maximum file size is 5MB."
              placeholder="Click to upload or drag and drop"
              disabled={isSubmitting}
              showPreview={true}
              previewSize="md"
              onError={(error) => {
                toast.error(error);
              }}
            />
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<SupplierTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={supplier}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Supplier"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingSupplier}
    />
  );
}
