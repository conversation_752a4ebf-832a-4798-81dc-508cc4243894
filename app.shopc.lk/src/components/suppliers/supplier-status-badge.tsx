"use client";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SupplierStatus } from "@/types/supplier";
import * as demoApi from "@/lib/suppliers/demo";
import * as api from "@/lib/suppliers/api";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { Loader2, Check } from "lucide-react";
import { useState } from "react";

interface SupplierStatusBadgeProps {
  supplierId: string;
  status: SupplierStatus;
  isDemo?: boolean;
  disabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (supplierId: string, newStatus: SupplierStatus) => void;
}

export function SupplierStatusBadge({
  supplierId,
  status,
  isDemo = false,
  disabled = false,
  onRefresh,
  onStatusUpdate,
}: SupplierStatusBadgeProps) {
  // Removed useToast hook - using Sonner directly
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (newStatus: SupplierStatus) => {
    // Don't make API call if status is already the same
    if (newStatus === status) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    setIsUpdating(true);

    try {
      const result = isDemo
        ? await demoApi.updateDemoSupplierApi(supplierId, {
            status: newStatus,
          })
        : await api.updateSupplierApi(supplierId, {
            status: newStatus,
          });

      if (result.status === ApiStatus.SUCCESS) {
        toast.success("Supplier status updated", {
          description: `Supplier status updated to ${getStatusLabel(newStatus)}`,
        });
        // Use the new status update handler if available
        if (onStatusUpdate) {
          onStatusUpdate(supplierId, newStatus);
        } else {
          // Fallback to onRefresh for backward compatibility
          onRefresh?.();
        }
      } else {
        toast.error("Failed to update status", {
          description: result.message || "Failed to update status",
        });
      }
    } catch (error) {
      console.error("Error updating supplier status:", error);
      toast.error("Failed to update supplier status", {
        description: "Failed to update status. Please try again.",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusLabel = (status: SupplierStatus): string => {
    switch (status) {
      case SupplierStatus.ACTIVE:
        return "Active";
      case SupplierStatus.INACTIVE:
        return "Inactive";
      case SupplierStatus.PENDING_APPROVAL:
        return "Pending Approval";
      case SupplierStatus.SUSPENDED:
        return "Suspended";
      case SupplierStatus.BLOCKED:
        return "Blocked";
      case SupplierStatus.TERMINATED:
        return "Terminated";
      default:
        return "Unknown";
    }
  };

  const getStatusVariant = (status: SupplierStatus) => {
    switch (status) {
      case SupplierStatus.ACTIVE:
        return "default";
      case SupplierStatus.PENDING_APPROVAL:
        return "secondary";
      case SupplierStatus.SUSPENDED:
      case SupplierStatus.BLOCKED:
      case SupplierStatus.TERMINATED:
        return "destructive";
      default:
        return "outline";
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Badge
          variant={getStatusVariant(status)}
          className={`${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer hover:opacity-80"
          } transition-opacity`}
        >
          {isUpdating ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          ) : (
            <span>{getStatusLabel(status)}</span>
          )}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {Object.values(SupplierStatus).map((statusOption) => (
          <DropdownMenuItem
            key={statusOption}
            onClick={() => handleStatusUpdate(statusOption)}
            className="flex items-center gap-2"
          >
            {status === statusOption && <Check className="h-4 w-4" />}
            <span>{getStatusLabel(statusOption)}</span>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
