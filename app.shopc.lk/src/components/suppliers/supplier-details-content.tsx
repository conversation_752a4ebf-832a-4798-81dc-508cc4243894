"use client";

import * as React from "react";
import {
  Info,
  User,
  Building2,
  Mail,
  Phone,
  Globe,
  Calendar,
  DollarSign,
  CreditCard,
  Clock,
  FileText,
  MapPin,
  Printer,
  Download,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useSupplierData } from "@/lib/suppliers/hooks";
import { SupplierListDto, SupplierStatus } from "@/types/supplier";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { useToast } from "@/hooks/use-toast";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface SupplierDetailsContentProps {
  supplier: SupplierListDto;
  isDemo?: boolean;
}

export function SupplierDetailsContent({
  supplier,
  isDemo = false,
}: SupplierDetailsContentProps) {
  const { toast } = useToast();
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch full supplier details
  const {
    data: fullSupplier,
    isLoading,
    error,
  } = useSupplierData(supplier.id, isDemo);

  const handlePrint = () => {
    if (printRef.current) {
      const printWindow = window.open("", "_blank");
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>Supplier Details - ${supplier.displayName}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px; }
                .section { margin-bottom: 20px; }
                .label { font-weight: bold; }
                .badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
                .active { background-color: #22c55e; color: white; }
                .inactive { background-color: #6b7280; color: white; }
              </style>
            </head>
            <body>
              ${printRef.current.innerHTML}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
      }
    }
  };

  const handleDownloadPDF = async () => {
    if (printRef.current) {
      try {
        const canvas = await html2canvas(printRef.current);
        const imgData = canvas.toDataURL("image/png");
        const pdf = new jsPDF();
        const imgWidth = 210;
        const pageHeight = 295;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let heightLeft = imgHeight;

        let position = 0;

        pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;

        while (heightLeft >= 0) {
          position = heightLeft - imgHeight;
          pdf.addPage();
          pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
          heightLeft -= pageHeight;
        }

        pdf.save(`supplier-details-${supplier.displayName}.pdf`);
        toast({
          title: "Success",
          description: "PDF downloaded successfully"
        });
      } catch (error) {
        console.error("Error generating PDF:", error);
        toast({
          title: "Error",
          description: "Failed to generate PDF",
          variant: "destructive"
        });
      }
    }
  };

  const getStatusColor = (status: SupplierStatus) => {
    switch (status) {
      case SupplierStatus.ACTIVE:
        return "bg-green-100 text-green-800";
      case SupplierStatus.INACTIVE:
        return "bg-gray-100 text-gray-800";
      case SupplierStatus.PENDING_APPROVAL:
        return "bg-yellow-100 text-yellow-800";
      case SupplierStatus.SUSPENDED:
        return "bg-orange-100 text-orange-800";
      case SupplierStatus.BLOCKED:
      case SupplierStatus.TERMINATED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusLabel = (status: SupplierStatus) => {
    switch (status) {
      case SupplierStatus.ACTIVE:
        return "Active";
      case SupplierStatus.INACTIVE:
        return "Inactive";
      case SupplierStatus.PENDING_APPROVAL:
        return "Pending Approval";
      case SupplierStatus.SUSPENDED:
        return "Suspended";
      case SupplierStatus.BLOCKED:
        return "Blocked";
      case SupplierStatus.TERMINATED:
        return "Terminated";
      default:
        return "Unknown";
    }
  };

  if (isLoading) {
    return <LoadingIndicator />;
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Failed to load supplier details</p>
      </div>
    );
  }

  const supplierData = fullSupplier?.data || supplier;

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex gap-2 justify-end">
        <Button variant="outline" size="sm" onClick={handlePrint}>
          <Printer className="h-4 w-4 mr-2" />
          Print
        </Button>
        <Button variant="outline" size="sm" onClick={handleDownloadPDF}>
          <Download className="h-4 w-4 mr-2" />
          Download PDF
        </Button>
      </div>

      <ScrollArea className="h-[calc(100vh-200px)]">
        <div ref={printRef} className="space-y-6 p-4">
          {/* Header */}
          <div className="header">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold">
                  {supplierData.displayName}
                </h2>
                <p className="text-muted-foreground">
                  {supplierData.companyName}
                </p>
              </div>
              <Badge
                className={cn("text-sm", getStatusColor(supplierData.status))}
              >
                {getStatusLabel(supplierData.status)}
              </Badge>
            </div>
          </div>

          {/* Basic Information */}
          <div className="section">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Info className="h-5 w-5" />
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Name:</span>
                  <span>{`${supplierData.firstName} ${supplierData.lastName}`}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Company:</span>
                  <span>{supplierData.companyName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Email:</span>
                  <span>{supplierData.email}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Phone:</span>
                  <span>{supplierData.phoneNumber}</span>
                </div>
                {supplierData.mobileNumber && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="label">Mobile:</span>
                    <span>{supplierData.mobileNumber}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="section">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Financial Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Opening Balance:</span>
                  <span>${supplierData.openingBalance}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Opening Balance Date:</span>
                  <span>{supplierData.openingBalanceDate}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="label">Net Days:</span>
                  <span>{supplierData.netDays} days</span>
                </div>
                {supplierData.creditLimit && (
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span className="label">Credit Limit:</span>
                    <span>${supplierData.creditLimit}</span>
                  </div>
                )}
                {supplierData.taxNumber && (
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="label">Tax Number:</span>
                    <span>{supplierData.taxNumber}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          {supplierData.notes && (
            <div className="section">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Notes
              </h3>
              <p className="text-sm text-muted-foreground bg-gray-50 p-3 rounded">
                {supplierData.notes}
              </p>
            </div>
          )}

          {/* Timestamps - Only show if full supplier data is available */}
          {fullSupplier?.data && "createdAt" in fullSupplier.data && (
            <div className="section">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Timestamps
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="label">Created:</span>
                    <span>
                      {format(
                        new Date((fullSupplier.data as any).createdAt),
                        "PPP"
                      )}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="label">Updated:</span>
                    <span>
                      {format(
                        new Date((fullSupplier.data as any).updatedAt),
                        "PPP"
                      )}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
