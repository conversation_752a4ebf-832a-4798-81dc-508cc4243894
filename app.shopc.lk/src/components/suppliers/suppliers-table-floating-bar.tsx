"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, CheckCircle2, XCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type SupplierListDto, SupplierStatus } from "@/types/supplier";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteSuppliers,
  useBulkUpdateSupplierStatus,
} from "@/lib/suppliers/hooks";
import { ApiStatus } from "@/types/common";

interface SuppliersTableFloatingBarProps {
  table: Table<SupplierListDto>;
  onRefresh?: (supplierIds?: string[]) => void;
  isDemo?: boolean;
}

export function SuppliersTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: SuppliersTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "activate" | "deactivate" | "suspend" | "terminate"
  >();

  const bulkDeleteMutation = useBulkDeleteSuppliers(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateSupplierStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedSuppliers = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedSuppliers.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedSuppliers.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: SupplierStatus) => {
      if (selectionCount === 0) return;

      setAction(status === SupplierStatus.ACTIVE ? "activate" : "deactivate");

      try {
        const supplierIds = selectedSuppliers.map((supplier) => supplier.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          supplierIds,
          status,
        });

        if (result.status === ApiStatus.SUCCESS && result.data) {
          toast({
            title: "Success",
            description: result.data.message ||
              `Successfully ${
                status === SupplierStatus.ACTIVE ? "activated" : "deactivated"
              } ${result.data.updated} supplier${result.data.updated > 1 ? "s" : ""}`,
          });
          table.toggleAllRowsSelected(false);
          onRefresh?.(supplierIds);
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to update suppliers",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error updating suppliers:", error);
        toast({
          title: "Error",
          description: "Failed to update suppliers",
          variant: "destructive",
        });
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedSuppliers,
      selectionCount,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No suppliers selected" };

    try {
      const supplierIds = selectedSuppliers.map((supplier) => supplier.id);
      const result = await bulkDeleteMutation.mutateAsync(supplierIds);

      if (result.status === ApiStatus.SUCCESS) {
        toast({
          title: "Success",
          description: `Successfully deleted ${selectionCount} supplier${
            selectionCount > 1 ? "s" : ""
          }`
        });
        return { success: true };
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to delete suppliers",
          variant: "destructive"
        });
        return { error: result.message || "Failed to delete suppliers" };
      }
    } catch (error) {
      console.error("Error deleting suppliers:", error);
      toast({
        title: "Error",
        description: "Failed to delete suppliers",
        variant: "destructive"
      });
      return { error: "Failed to delete suppliers" };
    }
  }, [selectedSuppliers, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(SupplierStatus.ACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(SupplierStatus.INACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={bulkDeleteMutation.isPending || selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<SupplierListDto>
        table={table}
        title="Suppliers"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} supplier${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass supplier IDs since they're deleted
        }}
      />
    </>
  );
}
