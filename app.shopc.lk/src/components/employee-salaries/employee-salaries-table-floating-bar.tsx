"use client";

import * as React from "react";
import { type Table } from "@tanstack/react-table";
import { Trash2, Check<PERSON><PERSON>cle2, <PERSON><PERSON><PERSON><PERSON>, Clock, Ban } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { type EmployeeSalaryTableData, EmployeeSalaryStatus } from "@/types/employee-salary";
import { BaseTableFloatingBar } from "@/components/shared/base-table-floating-bar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DeleteDialog } from "@/components/shared/delete-dialog";
import {
  useBulkDeleteEmployeeSalaries,
  useBulkUpdateEmployeeSalaryStatus,
} from "@/lib/employee-salaries/hooks";
import { ApiStatus } from "@/types/common";

interface EmployeeSalariesTableFloatingBarProps {
  table: Table<EmployeeSalaryTableData>;
  onRefresh?: (employeeSalaryIds?: string[]) => void;
  isDemo?: boolean;
}

export function EmployeeSalariesTableFloatingBar({
  table,
  onRefresh,
  isDemo = false,
}: EmployeeSalariesTableFloatingBarProps) {
  const { toast } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
  const [action, setAction] = React.useState<
    "delete" | "activate" | "deactivate" | "pending" | "suspend"
  >();

  const bulkDeleteMutation = useBulkDeleteEmployeeSalaries(isDemo);
  const bulkUpdateStatusMutation = useBulkUpdateEmployeeSalaryStatus(isDemo);

  const isPending = bulkUpdateStatusMutation.isPending;

  // Get selected rows and force re-render on selection changes
  const [selectionCount, setSelectionCount] = React.useState(0);

  const selectedRows = table.getSelectedRowModel().rows;
  const selectedEmployeeSalaries = selectedRows
    .map((row) => row.original)
    .filter(Boolean);

  // Track selection changes and force component update
  React.useEffect(() => {
    const newCount = selectedEmployeeSalaries.length;
    if (newCount !== selectionCount) {
      setSelectionCount(newCount);
    }
  }, [selectedEmployeeSalaries.length, selectionCount]);

  const handleBulkStatusUpdate = React.useCallback(
    async (status: EmployeeSalaryStatus) => {
      if (selectionCount === 0) return;

      const actionMap = {
        [EmployeeSalaryStatus.ACTIVE]: "activate",
        [EmployeeSalaryStatus.INACTIVE]: "deactivate",
        [EmployeeSalaryStatus.PENDING]: "pending",
        [EmployeeSalaryStatus.SUSPENDED]: "suspend",
      };

      setAction(actionMap[status] as any);

      try {
        const employeeSalaryIds = selectedEmployeeSalaries.map((salary) => salary.id);
        const result = await bulkUpdateStatusMutation.mutateAsync({
          ids: employeeSalaryIds,
          status,
        });

        if (result.status === ApiStatus.SUCCESS) {
          const statusLabel = {
            [EmployeeSalaryStatus.ACTIVE]: "activated",
            [EmployeeSalaryStatus.INACTIVE]: "deactivated",
            [EmployeeSalaryStatus.PENDING]: "set to pending",
            [EmployeeSalaryStatus.SUSPENDED]: "suspended",
          };

          toast({
            title: "Success",
            description: `Successfully ${statusLabel[status]} ${selectionCount} employee salary${
              selectionCount > 1 ? "s" : ""
            }`
          });
          table.toggleAllRowsSelected(false);
          onRefresh?.(employeeSalaryIds);
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to update employee salaries",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error updating employee salaries:", error);
        toast({
          title: "Error",
          description: "Failed to update employee salaries",
          variant: "destructive"
        });
      } finally {
        setAction(undefined);
      }
    },
    [
      selectedEmployeeSalaries,
      selectionCount,
      bulkUpdateStatusMutation,
      table,
      onRefresh,
    ]
  );

  const handleBulkDelete = React.useCallback(async () => {
    if (selectionCount === 0) return { error: "No employee salaries selected" };

    try {
      const employeeSalaryIds = selectedEmployeeSalaries.map((salary) => salary.id);
      const result = await bulkDeleteMutation.mutateAsync(employeeSalaryIds);

      if (result.status === ApiStatus.SUCCESS) {
        return {}; // Success
      } else {
        return {
          error: result.message || "Failed to delete employee salaries",
        };
      }
    } catch (error: any) {
      console.error("Error deleting employee salaries:", error);
      return { error: error.message || "Failed to delete employee salaries" };
    }
  }, [selectedEmployeeSalaries, selectionCount, bulkDeleteMutation]);

  const actions = (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(EmployeeSalaryStatus.ACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <CheckCircle2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Activate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(EmployeeSalaryStatus.INACTIVE)}
            disabled={isPending || selectionCount === 0}
          >
            <XCircle className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Deactivate selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(EmployeeSalaryStatus.PENDING)}
            disabled={isPending || selectionCount === 0}
          >
            <Clock className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Set selected to pending</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => handleBulkStatusUpdate(EmployeeSalaryStatus.SUSPENDED)}
            disabled={isPending || selectionCount === 0}
          >
            <Ban className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Suspend selected</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="secondary"
            size="icon"
            className="size-7 border"
            onClick={() => setDeleteDialogOpen(true)}
            disabled={selectionCount === 0}
          >
            <Trash2 className="size-3.5" aria-hidden="true" />
          </Button>
        </TooltipTrigger>
        <TooltipContent className="border bg-accent font-semibold text-foreground dark:bg-zinc-900">
          <p>Delete selected</p>
        </TooltipContent>
      </Tooltip>
    </>
  );

  return (
    <>
      <BaseTableFloatingBar<EmployeeSalaryTableData>
        table={table}
        title="Employee Salaries"
        excludeColumns={["select", "actions"]}
        actions={actions}
      />

      <DeleteDialog
        key={selectionCount} // Force re-render when selection changes
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemName={`${selectionCount} employee salary${selectionCount > 1 ? "s" : ""}`}
        showTrigger={false}
        onDelete={handleBulkDelete}
        onSuccess={() => {
          table.toggleAllRowsSelected(false);
          onRefresh?.(); // For delete operations, we don't need to pass IDs since they're deleted
        }}
      />
    </>
  );
}
