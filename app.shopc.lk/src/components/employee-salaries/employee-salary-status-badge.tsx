"use client";

import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { EmployeeSalaryStatus } from "@/types/employee-salary";
import * as demoApi from "@/lib/employee-salaries/demo";
import * as api from "@/lib/employee-salaries/api";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import { Loader2, Check } from "lucide-react";
import { useState } from "react";

interface EmployeeSalaryStatusBadgeProps {
  employeeSalaryId: string;
  status: EmployeeSalaryStatus;
  isDemo?: boolean;
  disabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (employeeSalaryId: string, newStatus: EmployeeSalaryStatus) => void;
}

export function EmployeeSalaryStatusBadge({
  employeeSalaryId,
  status,
  isDemo = false,
  disabled = false,
  onRefresh,
  onStatusUpdate,
}: EmployeeSalaryStatusBadgeProps) {
  // Removed useToast hook - using Sonner directly
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (newStatus: EmployeeSalaryStatus) => {
    // Don't make API call if status is already the same
    if (newStatus === status) {
      setIsOpen(false);
      return;
    }

    setIsOpen(false);
    setIsUpdating(true);

    try {
      const result = isDemo
        ? await demoApi.updateDemoEmployeeSalaryApi(employeeSalaryId, {
            status: newStatus,
          })
        : await api.updateEmployeeSalaryApi(employeeSalaryId, {
            status: newStatus,
          });

      if (result.status === ApiStatus.SUCCESS) {
        toast.success("Employee salary status updated", {
          description: `Employee salary status updated to ${getStatusLabel(newStatus)}`
        });
        // Use the new status update handler if available
        if (onStatusUpdate) {
          onStatusUpdate(employeeSalaryId, newStatus);
        } else {
          // Fallback to onRefresh for backward compatibility
          onRefresh?.();
        }
      } else {
        toast.error("Failed to update status", {
          description: result.message || "Failed to update status",
        });
      }
    } catch (error) {
      toast.error("Failed to update employee salary status", {
        description: "Failed to update employee salary status. Please try again.",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusLabel = (status: EmployeeSalaryStatus): string => {
    switch (status) {
      case EmployeeSalaryStatus.ACTIVE:
        return "Active";
      case EmployeeSalaryStatus.INACTIVE:
        return "Inactive";
      case EmployeeSalaryStatus.PENDING:
        return "Pending";
      case EmployeeSalaryStatus.SUSPENDED:
        return "Suspended";
      default:
        return "Unknown";
    }
  };

  const getStatusVariant = (status: EmployeeSalaryStatus) => {
    switch (status) {
      case EmployeeSalaryStatus.ACTIVE:
        return "default";
      case EmployeeSalaryStatus.INACTIVE:
        return "outline";
      case EmployeeSalaryStatus.PENDING:
        return "secondary";
      case EmployeeSalaryStatus.SUSPENDED:
        return "destructive";
      default:
        return "outline";
    }
  };

  const statusOptions = [
    {
      value: EmployeeSalaryStatus.ACTIVE,
      label: "Active",
    },
    {
      value: EmployeeSalaryStatus.INACTIVE,
      label: "Inactive",
    },
    {
      value: EmployeeSalaryStatus.PENDING,
      label: "Pending",
    },
    {
      value: EmployeeSalaryStatus.SUSPENDED,
      label: "Suspended",
    },
  ];

  return (
    <DropdownMenu open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
      <DropdownMenuTrigger asChild disabled={disabled}>
        <Badge
          variant={getStatusVariant(status)}
          className={`${
            disabled
              ? "cursor-not-allowed opacity-50"
              : "cursor-pointer hover:opacity-80"
          } transition-opacity`}
        >
          {isUpdating ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-3 w-3 animate-spin" />
              <span>Updating...</span>
            </div>
          ) : (
            <span>{getStatusLabel(status)}</span>
          )}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[120px]">
        {statusOptions.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => handleStatusUpdate(option.value)}
            disabled={isUpdating || disabled}
            className="flex items-center justify-between"
          >
            <span>{option.label}</span>
            {status === option.value && <Check className="h-3 w-3" />}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
