"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Loader2, User, DollarSign, CreditCard } from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { createEmployeeSalarySchema, updateEmployeeSalarySchema } from "@/lib/employee-salaries/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  EmployeeSalaryTableData,
  EmployeeSalaryStatus,
  UpdateEmployeeSalaryDto,
} from "@/types/employee-salary";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useEmployeeSalaryData,
  useCreateEmployeeSalary,
  useUpdateEmployeeSalary,
} from "@/lib/employee-salaries/hooks";
import { useEffect } from "react";

interface EmployeeSalarySheetProps {
  employeeSalary: EmployeeSalaryTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (employeeSalary?: EmployeeSalaryTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof createEmployeeSalarySchema>;

export function EmployeeSalarySheet({
  employeeSalary,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: EmployeeSalarySheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Fetch complete employee salary data if updating
  const { data: fullEmployeeSalaryResponse, isLoading: isLoadingEmployeeSalary } =
    useEmployeeSalaryData(employeeSalary?.id || "", isDemo);
  const fullEmployeeSalary = fullEmployeeSalaryResponse?.data;

  const form = useForm<FormData>({
    resolver: zodResolver(isUpdate ? updateEmployeeSalarySchema : createEmployeeSalarySchema),
    defaultValues: {
      employeeId: "",
      basicSalary: 0,
      bankAccountId: "",
      status: EmployeeSalaryStatus.ACTIVE,
      allowances: [],
      deductions: [],
    },
  });

  const createMutation = useCreateEmployeeSalary(isDemo);
  const updateMutation = useUpdateEmployeeSalary(isDemo);

  // Reset form when employee salary changes or dialog opens/closes
  useEffect(() => {
    if (open && isUpdate && fullEmployeeSalary) {
      form.reset({
        employeeId: fullEmployeeSalary.employeeId,
        basicSalary: fullEmployeeSalary.basicSalary,
        bankAccountId: fullEmployeeSalary.bankAccountId,
        status: fullEmployeeSalary.status,
        allowances: fullEmployeeSalary.allowances || [],
        deductions: fullEmployeeSalary.deductions || [],
      });
    } else if (open && !isUpdate) {
      form.reset({
        employeeId: "",
        basicSalary: 0,
        bankAccountId: "",
        status: EmployeeSalaryStatus.ACTIVE,
        allowances: [],
        deductions: [],
      });
    }
  }, [open, isUpdate, fullEmployeeSalary, form]);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      let result;
      if (isUpdate && employeeSalary?.id) {
        const updateData: UpdateEmployeeSalaryDto = {
          employeeId: data.employeeId,
          basicSalary: data.basicSalary,
          bankAccountId: data.bankAccountId,
          status: data.status,
          allowances: data.allowances,
          deductions: data.deductions,
        };
        result = await updateMutation.mutateAsync({
          id: employeeSalary.id,
          data: updateData,
        });
      } else {
        result = await createMutation.mutateAsync(data);
      }

      if (result.status === ApiStatus.SUCCESS) {
        toast.success(isUpdate
            ? "Employee salary updated successfully"
            : "Employee salary created successfully");
        onSuccess?.(result.data);
        onOpenChange?.(false);
        form.reset();
      } else {
        toast.error(result.message || "Something went wrong");
      }
    } catch (error: any) {
      console.error("Error submitting form:", error);
      toast.error(error.message || "Something went wrong");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange?.(newOpen);
  };

  const accordionSections = [
    {
      id: "basic-information",
      title: "Basic Information",
      icon: <User className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="employeeId">Employee *</Label>
            <Select
              value={form.watch("employeeId")}
              onValueChange={(value) => form.setValue("employeeId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select employee" />
              </SelectTrigger>
              <SelectContent>
                {/* TODO: Add employee options from API */}
                <SelectItem value="employee-1">Employee 1</SelectItem>
                <SelectItem value="employee-2">Employee 2</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.employeeId && (
              <p className="text-sm text-red-500">
                {form.formState.errors.employeeId.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="basicSalary">Basic Salary *</Label>
            <Input
              id="basicSalary"
              type="number"
              step="0.01"
              placeholder="Enter basic salary"
              {...form.register("basicSalary", { valueAsNumber: true })}
            />
            {form.formState.errors.basicSalary && (
              <p className="text-sm text-red-500">
                {form.formState.errors.basicSalary.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={form.watch("status")}
              onValueChange={(value) => form.setValue("status", value as EmployeeSalaryStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={EmployeeSalaryStatus.ACTIVE}>Active</SelectItem>
                <SelectItem value={EmployeeSalaryStatus.INACTIVE}>Inactive</SelectItem>
                <SelectItem value={EmployeeSalaryStatus.PENDING}>Pending</SelectItem>
                <SelectItem value={EmployeeSalaryStatus.SUSPENDED}>Suspended</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.status && (
              <p className="text-sm text-red-500">
                {form.formState.errors.status.message}
              </p>
            )}
          </div>
        </div>
      ),
    },
    {
      id: "bank-information",
      title: "Bank Information",
      icon: <CreditCard className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bankAccountId">Bank Account *</Label>
            <Select
              value={form.watch("bankAccountId")}
              onValueChange={(value) => form.setValue("bankAccountId", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select bank account" />
              </SelectTrigger>
              <SelectContent>
                {/* TODO: Add bank account options from API */}
                <SelectItem value="account-1">Account 1</SelectItem>
                <SelectItem value="account-2">Account 2</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.bankAccountId && (
              <p className="text-sm text-red-500">
                {form.formState.errors.bankAccountId.message}
              </p>
            )}
          </div>
        </div>
      ),
    },
  ];

  return (
    <BaseSheet
      open={open}
      onOpenChange={handleOpenChange}
      title={isUpdate ? "Edit Employee Salary" : "Add Employee Salary"}
      description={
        isUpdate
          ? "Update the employee salary information below."
          : "Fill in the details to create a new employee salary."
      }
      accordionSections={accordionSections}
      onSubmit={form.handleSubmit(onSubmit)}
      isSubmitting={isSubmitting}
      submitText={isUpdate ? "Update Employee Salary" : "Create Employee Salary"}
      formRef={formRef}
      isLoading={isUpdate && isLoadingEmployeeSalary}
    />
  );
}
