"use client";

import * as React from "react";
import {
  Info,
  DollarSign,
  User,
  CreditCard,
  Plus,
  Minus,
  Printer,
  Download,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { cn, formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { useEmployeeSalaryData } from "@/lib/employee-salaries/hooks";
import {
  EmployeeSalaryTableData,
  EmployeeSalaryStatus,
} from "@/types/employee-salary";
import { LoadingIndicator } from "@/components/shared/loading-indicator";
import { toast } from "sonner";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

interface EmployeeSalaryDetailsContentProps {
  employeeSalary: EmployeeSalaryTableData;
  isDemo?: boolean;
}

export function EmployeeSalaryDetailsContent({
  employeeSalary,
  isDemo = false,
}: EmployeeSalaryDetailsContentProps) {
  const printRef = React.useRef<HTMLDivElement>(null);

  // Fetch complete employee salary data
  const { data: fullEmployeeSalaryResponse, isLoading: isLoadingEmployeeSalary } =
    useEmployeeSalaryData(employeeSalary.id, isDemo);
  const fullEmployeeSalary = fullEmployeeSalaryResponse?.data;

  // Print functionality
  const handlePrint = React.useCallback(() => {
    const printWindow = window.open("", "_blank");
    if (!printWindow || !printRef.current) return;

    const printContent = printRef.current.cloneNode(true) as HTMLElement;

    // Create a complete HTML document for printing
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Employee Salary Details - ${fullEmployeeSalary?.employeeDisplayName || "Employee"}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .print-header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
            .print-header h1 { margin: 0; font-size: 24px; color: #333; }
            .print-header p { margin: 5px 0 0 0; color: #666; }
            .section { margin-bottom: 25px; page-break-inside: avoid; }
            .section h3 { color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
            .field { margin-bottom: 10px; }
            .field label { font-weight: bold; color: #555; display: block; margin-bottom: 3px; font-size: 12px; }
            .field div { color: #333; }
            .badge { display: inline-block; padding: 2px 8px; border-radius: 4px; font-size: 11px; font-weight: bold; }
            .badge.active { background-color: #d4edda; color: #155724; }
            .badge.inactive { background-color: #fff3cd; color: #856404; }
            .badge.pending { background-color: #d1ecf1; color: #0c5460; }
            .badge.suspended { background-color: #f8d7da; color: #721c24; }
            @media print { 
              body { margin: 0; }
              .no-print { display: none !important; }
              .page-break { page-break-before: always; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Employee Salary Details</h1>
            <p>Generated on ${format(new Date(), "PPP 'at' p")}</p>
          </div>
          ${printContent.innerHTML}
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  }, [fullEmployeeSalary]);

  // PDF export functionality
  const handleExportPDF = React.useCallback(async () => {
    if (!printRef.current || !fullEmployeeSalary) return;

    try {
      toast.info("Generating PDF...");
      
      const canvas = await html2canvas(printRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF("p", "mm", "a4");
      
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = canvas.width;
      const imgHeight = canvas.height;
      const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
      const imgX = (pdfWidth - imgWidth * ratio) / 2;
      const imgY = 30;

      pdf.addImage(imgData, "PNG", imgX, imgY, imgWidth * ratio, imgHeight * ratio);
      pdf.save(`employee-salary-${fullEmployeeSalary.employeeDisplayName.replace(/\s+/g, '-').toLowerCase()}.pdf`);
      
      toast.success("PDF exported successfully");
    } catch (error) {
      toast.error("Failed to export PDF");
      console.error("PDF export error:", error);
    }
  }, [fullEmployeeSalary]);

  const getStatusColor = (status: EmployeeSalaryStatus) => {
    switch (status) {
      case EmployeeSalaryStatus.ACTIVE:
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case EmployeeSalaryStatus.INACTIVE:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case EmployeeSalaryStatus.PENDING:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case EmployeeSalaryStatus.SUSPENDED:
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const getStatusLabel = (status: EmployeeSalaryStatus): string => {
    switch (status) {
      case EmployeeSalaryStatus.ACTIVE:
        return "Active";
      case EmployeeSalaryStatus.INACTIVE:
        return "Inactive";
      case EmployeeSalaryStatus.PENDING:
        return "Pending";
      case EmployeeSalaryStatus.SUSPENDED:
        return "Suspended";
      default:
        return "Unknown";
    }
  };

  if (isLoadingEmployeeSalary) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingIndicator />
      </div>
    );
  }

  if (!fullEmployeeSalary) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Employee salary not found</p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full">
      <div className="space-y-4 p-4">
        {/* Action Buttons */}
        <div className="flex gap-2 justify-end no-print">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportPDF}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export PDF
          </Button>
        </div>

        {/* Content for printing */}
        <div ref={printRef} className="space-y-6">
          {/* Header */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold">
                {fullEmployeeSalary.employeeDisplayName}
              </h2>
              <Badge className={cn("text-xs", getStatusColor(fullEmployeeSalary.status))}>
                {getStatusLabel(fullEmployeeSalary.status)}
              </Badge>
            </div>
            {fullEmployeeSalary.employeeEmail && (
              <p className="text-sm text-muted-foreground">
                {fullEmployeeSalary.employeeEmail}
              </p>
            )}
          </div>

          <Accordion type="multiple" defaultValue={["basic", "bank", "allowances", "deductions", "metadata"]} className="w-full">
            {/* Basic Information */}
            <AccordionItem value="basic">
              <AccordionTrigger className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Salary Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="field">
                    <label>Basic Salary</label>
                    <div className="text-lg font-semibold">
                      {formatCurrency(fullEmployeeSalary.basicSalary)}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Bank Information */}
            <AccordionItem value="bank">
              <AccordionTrigger className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Bank Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="field">
                    <label>Account Holder</label>
                    <div>{fullEmployeeSalary.bankAccountHolderName}</div>
                  </div>
                  <div className="field">
                    <label>Bank Name</label>
                    <div>{fullEmployeeSalary.bankName}</div>
                  </div>
                  <div className="field">
                    <label>Account Number</label>
                    <div>****{fullEmployeeSalary.bankAccountNumber.slice(-4)}</div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Allowances */}
            {fullEmployeeSalary.allowances && fullEmployeeSalary.allowances.length > 0 && (
              <AccordionItem value="allowances">
                <AccordionTrigger className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Allowances ({fullEmployeeSalary.allowances.length})
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3">
                    {fullEmployeeSalary.allowances.map((allowance) => (
                      <div key={allowance.id} className="flex justify-between items-center p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{allowance.allowanceTypeName}</p>
                          {allowance.notes && (
                            <p className="text-sm text-muted-foreground">{allowance.notes}</p>
                          )}
                        </div>
                        <p className="font-semibold text-green-600">
                          +{formatCurrency(allowance.amount)}
                        </p>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Deductions */}
            {fullEmployeeSalary.deductions && fullEmployeeSalary.deductions.length > 0 && (
              <AccordionItem value="deductions">
                <AccordionTrigger className="flex items-center gap-2">
                  <Minus className="h-4 w-4" />
                  Deductions ({fullEmployeeSalary.deductions.length})
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3">
                    {fullEmployeeSalary.deductions.map((deduction) => (
                      <div key={deduction.id} className="flex justify-between items-center p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{deduction.deductionTypeName}</p>
                          {deduction.notes && (
                            <p className="text-sm text-muted-foreground">{deduction.notes}</p>
                          )}
                        </div>
                        <p className="font-semibold text-red-600">
                          -{formatCurrency(deduction.amount)}
                        </p>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>
            )}

            {/* Metadata */}
            <AccordionItem value="metadata">
              <AccordionTrigger className="flex items-center gap-2">
                <Info className="h-4 w-4" />
                Record Information
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="field">
                    <label>Created At</label>
                    <div>{format(new Date(fullEmployeeSalary.createdAt), "PPP 'at' p")}</div>
                  </div>
                  <div className="field">
                    <label>Updated At</label>
                    <div>{format(new Date(fullEmployeeSalary.updatedAt), "PPP 'at' p")}</div>
                  </div>
                  <div className="field">
                    <label>Created By</label>
                    <div>{fullEmployeeSalary.createdBy}</div>
                  </div>
                  {fullEmployeeSalary.updatedBy && (
                    <div className="field">
                      <label>Updated By</label>
                      <div>{fullEmployeeSalary.updatedBy}</div>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </ScrollArea>
  );
}
