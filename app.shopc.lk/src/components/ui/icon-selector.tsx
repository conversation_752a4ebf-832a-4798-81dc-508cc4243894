"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Icon } from "@/components/ui/icon";
import type { IconName } from "@/lib/icons/types";
import { ChevronDown, Search, X } from "lucide-react";

// Icon mapping with searchable names using your custom Icon system
interface IconItem {
  name: IconName;
  keywords: string[];
}

const ICON_MAP: IconItem[] = [
  // Navigation & Home
  { name: "home", keywords: ["home", "house", "main", "dashboard", "start"] },
  {
    name: "menu",
    keywords: ["menu", "hamburger", "navigation", "nav", "bars"],
  },

  // Actions & Controls
  { name: "search", keywords: ["search", "find", "look", "magnify", "glass"] },
  { name: "plus", keywords: ["plus", "add", "new", "create", "more"] },
  {
    name: "minus",
    keywords: ["minus", "remove", "delete", "subtract", "less"],
  },
  { name: "close", keywords: ["close", "x", "cancel", "exit", "dismiss"] },
  {
    name: "check",
    keywords: [
      "check",
      "done",
      "tick",
      "approve",
      "yes",
      "complete",
      "success",
    ],
  },
  {
    name: "x",
    keywords: ["x", "close", "cancel", "no", "remove", "error", "fail"],
  },

  // Arrows & Direction
  { name: "arrowUp", keywords: ["arrow", "up", "top", "increase", "north"] },
  {
    name: "arrowDown",
    keywords: ["arrow", "down", "bottom", "decrease", "south"],
  },
  {
    name: "arrowLeft",
    keywords: ["arrow", "left", "back", "previous", "west"],
  },
  {
    name: "arrowRight",
    keywords: ["arrow", "right", "forward", "next", "east"],
  },
  { name: "chevronUp", keywords: ["chevron", "up", "collapse", "less"] },
  { name: "chevronDown", keywords: ["chevron", "down", "expand", "more"] },
  { name: "chevronLeft", keywords: ["chevron", "left", "back", "previous"] },
  { name: "chevronRight", keywords: ["chevron", "right", "forward", "next"] },

  // Status & Alerts
  {
    name: "warning",
    keywords: ["warning", "alert", "caution", "danger", "triangle"],
  },
  {
    name: "info",
    keywords: ["info", "information", "help", "details", "about", "circle"],
  },
  {
    name: "bell",
    keywords: ["bell", "notification", "alert", "alarm", "ring"],
  },

  // Users & Account
  {
    name: "user",
    keywords: ["user", "person", "account", "profile", "individual"],
  },
  {
    name: "users",
    keywords: ["users", "people", "team", "group", "community"],
  },
  {
    name: "settings",
    keywords: ["settings", "config", "preferences", "options", "gear", "cog"],
  },

  // Time & Schedule
  {
    name: "calendar",
    keywords: ["calendar", "date", "schedule", "event", "day"],
  },
  { name: "clock", keywords: ["clock", "time", "schedule", "hour", "watch"] },

  // Priority & Importance
  {
    name: "star",
    keywords: ["star", "favorite", "bookmark", "important", "rate", "priority"],
  },
  { name: "heart", keywords: ["heart", "love", "like", "favorite"] },
  { name: "bookmark", keywords: ["bookmark", "save", "favorite", "mark"] },

  // Communication
  {
    name: "mail",
    keywords: ["mail", "email", "message", "letter", "envelope"],
  },
  {
    name: "message",
    keywords: ["message", "chat", "comment", "talk", "conversation"],
  },
  { name: "send", keywords: ["send", "submit", "dispatch", "deliver"] },
  { name: "phone", keywords: ["phone", "call", "telephone", "mobile"] },

  // File & Data Management
  { name: "file", keywords: ["file", "document", "paper", "page"] },
  { name: "folder", keywords: ["folder", "directory", "files", "browse"] },
  { name: "download", keywords: ["download", "save", "export", "down", "get"] },
  { name: "upload", keywords: ["upload", "import", "up", "send", "add"] },
  { name: "edit", keywords: ["edit", "modify", "change", "update", "pencil"] },
  { name: "trash", keywords: ["trash", "delete", "remove", "bin", "garbage"] },

  // Security
  {
    name: "shield",
    keywords: ["shield", "security", "protection", "safe", "guard"],
  },
  {
    name: "lock",
    keywords: ["lock", "secure", "private", "closed", "protected"],
  },
  { name: "unlock", keywords: ["unlock", "open", "access", "unlocked"] },
  { name: "key", keywords: ["key", "password", "access", "login", "secure"] },

  // Visibility
  { name: "eye", keywords: ["eye", "view", "see", "visible", "show"] },
  {
    name: "eyeOff",
    keywords: ["eye", "hide", "invisible", "hidden", "private"],
  },

  // Sharing & Connection
  { name: "share", keywords: ["share", "export", "send", "distribute"] },
  { name: "link", keywords: ["link", "chain", "connect", "url", "attachment"] },

  // Location & Navigation
  {
    name: "globe",
    keywords: ["globe", "world", "earth", "global", "internet"],
  },
  { name: "map", keywords: ["map", "location", "navigation", "directions"] },

  // Media
  {
    name: "image",
    keywords: ["image", "picture", "photo", "gallery", "media"],
  },
  { name: "video", keywords: ["video", "movie", "film", "media", "play"] },
  { name: "music", keywords: ["music", "audio", "song", "note", "sound"] },
  { name: "mic", keywords: ["mic", "microphone", "audio", "voice", "record"] },
  { name: "volume", keywords: ["volume", "sound", "audio", "speaker", "loud"] },

  // System & Connectivity
  {
    name: "wifi",
    keywords: ["wifi", "wireless", "internet", "network", "connection"],
  },
  {
    name: "bluetooth",
    keywords: ["bluetooth", "wireless", "connect", "device", "pair"],
  },
  { name: "battery", keywords: ["battery", "power", "energy", "charge"] },
  { name: "power", keywords: ["power", "on", "start", "enable", "active"] },

  // Weather
  { name: "sun", keywords: ["sun", "sunny", "bright", "day", "light"] },
  { name: "moon", keywords: ["moon", "night", "dark", "lunar"] },
  { name: "cloud", keywords: ["cloud", "cloudy", "weather", "sky"] },
  {
    name: "lightning",
    keywords: ["lightning", "bolt", "electric", "energy", "zap", "fast"],
  },

  // Development
  { name: "database", keywords: ["database", "data", "storage", "sql"] },
  { name: "server", keywords: ["server", "hosting", "computer", "system"] },
  { name: "terminal", keywords: ["terminal", "console", "command", "cli"] },
  { name: "code", keywords: ["code", "programming", "development", "script"] },
  { name: "git", keywords: ["git", "version", "control", "branch"] },

  // Social
  { name: "github", keywords: ["github", "git", "code", "repository"] },
  { name: "twitter", keywords: ["twitter", "social", "tweet", "bird"] },
  {
    name: "linkedin",
    keywords: ["linkedin", "professional", "network", "career"],
  },
  { name: "facebook", keywords: ["facebook", "social", "network", "friends"] },
  { name: "instagram", keywords: ["instagram", "photo", "social", "camera"] },
  { name: "youtube", keywords: ["youtube", "video", "play", "watch"] },
];

// Popular icons for work orders and priorities
const POPULAR_ICON_NAMES: IconName[] = [
  "warning",
  "info",
  "star",
  "clock",
  "check",
  "x",
  "bell",
  "user",
  "shield",
  "lightning",
  "settings",
  "calendar",
  "heart",
  "home",
];

interface IconSelectorProps {
  value?: string;
  onChange?: (iconName: string | undefined) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function IconSelector({
  value,
  onChange,
  placeholder = "Select an icon",
  className,
  disabled = false,
}: IconSelectorProps) {
  const [open, setOpen] = React.useState(false);
  const [searchQuery, setSearchQuery] = React.useState("");

  // Filter icons based on search query
  const filteredIcons = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return {
        popular: ICON_MAP.filter((item) =>
          POPULAR_ICON_NAMES.includes(item.name)
        ),
        all: ICON_MAP.filter((item) => !POPULAR_ICON_NAMES.includes(item.name)),
      };
    }

    const query = searchQuery.toLowerCase().trim();
    const filtered = ICON_MAP.filter((item) => {
      const nameMatch = item.name.toLowerCase().includes(query);
      const keywordMatch = item.keywords.some((keyword) =>
        keyword.includes(query)
      );
      return nameMatch || keywordMatch;
    });

    return {
      popular: filtered.filter((item) =>
        POPULAR_ICON_NAMES.includes(item.name)
      ),
      all: filtered.filter((item) => !POPULAR_ICON_NAMES.includes(item.name)),
    };
  }, [searchQuery]);

  // Get the selected icon
  const selectedIcon = React.useMemo(() => {
    return ICON_MAP.find((item) => item.name === value);
  }, [value]);

  const handleIconSelect = (iconName: IconName) => {
    onChange?.(iconName === value ? undefined : iconName);
    setOpen(false);
    setSearchQuery("");
  };

  const renderIconGroup = (iconList: IconItem[], title: string) => {
    if (iconList.length === 0) return null;

    return (
      <div className="mb-4">
        <div className="text-sm font-medium text-muted-foreground px-2 mb-2">
          {title}
        </div>
        <div className="grid grid-cols-8 gap-1 px-2">
          {iconList.map((item) => {
            return (
              <button
                key={item.name}
                onClick={() => handleIconSelect(item.name)}
                className={cn(
                  "flex items-center justify-center p-2 h-10 w-10 rounded cursor-pointer hover:bg-accent transition-colors",
                  value === item.name && "bg-accent border-2 border-primary"
                )}
                title={item.name.replace(/([A-Z])/g, " $1").trim()}
                type="button"
              >
                <Icon name={item.name} size={16} className="text-current" />
              </button>
            );
          })}
        </div>
      </div>
    );
  };

  const hasResults =
    filteredIcons.popular.length > 0 || filteredIcons.all.length > 0;

  return (
    <Popover
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
        if (!newOpen) {
          setSearchQuery("");
        }
      }}
    >
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "justify-between h-10 w-full",
            !value && "text-muted-foreground",
            className
          )}
          disabled={disabled}
          type="button"
        >
          <div className="flex items-center gap-2">
            {selectedIcon ? (
              <>
                <Icon
                  name={selectedIcon.name}
                  size={16}
                  className="text-current"
                />
                <span className="capitalize">
                  {selectedIcon.name.replace(/([A-Z])/g, " $1").trim()}
                </span>
              </>
            ) : (
              <span>{placeholder}</span>
            )}
          </div>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[360px] p-0" align="start">
        <div className="flex items-center border-b px-3 py-2">
          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
          <Input
            placeholder="Search icons..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-8 border-0 bg-transparent p-0 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-0"
          />
        </div>

        <ScrollArea className="h-[300px]">
          <div className="py-2">
            {/* Clear selection option */}
            {value && (
              <div className="px-2 mb-2">
                <button
                  onClick={() => handleIconSelect("" as IconName)}
                  className="flex items-center gap-2 w-full p-2 hover:bg-accent rounded text-sm text-muted-foreground"
                  type="button"
                >
                  <X className="h-4 w-4" />
                  Clear selection
                </button>
              </div>
            )}

            {/* No results message */}
            {!hasResults && searchQuery && (
              <div className="text-center py-6 text-sm text-muted-foreground">
                No icons found for "{searchQuery}"
              </div>
            )}

            {/* Icon groups */}
            {renderIconGroup(filteredIcons.popular, "Popular Icons")}
            {renderIconGroup(filteredIcons.all, "All Icons")}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
