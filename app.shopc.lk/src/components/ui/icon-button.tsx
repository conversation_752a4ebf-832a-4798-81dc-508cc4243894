import React from 'react';
import { Icon } from '@/components/ui/icon';
import type { IconName } from '@/lib/icons/types';

interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: IconName;
  size?: number;
  variant?: 'default' | 'ghost' | 'outline';
}

export const IconButton: React.FC<IconButtonProps> = ({ 
  icon, 
  size = 20,
  variant = 'default',
  className = '',
  ...props 
}) => {
  const variantClasses = {
    default: 'bg-blue-500 hover:bg-blue-600 text-white',
    ghost: 'hover:bg-gray-100 text-gray-700',
    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'
  };

  return (
    <button
      className={`
        p-2 rounded-lg transition-colors inline-flex items-center justify-center
        ${variantClasses[variant]}
        ${className}
      `.trim()}
      {...props}
    >
      <Icon name={icon} size={size} />
    </button>
  );
};