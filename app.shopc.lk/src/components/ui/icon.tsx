'use client';

import { useState, useEffect, memo, forwardRef } from 'react';
import type { IconProps, IconDefinition, IconName } from '@/lib/icons/types';
import { iconRegistry } from '@/lib/icons/registry';

const iconCache = new Map<IconName, IconDefinition>();

export const Icon = memo(forwardRef<SVGSVGElement, IconProps>(({ 
  name, 
  size = 24, 
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  fallback = null,
  onLoad,
  onError,
  ...props 
}, ref) => {
  const [icon, setIcon] = useState<IconDefinition | null>(() => iconCache.get(name) || null);
  const [loading, setLoading] = useState(!iconCache.has(name));
  const [error, setError] = useState(false);

  useEffect(() => {
    if (iconCache.has(name)) {
      return;
    }

    const loadIcon = async () => {
      setLoading(true);
      setError(false);
      
      try {
        await new Promise(resolve => setTimeout(resolve, 50));
        
        if (iconRegistry[name]) {
          const iconData = iconRegistry[name];
          iconCache.set(name, iconData);
          setIcon(iconData);
          onLoad?.(name);
        } else {
          throw new Error(`Icon "${name}" not found`);
        }
      } catch (err) {
        console.error(`Failed to load icon: ${name}`, err);
        setError(true);
        onError?.(err as Error);
      } finally {
        setLoading(false);
      }
    };

    loadIcon();
  }, [name, onLoad, onError]);

  if (loading) {
    return (
      <div 
        className={`inline-flex items-center justify-center ${className}`}
        style={{ width: size, height: size }}
      >
        <svg
          ref={ref}
          width={size}
          height={size}
          viewBox="0 0 24 24"
          className="animate-spin"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          {...props}
        >
          <circle 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            strokeOpacity="0.25"
          />
          <path 
            d="M12 2a10 10 0 0 1 10 10" 
            stroke="currentColor"
            strokeLinecap="round"
          />
        </svg>
      </div>
    );
  }

  if (error || !icon) {
    if (fallback) {
      return <>{fallback}</>;
    }
    return (
      <div 
        className={`inline-flex items-center justify-center ${className}`}
        style={{ width: size, height: size }}
        title={`Icon "${name}" not found`}
      >
        <svg
          ref={ref}
          width={size}
          height={size}
          viewBox="0 0 24 24"
          fill="none"
          stroke={color}
          strokeWidth={strokeWidth}
          {...props}
        >
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
      </div>
    );
  }

  return (
    <svg
      ref={ref}
      width={size}
      height={size}
      viewBox={icon.viewBox}
      fill="none"
      stroke={color}
      strokeWidth={strokeWidth}
      className={className}
      aria-hidden="true"
      {...props}
    >
      {icon.paths.map((path, index) => (
        <path
          key={index}
          {...path}
          stroke={path.stroke || color}
          strokeWidth={path.strokeWidth || strokeWidth}
          fill={path.fill || 'none'}
        />
      ))}
    </svg>
  );
}));

Icon.displayName = 'Icon';

export default Icon;

export const preloadIcons = (iconNames: IconName[]): void => {
  iconNames.forEach(name => {
    if (!iconCache.has(name) && iconRegistry[name]) {
      iconCache.set(name, iconRegistry[name]);
    }
  });
};