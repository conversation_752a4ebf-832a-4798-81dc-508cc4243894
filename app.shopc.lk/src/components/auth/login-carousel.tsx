"use client";

import { useState, useEffect } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import type { CarouselApi } from "@/components/ui/carousel";

const allLoginImages = [
  {
    src: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80",
    alt: "Business Dashboard",
    title: "Modern Business Solutions",
    description: "Streamline your business operations with our comprehensive platform",
  },
  {
    src: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&q=80",
    alt: "Analytics View",
    title: "Powerful Analytics",
    description: "Make data-driven decisions with our advanced analytics tools",
  },
  {
    src: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&q=80",
    alt: "Team Collaboration",
    title: "Team Collaboration",
    description: "Work seamlessly with your team across all your business processes",
  },
  {
    src: "https://images.unsplash.com/photo-1553877522-43269d4ea984?w=800&q=80",
    alt: "Office Meeting",
    title: "Strategic Planning",
    description: "Plan your business strategy with powerful collaboration tools",
  },
  {
    src: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&q=80",
    alt: "Data Analysis",
    title: "Smart Insights",
    description: "Transform raw data into actionable business insights",
  },
  {
    src: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&q=80",
    alt: "Digital Innovation",
    title: "Digital Transformation",
    description: "Embrace the future with cutting-edge digital solutions",
  },
  {
    src: "https://images.unsplash.com/photo-1552664730-d307ca884978?w=800&q=80",
    alt: "Team Success",
    title: "Achieve Excellence",
    description: "Empower your team to reach new heights of productivity",
  },
  {
    src: "https://images.unsplash.com/photo-1531482615713-2afd69097998?w=800&q=80",
    alt: "Business Growth",
    title: "Scale Your Business",
    description: "Grow your business with scalable enterprise solutions",
  },
  {
    src: "https://images.unsplash.com/photo-1542626991-cbc4e32524cc?w=800&q=80",
    alt: "Innovation Hub",
    title: "Innovation Driven",
    description: "Stay ahead with innovative business management tools",
  },
  {
    src: "https://images.unsplash.com/photo-1557804506-669a67965ba0?w=800&q=80",
    alt: "Professional Team",
    title: "Professional Excellence",
    description: "Build professional workflows that deliver results",
  },
];

// Function to get 4 random images
const getRandomImages = (images: typeof allLoginImages, count: number = 4) => {
  const shuffled = [...images].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export function LoginCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loginImages] = useState(() => getRandomImages(allLoginImages, 4));

  useEffect(() => {
    if (!api) return;

    setCurrentSlide(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrentSlide(api.selectedScrollSnap());
    });
  }, [api]);

  return (
    <div className="absolute inset-0 h-full w-full">
      <Carousel
        setApi={setApi}
        opts={{
          align: "start",
          loop: true,
        }}
        plugins={[
          Autoplay({
            delay: 4000,
          }),
        ]}
        className="h-full w-full"
      >
        <CarouselContent className="h-full">
          {loginImages.map((image, index) => (
            <CarouselItem key={index} className="h-full">
              <div className="relative h-full w-full min-h-screen overflow-hidden">
                <img
                  src={image.src}
                  alt={image.alt}
                  className="absolute inset-0 h-full w-full object-cover"
                  style={{ 
                    filter: "brightness(0.8)",
                    minHeight: "100vh"
                  }}
                  onLoad={() => console.log('Image loaded:', image.src)}
                  onError={(e) => {
                    console.error('Image failed:', image.src);
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <div className="absolute inset-0 bg-black/20" />
                <div className="absolute bottom-8 left-8 right-8 text-white z-10">
                  <h3 className="text-2xl font-bold mb-2">{image.title}</h3>
                  <p className="text-lg opacity-90">{image.description}</p>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="left-4" />
        <CarouselNext className="right-4" />
      </Carousel>

      {/* Dots indicator */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {loginImages.map((_, index) => (
          <div
            key={index}
            className={`w-2 h-2 rounded-full transition-colors ${
              index === currentSlide ? "bg-white" : "bg-white/50"
            }`}
          />
        ))}
      </div>
    </div>
  );
}
