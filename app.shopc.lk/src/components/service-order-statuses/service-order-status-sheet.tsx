"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Settings,
  Palette,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AvailabilityInput } from "@/components/ui/availability-input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { serviceOrderStatusFormSchema } from "@/lib/service-order-statuses/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  ServiceOrderStatusTableData,
  ServiceOrderStatusType,
  UpdateServiceOrderStatusDto,
} from "@/types/service-order-status";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useServiceOrderStatus,
  useServiceOrderStatusNameAvailability,
  useCreateServiceOrderStatus,
  useUpdateServiceOrderStatus,
} from "@/lib/service-order-statuses/hooks";
import { useEffect } from "react";

interface ServiceOrderStatusSheetProps {
  serviceOrderStatus: ServiceOrderStatusTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (serviceOrderStatus?: ServiceOrderStatusTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof serviceOrderStatusFormSchema>;

export function ServiceOrderStatusSheet({
  serviceOrderStatus,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: ServiceOrderStatusSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Values for availability checking
  const [checkStatusName, setCheckStatusName] = React.useState<string>("");

  // Fetch complete service order status data if updating
  const { data: fullServiceOrderStatusResponse, isLoading: isLoadingServiceOrderStatus } =
    useServiceOrderStatus(serviceOrderStatus?.id || "", isDemo);
  const fullServiceOrderStatus = fullServiceOrderStatusResponse?.data;

  useEffect(() => {
    if (fullServiceOrderStatusResponse) {
      console.log("fullServiceOrderStatusResponse", fullServiceOrderStatusResponse);
    }
  }, [fullServiceOrderStatusResponse]);

  // Mutation hooks for create and update operations
  const createServiceOrderStatusMutation = useCreateServiceOrderStatus(isDemo);
  const updateServiceOrderStatusMutation = useUpdateServiceOrderStatus(isDemo);

  // Availability checks (only check if not updating the same service order status)
  const shouldCheckStatusNameAvailability =
    checkStatusName.length > 0 &&
    (!isUpdate ||
      (serviceOrderStatus && checkStatusName.toLowerCase() !== serviceOrderStatus.statusName.toLowerCase()));

  const { data: statusNameAvailabilityResponse, isLoading: isCheckingStatusName } =
    useServiceOrderStatusNameAvailability(checkStatusName, isDemo);

  const isStatusNameAvailable = shouldCheckStatusNameAvailability
    ? statusNameAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(serviceOrderStatusFormSchema),
    defaultValues: {
      statusCode: "",
      statusName: "",
      description: "",
      colorCode: "",
      statusType: ServiceOrderStatusType.INITIAL,
      isActive: true,
      isDefault: false,
    },
  });

  React.useEffect(() => {
    if (serviceOrderStatus && fullServiceOrderStatus) {
      // Use full service order status data for populating the form
      form.reset({
        statusCode: fullServiceOrderStatus.statusCode,
        statusName: fullServiceOrderStatus.statusName,
        description: fullServiceOrderStatus.description || "",
        colorCode: fullServiceOrderStatus.colorCode || "",
        statusType: fullServiceOrderStatus.statusType,
        isActive: fullServiceOrderStatus.isActive,
        isDefault: fullServiceOrderStatus.isDefault,
      });
    } else if (!serviceOrderStatus) {
      // Reset form for new service order status
      form.reset({
        statusCode: "",
        statusName: "",
        description: "",
        colorCode: "",
        statusType: ServiceOrderStatusType.INITIAL,
        isActive: true,
        isDefault: false,
      });
    }
  }, [serviceOrderStatus, fullServiceOrderStatus]);

  // Reset form when sheet closes
  React.useEffect(() => {
    if (!open) {
      form.reset();
      setIsSubmitting(false);
    }
  }, [open]);

  const {
    formState: { errors },
  } = form;

  // Check if sections have errors
  const hasBasicInfoErrors = !!(
    errors.statusCode ||
    errors.statusName ||
    errors.statusType ||
    errors.isActive ||
    errors.isDefault ||
    (shouldCheckStatusNameAvailability && isStatusNameAvailable === false)
  );

  const hasDetailsErrors = !!(
    errors.description ||
    errors.colorCode
  );

  const handleSubmit = async () => {
    const data = form.getValues();

    // Check availability before submitting
    if (shouldCheckStatusNameAvailability && isStatusNameAvailable === false) {
      toast.error(
        "Service order status name is already taken. Please choose a different name."
      );
      return;
    }

    setIsSubmitting(true);

    try {
      if (serviceOrderStatus) {
        // Update existing service order status
        const updateData: UpdateServiceOrderStatusDto = {
          statusCode: data.statusCode || undefined,
          statusName: data.statusName,
          description: data.description || undefined,
          colorCode: data.colorCode || undefined,
          statusType: data.statusType,
          isActive: data.isActive,
          isDefault: data.isDefault,
        };

        const response = await updateServiceOrderStatusMutation.mutateAsync({
          id: serviceOrderStatus.id,
          data: updateData,
        });

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service order status updated successfully");
          onOpenChange?.(false);
          onSuccess?.(serviceOrderStatus);
          return;
        }
        toast.error(response.message || "Failed to update service order status");
        return; // Don't close the sheet if there was an error
      } else {
        // Create new service order status
        const createData = {
          statusCode: data.statusCode,
          statusName: data.statusName,
          description: data.description || undefined,
          colorCode: data.colorCode || undefined,
          statusType: data.statusType,
          isActive: data.isActive,
          isDefault: data.isDefault,
        };

        const response = await createServiceOrderStatusMutation.mutateAsync(createData);

        if (response.status === ApiStatus.SUCCESS && response.data) {
          toast.success("Service order status created successfully");
          onOpenChange?.(false);
          onSuccess?.();
          return;
        }
        toast.error(response.message || "Failed to create service order status");
        return; // Don't close the sheet if there was an error
      }
    } catch (error) {
      console.error("Failed to save service order status:", error);
      toast.error("Failed to save service order status");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle availability checks
  const handleCheckStatusNameAvailability = (value: string) => {
    setCheckStatusName(value);
  };

  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Info className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Status Code</Label>
                <Input
                  {...form.register("statusCode")}
                  name="statusCode"
                  placeholder="Enter status code (e.g., PENDING, IN_PROGRESS)"
                  className={cn(
                    errors.statusCode && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.statusCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.statusCode.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Unique identifier for the status
                </p>
              </div>

              <AvailabilityInput
                name="statusName"
                label="Status Name"
                placeholder="Enter status name (e.g., Pending Approval, In Progress)"
                value={form.watch("statusName") || ""}
                onChange={(value) =>
                  form.setValue("statusName", value, { shouldValidate: true })
                }
                onCheckAvailability={handleCheckStatusNameAvailability}
                isCheckingAvailability={isCheckingStatusName}
                isAvailable={isStatusNameAvailable}
                shouldCheck={shouldCheckStatusNameAvailability}
                error={errors.statusName?.message}
                disabled={isSubmitting}
                fieldName="Status name"
              />

              <div>
                <Label>Status Type</Label>
                <Select
                  value={form.watch("statusType")}
                  onValueChange={(value) =>
                    form.setValue(
                      "statusType",
                      value as ServiceOrderStatusType,
                      {
                        shouldValidate: true,
                      }
                    )
                  }
                  disabled={isSubmitting}
                >
                  <SelectTrigger
                    className={cn(errors.statusType && "border-destructive")}
                  >
                    <SelectValue placeholder="Select status type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={ServiceOrderStatusType.INITIAL}>
                      Initial
                    </SelectItem>
                    <SelectItem value={ServiceOrderStatusType.IN_PROGRESS}>
                      In Progress
                    </SelectItem>
                    <SelectItem value={ServiceOrderStatusType.COMPLETED}>
                      Completed
                    </SelectItem>
                    <SelectItem value={ServiceOrderStatusType.CANCELLED}>
                      Cancelled
                    </SelectItem>
                    <SelectItem value={ServiceOrderStatusType.ON_HOLD}>
                      On Hold
                    </SelectItem>
                    <SelectItem value={ServiceOrderStatusType.PENDING_APPROVAL}>
                      Pending Approval
                    </SelectItem>
                    <SelectItem value={ServiceOrderStatusType.REJECTED}>
                      Rejected
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.statusType && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.statusType.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose the type of status this represents
                </p>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Active Status</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable this status for use in service orders
                  </p>
                </div>
                <Switch
                  checked={form.watch("isActive")}
                  onCheckedChange={(checked) =>
                    form.setValue("isActive", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.isActive && (
                <p className="text-sm text-destructive mt-1">
                  {errors.isActive.message}
                </p>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Default Status</Label>
                  <p className="text-sm text-muted-foreground">
                    Set as the default status for new service orders
                  </p>
                </div>
                <Switch
                  checked={form.watch("isDefault")}
                  onCheckedChange={(checked) =>
                    form.setValue("isDefault", checked, {
                      shouldValidate: true,
                    })
                  }
                  disabled={isSubmitting}
                />
              </div>
              {errors.isDefault && (
                <p className="text-sm text-destructive mt-1">
                  {errors.isDefault.message}
                </p>
              )}
            </div>
          </AccordionContent>
        </>
      ),
    },
    {
      id: "details",
      title: "Additional Details",
      icon: <Palette className="h-5 w-5 text-muted-foreground" />,
      hasErrors: hasDetailsErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-secondary/80 rounded-t-lg transition-colors bg-secondary">
            <div className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Additional Details</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6 pt-2">
            <div className="space-y-4">
              <div>
                <Label>Description</Label>
                <Textarea
                  {...form.register("description")}
                  name="description"
                  placeholder="Enter status description"
                  className={cn(errors.description && "border-destructive")}
                  disabled={isSubmitting}
                  rows={3}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Optional description for the status
                </p>
              </div>

              <div>
                <Label>Color Code</Label>
                <Input
                  {...form.register("colorCode")}
                  name="colorCode"
                  type="color"
                  placeholder="#3B82F6"
                  className={cn(
                    "h-10 w-full cursor-pointer",
                    errors.colorCode && "border-destructive"
                  )}
                  disabled={isSubmitting}
                />
                {errors.colorCode && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.colorCode.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground mt-1">
                  Choose a color to represent this status
                </p>
              </div>
            </div>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<ServiceOrderStatusTableData, FormData>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={serviceOrderStatus}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Service Order Status"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={isLoadingServiceOrderStatus}
    />
  );
}