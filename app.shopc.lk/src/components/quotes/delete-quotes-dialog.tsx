"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { QuoteTableData } from "@/types/quotes";
import { Icon } from "@/components/ui/icon";
import { deleteQuoteAction } from "@/lib/quotes/actions";
import { toast } from "sonner";

interface DeleteQuotesDialogProps {
  quote: QuoteTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  showTrigger?: boolean;
  isDemo?: boolean;
}

export function DeleteQuotesDialog({
  quote,
  open,
  onOpenChange,
  onSuccess,
  showTrigger = true,
  isDemo = false,
}: DeleteQuotesDialogProps) {
  const [isLoading, setIsLoading] = React.useState<boolean>(false);

  async function handleDelete() {
    if (!quote) return;

    setIsLoading(true);
    try {
      await deleteQuoteAction(quote._id, isDemo);
      toast.success("Quote deleted successfully");
      if (onSuccess) {
        onSuccess();
      }
      if (onOpenChange) {
        onOpenChange(false);
      }
    } catch (error) {
      console.error("Error deleting quote:", error);
      toast.error("Failed to delete quote");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Icon name="trash" size={16} />
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the quote {quote?.quoteNumber} and
            remove it from the system.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isLoading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
