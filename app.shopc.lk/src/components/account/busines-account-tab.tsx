"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Building2,
  RefreshCw,
  Loader2,
  Globe,
  ChevronRight,
  X,
  AlertTriangle,
  CheckCircle,
  Mail,
  Check,
} from "lucide-react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Business account interface
interface BusinessAccount {
  id: string;
  name: string;
  role: string;
  logo?: string;
  isActive: boolean;
  isOwner: boolean; // Flag to identify if user owns this account
}

// Business invitation interface
interface BusinessInvitation {
  id: string;
  businessName: string;
  inviterName: string;
  role: string;
  logo?: string;
  date: string;
}

interface BusinessAccountsTabProps {
  isDemo?: boolean;
}

export default function BusinessAccountsTab({
  isDemo = false,
}: BusinessAccountsTabProps) {
  // Removed useToast hook - using Sonner directly
  const [businessAccounts, setBusinessAccounts] = useState<BusinessAccount[]>(
    []
  );
  const [businessInvitations, setBusinessInvitations] = useState<
    BusinessInvitation[]
  >([]);
  const [loadingAccounts, setLoadingAccounts] = useState(false);
  const [loadingInvitations, setLoadingInvitations] = useState(false);
  const [switchingAccount, setSwitchingAccount] = useState(false);
  const [removingAccount, setRemovingAccount] = useState(false);
  const [processingInvitation, setProcessingInvitation] = useState(false);
  const [accountToRemove, setAccountToRemove] =
    useState<BusinessAccount | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // For activation confirmation
  const [accountToActivate, setAccountToActivate] =
    useState<BusinessAccount | null>(null);
  const [activateDialogOpen, setActivateDialogOpen] = useState(false);

  // Fetch data when component mounts
  useEffect(() => {
    fetchBusinessAccounts();
    fetchBusinessInvitations();
  }, []);

  // Function to fetch business accounts from API
  const fetchBusinessAccounts = async () => {
    setLoadingAccounts(true);
    try {
      const response = await fetch("/api/user/business-accounts");
      if (!response.ok) {
        throw new Error("Failed to fetch business accounts");
      }
      const data = await response.json();
      setBusinessAccounts(data.businessAccounts);
    } catch (error) {
      console.error("Error fetching business accounts:", error);
      toast.error("Failed to load business accounts", {
        description: "Failed to load business accounts. Please try again.",
      });
    } finally {
      setLoadingAccounts(false);
    }
  };

  // Function to fetch business invitations from API
  const fetchBusinessInvitations = async () => {
    setLoadingInvitations(true);
    try {
      const response = await fetch("/api/user/business-invitations");
      if (!response.ok) {
        throw new Error("Failed to fetch business invitations");
      }
      const data = await response.json();
      setBusinessInvitations(data.invitations);
    } catch (error) {
      console.error("Error fetching business invitations:", error);
      toast.error("Failed to load business invitations", {
        description: "Failed to load business invitations. Please try again.",
      });
    } finally {
      setLoadingInvitations(false);
    }
  };

  // Function to handle invitation response (accept/reject)
  const handleInvitation = async (invitationId: string, accept: boolean) => {
    setProcessingInvitation(true);
    try {
      const response = await fetch(
        `/api/user/business-invitations/${invitationId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            accept,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to ${accept ? "accept" : "reject"} invitation`);
      }

      const data = await response.json();

      // If accepted, add the new business account to the list
      if (accept && data.businessAccount) {
        setBusinessAccounts((prev) => [...prev, data.businessAccount]);
      }

      // Remove the invitation from the list
      setBusinessInvitations((prev) =>
        prev.filter((invitation) => invitation.id !== invitationId)
      );

      if (accept) {
        toast.success("Invitation accepted", {
          description: "You now have access to this business account",
        });
      } else {
        toast.success("Invitation rejected", {
          description: "The invitation has been rejected",
        });
      }

      // Refresh accounts list if invitation was accepted
      if (accept) {
        fetchBusinessAccounts();
      }
    } catch (error) {
      console.error(
        `Error ${accept ? "accepting" : "rejecting"} invitation:`,
        error
      );
      toast.error(`Failed to ${accept ? "accept" : "reject"} invitation`, {
        description: `Failed to ${accept ? "accept" : "reject"} invitation. Please try again.",
      });
    } finally {
      setProcessingInvitation(false);
    }
  };

  // Function to show activate confirmation
  const confirmActivate = (account: BusinessAccount, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent onClick

    // If account is already active, do nothing
    if (account.isActive) return;

    setAccountToActivate(account);
    setActivateDialogOpen(true);
  };

  // Function to switch active business account
  const switchBusinessAccount = async () => {
    if (switchingAccount || !accountToActivate) return;

    setSwitchingAccount(true);
    try {
      const response = await fetch("/api/user/business-accounts", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          businessAccountId: accountToActivate.id,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to switch business account");
      }

      const data = await response.json();
      setBusinessAccounts(data.businessAccounts);

      toast.success("Business account activated", {
        description: `You're now using ${accountToActivate.name}`,
      });
    } catch (error) {
      console.error("Error switching business account:", error);
      toast.error("Failed to switch business account", {
        description: "Failed to switch business account. Please try again.",
      });
    } finally {
      setSwitchingAccount(false);
      setActivateDialogOpen(false);
      setAccountToActivate(null);
    }
  };

  // Function to show remove confirmation
  const confirmRemove = (account: BusinessAccount, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the parent onClick
    setAccountToRemove(account);
    setConfirmDialogOpen(true);
  };

  // Function to remove business account access
  const removeBusinessAccount = async () => {
    if (!accountToRemove || removingAccount) return;

    setRemovingAccount(true);
    try {
      const response = await fetch(
        `/api/user/business-accounts/${accountToRemove.id}`,
        {
          method: "DELETE",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to remove business account access");
      }

      // Update local state by removing the account
      setBusinessAccounts((prev) =>
        prev.filter((account) => account.id !== accountToRemove.id)
      );

      toast.success("Access removed", {
        description: `Your access to ${accountToRemove.name} has been removed.`,
      });
    } catch (error) {
      console.error("Error removing business account access:", error);
      toast.error("Failed to remove business account access", {
        description: "Failed to remove business account access. Please try again.",
      });
    } finally {
      setRemovingAccount(false);
      setConfirmDialogOpen(false);
      setAccountToRemove(null);
    }
  };

  // Count pending invitations
  const pendingInvitationsCount = businessInvitations.length;

  return (
    <>
      {/* Business Invitations Card */}
      {pendingInvitationsCount > 0 && (
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              Pending Invitations
              <Badge variant="secondary">{pendingInvitationsCount}</Badge>
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              className="h-9"
              onClick={fetchBusinessInvitations}
              disabled={loadingInvitations}
            >
              {loadingInvitations ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loadingInvitations ? (
                <div className="flex justify-center items-center py-6">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <div className="space-y-3">
                  {businessInvitations.map((invitation) => (
                    <div
                      key={invitation.id}
                      className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border rounded-lg bg-accent/20"
                    >
                      <div className="flex items-center gap-3 mb-3 sm:mb-0">
                        <Avatar className="h-10 w-10">
                          {invitation.logo ? (
                            <AvatarImage
                              src={invitation.logo}
                              alt={invitation.businessName}
                            />
                          ) : (
                            <AvatarFallback className="bg-primary/10">
                              <Building2 className="h-5 w-5" />
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div>
                          <p className="font-medium">
                            {invitation.businessName}
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {invitation.role}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              From: {invitation.inviterName}
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            Received: {invitation.date}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          className="w-full sm:w-auto"
                          onClick={() => handleInvitation(invitation.id, true)}
                          disabled={processingInvitation}
                        >
                          {processingInvitation ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <Check className="mr-2 h-4 w-4" />
                          )}
                          Accept
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full sm:w-auto"
                          onClick={() => handleInvitation(invitation.id, false)}
                          disabled={processingInvitation}
                        >
                          {processingInvitation ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <X className="mr-2 h-4 w-4" />
                          )}
                          Reject
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Business Accounts Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Business Accounts</CardTitle>
          <Button
            variant="outline"
            size="sm"
            className="h-9"
            onClick={fetchBusinessAccounts}
            disabled={loadingAccounts}
          >
            {loadingAccounts ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-muted-foreground">
              These are the business accounts you have access to. You can
              activate an account to work with it or remove your access to
              accounts you don't own.
            </p>

            {loadingAccounts ? (
              <div className="flex justify-center items-center py-20">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : businessAccounts.length > 0 ? (
              <div className="space-y-3">
                {businessAccounts.map((account) => (
                  <div
                    key={account.id}
                    className={`flex items-center justify-between p-4 border rounded-lg transition-colors ${
                      account.isActive ? "bg-accent border-primary" : ""
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        {account.logo ? (
                          <AvatarImage src={account.logo} alt={account.name} />
                        ) : (
                          <AvatarFallback className="bg-primary/10">
                            <Building2 className="h-6 w-6" />
                          </AvatarFallback>
                        )}
                      </Avatar>
                      <div>
                        <p className="font-medium text-lg">{account.name}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline">{account.role}</Badge>
                          {account.isActive && (
                            <Badge className="bg-primary text-primary-foreground">
                              Active
                            </Badge>
                          )}
                          {account.isOwner && (
                            <Badge variant="secondary">Owner</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {!account.isActive && (
                        <Button
                          variant="default"
                          size="sm"
                          className="flex items-center gap-1"
                          onClick={(e) => confirmActivate(account, e)}
                          disabled={switchingAccount}
                        >
                          <CheckCircle className="h-4 w-4" />
                          Activate
                        </Button>
                      )}
                      {!account.isOwner && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-muted-foreground hover:text-destructive"
                          onClick={(e) => confirmRemove(account, e)}
                          disabled={removingAccount}
                        >
                          <X className="h-4 w-4" />
                          <span className="sr-only">Remove access</span>
                        </Button>
                      )}
                      <ChevronRight className="h-5 w-5 text-muted-foreground" />
                    </div>
                  </div>
                ))}
              </div>
            ) : pendingInvitationsCount > 0 ? (
              <div className="border rounded-lg p-10 text-center text-muted-foreground">
                <Mail className="h-16 w-16 mx-auto mb-4" />
                <p className="text-xl font-medium mb-2">Pending Invitations</p>
                <p className="text-sm max-w-md mx-auto">
                  You don't have access to any business accounts yet, but you
                  have pending invitations. Accept one to get started.
                </p>
              </div>
            ) : (
              <div className="border rounded-lg p-12 text-center text-muted-foreground">
                <Globe className="h-16 w-16 mx-auto mb-4" />
                <p className="text-xl font-medium mb-2">No Business Accounts</p>
                <p className="text-sm max-w-md mx-auto">
                  You don't have access to any business accounts yet. Contact
                  your administrator to get access.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Remove access confirmation dialog */}
      <AlertDialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Remove Access
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove your access to{" "}
              <strong>{accountToRemove?.name}</strong>? This action cannot be
              undone. You will need to request access again from an
              administrator.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={removingAccount}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={removeBusinessAccount}
              disabled={removingAccount}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {removingAccount ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removing...
                </>
              ) : (
                "Remove Access"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Activate business account confirmation dialog */}
      <AlertDialog
        open={activateDialogOpen}
        onOpenChange={setActivateDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-primary" />
              Activate Business Account
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to activate{" "}
              <strong>{accountToActivate?.name}</strong>? This will set it as
              your active business account for all operations.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={switchingAccount}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={switchBusinessAccount}
              disabled={switchingAccount}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {switchingAccount ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Activating...
                </>
              ) : (
                "Activate"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
