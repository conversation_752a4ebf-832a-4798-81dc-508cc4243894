"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  ShieldCheck,
  ShieldX,
  Loader2,
  QrCode,
  Smartphone,
  AlertTriangle,
  Copy,
  Check,
} from "lucide-react";
import {
  useGenerate2FASecret,
  useEnable2FA,
  useDisable2FA,
  useTwoFactorStatus,
} from "@/lib/account/hooks";
import { TwoFactorFormData } from "@/lib/account/types";
import { toast } from "sonner";

// Form validation schema
const twoFactorSchema = z.object({
  verificationCode: z
    .string()
    .min(6, "Verification code must be 6 digits")
    .max(6, "Verification code must be 6 digits")
    .regex(/^\d{6}$/, "Verification code must contain only numbers"),
});

type TwoFactorFormValues = z.infer<typeof twoFactorSchema>;

export function TwoFactorSettings() {
  const {
    data: twoFactorData,
    isLoading: twoFactorLoading,
    error: twoFactorError,
  } = useTwoFactorStatus();
  // Removed useToast hook - using Sonner directly
  const generate2FAMutation = useGenerate2FASecret();
  const enable2FAMutation = useEnable2FA();
  const disable2FAMutation = useDisable2FA();

  const [qrData, setQrData] = useState<{
    secret: string;
    qrCode: string;
  } | null>(null);
  const [showSetup, setShowSetup] = useState(false);
  const [secretCopied, setSecretCopied] = useState(false);

  const form = useForm<TwoFactorFormValues>({
    resolver: zodResolver(twoFactorSchema),
    defaultValues: {
      verificationCode: "",
    },
  });

  const handleGenerate2FA = async () => {
    try {
      const data = await generate2FAMutation.mutateAsync();
      setQrData(data);
      setShowSetup(true);
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const handleEnable2FA = async (formData: TwoFactorFormValues) => {
    try {
      await enable2FAMutation.mutateAsync({
        code: formData.verificationCode,
      });

      // Reset form and hide setup
      form.reset();
      setShowSetup(false);
      setQrData(null);
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const handleDisable2FA = async () => {
    try {
      await disable2FAMutation.mutateAsync();
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const copySecret = async () => {
    if (qrData?.secret) {
      try {
        await navigator.clipboard.writeText(qrData.secret);
        setSecretCopied(true);
        toast.success("Secret copied to clipboard", {
          description: "The secret key has been copied to your clipboard.",
        });
        setTimeout(() => setSecretCopied(false), 2000);
      } catch (error) {
        toast.error("Failed to copy secret", {
          description: "Failed to copy secret to clipboard.",
        });
      }
    }
  };

  const cancelSetup = () => {
    setShowSetup(false);
    setQrData(null);
    form.reset();
  };

  const isLoading =
    generate2FAMutation.isPending ||
    enable2FAMutation.isPending ||
    disable2FAMutation.isPending;

  // Show loading state
  if (twoFactorLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Two-Factor Authentication
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            Loading 2FA status...
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (twoFactorError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Two-Factor Authentication
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>
              Failed to load 2FA status. Please refresh the page to try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Show message if no 2FA data
  if (!twoFactorData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Two-Factor Authentication
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertDescription>
              No 2FA data available. Please refresh the page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Two-Factor Authentication
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Status */}
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex items-center gap-3">
            {twoFactorData?.twoFactorEnabled ? (
              <ShieldCheck className="h-8 w-8 text-green-600" />
            ) : (
              <ShieldX className="h-8 w-8 text-gray-400" />
            )}
            <div>
              <h3 className="font-medium">Two-Factor Authentication</h3>
              <p className="text-sm text-muted-foreground">
                {twoFactorData?.twoFactorEnabled
                  ? "Your account is protected with 2FA"
                  : "Add an extra layer of security to your account"}
              </p>
            </div>
          </div>
          <Badge
            variant={twoFactorData?.twoFactorEnabled ? "default" : "secondary"}
          >
            {twoFactorData?.twoFactorEnabled ? "Enabled" : "Disabled"}
          </Badge>
        </div>

        {/* Setup or Disable Section */}
        {!twoFactorData?.twoFactorEnabled && !showSetup && (
          <div className="space-y-4">
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription>
                Two-factor authentication adds an extra layer of security to
                your account. You'll need an authenticator app like Google
                Authenticator or Authy.
              </AlertDescription>
            </Alert>

            <Button
              onClick={handleGenerate2FA}
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {generate2FAMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <QrCode className="h-4 w-4 mr-2" />
                  Enable 2FA
                </>
              )}
            </Button>
          </div>
        )}

        {/* 2FA Setup Process */}
        {showSetup && qrData && (
          <div className="space-y-6">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Important:</strong> Save your secret key in a secure
                location. You'll need it to recover access if you lose your
                device.
              </AlertDescription>
            </Alert>

            {/* QR Code */}
            <div className="text-center space-y-4">
              <h3 className="font-medium">Step 1: Scan QR Code</h3>
              <div className="flex justify-center">
                <div className="p-4 bg-white rounded-lg border">
                  <img
                    src={qrData.qrCode}
                    alt="2FA QR Code"
                    className="w-48 h-48"
                  />
                </div>
              </div>
              <p className="text-sm text-muted-foreground">
                Scan this QR code with your authenticator app
              </p>
            </div>

            {/* Manual Secret */}
            <div className="space-y-2">
              <Label>Or enter this secret manually:</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={qrData.secret}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={copySecret}
                  disabled={isLoading}
                >
                  {secretCopied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Verification */}
            <form
              onSubmit={form.handleSubmit(handleEnable2FA)}
              className="space-y-4"
            >
              <div className="space-y-2">
                <Label htmlFor="verificationCode">
                  Step 2: Enter verification code
                </Label>
                <Input
                  id="verificationCode"
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                  {...form.register("verificationCode")}
                  disabled={isLoading}
                  className="text-center text-lg tracking-widest"
                />
                {form.formState.errors.verificationCode && (
                  <p className="text-sm text-destructive">
                    {form.formState.errors.verificationCode.message}
                  </p>
                )}
                <p className="text-sm text-muted-foreground">
                  Enter the 6-digit code from your authenticator app
                </p>
              </div>

              <div className="flex gap-2">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {enable2FAMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Enabling...
                    </>
                  ) : (
                    "Enable 2FA"
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={cancelSetup}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Disable 2FA */}
        {twoFactorData?.twoFactorEnabled && (
          <div className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Disabling two-factor authentication will make your account less
                secure. Make sure you understand the risks before proceeding.
              </AlertDescription>
            </Alert>

            <Button
              variant="destructive"
              onClick={handleDisable2FA}
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {disable2FAMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Disabling...
                </>
              ) : (
                <>
                  <ShieldX className="h-4 w-4 mr-2" />
                  Disable 2FA
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
