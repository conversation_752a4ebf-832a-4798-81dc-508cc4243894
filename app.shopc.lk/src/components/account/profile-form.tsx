"use client";

import { useState, useRef } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Upload, X, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Add the upload function
const uploadProfileImage = async (file: File): Promise<string> => {
  // Create a FormData instance
  const formData = new FormData();
  formData.append("profile_image", file);

  try {
    // In a real app, replace with your actual API endpoint
    const response = await fetch("/api/user/profile-image", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error("Failed to upload image");
    }

    const data = await response.json();
    return data.imageUrl; // Return the URL of the uploaded image
  } catch (error) {
    console.error("Error uploading image:", error);
    throw error;
  }
};

interface ProfileFormProps {
  isDemo?: boolean;
}

export default function ProfileForm({ isDemo = false }: ProfileFormProps) {
  // Removed useToast hook - using Sonner directly
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    name: "John Doe",
    email: "<EMAIL>",
    role: "Administrator",
    accountType: "Business",
  });
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [tempProfileImage, setTempProfileImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setTempProfileImage(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setTempProfileImage(null);
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Upload image if there's a new one
      let imageUrl = profileImage;

      if (imageFile) {
        setUploadingImage(true);
        try {
          imageUrl = await uploadProfileImage(imageFile);
          setUploadingImage(false);
        } catch (error) {
          setUploadingImage(false);
          setError("Failed to upload profile image. Please try again.");
          setIsLoading(false);
          return;
        }
      }

      // Make API call to update profile
      // In a real app, replace with your actual API endpoint
      const response = await fetch("/api/user/profile", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          profileImage: imageUrl,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update profile");
      }

      // Update profile image state with the new URL
      setProfileImage(imageUrl);
      setSuccess("Profile updated successfully");
      toast.success("Profile updated successfully", {
        description: "Your profile has been updated successfully.",
      });
      setShowForm(false);
    } catch (error) {
      setError("Failed to update profile. Please try again.");
      toast.error("Failed to update profile", {
        description: "Failed to update profile. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
      </CardHeader>
      <CardContent>
        {showForm ? (
          <div>
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              {success && (
                <Alert>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <Avatar className="w-24 h-24">
                    {tempProfileImage ? (
                      <AvatarImage src={tempProfileImage} alt={formData.name} />
                    ) : profileImage ? (
                      <AvatarImage src={profileImage} alt={formData.name} />
                    ) : (
                      <AvatarFallback className="text-lg bg-primary text-primary-foreground">
                        {getInitials(formData.name)}
                      </AvatarFallback>
                    )}
                    {uploadingImage && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
                        <Loader2 className="h-8 w-8 text-white animate-spin" />
                      </div>
                    )}
                  </Avatar>
                  {(tempProfileImage || profileImage) && (
                    <button
                      type="button"
                      onClick={handleRemoveImage}
                      className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={triggerFileInput}
                    className="flex items-center gap-2"
                    disabled={uploadingImage}
                  >
                    {uploadingImage ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Upload className="h-4 w-4" />
                    )}
                    Upload Photo
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Name</label>
                <Input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Email</label>
                <Input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>

              <div className="flex space-x-2">
                <Button type="submit" disabled={isLoading || uploadingImage}>
                  {isLoading ? "Updating..." : "Update Profile"}
                </Button>
                <Button variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="w-24 h-24">
                {profileImage ? (
                  <AvatarImage src={profileImage} alt={formData.name} />
                ) : (
                  <AvatarFallback className="text-lg bg-primary text-primary-foreground">
                    {getInitials(formData.name)}
                  </AvatarFallback>
                )}
              </Avatar>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Name</label>
                <div className="mt-1 p-2 border rounded-md">
                  {formData.name}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Email</label>
                <div className="mt-1 p-2 border rounded-md">
                  {formData.email}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Role</label>
                <div className="mt-1 p-2 border rounded-md">
                  {formData.role}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium">Account Type</label>
                <div className="mt-1 p-2 border rounded-md">
                  {formData.accountType}
                </div>
              </div>
            </div>

            <div className="pt-4">
              <Button onClick={() => setShowForm(true)}>Edit Profile</Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
