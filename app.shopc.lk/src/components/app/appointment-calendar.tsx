"use client";

import * as React from "react";
import {
  format,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  addDays,
  isToday,
  isSameDay,
  startOfMonth,
  endOfMonth,
  getMonth,
} from "date-fns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

import { Slider } from "@/components/ui/slider";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import NewAppointmentDialog from "@/components/appointments/new-appointment-dialog";
import AppointmentSheet from "@/components/appointments/appointment-sheet";
import MonthView from "@/components/appointments/month-view";
import SkeletonView from "@/components/appointments/skeleton-view";
import WorkingHoursDialog from "@/components/appointments/working-hours-dialog";
import { AppointmentCalendarHeader } from "@/components/appointments/appointment-calendar-header";

// Import hooks from the appointments library
import {
  useMonthAppointments,
  useWeekAppointments,
  useStaffAppointments,
  useQuickViewAppointments,
  useStaff,
  useServices,
  useCustomers,
  useCreateAppointment,
  useUpdateAppointment,
  useDeleteAppointment,
  useRefreshAppointments,
  calculateEndTime,
  formatTimeString,
  parseTimeStringTo24Hour,
  calculateAppointmentPosition,
} from "@/lib/appointments";

// Import types from centralized types directory
import {
  type Appointment,
  type MonthAppointment,
  type WeekAppointment,
  type StaffAppointment,
  type QuickViewAppointment,
  type BaseAppointment,
  type AppointmentStaff,
  type AppointmentService,
  type AppointmentCustomer,
  type AppointmentFormData,
  type AppointmentUpdateData,
  type ViewModeType,
  type AppointmentCalendarProps,
  type TimeSlot,
  type EndTimeSlot,
} from "@/types/app";

import { toast } from "sonner";
import ErrorBoundary from "@/components/ui/error-boundary";
import ResponsiveWeekView from "./responsive-week-view";
import ResponsiveStaffView from "./responsive-staff-view";
import ResponsiveQuickView from "./responsive-quick-view";

const AppointmentCalandar = ({ isDemo = false }: AppointmentCalendarProps) => {
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const [hoveredCell, setHoveredCell] = React.useState<{
    day: number;
    hour: number;
    minute: number;
  } | null>(null);
  const [viewMode, setViewMode] = React.useState<ViewModeType>("Week");
  const [workingHours, setWorkingHours] = React.useState({
    start: 9, // 9 AM
    end: 17, // 5 PM
  });
  const [showAllHours, setShowAllHours] = React.useState(false);
  const [showWorkingHoursDialog, setShowWorkingHoursDialog] =
    React.useState(false);
  // Add loading state
  const [isLoading, setIsLoading] = React.useState(true);

  // Staff pagination state
  const [currentStaffPage, setCurrentStaffPage] = React.useState(0);
  const staffPerPage = 4;

  // New appointment state
  const [newAppointmentDialog, setNewAppointmentDialog] = React.useState(false);
  const [selectedSlot, setSelectedSlot] = React.useState<TimeSlot | null>(null);
  const [selectedServiceId, setSelectedServiceId] = React.useState("");
  const [appointmentStaff, setAppointmentStaff] = React.useState("staff1");
  const [calendarOpen, setCalendarOpen] = React.useState(false);
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    undefined
  );
  const [timePickerOpen, setTimePickerOpen] = React.useState(false);
  const [selectedCustomer, setSelectedCustomer] = React.useState<string>("");
  const [customerSearchOpen, setCustomerSearchOpen] = React.useState(false);
  const [isRescheduling, setIsRescheduling] = React.useState(false);
  const [reschedulingAppointmentId, setReschedulingAppointmentId] =
    React.useState<string | null>(null);

  // End time state
  const [endTimePickerOpen, setEndTimePickerOpen] = React.useState(false);
  const [manualEndTimeInput, setManualEndTimeInput] = React.useState("");
  const [endTimeSlot, setEndTimeSlot] = React.useState<EndTimeSlot | null>(
    null
  );

  // State for appointment details sheet
  const [selectedAppointment, setSelectedAppointment] =
    React.useState<Appointment | null>(null);
  const [appointmentSheetOpen, setAppointmentSheetOpen] = React.useState(false);
  const [appointmentNotes, setAppointmentNotes] = React.useState("");

  // State for date click in month view
  const [selectedMonthDate, setSelectedMonthDate] = React.useState<Date | null>(
    null
  );
  const [dateAppointmentsDialogOpen, setDateAppointmentsDialogOpen] =
    React.useState(false);

  // Discount state
  const [discountPopoverOpen, setDiscountPopoverOpen] = React.useState(false);
  const [discountType, setDiscountType] = React.useState<
    "percentage" | "fixed"
  >("percentage");
  const [discountValue, setDiscountValue] = React.useState<number>(0);
  const [discountApplied, setDiscountApplied] = React.useState(false);

  // New state for manual time input
  const [manualTimeInput, setManualTimeInput] = React.useState("");

  // Generate the week range (Sunday to Saturday)
  const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 });
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 0 });
  const daysOfWeek = eachDayOfInterval({ start: weekStart, end: weekEnd });

  // Generate days for month view
  const getMonthDays = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 0 });

    return eachDayOfInterval({ start: startDate, end: endDate });
  };

  // Mutation hooks for CRUD operations
  const createAppointmentMutation = useCreateAppointment(isDemo);
  const updateAppointmentMutation = useUpdateAppointment(isDemo);
  const deleteAppointmentMutation = useDeleteAppointment(isDemo);

  // Fetch appointments based on view mode
  const { data: monthAppointmentsData, isLoading: isMonthLoading } =
    useMonthAppointments(currentDate, isDemo);

  const { data: weekAppointmentsData, isLoading: isWeekLoading } =
    useWeekAppointments(currentDate, isDemo);

  const { data: staffAppointmentsData, isLoading: isStaffLoading } =
    useStaffAppointments(currentDate, isDemo);

  const { data: quickViewAppointmentsData, isLoading: isQuickViewLoading } =
    useQuickViewAppointments(currentDate, isDemo);

  // Get the appropriate data and loading state based on view mode
  const { appointmentsData, isAppointmentsLoading } = React.useMemo(() => {
    switch (viewMode) {
      case "Month":
        return {
          appointmentsData: monthAppointmentsData,
          isAppointmentsLoading: isMonthLoading,
        };
      case "Week":
        return {
          appointmentsData: weekAppointmentsData,
          isAppointmentsLoading: isWeekLoading,
        };
      case "Staff":
        return {
          appointmentsData: staffAppointmentsData,
          isAppointmentsLoading: isStaffLoading,
        };
      case "Quick":
        return {
          appointmentsData: quickViewAppointmentsData,
          isAppointmentsLoading: isQuickViewLoading,
        };
      default:
        return {
          appointmentsData: weekAppointmentsData,
          isAppointmentsLoading: isWeekLoading,
        };
    }
  }, [
    viewMode,
    monthAppointmentsData,
    weekAppointmentsData,
    staffAppointmentsData,
    quickViewAppointmentsData,
    isMonthLoading,
    isWeekLoading,
    isStaffLoading,
    isQuickViewLoading,
  ]);

  // Set loading state based on appointments loading
  React.useEffect(() => {
    // Only show loading state when we're actually fetching data
    setIsLoading(isAppointmentsLoading);
  }, [isAppointmentsLoading]);

  // Initial data load is handled automatically by TanStack Query hooks

  // Map API response to component's appointment format based on view mode
  const mappedAppointments = React.useMemo(() => {
    if (!appointmentsData?.data) return [];
    return appointmentsData.data.map(
      (appointment: any): BaseAppointment => ({
        id: appointment.id,
        title: appointment.title,
        time: appointment.time,
        date: new Date(appointment.date),
        staffId: appointment.staffId,
        isPast: appointment.isPast,
        duration: appointment.duration,
        price: appointment.price,
        customerId: appointment.customerId,
        notes: appointment.notes,
        endTime: appointment.endTime,
      })
    );
  }, [appointmentsData]);

  // Type-specific appointment arrays for each view mode
  const monthAppointments = React.useMemo((): MonthAppointment[] => {
    return mappedAppointments as MonthAppointment[];
  }, [mappedAppointments]);

  const weekAppointments = React.useMemo((): WeekAppointment[] => {
    return mappedAppointments as WeekAppointment[];
  }, [mappedAppointments]);

  const staffAppointments = React.useMemo((): StaffAppointment[] => {
    return mappedAppointments as StaffAppointment[];
  }, [mappedAppointments]);

  const quickViewAppointments = React.useMemo((): QuickViewAppointment[] => {
    return mappedAppointments as QuickViewAppointment[];
  }, [mappedAppointments]);

  // Get the appropriate appointment array based on view mode
  const getAppointmentsForCurrentView = (): BaseAppointment[] => {
    switch (viewMode) {
      case "Month":
        return monthAppointments;
      case "Week":
        return weekAppointments;
      case "Staff":
        return staffAppointments;
      case "Quick":
        return quickViewAppointments;
      default:
        return mappedAppointments as BaseAppointment[];
    }
  };

  // Use view-specific appointments
  const appointments = getAppointmentsForCurrentView();

  // Fetch staff members
  const { data: staffData } = useStaff(isDemo);
  const staffMembers: AppointmentStaff[] = React.useMemo(() => {
    if (!staffData?.data) return [];
    return staffData.data;
  }, [staffData]);

  // Fetch services
  const { data: servicesData } = useServices(isDemo);
  const services: AppointmentService[] = React.useMemo(() => {
    if (!servicesData?.data) return [];
    return servicesData.data;
  }, [servicesData]);

  // Fetch customers
  const { data: customersData } = useCustomers(isDemo);
  const customers: AppointmentCustomer[] = React.useMemo(() => {
    if (!customersData?.data) return [];
    return customersData.data;
  }, [customersData]);

  // Use the refreshAppointments hook

  // Helper function to calculate appointment position and height in calendar
  const calculateAppointmentDisplay = (
    appointment: BaseAppointment,
    startHour: number
  ) => {
    return calculateAppointmentPosition(appointment, startHour);
  };

  // Gets appointments for a specific time slot
  const getAppointmentsForTimeSlot = (
    day: Date,
    hour: number,
    minute: number = 0
  ) => {
    return appointments.filter((appointment: BaseAppointment) => {
      // Skip appointments for different days
      if (!isSameDay(appointment.date, day)) {
        return false;
      }

      const { hour: appointmentHour, minute: appointmentMinute } =
        parseTimeStringTo24Hour(appointment.time);

      // Check if this appointment starts in this hour slot
      if (appointmentHour === hour) {
        return true;
      }

      // Check if appointment spans into this hour
      if (appointment.duration || appointment.endTime) {
        let endHour = appointmentHour;
        let endMinute = appointmentMinute;

        if (appointment.endTime) {
          const parsed = parseTimeStringTo24Hour(appointment.endTime);
          endHour = parsed.hour;
          endMinute = parsed.minute;
        } else if (appointment.duration) {
          // Calculate end time from duration
          const totalMinutes = appointmentMinute + appointment.duration;
          endHour = appointmentHour + Math.floor(totalMinutes / 60);
          endMinute = totalMinutes % 60;
        }

        // Check if appointment ends after this hour starts
        return endHour > hour || (endHour === hour && endMinute > 0);
      }

      return false;
    });
  };

  // Format time for hover tooltip
  const formatTimeForHover = (day: Date, hour: number, minute: number) => {
    const date = new Date(day);
    date.setHours(hour);
    date.setMinutes(minute);
    return format(date, "hh:mm a");
  };

  // Generate hour rows for full day (12 AM to 11 PM)
  const allHours = [
    { value: 0, display: 12, period: "AM" },
    { value: 1, display: 1, period: "AM" },
    { value: 2, display: 2, period: "AM" },
    { value: 3, display: 3, period: "AM" },
    { value: 4, display: 4, period: "AM" },
    { value: 5, display: 5, period: "AM" },
    { value: 6, display: 6, period: "AM" },
    { value: 7, display: 7, period: "AM" },
    { value: 8, display: 8, period: "AM" },
    { value: 9, display: 9, period: "AM" },
    { value: 10, display: 10, period: "AM" },
    { value: 11, display: 11, period: "AM" },
    { value: 12, display: 12, period: "PM" },
    { value: 13, display: 1, period: "PM" },
    { value: 14, display: 2, period: "PM" },
    { value: 15, display: 3, period: "PM" },
    { value: 16, display: 4, period: "PM" },
    { value: 17, display: 5, period: "PM" },
    { value: 18, display: 6, period: "PM" },
    { value: 19, display: 7, period: "PM" },
    { value: 20, display: 8, period: "PM" },
    { value: 21, display: 9, period: "PM" },
    { value: 22, display: 10, period: "PM" },
    { value: 23, display: 11, period: "PM" },
  ];

  // Filter hours based on working hours setting
  const hours = React.useMemo(() => {
    if (showAllHours) {
      return allHours;
    }
    return allHours.filter(
      (hour) =>
        hour.value >= workingHours.start && hour.value < workingHours.end
    );
  }, [workingHours, showAllHours]);

  // Check if there are appointments outside working hours
  const hasAppointmentsOutsideWorkingHours = React.useMemo(() => {
    if (showAllHours) return false;

    return appointments.some((appointment: BaseAppointment) => {
      const { hour: appointmentHour } = parseTimeStringTo24Hour(
        appointment.time
      );
      return (
        appointmentHour < workingHours.start ||
        appointmentHour >= workingHours.end
      );
    });
  }, [appointments, workingHours, showAllHours]);

  // Toggle between showing all hours or just working hours
  const toggleHoursDisplay = () => {
    setShowAllHours(!showAllHours);
  };

  // Update working hours
  const updateWorkingHours = (start: number, end: number) => {
    setWorkingHours({ start, end });
  };

  // Handle time slot click
  const handleTimeSlotClick = (date: Date, hour: number, minute: number) => {
    setSelectedSlot({ date, hour, minute });
    setSelectedDate(date);
    setManualTimeInput(format(new Date().setHours(hour, minute), "hh:mm a"));
    setNewAppointmentDialog(true);
  };

  // Handle date change in calendar
  const handleCalendarDateChange = (date: Date | undefined) => {
    if (date && selectedSlot) {
      setSelectedDate(date);
      setSelectedSlot({
        ...selectedSlot,
        date: date,
      });
    }
  };

  // Get selected service
  const getSelectedService = () => {
    if (!selectedServiceId) {
      console.log("No service ID selected");
      return null;
    }

    // Check if services array exists and is not empty
    if (!services || services.length === 0) {
      console.log("Services data not available", { services });
      return null;
    }

    const service = services.find(
      (service) => service.id === selectedServiceId
    );
    if (!service) {
      console.log("Service not found for ID:", selectedServiceId, {
        availableServices: services,
      });
    }
    return service;
  };

  // Update order total based on selected service
  const getOrderTotal = () => {
    const service = getSelectedService();

    if (!service) return "Rs0.00";

    // Base price
    let price = service.price;

    // Apply discount if any
    if (discountApplied && discountValue > 0) {
      if (discountType === "percentage") {
        // Ensure discount percentage doesn't exceed 100%
        const discountPercent = Math.min(discountValue, 100);
        price = price * (1 - discountPercent / 100);
      } else {
        // Ensure discount amount doesn't exceed price
        price = Math.max(0, price - discountValue);
      }
    }

    // Return formatted price
    return `Rs${price.toFixed(2)}`;
  };

  // Apply discount
  const applyDiscount = () => {
    setDiscountApplied(true);
    setDiscountPopoverOpen(false);
  };

  // Clear discount
  const clearDiscount = () => {
    setDiscountApplied(false);
    setDiscountValue(0);
  };

  // Handle service selection
  const handleServiceChange = (value: string) => {
    console.log("Service selected:", value);
    setSelectedServiceId(value);

    // Auto-calculate end time based on service duration
    const selectedService = services.find((service) => service.id === value);
    if (selectedService && selectedSlot) {
      console.log("Service details found:", selectedService);
      // Get duration in minutes
      const durationMinutes = selectedService.duration;

      // Calculate end time
      const startDate = new Date(selectedSlot.date);
      startDate.setHours(selectedSlot.hour, selectedSlot.minute, 0, 0);

      const endDate = new Date(startDate);
      endDate.setMinutes(endDate.getMinutes() + durationMinutes);

      // Set end time state
      setEndTimeSlot({
        hour: endDate.getHours(),
        minute: endDate.getMinutes(),
      });

      // Format for display
      setManualEndTimeInput(format(endDate, "hh:mm a"));
    } else {
      console.log("Could not set end time - service or slot missing", {
        service: selectedService,
        slot: selectedSlot,
      });
    }
  };

  // Close new appointment dialog and reset state
  const closeNewAppointmentDialog = () => {
    setSelectedSlot(null);
    setSelectedServiceId("");
    setAppointmentStaff("staff1");
    setSelectedCustomer("");
    setEndTimeSlot(null);
    setManualEndTimeInput("");
    setSelectedDate(undefined);
    setDiscountValue(0);
    setDiscountApplied(false);
    setIsRescheduling(false);
    setReschedulingAppointmentId(null);
    setNewAppointmentDialog(false);
  };

  // Prepare appointment for rescheduling
  const handleRescheduleAppointment = () => {
    if (!selectedAppointment) return;

    // Set the selected service
    const serviceId = services.find(
      (service) => service.name === selectedAppointment.title
    )?.id;

    if (serviceId) {
      setSelectedServiceId(serviceId);
    }

    // Set the staff member
    if (selectedAppointment.staffId) {
      setAppointmentStaff(selectedAppointment.staffId);
    }

    // Set the customer
    if (selectedAppointment.customerId) {
      setSelectedCustomer(selectedAppointment.customerId);
    }

    // Mark as rescheduling
    setIsRescheduling(true);
    setReschedulingAppointmentId(selectedAppointment.id);

    // Close appointment sheet and open the new appointment dialog
    setAppointmentSheetOpen(false);
    setNewAppointmentDialog(true);
  };

  // Book or reschedule appointment
  const bookAppointment = () => {
    console.log("Starting appointment booking process with:", {
      selectedSlot,
      selectedServiceId,
      appointmentStaff,
      selectedCustomer,
      services: services.length,
    });

    // Validate required fields
    if (!selectedSlot) {
      toast.error("Please select a time slot for the appointment.");
      return;
    }

    if (!selectedServiceId) {
      toast.error("Please select a service for the appointment.");
      return;
    }

    if (!appointmentStaff) {
      toast.error("Please select a staff member for the appointment.");
      return;
    }

    if (!selectedCustomer) {
      toast.error("Please select a customer for the appointment.");
      return;
    }

    const selectedService = getSelectedService();
    console.log("Selected service:", selectedService);

    if (!selectedService) {
      // Check if services are loaded properly
      if (!services || services.length === 0) {
        toast.error("Services data is not available. Please try again later.");
      } else {
        toast.error(`Service with ID "${selectedServiceId}" could not be found. Please select a valid service.`);
      }
      return;
    }

    // Rest of the function remains the same...
    // Create the appointment data
    const appointmentDate = new Date(selectedSlot.date);

    // Format time in HH:MM format
    const time = `${selectedSlot.hour}:${
      selectedSlot.minute === 0 ? "00" : selectedSlot.minute
    }`;

    // Calculate end time based on service duration
    const calculatedEndTime = calculateEndTime(
      appointmentDate,
      time,
      selectedService.duration
    );

    // Parse the price string to get numeric value
    let price = selectedService.price;
    if (discountApplied && discountValue > 0) {
      // Get numeric value from the formatted price string by removing non-numeric characters
      const priceString = getOrderTotal().replace(/[^0-9.]/g, "");
      price = parseFloat(priceString);
    }

    // Prepare data for the appointment
    const appointmentData: AppointmentFormData = {
      title: selectedService.name,
      time,
      date: appointmentDate,
      staffId: appointmentStaff,
      duration: selectedService.duration,
      price: price,
      customerId: selectedCustomer,
      notes: "",
      endTime: calculatedEndTime,
      isPast: false,
    };

    console.log("Appointment data prepared:", appointmentData);

    // Check if we're rescheduling an existing appointment
    if (isRescheduling && reschedulingAppointmentId) {
      // Update the existing appointment
      updateAppointmentMutation.mutate(
        {
          id: reschedulingAppointmentId,
          data: appointmentData,
        } as AppointmentUpdateData,
        {
          onSuccess: () => {
            toast.success("The appointment has been successfully rescheduled.");
            closeNewAppointmentDialog();
          },
          onError: (error: unknown) => {
            console.error("Error rescheduling appointment:", error);
            toast.error("Failed to reschedule the appointment. Please try again.");
          },
        }
      );
    } else {
      // Create a new appointment
      createAppointmentMutation.mutate(appointmentData, {
        onSuccess: () => {
          toast.success("The appointment has been successfully booked.");
          closeNewAppointmentDialog();
        },
        onError: (error: unknown) => {
          console.error("Error booking appointment:", error);
          toast.error("Failed to book the appointment. Please try again.");
        },
      });
    }
  };

  // Handle appointment cancellation/deletion
  const handleCancelAppointment = () => {
    if (!selectedAppointment) return;

    deleteAppointmentMutation.mutate(selectedAppointment.id, {
      onSuccess: () => {
        toast.success("The appointment has been successfully cancelled.");
        setAppointmentSheetOpen(false);
        setSelectedAppointment(null);
      },
      onError: (error: unknown) => {
        toast.error("Failed to cancel the appointment. Please try again.");
      },
    });
  };

  // Save notes for an appointment
  const saveAppointmentNotes = () => {
    if (!selectedAppointment) return;

    updateAppointmentMutation.mutate(
      {
        id: selectedAppointment.id,
        data: { notes: appointmentNotes },
      } as AppointmentUpdateData,
      {
        onSuccess: () => {
          toast.success("The appointment notes have been successfully saved.");
          // Update the local state to reflect the changes
          setSelectedAppointment((prev) =>
            prev ? { ...prev, notes: appointmentNotes } : null
          );
        },
        onError: (error: unknown) => {
          toast.error("Failed to save appointment notes. Please try again.");
        },
      }
    );
  };

  // Process manual end time entry
  const processManualEndTimeEntry = (value: string) => {
    setManualEndTimeInput(value);

    // Try to parse the manual end time input
    try {
      // Check for basic HH:MM format with optional am/pm
      const timeRegex = /^(\d{1,2}):(\d{2})\s*(am|pm|AM|PM)?$/;
      const match = value.trim().match(timeRegex);

      if (match) {
        let hours = parseInt(match[1]);
        const minutes = parseInt(match[2]);
        const period = match[3]?.toLowerCase();

        // Handle 12-hour format
        if (period === "pm" && hours < 12) {
          hours += 12;
        } else if (period === "am" && hours === 12) {
          hours = 0;
        }

        // Validate hours and minutes
        if (hours >= 0 && hours < 24 && minutes >= 0 && minutes < 60) {
          // Set end time
          setEndTimeSlot({
            hour: hours,
            minute: minutes,
          });
        }
      }
    } catch (error) {
      console.log("Could not parse end time:", value);
    }
  };

  // Handle end time change
  const handleEndTimeChange = (hour: number, minute: number) => {
    setEndTimeSlot({
      hour,
      minute,
    });
    setEndTimePickerOpen(false);

    const endDate = new Date();
    endDate.setHours(hour, minute, 0, 0);
    setManualEndTimeInput(format(endDate, "hh:mm a"));
  };

  // Render end time picker grid
  const renderEndTimePickerGrid = () => {
    // We'll show a subset of time slots to match the image
    const displaySlots = [
      { hour: 0, minute: 0, label: "12:00 am" },
      { hour: 0, minute: 15, label: "12:15 am" },
      { hour: 0, minute: 30, label: "12:30 am" },
      { hour: 0, minute: 45, label: "12:45 am" },
      { hour: 1, minute: 0, label: "01:00 am" },
      { hour: 1, minute: 15, label: "01:15 am" },
      { hour: 1, minute: 30, label: "01:30 am" },
      { hour: 1, minute: 45, label: "01:45 am" },
      { hour: 2, minute: 0, label: "02:00 am" },
      { hour: 2, minute: 15, label: "02:15 am" },
      { hour: 2, minute: 30, label: "02:30 am" },
      { hour: 2, minute: 45, label: "02:45 am" },
      { hour: 3, minute: 0, label: "03:00 am" },
      { hour: 3, minute: 15, label: "03:15 am" },
      { hour: 3, minute: 30, label: "03:30 am" },
    ];

    return (
      <div className="grid grid-cols-3 gap-0 bg-background max-h-[300px]">
        {displaySlots.map((slot, index) => (
          <button
            key={index}
            className={`p-3 text-sm hover:bg-primary/10 border text-center ${
              endTimeSlot?.hour === slot.hour &&
              endTimeSlot?.minute === slot.minute
                ? "bg-primary text-primary-foreground"
                : "text-foreground"
            }`}
            onClick={() => handleEndTimeChange(slot.hour, slot.minute)}
          >
            {slot.label}
          </button>
        ))}
      </div>
    );
  };

  // Helper function to format appointment duration
  const formatDuration = (startTime: string, durationMinutes?: number) => {
    if (!durationMinutes) return startTime;

    const [hourStr, minuteStr] = startTime.split(":");
    const timeStr = startTime.toLowerCase();

    // Convert start time to 24-hour format
    let startHour = parseInt(hourStr);
    if (timeStr.includes("pm") && startHour < 12) {
      startHour += 12;
    } else if (timeStr.includes("am") && startHour === 12) {
      startHour = 0;
    }

    // Parse minutes, handling case where minutes include period
    const minuteMatch = minuteStr.match(/^(\d+)/);
    const startMinute = minuteMatch ? parseInt(minuteMatch[1]) : 0;

    // Calculate end time
    let endMinutes = startMinute + durationMinutes;
    let endHour = startHour + Math.floor(endMinutes / 60);
    endMinutes = endMinutes % 60;

    // Format times for display
    const startTimeFormatted = formatTimeString(startHour, startMinute);
    const endTimeFormatted = formatTimeString(endHour, endMinutes);

    return `${startTimeFormatted} - ${endTimeFormatted} (${durationMinutes} min)`;
  };

  // Handle appointment click to open details sheet
  const handleAppointmentClick = (appointment: BaseAppointment) => {
    // Find the service details if available
    const serviceDetails = services.find(
      (service) => service.name === appointment.title
    );

    // Set price and duration from service if not on appointment already
    if (serviceDetails) {
      appointment.price = appointment.price || serviceDetails.price;
      appointment.duration = appointment.duration || serviceDetails.duration;
    }

    setSelectedAppointment(appointment);
    setAppointmentNotes(appointment.notes || "");
    setAppointmentSheetOpen(true);
  };

  // Handle view mode change (Week, Month, Staff)
  const handleViewModeChange = (mode: ViewModeType) => {
    // We don't need a loading state for view changes since we already have the data
    setViewMode(mode);
  };

  // Handle date change from header
  const handleHeaderDateChange = (date: Date) => {
    setCurrentDate(date);
  };

  // Handle date click in month view
  const handleDateClick = (date: Date) => {
    setSelectedMonthDate(date);
    setDateAppointmentsDialogOpen(true);
  };

  // Get appointments for selected date
  const getAppointmentsForSelectedDate = () => {
    if (!selectedMonthDate) return [];
    return appointments.filter((appointment: BaseAppointment) =>
      isSameDay(appointment.date, selectedMonthDate)
    );
  };

  // Render the staff view
  const renderStaffView = () => {
    // The component has been moved to its own file, so we don't need this function anymore
    return null;
  };

  // Render the appropriate view based on view mode
  const renderView = () => {
    if (isLoading) {
      return <SkeletonView viewMode={viewMode} />;
    }

    switch (viewMode) {
      case "Week":
        return (
          <ResponsiveWeekView
            daysOfWeek={daysOfWeek}
            hours={hours}
            appointments={appointments}
            hasAppointmentsOutsideWorkingHours={
              hasAppointmentsOutsideWorkingHours
            }
            showAllHours={showAllHours}
            toggleHoursDisplay={toggleHoursDisplay}
            setHoveredCell={setHoveredCell}
            hoveredCell={hoveredCell}
            handleTimeSlotClick={handleTimeSlotClick}
            formatTimeForHover={formatTimeForHover}
            handleAppointmentClick={(appointment) =>
              handleAppointmentClick(appointment as BaseAppointment)
            }
            staffMembers={staffMembers}
            calculateAppointmentDisplay={calculateAppointmentDisplay}
          />
        );
      case "Month":
        return (
          <MonthView
            currentDate={currentDate}
            monthDays={getMonthDays()}
            appointments={appointments}
            weekdaysShort={["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]}
            onDateClick={handleDateClick}
          />
        );
      case "Staff":
        return (
          <ResponsiveStaffView
            staffMembers={staffMembers}
            hours={hours}
            currentDate={currentDate}
            appointments={appointments}
            hasAppointmentsOutsideWorkingHours={
              hasAppointmentsOutsideWorkingHours
            }
            showAllHours={showAllHours}
            toggleHoursDisplay={toggleHoursDisplay}
            handleTimeSlotClick={handleTimeSlotClick}
            handleAppointmentClick={(appointment) =>
              handleAppointmentClick(appointment as BaseAppointment)
            }
            calculateAppointmentDisplay={calculateAppointmentDisplay}
          />
        );
      case "Quick":
        return (
          <ResponsiveQuickView
            daysOfWeek={daysOfWeek}
            staffMembers={staffMembers}
            appointments={appointments}
            hours={hours}
            allHours={allHours}
            showAllHours={showAllHours}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ErrorBoundary>
      <div className="container mx-auto p-4 space-y-4">
        {/* Render the appointment details sheet */}
        <ErrorBoundary>
          <AppointmentSheet
            open={appointmentSheetOpen}
            onOpenChange={setAppointmentSheetOpen}
            appointment={selectedAppointment}
            staffMembers={staffMembers}
            customers={customers}
            appointmentNotes={appointmentNotes}
            setAppointmentNotes={setAppointmentNotes}
            handleRescheduleAppointment={handleRescheduleAppointment}
            handleCancelAppointment={handleCancelAppointment}
            saveAppointmentNotes={saveAppointmentNotes}
            formatDuration={formatDuration}
          />
        </ErrorBoundary>

        {/* Header with navigation and view selection */}
        <ErrorBoundary>
          <AppointmentCalendarHeader
            viewMode={viewMode}
            currentDate={currentDate}
            showAllHours={showAllHours}
            onViewModeChange={handleViewModeChange}
            onDateChange={handleHeaderDateChange}
            onShowWorkingHoursDialog={setShowWorkingHoursDialog}
            onToggleHoursDisplay={toggleHoursDisplay}
          />
        </ErrorBoundary>

        {/* Working hours dialog */}
        <ErrorBoundary>
          <WorkingHoursDialog
            open={showWorkingHoursDialog}
            onOpenChange={setShowWorkingHoursDialog}
            workingHours={workingHours}
            onWorkingHoursChange={updateWorkingHours}
            showAllHours={showAllHours}
            onToggleAllHours={toggleHoursDisplay}
          />
        </ErrorBoundary>

        {/* Date appointments dialog */}
        <ErrorBoundary>
          <Dialog
            open={dateAppointmentsDialogOpen}
            onOpenChange={setDateAppointmentsDialogOpen}
          >
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  Appointments for{" "}
                  {selectedMonthDate &&
                    format(selectedMonthDate, "MMMM d, yyyy")}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-3">
                {getAppointmentsForSelectedDate().length > 0 ? (
                  getAppointmentsForSelectedDate().map((appointment) => (
                    <Card
                      key={appointment.id}
                      className="p-4 cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => {
                        setSelectedAppointment(appointment);
                        setAppointmentNotes(appointment.notes || "");
                        setDateAppointmentsDialogOpen(false);
                        setAppointmentSheetOpen(true);
                      }}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-lg">
                            {appointment.title}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {appointment.time}
                          </p>
                          {appointment.duration && (
                            <p className="text-sm text-gray-500">
                              {appointment.duration} minutes
                            </p>
                          )}
                          {appointment.notes && (
                            <p className="text-sm text-gray-500 mt-1">
                              {appointment.notes}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          {appointment.price && (
                            <p className="font-medium">
                              Rs{appointment.price.toFixed(2)}
                            </p>
                          )}
                          {appointment.isPast && (
                            <span className="text-xs text-gray-400">Past</span>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      No appointments scheduled for this date.
                    </p>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </ErrorBoundary>

        {/* New appointment dialog */}
        <ErrorBoundary>
          <NewAppointmentDialog
            open={newAppointmentDialog}
            onOpenChange={setNewAppointmentDialog}
            isRescheduling={isRescheduling}
            services={services}
            staffMembers={staffMembers}
            customers={customers}
            selectedServiceId={selectedServiceId}
            appointmentStaff={appointmentStaff}
            selectedDate={selectedDate}
            selectedSlot={selectedSlot}
            endTimeSlot={endTimeSlot}
            manualEndTimeInput={manualEndTimeInput}
            selectedCustomer={selectedCustomer}
            discountApplied={discountApplied}
            discountType={discountType as "fixed" | "percentage"}
            discountValue={discountValue}
            manualTimeInput={manualTimeInput}
            handleServiceChange={handleServiceChange}
            getSelectedService={getSelectedService}
            setAppointmentStaff={setAppointmentStaff}
            handleDateChange={handleCalendarDateChange}
            processManualEndTimeEntry={processManualEndTimeEntry}
            handleEndTimeChange={handleEndTimeChange}
            setManualTimeInput={setManualTimeInput}
            formatTimeString={formatTimeString}
            getOrderTotal={getOrderTotal}
            setDiscountType={
              setDiscountType as (type: "fixed" | "percentage") => void
            }
            setDiscountValue={setDiscountValue}
            clearDiscount={clearDiscount}
            applyDiscount={applyDiscount}
            bookAppointment={bookAppointment}
            setSelectedCustomer={setSelectedCustomer}
            setSelectedSlot={setSelectedSlot}
          />
        </ErrorBoundary>

        {/* Render the appropriate view */}
        <ErrorBoundary>{renderView()}</ErrorBoundary>
      </div>
    </ErrorBoundary>
  );
};

export default AppointmentCalandar;
