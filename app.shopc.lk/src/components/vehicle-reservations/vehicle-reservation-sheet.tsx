"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import {
  Car,
  Calendar as CalendarIcon,
  MapPin,
  CreditCard,
  Info,
  Clock,
  User,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  VehicleReservationDetails,
  ReservationStatus,
  PaymentStatus,
  VehicleClass,
  CreateVehicleReservationRequest,
  UpdateVehicleReservationRequest,
} from "@/types/vehicle-reservation";
import React from "react";
import { BaseSheet } from "@/components/shared/base-sheet";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { useReservationById } from "@/lib/vehicle-reservations/hooks";
import { toast } from "sonner";
import { ApiStatus } from "@/types/common";
import {
  createReservation,
  updateReservation,
} from "@/lib/vehicle-reservations/api";
import {
  createDemoReservation,
  updateDemoReservation,
} from "@/lib/vehicle-reservations/demo";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useAvailableVehicles } from "@/lib/vehicle-reservations/hooks";

// Define the validation schema
const reservationSchema = z.object({
  customer: z.object({
    customerId: z.string().min(1, "Customer is required"),
  }),
  location: z.object({
    pickupLocation: z.string().min(1, "Pickup location is required"),
    pickupDate: z.date(),
    pickupTime: z.string().optional(),
    returnLocation: z.string().optional(),
    returnDate: z.date(),
    returnTime: z.string().optional(),
  }),
  vehicle: z.object({
    vehicleClass: z.nativeEnum(VehicleClass),
    vehicleName: z.string().optional(),
    licensePlate: z.string().optional(),
    make: z.string().optional(),
    model: z.string().optional(),
    year: z
      .number()
      .optional()
      .or(
        z.string().transform((val) => (val === "" ? undefined : parseInt(val)))
      ),
    color: z.string().optional(),
  }),
  pricing: z.object({
    dailyRate: z.number().min(0, "Daily rate must be a positive number"),
    totalDays: z.number().min(1, "Total days must be at least 1"),
    totalPrice: z.number().min(0, "Total price must be a positive number"),
    totalPaid: z
      .number()
      .min(0, "Total paid must be a positive number")
      .optional(),
    totalRefunded: z
      .number()
      .min(0, "Total refunded must be a positive number")
      .optional(),
    depositAmount: z
      .number()
      .min(0, "Deposit must be a positive number")
      .optional(),
    discountAmount: z
      .number()
      .min(0, "Discount must be a positive number")
      .optional(),
    taxAmount: z
      .number()
      .min(0, "Tax amount must be a positive number")
      .optional(),
    taxRate: z.number().min(0, "Tax rate must be a positive number").optional(),
  }),
  details: z.object({
    status: z.nativeEnum(ReservationStatus).default(ReservationStatus.PENDING),
    paymentStatus: z.nativeEnum(PaymentStatus).default(PaymentStatus.UNPAID),
    notes: z.string().optional(),
  }),
});

type ReservationFormValues = z.infer<typeof reservationSchema>;

interface VehicleReservationSheetProps {
  isDemo?: boolean;
  onSuccess?: () => void;
  reservationId?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  isUpdate?: boolean;
}

// Create DatePicker component since it doesn't exist
function DatePicker({
  date,
  onSelect,
}: {
  date?: Date;
  onSelect: (date: Date) => void;
}) {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="w-full justify-start text-left font-normal"
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {date ? format(date, "PPP") : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          mode="single"
          selected={date}
          onSelect={(date) => {
            if (date) onSelect(date);
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

export function VehicleReservationSheet({
  isDemo = false,
  onSuccess,
  reservationId,
  open,
  onOpenChange,
  isUpdate = false,
}: VehicleReservationSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;

  const { data: reservation } = useReservationById(reservationId, isDemo);
  const [pickupDate, setPickupDate] = React.useState<Date | undefined>();
  const [returnDate, setReturnDate] = React.useState<Date | undefined>();
  const [selectedVehicleClass, setSelectedVehicleClass] = React.useState<
    VehicleClass | undefined
  >();

  // Get available vehicles based on dates and vehicle class
  const { data: availableVehicles, isLoading: isLoadingVehicles } =
    useAvailableVehicles(pickupDate, returnDate, selectedVehicleClass, isDemo);

  const form = useForm<ReservationFormValues>({
    resolver: zodResolver(reservationSchema),
    defaultValues: {
      customer: {
        customerId: "",
      },
      location: {
        pickupLocation: "",
        pickupDate: new Date(),
        pickupTime: "10:00",
        returnLocation: "",
        returnDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
        returnTime: "10:00",
      },
      vehicle: {
        vehicleClass: VehicleClass.STANDARD,
        vehicleName: "",
        licensePlate: "",
        make: "",
        model: "",
        year: undefined,
        color: "",
      },
      pricing: {
        dailyRate: 0,
        totalDays: 1,
        totalPrice: 0,
        totalPaid: 0,
        totalRefunded: 0,
        depositAmount: 0,
        discountAmount: 0,
        taxAmount: 0,
        taxRate: 0,
      },
      details: {
        status: ReservationStatus.PENDING,
        paymentStatus: PaymentStatus.UNPAID,
        notes: "",
      },
    },
  });

  // Update form when reservation data is available
  React.useEffect(() => {
    if (reservation?.data && isUpdate) {
      const data = reservation.data;

      // Update pickup and return dates for the vehicle availability query
      setPickupDate(new Date(data.locationInfo.pickupDate));
      setReturnDate(new Date(data.locationInfo.returnDate));
      setSelectedVehicleClass(data.vehicleInfo.vehicleClass);

      // Set form values
      form.reset({
        customer: {
          customerId: data.customerId,
        },
        location: {
          pickupLocation: data.locationInfo.pickupLocation,
          pickupDate: new Date(data.locationInfo.pickupDate),
          pickupTime: data.locationInfo.pickupTime || "10:00",
          returnLocation: data.locationInfo.returnLocation || "",
          returnDate: new Date(data.locationInfo.returnDate),
          returnTime: data.locationInfo.returnTime || "10:00",
        },
        vehicle: {
          vehicleClass: data.vehicleInfo.vehicleClass,
          vehicleName: data.vehicleInfo.vehicleName || "",
          licensePlate: data.vehicleInfo.licensePlate || "",
          make: data.vehicleInfo.make || "",
          model: data.vehicleInfo.model || "",
          year: data.vehicleInfo.year,
          color: data.vehicleInfo.color || "",
        },
        pricing: {
          dailyRate: data.pricingInfo.dailyRate,
          totalDays: data.pricingInfo.totalDays,
          totalPrice: data.pricingInfo.totalPrice,
          totalPaid: data.pricingInfo.totalPaid,
          totalRefunded: data.pricingInfo.totalRefunded,
          depositAmount: data.pricingInfo.depositAmount || 0,
          discountAmount: data.pricingInfo.discountAmount || 0,
          taxAmount: data.pricingInfo.taxAmount || 0,
          taxRate: data.pricingInfo.taxRate || 0,
        },
        details: {
          status: data.status,
          paymentStatus: data.paymentStatus,
          notes: data.notes || "",
        },
      });
    }
  }, [reservation, form, isUpdate]);

  // Calculate total price when relevant fields change
  React.useEffect(() => {
    const dailyRate = form.watch("pricing.dailyRate");
    const totalDays = form.watch("pricing.totalDays");
    const discountAmount = form.watch("pricing.discountAmount") || 0;
    const taxAmount = form.watch("pricing.taxAmount") || 0;

    if (dailyRate && totalDays) {
      const subtotal = dailyRate * totalDays;
      const totalPrice = subtotal - discountAmount + taxAmount;
      form.setValue("pricing.totalPrice", totalPrice);
    }
  }, [
    form.watch("pricing.dailyRate"),
    form.watch("pricing.totalDays"),
    form.watch("pricing.discountAmount"),
    form.watch("pricing.taxAmount"),
  ]);

  // Set up a single effect to handle date-related calculations and state updates
  React.useEffect(() => {
    // Register a one-time subscription to watch date fields
    const subscription = form.watch((value, { name, type }) => {
      if (name === "location.pickupDate" || name === "location.returnDate") {
        const pickupDate = form.getValues("location.pickupDate");
        const returnDate = form.getValues("location.returnDate");

        // Only process if both dates exist
        if (pickupDate && returnDate) {
          // Update vehicle availability state
          setPickupDate(pickupDate);
          setReturnDate(returnDate);

          // Calculate total days
          const totalMs = returnDate.getTime() - pickupDate.getTime();
          const totalDays = Math.max(
            1,
            Math.ceil(totalMs / (1000 * 60 * 60 * 24))
          );

          // Only update if the value changed
          const currentTotalDays = form.getValues("pricing.totalDays");
          if (currentTotalDays !== totalDays) {
            form.setValue("pricing.totalDays", totalDays, {
              shouldDirty: true,
            });
          }
        }
      }
    });

    // Initialize the vehicle availability state variables
    const initialPickupDate = form.getValues("location.pickupDate");
    const initialReturnDate = form.getValues("location.returnDate");
    if (initialPickupDate && initialReturnDate) {
      setPickupDate(initialPickupDate);
      setReturnDate(initialReturnDate);

      // Calculate initial total days
      const totalMs = initialReturnDate.getTime() - initialPickupDate.getTime();
      const totalDays = Math.max(1, Math.ceil(totalMs / (1000 * 60 * 60 * 24)));
      const currentTotalDays = form.getValues("pricing.totalDays");
      if (currentTotalDays !== totalDays) {
        form.setValue("pricing.totalDays", totalDays, { shouldDirty: true });
      }
    }

    // Clean up
    return () => subscription.unsubscribe();
  }, [form]); // Only depend on form instance, which is stable

  // Handle vehicle class selection
  const handleVehicleClassChange = (value: string) => {
    const vehicleClass = value as VehicleClass;
    form.setValue("vehicle.vehicleClass", vehicleClass);
    setSelectedVehicleClass(vehicleClass);

    // Reset vehicle details when class changes
    form.setValue("vehicle.vehicleName", "");
    form.setValue("vehicle.licensePlate", "");
    form.setValue("vehicle.make", "");
    form.setValue("vehicle.model", "");
    form.setValue("vehicle.year", undefined);
    form.setValue("vehicle.color", "");
  };

  const handleSubmit = async (data: ReservationFormValues) => {
    try {
      if (isUpdate && reservationId) {
        // Update existing reservation
        const updateData: UpdateVehicleReservationRequest = {
          id: reservationId,
          locationInfo: {
            pickupLocation: data.location.pickupLocation,
            pickupDate: data.location.pickupDate,
            pickupTime: data.location.pickupTime,
            returnLocation:
              data.location.returnLocation || data.location.pickupLocation,
            returnDate: data.location.returnDate,
            returnTime: data.location.returnTime,
          },
          vehicleInfo: {
            vehicleClass: data.vehicle.vehicleClass,
            vehicleName: data.vehicle.vehicleName,
            licensePlate: data.vehicle.licensePlate,
            make: data.vehicle.make,
            model: data.vehicle.model,
            year: data.vehicle.year,
            color: data.vehicle.color,
          },
          pricingInfo: {
            dailyRate: data.pricing.dailyRate,
            totalDays: data.pricing.totalDays,
            totalPrice: data.pricing.totalPrice,
            totalPaid: data.pricing.totalPaid || 0,
            totalRefunded: data.pricing.totalRefunded || 0,
            depositAmount: data.pricing.depositAmount,
            discountAmount: data.pricing.discountAmount,
            taxAmount: data.pricing.taxAmount,
            taxRate: data.pricing.taxRate,
          },
          status: data.details.status,
          paymentStatus: data.details.paymentStatus,
          notes: data.details.notes,
        };

        if (isDemo) {
          const result = await updateDemoReservation(reservationId, updateData);
          if (result.status === ApiStatus.SUCCESS) {
            toast.success("Reservation has been updated successfully");
            onSuccess?.();
          }
        } else {
          const result = await updateReservation(reservationId, updateData);
          if (result.status === ApiStatus.SUCCESS) {
            toast.success("Reservation has been updated successfully");
            onSuccess?.();
          }
        }
      } else {
        // Create new reservation
        const createData: CreateVehicleReservationRequest = {
          customerId: data.customer.customerId,
          locationInfo: {
            pickupLocation: data.location.pickupLocation,
            pickupDate: data.location.pickupDate,
            pickupTime: data.location.pickupTime,
            returnLocation:
              data.location.returnLocation || data.location.pickupLocation,
            returnDate: data.location.returnDate,
            returnTime: data.location.returnTime,
          },
          vehicleInfo: {
            vehicleClass: data.vehicle.vehicleClass,
            vehicleName: data.vehicle.vehicleName,
            licensePlate: data.vehicle.licensePlate,
            make: data.vehicle.make,
            model: data.vehicle.model,
            year: data.vehicle.year,
            color: data.vehicle.color,
          },
          pricingInfo: {
            dailyRate: data.pricing.dailyRate,
            totalDays: data.pricing.totalDays,
            totalPrice: data.pricing.totalPrice,
            totalPaid: data.pricing.totalPaid || 0,
            totalRefunded: data.pricing.totalRefunded || 0,
            depositAmount: data.pricing.depositAmount,
            discountAmount: data.pricing.discountAmount,
            taxAmount: data.pricing.taxAmount,
            taxRate: data.pricing.taxRate,
          },
          status: data.details.status,
          paymentStatus: data.details.paymentStatus,
          notes: data.details.notes,
        };

        if (isDemo) {
          const result = await createDemoReservation(createData);
          if (result.status === ApiStatus.SUCCESS) {
            toast.success("Reservation has been created successfully");
            form.reset();
            onSuccess?.();
          }
        } else {
          const result = await createReservation(createData);
          if (result.status === ApiStatus.SUCCESS) {
            toast.success("Reservation has been created successfully");
            form.reset();
            onSuccess?.();
          }
        }
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "An unexpected error occurred");
    }
  };

  return (
    <BaseSheet<VehicleReservationDetails, ReservationFormValues>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={reservation?.data}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isUpdate}
      title="Reservation"
      sections={[
        {
          id: "customer",
          title: "Customer Information",
          icon: <User className="h-5 w-5" />,
          content: (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="customerId">Customer ID</Label>
                <Input
                  id="customerId"
                  placeholder="Enter customer ID"
                  {...form.register("customer.customerId")}
                />
                {form.formState.errors.customer?.customerId && (
                  <p className="text-sm text-destructive">
                    {form.formState.errors.customer.customerId.message}
                  </p>
                )}
              </div>
            </div>
          ),
        },
        {
          id: "location",
          title: "Location & Dates",
          icon: <MapPin className="h-5 w-5" />,
          content: (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="pickupLocation">Pickup Location</Label>
                <Input
                  id="pickupLocation"
                  placeholder="Enter pickup location"
                  {...form.register("location.pickupLocation")}
                />
                {form.formState.errors.location?.pickupLocation && (
                  <p className="text-sm text-destructive">
                    {form.formState.errors.location.pickupLocation.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="pickupDate">Pickup Date</Label>
                  <DatePicker
                    date={form.watch("location.pickupDate")}
                    onSelect={(date: Date) =>
                      form.setValue("location.pickupDate", date)
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="pickupTime">Pickup Time</Label>
                  <Input
                    id="pickupTime"
                    placeholder="10:00"
                    {...form.register("location.pickupTime")}
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="returnLocation">
                  Return Location (leave blank if same as pickup)
                </Label>
                <Input
                  id="returnLocation"
                  placeholder="Enter return location"
                  {...form.register("location.returnLocation")}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="returnDate">Return Date</Label>
                  <DatePicker
                    date={form.watch("location.returnDate")}
                    onSelect={(date: Date) =>
                      form.setValue("location.returnDate", date)
                    }
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="returnTime">Return Time</Label>
                  <Input
                    id="returnTime"
                    placeholder="10:00"
                    {...form.register("location.returnTime")}
                  />
                </div>
              </div>
            </div>
          ),
        },
        {
          id: "vehicle",
          title: "Vehicle Details",
          icon: <Car className="h-5 w-5" />,
          content: (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="vehicleClass">Vehicle Class</Label>
                <Select
                  onValueChange={handleVehicleClassChange}
                  defaultValue={form.watch("vehicle.vehicleClass")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select vehicle class" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(VehicleClass).map((vehicleClass) => (
                      <SelectItem key={vehicleClass} value={vehicleClass}>
                        {vehicleClass.replace("_", " ")}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.vehicle?.vehicleClass && (
                  <p className="text-sm text-destructive">
                    {form.formState.errors.vehicle.vehicleClass.message}
                  </p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="vehicleName">Vehicle Name</Label>
                <Input
                  id="vehicleName"
                  placeholder="Enter vehicle name"
                  {...form.register("vehicle.vehicleName")}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="make">Make</Label>
                  <Input
                    id="make"
                    placeholder="Enter make"
                    {...form.register("vehicle.make")}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="model">Model</Label>
                  <Input
                    id="model"
                    placeholder="Enter model"
                    {...form.register("vehicle.model")}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="year">Year</Label>
                  <Input
                    id="year"
                    placeholder="Enter year"
                    {...form.register("vehicle.year")}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="color">Color</Label>
                  <Input
                    id="color"
                    placeholder="Enter color"
                    {...form.register("vehicle.color")}
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="licensePlate">License Plate</Label>
                <Input
                  id="licensePlate"
                  placeholder="Enter license plate"
                  {...form.register("vehicle.licensePlate")}
                />
              </div>
            </div>
          ),
        },
        {
          id: "pricing",
          title: "Pricing Information",
          icon: <CreditCard className="h-5 w-5" />,
          content: (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="dailyRate">Daily Rate</Label>
                  <Input
                    id="dailyRate"
                    type="number"
                    min="0"
                    placeholder="Enter daily rate"
                    {...form.register("pricing.dailyRate", {
                      valueAsNumber: true,
                    })}
                  />
                  {form.formState.errors.pricing?.dailyRate && (
                    <p className="text-sm text-destructive">
                      {form.formState.errors.pricing.dailyRate.message}
                    </p>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="totalDays">Total Days</Label>
                  <Input
                    id="totalDays"
                    type="number"
                    min="1"
                    disabled
                    {...form.register("pricing.totalDays", {
                      valueAsNumber: true,
                    })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="discountAmount">Discount Amount</Label>
                  <Input
                    id="discountAmount"
                    type="number"
                    min="0"
                    placeholder="Enter discount"
                    {...form.register("pricing.discountAmount", {
                      valueAsNumber: true,
                    })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="taxAmount">Tax Amount</Label>
                  <Input
                    id="taxAmount"
                    type="number"
                    min="0"
                    placeholder="Enter tax amount"
                    {...form.register("pricing.taxAmount", {
                      valueAsNumber: true,
                    })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="depositAmount">Deposit Amount</Label>
                  <Input
                    id="depositAmount"
                    type="number"
                    min="0"
                    placeholder="Enter deposit"
                    {...form.register("pricing.depositAmount", {
                      valueAsNumber: true,
                    })}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="totalPrice">Total Price</Label>
                  <Input
                    id="totalPrice"
                    type="number"
                    disabled
                    {...form.register("pricing.totalPrice", {
                      valueAsNumber: true,
                    })}
                  />
                </div>
              </div>

              {isUpdate && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="totalPaid">Total Paid</Label>
                    <Input
                      id="totalPaid"
                      type="number"
                      min="0"
                      placeholder="Enter amount paid"
                      {...form.register("pricing.totalPaid", {
                        valueAsNumber: true,
                      })}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="totalRefunded">Total Refunded</Label>
                    <Input
                      id="totalRefunded"
                      type="number"
                      min="0"
                      placeholder="Enter amount refunded"
                      {...form.register("pricing.totalRefunded", {
                        valueAsNumber: true,
                      })}
                    />
                  </div>
                </div>
              )}
            </div>
          ),
        },
        {
          id: "details",
          title: "Reservation Details",
          icon: <Info className="h-5 w-5" />,
          content: (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="status">Reservation Status</Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue(
                        "details.status",
                        value as ReservationStatus
                      )
                    }
                    defaultValue={form.watch("details.status")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ReservationStatus).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.replace("_", " ")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="paymentStatus">Payment Status</Label>
                  <Select
                    onValueChange={(value) =>
                      form.setValue(
                        "details.paymentStatus",
                        value as PaymentStatus
                      )
                    }
                    defaultValue={form.watch("details.paymentStatus")}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select payment status" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(PaymentStatus).map((status) => (
                        <SelectItem key={status} value={status}>
                          {status.replace("_", " ")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Enter any additional notes"
                  className="min-h-[120px]"
                  {...form.register("details.notes")}
                />
              </div>
            </div>
          ),
        },
      ]}
      onSubmit={async () => {
        await form.handleSubmit(handleSubmit)();
      }}
      formRef={formRef}
    />
  );
}
