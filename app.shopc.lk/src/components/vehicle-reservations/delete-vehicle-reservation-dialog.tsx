"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteReservation } from "@/lib/vehicle-reservations/api";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import {
  reservationKeys,
  useReservationById,
} from "@/lib/vehicle-reservations/hooks";
import { deleteDemoReservation } from "@/lib/vehicle-reservations/demo";

interface DeleteVehicleReservationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  reservationId: string;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteVehicleReservationDialog({
  open,
  onOpenChange,
  reservationId,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteVehicleReservationDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);
  const queryClient = useQueryClient();

  // Optionally fetch reservation details for display purposes
  const { data: reservationData } = useReservationById(reservationId, isDemo);

  const reservationNumber =
    reservationData?.data?.reservationNumber || reservationId;

  const handleDelete = React.useCallback(async () => {
    if (!reservationId) return;

    setIsDeleting(true);

    try {
      if (isDemo) {
        // Simulate API call delay for demo
        await deleteDemoReservation(reservationId);
      } else {
        // Call the actual API to delete the reservation
        const result = await deleteReservation(reservationId);

        if (result.status !== "SUCCESS") {
          throw new Error(result.message || "Failed to delete reservation");
        }
      }

      // Invalidate queries to refresh the UI
      queryClient.invalidateQueries({ queryKey: reservationKeys.all });

      // Show success toast
      toast.success("Reservation has been deleted successfully");

      // Call onSuccess callback
      onSuccess?.();

      // Close the dialog
      onOpenChange(false);
    } catch (error) {
      toast.error("Error deleting reservation: " + (error instanceof Error ? error.message : "An unexpected error occurred"));
    } finally {
      setIsDeleting(false);
    }
  }, [reservationId, isDemo, onSuccess, onOpenChange, queryClient]);

  if (!reservationId) {
    return null;
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Are you sure you want to delete this reservation?
          </AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the reservation "{reservationNumber}"
            and all associated data. This action cannot be undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            className="bg-destructive text-destructive-foreground"
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
