{"name": "e9-next-jwt-admin-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.7.0", "@hookform/resolvers": "^3.9.1", "@number-flow/react": "^0.5.8", "@prisma/client": "^6.3.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@t3-oss/env-nextjs": "^0.11.1", "@tabler/icons-react": "^3.31.0", "@tanstack/react-query": "^5.67.2", "@tanstack/react-query-devtools": "^5.67.2", "@tanstack/react-table": "^8.20.5", "@tensorflow/tfjs": "^4.22.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-blockquote": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/extension-youtube": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/js-cookie": "^3.0.6", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.13", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "deepmerge": "^4.3.1", "embla-carousel-auto-scroll": "^8.5.2", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.5.2", "emblor": "^1.4.7", "file-saver": "^2.0.5", "framer-motion": "^12.6.3", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "i18n-iso-countries": "^7.14.0", "immer": "^10.1.1", "input-otp": "^1.4.2", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libphonenumber-js": "^1.12.6", "lucide-react": "^0.488.0", "motion": "^12.5.0", "next": "15.1.0", "next-contentlayer2": "^0.5.7", "next-intl": "^4.0.2", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nuqs": "^2.2.3", "papaparse": "^5.5.2", "query-string": "^9.1.1", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-confetti": "^6.4.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.0", "react-medium-image-zoom": "^5.2.14", "react-phone-number-input": "^3.4.12", "react-resizable-panels": "^2.1.7", "react-use-measure": "^2.1.7", "recharts": "^2.15.3", "sonner": "^1.7.1", "styled-components": "^6.1.15", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.1", "xlsx": "^0.18.5", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/canvas-confetti": "^1.9.0", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/papaparse": "^5.3.15", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.0", "postcss": "^8", "prisma": "^6.3.1", "tailwindcss": "^3.4.1", "typescript": "^5"}}