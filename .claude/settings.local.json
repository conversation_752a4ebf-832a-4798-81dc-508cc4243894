{"permissions": {"allow": ["Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(yarn lint)", "Bash(npx tsc:*)", "Bash(yarn build)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(yarn lint:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(timeout 30 yarn dev)", "Bash(yarn db:generate:*)", "Bash(yarn db:migrate:*)", "Bash(yarn db:push:*)", "Bash(grep:*)", "Bash(ls:*)", "Bash(npm run lint:*)", "Bash(npm run build:*)", "Bash(rg:*)", "<PERSON><PERSON>(sed:*)", "Bash(for file in *.tsx)", "Bash(do sed -i '' 's/serviceType/serviceCategory/g' \"$file\")", "Bash(done)", "Bash(npx eslint:*)", "Bash(yarn add:*)", "Bash(yarn format:*)", "Bash(node:*)", "Bash(for file in locations.schema.ts media.schema.ts meetings.schema.ts modifier-groups.schema.ts modifiers.schema.ts)", "Bash(do sed -i '' 's/import { createBaseEntityBusinessFields } from/import {\\n  createBaseEntityBusinessFields,\\n  createBaseEntityBusinessIndexes,\\n} from/' \"$file\")", "Bash(do echo \"=== $file ===\")", "Bash(for file in service-time-slots.schema.ts service-order-types.schema.ts service-order-statuses.schema.ts service-order-priorities.schema.ts)", "Bash(for file in restaurant-time-slots.schema.ts restaurant-menu-items.schema.ts referrals.schema.ts recurring-activities.schema.ts purchasing.schema.ts providers.schema.ts promo-codes.schema.ts projects.schema.ts)", "Bash(git checkout:*)", "Bash(# Update specific table patterns in purchasing.schema.ts\nsed -i '''' ''\ns/businessIdIndex: index(''\\''''purchase_requisitions_business_id_index''\\'''').on(/...createBaseEntityBusinessIndexes(t, ''\\''''purchase_requisitions''\\''''),\\n    _placeholder: index(''\\''''xxx''\\'''').on(/\ns/businessIdIndex: index(''\\''''purchase_requisition_lines_business_id_index''\\'''').on(/...createBaseEntityBusinessIndexes(t, ''\\''''purchase_requisition_lines''\\''''),\\n    _placeholder: index(''\\''''xxx''\\'''').on(/\ns/businessIdIndex: index(''\\''''purchase_quotations_business_id_index''\\'''').on(/...createBaseEntityBusinessIndexes(t, ''\\''''purchase_quotations''\\''''),\\n    _placeholder: index(''\\''''xxx''\\'''').on(/\ns/businessIdIndex: index(''\\''''purchase_quotation_lines_business_id_index''\\'''').on(/...createBaseEntityBusinessIndexes(t, ''\\''''purchase_quotation_lines''\\''''),\\n    _placeholder: index(''\\''''xxx''\\'''').on(/\ns/businessIdIndex: index(''\\''''purchase_orders_business_id_index''\\'''').on(/...createBaseEntityBusinessIndexes(t, ''\\''''purchase_orders''\\''''),\\n    _placeholder: index(''\\''''xxx''\\'''').on(/\n'' /Users/<USER>/Documents/e9-projects/shopc/apps/api-bizc-app/src/app/drizzle/schema/purchasing.schema.ts)", "Bash(# Remove placeholder lines\nsed -i '''' ''/.*_placeholder: index(''\\''''xxx''\\'''').on.*/d'' /Users/<USER>/Documents/e9-projects/shopc/apps/api-bizc-app/src/app/drizzle/schema/purchasing.schema.ts)", "Bash(cp:*)", "Bash(yarn eslint:*)", "Bash(yarn test:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(yarn start:dev)", "Bash(/tmp/update_schema_files.sh:*)", "Bash(for file in modules.schema.ts packages.schema.ts payment-methods.schema.ts rental-items.schema.ts payment-account-types.schema.ts taxes.schema.ts product-racks.schema.ts payment-accounts.schema.ts notifications.schema.ts media-array.schema.ts performance-reviews.schema.ts suppliers.schema.ts)", "Bash(do echo \"Processing $file\")", "<PERSON><PERSON>(comm:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(pkill:*)", "Bash(for:*)", "Bash(do echo \"Processing $file...\")", "Bash(npm install:*)", "<PERSON><PERSON>(python:*)", "Bash(psql:*)", "Bash(yarn db:studio:*)", "<PERSON><PERSON>(timeout 10 yarn start:dev)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(touch:*)", "Bash(do mv \"$file\" \"$file//media-array/media-association\")"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": []}