import { Module, Global, DynamicModule } from '@nestjs/common';
import { AppLoggerService } from './logger.service';

@Global()
@Module({})
export class LoggerModule {
  static forRoot(): DynamicModule {
    return {
      module: LoggerModule,
      providers: [AppLoggerService],
      exports: [AppLoggerService],
    };
  }

  static forFeature(context: string): DynamicModule {
    return {
      module: LoggerModule,
      providers: [
        {
          provide: `LOGGER_SERVICE_${context.toUpperCase()}`,
          useFactory: () => {
            return AppLoggerService.create(context);
          },
        },
      ],
      exports: [`LOGGER_SERVICE_${context.toUpperCase()}`],
    };
  }
}
