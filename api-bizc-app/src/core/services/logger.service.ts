import { Injectable, Logger, LogLevel } from '@nestjs/common';

export interface LoggerConfig {
  context?: string;
  timestamp?: boolean;
  logLevels?: LogLevel[];
}

@Injectable()
export class AppLoggerService {
  private logger: Logger;
  private context: string;

  constructor() {
    this.context = 'Application';
    this.logger = new Logger(this.context);
  }

  /**
   * Set the context for this logger instance
   */
  setContext(context: string): void {
    this.context = context;
    // Create a new logger instance with the updated context
    this.logger = new Logger(context);
  }

  /**
   * Create a new logger instance with a specific context
   */
  static create(context: string): AppLoggerService {
    const logger = new AppLoggerService();
    logger.setContext(context);
    return logger;
  }

  /**
   * Log a message at the verbose level
   */
  verbose(message: any, context?: string): void {
    this.logger.verbose(message, context || this.context);
  }

  /**
   * Log a message at the debug level
   */
  debug(message: any, context?: string): void {
    this.logger.debug(message, context || this.context);
  }

  /**
   * Log a message at the log level
   */
  log(message: any, context?: string): void {
    this.logger.log(message, context || this.context);
  }

  /**
   * Log a message at the warn level
   */
  warn(message: any, context?: string): void {
    this.logger.warn(message, context || this.context);
  }

  /**
   * Log a message at the error level
   */
  error(message: any, trace?: string, context?: string): void {
    this.logger.error(message, trace, context || this.context);
  }

  /**
   * Log a message at the fatal level
   */
  fatal(message: any, trace?: string, context?: string): void {
    this.logger.fatal(message, trace, context || this.context);
  }

  /**
   * Log method entry with optional parameters
   */
  logMethodEntry(methodName: string, params?: any): void {
    const paramString = params ? ` with params: ${JSON.stringify(params)}` : '';
    this.debug(`Entering ${methodName}${paramString}`);
  }

  /**
   * Log method exit with optional result
   */
  logMethodExit(methodName: string, result?: any): void {
    const resultString = result
      ? ` with result: ${JSON.stringify(result)}`
      : '';
    this.debug(`Exiting ${methodName}${resultString}`);
  }

  /**
   * Log database operation
   */
  logDatabaseOperation(
    operation: string,
    table: string,
    condition?: any,
  ): void {
    const conditionString = condition
      ? ` where ${JSON.stringify(condition)}`
      : '';
    this.debug(`Database ${operation} on ${table}${conditionString}`);
  }

  /**
   * Log API request
   */
  logApiRequest(method: string, url: string, userId?: string): void {
    const userString = userId ? ` by user ${userId}` : '';
    this.log(`${method} ${url}${userString}`);
  }

  /**
   * Log API response
   */
  logApiResponse(
    method: string,
    url: string,
    statusCode: number,
    duration?: number,
  ): void {
    const durationString = duration ? ` in ${duration}ms` : '';
    this.log(`${method} ${url} - ${statusCode}${durationString}`);
  }

  /**
   * Log business operation
   */
  logBusinessOperation(
    operation: string,
    entityType: string,
    entityId: string,
    userId?: string,
  ): void {
    const userString = userId ? ` by user ${userId}` : '';
    this.log(
      `Business operation: ${operation} ${entityType} ${entityId}${userString}`,
    );
  }

  /**
   * Log authentication event
   */
  logAuthEvent(event: string, userId?: string, details?: any): void {
    const userString = userId ? ` for user ${userId}` : '';
    const detailsString = details ? ` - ${JSON.stringify(details)}` : '';
    this.log(`Auth event: ${event}${userString}${detailsString}`);
  }

  /**
   * Log security event
   */
  logSecurityEvent(event: string, details?: any): void {
    const detailsString = details ? ` - ${JSON.stringify(details)}` : '';
    this.warn(`Security event: ${event}${detailsString}`);
  }

  /**
   * Log performance metric
   */
  logPerformance(
    operation: string,
    duration: number,
    threshold?: number,
  ): void {
    const message = `Performance: ${operation} took ${duration}ms`;
    if (threshold && duration > threshold) {
      this.warn(`${message} (exceeded threshold of ${threshold}ms)`);
    } else {
      this.debug(message);
    }
  }

  /**
   * Log validation error
   */
  logValidationError(field: string, value: any, rule: string): void {
    this.error(
      `Validation error: ${field} with value "${value}" failed rule "${rule}"`,
    );
  }

  /**
   * Log external service call
   */
  logExternalServiceCall(
    service: string,
    operation: string,
    success: boolean,
    duration?: number,
  ): void {
    const status = success ? 'SUCCESS' : 'FAILED';
    const durationString = duration ? ` in ${duration}ms` : '';
    this.log(
      `External service: ${service}.${operation} - ${status}${durationString}`,
    );
  }
}
