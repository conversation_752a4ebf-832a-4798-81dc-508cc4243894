import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetRepairOrderDto } from './dto/create-asset-repair-order.dto';
import { UpdateAssetRepairOrderDto } from './dto/update-asset-repair-order.dto';
import { AssetRepairOrderDto } from './dto/asset-repair-order.dto';
import { AssetRepairOrderSlimDto } from './dto/asset-repair-order-slim.dto';
import { AssetRepairOrderListDto } from './dto/asset-repair-order-list.dto';
import { assetRepairOrders } from '../drizzle/schema/asset-repair-orders.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { paymentMethods } from '../drizzle/schema/payment-methods.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { RepairStatus, RepairPriority, RepairType } from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MediaService } from '../media/media.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class AssetRepairOrdersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetRepairOrderDto: CreateAssetRepairOrderDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate asset exists and belongs to business
      const asset = await this.db
        .select()
        .from(assets)
        .where(
          and(
            eq(assets.id, createAssetRepairOrderDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!asset) {
        throw new BadRequestException('Asset not found or access denied');
      }

      // Validate staff members exist
      const reportedByStaff = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createAssetRepairOrderDto.reportedBy),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!reportedByStaff) {
        throw new BadRequestException('Reported by staff member not found');
      }

      if (createAssetRepairOrderDto.assignedTo) {
        const assignedToStaff = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createAssetRepairOrderDto.assignedTo),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!assignedToStaff) {
          throw new BadRequestException('Assigned to staff member not found');
        }
      }

      // Validate expense account exists
      const expenseAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, createAssetRepairOrderDto.expenseAccountId),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!expenseAccount) {
        throw new BadRequestException('Expense account not found');
      }

      // Generate repair order number
      const repairOrderNumber =
        await this.generateRepairOrderNumber(businessId);

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        await this.mediaService.uploadMultipleMediaWithReference(
          attachmentFiles,
          'asset-repair-orders/attachments',
          businessId,
          userId,
          'asset-repair-order',
        );
      }

      // Create the repair order
      const newRepairOrder = await this.db.transaction(async (tx) => {
        const [repairOrder] = await tx
          .insert(assetRepairOrders)
          .values({
            businessId,
            assetId: createAssetRepairOrderDto.assetId,
            repairOrderNumber,
            title: createAssetRepairOrderDto.title,
            problemDescription: createAssetRepairOrderDto.problemDescription,
            diagnosisNotes: createAssetRepairOrderDto.diagnosisNotes,
            repairSolution: createAssetRepairOrderDto.repairSolution,
            repairType:
              createAssetRepairOrderDto.repairType ?? RepairType.CORRECTIVE,
            status: createAssetRepairOrderDto.status ?? RepairStatus.REPORTED,
            priority:
              createAssetRepairOrderDto.priority ?? RepairPriority.NORMAL,
            targetCompletionDate: createAssetRepairOrderDto.targetCompletionDate
              ? new Date(createAssetRepairOrderDto.targetCompletionDate)
              : undefined,
            actualStartDate: createAssetRepairOrderDto.actualStartDate
              ? new Date(createAssetRepairOrderDto.actualStartDate)
              : undefined,
            completedAt: createAssetRepairOrderDto.completedAt
              ? new Date(createAssetRepairOrderDto.completedAt)
              : undefined,
            completionNotes: createAssetRepairOrderDto.completionNotes,
            reportedBy: createAssetRepairOrderDto.reportedBy,
            assignedTo: createAssetRepairOrderDto.assignedTo,
            supplierId: createAssetRepairOrderDto.supplierId,
            isWarrantyRepair:
              createAssetRepairOrderDto.isWarrantyRepair ?? false,
            warrantyClaimNumber: createAssetRepairOrderDto.warrantyClaimNumber,
            expenseAccountId: createAssetRepairOrderDto.expenseAccountId,
            paymentAccountId: createAssetRepairOrderDto.paymentAccountId,
            paymentDate: createAssetRepairOrderDto.paymentDate,
            paymentMethodId: createAssetRepairOrderDto.paymentMethodId,
            paymentReferenceNumber:
              createAssetRepairOrderDto.paymentReferenceNumber,
            amountType: createAssetRepairOrderDto.amountType,
            subtotal: createAssetRepairOrderDto.subtotal ?? '0.00',
            total: createAssetRepairOrderDto.total ?? '0.00',
            taxId: createAssetRepairOrderDto.taxId,
            qualityCheckBy: createAssetRepairOrderDto.qualityCheckBy,
            createdBy: userId,
          })
          .returning();

        return repairOrder;
      });

      // Log the repair order creation activity
      await this.activityLogService.logCreate(
        newRepairOrder.id,
        EntityType.ASSET_REPAIR_ORDER,
        userId,
        businessId,
        {
          reason: `Asset repair order "${createAssetRepairOrderDto.title}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newRepairOrder.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create asset repair order: ${error.message}`,
      );
    }
  }

  private async generateRepairOrderNumber(businessId: string): Promise<string> {
    // Get the count of existing repair orders for this business
    const countResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetRepairOrders)
      .where(eq(assetRepairOrders.businessId, businessId));

    const count = Number(countResult[0].count) + 1;
    const currentYear = new Date().getFullYear();

    return `ARO-${currentYear}-${count.toString().padStart(6, '0')}`;
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: AssetRepairOrderDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(assetRepairOrders.isDeleted, false),
      eq(assetRepairOrders.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetRepairOrders.reportedAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetRepairOrders.reportedAt, toDate));
      }
    }

    // Find all repair orders for the user's active business with pagination
    const result = await this.db
      .select()
      .from(assetRepairOrders)
      .where(and(...whereConditions))
      .orderBy(desc(assetRepairOrders.reportedAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetRepairOrders)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: await Promise.all(
        result.map((repairOrder) => this.mapToAssetRepairOrderDto(repairOrder)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    title?: string,
    status?: string,
    priority?: string,
    repairType?: string,
    assetName?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetRepairOrderListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetRepairOrders.isDeleted, false),
      eq(assetRepairOrders.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetRepairOrders.reportedAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetRepairOrders.reportedAt, toDate));
      }
    }

    // Add title filtering if provided
    if (title) {
      whereConditions.push(ilike(assetRepairOrders.title, `%${title}%`));
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as RepairStatus);
      whereConditions.push(inArray(assetRepairOrders.status, statusArray));
    }

    // Add priority filtering if provided
    if (priority) {
      const decodedPriority = decodeURIComponent(priority);
      const priorityArray = decodedPriority
        .split(',')
        .map((s) => s.trim() as RepairPriority);
      whereConditions.push(inArray(assetRepairOrders.priority, priorityArray));
    }

    // Add repair type filtering if provided
    if (repairType) {
      const decodedRepairType = decodeURIComponent(repairType);
      const repairTypeArray = decodedRepairType
        .split(',')
        .map((s) => s.trim() as RepairType);
      whereConditions.push(
        inArray(assetRepairOrders.repairType, repairTypeArray),
      );
    }

    // Add asset name filtering if provided
    if (assetName) {
      // This will be handled in the join query below
    }

    // Parse and apply additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions: any[] = [];

        for (const filter of parsedFilters) {
          if (filter.field && filter.value !== undefined) {
            switch (filter.field) {
              case 'title':
                filterConditions.push(
                  ilike(assetRepairOrders.title, `%${filter.value}%`),
                );
                break;
              case 'status':
                filterConditions.push(
                  eq(assetRepairOrders.status, filter.value),
                );
                break;
              case 'priority':
                filterConditions.push(
                  eq(assetRepairOrders.priority, filter.value),
                );
                break;
              case 'repairType':
                filterConditions.push(
                  eq(assetRepairOrders.repairType, filter.value),
                );
                break;
              case 'isWarrantyRepair':
                filterConditions.push(
                  eq(assetRepairOrders.isWarrantyRepair, filter.value),
                );
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions following categories pattern
    // Default sort: reportedAt descending, then by ID for consistent pagination
    let orderBy = [
      desc(assetRepairOrders.reportedAt),
      asc(assetRepairOrders.id),
    ];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.field) {
            case 'reportedAt':
              orderBy = isDesc
                ? [
                    desc(assetRepairOrders.reportedAt),
                    desc(assetRepairOrders.id),
                  ]
                : [
                    asc(assetRepairOrders.reportedAt),
                    asc(assetRepairOrders.id),
                  ];
              break;
            case 'title':
              orderBy = isDesc
                ? [desc(assetRepairOrders.title), desc(assetRepairOrders.id)]
                : [asc(assetRepairOrders.title), asc(assetRepairOrders.id)];
              break;
            case 'status':
              orderBy = isDesc
                ? [desc(assetRepairOrders.status), desc(assetRepairOrders.id)]
                : [asc(assetRepairOrders.status), asc(assetRepairOrders.id)];
              break;
            case 'priority':
              orderBy = isDesc
                ? [desc(assetRepairOrders.priority), desc(assetRepairOrders.id)]
                : [asc(assetRepairOrders.priority), asc(assetRepairOrders.id)];
              break;
            case 'targetCompletionDate':
              orderBy = isDesc
                ? [
                    desc(assetRepairOrders.targetCompletionDate),
                    desc(assetRepairOrders.id),
                  ]
                : [
                    asc(assetRepairOrders.targetCompletionDate),
                    asc(assetRepairOrders.id),
                  ];
              break;
            default:
              orderBy = [
                desc(assetRepairOrders.reportedAt),
                asc(assetRepairOrders.id),
              ];
          }
        }
      } catch {
        // Invalid JSON, use default sort
        orderBy = [
          desc(assetRepairOrders.reportedAt),
          asc(assetRepairOrders.id),
        ];
      }
    }

    // Add asset name filtering to where conditions if provided
    if (assetName) {
      whereConditions.push(ilike(assets.name, `%${assetName}%`));
    }

    // Execute query with joins for related data
    const result = await this.db
      .select({
        repairOrder: assetRepairOrders,
        asset: {
          name: assets.name,
          assetCode: assets.assetCode,
        },
        reportedByStaff: {
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        },
      })
      .from(assetRepairOrders)
      .leftJoin(assets, eq(assetRepairOrders.assetId, assets.id))
      .leftJoin(staffMembers, eq(assetRepairOrders.reportedBy, staffMembers.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetRepairOrders)
      .leftJoin(assets, eq(assetRepairOrders.assetId, assets.id))
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: await Promise.all(
        result.map((row) =>
          this.mapToAssetRepairOrderListDto(
            row.repairOrder,
            row.asset,
            row.reportedByStaff,
          ),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    businessId: string | null,
    id: string,
  ): Promise<AssetRepairOrderDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const repairOrder = await this.db
      .select()
      .from(assetRepairOrders)
      .where(
        and(
          eq(assetRepairOrders.id, id),
          eq(assetRepairOrders.businessId, businessId),
          eq(assetRepairOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!repairOrder) {
      throw new NotFoundException('Asset repair order not found');
    }

    // Disable view logging for performance

    return await this.mapToAssetRepairOrderDto(repairOrder);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetRepairOrderDto: UpdateAssetRepairOrderDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetRepairOrderDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if repair order exists and belongs to business
      const existingRepairOrder = await this.db
        .select()
        .from(assetRepairOrders)
        .where(
          and(
            eq(assetRepairOrders.id, id),
            eq(assetRepairOrders.businessId, businessId),
            eq(assetRepairOrders.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingRepairOrder) {
        throw new NotFoundException('Asset repair order not found');
      }

      // Validate asset if being updated
      if (updateAssetRepairOrderDto.assetId) {
        const asset = await this.db
          .select()
          .from(assets)
          .where(
            and(
              eq(assets.id, updateAssetRepairOrderDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!asset) {
          throw new BadRequestException('Asset not found or access denied');
        }
      }

      // Validate staff members if being updated
      if (updateAssetRepairOrderDto.reportedBy) {
        const reportedByStaff = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateAssetRepairOrderDto.reportedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!reportedByStaff) {
          throw new BadRequestException('Reported by staff member not found');
        }
      }

      if (updateAssetRepairOrderDto.assignedTo) {
        const assignedToStaff = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateAssetRepairOrderDto.assignedTo),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!assignedToStaff) {
          throw new BadRequestException('Assigned to staff member not found');
        }
      }

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        await this.mediaService.uploadMultipleMediaWithReference(
          attachmentFiles,
          'asset-repair-orders/attachments',
          businessId,
          userId,
          'asset-repair-order',
        );
      }

      // Update the repair order
      const updatedRepairOrder = await this.db.transaction(async (tx) => {
        const updateData: any = {
          updatedAt: new Date(),
        };

        // Only update fields that are provided
        if (updateAssetRepairOrderDto.assetId !== undefined) {
          updateData.assetId = updateAssetRepairOrderDto.assetId;
        }
        if (updateAssetRepairOrderDto.title !== undefined) {
          updateData.title = updateAssetRepairOrderDto.title;
        }
        if (updateAssetRepairOrderDto.problemDescription !== undefined) {
          updateData.problemDescription =
            updateAssetRepairOrderDto.problemDescription;
        }
        if (updateAssetRepairOrderDto.diagnosisNotes !== undefined) {
          updateData.diagnosisNotes = updateAssetRepairOrderDto.diagnosisNotes;
        }
        if (updateAssetRepairOrderDto.repairSolution !== undefined) {
          updateData.repairSolution = updateAssetRepairOrderDto.repairSolution;
        }
        if (updateAssetRepairOrderDto.repairType !== undefined) {
          updateData.repairType = updateAssetRepairOrderDto.repairType;
        }
        if (updateAssetRepairOrderDto.status !== undefined) {
          updateData.status = updateAssetRepairOrderDto.status;
        }
        if (updateAssetRepairOrderDto.priority !== undefined) {
          updateData.priority = updateAssetRepairOrderDto.priority;
        }
        if (updateAssetRepairOrderDto.targetCompletionDate !== undefined) {
          updateData.targetCompletionDate =
            updateAssetRepairOrderDto.targetCompletionDate
              ? new Date(updateAssetRepairOrderDto.targetCompletionDate)
              : null;
        }
        if (updateAssetRepairOrderDto.actualStartDate !== undefined) {
          updateData.actualStartDate = updateAssetRepairOrderDto.actualStartDate
            ? new Date(updateAssetRepairOrderDto.actualStartDate)
            : null;
        }
        if (updateAssetRepairOrderDto.completedAt !== undefined) {
          updateData.completedAt = updateAssetRepairOrderDto.completedAt
            ? new Date(updateAssetRepairOrderDto.completedAt)
            : null;
        }
        if (updateAssetRepairOrderDto.completionNotes !== undefined) {
          updateData.completionNotes =
            updateAssetRepairOrderDto.completionNotes;
        }
        if (updateAssetRepairOrderDto.reportedBy !== undefined) {
          updateData.reportedBy = updateAssetRepairOrderDto.reportedBy;
        }
        if (updateAssetRepairOrderDto.assignedTo !== undefined) {
          updateData.assignedTo = updateAssetRepairOrderDto.assignedTo;
        }
        if (updateAssetRepairOrderDto.supplierId !== undefined) {
          updateData.supplierId = updateAssetRepairOrderDto.supplierId;
        }
        if (updateAssetRepairOrderDto.isWarrantyRepair !== undefined) {
          updateData.isWarrantyRepair =
            updateAssetRepairOrderDto.isWarrantyRepair;
        }
        if (updateAssetRepairOrderDto.warrantyClaimNumber !== undefined) {
          updateData.warrantyClaimNumber =
            updateAssetRepairOrderDto.warrantyClaimNumber;
        }
        if (updateAssetRepairOrderDto.expenseAccountId !== undefined) {
          updateData.expenseAccountId =
            updateAssetRepairOrderDto.expenseAccountId;
        }
        if (updateAssetRepairOrderDto.paymentAccountId !== undefined) {
          updateData.paymentAccountId =
            updateAssetRepairOrderDto.paymentAccountId;
        }
        if (updateAssetRepairOrderDto.paymentDate !== undefined) {
          updateData.paymentDate = updateAssetRepairOrderDto.paymentDate;
        }
        if (updateAssetRepairOrderDto.paymentMethodId !== undefined) {
          updateData.paymentMethodId =
            updateAssetRepairOrderDto.paymentMethodId;
        }
        if (updateAssetRepairOrderDto.paymentReferenceNumber !== undefined) {
          updateData.paymentReferenceNumber =
            updateAssetRepairOrderDto.paymentReferenceNumber;
        }
        if (updateAssetRepairOrderDto.amountType !== undefined) {
          updateData.amountType = updateAssetRepairOrderDto.amountType;
        }
        if (updateAssetRepairOrderDto.subtotal !== undefined) {
          updateData.subtotal = updateAssetRepairOrderDto.subtotal;
        }
        if (updateAssetRepairOrderDto.total !== undefined) {
          updateData.total = updateAssetRepairOrderDto.total;
        }
        if (updateAssetRepairOrderDto.taxId !== undefined) {
          updateData.taxId = updateAssetRepairOrderDto.taxId;
        }
        if (updateAssetRepairOrderDto.qualityCheckBy !== undefined) {
          updateData.qualityCheckBy = updateAssetRepairOrderDto.qualityCheckBy;
        }

        const [repairOrder] = await tx
          .update(assetRepairOrders)
          .set(updateData)
          .where(
            and(
              eq(assetRepairOrders.id, id),
              eq(assetRepairOrders.businessId, businessId),
              eq(assetRepairOrders.isDeleted, false),
            ),
          )
          .returning();

        return repairOrder;
      });

      // Log the repair order update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ASSET_REPAIR_ORDER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToAssetRepairOrderDto(updatedRepairOrder);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update asset repair order: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the repair order
    const repairOrder = await this.db
      .select()
      .from(assetRepairOrders)
      .where(
        and(
          eq(assetRepairOrders.id, id),
          eq(assetRepairOrders.businessId, businessId),
          eq(assetRepairOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!repairOrder) {
      throw new NotFoundException('Asset repair order not found');
    }

    // Soft delete the repair order
    await this.db
      .update(assetRepairOrders)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(assetRepairOrders.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ASSET_REPAIR_ORDER,
      userId,
      businessId,
      {
        reason: `Asset repair order "${repairOrder.title}" was deleted`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Asset repair order with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    assetRepairOrderIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedCount: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetRepairOrderIds || assetRepairOrderIds.length === 0) {
        throw new BadRequestException(
          'No asset repair order IDs provided for deletion',
        );
      }

      // Get all repair orders that exist and belong to the business
      const existingRepairOrders = await this.db
        .select({
          id: assetRepairOrders.id,
          title: assetRepairOrders.title,
          businessId: assetRepairOrders.businessId,
        })
        .from(assetRepairOrders)
        .where(
          and(
            inArray(assetRepairOrders.id, assetRepairOrderIds),
            eq(assetRepairOrders.businessId, businessId),
            eq(assetRepairOrders.isDeleted, false),
          ),
        );

      if (existingRepairOrders.length === 0) {
        throw new NotFoundException(
          'No valid asset repair orders found for deletion',
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const repairOrder of existingRepairOrders) {
          // Soft delete the repair order
          await tx
            .update(assetRepairOrders)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(assetRepairOrders.id, repairOrder.id));

          deletedIds.push(repairOrder.id);

          // Log the activity for each deleted repair order
          await this.activityLogService.logDelete(
            repairOrder.id,
            EntityType.ASSET_REPAIR_ORDER,
            userId,
            businessId,
            {
              reason: `Asset repair order "${repairOrder.title}" was deleted (bulk)`,
              source: metadata?.source || ActivitySource.WEB,
              ipAddress: metadata?.ipAddress,
              userAgent: metadata?.userAgent,
              sessionId: metadata?.sessionId,
            },
          );
        }
      });

      return {
        deletedCount: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} asset repair orders`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete asset repair orders: ${error.message}`,
      );
    }
  }

  async bulkUpdateAssetRepairOrderStatus(
    userId: string,
    businessId: string | null,
    assetRepairOrderIds: string[],
    status: RepairStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ assetRepairOrderId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetRepairOrderIds || assetRepairOrderIds.length === 0) {
        throw new BadRequestException(
          'No asset repair order IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ assetRepairOrderId: string; error: string }> = [];

      // Process each repair order ID
      await this.db.transaction(async (tx) => {
        for (const assetRepairOrderId of assetRepairOrderIds) {
          try {
            // Check if repair order exists and belongs to the business
            const existingRepairOrder = await tx
              .select()
              .from(assetRepairOrders)
              .where(
                and(
                  eq(assetRepairOrders.id, assetRepairOrderId),
                  eq(assetRepairOrders.businessId, businessId),
                  eq(assetRepairOrders.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingRepairOrder) {
              failed.push({
                assetRepairOrderId,
                error: 'Asset repair order not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingRepairOrder.status === status) {
              failed.push({
                assetRepairOrderId,
                error: `Asset repair order already has status: ${status}`,
              });
              continue;
            }

            // Update the repair order status
            await tx
              .update(assetRepairOrders)
              .set({
                status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(assetRepairOrders.id, assetRepairOrderId),
                  eq(assetRepairOrders.businessId, businessId),
                  eq(assetRepairOrders.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(assetRepairOrderId);

            // Log the status update activity
            await this.activityLogService.logStatusChange(
              assetRepairOrderId,
              EntityType.ASSET_REPAIR_ORDER,
              existingRepairOrder.status,
              status,
              userId,
              businessId,
              {
                reason: `Asset repair order status updated from ${existingRepairOrder.status} to ${status}`,
                ipAddress: metadata?.ipAddress,
                userAgent: metadata?.userAgent,
                sessionId: metadata?.sessionId,
              },
            );
          } catch (error) {
            failed.push({
              assetRepairOrderId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update asset repair order status: ${error.message}`,
      );
    }
  }

  // Utility methods for returning IDs
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetRepairOrderDto: CreateAssetRepairOrderDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return await this.create(
      userId,
      businessId,
      createAssetRepairOrderDto,
      attachmentFiles,
      metadata,
    );
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetRepairOrderDto: UpdateAssetRepairOrderDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateAssetRepairOrderDto,
      attachmentFiles,
      metadata,
    );
    return { id };
  }

  // Mapping methods
  private async mapToAssetRepairOrderDto(
    repairOrder: typeof assetRepairOrders.$inferSelect,
  ): Promise<AssetRepairOrderDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      repairOrder.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (repairOrder.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        repairOrder.updatedBy.toString(),
      );
    }

    // Get asset information
    const asset = await this.db
      .select({
        name: assets.name,
        assetCode: assets.assetCode,
      })
      .from(assets)
      .where(eq(assets.id, repairOrder.assetId))
      .then((results) => results[0]);

    // Get staff member names
    const reportedByStaff = await this.db
      .select({
        firstName: staffMembers.firstName,
        lastName: staffMembers.lastName,
      })
      .from(staffMembers)
      .where(eq(staffMembers.id, repairOrder.reportedBy))
      .then((results) => results[0]);

    let assignedToStaff: { firstName: string; lastName: string } | undefined;
    if (repairOrder.assignedTo) {
      assignedToStaff = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, repairOrder.assignedTo))
        .then((results) => results[0]);
    }

    // Get supplier name if applicable
    let supplierName: string | undefined;
    if (repairOrder.supplierId) {
      const supplier = await this.db
        .select({
          displayName: suppliers.displayName,
        })
        .from(suppliers)
        .where(eq(suppliers.id, repairOrder.supplierId))
        .then((results) => results[0]);
      supplierName = supplier?.displayName;
    }

    // Get account names
    const expenseAccount = await this.db
      .select({
        name: accounts.name,
      })
      .from(accounts)
      .where(eq(accounts.id, repairOrder.expenseAccountId))
      .then((results) => results[0]);

    let paymentAccountName: string | undefined;
    if (repairOrder.paymentAccountId) {
      const paymentAccount = await this.db
        .select({
          name: accounts.name,
        })
        .from(accounts)
        .where(eq(accounts.id, repairOrder.paymentAccountId))
        .then((results) => results[0]);
      paymentAccountName = paymentAccount?.name;
    }

    // Get payment method name if applicable
    let paymentMethodName: string | undefined;
    if (repairOrder.paymentMethodId) {
      const paymentMethod = await this.db
        .select({
          name: paymentMethods.name,
        })
        .from(paymentMethods)
        .where(eq(paymentMethods.id, repairOrder.paymentMethodId))
        .then((results) => results[0]);
      paymentMethodName = paymentMethod?.name;
    }

    // Get tax name if applicable
    let taxName: string | undefined;
    if (repairOrder.taxId) {
      const tax = await this.db
        .select({
          taxName: taxes.taxName,
        })
        .from(taxes)
        .where(eq(taxes.id, repairOrder.taxId))
        .then((results) => results[0]);
      taxName = tax?.taxName;
    }

    // Get quality check staff name if applicable
    let qualityCheckByName: string | undefined;
    if (repairOrder.qualityCheckBy) {
      const qualityCheckStaff = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, repairOrder.qualityCheckBy))
        .then((results) => results[0]);
      qualityCheckByName = qualityCheckStaff
        ? `${qualityCheckStaff.firstName} ${qualityCheckStaff.lastName}`.trim()
        : undefined;
    }

    const repairOrderDto: AssetRepairOrderDto = {
      id: repairOrder.id.toString(),
      businessId: repairOrder.businessId.toString(),
      assetId: repairOrder.assetId.toString(),
      repairOrderNumber: repairOrder.repairOrderNumber,
      title: repairOrder.title,
      problemDescription: repairOrder.problemDescription,
      diagnosisNotes: repairOrder.diagnosisNotes,
      repairSolution: repairOrder.repairSolution,
      repairType: repairOrder.repairType,
      status: repairOrder.status,
      priority: repairOrder.priority,
      reportedAt: repairOrder.reportedAt,
      targetCompletionDate: repairOrder.targetCompletionDate,
      actualStartDate: repairOrder.actualStartDate,
      completedAt: repairOrder.completedAt,
      completionNotes: repairOrder.completionNotes,
      reportedBy: repairOrder.reportedBy.toString(),
      reportedByName:
        `${reportedByStaff.firstName} ${reportedByStaff.lastName}`.trim(),
      assignedTo: repairOrder.assignedTo?.toString(),
      assignedToName: assignedToStaff
        ? `${assignedToStaff.firstName} ${assignedToStaff.lastName}`.trim()
        : undefined,
      supplierId: repairOrder.supplierId?.toString(),
      supplierName,
      isWarrantyRepair: repairOrder.isWarrantyRepair,
      warrantyClaimNumber: repairOrder.warrantyClaimNumber,
      expenseAccountId: repairOrder.expenseAccountId.toString(),
      expenseAccountName: expenseAccount.name,
      paymentAccountId: repairOrder.paymentAccountId?.toString(),
      paymentAccountName,
      paymentDate: repairOrder.paymentDate,
      paymentMethodId: repairOrder.paymentMethodId?.toString(),
      paymentMethodName,
      paymentReferenceNumber: repairOrder.paymentReferenceNumber,
      amountType: repairOrder.amountType,
      subtotal: repairOrder.subtotal,
      total: repairOrder.total,
      taxId: repairOrder.taxId?.toString(),
      taxName,
      qualityCheckBy: repairOrder.qualityCheckBy?.toString(),
      qualityCheckByName,
      assetName: asset.name,
      assetCode: asset.assetCode,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: repairOrder.createdAt,
      updatedAt: repairOrder.updatedAt,
    };

    return repairOrderDto;
  }

  private async mapToAssetRepairOrderListDto(
    repairOrder: typeof assetRepairOrders.$inferSelect,
    asset?: { name: string; assetCode: string },
    reportedByStaff?: { firstName: string; lastName: string },
  ): Promise<AssetRepairOrderListDto> {
    // Get user name for createdBy
    const createdByName = await this.usersService.getUserName(
      repairOrder.createdBy.toString(),
    );

    // Get asset information if not provided
    let assetInfo = asset;
    if (!assetInfo) {
      assetInfo = await this.db
        .select({
          name: assets.name,
          assetCode: assets.assetCode,
        })
        .from(assets)
        .where(eq(assets.id, repairOrder.assetId))
        .then((results) => results[0]);
    }

    // Get reported by staff information if not provided
    let reportedByInfo = reportedByStaff;
    if (!reportedByInfo) {
      reportedByInfo = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, repairOrder.reportedBy))
        .then((results) => results[0]);
    }

    // Get assigned to staff name if applicable
    let assignedToName: string | undefined;
    if (repairOrder.assignedTo) {
      const assignedToStaff = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, repairOrder.assignedTo))
        .then((results) => results[0]);
      assignedToName = assignedToStaff
        ? `${assignedToStaff.firstName} ${assignedToStaff.lastName}`.trim()
        : undefined;
    }

    // Get supplier name if applicable
    let supplierName: string | undefined;
    if (repairOrder.supplierId) {
      const supplier = await this.db
        .select({
          displayName: suppliers.displayName,
        })
        .from(suppliers)
        .where(eq(suppliers.id, repairOrder.supplierId))
        .then((results) => results[0]);
      supplierName = supplier?.displayName;
    }

    // Get expense account name
    const expenseAccount = await this.db
      .select({
        name: accounts.name,
      })
      .from(accounts)
      .where(eq(accounts.id, repairOrder.expenseAccountId))
      .then((results) => results[0]);

    return {
      id: repairOrder.id.toString(),
      repairOrderNumber: repairOrder.repairOrderNumber,
      title: repairOrder.title,
      problemDescription: repairOrder.problemDescription,
      repairType: repairOrder.repairType,
      status: repairOrder.status,
      priority: repairOrder.priority,
      reportedAt: repairOrder.reportedAt,
      targetCompletionDate: repairOrder.targetCompletionDate,
      completedAt: repairOrder.completedAt,
      assetName: assetInfo.name,
      assetCode: assetInfo.assetCode,
      reportedByName:
        `${reportedByInfo.firstName} ${reportedByInfo.lastName}`.trim(),
      assignedToName,
      supplierName,
      isWarrantyRepair: repairOrder.isWarrantyRepair,
      expenseAccountName: expenseAccount.name,
      total: repairOrder.total,
      createdBy: createdByName,
      createdAt: repairOrder.createdAt,
      updatedAt: repairOrder.updatedAt,
    };
  }

  private async mapToAssetRepairOrderSlimDto(
    repairOrder: typeof assetRepairOrders.$inferSelect,
  ): Promise<AssetRepairOrderSlimDto> {
    // Get asset information
    const asset = await this.db
      .select({
        name: assets.name,
        assetCode: assets.assetCode,
      })
      .from(assets)
      .where(eq(assets.id, repairOrder.assetId))
      .then((results) => results[0]);

    // Get reported by staff information
    const reportedByStaff = await this.db
      .select({
        firstName: staffMembers.firstName,
        lastName: staffMembers.lastName,
      })
      .from(staffMembers)
      .where(eq(staffMembers.id, repairOrder.reportedBy))
      .then((results) => results[0]);

    // Get assigned to staff name if applicable
    let assignedToName: string | undefined;
    if (repairOrder.assignedTo) {
      const assignedToStaff = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, repairOrder.assignedTo))
        .then((results) => results[0]);
      assignedToName = assignedToStaff
        ? `${assignedToStaff.firstName} ${assignedToStaff.lastName}`.trim()
        : undefined;
    }

    return {
      id: repairOrder.id.toString(),
      repairOrderNumber: repairOrder.repairOrderNumber,
      title: repairOrder.title,
      status: repairOrder.status,
      priority: repairOrder.priority,
      repairType: repairOrder.repairType,
      assetName: asset.name,
      assetCode: asset.assetCode,
      reportedAt: repairOrder.reportedAt,
      targetCompletionDate: repairOrder.targetCompletionDate,
      reportedByName:
        `${reportedByStaff.firstName} ${reportedByStaff.lastName}`.trim(),
      assignedToName,
      total: repairOrder.total,
    };
  }

  async findAllSlim(businessId: string): Promise<AssetRepairOrderSlimDto[]> {
    const repairOrders = await this.db
      .select()
      .from(assetRepairOrders)
      .where(and(eq(assetRepairOrders.businessId, businessId)))
      .orderBy(desc(assetRepairOrders.reportedAt));

    return Promise.all(
      repairOrders.map((repairOrder) =>
        this.mapToAssetRepairOrderSlimDto(repairOrder),
      ),
    );
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string,
    createAssetRepairOrderDtos: CreateAssetRepairOrderDto[],
    attachments: Express.Multer.File[] = [],
  ): Promise<{ ids: string[] }> {
    if (
      !Array.isArray(createAssetRepairOrderDtos) ||
      createAssetRepairOrderDtos.length === 0
    ) {
      throw new BadRequestException(
        'Asset repair orders array is required and cannot be empty',
      );
    }

    const createdIds: string[] = [];

    await this.db.transaction(async (tx) => {
      for (let i = 0; i < createAssetRepairOrderDtos.length; i++) {
        const dto = createAssetRepairOrderDtos[i];
        const attachment = attachments.find(
          (_, index) => index === dto['attachmentIndex'],
        );

        // Generate repair order number
        const repairOrderNumber =
          await this.generateRepairOrderNumber(businessId);

        // Upload attachment if provided
        if (attachment) {
          await this.mediaService.uploadMedia(
            attachment,
            'asset-repair-attachments',
            businessId,
            userId,
          );
        }

        // Create the repair order
        const [newRepairOrder] = await tx
          .insert(assetRepairOrders)
          .values({
            businessId,
            assetId: dto.assetId,
            repairOrderNumber,
            title: dto.title,
            problemDescription: dto.problemDescription,
            diagnosisNotes: dto.diagnosisNotes,
            repairSolution: dto.repairSolution,
            repairType: dto.repairType || RepairType.CORRECTIVE,
            status: dto.status || RepairStatus.REPORTED,
            priority: dto.priority || RepairPriority.NORMAL,
            targetCompletionDate: dto.targetCompletionDate
              ? new Date(dto.targetCompletionDate)
              : undefined,
            actualStartDate: dto.actualStartDate
              ? new Date(dto.actualStartDate)
              : undefined,
            completedAt: dto.completedAt
              ? new Date(dto.completedAt)
              : undefined,
            completionNotes: dto.completionNotes,
            reportedBy: dto.reportedBy,
            assignedTo: dto.assignedTo,
            supplierId: dto.supplierId,
            isWarrantyRepair: dto.isWarrantyRepair || false,
            warrantyClaimNumber: dto.warrantyClaimNumber,
            expenseAccountId: dto.expenseAccountId,
            paymentAccountId: dto.paymentAccountId,
            paymentDate: dto.paymentDate,
            paymentMethodId: dto.paymentMethodId,
            paymentReferenceNumber: dto.paymentReferenceNumber,
            amountType: dto.amountType,
            subtotal: dto.subtotal ?? '0.00',
            total: dto.total ?? '0.00',
            taxId: dto.taxId,
            qualityCheckBy: dto.qualityCheckBy,
            createdBy: userId,
          })
          .returning({ id: assetRepairOrders.id });

        createdIds.push(newRepairOrder.id.toString());

        // Log activity
        await this.activityLogService.logCreate(
          newRepairOrder.id.toString(),
          EntityType.ASSET_REPAIR_ORDER,
          userId,
          businessId,
          {
            reason: 'Asset repair order created',
          },
        );
      }
    });

    return { ids: createdIds };
  }
}
