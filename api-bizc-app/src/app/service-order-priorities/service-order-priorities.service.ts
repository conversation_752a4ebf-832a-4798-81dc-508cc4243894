import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateServiceOrderPriorityDto } from './dto/create-service-order-priority.dto';
import { UpdateServiceOrderPriorityDto } from './dto/update-service-order-priority.dto';
import { ServiceOrderPriorityDto } from './dto/service-order-priority.dto';
import { ServiceOrderPrioritySlimDto } from './dto/service-order-priority-slim.dto';
import { ServiceOrderPriorityListDto } from './dto/service-order-priority-list.dto';
import { serviceOrderPriorities } from '../drizzle/schema/service-order-priorities.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
  sql,
  count,
} from 'drizzle-orm';

@Injectable()
export class ServiceOrderPrioritiesService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceOrderPriorityDto: CreateServiceOrderPriorityDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service order priority with the same priority code already exists for this business
      const existingByCode = await this.db
        .select()
        .from(serviceOrderPriorities)
        .where(
          and(
            eq(serviceOrderPriorities.businessId, businessId),
            ilike(
              serviceOrderPriorities.priorityCode,
              createServiceOrderPriorityDto.priorityCode,
            ),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByCode) {
        throw new ConflictException(
          `Service order priority with code '${createServiceOrderPriorityDto.priorityCode}' already exists`,
        );
      }

      // Check if a service order priority with the same priority name already exists for this business
      const existingByName = await this.db
        .select()
        .from(serviceOrderPriorities)
        .where(
          and(
            eq(serviceOrderPriorities.businessId, businessId),
            ilike(
              serviceOrderPriorities.priorityName,
              createServiceOrderPriorityDto.priorityName,
            ),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByName) {
        throw new ConflictException(
          `Service order priority with name '${createServiceOrderPriorityDto.priorityName}' already exists`,
        );
      }

      // Get the next position
      const maxPositionResult = await this.db
        .select({
          maxPosition: sql<number>`COALESCE(MAX(${serviceOrderPriorities.position}), 0)`,
        })
        .from(serviceOrderPriorities)
        .where(
          and(
            eq(serviceOrderPriorities.businessId, businessId),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        );

      const nextPosition = (maxPositionResult[0]?.maxPosition || 0) + 1;

      // If this is set as default, unset any existing default
      if (createServiceOrderPriorityDto.isDefault) {
        await this.db
          .update(serviceOrderPriorities)
          .set({
            isDefault: false,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(
            and(
              eq(serviceOrderPriorities.businessId, businessId),
              eq(serviceOrderPriorities.isDefault, true),
              eq(serviceOrderPriorities.isDeleted, false),
            ),
          );
      }

      const result = await this.db
        .insert(serviceOrderPriorities)
        .values({
          businessId,
          priorityCode: createServiceOrderPriorityDto.priorityCode,
          priorityName: createServiceOrderPriorityDto.priorityName,
          description: createServiceOrderPriorityDto.description,
          colorCode: createServiceOrderPriorityDto.colorCode,
          iconName: createServiceOrderPriorityDto.iconName,
          severityLevel: createServiceOrderPriorityDto.severityLevel,
          position: nextPosition,
          isActive: createServiceOrderPriorityDto.isActive ?? true,
          isDefault: createServiceOrderPriorityDto.isDefault ?? false,
          escalationMinutes: createServiceOrderPriorityDto.escalationMinutes,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: serviceOrderPriorities.id });

      return { id: result[0].id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create service order priority');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ServiceOrderPriorityDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(serviceOrderPriorities.isDeleted, false),
      eq(serviceOrderPriorities.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceOrderPriorities.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(serviceOrderPriorities.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(serviceOrderPriorities)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const result = await this.db
      .select({
        id: serviceOrderPriorities.id,
        businessId: serviceOrderPriorities.businessId,
        priorityCode: serviceOrderPriorities.priorityCode,
        priorityName: serviceOrderPriorities.priorityName,
        description: serviceOrderPriorities.description,
        colorCode: serviceOrderPriorities.colorCode,
        iconName: serviceOrderPriorities.iconName,
        severityLevel: serviceOrderPriorities.severityLevel,
        position: serviceOrderPriorities.position,
        isActive: serviceOrderPriorities.isActive,
        isDefault: serviceOrderPriorities.isDefault,
        escalationMinutes: serviceOrderPriorities.escalationMinutes,
        createdBy: users.name,
        updatedBy: serviceOrderPriorities.updatedBy,
        createdAt: serviceOrderPriorities.createdAt,
        updatedAt: serviceOrderPriorities.updatedAt,
      })
      .from(serviceOrderPriorities)
      .leftJoin(users, eq(serviceOrderPriorities.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(
        asc(serviceOrderPriorities.position),
        asc(serviceOrderPriorities.id),
      )
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      result.map((serviceOrderPriority) =>
        this.mapToServiceOrderPriorityDto(serviceOrderPriority),
      ),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    priorityCode?: string,
    priorityName?: string,
    severityLevel?: string,
    isActive?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ServiceOrderPriorityListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(serviceOrderPriorities.isDeleted, false),
      eq(serviceOrderPriorities.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceOrderPriorities.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(serviceOrderPriorities.createdAt, toDate));
      }
    }

    // Build filter conditions
    const filterConditions = [];

    if (priorityCode) {
      filterConditions.push(
        ilike(serviceOrderPriorities.priorityCode, `%${priorityCode}%`),
      );
    }

    if (priorityName) {
      filterConditions.push(
        ilike(serviceOrderPriorities.priorityName, `%${priorityName}%`),
      );
    }

    if (severityLevel) {
      const level = parseInt(severityLevel);
      if (!isNaN(level)) {
        filterConditions.push(eq(serviceOrderPriorities.severityLevel, level));
      }
    }

    if (isActive !== undefined) {
      const activeValue = isActive === 'true';
      filterConditions.push(eq(serviceOrderPriorities.isActive, activeValue));
    }

    // Handle additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const [key, value] of Object.entries(parsedFilters)) {
          if (value && typeof value === 'string') {
            switch (key) {
              case 'description':
                filterConditions.push(
                  ilike(serviceOrderPriorities.description, `%${value}%`),
                );
                break;
              case 'colorCode':
                filterConditions.push(
                  ilike(serviceOrderPriorities.colorCode, `%${value}%`),
                );
                break;
              case 'iconName':
                filterConditions.push(
                  ilike(serviceOrderPriorities.iconName, `%${value}%`),
                );
                break;
            }
          }
        }
      } catch (error) {
        // Invalid JSON, ignore filters
      }
    }

    // Combine filter conditions based on join operator
    if (filterConditions.length > 0) {
      if (joinOperator === 'or') {
        whereConditions.push(or(...filterConditions));
      } else {
        whereConditions.push(...filterConditions);
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(serviceOrderPriorities)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Build order by clause
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'priorityCode':
          orderBy = isDesc
            ? desc(serviceOrderPriorities.priorityCode)
            : asc(serviceOrderPriorities.priorityCode);
          break;
        case 'priorityName':
          orderBy = isDesc
            ? desc(serviceOrderPriorities.priorityName)
            : asc(serviceOrderPriorities.priorityName);
          break;
        case 'severityLevel':
          orderBy = isDesc
            ? desc(serviceOrderPriorities.severityLevel)
            : asc(serviceOrderPriorities.severityLevel);
          break;
        case 'position':
          orderBy = isDesc
            ? desc(serviceOrderPriorities.position)
            : asc(serviceOrderPriorities.position);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(serviceOrderPriorities.createdAt)
            : asc(serviceOrderPriorities.createdAt);
          break;
        default:
          orderBy = asc(serviceOrderPriorities.position);
      }
    } else {
      orderBy = asc(serviceOrderPriorities.position);
    }

    // Get paginated results
    const result = await this.db
      .select({
        id: serviceOrderPriorities.id,
        priorityCode: serviceOrderPriorities.priorityCode,
        priorityName: serviceOrderPriorities.priorityName,
        description: serviceOrderPriorities.description,
        colorCode: serviceOrderPriorities.colorCode,
        iconName: serviceOrderPriorities.iconName,
        severityLevel: serviceOrderPriorities.severityLevel,
        position: serviceOrderPriorities.position,
        isActive: serviceOrderPriorities.isActive,
        isDefault: serviceOrderPriorities.isDefault,
        escalationMinutes: serviceOrderPriorities.escalationMinutes,
        createdBy: users.name,
        createdAt: serviceOrderPriorities.createdAt,
        updatedAt: serviceOrderPriorities.updatedAt,
      })
      .from(serviceOrderPriorities)
      .leftJoin(users, eq(serviceOrderPriorities.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(orderBy, asc(serviceOrderPriorities.id))
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      result.map((serviceOrderPriority) =>
        this.mapToServiceOrderPriorityListDto(serviceOrderPriority),
      ),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ServiceOrderPriorityDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: serviceOrderPriorities.id,
        businessId: serviceOrderPriorities.businessId,
        priorityCode: serviceOrderPriorities.priorityCode,
        priorityName: serviceOrderPriorities.priorityName,
        description: serviceOrderPriorities.description,
        colorCode: serviceOrderPriorities.colorCode,
        iconName: serviceOrderPriorities.iconName,
        severityLevel: serviceOrderPriorities.severityLevel,
        position: serviceOrderPriorities.position,
        isActive: serviceOrderPriorities.isActive,
        isDefault: serviceOrderPriorities.isDefault,
        escalationMinutes: serviceOrderPriorities.escalationMinutes,
        createdBy: users.name,
        updatedBy: serviceOrderPriorities.updatedBy,
        createdAt: serviceOrderPriorities.createdAt,
        updatedAt: serviceOrderPriorities.updatedAt,
      })
      .from(serviceOrderPriorities)
      .leftJoin(users, eq(serviceOrderPriorities.createdBy, users.id))
      .where(
        and(
          eq(serviceOrderPriorities.id, id),
          eq(serviceOrderPriorities.businessId, businessId),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Service order priority not found');
    }

    return this.mapToServiceOrderPriorityDto(result);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceOrderPriorityDto: UpdateServiceOrderPriorityDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the service order priority exists
      const existingPriority = await this.db
        .select()
        .from(serviceOrderPriorities)
        .where(
          and(
            eq(serviceOrderPriorities.id, id),
            eq(serviceOrderPriorities.businessId, businessId),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPriority) {
        throw new NotFoundException('Service order priority not found');
      }

      // Check for conflicts if priority code is being updated
      if (
        updateServiceOrderPriorityDto.priorityCode &&
        updateServiceOrderPriorityDto.priorityCode !==
          existingPriority.priorityCode
      ) {
        const existingByCode = await this.db
          .select()
          .from(serviceOrderPriorities)
          .where(
            and(
              eq(serviceOrderPriorities.businessId, businessId),
              ilike(
                serviceOrderPriorities.priorityCode,
                updateServiceOrderPriorityDto.priorityCode,
              ),
              eq(serviceOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByCode) {
          throw new ConflictException(
            `Service order priority with code '${updateServiceOrderPriorityDto.priorityCode}' already exists`,
          );
        }
      }

      // Check for conflicts if priority name is being updated
      if (
        updateServiceOrderPriorityDto.priorityName &&
        updateServiceOrderPriorityDto.priorityName !==
          existingPriority.priorityName
      ) {
        const existingByName = await this.db
          .select()
          .from(serviceOrderPriorities)
          .where(
            and(
              eq(serviceOrderPriorities.businessId, businessId),
              ilike(
                serviceOrderPriorities.priorityName,
                updateServiceOrderPriorityDto.priorityName,
              ),
              eq(serviceOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByName) {
          throw new ConflictException(
            `Service order priority with name '${updateServiceOrderPriorityDto.priorityName}' already exists`,
          );
        }
      }

      // If this is being set as default, unset any existing default
      if (updateServiceOrderPriorityDto.isDefault) {
        await this.db
          .update(serviceOrderPriorities)
          .set({
            isDefault: false,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(
            and(
              eq(serviceOrderPriorities.businessId, businessId),
              eq(serviceOrderPriorities.isDefault, true),
              eq(serviceOrderPriorities.isDeleted, false),
            ),
          );
      }

      // Update the service order priority
      await this.db
        .update(serviceOrderPriorities)
        .set({
          ...updateServiceOrderPriorityDto,
          updatedAt: new Date(),
          updatedBy: userId,
        })
        .where(eq(serviceOrderPriorities.id, id));

      return { id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update service order priority');
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the service order priority exists
    const existingPriority = await this.db
      .select()
      .from(serviceOrderPriorities)
      .where(
        and(
          eq(serviceOrderPriorities.id, id),
          eq(serviceOrderPriorities.businessId, businessId),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingPriority) {
      throw new NotFoundException('Service order priority not found');
    }

    // Soft delete the service order priority
    await this.db
      .update(serviceOrderPriorities)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(serviceOrderPriorities.id, id));

    return {
      message: 'Service order priority deleted successfully',
      id,
    };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createServiceOrderPriorityDtos: CreateServiceOrderPriorityDto[],
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (
      !createServiceOrderPriorityDtos ||
      createServiceOrderPriorityDtos.length === 0
    ) {
      throw new BadRequestException('No service order priorities provided');
    }

    // Check for duplicate priority codes within the batch
    const priorityCodes = createServiceOrderPriorityDtos.map((dto) =>
      dto.priorityCode.toLowerCase(),
    );
    const duplicateCodes = priorityCodes.filter(
      (code, index) => priorityCodes.indexOf(code) !== index,
    );
    if (duplicateCodes.length > 0) {
      throw new ConflictException(
        `Duplicate priority codes in batch: ${duplicateCodes.join(', ')}`,
      );
    }

    // Check for duplicate priority names within the batch
    const priorityNames = createServiceOrderPriorityDtos.map((dto) =>
      dto.priorityName.toLowerCase(),
    );
    const duplicateNames = priorityNames.filter(
      (name, index) => priorityNames.indexOf(name) !== index,
    );
    if (duplicateNames.length > 0) {
      throw new ConflictException(
        `Duplicate priority names in batch: ${duplicateNames.join(', ')}`,
      );
    }

    // Check for existing priority codes
    const existingCodes = await this.db
      .select({ priorityCode: serviceOrderPriorities.priorityCode })
      .from(serviceOrderPriorities)
      .where(
        and(
          eq(serviceOrderPriorities.businessId, businessId),
          inArray(serviceOrderPriorities.priorityCode, priorityCodes),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      );

    if (existingCodes.length > 0) {
      throw new ConflictException(
        `Priority codes already exist: ${existingCodes.map((c) => c.priorityCode).join(', ')}`,
      );
    }

    // Check for existing priority names
    const existingNames = await this.db
      .select({ priorityName: serviceOrderPriorities.priorityName })
      .from(serviceOrderPriorities)
      .where(
        and(
          eq(serviceOrderPriorities.businessId, businessId),
          inArray(serviceOrderPriorities.priorityName, priorityNames),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      );

    if (existingNames.length > 0) {
      throw new ConflictException(
        `Priority names already exist: ${existingNames.map((n) => n.priorityName).join(', ')}`,
      );
    }

    // Get the current max position
    const maxPositionResult = await this.db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${serviceOrderPriorities.position}), 0)`,
      })
      .from(serviceOrderPriorities)
      .where(
        and(
          eq(serviceOrderPriorities.businessId, businessId),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      );

    const nextPosition = (maxPositionResult[0]?.maxPosition || 0) + 1;

    // Check if any of the new priorities is set as default
    const hasDefault = createServiceOrderPriorityDtos.some(
      (dto) => dto.isDefault,
    );
    if (hasDefault) {
      // Unset any existing default
      await this.db
        .update(serviceOrderPriorities)
        .set({
          isDefault: false,
          updatedAt: new Date(),
          updatedBy: userId,
        })
        .where(
          and(
            eq(serviceOrderPriorities.businessId, businessId),
            eq(serviceOrderPriorities.isDefault, true),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        );
    }

    // Prepare bulk insert data
    const insertData = createServiceOrderPriorityDtos.map((dto, index) => ({
      businessId,
      priorityCode: dto.priorityCode,
      priorityName: dto.priorityName,
      description: dto.description,
      colorCode: dto.colorCode,
      iconName: dto.iconName,
      severityLevel: dto.severityLevel,
      position: nextPosition + index,
      isActive: dto.isActive ?? true,
      isDefault: dto.isDefault ?? false,
      escalationMinutes: dto.escalationMinutes,
      createdBy: userId,
      updatedBy: userId,
    }));

    // Bulk insert
    const result = await this.db
      .insert(serviceOrderPriorities)
      .values(insertData)
      .returning({ id: serviceOrderPriorities.id });

    return { ids: result.map((r) => r.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('No service order priority IDs provided');
    }

    // Check which priorities exist and belong to the business
    const existingPriorities = await this.db
      .select({ id: serviceOrderPriorities.id })
      .from(serviceOrderPriorities)
      .where(
        and(
          inArray(serviceOrderPriorities.id, ids),
          eq(serviceOrderPriorities.businessId, businessId),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      );

    const existingIds = existingPriorities.map((p) => p.id);

    if (existingIds.length === 0) {
      throw new NotFoundException(
        'No valid service order priorities found to delete',
      );
    }

    // Soft delete the priorities
    await this.db
      .update(serviceOrderPriorities)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(serviceOrderPriorities.id, existingIds));

    return {
      deletedCount: existingIds.length,
      deletedIds: existingIds,
    };
  }

  async checkPriorityCodeAvailability(
    businessId: string | null,
    priorityCode: string,
    excludeId?: string,
  ): Promise<{ available: boolean; priorityCode: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(serviceOrderPriorities.businessId, businessId),
      ilike(serviceOrderPriorities.priorityCode, priorityCode),
      eq(serviceOrderPriorities.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${serviceOrderPriorities.id} != ${excludeId}`);
    }

    const existing = await this.db
      .select()
      .from(serviceOrderPriorities)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existing;

    return {
      available,
      priorityCode,
      message: available
        ? 'Priority code is available'
        : 'Priority code is already taken',
    };
  }

  async checkPriorityNameAvailability(
    businessId: string | null,
    priorityName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; priorityName: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(serviceOrderPriorities.businessId, businessId),
      ilike(serviceOrderPriorities.priorityName, priorityName),
      eq(serviceOrderPriorities.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${serviceOrderPriorities.id} != ${excludeId}`);
    }

    const existing = await this.db
      .select()
      .from(serviceOrderPriorities)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existing;

    return {
      available,
      priorityName,
      message: available
        ? 'Priority name is available'
        : 'Priority name is already taken',
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ServiceOrderPrioritySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: serviceOrderPriorities.id,
        priorityName: serviceOrderPriorities.priorityName,
        priorityCode: serviceOrderPriorities.priorityCode,
        severityLevel: serviceOrderPriorities.severityLevel,
        position: serviceOrderPriorities.position,
        colorCode: serviceOrderPriorities.colorCode,
        iconName: serviceOrderPriorities.iconName,
        isActive: serviceOrderPriorities.isActive,
      })
      .from(serviceOrderPriorities)
      .where(
        and(
          eq(serviceOrderPriorities.businessId, businessId),
          eq(serviceOrderPriorities.isActive, true),
          eq(serviceOrderPriorities.isDeleted, false),
        ),
      )
      .orderBy(
        asc(serviceOrderPriorities.position),
        asc(serviceOrderPriorities.id),
      );

    return await Promise.all(
      result.map((serviceOrderPriority) =>
        this.mapToServiceOrderPrioritySlimDto(serviceOrderPriority),
      ),
    );
  }

  async updateServiceOrderPriorityPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for service order priority ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Extract IDs for validation
      const updateIds = updates.map((u) => u.id);

      // Verify all service order priorities exist and belong to the business
      const existingPriorities = await this.db
        .select({
          id: serviceOrderPriorities.id,
          priorityName: serviceOrderPriorities.priorityName,
        })
        .from(serviceOrderPriorities)
        .where(
          and(
            inArray(serviceOrderPriorities.id, updateIds),
            eq(serviceOrderPriorities.businessId, businessId),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        );

      if (existingPriorities.length !== updates.length) {
        const foundIds = existingPriorities.map((p) => p.id);
        const missingIds = updateIds.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Service order priorities not found: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdatePositions(tx, businessId, updates);
        updatedCount = updates.length;
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update service order priority positions',
      );
    }
  }

  private async batchUpdatePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    // Group updates by position to minimize database calls
    const positionGroups = new Map<number, string[]>();

    for (const update of updates) {
      if (!positionGroups.has(update.position)) {
        positionGroups.set(update.position, []);
      }
      positionGroups.get(update.position).push(update.id);
    }

    // Execute batch updates for each position
    const updatePromises = [];
    for (const [position, ids] of positionGroups) {
      const updatePromise = tx
        .update(serviceOrderPriorities)
        .set({
          position,
          updatedAt: new Date(),
        })
        .where(
          and(
            inArray(serviceOrderPriorities.id, ids),
            eq(serviceOrderPriorities.businessId, businessId),
          ),
        );
      updatePromises.push(updatePromise);
    }

    await Promise.all(updatePromises);
  }

  // Utility mapping methods
  private async mapToServiceOrderPriorityDto(
    data: any,
  ): Promise<ServiceOrderPriorityDto> {
    return {
      id: data.id,
      businessId: data.businessId,
      priorityCode: data.priorityCode,
      priorityName: data.priorityName,
      description: data.description,
      colorCode: data.colorCode,
      iconName: data.iconName,
      severityLevel: data.severityLevel,
      position: data.position,
      isActive: data.isActive,
      isDefault: data.isDefault,
      escalationMinutes: data.escalationMinutes,
      createdBy: data.createdBy || 'Unknown',
      updatedBy: data.updatedBy,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
    };
  }

  private async mapToServiceOrderPriorityListDto(
    data: any,
  ): Promise<ServiceOrderPriorityListDto> {
    return {
      id: data.id,
      priorityCode: data.priorityCode,
      priorityName: data.priorityName,
      description: data.description,
      colorCode: data.colorCode,
      iconName: data.iconName,
      severityLevel: data.severityLevel,
      position: data.position,
      isActive: data.isActive,
      isDefault: data.isDefault,
      escalationMinutes: data.escalationMinutes,
      createdBy: data.createdBy || 'Unknown',
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    };
  }

  private async mapToServiceOrderPrioritySlimDto(
    data: any,
  ): Promise<ServiceOrderPrioritySlimDto> {
    return {
      id: data.id,
      priorityName: data.priorityName,
      priorityCode: data.priorityCode,
      severityLevel: data.severityLevel,
      position: data.position,
      colorCode: data.colorCode,
      iconName: data.iconName,
      isActive: data.isActive,
    };
  }
}
