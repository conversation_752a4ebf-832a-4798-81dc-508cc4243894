import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateServiceDto } from './dto/create-service.dto';
import { UpdateServiceDto } from './dto/update-service.dto';
import { ServiceDto } from './dto/service.dto';
import { ServiceSlimDto } from './dto/service-slim.dto';
import { ServiceListDto } from './dto/service-list.dto';
import { DeleteServiceResponseDto } from './dto/delete-service-response.dto';
import { BulkDeleteServiceResponseDto } from './dto/bulk-delete-service-response.dto';
import { PaginatedServicesResponseDto } from './dto/paginated-services-response.dto';
import {
  services,
  serviceLocations,
  serviceStaff,
} from '../drizzle/schema/services.schema';
import { media } from '../drizzle/schema/media.schema';
import { serviceCategories } from '../drizzle/schema/service-categories.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { and, eq, desc, asc, ilike, inArray, sql, count } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { MediaService } from '../media/media.service';
import { UsersService } from '../users/users.service';
import { ServiceStatus } from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class ServicesService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceDto: CreateServiceDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service with the same SKU already exists for this business
      const existingService = await this.db
        .select()
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            ilike(services.sku, createServiceDto.sku),
            eq(services.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingService) {
        throw new ConflictException(
          `Service with SKU "${createServiceDto.sku}" already exists`,
        );
      }

      // Validate location allocation
      if (
        !createServiceDto.isAllocatedToAllLocations &&
        (!createServiceDto.locationIds ||
          createServiceDto.locationIds.length === 0)
      ) {
        throw new BadRequestException(
          'Location IDs are required when not allocated to all locations',
        );
      }

      // Validate locations exist if provided
      if (
        !createServiceDto.isAllocatedToAllLocations &&
        createServiceDto.locationIds &&
        createServiceDto.locationIds.length > 0
      ) {
        const existingLocations = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              inArray(locations.id, createServiceDto.locationIds),
              eq(locations.isDeleted, false),
            ),
          );

        if (existingLocations.length !== createServiceDto.locationIds.length) {
          throw new BadRequestException(
            'Some locations do not exist or belong to this business',
          );
        }
      }

      // Get the next position for this business
      const maxPositionResult = await this.db
        .select({
          maxPosition: sql<number>`COALESCE(MAX(${services.position}), -1)`,
        })
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            eq(services.isDeleted, false),
          ),
        );

      const nextPosition = (maxPositionResult[0]?.maxPosition ?? -1) + 1;

      // Create the service
      const [newService] = await this.db
        .insert(services)
        .values({
          businessId,
          name: createServiceDto.name,
          sku: createServiceDto.sku,
          serviceCategoryId: createServiceDto.serviceCategoryId,
          serviceSubCategoryId: createServiceDto.serviceSubCategoryId,
          description: createServiceDto.description,
          priceRate: createServiceDto.priceRate,
          incomeAccountId: createServiceDto.incomeAccountId,
          salesTaxId: createServiceDto.salesTaxId,
          sellToCustomers: createServiceDto.sellToCustomers ?? true,
          purchaseFromSupplier: createServiceDto.purchaseFromSupplier ?? false,
          purchaseDescription: createServiceDto.purchaseDescription,
          purchaseCost: createServiceDto.purchaseCost,
          expenseAccountId: createServiceDto.expenseAccountId,
          preferredSupplierId: createServiceDto.preferredSupplierId,
          availableOnline: createServiceDto.availableOnline ?? false,
          position: createServiceDto.position ?? nextPosition,
          seoTitle: createServiceDto.seoTitle,
          seoDescription: createServiceDto.seoDescription,
          seoKeywords: createServiceDto.seoKeywords,
          status: createServiceDto.status ?? ServiceStatus.ACTIVE,
          isAllocatedToAllLocations:
            createServiceDto.isAllocatedToAllLocations ?? false,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      let ogImageId: string | undefined;

      // Handle multiple service images upload first
      if (imageFiles && imageFiles.length > 0) {
        try {
          await this.mediaService.uploadMultipleMediaWithReference(
            imageFiles,
            'services',
            businessId,
            userId,
            newService.id,
          );
        } catch (error) {
          console.warn('Failed to upload service images:', error.message);
        }
      }

      // Handle OG image upload - use specific image from imageFiles if ogImageIndex is provided
      if (
        createServiceDto.ogImageIndex !== undefined &&
        imageFiles &&
        imageFiles.length > 0
      ) {
        const ogImageIndex = createServiceDto.ogImageIndex;
        if (ogImageIndex >= 0 && ogImageIndex < imageFiles.length) {
          try {
            const ogImageMedia = await this.mediaService.uploadMedia(
              imageFiles[ogImageIndex],
              'services/og-images',
              businessId,
              userId,
            );
            ogImageId = ogImageMedia.id;
          } catch (error) {
            console.warn(
              'Failed to upload OG image from imageFiles:',
              error.message,
            );
          }
        }
      } else if (ogImageFile) {
        // Handle separate OG image file
        try {
          const ogImageMedia = await this.mediaService.uploadMedia(
            ogImageFile,
            'services/og-images',
            businessId,
            userId,
          );
          ogImageId = ogImageMedia.id;
        } catch (error) {
          console.warn('Failed to upload OG image:', error.message);
        }
      }

      // Update service with OG image reference if we have one
      if (ogImageId) {
        await this.db
          .update(services)
          .set({ ogImage: ogImageId })
          .where(eq(services.id, newService.id));
      }

      // Handle location assignments
      if (
        !createServiceDto.isAllocatedToAllLocations &&
        createServiceDto.locationIds
      ) {
        const locationAssignments = createServiceDto.locationIds.map(
          (locationId) => ({
            serviceId: newService.id,
            locationId,
            businessId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db.insert(serviceLocations).values(locationAssignments);
      }

      // Handle staff assignments
      if (
        createServiceDto.staffMemberIds &&
        createServiceDto.staffMemberIds.length > 0
      ) {
        const staffAssignments = createServiceDto.staffMemberIds.map(
          (staffMemberId) => ({
            serviceId: newService.id,
            staffMemberId,
            businessId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db.insert(serviceStaff).values(staffAssignments);
      }

      // Log activity
      await this.activityLogService.logCreate(
        newService.id,
        EntityType.SERVICE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id: newService.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create service: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createServiceDto: CreateServiceDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    return this.create(
      userId,
      businessId,
      createServiceDto,
      imageFiles,
      ogImageFile,
    );
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createServiceDtos: CreateServiceDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<string[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (let i = 0; i < createServiceDtos.length; i++) {
      try {
        const createServiceDto = createServiceDtos[i];

        // Handle image assignment based on imageIndex
        let serviceImages: Express.Multer.File[] | undefined;
        if (
          imageFiles &&
          'imageIndex' in createServiceDto &&
          typeof createServiceDto.imageIndex === 'number'
        ) {
          const imageIndex = createServiceDto.imageIndex;
          if (imageIndex >= 0 && imageIndex < imageFiles.length) {
            serviceImages = [imageFiles[imageIndex]];
          }
        }

        const result = await this.create(
          userId,
          businessId,
          createServiceDto,
          serviceImages,
        );
        createdIds.push(result.id);
      } catch (error) {
        errors.push(`Service ${i + 1}: ${error.message}`);
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create services: ${errors.join(', ')}`,
      );
    }

    return createdIds;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createServiceDtos: CreateServiceDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    const ids = await this.bulkCreate(
      userId,
      businessId,
      createServiceDtos,
      imageFiles,
    );
    return { ids };
  }

  async findAll(
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'position',
    sortOrder: 'asc' | 'desc' = 'asc',
    status?: ServiceStatus,
    serviceCategoryId?: string,
  ): Promise<PaginatedServicesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(services.businessId, businessId),
      eq(services.isDeleted, false),
    ];

    if (search) {
      whereConditions.push(
        sql`(${services.name} ILIKE ${`%${search}%`} OR ${services.sku} ILIKE ${`%${search}%`} OR ${services.description} ILIKE ${`%${search}%`})`,
      );
    }

    if (status) {
      whereConditions.push(eq(services.status, status));
    }

    if (serviceCategoryId) {
      whereConditions.push(eq(services.serviceCategoryId, serviceCategoryId));
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(services)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;

    // Build order by
    const orderByColumn =
      sortBy === 'name'
        ? services.name
        : sortBy === 'sku'
          ? services.sku
          : sortBy === 'priceRate'
            ? services.priceRate
            : sortBy === 'createdAt'
              ? services.createdAt
              : services.position;

    const orderBy =
      sortOrder === 'desc' ? desc(orderByColumn) : asc(orderByColumn);

    // Get services with related data (optimized - exclude timestamps)
    const servicesData = await this.db
      .select({
        id: services.id,
        name: services.name,
        sku: services.sku,
        description: services.description,
        priceRate: services.priceRate,
        availableOnline: services.availableOnline,
        position: services.position,
        status: services.status,
        serviceCategoryName: serviceCategories.name,
      })
      .from(services)
      .leftJoin(
        serviceCategories,
        eq(services.serviceCategoryId, serviceCategories.id),
      )
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Generate signed URLs for first image of each service (optimized)
    const data: ServiceListDto[] = await Promise.all(
      servicesData.map(async (service) => {
        let imageUrl: string | undefined;

        // Get only the first image for list view (optimization)
        const serviceImages = await this.mediaService.findByReferenceId(
          service.id,
          businessId,
        );

        if (serviceImages.length > 0) {
          try {
            // Generate signed URL for the first image only
            imageUrl = await this.mediaService.generateSignedUrlForMedia(
              serviceImages[0].id,
              businessId,
              'services',
              60, // 60 minutes expiration
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for service ${service.id} image:`,
              error.message,
            );
          }
        }

        return {
          id: service.id,
          name: service.name,
          sku: service.sku,
          description: service.description,
          priceRate: service.priceRate,
          serviceCategoryName: service.serviceCategoryName || '',
          availableOnline: service.availableOnline,
          position: service.position,
          status: service.status,
          image: imageUrl, // Only first image with signed URL
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, businessId: string | null): Promise<ServiceDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const serviceData = (await this.db
      .select({
        id: services.id,
        businessId: services.businessId,
        name: services.name,
        itemType: services.itemType,
        sku: services.sku,
        serviceCategoryId: services.serviceCategoryId,
        serviceSubCategoryId: services.serviceSubCategoryId,
        description: services.description,
        priceRate: services.priceRate,
        incomeAccountId: services.incomeAccountId,
        salesTaxId: services.salesTaxId,
        sellToCustomers: services.sellToCustomers,
        purchaseFromSupplier: services.purchaseFromSupplier,
        purchaseDescription: services.purchaseDescription,
        purchaseCost: services.purchaseCost,
        expenseAccountId: services.expenseAccountId,
        preferredSupplierId: services.preferredSupplierId,
        availableOnline: services.availableOnline,
        position: services.position,
        seoTitle: services.seoTitle,
        seoDescription: services.seoDescription,
        seoKeywords: services.seoKeywords,
        ogImage: services.ogImage,
        isAllocatedToAllLocations: services.isAllocatedToAllLocations,
        status: services.status,
        createdAt: services.createdAt,
        updatedAt: services.updatedAt,
        createdBy: services.createdBy,
        updatedBy: services.updatedBy,
        // Related data
        serviceCategoryName: serviceCategories.name,
        serviceSubCategoryName: sql<string>`service_sub_category.name`,
        incomeAccountName: accounts.name,
        salesTaxName: taxes.taxName,
        expenseAccountName: sql<string>`expense_account.name`,
        preferredSupplierName: suppliers.displayName,
        createdByName: sql<string>`creator.first_name || ' ' || creator.last_name`,
        updatedByName: sql<string>`updater.first_name || ' ' || updater.last_name`,
        ogImageUrl: media.publicUrl,
      })
      .from(services)
      .leftJoin(
        serviceCategories,
        eq(services.serviceCategoryId, serviceCategories.id),
      )
      .leftJoin(
        sql`service_categories AS service_sub_category`,
        sql`${services.serviceSubCategoryId} = service_sub_category.id`,
      )
      .leftJoin(accounts, eq(services.incomeAccountId, accounts.id))
      .leftJoin(taxes, eq(services.salesTaxId, taxes.id))
      .leftJoin(
        sql`accounts AS expense_account`,
        sql`${services.expenseAccountId} = expense_account.id`,
      )
      .leftJoin(suppliers, eq(services.preferredSupplierId, suppliers.id))
      .leftJoin(sql`users AS creator`, sql`${services.createdBy} = creator.id`)
      .leftJoin(sql`users AS updater`, sql`${services.updatedBy} = updater.id`)
      .leftJoin(media, eq(services.ogImage, media.id))
      .where(
        and(
          eq(services.id, id),
          eq(services.businessId, businessId),
          eq(services.isDeleted, false),
        ),
      )
      .then((results) => results[0])) as ServiceDto;

    if (!serviceData) {
      throw new NotFoundException('Service not found');
    }

    // Get service images and generate signed URLs for all
    const serviceImages = await this.mediaService.findByReferenceId(
      id,
      businessId,
    );

    // Generate signed URLs for all service images
    const imagesWithSignedUrls = await Promise.all(
      serviceImages.map(async (img) => {
        try {
          const signedUrl = await this.mediaService.generateSignedUrlForMedia(
            img.id,
            businessId,
            'services',
            60, // 60 minutes expiration
          );
          return signedUrl;
        } catch (error) {
          console.warn(
            `Failed to generate signed URL for service image ${img.id}:`,
            error.message,
          );
          return img.publicUrl;
        }
      }),
    );

    // Generate signed URL for OG image if exists
    let ogImageUrl: string | undefined;
    if (serviceData.ogImage) {
      try {
        ogImageUrl = await this.mediaService.generateSignedUrlForMedia(
          serviceData.ogImage,
          businessId,
          'services/og-images',
          60, // 60 minutes expiration
        );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for service ${id} OG image:`,
          error.message,
        );
        ogImageUrl = serviceData.ogImage; // Fallback to original URL
      }
    }

    // Get locations
    let serviceLocationsData: { id: string; name: string }[] = [];
    if (!serviceData.isAllocatedToAllLocations) {
      serviceLocationsData = await this.db
        .select({
          id: locations.id,
          name: locations.name,
        })
        .from(serviceLocations)
        .leftJoin(locations, eq(serviceLocations.locationId, locations.id))
        .where(eq(serviceLocations.serviceId, id));
    }

    // Get staff members
    const serviceStaffData = await this.db
      .select({
        id: staffMembers.id,
        name: sql<string>`${staffMembers.firstName} || ' ' || ${staffMembers.lastName}`,
      })
      .from(serviceStaff)
      .leftJoin(staffMembers, eq(serviceStaff.staffMemberId, staffMembers.id))
      .where(eq(serviceStaff.serviceId, id));

    return {
      id: serviceData.id,
      businessId: serviceData.businessId,
      name: serviceData.name,
      itemType: serviceData.itemType,
      sku: serviceData.sku,
      serviceCategoryId: serviceData.serviceCategoryId,
      serviceCategoryName: serviceData.serviceCategoryName || '',
      description: serviceData.description,
      priceRate: serviceData.priceRate,
      incomeAccountId: serviceData.incomeAccountId,
      incomeAccountName: serviceData.incomeAccountName || '',
      salesTaxId: serviceData.salesTaxId,
      salesTaxName: serviceData.salesTaxName || '',
      sellToCustomers: serviceData.sellToCustomers,
      purchaseFromSupplier: serviceData.purchaseFromSupplier,
      purchaseDescription: serviceData.purchaseDescription,
      purchaseCost: serviceData.purchaseCost,
      expenseAccountId: serviceData.expenseAccountId,
      expenseAccountName: serviceData.expenseAccountName,
      preferredSupplierId: serviceData.preferredSupplierId,
      preferredSupplierName: serviceData.preferredSupplierName,
      availableOnline: serviceData.availableOnline,
      position: serviceData.position,
      seoTitle: serviceData.seoTitle,
      seoDescription: serviceData.seoDescription,
      seoKeywords: serviceData.seoKeywords,
      ogImage: ogImageUrl,
      images: imagesWithSignedUrls,
      isAllocatedToAllLocations: serviceData.isAllocatedToAllLocations,
      locations: serviceLocationsData,
      staffMembers: serviceStaffData,
      createdBy: serviceData.createdBy,
      updatedBy: serviceData.updatedBy,
      status: serviceData.status,
      createdAt: serviceData.createdAt,
      updatedAt: serviceData.updatedAt,
      deletedAt: null, // Always null since we only return non-deleted services
    };
  }

  async update(
    id: string,
    userId: string,
    businessId: string | null,
    updateServiceDto: UpdateServiceDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if service exists
    const existingService = await this.db
      .select()
      .from(services)
      .where(
        and(
          eq(services.id, id),
          eq(services.businessId, businessId),
          eq(services.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingService) {
      throw new NotFoundException('Service not found');
    }

    // Check for SKU conflicts if SKU is being updated
    if (updateServiceDto.sku && updateServiceDto.sku !== existingService.sku) {
      const conflictingService = await this.db
        .select()
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            ilike(services.sku, updateServiceDto.sku),
            sql`${services.id} != ${id}`,
            eq(services.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (conflictingService) {
        throw new ConflictException(
          `Service with SKU "${updateServiceDto.sku}" already exists`,
        );
      }
    }

    // Validate location allocation
    if (
      updateServiceDto.isAllocatedToAllLocations === false &&
      (!updateServiceDto.locationIds ||
        updateServiceDto.locationIds.length === 0)
    ) {
      throw new BadRequestException(
        'Location IDs are required when not allocated to all locations',
      );
    }

    // Validate locations exist if provided
    if (
      updateServiceDto.isAllocatedToAllLocations === false &&
      updateServiceDto.locationIds &&
      updateServiceDto.locationIds.length > 0
    ) {
      const existingLocations = await this.db
        .select({ id: locations.id })
        .from(locations)
        .where(
          and(
            eq(locations.businessId, businessId),
            inArray(locations.id, updateServiceDto.locationIds),
            eq(locations.isDeleted, false),
          ),
        );

      if (existingLocations.length !== updateServiceDto.locationIds.length) {
        throw new BadRequestException(
          'Some locations do not exist or belong to this business',
        );
      }
    }

    // Update the service
    await this.db
      .update(services)
      .set({
        ...updateServiceDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(services.id, id));

    let ogImageId = existingService.ogImage;

    // Handle service images update first
    if (imageFiles && imageFiles.length > 0) {
      try {
        await this.mediaService.updateMediaForReference(
          id,
          imageFiles,
          'services',
          businessId,
          userId,
        );
      } catch (error) {
        console.warn('Failed to update service images:', error.message);
      }
    }

    // Handle OG image update - use specific image from imageFiles if ogImageIndex is provided
    if (
      updateServiceDto.ogImageIndex !== undefined &&
      imageFiles &&
      imageFiles.length > 0
    ) {
      const ogImageIndex = updateServiceDto.ogImageIndex;
      if (ogImageIndex >= 0 && ogImageIndex < imageFiles.length) {
        try {
          ogImageId = await this.mediaService.updateMediaReference(
            existingService.ogImage,
            imageFiles[ogImageIndex],
            'services/og-images',
            businessId,
            userId,
          );
        } catch (error) {
          console.warn(
            'Failed to update OG image from imageFiles:',
            error.message,
          );
        }
      }
    } else if (ogImageFile) {
      // Handle separate OG image file
      try {
        ogImageId = await this.mediaService.updateMediaReference(
          existingService.ogImage,
          ogImageFile,
          'services/og-images',
          businessId,
          userId,
        );
      } catch (error) {
        console.warn('Failed to update OG image:', error.message);
      }
    }

    // Update service with OG image reference if it changed
    if (ogImageId !== existingService.ogImage) {
      await this.db
        .update(services)
        .set({ ogImage: ogImageId })
        .where(eq(services.id, id));
    }

    // Handle location assignments update
    if (updateServiceDto.isAllocatedToAllLocations !== undefined) {
      // Delete existing location assignments
      await this.db
        .delete(serviceLocations)
        .where(eq(serviceLocations.serviceId, id));

      // Add new location assignments if not allocated to all locations
      if (
        !updateServiceDto.isAllocatedToAllLocations &&
        updateServiceDto.locationIds
      ) {
        const locationAssignments = updateServiceDto.locationIds.map(
          (locationId) => ({
            serviceId: id,
            locationId,
            businessId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db.insert(serviceLocations).values(locationAssignments);
      }
    }

    // Handle staff assignments update
    if (updateServiceDto.staffMemberIds !== undefined) {
      // Delete existing staff assignments
      await this.db.delete(serviceStaff).where(eq(serviceStaff.serviceId, id));

      // Add new staff assignments
      if (updateServiceDto.staffMemberIds.length > 0) {
        const staffAssignments = updateServiceDto.staffMemberIds.map(
          (staffMemberId) => ({
            serviceId: id,
            staffMemberId,
            businessId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db.insert(serviceStaff).values(staffAssignments);
      }
    }

    // Log activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.SERVICE,
      userId,
      businessId,
      {
        source: ActivitySource.WEB,
      },
    );

    return { id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceDto: UpdateServiceDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    return this.update(
      id,
      userId,
      businessId,
      updateServiceDto,
      imageFiles,
      ogImageFile,
    );
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<DeleteServiceResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if service exists
    const existingService = await this.db
      .select()
      .from(services)
      .where(
        and(
          eq(services.id, id),
          eq(services.businessId, businessId),
          eq(services.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingService) {
      throw new NotFoundException('Service not found');
    }

    // Soft delete the service
    await this.db
      .update(services)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(services.id, id));

    // Delete associated media
    try {
      await this.mediaService.deleteMediaByReferenceId(
        id,
        businessId,
        'services',
      );
    } catch (error) {
      console.warn('Failed to delete service media:', error.message);
    }

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.SERVICE,
      userId,
      businessId,
      {
        source: ActivitySource.WEB,
      },
    );

    return {
      success: true,
      message: `Service with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    serviceIds: string[],
  ): Promise<BulkDeleteServiceResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!serviceIds || serviceIds.length === 0) {
      throw new BadRequestException('No service IDs provided');
    }

    // Check if all services exist
    const existingServices = await this.db
      .select({
        id: services.id,
        name: services.name,
        businessId: services.businessId,
      })
      .from(services)
      .where(
        and(
          inArray(services.id, serviceIds),
          eq(services.businessId, businessId),
          eq(services.isDeleted, false),
        ),
      );

    if (existingServices.length === 0) {
      throw new NotFoundException('No services found for deletion');
    }

    const deletedIds: string[] = [];
    const currentTime = new Date();

    // Use transaction to ensure all deletions succeed or fail together
    await this.db.transaction(async (tx) => {
      for (const service of existingServices) {
        // Soft delete the service
        await tx
          .update(services)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: currentTime,
          })
          .where(eq(services.id, service.id));

        deletedIds.push(service.id);

        // Delete associated media
        try {
          await this.mediaService.deleteMediaByReferenceId(
            service.id,
            businessId,
            'services',
          );
        } catch (error) {
          console.warn(
            `Failed to delete media for service ${service.id}:`,
            error.message,
          );
        }

        // Log the activity for each deleted service
        await this.activityLogService.logDelete(
          service.id,
          EntityType.SERVICE,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );
      }
    });

    return {
      deleted: deletedIds.length,
      message: `Successfully deleted ${deletedIds.length} services`,
      deletedIds,
    };
  }

  async findAllSlim(businessId: string | null): Promise<ServiceSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const servicesData = await this.db
      .select({
        id: services.id,
        name: services.name,
        sku: services.sku,
        position: services.position,
        priceRate: services.priceRate,
      })
      .from(services)
      .where(
        and(
          eq(services.businessId, businessId),
          eq(services.status, ServiceStatus.ACTIVE),
          eq(services.isDeleted, false),
        ),
      )
      .orderBy(asc(services.position));

    // Get images for each service
    const serviceIds = servicesData.map((service) => service.id);
    const imagesData: any[] = [];

    if (serviceIds.length > 0) {
      for (const serviceId of serviceIds) {
        const serviceImages = await this.mediaService.findByReferenceId(
          serviceId,
          businessId,
        );
        imagesData.push(
          ...serviceImages.map((img) => ({ ...img, referenceId: serviceId })),
        );
      }
    }

    return servicesData.map((service) => {
      const serviceImages = imagesData.filter(
        (img) => img.referenceId === service.id,
      );

      return {
        id: service.id,
        name: service.name,
        sku: service.sku,
        position: service.position,
        priceRate: service.priceRate,
        images: serviceImages.map((img) => ({
          id: img.id,
          publicUrl: img.publicUrl,
          originalName: img.originalName,
        })),
      };
    });
  }

  async checkSkuAvailability(
    sku: string,
    businessId: string | null,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(services.businessId, businessId),
      ilike(services.sku, sku),
      eq(services.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${services.id} != ${excludeId}`);
    }

    const existingService = await this.db
      .select()
      .from(services)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingService };
  }

  /**
   * Get services count for multiple service types
   * Used by service-categories module to display service counts
   */
  async getServicesCountForServiceCategories(
    serviceCategoryIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (serviceCategoryIds.length === 0) {
        return new Map();
      }

      // Count services by service type ID
      const serviceTypeResults = await this.db
        .select({
          serviceCategoryId: services.serviceCategoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            inArray(services.serviceCategoryId, serviceCategoryIds),
            eq(services.isDeleted, false),
          ),
        )
        .groupBy(services.serviceCategoryId);

      // Create lookup map
      const countsMap = new Map<string, number>();

      // Initialize all service type IDs with 0
      serviceCategoryIds.forEach((id) => countsMap.set(id, 0));

      // Add actual counts
      serviceTypeResults.forEach((result) => {
        if (result.serviceCategoryId) {
          countsMap.set(result.serviceCategoryId, result.count);
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get services count for service types:',
        error.message,
      );
      return new Map();
    }
  }

  /**
   * Get services count for a single service type
   * Used by service-categories module to display service count
   */
  async getServicesCountForServiceCategory(
    serviceCategoryId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({
          count: sql<number>`count(*)`.as('count'),
        })
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            eq(services.serviceCategoryId, serviceCategoryId),
            eq(services.isDeleted, false),
          ),
        );

      return Number(result[0]?.count || 0);
    } catch (error) {
      console.warn(
        `Failed to get services count for service category ${serviceCategoryId}:`,
        error.message,
      );
      return 0;
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a service with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingService = await this.db
      .select()
      .from(services)
      .where(
        and(
          eq(services.businessId, businessId),
          ilike(services.name, name),
          eq(services.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingService };
  }

  async checkSlugAvailability(
    userId: string,
    businessId: string | null,
    slug: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a service with the same slug already exists for this business
    const existingService = await this.db
      .select()
      .from(services)
      .where(
        and(
          eq(services.businessId, businessId),
          eq(services.seoTitle, slug), // Using seoTitle as slug equivalent
          eq(services.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingService };
  }

  async checkShortCodeAvailability(
    userId: string,
    businessId: string | null,
    shortCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a service with the same SKU already exists for this business
    // Using SKU as the equivalent of shortCode for services
    const existingService = await this.db
      .select()
      .from(services)
      .where(
        and(
          eq(services.businessId, businessId),
          eq(services.sku, shortCode),
          eq(services.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingService };
  }

  async updateServicePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for service ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all services belong to the business
      const serviceIds = updates.map((update) => update.id);
      const existingServices = await this.db
        .select({ id: services.id })
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            inArray(services.id, serviceIds),
            eq(services.isDeleted, false),
          ),
        );

      if (existingServices.length !== serviceIds.length) {
        throw new BadRequestException(
          'Some services do not exist or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          await tx
            .update(services)
            .set({
              position: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(services.id, update.id),
                eq(services.businessId, businessId),
                eq(services.isDeleted, false),
              ),
            );

          // Log position update activity
          await this.activityLogService.logUpdate(
            update.id,
            EntityType.SERVICE,
            userId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );
        }
        updatedCount = updates.length;
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update service positions: ${error.message}`,
      );
    }
  }
}
