import { ApiProperty } from '@nestjs/swagger';
import { ServiceStatus } from '../../shared/types';

export class ServiceDto {
  @ApiProperty({ description: 'Unique identifier for the service' })
  id: string;

  @ApiProperty({ description: 'Business ID the service belongs to' })
  businessId: string;

  @ApiProperty({ description: 'Service name' })
  name: string;

  @ApiProperty({ description: 'Item type (always "service")' })
  itemType: string;

  @ApiProperty({ description: 'SKU for the service' })
  sku: string;

  @ApiProperty({ description: 'Service category ID' })
  serviceCategoryId: string;

  @ApiProperty({ description: 'Service category name' })
  serviceCategoryName: string;

  @ApiProperty({
    description: 'Service sub category ID',
    required: false,
    nullable: true,
  })
  serviceSubCategoryId?: string;

  @ApiProperty({
    description: 'Service sub category name',
    required: false,
    nullable: true,
  })
  serviceSubCategoryName?: string;

  @ApiProperty({
    description: 'Service description',
    required: false,
    nullable: true,
  })
  description?: string;

  @ApiProperty({ description: 'Price rate for the service' })
  priceRate: string;

  @ApiProperty({ description: 'Income account ID' })
  incomeAccountId: string;

  @ApiProperty({ description: 'Income account name' })
  incomeAccountName: string;

  @ApiProperty({ description: 'Sales tax ID' })
  salesTaxId: string;

  @ApiProperty({ description: 'Sales tax name' })
  salesTaxName: string;

  @ApiProperty({ description: 'Whether service can be sold to customers' })
  sellToCustomers: boolean;

  @ApiProperty({ description: 'Whether to purchase from supplier' })
  purchaseFromSupplier: boolean;

  @ApiProperty({
    description: 'Purchase description',
    required: false,
    nullable: true,
  })
  purchaseDescription?: string;

  @ApiProperty({
    description: 'Purchase cost',
    required: false,
    nullable: true,
  })
  purchaseCost?: string;

  @ApiProperty({
    description: 'Expense account ID',
    required: false,
    nullable: true,
  })
  expenseAccountId?: string;

  @ApiProperty({
    description: 'Expense account name',
    required: false,
    nullable: true,
  })
  expenseAccountName?: string;

  @ApiProperty({
    description: 'Preferred supplier ID',
    required: false,
    nullable: true,
  })
  preferredSupplierId?: string;

  @ApiProperty({
    description: 'Preferred supplier name',
    required: false,
    nullable: true,
  })
  preferredSupplierName?: string;

  @ApiProperty({ description: 'Whether service is available online' })
  availableOnline: boolean;

  @ApiProperty({ description: 'Display position' })
  position: number;

  @ApiProperty({
    description: 'SEO title',
    required: false,
    nullable: true,
  })
  seoTitle?: string;

  @ApiProperty({
    description: 'SEO description',
    required: false,
    nullable: true,
  })
  seoDescription?: string;

  @ApiProperty({
    description: 'SEO keywords',
    required: false,
    nullable: true,
    type: [String],
  })
  seoKeywords?: string[];

  @ApiProperty({
    description: 'OG image URL',
    required: false,
    nullable: true,
  })
  ogImage?: string;

  @ApiProperty({
    description: 'Service images',
    type: [Object],
    example: [
      {
        id: 'uuid',
        publicUrl: 'https://example.com/image.jpg',
        originalName: 'image.jpg',
      },
    ],
  })
  images: string[];

  @ApiProperty({ description: 'Whether service is allocated to all locations' })
  isAllocatedToAllLocations: boolean;

  @ApiProperty({
    description: 'Locations assigned to this service',
    type: [Object],
    example: [{ id: 'uuid', name: 'Main Location' }],
  })
  locations: { id: string; name: string }[];

  @ApiProperty({
    description: 'Staff members assigned to this service',
    type: [Object],
    example: [{ id: 'uuid', name: 'John Doe' }],
  })
  staffMembers: { id: string; name: string }[];

  @ApiProperty({
    example: 'John Doe',
    description: 'Name of the user who created the service',
  })
  createdBy: string;

  @ApiProperty({
    example: 'Jane Smith',
    description: 'Name of the user who last updated the service',
    required: false,
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    example: ServiceStatus.ACTIVE,
    enum: ServiceStatus,
    enumName: 'ServiceStatus',
    description: 'Service status',
  })
  status: ServiceStatus;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Deletion timestamp',
    required: false,
    nullable: true,
  })
  deletedAt?: Date;
}
