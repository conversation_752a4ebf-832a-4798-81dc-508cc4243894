import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsBoolean,
  IsEnum,
  IsNumber,
  Min,
  IsArray,
  IsDecimal,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ServiceStatus } from '../../shared/types';

export class CreateServiceDto {
  @ApiProperty({ description: 'Service name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  name: string;

  @ApiProperty({ description: 'SKU for the service', maxLength: 100 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  sku: string;

  @ApiProperty({ description: 'Service category ID' })
  @IsNotEmpty()
  @IsUUID()
  serviceCategoryId: string;

  @ApiProperty({
    description: 'Service sub category ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  serviceSubCategoryId?: string;

  @ApiProperty({
    description: 'Service description',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Price rate for the service',
    example: '99.99',
  })
  @IsNotEmpty()
  @IsDecimal({ decimal_digits: '0,2' })
  priceRate: string;

  @ApiProperty({ description: 'Income account ID' })
  @IsNotEmpty()
  @IsUUID()
  incomeAccountId: string;

  @ApiProperty({ description: 'Sales tax ID' })
  @IsNotEmpty()
  @IsUUID()
  salesTaxId: string;

  @ApiProperty({
    description: 'Whether service can be sold to customers',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  sellToCustomers?: boolean;

  @ApiProperty({
    description: 'Whether to purchase from supplier',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  purchaseFromSupplier?: boolean;

  @ApiProperty({
    description: 'Purchase description',
    required: false,
  })
  @IsOptional()
  @IsString()
  purchaseDescription?: string;

  @ApiProperty({
    description: 'Purchase cost',
    required: false,
    example: '50.00',
  })
  @IsOptional()
  @IsDecimal({ decimal_digits: '0,2' })
  purchaseCost?: string;

  @ApiProperty({
    description: 'Expense account ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  expenseAccountId?: string;

  @ApiProperty({
    description: 'Preferred supplier ID',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  preferredSupplierId?: string;

  @ApiProperty({
    description: 'Whether service is available online',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  availableOnline?: boolean;

  @ApiProperty({
    description: 'Display position',
    default: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  position?: number;

  @ApiProperty({
    description: 'SEO title',
    required: false,
  })
  @IsOptional()
  @IsString()
  seoTitle?: string;

  @ApiProperty({
    description: 'SEO description',
    required: false,
  })
  @IsOptional()
  @IsString()
  seoDescription?: string;

  @ApiProperty({
    description: 'SEO keywords',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  seoKeywords?: string[];

  @ApiProperty({
    description: 'Service status',
    enum: ServiceStatus,
    default: ServiceStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ServiceStatus)
  status?: ServiceStatus;

  @ApiPropertyOptional({
    description: 'Whether service is allocated to all locations',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isAllocatedToAllLocations?: boolean;

  @ApiPropertyOptional({
    description:
      'Array of location IDs to assign service to (ignored if isAllocatedToAllLocations is true)',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      // If it's a single string, try to parse as JSON or wrap in array
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [value];
      }
    }
    if (Array.isArray(value)) {
      return value;
    }
    return value;
  })
  locationIds?: string[];

  @ApiPropertyOptional({
    description: 'Array of staff member IDs to assign to this service',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [value];
      }
    }
    if (Array.isArray(value)) {
      return value;
    }
    return value;
  })
  staffMemberIds?: string[];

  @ApiPropertyOptional({
    example: 0,
    description:
      'Index of the image file in the images array to use for this service (0-based)',
    minimum: 0,
  })
  @IsNumber({}, { message: 'Image index must be a number' })
  @Min(0, { message: 'Image index must be 0 or greater' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  imageIndex?: number;

  @ApiPropertyOptional({
    example: 0,
    description:
      'Index of the Open Graph image file in the images array to use for social media sharing (0-based)',
    minimum: 0,
  })
  @IsNumber({}, { message: 'OG image index must be a number' })
  @Min(0, { message: 'OG image index must be 0 or greater' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? value : parsed;
    }
    return value;
  })
  ogImageIndex?: number;
}
