import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateMenuItemDto } from './dto/create-menu-item.dto';
import { UpdateMenuItemDto } from './dto/update-menu-item.dto';
import { MenuItemDto } from './dto/menu-item.dto';
import { MenuItemSlimDto } from './dto/menu-item-slim.dto';
import { MenuItemListDto } from './dto/menu-item-list.dto';
import { menuItems, recipes } from '../drizzle/schema/menu-items.schema';
import { products } from '../drizzle/schema/products.schema';
import { media } from '../drizzle/schema/media.schema';

import {
  and,
  eq,
  isNull,
  desc,
  asc,
  ilike,
  or,
  sql,
  inArray,
} from 'drizzle-orm';
import { MediaService } from '../media/media.service';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { UsersService } from '../users/users.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { FlavorProfile } from '../shared/types';

@Injectable()
export class MenuItemsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly mediaService: MediaService,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createMenuItemDto: CreateMenuItemDto,
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify the product exists and belongs to the business
      const product = await this.db
        .select()
        .from(products)
        .where(
          and(
            eq(products.id, createMenuItemDto.productId),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!product) {
        throw new BadRequestException(
          'Product not found or does not belong to this business',
        );
      }

      // Check if menu item already exists for this product
      const existingMenuItem = await this.db
        .select()
        .from(menuItems)
        .where(
          and(
            eq(menuItems.productId, createMenuItemDto.productId),
            eq(menuItems.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingMenuItem) {
        throw new ConflictException(
          'Menu item already exists for this product',
        );
      }

      // Verify recipe exists if provided
      if (createMenuItemDto.recipeId) {
        const recipe = await this.db
          .select()
          .from(recipes)
          .where(
            and(
              eq(recipes.id, createMenuItemDto.recipeId),
              eq(recipes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!recipe) {
          throw new BadRequestException('Recipe not found');
        }
      }

      // Create the menu item
      const newMenuItem = await this.db
        .insert(menuItems)
        .values({
          businessId,
          productId: createMenuItemDto.productId,
          prepTimeMinutes: createMenuItemDto.prepTimeMinutes?.toString(),
          cookTimeMinutes: createMenuItemDto.cookTimeMinutes?.toString(),
          recipeId: createMenuItemDto.recipeId,
          servingsPerOrder: createMenuItemDto.servingsPerOrder ?? 1,
          caloriesPerServing: createMenuItemDto.caloriesPerServing,
          availableBreakfast: createMenuItemDto.availableBreakfast ?? false,
          availableBrunch: createMenuItemDto.availableBrunch ?? false,
          availableLunch: createMenuItemDto.availableLunch ?? false,
          availableHappyHour: createMenuItemDto.availableHappyHour ?? false,
          availableDinner: createMenuItemDto.availableDinner ?? false,
          availableLateNight: createMenuItemDto.availableLateNight ?? false,
          isVegetarian: createMenuItemDto.isVegetarian ?? false,
          isVegan: createMenuItemDto.isVegan ?? false,
          isGlutenFree: createMenuItemDto.isGlutenFree ?? false,
          isDairyFree: createMenuItemDto.isDairyFree ?? false,
          isNutFree: createMenuItemDto.isNutFree ?? false,
          isHalal: createMenuItemDto.isHalal ?? false,
          isKosher: createMenuItemDto.isKosher ?? false,
          spiceLevel: createMenuItemDto.spiceLevel ?? 0,
          flavorProfile:
            createMenuItemDto.flavorProfile ?? FlavorProfile.SAVORY,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: menuItems.id });

      const menuItemId = newMenuItem[0].id;

      // Handle multiple image uploads if provided
      if (imageFiles && imageFiles.length > 0) {
        try {
          await this.mediaService.uploadMultipleMediaWithReference(
            imageFiles,
            'menu-items/images',
            businessId,
            userId,
            menuItemId,
          );
        } catch (error) {
          console.warn('Failed to upload menu item images:', error.message);
        }
      }

      // Log activity
      await this.activityLogService.logCreate(
        menuItemId,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          reason: `Created menu item for product: ${product.name}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: menuItemId };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create menu item: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    search?: string,
    isVegetarian?: string,
    isVegan?: string,
    isGlutenFree?: string,
    spiceLevel?: string,
    flavorProfile?: string,
    availableBreakfast?: string,
    availableLunch?: string,
    availableDinner?: string,
  ): Promise<{
    data: MenuItemListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(menuItems.isDeleted, false),
      eq(products.businessId, businessId),
      eq(products.isDeleted, false),
    ];

    // Add search condition
    if (search) {
      whereConditions.push(
        or(
          ilike(products.name, `%${search}%`),
          ilike(products.description, `%${search}%`),
        ),
      );
    }

    // Add filter conditions
    if (isVegetarian === 'true') {
      whereConditions.push(eq(menuItems.isVegetarian, true));
    }
    if (isVegan === 'true') {
      whereConditions.push(eq(menuItems.isVegan, true));
    }
    if (isGlutenFree === 'true') {
      whereConditions.push(eq(menuItems.isGlutenFree, true));
    }
    if (spiceLevel) {
      whereConditions.push(eq(menuItems.spiceLevel, parseInt(spiceLevel)));
    }
    if (flavorProfile) {
      whereConditions.push(eq(menuItems.flavorProfile, flavorProfile as any));
    }
    if (availableBreakfast === 'true') {
      whereConditions.push(eq(menuItems.availableBreakfast, true));
    }
    if (availableLunch === 'true') {
      whereConditions.push(eq(menuItems.availableLunch, true));
    }
    if (availableDinner === 'true') {
      whereConditions.push(eq(menuItems.availableDinner, true));
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(menuItems)
      .innerJoin(products, eq(menuItems.productId, products.id))
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with images
    const menuItemsData = await this.db
      .select({
        id: menuItems.id,
        productId: menuItems.productId,
        name: products.name,
        description: products.description,
        price: products.basePrice,
        prepTimeMinutes: menuItems.prepTimeMinutes,
        cookTimeMinutes: menuItems.cookTimeMinutes,
        caloriesPerServing: menuItems.caloriesPerServing,
        isVegetarian: menuItems.isVegetarian,
        isVegan: menuItems.isVegan,
        isGlutenFree: menuItems.isGlutenFree,
        spiceLevel: menuItems.spiceLevel,
        flavorProfile: menuItems.flavorProfile,
        createdAt: menuItems.createdAt,
        updatedAt: menuItems.updatedAt,
      })
      .from(menuItems)
      .innerJoin(products, eq(menuItems.productId, products.id))
      .where(and(...whereConditions))
      .orderBy(desc(menuItems.createdAt))
      .limit(limit)
      .offset(offset);

    // Get images for each menu item
    const menuItemIds = menuItemsData.map((item) => item.id);
    const imagesData =
      menuItemIds.length > 0
        ? await this.db
            .select({
              referenceId: media.referenceId,
              id: media.id,
              publicUrl: media.publicUrl,
              originalName: media.originalName,
            })
            .from(media)
            .where(
              and(
                inArray(media.referenceId, menuItemIds),
                eq(media.businessId, businessId),
              ),
            )
        : [];

    // Group images by menu item ID
    const imagesByMenuItemId = imagesData.reduce(
      (acc, image) => {
        if (!acc[image.referenceId]) {
          acc[image.referenceId] = [];
        }
        acc[image.referenceId].push({
          id: image.id,
          publicUrl: image.publicUrl,
          originalName: image.originalName,
        });
        return acc;
      },
      {} as Record<string, any[]>,
    );

    const formattedData: MenuItemListDto[] = menuItemsData.map((item) => ({
      id: item.id,
      productId: item.productId,
      name: item.name,
      description: item.description,
      price: item.price ? parseFloat(item.price) : undefined,
      prepTimeMinutes: item.prepTimeMinutes
        ? parseFloat(item.prepTimeMinutes)
        : undefined,
      cookTimeMinutes: item.cookTimeMinutes
        ? parseFloat(item.cookTimeMinutes)
        : undefined,
      caloriesPerServing: item.caloriesPerServing,
      isVegetarian: item.isVegetarian,
      isVegan: item.isVegan,
      isGlutenFree: item.isGlutenFree,
      spiceLevel: item.spiceLevel,
      flavorProfile: item.flavorProfile as any,
      image: imagesByMenuItemId[item.id]?.[0],
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    }));

    return {
      data: formattedData,
      meta: { total, page, totalPages },
    };
  }

  async findAllSlim(businessId: string | null): Promise<MenuItemSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const menuItemsData = await this.db
      .select({
        id: menuItems.id,
        name: products.name,
        productId: menuItems.productId,
        prepTimeMinutes: menuItems.prepTimeMinutes,
        spiceLevel: menuItems.spiceLevel,
      })
      .from(menuItems)
      .innerJoin(products, eq(menuItems.productId, products.id))
      .where(
        and(
          eq(menuItems.isDeleted, false),
          eq(products.businessId, businessId),
          eq(products.isDeleted, false),
        ),
      )
      .orderBy(asc(products.name));

    // Get first image for each menu item
    const menuItemIds = menuItemsData.map((item) => item.id);
    const imagesData =
      menuItemIds.length > 0
        ? await this.db
            .select({
              referenceId: media.referenceId,
              id: media.id,
              publicUrl: media.publicUrl,
              originalName: media.originalName,
            })
            .from(media)
            .where(
              and(
                inArray(media.referenceId, menuItemIds),
                eq(media.businessId, businessId),
              ),
            )
        : [];

    // Group images by menu item ID (take first image only)
    const imagesByMenuItemId = imagesData.reduce(
      (acc, image) => {
        if (!acc[image.referenceId]) {
          acc[image.referenceId] = {
            id: image.id,
            publicUrl: image.publicUrl,
            originalName: image.originalName,
          };
        }
        return acc;
      },
      {} as Record<string, any>,
    );

    return menuItemsData.map((item) => ({
      id: item.id,
      name: item.name,
      productId: item.productId,
      prepTimeMinutes: item.prepTimeMinutes
        ? parseFloat(item.prepTimeMinutes)
        : undefined,
      spiceLevel: item.spiceLevel,
      image: imagesByMenuItemId[item.id],
    }));
  }

  async findOne(id: string): Promise<MenuItemDto> {
    const menuItemData = await this.db
      .select({
        id: menuItems.id,
        productId: menuItems.productId,
        prepTimeMinutes: menuItems.prepTimeMinutes,
        cookTimeMinutes: menuItems.cookTimeMinutes,
        recipeId: menuItems.recipeId,
        servingsPerOrder: menuItems.servingsPerOrder,
        caloriesPerServing: menuItems.caloriesPerServing,
        availableBreakfast: menuItems.availableBreakfast,
        availableBrunch: menuItems.availableBrunch,
        availableLunch: menuItems.availableLunch,
        availableHappyHour: menuItems.availableHappyHour,
        availableDinner: menuItems.availableDinner,
        availableLateNight: menuItems.availableLateNight,
        isVegetarian: menuItems.isVegetarian,
        isVegan: menuItems.isVegan,
        isGlutenFree: menuItems.isGlutenFree,
        isDairyFree: menuItems.isDairyFree,
        isNutFree: menuItems.isNutFree,
        isHalal: menuItems.isHalal,
        isKosher: menuItems.isKosher,
        spiceLevel: menuItems.spiceLevel,
        flavorProfile: menuItems.flavorProfile,
        createdAt: menuItems.createdAt,
        updatedAt: menuItems.updatedAt,
        createdBy: menuItems.createdBy,
        updatedBy: menuItems.updatedBy,
        // Product data
        productName: products.name,
        productDescription: products.description,
        productPrice: products.basePrice,
        productCost: products.standardCost,
        productBusinessId: products.businessId,
        // Recipe data
        recipeName: recipes.recipeName,
        recipeCode: recipes.recipeCode,
      })
      .from(menuItems)
      .innerJoin(products, eq(menuItems.productId, products.id))
      .leftJoin(recipes, eq(menuItems.recipeId, recipes.id))
      .where(and(eq(menuItems.id, id), eq(menuItems.isDeleted, false)))
      .then((results) => results[0]);

    if (!menuItemData) {
      throw new NotFoundException(`Menu item with ID ${id} not found`);
    }

    // Get creator and updater names
    const createdByUser = await this.usersService.getUserName(
      menuItemData.createdBy,
    );
    const updatedByUser = menuItemData.updatedBy
      ? await this.usersService.getUserName(menuItemData.updatedBy)
      : null;

    // Get images for this menu item
    const imagesData = await this.db
      .select({
        id: media.id,
        publicUrl: media.publicUrl,
        originalName: media.originalName,
      })
      .from(media)
      .where(
        and(
          eq(media.referenceId, id),
          eq(media.businessId, menuItemData.productBusinessId),
        ),
      );

    return {
      id: menuItemData.id,
      productId: menuItemData.productId,
      product: {
        id: menuItemData.productId,
        name: menuItemData.productName,
        description: menuItemData.productDescription,
        price: menuItemData.productPrice
          ? parseFloat(menuItemData.productPrice)
          : undefined,
        cost: menuItemData.productCost
          ? parseFloat(menuItemData.productCost)
          : undefined,
        businessId: menuItemData.productBusinessId,
      },
      prepTimeMinutes: menuItemData.prepTimeMinutes
        ? parseFloat(menuItemData.prepTimeMinutes)
        : undefined,
      cookTimeMinutes: menuItemData.cookTimeMinutes
        ? parseFloat(menuItemData.cookTimeMinutes)
        : undefined,
      recipeId: menuItemData.recipeId,
      recipe: menuItemData.recipeId
        ? {
            id: menuItemData.recipeId,
            recipeName: menuItemData.recipeName,
            recipeCode: menuItemData.recipeCode,
          }
        : undefined,
      servingsPerOrder: menuItemData.servingsPerOrder,
      caloriesPerServing: menuItemData.caloriesPerServing,
      availableBreakfast: menuItemData.availableBreakfast,
      availableBrunch: menuItemData.availableBrunch,
      availableLunch: menuItemData.availableLunch,
      availableHappyHour: menuItemData.availableHappyHour,
      availableDinner: menuItemData.availableDinner,
      availableLateNight: menuItemData.availableLateNight,
      isVegetarian: menuItemData.isVegetarian,
      isVegan: menuItemData.isVegan,
      isGlutenFree: menuItemData.isGlutenFree,
      isDairyFree: menuItemData.isDairyFree,
      isNutFree: menuItemData.isNutFree,
      isHalal: menuItemData.isHalal,
      isKosher: menuItemData.isKosher,
      spiceLevel: menuItemData.spiceLevel,
      flavorProfile: menuItemData.flavorProfile as any,
      images: imagesData,
      createdBy: createdByUser || 'Unknown',
      updatedBy: updatedByUser,
      createdAt: menuItemData.createdAt.toISOString(),
      updatedAt: menuItemData.updatedAt.toISOString(),
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateMenuItemDto: UpdateMenuItemDto,
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Get the existing menu item
      const existingMenuItem = await this.db
        .select({
          id: menuItems.id,
          productId: menuItems.productId,
          businessId: products.businessId,
        })
        .from(menuItems)
        .innerJoin(products, eq(menuItems.productId, products.id))
        .where(and(eq(menuItems.id, id), eq(menuItems.isDeleted, false)))
        .then((results) => results[0]);

      if (!existingMenuItem) {
        throw new NotFoundException(`Menu item with ID ${id} not found`);
      }

      // Verify business ownership
      if (businessId !== existingMenuItem.businessId) {
        throw new UnauthorizedException(
          'Access denied to update this menu item',
        );
      }

      // Verify new product exists and belongs to the business if productId is being updated
      if (
        updateMenuItemDto.productId &&
        updateMenuItemDto.productId !== existingMenuItem.productId
      ) {
        const product = await this.db
          .select()
          .from(products)
          .where(
            and(
              eq(products.id, updateMenuItemDto.productId),
              eq(products.businessId, businessId),
              eq(products.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!product) {
          throw new BadRequestException(
            'Product not found or does not belong to this business',
          );
        }

        // Check if another menu item already exists for this product
        const existingMenuItemForProduct = await this.db
          .select()
          .from(menuItems)
          .where(
            and(
              eq(menuItems.productId, updateMenuItemDto.productId),
              eq(menuItems.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (
          existingMenuItemForProduct &&
          existingMenuItemForProduct.id !== id
        ) {
          throw new ConflictException(
            'Menu item already exists for this product',
          );
        }
      }

      // Verify recipe exists if provided
      if (updateMenuItemDto.recipeId) {
        const recipe = await this.db
          .select()
          .from(recipes)
          .where(
            and(
              eq(recipes.id, updateMenuItemDto.recipeId),
              eq(recipes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!recipe) {
          throw new BadRequestException('Recipe not found');
        }
      }

      // Build update object
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      if (updateMenuItemDto.productId !== undefined) {
        updateData.productId = updateMenuItemDto.productId;
      }
      if (updateMenuItemDto.prepTimeMinutes !== undefined) {
        updateData.prepTimeMinutes =
          updateMenuItemDto.prepTimeMinutes?.toString();
      }
      if (updateMenuItemDto.cookTimeMinutes !== undefined) {
        updateData.cookTimeMinutes =
          updateMenuItemDto.cookTimeMinutes?.toString();
      }
      if (updateMenuItemDto.recipeId !== undefined) {
        updateData.recipeId = updateMenuItemDto.recipeId;
      }
      if (updateMenuItemDto.servingsPerOrder !== undefined) {
        updateData.servingsPerOrder = updateMenuItemDto.servingsPerOrder;
      }
      if (updateMenuItemDto.caloriesPerServing !== undefined) {
        updateData.caloriesPerServing = updateMenuItemDto.caloriesPerServing;
      }
      if (updateMenuItemDto.availableBreakfast !== undefined) {
        updateData.availableBreakfast = updateMenuItemDto.availableBreakfast;
      }
      if (updateMenuItemDto.availableBrunch !== undefined) {
        updateData.availableBrunch = updateMenuItemDto.availableBrunch;
      }
      if (updateMenuItemDto.availableLunch !== undefined) {
        updateData.availableLunch = updateMenuItemDto.availableLunch;
      }
      if (updateMenuItemDto.availableHappyHour !== undefined) {
        updateData.availableHappyHour = updateMenuItemDto.availableHappyHour;
      }
      if (updateMenuItemDto.availableDinner !== undefined) {
        updateData.availableDinner = updateMenuItemDto.availableDinner;
      }
      if (updateMenuItemDto.availableLateNight !== undefined) {
        updateData.availableLateNight = updateMenuItemDto.availableLateNight;
      }
      if (updateMenuItemDto.isVegetarian !== undefined) {
        updateData.isVegetarian = updateMenuItemDto.isVegetarian;
      }
      if (updateMenuItemDto.isVegan !== undefined) {
        updateData.isVegan = updateMenuItemDto.isVegan;
      }
      if (updateMenuItemDto.isGlutenFree !== undefined) {
        updateData.isGlutenFree = updateMenuItemDto.isGlutenFree;
      }
      if (updateMenuItemDto.isDairyFree !== undefined) {
        updateData.isDairyFree = updateMenuItemDto.isDairyFree;
      }
      if (updateMenuItemDto.isNutFree !== undefined) {
        updateData.isNutFree = updateMenuItemDto.isNutFree;
      }
      if (updateMenuItemDto.isHalal !== undefined) {
        updateData.isHalal = updateMenuItemDto.isHalal;
      }
      if (updateMenuItemDto.isKosher !== undefined) {
        updateData.isKosher = updateMenuItemDto.isKosher;
      }
      if (updateMenuItemDto.spiceLevel !== undefined) {
        updateData.spiceLevel = updateMenuItemDto.spiceLevel;
      }
      if (updateMenuItemDto.flavorProfile !== undefined) {
        updateData.flavorProfile = updateMenuItemDto.flavorProfile;
      }

      // Update the menu item
      await this.db
        .update(menuItems)
        .set(updateData)
        .where(eq(menuItems.id, id));

      // Handle image updates if provided
      if (imageFiles && imageFiles.length > 0) {
        try {
          await this.mediaService.updateMediaForReference(
            id,
            imageFiles,
            'menu-items/images',
            businessId,
            userId,
          );
        } catch (error) {
          console.warn('Failed to update menu item images:', error.message);
        }
      }

      // Log activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update menu item: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Get the menu item to verify ownership
      const menuItemData = await this.db
        .select({
          id: menuItems.id,
          productName: products.name,
          businessId: products.businessId,
        })
        .from(menuItems)
        .innerJoin(products, eq(menuItems.productId, products.id))
        .where(and(eq(menuItems.id, id), eq(menuItems.isDeleted, false)))
        .then((results) => results[0]);

      if (!menuItemData) {
        throw new NotFoundException(`Menu item with ID ${id} not found`);
      }

      // Verify business ownership
      if (businessId !== menuItemData.businessId) {
        throw new UnauthorizedException(
          'Access denied to delete this menu item',
        );
      }

      // Soft delete the menu item
      await this.db
        .update(menuItems)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(menuItems.id, id));

      // Log activity
      await this.activityLogService.logDelete(
        id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          reason: `Deleted menu item: ${menuItemData.productName}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete menu item: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createMenuItemsDto: CreateMenuItemDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<MenuItemDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createMenuItemsDto || createMenuItemsDto.length === 0) {
        throw new BadRequestException('No menu items provided for creation');
      }

      // Validate that if images are provided, they don't exceed the number of menu items
      if (imageFiles && imageFiles.length > createMenuItemsDto.length) {
        throw new BadRequestException(
          'Number of images cannot exceed number of menu items',
        );
      }

      const createdMenuItems: MenuItemDto[] = [];
      const errors: string[] = [];

      for (let i = 0; i < createMenuItemsDto.length; i++) {
        const createMenuItemDto = createMenuItemsDto[i];
        try {
          // Get the image file for this menu item if available
          const imageFile =
            createMenuItemDto.imageIndex !== undefined && imageFiles
              ? imageFiles[createMenuItemDto.imageIndex]
              : undefined;

          const result = await this.create(
            userId,
            businessId,
            createMenuItemDto,
            imageFile ? [imageFile] : undefined,
            metadata,
          );

          const menuItem = await this.findOne(result.id);
          createdMenuItems.push(menuItem);
        } catch (error) {
          errors.push(`Menu item ${i + 1}: ${error.message}`);
        }
      }

      if (errors.length > 0 && createdMenuItems.length === 0) {
        throw new BadRequestException(
          `All menu items failed to create: ${errors.join(', ')}`,
        );
      }

      if (errors.length > 0) {
        console.warn(`Some menu items failed to create: ${errors.join(', ')}`);
      }

      return createdMenuItems;
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create menu items: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createMenuItemsDto: CreateMenuItemDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const createdMenuItems = await this.bulkCreate(
      userId,
      businessId,
      createMenuItemsDto,
      imageFiles,
      metadata,
    );
    return { ids: createdMenuItems.map((item) => item.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No menu item IDs provided for deletion');
      }

      let deletedCount = 0;
      const errors: string[] = [];

      for (const id of ids) {
        try {
          await this.remove(userId, businessId, id, metadata);
          deletedCount++;
        } catch (error) {
          errors.push(`Menu item ${id}: ${error.message}`);
        }
      }

      if (errors.length > 0 && deletedCount === 0) {
        throw new BadRequestException(
          `All menu items failed to delete: ${errors.join(', ')}`,
        );
      }

      if (errors.length > 0) {
        console.warn(`Some menu items failed to delete: ${errors.join(', ')}`);
      }

      return { deletedCount };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete menu items: ${error.message}`,
      );
    }
  }
}
