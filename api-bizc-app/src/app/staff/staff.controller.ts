import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { StaffService } from './staff.service';
import { CreateStaffMemberDto } from './dto/create-staff-member.dto';
import { UpdateStaffMemberDto } from './dto/update-staff-member.dto';
import { StaffMemberDto } from './dto/staff-member.dto';
import { StaffMemberSlimDto } from './dto/staff-member-slim.dto';
import { StaffMemberAutocompleteDto } from './dto/staff-member-autocomplete.dto';
import { PaginatedStaffResponseDto } from './dto/paginated-staff-response.dto';
import { StaffMemberIdResponseDto } from './dto/staff-member-id-response.dto';
import { DeleteStaffMemberResponseDto } from './dto/delete-staff-member-response.dto';
import { BulkCreateStaffDto } from './dto/bulk-create-staff.dto';
import { BulkStaffIdsResponseDto } from './dto/bulk-staff-ids-response.dto';
import { BulkDeleteStaffDto } from './dto/bulk-delete-staff.dto';
import { BulkDeleteStaffResponseDto } from './dto/bulk-delete-staff-response.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import { EmploymentStatus, EmploymentType } from '../shared/types';
import {
  CreateStaffPhysicalInfoDto,
  UpdateStaffPhysicalInfoDto,
  StaffPhysicalInfoDto,
} from './dto/staff-physical-info.dto';
import { AssignStaffLeaveTypeDto } from './dto/assign-staff-leave-type.dto';
import { BulkAssignStaffLeaveTypesDto } from './dto/bulk-assign-staff-leave-types.dto';
import { UpdateStaffLeaveTypeDto } from './dto/update-staff-leave-type.dto';
import { StaffLeaveTypeDto } from './dto/staff-leave-type.dto';
import {
  StaffLeaveTypeIdResponseDto,
  DeleteStaffLeaveTypeResponseDto,
  BulkAssignStaffLeaveTypeResponseDto,
  PaginatedStaffLeaveTypesResponseDto,
} from './dto/staff-leave-type-response.dto';

@ApiTags('staff')
@Controller('staff')
@UseGuards(PermissionsGuard)
export class StaffController {
  constructor(private readonly staffService: StaffService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'profileImage', maxCount: 1 },
      { name: 'documents', maxCount: 10 },
    ]),
  )
  @ApiOperation({ summary: 'Create a new staff member with optional files' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description:
      'Staff member creation with optional profile image and document uploads',
    schema: {
      type: 'object',
      properties: {
        firstName: {
          type: 'string',
          example: 'John',
          description: 'First name',
        },
        lastName: {
          type: 'string',
          example: 'Doe',
          description: 'Last name',
        },
        displayName: {
          type: 'string',
          example: 'John Doe',
          description: 'Display name',
        },
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'Email address',
        },
        phone: {
          type: 'string',
          example: '+1234567890',
          description: 'Phone number',
        },
        employeeId: {
          type: 'string',
          example: 'EMP001',
          description: 'Employee ID',
        },
        departmentId: {
          type: 'string',
          format: 'uuid',
          description: 'Department ID',
        },
        designationId: {
          type: 'string',
          format: 'uuid',
          description: 'Designation ID',
        },
        dateOfBirth: {
          type: 'string',
          format: 'date',
          description: 'Date of birth',
        },
        dateOfJoining: {
          type: 'string',
          format: 'date',
          description: 'Date of joining',
        },
        employmentType: {
          type: 'string',
          enum: Object.values(EmploymentType),
          description: 'Employment type',
          default: EmploymentType.FULL_TIME,
        },
        status: {
          type: 'string',
          enum: Object.values(EmploymentStatus),
          description: 'Employment status',
          default: EmploymentStatus.ACTIVE,
        },
        isUser: {
          type: 'boolean',
          description: 'Whether this staff member is also a user',
          default: false,
        },
        addressId: {
          type: 'string',
          format: 'uuid',
          description: 'Address ID',
        },
        profileImage: {
          type: 'string',
          format: 'binary',
          description: 'Profile image file',
        },
        documents: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Document files to upload',
        },

        physicalInfo: {
          type: 'object',
          description:
            'Physical information (can be JSON string for multipart)',
          properties: {
            heightCm: { type: 'string', description: 'Height in centimeters' },
            weightKg: { type: 'string', description: 'Weight in kilograms' },
            bloodType: { type: 'string', description: 'Blood type' },
            visionLeft: {
              type: 'string',
              description: 'Left eye vision condition',
            },
            visionRight: {
              type: 'string',
              description: 'Right eye vision condition',
            },
            hearingLeft: {
              type: 'string',
              description: 'Left ear hearing condition',
            },
            hearingRight: {
              type: 'string',
              description: 'Right ear hearing condition',
            },
            handLeft: { type: 'string', description: 'Left hand condition' },
            handRight: { type: 'string', description: 'Right hand condition' },
            legLeft: { type: 'string', description: 'Left leg condition' },
            legRight: { type: 'string', description: 'Right leg condition' },
          },
        },
        familyDetails: {
          type: 'object',
          description: 'Family details (can be JSON string for multipart)',
          properties: {
            maritalStatus: {
              type: 'string',
              enum: ['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED'],
              description: 'Marital status',
            },
            numberOfChildren: {
              type: 'number',
              description: 'Number of children',
            },
            isSpouseWorking: {
              type: 'boolean',
              description: 'Whether spouse is working',
            },
            spouseFirstName: {
              type: 'string',
              description: 'Spouse first name',
            },
            spouseMiddleName: {
              type: 'string',
              description: 'Spouse middle name',
            },
            spouseLastName: { type: 'string', description: 'Spouse last name' },
            spouseBirthDate: {
              type: 'string',
              format: 'date',
              description: 'Spouse birth date',
            },
            spouseGender: {
              type: 'string',
              enum: ['MALE', 'FEMALE', 'OTHER'],
              description: 'Spouse gender',
            },
            spouseEmail: {
              type: 'string',
              format: 'email',
              description: 'Spouse email',
            },
            spousePhone: { type: 'string', description: 'Spouse phone' },
            notes: { type: 'string', description: 'Additional notes' },
          },
        },
        emergencyContacts: {
          type: 'array',
          description: 'Emergency contacts (can be JSON string for multipart)',
          items: {
            type: 'object',
            properties: {
              firstName: { type: 'string', description: 'First name' },
              middleName: { type: 'string', description: 'Middle name' },
              lastName: { type: 'string', description: 'Last name' },
              relationship: {
                type: 'string',
                description: 'Relationship to staff member',
              },
              mobilePhone: { type: 'string', description: 'Mobile phone' },
              housePhone: { type: 'string', description: 'House phone' },
              officePhone: { type: 'string', description: 'Office phone' },
              addressId: {
                type: 'string',
                format: 'uuid',
                description: 'Address ID',
              },
              priority: { type: 'number', description: 'Priority order' },
              isActive: { type: 'boolean', description: 'Is active' },
              notes: { type: 'string', description: 'Additional notes' },
            },
          },
        },
      },
      required: ['firstName', 'lastName', 'displayName', 'email'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The staff member has been successfully created',
    type: StaffMemberIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict - Staff member with this email/display name already exists',
  })
  async create(
    @Request() req,
    @Body() createStaffMemberDto: CreateStaffMemberDto,
    @ActivityMetadata() metadata: ActivityMetadataType,

    @UploadedFiles()
    files?: {
      profileImage?: Express.Multer.File[];
      documents?: Express.Multer.File[];
    },
  ): Promise<StaffMemberIdResponseDto> {
    const profileImageFile = files?.profileImage?.[0];
    const documentFiles = files?.documents;

    return this.staffService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createStaffMemberDto,
      profileImageFile,
      documentFiles,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_CREATE)
  @ApiOperation({ summary: 'Bulk create staff members' })
  @ApiBody({
    description: 'Array of staff member data for bulk creation',
    type: BulkCreateStaffDto,
  })
  @ApiResponse({
    status: 201,
    description: 'The staff members have been successfully created',
    type: BulkStaffIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Staff members with duplicate data already exist',
  })
  async bulkCreate(
    @Request() req,
    @Body() bulkCreateStaffDto: BulkCreateStaffDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkStaffIdsResponseDto> {
    return this.staffService.bulkCreateAndReturnIds(
      bulkCreateStaffDto,
      req.user.activeBusinessId,
      req.user.id,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({
    summary: 'Get all staff members for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-12-31',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search by name, email, or employee ID',
    required: false,
    type: String,
    example: 'John Doe',
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by employment status',
    required: false,
    enum: EmploymentStatus,
    example: EmploymentStatus.ACTIVE,
  })
  @ApiQuery({
    name: 'employmentType',
    description: 'Filter by employment type',
    required: false,
    enum: EmploymentType,
    example: EmploymentType.FULL_TIME,
  })
  @ApiQuery({
    name: 'departmentId',
    description: 'Filter by department ID',
    required: false,
    type: String,
    format: 'uuid',
  })
  @ApiQuery({
    name: 'designationId',
    description: 'Filter by designation ID',
    required: false,
    type: String,
    format: 'uuid',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort by field',
    required: false,
    enum: ['displayName', 'email', 'createdAt', 'dateOfJoining'],
    example: 'displayName',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'Sort order',
    required: false,
    enum: ['asc', 'desc'],
    example: 'asc',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"displayName","value":"John","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: displayName, email, createdAt, dateOfJoining',
    required: false,
    type: String,
    example: '[{"id":"displayName","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all staff members for the user's active business with pagination",
    type: PaginatedStaffResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('search') search?: string,
    @Query('status') status?: EmploymentStatus,
    @Query('employmentType') employmentType?: EmploymentType,
    @Query('departmentId') departmentId?: string,
    @Query('designationId') designationId?: string,
    @Query('departmentName') departmentName?: string,
    @Query('designationTitle') designationTitle?: string,
    @Query('sortBy')
    sortBy?: 'displayName' | 'email' | 'createdAt' | 'dateOfJoining',
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedStaffResponseDto> {
    return this.staffService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      {
        page: page ? parseInt(page.toString()) : undefined,
        limit: limit ? parseInt(limit.toString()) : undefined,
        from,
        to,
        search,
        status,
        employmentType,
        departmentId,
        designationId,
        departmentName,
        designationTitle,
        sortBy,
        sortOrder,
        filters,
        joinOperator,
        sort,
      },
    );
  }

  @Get('check-display-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Check if a display name is available' })
  @ApiQuery({
    name: 'displayName',
    description: 'Display name to check',
    required: true,
    type: String,
    example: 'John Doe',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the display name is available',
    schema: {
      type: 'object',
      properties: {
        available: {
          type: 'boolean',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkDisplayNameAvailability(
    @Request() req,
    @Query('displayName') displayName: string,
  ): Promise<{ available: boolean }> {
    return this.staffService.checkDisplayNameAvailability(
      req.user.activeBusinessId,
      displayName,
    );
  }

  @Get('check-email-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Check if an email is available' })
  @ApiQuery({
    name: 'email',
    description: 'Email to check',
    required: true,
    type: String,
    example: '<EMAIL>',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the email is available',
    schema: {
      type: 'object',
      properties: {
        available: {
          type: 'boolean',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkEmailAvailability(
    @Request() req,
    @Query('email') email: string,
  ): Promise<{ available: boolean }> {
    return this.staffService.checkEmailAvailability(
      req.user.activeBusinessId,
      email,
    );
  }

  @Get('check-employee-id-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Check if an employee ID is available' })
  @ApiQuery({
    name: 'employeeId',
    description: 'Employee ID to check',
    required: true,
    type: String,
    example: 'EMP001',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the employee ID is available',
    schema: {
      type: 'object',
      properties: {
        available: {
          type: 'boolean',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkEmployeeIdAvailability(
    @Request() req,
    @Query('employeeId') employeeId: string,
  ): Promise<{ available: boolean }> {
    return this.staffService.checkEmployeeIdAvailability(
      req.user.activeBusinessId,
      employeeId,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Get all active staff members in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All active staff members returned successfully',
    type: [StaffMemberSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAllSlim(@Request() req): Promise<StaffMemberSlimDto[]> {
    return this.staffService.findSlim(req.user.activeBusinessId);
  }

  @Get('autocomplete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({
    summary: 'Search staff members for autocomplete',
    description:
      'Returns staff members matching the search query for autocomplete functionality',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term to filter staff members by name',
    required: false,
    type: String,
    example: 'john',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of results to return (default: 10, max: 50)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Staff members returned successfully',
    type: [StaffMemberAutocompleteDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAutocomplete(
    @Request() req,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
  ): Promise<StaffMemberAutocompleteDto[]> {
    return this.staffService.findAutocomplete(
      req.user.activeBusinessId,
      search,
      limit ? Math.min(parseInt(limit.toString()), 50) : 10,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Get a staff member by ID' })
  @ApiParam({
    name: 'id',
    description: 'Staff member ID',
    type: String,
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the staff member with complete details',
    type: StaffMemberDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Staff member not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this staff member',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<StaffMemberDto> {
    console.log(')))))))))) ======================= 0000 ');
    return this.staffService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'profileImage', maxCount: 1 },
      { name: 'documents', maxCount: 10 },
    ]),
  )
  @ApiOperation({
    summary: 'Update a staff member with optional profile image and documents',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'id',
    description: 'Staff member ID',
    type: String,
    format: 'uuid',
  })
  @ApiBody({
    description:
      'Staff member update with optional profile image and document uploads',
    schema: {
      type: 'object',
      properties: {
        firstName: {
          type: 'string',
          example: 'John',
          description: 'First name',
        },
        lastName: {
          type: 'string',
          example: 'Doe',
          description: 'Last name',
        },
        displayName: {
          type: 'string',
          example: 'John Doe',
          description: 'Display name',
        },
        email: {
          type: 'string',
          format: 'email',
          example: '<EMAIL>',
          description: 'Email address',
        },
        phone: {
          type: 'string',
          example: '+1234567890',
          description: 'Phone number',
        },
        employeeId: {
          type: 'string',
          example: 'EMP001',
          description: 'Employee ID',
        },
        departmentId: {
          type: 'string',
          format: 'uuid',
          description: 'Department ID',
        },
        designationId: {
          type: 'string',
          format: 'uuid',
          description: 'Designation ID',
        },
        dateOfBirth: {
          type: 'string',
          format: 'date',
          description: 'Date of birth',
        },
        dateOfJoining: {
          type: 'string',
          format: 'date',
          description: 'Date of joining',
        },
        employmentType: {
          type: 'string',
          enum: Object.values(EmploymentType),
          description: 'Employment type',
        },
        status: {
          type: 'string',
          enum: Object.values(EmploymentStatus),
          description: 'Employment status',
        },
        isUser: {
          type: 'boolean',
          description: 'Whether this staff member is also a user',
        },
        addressId: {
          type: 'string',
          format: 'uuid',
          description: 'Address ID',
        },
        profileImage: {
          type: 'string',
          format: 'binary',
          description: 'Profile image file',
        },
        documents: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Document files to upload',
        },

        physicalInfo: {
          type: 'object',
          description:
            'Physical information (can be JSON string for multipart)',
          properties: {
            heightCm: { type: 'string', description: 'Height in centimeters' },
            weightKg: { type: 'string', description: 'Weight in kilograms' },
            bloodType: { type: 'string', description: 'Blood type' },
            visionLeft: {
              type: 'string',
              description: 'Left eye vision condition',
            },
            visionRight: {
              type: 'string',
              description: 'Right eye vision condition',
            },
            hearingLeft: {
              type: 'string',
              description: 'Left ear hearing condition',
            },
            hearingRight: {
              type: 'string',
              description: 'Right ear hearing condition',
            },
            handLeft: { type: 'string', description: 'Left hand condition' },
            handRight: { type: 'string', description: 'Right hand condition' },
            legLeft: { type: 'string', description: 'Left leg condition' },
            legRight: { type: 'string', description: 'Right leg condition' },
          },
        },
        familyDetails: {
          type: 'object',
          description: 'Family details (can be JSON string for multipart)',
          properties: {
            maritalStatus: {
              type: 'string',
              enum: ['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED', 'SEPARATED'],
              description: 'Marital status',
            },
            numberOfChildren: {
              type: 'number',
              description: 'Number of children',
            },
            isSpouseWorking: {
              type: 'boolean',
              description: 'Whether spouse is working',
            },
            spouseFirstName: {
              type: 'string',
              description: 'Spouse first name',
            },
            spouseMiddleName: {
              type: 'string',
              description: 'Spouse middle name',
            },
            spouseLastName: { type: 'string', description: 'Spouse last name' },
            spouseBirthDate: {
              type: 'string',
              format: 'date',
              description: 'Spouse birth date',
            },
            spouseGender: {
              type: 'string',
              enum: ['MALE', 'FEMALE', 'OTHER'],
              description: 'Spouse gender',
            },
            spouseEmail: {
              type: 'string',
              format: 'email',
              description: 'Spouse email',
            },
            spousePhone: { type: 'string', description: 'Spouse phone' },
            notes: { type: 'string', description: 'Additional notes' },
          },
        },
        emergencyContacts: {
          type: 'array',
          description: 'Emergency contacts (can be JSON string for multipart)',
          items: {
            type: 'object',
            properties: {
              firstName: { type: 'string', description: 'First name' },
              middleName: { type: 'string', description: 'Middle name' },
              lastName: { type: 'string', description: 'Last name' },
              relationship: {
                type: 'string',
                description: 'Relationship to staff member',
              },
              mobilePhone: { type: 'string', description: 'Mobile phone' },
              housePhone: { type: 'string', description: 'House phone' },
              officePhone: { type: 'string', description: 'Office phone' },
              addressId: {
                type: 'string',
                format: 'uuid',
                description: 'Address ID',
              },
              priority: { type: 'number', description: 'Priority order' },
              isActive: { type: 'boolean', description: 'Is active' },
              notes: { type: 'string', description: 'Additional notes' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'The staff member has been successfully updated',
    type: StaffMemberIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 404,
    description: 'Staff member not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this staff member',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Duplicate data conflict',
  })
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateStaffMemberDto: UpdateStaffMemberDto,
    @UploadedFiles()
    files?: {
      profileImage?: Express.Multer.File[];
      documents?: Express.Multer.File[];
    },
  ): Promise<StaffMemberIdResponseDto> {
    const profileImageFile = files?.profileImage?.[0];
    const documentFiles = files?.documents;

    return this.staffService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateStaffMemberDto,
      profileImageFile,
      documentFiles,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_DELETE)
  @ApiOperation({ summary: 'Bulk delete staff members' })
  @ApiBody({
    description: 'Array of staff member IDs to delete',
    type: BulkDeleteStaffDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Staff members have been successfully deleted',
    type: BulkDeleteStaffResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or staff members not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more staff members not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteStaffDto: BulkDeleteStaffDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteStaffResponseDto> {
    return this.staffService.bulkDelete(
      bulkDeleteStaffDto,
      req.user.activeBusinessId,
      req.user.id,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_DELETE)
  @ApiOperation({ summary: 'Delete a staff member' })
  @ApiParam({
    name: 'id',
    description: 'Staff member ID',
    type: String,
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'The staff member has been successfully deleted',
    type: DeleteStaffMemberResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Staff member not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this staff member',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteStaffMemberResponseDto> {
    return this.staffService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  // Physical Info Endpoints
  @Post(':id/physical-info')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_UPDATE)
  @ApiOperation({ summary: 'Create physical information for a staff member' })
  @ApiParam({ name: 'id', description: 'Staff member ID' })
  @ApiBody({ type: CreateStaffPhysicalInfoDto })
  @ApiResponse({
    status: 201,
    description: 'Physical information created successfully',
    type: StaffPhysicalInfoDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Staff member not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Physical information already exists',
  })
  async createPhysicalInfo(
    @Request() req,
    @Param('id') staffMemberId: string,
    @Body() createPhysicalInfoDto: CreateStaffPhysicalInfoDto,
  ): Promise<StaffPhysicalInfoDto> {
    return this.staffService.createStaffPhysicalInfo(
      req.user.id,
      req.user.activeBusinessId,
      staffMemberId,
      createPhysicalInfoDto,
    );
  }

  @Get(':id/physical-info')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Get physical information for a staff member' })
  @ApiParam({ name: 'id', description: 'Staff member ID' })
  @ApiResponse({
    status: 200,
    description: 'Physical information retrieved successfully',
    type: StaffPhysicalInfoDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Physical information not found',
  })
  async getPhysicalInfo(
    @Request() req,
    @Param('id') staffMemberId: string,
  ): Promise<StaffPhysicalInfoDto> {
    return this.staffService.getStaffPhysicalInfo(
      req.user.activeBusinessId,
      staffMemberId,
    );
  }

  @Patch(':id/physical-info')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_UPDATE)
  @ApiOperation({ summary: 'Update physical information for a staff member' })
  @ApiParam({ name: 'id', description: 'Staff member ID' })
  @ApiBody({ type: UpdateStaffPhysicalInfoDto })
  @ApiResponse({
    status: 200,
    description: 'Physical information updated successfully',
    type: StaffPhysicalInfoDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Physical information not found',
  })
  async updatePhysicalInfo(
    @Request() req,
    @Param('id') staffMemberId: string,
    @Body() updatePhysicalInfoDto: UpdateStaffPhysicalInfoDto,
  ): Promise<StaffPhysicalInfoDto> {
    return this.staffService.updateStaffPhysicalInfo(
      req.user.id,
      req.user.activeBusinessId,
      staffMemberId,
      updatePhysicalInfoDto,
    );
  }

  @Delete(':id/physical-info')
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_DELETE)
  @ApiOperation({ summary: 'Delete physical information for a staff member' })
  @ApiParam({ name: 'id', description: 'Staff member ID' })
  @ApiResponse({
    status: 200,
    description: 'Physical information deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Physical information not found',
  })
  async deletePhysicalInfo(
    @Request() req,
    @Param('id') staffMemberId: string,
  ): Promise<{ success: boolean; message: string }> {
    return this.staffService.deleteStaffPhysicalInfo(
      req.user.id,
      req.user.activeBusinessId,
      staffMemberId,
    );
  }

  // Staff Leave Type Assignment Endpoints

  @Post('leave-types/assign')
  @UseGuards(PermissionsGuard)
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_UPDATE)
  @ApiOperation({ summary: 'Assign a leave type to a staff member' })
  @ApiBody({ type: AssignStaffLeaveTypeDto })
  @ApiResponse({
    status: 201,
    description: 'Leave type assigned successfully',
    type: StaffLeaveTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Staff member or leave type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Leave type already assigned to staff member',
  })
  async assignStaffLeaveType(
    @Request() req,
    @Body() assignStaffLeaveTypeDto: AssignStaffLeaveTypeDto,
  ): Promise<StaffLeaveTypeIdResponseDto> {
    return this.staffService.assignStaffLeaveType(
      req.user.id,
      req.user.activeBusinessId,
      assignStaffLeaveTypeDto,
    );
  }

  @Post('leave-types/bulk-assign')
  @UseGuards(PermissionsGuard)
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_UPDATE)
  @ApiOperation({ summary: 'Bulk assign leave types to staff members' })
  @ApiBody({ type: BulkAssignStaffLeaveTypesDto })
  @ApiResponse({
    status: 201,
    description: 'Leave types assigned successfully',
    type: BulkAssignStaffLeaveTypeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
  })
  async bulkAssignStaffLeaveTypes(
    @Request() req,
    @Body() bulkAssignDto: BulkAssignStaffLeaveTypesDto,
  ): Promise<BulkAssignStaffLeaveTypeResponseDto> {
    return this.staffService.bulkAssignStaffLeaveTypes(
      req.user.id,
      req.user.activeBusinessId,
      bulkAssignDto,
    );
  }

  @Get('leave-types')
  @UseGuards(PermissionsGuard)
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Get all staff leave type assignments' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'staffId',
    required: false,
    description: 'Filter by staff member ID',
  })
  @ApiQuery({
    name: 'leaveTypeId',
    required: false,
    description: 'Filter by leave type ID',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    description: 'Filter by active status',
    type: Boolean,
  })
  @ApiResponse({
    status: 200,
    description: 'Staff leave type assignments retrieved successfully',
    type: PaginatedStaffLeaveTypesResponseDto,
  })
  async getStaffLeaveTypes(
    @Request() req,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('staffId') staffId?: string,
    @Query('leaveTypeId') leaveTypeId?: string,
    @Query('isActive') isActive?: string,
  ): Promise<PaginatedStaffLeaveTypesResponseDto> {
    const parsedPage = parseInt(page, 10);
    const parsedLimit = parseInt(limit, 10);
    const parsedIsActive =
      isActive !== undefined ? isActive === 'true' : undefined;

    return this.staffService.getStaffLeaveTypes(
      req.user.activeBusinessId,
      parsedPage,
      parsedLimit,
      staffId,
      leaveTypeId,
      parsedIsActive,
    );
  }

  @Get('leave-types/:assignmentId')
  @UseGuards(PermissionsGuard)
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_READ)
  @ApiOperation({ summary: 'Get a specific staff leave type assignment' })
  @ApiParam({
    name: 'assignmentId',
    description: 'Staff leave type assignment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Staff leave type assignment retrieved successfully',
    type: StaffLeaveTypeDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Staff leave type assignment not found',
  })
  async getStaffLeaveTypeById(
    @Request() req,
    @Param('assignmentId') assignmentId: string,
  ): Promise<StaffLeaveTypeDto> {
    return this.staffService.getStaffLeaveTypeById(
      req.user.activeBusinessId,
      assignmentId,
    );
  }

  @Patch('leave-types/:assignmentId')
  @UseGuards(PermissionsGuard)
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_UPDATE)
  @ApiOperation({ summary: 'Update a staff leave type assignment' })
  @ApiParam({
    name: 'assignmentId',
    description: 'Staff leave type assignment ID',
  })
  @ApiBody({ type: UpdateStaffLeaveTypeDto })
  @ApiResponse({
    status: 200,
    description: 'Staff leave type assignment updated successfully',
    type: StaffLeaveTypeIdResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Staff leave type assignment not found',
  })
  async updateStaffLeaveType(
    @Request() req,
    @Param('assignmentId') assignmentId: string,
    @Body() updateDto: UpdateStaffLeaveTypeDto,
  ): Promise<StaffLeaveTypeIdResponseDto> {
    return this.staffService.updateStaffLeaveType(
      req.user.id,
      req.user.activeBusinessId,
      assignmentId,
      updateDto,
    );
  }

  @Delete('leave-types/:assignmentId')
  @UseGuards(PermissionsGuard)
  @ApiBearerAuth()
  @RequirePermissions(Permission.STAFF_DELETE)
  @ApiOperation({ summary: 'Remove a staff leave type assignment' })
  @ApiParam({
    name: 'assignmentId',
    description: 'Staff leave type assignment ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Staff leave type assignment deleted successfully',
    type: DeleteStaffLeaveTypeResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Staff leave type assignment not found',
  })
  async removeStaffLeaveType(
    @Request() req,
    @Param('assignmentId') assignmentId: string,
  ): Promise<DeleteStaffLeaveTypeResponseDto> {
    return this.staffService.removeStaffLeaveType(
      req.user.id,
      req.user.activeBusinessId,
      assignmentId,
    );
  }
}
