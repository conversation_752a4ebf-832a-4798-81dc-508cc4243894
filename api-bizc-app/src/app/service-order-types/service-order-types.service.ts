import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateServiceOrderTypeDto } from './dto/create-service-order-type.dto';
import { UpdateServiceOrderTypeDto } from './dto/update-service-order-type.dto';
import { ServiceOrderTypeDto } from './dto/service-order-type.dto';
import { ServiceOrderTypeSlimDto } from './dto/service-order-type-slim.dto';
import { ServiceOrderTypeListDto } from './dto/service-order-type-list.dto';
import { serviceOrderTypes } from '../drizzle/schema/service-order-types.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { ServiceOrderTypeStatus } from '../shared/types/service-order.enum';
import { UsersService } from '../users/users.service';

@Injectable()
export class ServiceOrderTypesService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceOrderTypeDto: CreateServiceOrderTypeDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service order type with the same type code already exists for this business
      const existingByTypeCode = await this.db
        .select()
        .from(serviceOrderTypes)
        .where(
          and(
            eq(serviceOrderTypes.businessId, businessId),
            ilike(
              serviceOrderTypes.typeCode,
              createServiceOrderTypeDto.typeCode,
            ),
            eq(serviceOrderTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByTypeCode) {
        throw new ConflictException(
          `Service order type with type code '${createServiceOrderTypeDto.typeCode}' already exists`,
        );
      }

      // Check if a service order type with the same type name already exists for this business
      const existingByTypeName = await this.db
        .select()
        .from(serviceOrderTypes)
        .where(
          and(
            eq(serviceOrderTypes.businessId, businessId),
            ilike(
              serviceOrderTypes.typeName,
              createServiceOrderTypeDto.typeName,
            ),
            eq(serviceOrderTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByTypeName) {
        throw new ConflictException(
          `Service order type with type name '${createServiceOrderTypeDto.typeName}' already exists`,
        );
      }

      // Insert new service order type
      const [serviceOrderType] = await this.db
        .insert(serviceOrderTypes)
        .values({
          businessId,
          typeCode: createServiceOrderTypeDto.typeCode,
          typeName: createServiceOrderTypeDto.typeName,
          category: createServiceOrderTypeDto.category,
          requiresItems: createServiceOrderTypeDto.requiresItems ?? false,
          requiresDiagnostics:
            createServiceOrderTypeDto.requiresDiagnostics ?? false,
          workflowTemplate: createServiceOrderTypeDto.workflowTemplate,
          status:
            createServiceOrderTypeDto.status ?? ServiceOrderTypeStatus.ACTIVE,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: serviceOrderTypes.id });

      // Log the activity
      await this.activityLogService.logCreate(
        serviceOrderType.id,
        EntityType.SERVICE_ORDER_TYPE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id: serviceOrderType.id };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create service order type: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createServiceOrderTypeDto: CreateServiceOrderTypeDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createServiceOrderTypeDto);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createServiceOrderTypeDtos: CreateServiceOrderTypeDto[],
  ): Promise<string[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (const dto of createServiceOrderTypeDtos) {
      try {
        const result = await this.create(userId, businessId, dto);
        createdIds.push(result.id);
      } catch (error) {
        errors.push(`${dto.typeName}: ${error.message}`);
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create service order types: ${errors.join(', ')}`,
      );
    }

    return createdIds;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createServiceOrderTypeDtos: CreateServiceOrderTypeDto[],
  ): Promise<{ ids: string[] }> {
    const ids = await this.bulkCreate(
      userId,
      businessId,
      createServiceOrderTypeDtos,
    );
    return { ids };
  }

  async findOne(
    id: string,
    businessId: string | null,
  ): Promise<ServiceOrderTypeDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: serviceOrderTypes.id,
        businessId: serviceOrderTypes.businessId,
        typeCode: serviceOrderTypes.typeCode,
        typeName: serviceOrderTypes.typeName,
        category: serviceOrderTypes.category,
        requiresItems: serviceOrderTypes.requiresItems,
        requiresDiagnostics: serviceOrderTypes.requiresDiagnostics,
        workflowTemplate: serviceOrderTypes.workflowTemplate,
        status: serviceOrderTypes.status,
        createdAt: serviceOrderTypes.createdAt,
        updatedAt: serviceOrderTypes.updatedAt,
        createdByUser: {
          id: users.id,
          name: users.name,
        },
        updatedByUser: {
          id: users.id,
          name: users.name,
        },
      })
      .from(serviceOrderTypes)
      .leftJoin(users, eq(serviceOrderTypes.createdBy, users.id))
      .leftJoin(users, eq(serviceOrderTypes.updatedBy, users.id))
      .where(
        and(
          eq(serviceOrderTypes.id, id),
          eq(serviceOrderTypes.businessId, businessId),
          eq(serviceOrderTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Service order type not found');
    }

    return {
      id: result.id,
      businessId: result.businessId,
      typeCode: result.typeCode,
      typeName: result.typeName,
      category: result.category,
      requiresItems: result.requiresItems,
      requiresDiagnostics: result.requiresDiagnostics,
      workflowTemplate: result.workflowTemplate,
      status: result.status,
      createdBy: result.createdByUser?.name || 'Unknown',
      updatedBy: result.updatedByUser?.name || 'Unknown',
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      deletedAt: null, // Always null since we only return non-deleted service order types
    };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ServiceOrderTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(serviceOrderTypes.isDeleted, false),
      eq(serviceOrderTypes.status, ServiceOrderTypeStatus.ACTIVE),
      eq(serviceOrderTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceOrderTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(serviceOrderTypes.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(serviceOrderTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: serviceOrderTypes.id,
        businessId: serviceOrderTypes.businessId,
        typeCode: serviceOrderTypes.typeCode,
        typeName: serviceOrderTypes.typeName,
        category: serviceOrderTypes.category,
        requiresItems: serviceOrderTypes.requiresItems,
        requiresDiagnostics: serviceOrderTypes.requiresDiagnostics,
        workflowTemplate: serviceOrderTypes.workflowTemplate,
        status: serviceOrderTypes.status,
        createdAt: serviceOrderTypes.createdAt,
        updatedAt: serviceOrderTypes.updatedAt,
        createdByUser: {
          id: users.id,
          name: users.name,
        },
        updatedByUser: {
          id: users.id,
          name: users.name,
        },
      })
      .from(serviceOrderTypes)
      .leftJoin(users, eq(serviceOrderTypes.createdBy, users.id))
      .leftJoin(users, eq(serviceOrderTypes.updatedBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(serviceOrderTypes.createdAt))
      .limit(limit)
      .offset(offset);

    const data: ServiceOrderTypeDto[] = results.map((result) => ({
      id: result.id,
      businessId: result.businessId,
      typeCode: result.typeCode,
      typeName: result.typeName,
      category: result.category,
      requiresItems: result.requiresItems,
      requiresDiagnostics: result.requiresDiagnostics,
      workflowTemplate: result.workflowTemplate,
      status: result.status,
      createdBy: result.createdByUser?.name || 'Unknown',
      updatedBy: result.updatedByUser?.name || 'Unknown',
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      deletedAt: null, // Always null since we only return non-deleted service order types
    }));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(
    //   businessId,
    //   EntityType.SERVICE_ORDER_TYPE,
    //   userId,
    //   businessId,
    //   {
    //     source: ActivitySource.WEB,
    //   },
    // );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    typeCode?: string,
    typeName?: string,
    category?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ServiceOrderTypeListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(serviceOrderTypes.isDeleted, false),
      eq(serviceOrderTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceOrderTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(serviceOrderTypes.createdAt, toDate));
      }
    }

    // Build filter conditions
    const filterConditions = [];

    if (typeCode) {
      filterConditions.push(ilike(serviceOrderTypes.typeCode, `%${typeCode}%`));
    }

    if (typeName) {
      filterConditions.push(ilike(serviceOrderTypes.typeName, `%${typeName}%`));
    }

    if (category) {
      filterConditions.push(eq(serviceOrderTypes.category, category as any));
    }

    if (status) {
      filterConditions.push(eq(serviceOrderTypes.status, status as any));
    }

    // Handle additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const [key, value] of Object.entries(parsedFilters)) {
          if (value && typeof value === 'string') {
            switch (key) {
              case 'typeCode':
                filterConditions.push(
                  ilike(serviceOrderTypes.typeCode, `%${value}%`),
                );
                break;
              case 'typeName':
                filterConditions.push(
                  ilike(serviceOrderTypes.typeName, `%${value}%`),
                );
                break;
              case 'category':
                filterConditions.push(
                  eq(serviceOrderTypes.category, value as any),
                );
                break;
              case 'status':
                filterConditions.push(
                  eq(serviceOrderTypes.status, value as any),
                );
                break;
            }
          }
        }
      } catch {
        // Ignore invalid JSON filters
      }
    }

    // Combine filter conditions based on join operator
    if (filterConditions.length > 0) {
      if (joinOperator === 'or') {
        whereConditions.push(or(...filterConditions));
      } else {
        whereConditions.push(...filterConditions);
      }
    }

    // Build order by clause
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'typeCode':
          orderBy = isDesc
            ? desc(serviceOrderTypes.typeCode)
            : asc(serviceOrderTypes.typeCode);
          break;
        case 'typeName':
          orderBy = isDesc
            ? desc(serviceOrderTypes.typeName)
            : asc(serviceOrderTypes.typeName);
          break;
        case 'category':
          orderBy = isDesc
            ? desc(serviceOrderTypes.category)
            : asc(serviceOrderTypes.category);
          break;
        case 'status':
          orderBy = isDesc
            ? desc(serviceOrderTypes.status)
            : asc(serviceOrderTypes.status);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(serviceOrderTypes.createdAt)
            : asc(serviceOrderTypes.createdAt);
          break;
        default:
          orderBy = desc(serviceOrderTypes.createdAt);
      }
    } else {
      orderBy = desc(serviceOrderTypes.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(serviceOrderTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: serviceOrderTypes.id,
        typeCode: serviceOrderTypes.typeCode,
        typeName: serviceOrderTypes.typeName,
        category: serviceOrderTypes.category,
        status: serviceOrderTypes.status,
        requiresItems: serviceOrderTypes.requiresItems,
        requiresDiagnostics: serviceOrderTypes.requiresDiagnostics,
        workflowTemplate: serviceOrderTypes.workflowTemplate,
      })
      .from(serviceOrderTypes)
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    const data: ServiceOrderTypeListDto[] = results.map((result) => ({
      id: result.id,
      typeCode: result.typeCode,
      typeName: result.typeName,
      category: result.category,
      status: result.status,
      requiresItems: result.requiresItems,
      requiresDiagnostics: result.requiresDiagnostics,
      workflowTemplate: result.workflowTemplate,
    }));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(
    //   businessId,
    //   EntityType.SERVICE_ORDER_TYPE,
    //   userId,
    //   businessId,
    //   {
    //     source: ActivitySource.WEB,
    //   },
    // );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<ServiceOrderTypeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: serviceOrderTypes.id,
        typeName: serviceOrderTypes.typeName,
        typeCode: serviceOrderTypes.typeCode,
      })
      .from(serviceOrderTypes)
      .where(
        and(
          eq(serviceOrderTypes.businessId, businessId),
          eq(serviceOrderTypes.status, ServiceOrderTypeStatus.ACTIVE),
          eq(serviceOrderTypes.isDeleted, false),
        ),
      )
      .orderBy(asc(serviceOrderTypes.typeName));

    return results.map((result) => ({
      id: result.id,
      typeName: result.typeName,
      typeCode: result.typeCode,
    }));
  }

  async update(
    id: string,
    userId: string,
    businessId: string | null,
    updateServiceOrderTypeDto: UpdateServiceOrderTypeDto,
  ): Promise<ServiceOrderTypeDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if service order type exists
    const existingServiceOrderType = await this.db
      .select()
      .from(serviceOrderTypes)
      .where(
        and(
          eq(serviceOrderTypes.id, id),
          eq(serviceOrderTypes.businessId, businessId),
          eq(serviceOrderTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingServiceOrderType) {
      throw new NotFoundException('Service order type not found');
    }

    // Check for conflicts if updating type code
    if (
      updateServiceOrderTypeDto.typeCode &&
      updateServiceOrderTypeDto.typeCode !== existingServiceOrderType.typeCode
    ) {
      const existingByTypeCode = await this.db
        .select()
        .from(serviceOrderTypes)
        .where(
          and(
            eq(serviceOrderTypes.businessId, businessId),
            ilike(
              serviceOrderTypes.typeCode,
              updateServiceOrderTypeDto.typeCode,
            ),
            eq(serviceOrderTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByTypeCode && existingByTypeCode.id !== id) {
        throw new ConflictException(
          `Service order type with type code '${updateServiceOrderTypeDto.typeCode}' already exists`,
        );
      }
    }

    // Check for conflicts if updating type name
    if (
      updateServiceOrderTypeDto.typeName &&
      updateServiceOrderTypeDto.typeName !== existingServiceOrderType.typeName
    ) {
      const existingByTypeName = await this.db
        .select()
        .from(serviceOrderTypes)
        .where(
          and(
            eq(serviceOrderTypes.businessId, businessId),
            ilike(
              serviceOrderTypes.typeName,
              updateServiceOrderTypeDto.typeName,
            ),
            eq(serviceOrderTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByTypeName && existingByTypeName.id !== id) {
        throw new ConflictException(
          `Service order type with type name '${updateServiceOrderTypeDto.typeName}' already exists`,
        );
      }
    }

    // Update the service order type
    const [updatedServiceOrderType] = await this.db
      .update(serviceOrderTypes)
      .set({
        typeCode: updateServiceOrderTypeDto.typeCode,
        typeName: updateServiceOrderTypeDto.typeName,
        category: updateServiceOrderTypeDto.category,
        requiresItems: updateServiceOrderTypeDto.requiresItems,
        requiresDiagnostics: updateServiceOrderTypeDto.requiresDiagnostics,
        workflowTemplate: updateServiceOrderTypeDto.workflowTemplate,
        status: updateServiceOrderTypeDto.status,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(serviceOrderTypes.id, id))
      .returning();

    // Log the activity
    await this.activityLogService.logUpdate(
      updatedServiceOrderType.id,
      EntityType.SERVICE_ORDER_TYPE,
      userId,
      businessId,
      {
        source: ActivitySource.WEB,
      },
    );

    // Return the updated service order type
    return this.findOne(id, businessId);
  }

  async remove(
    id: string,
    userId: string,
    businessId: string | null,
  ): Promise<{ message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if service order type exists
    const existingServiceOrderType = await this.db
      .select()
      .from(serviceOrderTypes)
      .where(
        and(
          eq(serviceOrderTypes.id, id),
          eq(serviceOrderTypes.businessId, businessId),
          eq(serviceOrderTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingServiceOrderType) {
      throw new NotFoundException('Service order type not found');
    }

    // Soft delete the service order type
    await this.db
      .update(serviceOrderTypes)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(serviceOrderTypes.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      existingServiceOrderType.id,
      EntityType.SERVICE_ORDER_TYPE,
      userId,
      businessId,
      {
        source: ActivitySource.WEB,
      },
    );

    return { message: 'Service order type deleted successfully' };
  }

  async bulkDelete(
    serviceOrderTypeIds: string[],
    userId: string,
    businessId: string | null,
  ): Promise<{ deleted: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!serviceOrderTypeIds || serviceOrderTypeIds.length === 0) {
      throw new BadRequestException('No service order type IDs provided');
    }

    // Check which service order types exist
    const existingServiceOrderTypes = await this.db
      .select()
      .from(serviceOrderTypes)
      .where(
        and(
          inArray(serviceOrderTypes.id, serviceOrderTypeIds),
          eq(serviceOrderTypes.businessId, businessId),
          eq(serviceOrderTypes.isDeleted, false),
        ),
      );

    if (existingServiceOrderTypes.length === 0) {
      throw new NotFoundException('No service order types found to delete');
    }

    // Soft delete the service order types
    await this.db
      .update(serviceOrderTypes)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          inArray(
            serviceOrderTypes.id,
            existingServiceOrderTypes.map((sot) => sot.id),
          ),
          eq(serviceOrderTypes.businessId, businessId),
        ),
      );

    // Log the activity for each deleted service order type
    for (const serviceOrderType of existingServiceOrderTypes) {
      await this.activityLogService.logDelete(
        serviceOrderType.id,
        EntityType.SERVICE_ORDER_TYPE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );
    }

    return {
      deleted: existingServiceOrderTypes.length,
      message: `Successfully deleted ${existingServiceOrderTypes.length} service order types`,
    };
  }

  async bulkUpdateStatus(
    serviceOrderTypeIds: string[],
    status: ServiceOrderTypeStatus,
    userId: string,
    businessId: string | null,
  ): Promise<{
    updated: number;
    message: string;
    updatedIds: string[];
    failed?: { serviceOrderTypeId: string; error: string }[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!serviceOrderTypeIds || serviceOrderTypeIds.length === 0) {
      throw new BadRequestException('No service order type IDs provided');
    }

    const updatedIds: string[] = [];
    const failed: { serviceOrderTypeId: string; error: string }[] = [];

    for (const serviceOrderTypeId of serviceOrderTypeIds) {
      try {
        // Check if service order type exists
        const existingServiceOrderType = await this.db
          .select()
          .from(serviceOrderTypes)
          .where(
            and(
              eq(serviceOrderTypes.id, serviceOrderTypeId),
              eq(serviceOrderTypes.businessId, businessId),
              eq(serviceOrderTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingServiceOrderType) {
          failed.push({
            serviceOrderTypeId,
            error: 'Service order type not found',
          });
          continue;
        }

        // Update the status
        await this.db
          .update(serviceOrderTypes)
          .set({
            status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceOrderTypes.id, serviceOrderTypeId));

        updatedIds.push(serviceOrderTypeId);

        // Log the activity
        await this.activityLogService.logUpdate(
          serviceOrderTypeId,
          EntityType.SERVICE_ORDER_TYPE,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );
      } catch (error) {
        failed.push({
          serviceOrderTypeId,
          error: error.message,
        });
      }
    }

    return {
      updated: updatedIds.length,
      message: `Successfully updated status for ${updatedIds.length} service order types`,
      updatedIds,
      failed: failed.length > 0 ? failed : undefined,
    };
  }

  async checkTypeNameAvailability(
    typeName: string,
    businessId: string | null,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(serviceOrderTypes.businessId, businessId),
      ilike(serviceOrderTypes.typeName, typeName),
      eq(serviceOrderTypes.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${serviceOrderTypes.id} != ${excludeId}`);
    }

    const existingServiceOrderType = await this.db
      .select()
      .from(serviceOrderTypes)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingServiceOrderType };
  }

  async checkTypeCodeAvailability(
    typeCode: string,
    businessId: string | null,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(serviceOrderTypes.businessId, businessId),
      ilike(serviceOrderTypes.typeCode, typeCode),
      eq(serviceOrderTypes.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${serviceOrderTypes.id} != ${excludeId}`);
    }

    const existingServiceOrderType = await this.db
      .select()
      .from(serviceOrderTypes)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingServiceOrderType };
  }
}
