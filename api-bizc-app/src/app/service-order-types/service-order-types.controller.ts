import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ServiceOrderTypesService } from './service-order-types.service';
import { CreateServiceOrderTypeDto } from './dto/create-service-order-type.dto';
import { UpdateServiceOrderTypeDto } from './dto/update-service-order-type.dto';
import { ServiceOrderTypeDto } from './dto/service-order-type.dto';
import { ServiceOrderTypeSlimDto } from './dto/service-order-type-slim.dto';
import { ServiceOrderTypeIdResponseDto } from './dto/service-order-type-id-response.dto';
import { BulkServiceOrderTypeIdsResponseDto } from './dto/bulk-service-order-type-ids-response.dto';
import { BulkCreateServiceOrderTypeDto } from './dto/bulk-create-service-order-type.dto';
import { BulkDeleteServiceOrderTypeDto } from './dto/bulk-delete-service-order-type.dto';
import { BulkDeleteServiceOrderTypeResponseDto } from './dto/bulk-delete-service-order-type-response.dto';
import {
  BulkUpdateServiceOrderTypeStatusDto,
  BulkUpdateServiceOrderTypeStatusResponseDto,
} from './dto/bulk-update-service-order-type-status.dto';
import { PaginatedServiceOrderTypesResponseDto } from './dto/paginated-service-order-types-response.dto';
import { DeleteServiceOrderTypeResponseDto } from './dto/delete-service-order-type-response.dto';
import { ServiceOrderTypeNameAvailabilityResponseDto } from './dto/check-service-order-type-name.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('service-order-types')
@Controller('service-order-types')
@UseGuards(PermissionsGuard)
export class ServiceOrderTypesController {
  constructor(
    private readonly serviceOrderTypesService: ServiceOrderTypesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_CREATE)
  @ApiOperation({
    summary: 'Create a new service order type',
    description: 'Creates a new service order type for the active business',
  })
  @ApiResponse({
    status: 201,
    description: 'Service order type created successfully',
    type: ServiceOrderTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict - Service order type with same code or name already exists',
  })
  create(
    @Request() req,
    @Body() createServiceOrderTypeDto: CreateServiceOrderTypeDto,
  ): Promise<ServiceOrderTypeIdResponseDto> {
    return this.serviceOrderTypesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createServiceOrderTypeDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_CREATE)
  @ApiOperation({
    summary: 'Bulk create service order types',
    description: 'Creates multiple service order types at once',
  })
  @ApiResponse({
    status: 201,
    description: 'Service order types created successfully',
    type: BulkServiceOrderTypeIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Service order type codes or names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateServiceOrderTypeDto: BulkCreateServiceOrderTypeDto,
  ): Promise<BulkServiceOrderTypeIdsResponseDto> {
    return this.serviceOrderTypesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateServiceOrderTypeDto.serviceOrderTypes,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get all service order types for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter by creation date from (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter by creation date to (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'typeCode',
    description: 'Filter by type code (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'typeName',
    description: 'Filter by type name (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'category',
    description: 'Filter by category',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (e.g., typeName:asc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service order types retrieved successfully',
    type: PaginatedServiceOrderTypesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('typeCode') typeCode?: string,
    @Query('typeName') typeName?: string,
    @Query('category') category?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedServiceOrderTypesResponseDto> {
    return this.serviceOrderTypesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page, 10) : 1,
      limit ? parseInt(limit, 10) : 10,
      from,
      to,
      typeCode,
      typeName,
      category,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get all service order types in slim format',
    description:
      'Returns a simplified list of service order types with minimal data',
  })
  @ApiResponse({
    status: 200,
    description: 'Service order types retrieved successfully',
    type: [ServiceOrderTypeSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<ServiceOrderTypeSlimDto[]> {
    return this.serviceOrderTypesService.findAllSlim(req.user.activeBusinessId);
  }

  @Get('check-type-name/:typeName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Check if service order type name is available',
    description: 'Checks if a service order type name is available for use',
  })
  @ApiParam({
    name: 'typeName',
    description: 'Service order type name to check',
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Service order type ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: ServiceOrderTypeNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkTypeNameAvailability(
    @Request() req,
    @Param('typeName') typeName: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<ServiceOrderTypeNameAvailabilityResponseDto> {
    return this.serviceOrderTypesService.checkTypeNameAvailability(
      typeName,
      req.user.activeBusinessId,
      excludeId,
    );
  }

  @Get('check-type-code/:typeCode')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Check if service order type code is available',
    description: 'Checks if a service order type code is available for use',
  })
  @ApiParam({
    name: 'typeCode',
    description: 'Service order type code to check',
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Service order type ID to exclude from check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Code availability checked successfully',
    type: ServiceOrderTypeNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkTypeCodeAvailability(
    @Request() req,
    @Param('typeCode') typeCode: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<ServiceOrderTypeNameAvailabilityResponseDto> {
    return this.serviceOrderTypesService.checkTypeCodeAvailability(
      typeCode,
      req.user.activeBusinessId,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get a service order type by ID',
    description: 'Retrieves a specific service order type by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Service order type ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service order type retrieved successfully',
    type: ServiceOrderTypeDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Service order type not found',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<ServiceOrderTypeDto> {
    return this.serviceOrderTypesService.findOne(id, req.user.activeBusinessId);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_UPDATE)
  @ApiOperation({
    summary: 'Update a service order type',
    description: 'Updates an existing service order type by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Service order type ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service order type updated successfully',
    type: ServiceOrderTypeDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Service order type not found',
  })
  @ApiResponse({
    status: 409,
    description:
      'Conflict - Service order type with same code or name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateServiceOrderTypeDto: UpdateServiceOrderTypeDto,
  ): Promise<ServiceOrderTypeDto> {
    return this.serviceOrderTypesService.update(
      id,
      req.user.id,
      req.user.activeBusinessId,
      updateServiceOrderTypeDto,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_DELETE)
  @ApiOperation({
    summary: 'Delete a service order type',
    description: 'Soft deletes a service order type by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Service order type ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service order type deleted successfully',
    type: DeleteServiceOrderTypeResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Service order type not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteServiceOrderTypeResponseDto> {
    return this.serviceOrderTypesService.remove(
      id,
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Delete('bulk/delete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_DELETE)
  @ApiOperation({
    summary: 'Bulk delete service order types',
    description: 'Soft deletes multiple service order types at once',
  })
  @ApiResponse({
    status: 200,
    description: 'Service order types deleted successfully',
    type: BulkDeleteServiceOrderTypeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - No service order types found to delete',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteServiceOrderTypeDto: BulkDeleteServiceOrderTypeDto,
  ): Promise<BulkDeleteServiceOrderTypeResponseDto> {
    return this.serviceOrderTypesService.bulkDelete(
      bulkDeleteServiceOrderTypeDto.serviceOrderTypeIds,
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_UPDATE)
  @ApiOperation({
    summary: 'Bulk update service order type status',
    description: 'Updates the status of multiple service order types at once',
  })
  @ApiResponse({
    status: 200,
    description: 'Service order type statuses updated successfully',
    type: BulkUpdateServiceOrderTypeStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body()
    bulkUpdateServiceOrderTypeStatusDto: BulkUpdateServiceOrderTypeStatusDto,
  ): Promise<BulkUpdateServiceOrderTypeStatusResponseDto> {
    return this.serviceOrderTypesService.bulkUpdateStatus(
      bulkUpdateServiceOrderTypeStatusDto.serviceOrderTypeIds,
      bulkUpdateServiceOrderTypeStatusDto.status,
      req.user.id,
      req.user.activeBusinessId,
    );
  }
}
