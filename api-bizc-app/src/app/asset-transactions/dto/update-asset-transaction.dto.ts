import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsEnum,
  IsDateString,
  IsArray,
  IsNumber,
  Min,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  AssetsTransactionType,
  AssetTransactionStatus,
} from '../../shared/types';

export class UpdateAssetTransactionDto {
  @ApiPropertyOptional({
    description: 'Asset ID for the transaction',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  assetId?: string;

  @ApiPropertyOptional({
    description: 'Transaction type',
    enum: AssetsTransactionType,
    enumName: 'AssetsTransactionType',
    example: AssetsTransactionType.ALLOCATION,
  })
  @IsOptional()
  @IsEnum(AssetsTransactionType, {
    message: 'Transaction type must be a valid asset transaction type',
  })
  transactionType?: AssetsTransactionType;

  @ApiPropertyOptional({
    description: 'Transaction status',
    enum: AssetTransactionStatus,
    enumName: 'AssetTransactionStatus',
    example: AssetTransactionStatus.IN_PROGRESS,
  })
  @IsOptional()
  @IsEnum(AssetTransactionStatus, {
    message: 'Status must be a valid asset transaction status',
  })
  status?: AssetTransactionStatus;

  @ApiPropertyOptional({
    description: 'Reference number for the transaction',
    maxLength: 191,
    example: 'TXN-2024-001',
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  refNo?: string;

  @ApiPropertyOptional({
    description: 'Staff member ID who receives the asset',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  receiver?: string;

  @ApiPropertyOptional({
    description: 'Source location ID for the transaction',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  fromLocationId?: string;

  @ApiPropertyOptional({
    description: 'Destination location ID for the transaction',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  toLocationId?: string;

  @ApiPropertyOptional({
    description: 'Date and time of the transaction',
    example: '2024-01-15T10:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  transactionDatetime?: string;

  @ApiPropertyOptional({
    description:
      'Date until which the asset is allocated (for allocation transactions)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  allocatedUpto?: string;

  @ApiPropertyOptional({
    description: 'Return due date for the asset',
    example: '2024-06-30',
  })
  @IsOptional()
  @IsDateString()
  returnDueDate?: string;

  @ApiPropertyOptional({
    description: 'Actual return date of the asset',
    example: '2024-06-28',
  })
  @IsOptional()
  @IsDateString()
  actualReturnDate?: string;

  @ApiPropertyOptional({
    description: 'Asset condition before the transaction',
    example: 'good',
  })
  @IsOptional()
  @IsString()
  conditionBefore?: string;

  @ApiPropertyOptional({
    description: 'Asset condition after the transaction',
    example: 'excellent',
  })
  @IsOptional()
  @IsString()
  conditionAfter?: string;

  @ApiPropertyOptional({
    description: 'Additional notes for the transaction',
    example: 'Asset transferred for project requirements',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Parent transaction ID (for related transactions)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;

  @ApiPropertyOptional({
    description:
      'Array of attachment file indexes to use for this asset transaction (0-based)',
    type: [Number],
    example: [0, 1, 2],
  })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  @Min(0, { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [parseInt(value, 10)];
      }
    }
    if (Array.isArray(value)) {
      return value.map((v) => (typeof v === 'string' ? parseInt(v, 10) : v));
    }
    return value;
  })
  attachmentIndexes?: number[];
}
