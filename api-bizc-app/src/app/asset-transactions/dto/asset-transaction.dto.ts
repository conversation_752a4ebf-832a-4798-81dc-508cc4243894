import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AssetsTransactionType,
  AssetTransactionStatus,
} from '../../shared/types';

export class AssetTransactionDto {
  @ApiProperty({
    description: 'Unique identifier for the asset transaction',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Business ID the transaction belongs to',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  businessId: string;

  @ApiPropertyOptional({
    description: 'Asset ID for the transaction',
    example: '550e8400-e29b-41d4-a716-446655440002',
    nullable: true,
  })
  assetId?: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: AssetsTransactionType,
    enumName: 'AssetsTransactionType',
    example: AssetsTransactionType.ALLOCATION,
  })
  transactionType: AssetsTransactionType;

  @ApiProperty({
    description: 'Transaction status',
    enum: AssetTransactionStatus,
    enumName: 'AssetTransactionStatus',
    example: AssetTransactionStatus.COMPLETED,
  })
  status: AssetTransactionStatus;

  @ApiProperty({
    description: 'Reference number for the transaction',
    example: 'TXN-2024-001',
  })
  refNo: string;

  @ApiPropertyOptional({
    description: 'Staff member ID who receives the asset',
    example: '550e8400-e29b-41d4-a716-446655440003',
    nullable: true,
  })
  receiver?: string;

  @ApiPropertyOptional({
    description: 'Receiver staff member name',
    example: 'John Doe',
    nullable: true,
  })
  receiverName?: string;

  @ApiPropertyOptional({
    description: 'Source location ID for the transaction',
    example: '550e8400-e29b-41d4-a716-446655440004',
    nullable: true,
  })
  fromLocationId?: string;

  @ApiPropertyOptional({
    description: 'Source location name',
    example: 'Main Warehouse',
    nullable: true,
  })
  fromLocationName?: string;

  @ApiPropertyOptional({
    description: 'Destination location ID for the transaction',
    example: '550e8400-e29b-41d4-a716-446655440005',
    nullable: true,
  })
  toLocationId?: string;

  @ApiPropertyOptional({
    description: 'Destination location name',
    example: 'Branch Office',
    nullable: true,
  })
  toLocationName?: string;

  @ApiProperty({
    description: 'Date and time of the transaction',
    example: '2024-01-15T10:30:00Z',
  })
  transactionDatetime: Date;

  @ApiPropertyOptional({
    description:
      'Date until which the asset is allocated (for allocation transactions)',
    example: '2024-12-31',
    nullable: true,
  })
  allocatedUpto?: Date;

  @ApiPropertyOptional({
    description: 'Return due date for the asset',
    example: '2024-06-30',
    nullable: true,
  })
  returnDueDate?: Date;

  @ApiPropertyOptional({
    description: 'Actual return date of the asset',
    example: '2024-06-28',
    nullable: true,
  })
  actualReturnDate?: Date;

  @ApiPropertyOptional({
    description: 'Asset condition before the transaction',
    example: 'good',
    nullable: true,
  })
  conditionBefore?: string;

  @ApiPropertyOptional({
    description: 'Asset condition after the transaction',
    example: 'excellent',
    nullable: true,
  })
  conditionAfter?: string;

  @ApiPropertyOptional({
    description: 'Additional notes for the transaction',
    example: 'Asset transferred for project requirements',
    nullable: true,
  })
  notes?: string;

  @ApiPropertyOptional({
    description: 'Parent transaction ID (for related transactions)',
    example: '550e8400-e29b-41d4-a716-446655440006',
    nullable: true,
  })
  parentId?: string;

  @ApiPropertyOptional({
    description: 'Asset information',
    type: Object,
    example: {
      id: '550e8400-e29b-41d4-a716-446655440002',
      name: 'Laptop Dell XPS 13',
      assetTag: 'ASSET-001',
    },
    nullable: true,
  })
  asset?: {
    id: string;
    name: string;
    assetTag: string;
  };

  @ApiProperty({
    description: 'Name of the user who created the transaction',
    example: 'Admin User',
  })
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Name of the user who last updated the transaction',
    example: 'Manager User',
    nullable: true,
  })
  updatedBy?: string;

  @ApiPropertyOptional({
    description: 'Array of attachment details',
    type: Array,
    example: [
      {
        id: '550e8400-e29b-41d4-a716-446655440007',
        fileName: 'receipt.pdf',
        originalName: 'Transaction Receipt.pdf',
        publicUrl: 'https://example.com/receipt.pdf',
        size: 1024000,
        mimeType: 'application/pdf',
      },
    ],
  })
  attachments?: Array<{
    id: string;
    fileName: string;
    originalName: string;
    publicUrl: string;
    size: number;
    mimeType: string;
  }>;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T08:00:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
