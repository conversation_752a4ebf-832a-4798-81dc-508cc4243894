import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import { PackageDto } from './dto/package.dto';
import { PackageSlimDto } from './dto/package-slim.dto';
import { PackageListDto } from './dto/package-list.dto';
import { packages } from '../drizzle/schema/packages.schema';
import { media, MediaReferenceType } from '../drizzle/schema/media.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { PackageStatus } from '../shared/types';
import { PackageType } from '../drizzle/schema/packages.schema';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class PackagesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPackageDto: CreatePackageDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a package with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingPackage = await this.db
        .select()
        .from(packages)
        .where(
          and(
            eq(packages.businessId, businessId),
            ilike(packages.name, createPackageDto.name),
            eq(packages.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingPackage) {
        throw new ConflictException(
          `A package with the name '${createPackageDto.name}' already exists for this business`,
        );
      }

      // Check if a package with the same package code already exists for this business
      if (createPackageDto.packageCode) {
        const existingCodePackage = await this.db
          .select()
          .from(packages)
          .where(
            and(
              eq(packages.businessId, businessId),
              ilike(packages.packageCode, createPackageDto.packageCode),
              eq(packages.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingCodePackage) {
          throw new ConflictException(
            `A package with the code '${createPackageDto.packageCode}' already exists for this business`,
          );
        }
      }

      // Get the next position for this business
      const maxPositionResult = await this.db
        .select({
          maxPosition: sql<number>`COALESCE(MAX(${packages.position}), 0)`,
        })
        .from(packages)
        .where(
          and(
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        );

      const nextPosition = (maxPositionResult[0]?.maxPosition || 0) + 1;

      let ogImageId: string | undefined;

      // Create the package first to get the ID
      const newPackage = await this.db
        .insert(packages)
        .values({
          businessId,
          packageCode: createPackageDto.packageCode,
          name: createPackageDto.name,
          shortDescription: createPackageDto.shortDescription,
          description: createPackageDto.description,
          type: createPackageDto.type,
          status: createPackageDto.status || PackageStatus.ACTIVE,
          basePrice: createPackageDto.basePrice,
          salePrice: createPackageDto.salePrice,
          cost: createPackageDto.cost,
          incomeAccountId: createPackageDto.incomeAccountId,
          expenseAccountId: createPackageDto.expenseAccountId,
          seoTitle: createPackageDto.seoTitle,
          seoDescription: createPackageDto.seoDescription,
          seoKeywords: createPackageDto.seoKeywords,
          position: nextPosition,
          taxType: createPackageDto.taxType,
          defaultTaxRateId: createPackageDto.defaultTaxRateId,
          key1: createPackageDto.key1,
          key2: createPackageDto.key2,
          key3: createPackageDto.key3,
          key4: createPackageDto.key4,
          key5: createPackageDto.key5,
          key6: createPackageDto.key6,
          key7: createPackageDto.key7,
          key8: createPackageDto.key8,
          key9: createPackageDto.key9,
          key10: createPackageDto.key10,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: packages.id });

      const packageId = newPackage[0].id;

      // Handle image upload
      if (imageFile) {
        await this.mediaService.uploadMultipleMediaWithReference(
          [imageFile],
          MediaReferenceType.PACKAGES,
          businessId,
          userId,
          packageId,
        );
      }

      // Handle ogImage upload
      if (ogImageFile) {
        const uploadedOgImage =
          await this.mediaService.uploadMediaWithReference(
            ogImageFile,
            MediaReferenceType.PACKAGES,
            businessId,
            userId,
            packageId,
          );
        if (uploadedOgImage) {
          ogImageId = uploadedOgImage.id;
        }
      }

      // Update the package with ogImage if uploaded
      if (ogImageId) {
        await this.db
          .update(packages)
          .set({ ogImage: ogImageId })
          .where(eq(packages.id, packageId));
      }

      // Log the activity
      await this.activityLogService.logCreate(
        packageId,
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id: packageId };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create package: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPackageDto: CreatePackageDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    return this.create(
      userId,
      businessId,
      createPackageDto,
      imageFile,
      ogImageFile,
    );
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    packagesJson: string,
    images?: Express.Multer.File[],
  ): Promise<{ ids: string[]; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      let packagesToCreate: CreatePackageDto[];
      try {
        packagesToCreate = JSON.parse(packagesJson);
      } catch {
        throw new BadRequestException('Invalid JSON format for packages');
      }

      if (!Array.isArray(packagesToCreate) || packagesToCreate.length === 0) {
        throw new BadRequestException('Packages array cannot be empty');
      }

      // Check for duplicate names within the request
      const names = packagesToCreate.map((pkg) => pkg.name.toLowerCase());
      const duplicateNames = names.filter(
        (name, index) => names.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate package names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for duplicate package codes within the request
      const codes = packagesToCreate
        .map((pkg) => pkg.packageCode?.toLowerCase())
        .filter(Boolean);
      const duplicateCodes = codes.filter(
        (code, index) => codes.indexOf(code) !== index,
      );
      if (duplicateCodes.length > 0) {
        throw new BadRequestException(
          `Duplicate package codes found in request: ${duplicateCodes.join(', ')}`,
        );
      }

      // Check if any packages with these names already exist
      const existingPackages = await this.db
        .select({ name: packages.name, packageCode: packages.packageCode })
        .from(packages)
        .where(
          and(
            eq(packages.businessId, businessId),
            or(
              inArray(packages.name, names),
              inArray(packages.packageCode, codes.filter(Boolean)),
            ),
            eq(packages.isDeleted, false),
          ),
        );

      if (existingPackages.length > 0) {
        const existingNames = existingPackages.map((pkg) => pkg.name);
        const existingCodes = existingPackages.map((pkg) => pkg.packageCode);
        throw new ConflictException(
          `Packages already exist with names: ${existingNames.join(', ')} or codes: ${existingCodes.join(', ')}`,
        );
      }

      // Get the next position for this business
      const maxPositionResult = await this.db
        .select({
          maxPosition: sql<number>`COALESCE(MAX(${packages.position}), 0)`,
        })
        .from(packages)
        .where(
          and(
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        );

      let nextPosition = (maxPositionResult[0]?.maxPosition || 0) + 1;

      const createdIds: string[] = [];

      // Create packages one by one to handle images properly
      for (const packageDto of packagesToCreate) {
        let imageFile: Express.Multer.File | undefined;
        let ogImageFile: Express.Multer.File | undefined;

        // Handle image mapping if imageIndex is provided
        if (
          packageDto.imageIndex !== undefined &&
          images &&
          images[packageDto.imageIndex]
        ) {
          imageFile = images[packageDto.imageIndex];
        }

        // Handle ogImage mapping if ogImageIndex is provided
        if (
          packageDto.ogImageIndex !== undefined &&
          images &&
          images[packageDto.ogImageIndex]
        ) {
          ogImageFile = images[packageDto.ogImageIndex];
        }

        let ogImageId: string | undefined;

        // Create the package first to get the ID
        const newPackage = await this.db
          .insert(packages)
          .values({
            businessId,
            packageCode: packageDto.packageCode,
            name: packageDto.name,
            shortDescription: packageDto.shortDescription,
            description: packageDto.description,
            type: packageDto.type,
            status: packageDto.status || PackageStatus.ACTIVE,
            basePrice: packageDto.basePrice,
            salePrice: packageDto.salePrice,
            cost: packageDto.cost,
            incomeAccountId: packageDto.incomeAccountId,
            expenseAccountId: packageDto.expenseAccountId,
            seoTitle: packageDto.seoTitle,
            seoDescription: packageDto.seoDescription,
            seoKeywords: packageDto.seoKeywords,
            position: nextPosition++,
            taxType: packageDto.taxType,
            defaultTaxRateId: packageDto.defaultTaxRateId,
            key1: packageDto.key1,
            key2: packageDto.key2,
            key3: packageDto.key3,
            key4: packageDto.key4,
            key5: packageDto.key5,
            key6: packageDto.key6,
            key7: packageDto.key7,
            key8: packageDto.key8,
            key9: packageDto.key9,
            key10: packageDto.key10,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: packages.id });

        const packageId = newPackage[0].id;
        createdIds.push(packageId);

        // Handle image upload
        if (imageFile) {
          await this.mediaService.uploadMultipleMediaWithReference(
            [imageFile],
            MediaReferenceType.PACKAGES,
            businessId,
            userId,
            packageId,
          );
        }

        // Handle ogImage upload
        if (ogImageFile) {
          const uploadedOgImage =
            await this.mediaService.uploadMediaWithReference(
              ogImageFile,
              MediaReferenceType.PACKAGES,
              businessId,
              userId,
              packageId,
            );
          if (uploadedOgImage) {
            ogImageId = uploadedOgImage.id;
          }
        }

        // Update the package with ogImage if uploaded
        if (ogImageId) {
          await this.db
            .update(packages)
            .set({ ogImage: ogImageId })
            .where(eq(packages.id, packageId));
        }
      }

      // Log the activity
      await this.activityLogService.logCreate(
        createdIds.join(','),
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        ids: createdIds,
        message: `Successfully created ${createdIds.length} packages`,
      };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create packages: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    from?: string,
    to?: string,
    name?: string,
    packageCode?: string,
    type?: string,
    status?: string,
    filters?: string,
    joinOperator: 'and' | 'or' = 'and',
    sort?: string,
  ): Promise<{
    data: PackageListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Build base conditions
      const conditions = [
        eq(packages.businessId, businessId),
        eq(packages.isDeleted, false),
      ];

      // Add date range filters
      if (from) {
        conditions.push(gte(packages.createdAt, new Date(from)));
      }
      if (to) {
        conditions.push(lte(packages.createdAt, new Date(to)));
      }

      // Add simple filters
      if (name) {
        conditions.push(
          or(
            ilike(packages.name, `%${name}%`),
            ilike(packages.packageCode, `%${name}%`),
          ),
        );
      }

      if (packageCode) {
        conditions.push(ilike(packages.packageCode, `%${packageCode}%`));
      }

      if (type) {
        const types = type.split(',').map((t) => t.trim()) as PackageType[];
        conditions.push(inArray(packages.type, types));
      }

      if (status) {
        const statuses = status
          .split(',')
          .map((s) => s.trim()) as PackageStatus[];
        conditions.push(inArray(packages.status, statuses));
      }

      // Handle advanced filters
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = parsedFilters.map((filter: any) => {
            const { id, value, operator } = filter;

            switch (operator) {
              case 'iLike':
                return ilike(packages[id], `%${value}%`);
              case 'notILike':
                return sql`NOT ${ilike(packages[id], `%${value}%`)}`;
              case 'eq':
                return eq(packages[id], value);
              case 'ne':
                return sql`${packages[id]} != ${value}`;
              case 'isEmpty':
                return sql`${packages[id]} IS NULL OR ${packages[id]} = ''`;
              case 'isNotEmpty':
                return sql`${packages[id]} IS NOT NULL AND ${packages[id]} != ''`;
              default:
                return eq(packages[id], value);
            }
          });

          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              conditions.push(or(...filterConditions));
            } else {
              conditions.push(and(...filterConditions));
            }
          }
        } catch {
          // Ignore invalid filter JSON
        }
      }

      // Handle sorting
      let orderBy = [asc(packages.position), asc(packages.id)];
      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          if (Array.isArray(parsedSort) && parsedSort.length > 0) {
            orderBy = parsedSort.map((sortItem: any) => {
              const { id, desc: isDesc } = sortItem;
              return isDesc ? desc(packages[id]) : asc(packages[id]);
            });
          }
        } catch {
          // Use default sorting if sort JSON is invalid
        }
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Get total count
      const totalResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(packages)
        .where(and(...conditions));

      const total = totalResult[0]?.count || 0;

      // Get packages with user information
      const result = await this.db
        .select({
          id: packages.id,
          packageCode: packages.packageCode,
          name: packages.name,
          shortDescription: packages.shortDescription,
          type: packages.type,
          status: packages.status,
          basePrice: packages.basePrice,
          salePrice: packages.salePrice,
          position: packages.position,
          createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
          updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
          createdAt: packages.createdAt,
          updatedAt: packages.updatedAt,
          image: sql<string>`CASE WHEN ${media.id} IS NOT NULL THEN ${media.signedUrl} ELSE NULL END`,
        })
        .from(packages)
        .leftJoin(users, eq(packages.createdBy, users.id))
        .leftJoin(
          sql`${users} AS updated_user`,
          eq(packages.updatedBy, sql`updated_user.id`),
        )
        .leftJoin(
          media,
          and(
            eq(media.referenceType, 'packages'),
            eq(media.referenceId, packages.id),
            eq(media.isDeleted, false),
          ),
        )
        .where(and(...conditions))
        .orderBy(...orderBy)
        .limit(limit)
        .offset(offset);

      // Note: Skipping view activity logging for performance reasons
      // await this.activityLogService.logView(...);

      return {
        data: result,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch packages: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingPackage = await this.db
        .select({ id: packages.id })
        .from(packages)
        .where(
          and(
            eq(packages.businessId, businessId),
            ilike(packages.name, name),
            eq(packages.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingPackage };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to check name availability: ${error.message}`,
      );
    }
  }

  async checkPackageCodeAvailability(
    userId: string,
    businessId: string | null,
    packageCode: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingPackage = await this.db
        .select({ id: packages.id })
        .from(packages)
        .where(
          and(
            eq(packages.businessId, businessId),
            ilike(packages.packageCode, packageCode),
            eq(packages.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingPackage };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to check package code availability: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<PackageSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: packages.id,
        name: packages.name,
        position: packages.position,
      })
      .from(packages)
      .where(
        and(
          eq(packages.isDeleted, false),
          eq(packages.status, PackageStatus.ACTIVE),
          eq(packages.businessId, businessId),
        ),
      )
      .orderBy(asc(packages.position), asc(packages.id));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result;
  }

  async findOne(userId: string, id: string): Promise<PackageDto> {
    try {
      const result = await this.db
        .select({
          id: packages.id,
          businessId: packages.businessId,
          packageCode: packages.packageCode,
          name: packages.name,
          shortDescription: packages.shortDescription,
          description: packages.description,
          type: packages.type,
          status: packages.status,
          basePrice: packages.basePrice,
          salePrice: packages.salePrice,
          cost: packages.cost,
          incomeAccountId: packages.incomeAccountId,
          expenseAccountId: packages.expenseAccountId,
          seoTitle: packages.seoTitle,
          seoDescription: packages.seoDescription,
          seoKeywords: packages.seoKeywords,
          ogImage: sql<string>`CASE WHEN og_media.id IS NOT NULL THEN og_media.signed_url ELSE NULL END`,
          position: packages.position,
          taxType: packages.taxType,
          defaultTaxRateId: packages.defaultTaxRateId,
          key1: packages.key1,
          key2: packages.key2,
          key3: packages.key3,
          key4: packages.key4,
          key5: packages.key5,
          key6: packages.key6,
          key7: packages.key7,
          key8: packages.key8,
          key9: packages.key9,
          key10: packages.key10,
          createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
          updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
          createdAt: packages.createdAt,
          updatedAt: packages.updatedAt,
        })
        .from(packages)
        .leftJoin(users, eq(packages.createdBy, users.id))
        .leftJoin(
          sql`${users} AS updated_user`,
          eq(packages.updatedBy, sql`updated_user.id`),
        )
        .leftJoin(
          sql`${media} AS og_media`,
          eq(packages.ogImage, sql`og_media.id`),
        )
        .where(and(eq(packages.id, id), eq(packages.isDeleted, false)))
        .then((results) => results[0]);

      if (!result) {
        throw new NotFoundException('Package not found');
      }

      // Access control is handled by the business ID check in the query

      // Note: Skipping view activity logging for performance reasons
      // await this.activityLogService.logView(...);

      return result;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch package: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePackageDto: UpdatePackageDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if package exists and belongs to the business
      const existingPackage = await this.db
        .select()
        .from(packages)
        .where(
          and(
            eq(packages.id, id),
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPackage) {
        throw new NotFoundException('Package not found');
      }

      // Check for name conflicts (excluding current package)
      if (updatePackageDto.name) {
        const nameConflict = await this.db
          .select()
          .from(packages)
          .where(
            and(
              eq(packages.businessId, businessId),
              ilike(packages.name, updatePackageDto.name),
              sql`${packages.id} != ${id}`,
              eq(packages.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (nameConflict) {
          throw new ConflictException(
            `A package with the name '${updatePackageDto.name}' already exists for this business`,
          );
        }
      }

      // Check for package code conflicts (excluding current package)
      if (updatePackageDto.packageCode) {
        const codeConflict = await this.db
          .select()
          .from(packages)
          .where(
            and(
              eq(packages.businessId, businessId),
              ilike(packages.packageCode, updatePackageDto.packageCode),
              sql`${packages.id} != ${id}`,
              eq(packages.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (codeConflict) {
          throw new ConflictException(
            `A package with the code '${updatePackageDto.packageCode}' already exists for this business`,
          );
        }
      }

      let ogImageId: string | undefined;

      // Handle image upload
      if (imageFile) {
        await this.mediaService.uploadMultipleMediaWithReference(
          [imageFile],
          MediaReferenceType.PACKAGES,
          businessId,
          userId,
          id,
        );
      }

      // Handle ogImage upload
      if (ogImageFile) {
        const uploadedOgImage =
          await this.mediaService.uploadMediaWithReference(
            ogImageFile,
            MediaReferenceType.PACKAGES,
            businessId,
            userId,
            id,
          );
        if (uploadedOgImage) {
          ogImageId = uploadedOgImage.id;
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update fields that are provided
      if (updatePackageDto.packageCode !== undefined)
        updateData.packageCode = updatePackageDto.packageCode;
      if (updatePackageDto.name !== undefined)
        updateData.name = updatePackageDto.name;
      if (updatePackageDto.shortDescription !== undefined)
        updateData.shortDescription = updatePackageDto.shortDescription;
      if (updatePackageDto.description !== undefined)
        updateData.description = updatePackageDto.description;
      if (updatePackageDto.type !== undefined)
        updateData.type = updatePackageDto.type;
      if (updatePackageDto.status !== undefined)
        updateData.status = updatePackageDto.status;
      if (updatePackageDto.basePrice !== undefined)
        updateData.basePrice = updatePackageDto.basePrice;
      if (updatePackageDto.salePrice !== undefined)
        updateData.salePrice = updatePackageDto.salePrice;
      if (updatePackageDto.cost !== undefined)
        updateData.cost = updatePackageDto.cost;
      if (updatePackageDto.incomeAccountId !== undefined)
        updateData.incomeAccountId = updatePackageDto.incomeAccountId;
      if (updatePackageDto.expenseAccountId !== undefined)
        updateData.expenseAccountId = updatePackageDto.expenseAccountId;
      if (updatePackageDto.seoTitle !== undefined)
        updateData.seoTitle = updatePackageDto.seoTitle;
      if (updatePackageDto.seoDescription !== undefined)
        updateData.seoDescription = updatePackageDto.seoDescription;
      if (updatePackageDto.seoKeywords !== undefined)
        updateData.seoKeywords = updatePackageDto.seoKeywords;
      if (ogImageId !== undefined) updateData.ogImage = ogImageId;
      if (updatePackageDto.taxType !== undefined)
        updateData.taxType = updatePackageDto.taxType;
      if (updatePackageDto.defaultTaxRateId !== undefined)
        updateData.defaultTaxRateId = updatePackageDto.defaultTaxRateId;
      if (updatePackageDto.key1 !== undefined)
        updateData.key1 = updatePackageDto.key1;
      if (updatePackageDto.key2 !== undefined)
        updateData.key2 = updatePackageDto.key2;
      if (updatePackageDto.key3 !== undefined)
        updateData.key3 = updatePackageDto.key3;
      if (updatePackageDto.key4 !== undefined)
        updateData.key4 = updatePackageDto.key4;
      if (updatePackageDto.key5 !== undefined)
        updateData.key5 = updatePackageDto.key5;
      if (updatePackageDto.key6 !== undefined)
        updateData.key6 = updatePackageDto.key6;
      if (updatePackageDto.key7 !== undefined)
        updateData.key7 = updatePackageDto.key7;
      if (updatePackageDto.key8 !== undefined)
        updateData.key8 = updatePackageDto.key8;
      if (updatePackageDto.key9 !== undefined)
        updateData.key9 = updatePackageDto.key9;
      if (updatePackageDto.key10 !== undefined)
        updateData.key10 = updatePackageDto.key10;

      // Update the package
      await this.db.update(packages).set(updateData).where(eq(packages.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update package: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePackageDto: UpdatePackageDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    return this.update(
      userId,
      businessId,
      id,
      updatePackageDto,
      imageFile,
      ogImageFile,
    );
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if package exists and belongs to the business
      const existingPackage = await this.db
        .select({ name: packages.name })
        .from(packages)
        .where(
          and(
            eq(packages.id, id),
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPackage) {
        throw new NotFoundException('Package not found');
      }

      // Soft delete the package
      await this.db
        .update(packages)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(packages.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        id,
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Package deleted successfully',
        id,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete package: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    packageIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
    failed: Array<{ id: string; reason: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!packageIds || packageIds.length === 0) {
        throw new BadRequestException('Package IDs array cannot be empty');
      }

      // Find existing packages
      const existingPackages = await this.db
        .select({ id: packages.id, name: packages.name })
        .from(packages)
        .where(
          and(
            inArray(packages.id, packageIds),
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        );

      const existingIds = existingPackages.map((pkg) => pkg.id);
      const failed: Array<{ id: string; reason: string }> = [];

      // Find packages that don't exist
      packageIds.forEach((id) => {
        if (!existingIds.includes(id)) {
          failed.push({ id, reason: 'Package not found' });
        }
      });

      let deletedCount = 0;
      const deletedIds: string[] = [];

      if (existingIds.length > 0) {
        // Soft delete existing packages
        await this.db
          .update(packages)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(packages.id, existingIds));

        deletedCount = existingIds.length;
        deletedIds.push(...existingIds);
      }

      // Log the activity
      await this.activityLogService.logDelete(
        deletedIds.join(','),
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        deleted: deletedCount,
        message: `Successfully deleted ${deletedCount} packages`,
        deletedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete packages: ${error.message}`,
      );
    }
  }

  async updatePackagePositions(
    userId: string,
    businessId: string | null,
    updates: Array<{ id: string; position: number }>,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('Updates array cannot be empty');
      }

      const packageIds = updates.map((update) => update.id);

      // Verify all packages exist and belong to the business
      const existingPackages = await this.db
        .select({ id: packages.id })
        .from(packages)
        .where(
          and(
            inArray(packages.id, packageIds),
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        );

      const existingIds = existingPackages.map((pkg) => pkg.id);
      const missingIds = packageIds.filter((id) => !existingIds.includes(id));

      if (missingIds.length > 0) {
        throw new NotFoundException(
          `Packages not found: ${missingIds.join(', ')}`,
        );
      }

      // Update positions
      let updatedCount = 0;
      for (const update of updates) {
        await this.db
          .update(packages)
          .set({
            position: update.position,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(packages.id, update.id));
        updatedCount++;
      }

      // Log the activity
      await this.activityLogService.logUpdate(
        packageIds.join(','),
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update package positions: ${error.message}`,
      );
    }
  }

  async bulkUpdatePackageStatus(
    userId: string,
    businessId: string | null,
    packageIds: string[],
    status: PackageStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ id: string; reason: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!packageIds || packageIds.length === 0) {
        throw new BadRequestException('Package IDs array cannot be empty');
      }

      // Find existing packages
      const existingPackages = await this.db
        .select({ id: packages.id, name: packages.name })
        .from(packages)
        .where(
          and(
            inArray(packages.id, packageIds),
            eq(packages.businessId, businessId),
            eq(packages.isDeleted, false),
          ),
        );

      const existingIds = existingPackages.map((pkg) => pkg.id);
      const failed: Array<{ id: string; reason: string }> = [];

      // Find packages that don't exist
      packageIds.forEach((id) => {
        if (!existingIds.includes(id)) {
          failed.push({ id, reason: 'Package not found' });
        }
      });

      let updatedCount = 0;
      const updatedIds: string[] = [];

      if (existingIds.length > 0) {
        // Update status for existing packages
        await this.db
          .update(packages)
          .set({
            status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(packages.id, existingIds));

        updatedCount = existingIds.length;
        updatedIds.push(...existingIds);
      }

      // Log the activity
      await this.activityLogService.logUpdate(
        updatedIds.join(','),
        EntityType.PACKAGE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update package status: ${error.message}`,
      );
    }
  }
}
