import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateRentalItemCategoryDto } from './dto/create-rental-item-categories.dto';
import { UpdateRentalItemCategoryDto } from './dto/update-rental-item-categories.dto';
import { RentalItemCategoryDto } from './dto/rental-item-categories.dto';
import { RentalItemCategorySlimDto } from './dto/rental-item-categories-slim.dto';
import { RentalItemCategoryListDto } from './dto/rental-item-categories-list.dto';
import {
  rentalItemCategories,
  rentalItemCategoryLocations,
} from '../drizzle/schema/rental-item-categories.schema';
import { assetCategories } from '../drizzle/schema/asset-categories.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { RentalItemCategoryStatus } from '@app/shared/types/rental-item-category.enum';
import { AssetCategoryReferenceType } from '../shared/types/asset-category-reference.enum';
import { CategoryStatus } from '../shared/types';
import { rentalItems } from '@app/drizzle/schema/rental-items.schema';
import { media } from '@app/drizzle/schema/media.schema';
import { locations } from '@app/drizzle/schema/locations.schema';

@Injectable()
export class RentalItemCategoriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createRentalItemCategoryDto: CreateRentalItemCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a category with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingCategory = await this.db
        .select()
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            ilike(rentalItemCategories.name, createRentalItemCategoryDto.name),
            eq(rentalItemCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingCategory) {
        throw new ConflictException(
          `A rental item category with the name '${createRentalItemCategoryDto.name}' already exists for this business`,
        );
      }

      // Create slug from name if not provided
      if (!createRentalItemCategoryDto.slug) {
        createRentalItemCategoryDto.slug = createRentalItemCategoryDto.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');
      }

      // Check if a category with the same slug already exists for this business
      if (createRentalItemCategoryDto.slug) {
        const existingSlugCategory = await this.db
          .select()
          .from(rentalItemCategories)
          .where(
            and(
              eq(rentalItemCategories.businessId, businessId),
              eq(rentalItemCategories.slug, createRentalItemCategoryDto.slug),
              eq(rentalItemCategories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingSlugCategory) {
          throw new ConflictException(
            `A rental item category with the slug '${createRentalItemCategoryDto.slug}' already exists for this business`,
          );
        }
      }

      // Check if a category with the same shortCode already exists for this business
      if (createRentalItemCategoryDto.shortCode) {
        const existingShortCodeCategory = await this.db
          .select()
          .from(rentalItemCategories)
          .where(
            and(
              eq(rentalItemCategories.businessId, businessId),
              eq(
                rentalItemCategories.shortCode,
                createRentalItemCategoryDto.shortCode,
              ),
              eq(rentalItemCategories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingShortCodeCategory) {
          throw new ConflictException(
            `A rental item category with the short code '${createRentalItemCategoryDto.shortCode}' already exists for this business`,
          );
        }
      }

      // Handle parent category if provided
      if (createRentalItemCategoryDto.parentId) {
        const parentCategory = await this.db
          .select()
          .from(rentalItemCategories)
          .where(
            and(
              eq(rentalItemCategories.id, createRentalItemCategoryDto.parentId),
              eq(rentalItemCategories.businessId, businessId),
              eq(rentalItemCategories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentCategory) {
          throw new BadRequestException('Parent category not found');
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'rentalItemCategories',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'rentalItemCategories/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      console.log(
        'createRentalItemCategoryDto.locationIds',
        createRentalItemCategoryDto.locationIds,
      );

      // Use a transaction to ensure position reordering, category creation, and asset-category creation are atomic
      const newCategory = await this.db.transaction(async (tx) => {
        // Shift all existing categories down by 1 position to make room at position 1
        await this.reorderPositions(
          tx,
          businessId,
          createRentalItemCategoryDto.parentId,
          1, // Insert at position 1 (first position)
        );

        // Insert new category at position 1
        const [category] = await tx
          .insert(rentalItemCategories)
          .values({
            businessId,
            name: createRentalItemCategoryDto.name,
            shortCode: createRentalItemCategoryDto.shortCode,
            parentId: createRentalItemCategoryDto.parentId,
            description: createRentalItemCategoryDto.description,
            slug: createRentalItemCategoryDto.slug,
            availableOnline:
              createRentalItemCategoryDto.availableOnline ?? false,
            position: 1, // Always create new categories at position 1 (first)
            color: createRentalItemCategoryDto.color,
            image: mediaId,
            isAllocatedToAllLocations:
              createRentalItemCategoryDto.isAllocatedToAllLocations ?? false,
            seoTitle: createRentalItemCategoryDto.seoTitle,
            seoDescription: createRentalItemCategoryDto.seoDescription,
            seoKeywords: createRentalItemCategoryDto.seoKeywords,
            ogImage: ogImageId,
            createdBy: userId,
            status:
              createRentalItemCategoryDto.status ??
              RentalItemCategoryStatus.ACTIVE,
          })
          .returning();

        // Automatically create corresponding asset-category
        const [assetCategory] = await tx
          .insert(assetCategories)
          .values({
            businessId,
            name: createRentalItemCategoryDto.name,
            description: createRentalItemCategoryDto.description,
            referenceId: category.id,
            referenceType: AssetCategoryReferenceType.RENTAL_ITEM_CATEGORY,
            createdBy: userId,
            status: CategoryStatus.ACTIVE,
          })
          .returning();

        // Update the rental-item-category with the asset-category ID
        await tx
          .update(rentalItemCategories)
          .set({ assetCategoryId: assetCategory.id })
          .where(eq(rentalItemCategories.id, category.id));

        return { ...category, assetCategoryId: assetCategory.id };
      });

      // Handle location associations
      await this.manageRentalItemCategoryLocations(
        newCategory.id,
        businessId,
        createRentalItemCategoryDto.isAllocatedToAllLocations ?? false,
        createRentalItemCategoryDto.locationIds,
        userId,
      );

      // Log the category creation activity
      await this.activityLogService.logCreate(
        newCategory.id,
        EntityType.RENTAL_ITEM_CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newCategory.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create category: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: RentalItemCategoryDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(rentalItemCategories.isDeleted, false),
      eq(rentalItemCategories.status, RentalItemCategoryStatus.ACTIVE),
      eq(rentalItemCategories.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(rentalItemCategories.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(rentalItemCategories.createdAt, toDate));
      }
    }

    // Find all categories for the user's active business with pagination
    const result = await this.db
      .select()
      .from(rentalItemCategories)
      .where(and(...whereConditions))
      .orderBy(desc(rentalItemCategories.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(rentalItemCategories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return {
      data: await Promise.all(
        result.map((rentalItemCategory) =>
          this.mapToRentalItemCategoryDto(rentalItemCategory),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllByBusiness(
    businessId: string,
  ): Promise<RentalItemCategoryDto[]> {
    // This method keeps same functionality but we might consider if this is still needed
    // since we now use activeBusinessId by default
    const result = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.isDeleted, false),
          eq(rentalItemCategories.status, RentalItemCategoryStatus.ACTIVE),
          eq(rentalItemCategories.businessId, businessId),
        ),
      )
      .orderBy(
        asc(rentalItemCategories.position),
        asc(rentalItemCategories.id),
      );

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return await Promise.all(
      result.map((rentalItemCategory) =>
        this.mapToRentalItemCategoryDto(rentalItemCategory),
      ),
    );
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    shortCode?: string,
    status?: string,
    availableOnline?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: RentalItemCategoryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(rentalItemCategories.isDeleted, false),
      eq(rentalItemCategories.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(rentalItemCategories.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(rentalItemCategories.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches both name and shortCode)
    if (name) {
      whereConditions.push(
        or(
          ilike(rentalItemCategories.name, `%${name}%`),
          ilike(rentalItemCategories.shortCode, `%${name}%`),
        ),
      );
    }

    // Add slug filtering if provided
    if (slug) {
      whereConditions.push(ilike(rentalItemCategories.slug, `%${slug}%`));
    }

    // Add shortCode filtering if provided
    if (shortCode) {
      whereConditions.push(
        ilike(rentalItemCategories.shortCode, `%${shortCode}%`),
      );
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as RentalItemCategoryStatus);
      whereConditions.push(inArray(rentalItemCategories.status, statusArray));
    }

    // Add availableOnline filtering if provided
    if (availableOnline) {
      // Decode URL-encoded commas and split by comma
      const decodedAvailableOnline = decodeURIComponent(availableOnline);
      const availableOnlineValues = decodedAvailableOnline
        .split(',')
        .map((s) => s.trim());
      if (availableOnlineValues.length === 1) {
        const boolValue = availableOnlineValues[0].toLowerCase() === 'true';
        whereConditions.push(
          eq(rentalItemCategories.availableOnline, boolValue),
        );
      } else {
        // Handle multiple boolean values (true, false)
        const boolValues = availableOnlineValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(rentalItemCategories.availableOnline, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(rentalItemCategories.availableOnline, false));
        }
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(rentalItemCategories.name, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(rentalItemCategories.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(rentalItemCategories.name, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${rentalItemCategories.name} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${rentalItemCategories.name} IS NULL OR ${rentalItemCategories.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${rentalItemCategories.name} IS NOT NULL AND ${rentalItemCategories.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(
                      rentalItemCategories.status,
                      value as RentalItemCategoryStatus[],
                    ),
                  );
                } else {
                  filterConditions.push(eq(rentalItemCategories.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${rentalItemCategories.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${rentalItemCategories.status} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as RentalItemCategoryStatus);
                  filterConditions.push(
                    inArray(rentalItemCategories.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(rentalItemCategories.status, value));
                }
                break;
            }
          } else if (fieldId === 'slug') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(rentalItemCategories.slug, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(rentalItemCategories.slug, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(rentalItemCategories.slug, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${rentalItemCategories.slug} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${rentalItemCategories.slug} IS NULL OR ${rentalItemCategories.slug} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${rentalItemCategories.slug} IS NOT NULL AND ${rentalItemCategories.slug} != ''`,
                );
                break;
            }
          } else if (fieldId === 'shortCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(rentalItemCategories.shortCode, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(rentalItemCategories.shortCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(
                  eq(rentalItemCategories.shortCode, value),
                );
                break;
              case 'ne':
                filterConditions.push(
                  sql`${rentalItemCategories.shortCode} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${rentalItemCategories.shortCode} IS NULL OR ${rentalItemCategories.shortCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${rentalItemCategories.shortCode} IS NOT NULL AND ${rentalItemCategories.shortCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'availableOnline') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(
                      eq(rentalItemCategories.availableOnline, true),
                    );
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(rentalItemCategories.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(rentalItemCategories.availableOnline, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(
                  eq(rentalItemCategories.availableOnline, !boolValue),
                );
                break;
              }
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const availableOnlineValues = decodedValue
                    .split(',')
                    .map((s) => s.trim());
                  const boolValues = availableOnlineValues.map(
                    (s) => s.toLowerCase() === 'true',
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(
                      eq(rentalItemCategories.availableOnline, true),
                    );
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(rentalItemCategories.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(rentalItemCategories.availableOnline, boolValue),
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions with proper indexing strategy
    // Default sort: position ascending, then by ID for consistent pagination when positions are equal
    // This leverages the composite index for optimal performance
    let orderBy = [
      asc(rentalItemCategories.position),
      asc(rentalItemCategories.id),
    ];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              // Use name index for sorting
              orderBy = [
                isDesc
                  ? desc(rentalItemCategories.name)
                  : asc(rentalItemCategories.name),
                asc(rentalItemCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'position':
              // Optimized position sorting with secondary sort for pagination consistency
              orderBy = [
                isDesc
                  ? desc(rentalItemCategories.position)
                  : asc(rentalItemCategories.position),
                asc(rentalItemCategories.id), // Use ID for consistent pagination when positions are equal
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(rentalItemCategories.createdAt)
                  : asc(rentalItemCategories.createdAt),
                asc(rentalItemCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc
                  ? desc(rentalItemCategories.updatedAt)
                  : asc(rentalItemCategories.updatedAt),
                asc(rentalItemCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'productsCount':
            case 'subcategoriesCount':
              // These will be handled with post-query sorting since they are calculated fields
              // Use position as default to maintain performance
              orderBy = [
                asc(rentalItemCategories.position),
                asc(rentalItemCategories.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Check if we need to sort by calculated fields
    const needsPostQuerySort =
      sort &&
      (() => {
        try {
          const parsedSort = JSON.parse(sort);
          return (
            parsedSort.length > 0 &&
            (parsedSort[0].id === 'productsCount' ||
              parsedSort[0].id === 'subcategoriesCount')
          );
        } catch {
          return false;
        }
      })();

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: rentalItemCategories.id,
        name: rentalItemCategories.name,
        shortCode: rentalItemCategories.shortCode,
        slug: rentalItemCategories.slug,
        status: rentalItemCategories.status,
        availableOnline: rentalItemCategories.availableOnline,
        position: rentalItemCategories.position,
        parentId: rentalItemCategories.parentId,
        color: rentalItemCategories.color,
        isAllocatedToAllLocations:
          rentalItemCategories.isAllocatedToAllLocations,
        imageId: rentalItemCategories.image,
        imagePublicUrl: media.publicUrl,
        imageFileName: media.fileName,
        createdAt: rentalItemCategories.createdAt,
        updatedAt: rentalItemCategories.updatedAt,
      })
      .from(rentalItemCategories)
      .leftJoin(media, eq(rentalItemCategories.image, media.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(needsPostQuerySort ? undefined : limit)
      .offset(needsPostQuerySort ? undefined : offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(rentalItemCategories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get parent names and subcategories count
    const categoryIds = result.map((cat) => cat.id);

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: rentalItemCategories.id,
        parentId: rentalItemCategories.parentId,
        parentName: sql<string>`parent_cat.name`.as('parentName'),
      })
      .from(rentalItemCategories)
      .leftJoin(
        sql`${rentalItemCategories} parent_cat`,
        sql`${rentalItemCategories.parentId} = parent_cat.id`,
      )
      .where(
        and(
          inArray(rentalItemCategories.id, categoryIds),
          eq(rentalItemCategories.isDeleted, false),
        ),
      );

    // Get subcategories count for each category
    const subcategoriesCountQuery = await this.db
      .select({
        parentId: rentalItemCategories.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(rentalItemCategories)
      .where(
        and(
          inArray(rentalItemCategories.parentId, categoryIds),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .groupBy(rentalItemCategories.parentId);

    // Get products count for each category
    const productsCountMap = await this.getProductsCountForRentalItemCategories(
      categoryIds,
      businessId,
    );

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subcategoriesCountMap = new Map(
      subcategoriesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    // Generate signed URLs for images and build final data
    let data = await Promise.all(
      result.map(async (rentalItemCategory) => {
        let imageUrl: string | undefined;

        if (rentalItemCategory.imageFileName) {
          try {
            // Generate signed URL with 60 minutes expiration for the image
            imageUrl = await this.gcsUploadService.generateSignedUrl(
              rentalItemCategory.imageFileName,
              'rental-item-categories', // folder where rental item category images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for rental item category ${rentalItemCategory.id} image:`,
              error.message,
            );
            // Fallback to public URL if signed URL generation fails
            imageUrl = rentalItemCategory.imagePublicUrl || undefined;
          }
        }

        return {
          id: rentalItemCategory.id.toString(),
          name: rentalItemCategory.name,
          shortCode: rentalItemCategory.shortCode,
          slug: rentalItemCategory.slug,
          status: rentalItemCategory.status,
          availableOnline: rentalItemCategory.availableOnline,
          parentId: rentalItemCategory.parentId?.toString(),
          parentName: parentInfoMap.get(rentalItemCategory.id) || undefined,
          color: rentalItemCategory.color,
          isAllocatedToAllLocations:
            rentalItemCategory.isAllocatedToAllLocations,
          subcategoriesCount:
            subcategoriesCountMap.get(rentalItemCategory.id) || 0,
          productsCount: productsCountMap.get(rentalItemCategory.id) || 0,
          image: imageUrl,
        };
      }),
    );

    // Handle post-query sorting for calculated fields
    if (needsPostQuerySort && sort) {
      try {
        const parsedSort = JSON.parse(sort);
        const sortField = parsedSort[0];
        const isDesc = sortField.desc === true;

        if (sortField.id === 'productsCount') {
          data.sort((a, b) => {
            const diff = a.productsCount - b.productsCount;
            return isDesc ? -diff : diff;
          });
        } else if (sortField.id === 'subcategoriesCount') {
          data.sort((a, b) => {
            const diff = a.subcategoriesCount - b.subcategoriesCount;
            return isDesc ? -diff : diff;
          });
        }

        // Apply pagination after sorting
        data = data.slice(offset, offset + limit);
      } catch {
        // Invalid JSON, use unsorted data with pagination
        data = data.slice(offset, offset + limit);
      }
    }

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a category with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingCategory = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.businessId, businessId),
          ilike(rentalItemCategories.name, name),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCategory };
  }

  async checkSlugAvailability(
    businessId: string | null,
    slug: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a category with the same slug already exists for this business
    const existingCategory = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.businessId, businessId),
          eq(rentalItemCategories.slug, slug),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCategory };
  }

  async checkShortCodeAvailability(
    businessId: string | null,
    shortCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a category with the same shortCode already exists for this business
    const existingCategory = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.businessId, businessId),
          eq(rentalItemCategories.shortCode, shortCode),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCategory };
  }

  async findOne(userId: string, id: string): Promise<RentalItemCategoryDto> {
    // Get the rental item category
    const rentalItemCategory = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.id, id),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!rentalItemCategory) {
      throw new NotFoundException(
        `Rental item category with ID ${id} not found`,
      );
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== rentalItemCategory.businessId
    ) {
      throw new UnauthorizedException(
        'Access denied to this rental item category',
      );
    }

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return await this.mapToRentalItemCategoryDto(rentalItemCategory);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateRentalItemCategoryDto: UpdateRentalItemCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<RentalItemCategoryDto> {
    // Get the category
    const existingRentalItemCategory = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.id, id),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingRentalItemCategory) {
      throw new NotFoundException(
        `Rental item category with ID ${id} not found`,
      );
    }

    // Verify business ownership
    if (businessId !== existingRentalItemCategory.businessId) {
      throw new UnauthorizedException('Access denied to update this category');
    }

    // Check for name conflict if name is being updated
    if (
      updateRentalItemCategoryDto.name &&
      updateRentalItemCategoryDto.name !== existingRentalItemCategory.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            ilike(rentalItemCategories.name, updateRentalItemCategoryDto.name),
            eq(rentalItemCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A rental item category with the name '${updateRentalItemCategoryDto.name}' already exists for this business`,
        );
      }
    }

    // Handle parent category validation if being updated
    if (updateRentalItemCategoryDto.parentId) {
      // Prevent category from being its own parent
      if (updateRentalItemCategoryDto.parentId === id) {
        throw new BadRequestException('Category cannot be its own parent');
      }

      const parentCategory = await this.db
        .select()
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.id, updateRentalItemCategoryDto.parentId),
            eq(
              rentalItemCategories.businessId,
              existingRentalItemCategory.businessId,
            ),
            eq(rentalItemCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!parentCategory) {
        throw new BadRequestException('Parent rental item category not found');
      }
    }

    let mediaId = existingRentalItemCategory.image;
    let ogImageId = existingRentalItemCategory.ogImage;

    // Handle image update if provided
    if (imageFile) {
      mediaId = await this.mediaService.updateMediaReference(
        existingRentalItemCategory.image,
        imageFile,
        'rental-item-categories',
        businessId,
        userId,
      );
    }

    // Handle OG image update if provided
    if (ogImageFile) {
      ogImageId = await this.mediaService.updateMediaReference(
        existingRentalItemCategory.ogImage,
        ogImageFile,
        'rental-item-categories/og-images',
        businessId,
        userId,
      );
    }

    // Update slug if name is changing and slug not explicitly provided
    if (
      updateRentalItemCategoryDto.name &&
      !updateRentalItemCategoryDto.slug &&
      updateRentalItemCategoryDto.name !== existingRentalItemCategory.name
    ) {
      updateRentalItemCategoryDto.slug = updateRentalItemCategoryDto.name
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '');
    }

    // Check for slug conflict if slug is being updated
    if (
      updateRentalItemCategoryDto.slug &&
      updateRentalItemCategoryDto.slug !== existingRentalItemCategory.slug
    ) {
      const slugConflict = await this.db
        .select()
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            eq(rentalItemCategories.slug, updateRentalItemCategoryDto.slug),
            eq(rentalItemCategories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (slugConflict) {
        throw new ConflictException(
          `A rental item category with the slug '${updateRentalItemCategoryDto.slug}' already exists for this business`,
        );
      }
    }

    // Check for shortCode conflict if shortCode is being updated
    if (
      updateRentalItemCategoryDto.shortCode !== undefined &&
      updateRentalItemCategoryDto.shortCode !==
        existingRentalItemCategory.shortCode
    ) {
      if (updateRentalItemCategoryDto.shortCode) {
        const shortCodeConflict = await this.db
          .select()
          .from(rentalItemCategories)
          .where(
            and(
              eq(rentalItemCategories.businessId, businessId),
              eq(
                rentalItemCategories.shortCode,
                updateRentalItemCategoryDto.shortCode,
              ),
              eq(rentalItemCategories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (shortCodeConflict) {
          throw new ConflictException(
            `A rental item category with the short code '${updateRentalItemCategoryDto.shortCode}' already exists for this business`,
          );
        }
      }
    }

    try {
      // Update the category and corresponding asset-category in a transaction
      const updatedRentalItemCategory = await this.db.transaction(
        async (tx) => {
          // Update the rental-item-category
          const [updated] = await tx
            .update(rentalItemCategories)
            .set({
              ...updateRentalItemCategoryDto,
              image: mediaId,
              ogImage: ogImageId,
              updatedAt: new Date(),
            })
            .where(eq(rentalItemCategories.id, id))
            .returning();

          // Update the corresponding asset-category if it exists
          if (updated.assetCategoryId) {
            await tx
              .update(assetCategories)
              .set({
                name: updateRentalItemCategoryDto.name || updated.name,
                description:
                  updateRentalItemCategoryDto.description !== undefined
                    ? updateRentalItemCategoryDto.description
                    : updated.description,
                updatedAt: new Date(),
              })
              .where(eq(assetCategories.id, updated.assetCategoryId));
          }

          return updated;
        },
      );

      // Handle location associations if provided
      if (
        updateRentalItemCategoryDto.isAllocatedToAllLocations !== undefined ||
        updateRentalItemCategoryDto.locationIds !== undefined
      ) {
        await this.manageRentalItemCategoryLocations(
          updatedRentalItemCategory.id,
          businessId,
          updateRentalItemCategoryDto.isAllocatedToAllLocations ??
            existingRentalItemCategory.isAllocatedToAllLocations,
          updateRentalItemCategoryDto.locationIds,
          userId,
        );
      }

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.RENTAL_ITEM_CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToRentalItemCategoryDto(updatedRentalItemCategory);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update rental item category: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the category
    const existingRentalItemCategory = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.id, id),
          eq(rentalItemCategories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingRentalItemCategory) {
      throw new NotFoundException(
        `Rental item category with ID ${id} not found`,
      );
    }

    if (businessId !== existingRentalItemCategory.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this rental item category',
      );
    }

    // Check for child categories
    const childRentalItemCategories = await this.db
      .select()
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.parentId, id),
          eq(rentalItemCategories.isDeleted, false),
        ),
      );

    if (childRentalItemCategories.length > 0) {
      throw new BadRequestException(
        'Cannot delete rental item category with child rental item categorys. Please remove or reassign child rental item categorys first.',
      );
    }

    // Soft delete the category and corresponding asset-category in a transaction
    await this.db.transaction(async (tx) => {
      // Soft delete the rental-item-category
      await tx
        .update(rentalItemCategories)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(rentalItemCategories.id, id));

      // Soft delete the corresponding asset-category if it exists
      if (existingRentalItemCategory.assetCategoryId) {
        await tx
          .update(assetCategories)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(
            eq(assetCategories.id, existingRentalItemCategory.assetCategoryId),
          );
      }
    });

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.RENTAL_ITEM_CATEGORY,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Rental item category with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    rentalItemCategoryIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!rentalItemCategoryIds || rentalItemCategoryIds.length === 0) {
        throw new BadRequestException(
          'No rental item category IDs provided for deletion',
        );
      }

      // Get all categories that exist and belong to the business
      const existingCategories = await this.db
        .select({
          id: rentalItemCategories.id,
          name: rentalItemCategories.name,
          businessId: rentalItemCategories.businessId,
        })
        .from(rentalItemCategories)
        .where(
          and(
            inArray(rentalItemCategories.id, rentalItemCategoryIds),
            eq(rentalItemCategories.businessId, businessId),
            eq(rentalItemCategories.isDeleted, false),
          ),
        );

      if (existingCategories.length === 0) {
        throw new NotFoundException(
          'No valid rental item categorys found for deletion',
        );
      }

      // Check if any of the found categories have child categories
      const categoriesWithChildren = await this.db
        .select({
          parentId: rentalItemCategories.parentId,
          childName: rentalItemCategories.name,
        })
        .from(rentalItemCategories)
        .where(
          and(
            inArray(
              rentalItemCategories.parentId,
              existingCategories.map((c) => c.id),
            ),
            eq(rentalItemCategories.isDeleted, false),
          ),
        );

      if (categoriesWithChildren.length > 0) {
        const parentIds = [
          ...new Set(categoriesWithChildren.map((c) => c.parentId)),
        ];
        const parentNames = existingCategories
          .filter((c) => parentIds.includes(c.id))
          .map((c) => c.name);

        throw new BadRequestException(
          `Cannot delete categories with child categories: ${parentNames.join(', ')}. Please remove or reassign child categories first.`,
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const category of existingCategories) {
          // Soft delete the category
          await tx
            .update(rentalItemCategories)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(rentalItemCategories.id, category.id));

          deletedIds.push(category.id);
        }
      });

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.RENTAL_ITEM_CATEGORY,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { rentalItemCategoryIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} categories`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete categories: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<RentalItemCategorySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all categories with only essential fields
    const categoryResults = await this.db
      .select({
        id: rentalItemCategories.id,
        name: rentalItemCategories.name,
        position: rentalItemCategories.position,
      })
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.isDeleted, false),
          eq(rentalItemCategories.status, RentalItemCategoryStatus.ACTIVE),
          eq(rentalItemCategories.businessId, businessId),
        ),
      )
      .orderBy(
        asc(rentalItemCategories.position),
        asc(rentalItemCategories.id),
      );

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return categoryResults.map((category) => ({
      id: category.id.toString(),
      name: category.name,
      position: category.position,
    }));
  }

  async findAllHierarchy(
    businessId: string | null,
  ): Promise<
    { id: string; name: string; parentId: string | null; position: number }[]
  > {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all categories with only the essential hierarchy fields
    const categoryResults = await this.db
      .select({
        id: rentalItemCategories.id,
        name: rentalItemCategories.name,
        parentId: rentalItemCategories.parentId,
        position: rentalItemCategories.position,
      })
      .from(rentalItemCategories)
      .where(
        and(
          eq(rentalItemCategories.isDeleted, false),
          eq(rentalItemCategories.status, RentalItemCategoryStatus.ACTIVE),
          eq(rentalItemCategories.businessId, businessId),
        ),
      )
      .orderBy(
        asc(rentalItemCategories.position),
        asc(rentalItemCategories.id),
      );

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return categoryResults.map((category) => ({
      id: category.id.toString(),
      name: category.name,
      parentId: category.parentId?.toString() || null,
      position: category.position,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createRentalItemCategoryDto: CreateRentalItemCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    const rentalItemCategory = await this.create(
      userId,
      businessId,
      createRentalItemCategoryDto,
      imageFile,
      ogImageFile,
    );
    return { id: rentalItemCategory.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createRentalItemCategoriesDto: CreateRentalItemCategoryDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<RentalItemCategoryDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createRentalItemCategoriesDto ||
        createRentalItemCategoriesDto.length === 0
      ) {
        throw new BadRequestException(
          'No rental item categorys provided for creation',
        );
      }

      // Validate that if images are provided, they don't exceed the number of categories
      if (
        imageFiles &&
        imageFiles.length > createRentalItemCategoriesDto.length
      ) {
        throw new BadRequestException(
          'Number of images cannot exceed number of rental item categorys',
        );
      }

      // Validate imageIndex values if provided
      if (imageFiles) {
        for (let i = 0; i < createRentalItemCategoriesDto.length; i++) {
          const rentalItemCategory = createRentalItemCategoriesDto[i];
          if (rentalItemCategory.imageIndex !== undefined) {
            if (rentalItemCategory.imageIndex < 0) {
              throw new BadRequestException(
                `Rental item category "${rentalItemCategory.name}" has invalid imageIndex: ${rentalItemCategory.imageIndex}. Must be 0 or greater.`,
              );
            }
            if (rentalItemCategory.imageIndex >= imageFiles.length) {
              throw new BadRequestException(
                `Rental item category "${rentalItemCategory.name}" has imageIndex ${rentalItemCategory.imageIndex} but only ${imageFiles.length} images provided.`,
              );
            }
          }
        }
      }

      // Generate slugs for categories that don't have them
      createRentalItemCategoriesDto.forEach((dto) => {
        if (!dto.slug) {
          dto.slug = dto.name
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '');
        }
      });

      // Check for duplicate names within the request
      const requestNames = createRentalItemCategoriesDto.map((dto) =>
        dto.name.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate category names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for duplicate slugs within the request
      const requestSlugs = createRentalItemCategoriesDto
        .map((dto) => dto.slug)
        .filter((slug): slug is string => Boolean(slug));
      const duplicateSlugs = requestSlugs.filter(
        (slug, index) => requestSlugs.indexOf(slug) !== index,
      );
      if (duplicateSlugs.length > 0) {
        throw new BadRequestException(
          `Duplicate category slugs found in request: ${duplicateSlugs.join(', ')}`,
        );
      }

      // Check for duplicate shortCodes within the request
      const requestShortCodes = createRentalItemCategoriesDto
        .map((dto) => dto.shortCode)
        .filter((shortCode): shortCode is string => Boolean(shortCode));
      const duplicateShortCodes = requestShortCodes.filter(
        (shortCode, index) => requestShortCodes.indexOf(shortCode) !== index,
      );
      if (duplicateShortCodes.length > 0) {
        throw new BadRequestException(
          `Duplicate category short codes found in request: ${duplicateShortCodes.join(', ')}`,
        );
      }

      // Check if any categories with the same names already exist for this business
      const existingCategories = await this.db
        .select()
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            sql`LOWER(${rentalItemCategories.name}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(rentalItemCategories.isDeleted, false),
          ),
        );

      if (existingCategories.length > 0) {
        const existingNames = existingCategories.map((c) => c.name);
        throw new ConflictException(
          `Categories with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      // Check if any categories with the same slugs already exist for this business
      if (requestSlugs.length > 0) {
        const existingSlugCategories = await this.db
          .select()
          .from(rentalItemCategories)
          .where(
            and(
              eq(rentalItemCategories.businessId, businessId),
              inArray(rentalItemCategories.slug, requestSlugs),
              eq(rentalItemCategories.isDeleted, false),
            ),
          );

        if (existingSlugCategories.length > 0) {
          const existingSlugs = existingSlugCategories.map((c) => c.slug);
          throw new ConflictException(
            `Categories with the following slugs already exist: ${existingSlugs.join(', ')}`,
          );
        }
      }

      // Check if any categories with the same shortCodes already exist for this business
      if (requestShortCodes.length > 0) {
        const existingShortCodeCategories = await this.db
          .select()
          .from(rentalItemCategories)
          .where(
            and(
              eq(rentalItemCategories.businessId, businessId),
              inArray(rentalItemCategories.shortCode, requestShortCodes),
              eq(rentalItemCategories.isDeleted, false),
            ),
          );

        if (existingShortCodeCategories.length > 0) {
          const existingShortCodes = existingShortCodeCategories.map(
            (c) => c.shortCode,
          );
          throw new ConflictException(
            `Categories with the following short codes already exist: ${existingShortCodes.join(', ')}`,
          );
        }
      }

      const createdRentalItemCategories: RentalItemCategoryDto[] = [];

      // Use a transaction to ensure all categories are created or none are
      await this.db.transaction(async (tx) => {
        // Group categories by parent to assign proper sequential positions
        const rentalItemCategoryGroups = new Map<
          string,
          CreateRentalItemCategoryDto[]
        >();

        for (const dto of createRentalItemCategoriesDto) {
          const parentKey = dto.parentId || 'root';
          if (!rentalItemCategoryGroups.has(parentKey)) {
            rentalItemCategoryGroups.set(parentKey, []);
          }
          rentalItemCategoryGroups.get(parentKey).push(dto);
        }

        // For each parent group, shift existing categories to make room for new ones at the beginning
        for (const [parentKey, groupCategories] of rentalItemCategoryGroups) {
          const parentId = parentKey === 'root' ? null : parentKey;

          // Shift existing categories down by the number of new categories being added
          await tx
            .update(rentalItemCategories)
            .set({
              position: sql`${rentalItemCategories.position} + ${groupCategories.length}`,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(rentalItemCategories.businessId, businessId),
                parentId
                  ? eq(rentalItemCategories.parentId, parentId)
                  : isNull(rentalItemCategories.parentId),
                eq(rentalItemCategories.isDeleted, false),
              ),
            );
        }

        for (let i = 0; i < createRentalItemCategoriesDto.length; i++) {
          const createRentalItemCategoryDto = createRentalItemCategoriesDto[i];
          const parentKey = createRentalItemCategoryDto.parentId || 'root';

          // Calculate position within the parent group (starting from position 1)
          const groupRentalItemCategories =
            rentalItemCategoryGroups.get(parentKey);
          const indexInGroup = groupRentalItemCategories.indexOf(
            createRentalItemCategoryDto,
          );
          const categoryPosition = indexInGroup + 1; // Start from position 1

          // Get image file based on imageIndex if specified, otherwise use array index
          let imageFile: Express.Multer.File | undefined;
          if (createRentalItemCategoryDto.imageIndex !== undefined) {
            // Use specific image index if provided
            imageFile = imageFiles?.[createRentalItemCategoryDto.imageIndex];
          } else {
            // Fallback to array index mapping for backward compatibility
            imageFile = imageFiles?.[i];
          }

          let mediaId: string | undefined;

          // Upload image if provided for this category
          if (imageFile) {
            const uploadedMedia = await this.mediaService.uploadMedia(
              imageFile,
              'categories',
              businessId,
              userId,
            );
            mediaId = uploadedMedia.id;
          }

          // Create the category with proper position (starting from position 1)
          const [newCategory] = await tx
            .insert(rentalItemCategories)
            .values({
              businessId,
              name: createRentalItemCategoryDto.name,
              shortCode: createRentalItemCategoryDto.shortCode,
              parentId: createRentalItemCategoryDto.parentId,
              description: createRentalItemCategoryDto.description,
              slug: createRentalItemCategoryDto.slug,
              availableOnline:
                createRentalItemCategoryDto.availableOnline ?? false,
              position: categoryPosition, // Position starts from 1 for new categories
              color: createRentalItemCategoryDto.color,
              image: mediaId,
              isAllocatedToAllLocations:
                createRentalItemCategoryDto.isAllocatedToAllLocations ?? false,
              seoTitle: createRentalItemCategoryDto.seoTitle,
              seoDescription: createRentalItemCategoryDto.seoDescription,
              seoKeywords: createRentalItemCategoryDto.seoKeywords,
              createdBy: userId,
              status:
                createRentalItemCategoryDto.status ??
                RentalItemCategoryStatus.ACTIVE,
            })
            .returning();

          // Automatically create corresponding asset-category
          const [assetCategory] = await tx
            .insert(assetCategories)
            .values({
              businessId,
              name: createRentalItemCategoryDto.name,
              description: createRentalItemCategoryDto.description,
              referenceId: newCategory.id,
              referenceType: AssetCategoryReferenceType.RENTAL_ITEM_CATEGORY,
              createdBy: userId,
              status: CategoryStatus.ACTIVE,
            })
            .returning();

          // Update the rental-item-category with the asset-category ID
          await tx
            .update(rentalItemCategories)
            .set({ assetCategoryId: assetCategory.id })
            .where(eq(rentalItemCategories.id, newCategory.id));

          // Handle location associations
          await this.manageRentalItemCategoryLocations(
            newCategory.id,
            businessId,
            createRentalItemCategoryDto.isAllocatedToAllLocations ?? false,
            createRentalItemCategoryDto.locationIds,
            userId,
          );

          createdRentalItemCategories.push(
            await this.mapToRentalItemCategoryDto(newCategory),
          );
        }
      });

      // Log bulk create operation
      const createdIds = createdRentalItemCategories.map((cat) => cat.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.RENTAL_ITEM_CATEGORY,
        createdIds,
        {
          names: createRentalItemCategoriesDto.map((dto) => dto.name),
          status: RentalItemCategoryStatus.ACTIVE,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createRentalItemCategoriesDto.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return createdRentalItemCategories;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create rental item categorys: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createRentalItemCategoriesDto: CreateRentalItemCategoryDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    const rentalItemCategories = await this.bulkCreate(
      userId,
      businessId,
      createRentalItemCategoriesDto,
      imageFiles,
    );
    return {
      ids: rentalItemCategories.map(
        (rentalItemCategory) => rentalItemCategory.id,
      ),
    };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateRentalItemCategoryDto: UpdateRentalItemCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateRentalItemCategoryDto,
      imageFile,
      ogImageFile,
    );
    return { id };
  }

  async updateRentalItemCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for rental item category ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all categories belong to the business
      const categoryIds = updates.map((update) => update.id);
      const existingCategories = await this.db
        .select({
          id: rentalItemCategories.id,
          parentId: rentalItemCategories.parentId,
          currentPosition: rentalItemCategories.position,
        })
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            inArray(rentalItemCategories.id, categoryIds),
            eq(rentalItemCategories.isDeleted, false),
          ),
        );

      if (existingCategories.length !== categoryIds.length) {
        const foundIds = existingCategories.map((cat) => cat.id);
        const missingIds = categoryIds.filter((id) => !foundIds.includes(id));
        throw new BadRequestException(
          `Rental item categorys not found or don't belong to this business: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdatePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        // Log bulk position update operation
        const updatedIds = updates.map((update) => update.id);
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.RENTAL_ITEM_CATEGORY,
          updatedIds,
          { positionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
            source: ActivitySource.WEB,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        // This is much more efficient than always normalizing
        await this.conditionalNormalizePositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update rental item category positions: ${error.message}`,
      );
    }
  }

  async bulkUpdateRentalItemCategoryHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      const failed: Array<{ id: string; error: string }> = [];
      let updatedCount = 0;

      // Validate that all categories belong to the business
      const categoryIds = updates.map((update) => update.id);
      const existingCategories = await this.db
        .select({
          id: rentalItemCategories.id,
          parentId: rentalItemCategories.parentId,
        })
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            inArray(rentalItemCategories.id, categoryIds),
            eq(rentalItemCategories.isDeleted, false),
          ),
        );

      const existingCategoryMap = new Map(
        existingCategories.map((cat) => [cat.id, cat]),
      );

      // Use a transaction to ensure data consistency
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          try {
            // Check if category exists
            if (!existingCategoryMap.has(update.id)) {
              failed.push({
                id: update.id,
                error:
                  'Rental item category not found or does not belong to this business',
              });
              continue;
            }

            // Check if parent exists (if parentId is not null)
            if (update.parentId !== null) {
              const parentExists = await tx
                .select({ id: rentalItemCategories.id })
                .from(rentalItemCategories)
                .where(
                  and(
                    eq(rentalItemCategories.id, update.parentId),
                    eq(rentalItemCategories.businessId, businessId),
                    eq(rentalItemCategories.isDeleted, false),
                  ),
                )
                .limit(1);

              if (parentExists.length === 0) {
                failed.push({
                  id: update.id,
                  error: 'Parent rental item category not found',
                });
                continue;
              }

              // Check for circular reference (rental item category cannot be its own ancestor)
              if (update.parentId === update.id) {
                failed.push({
                  id: update.id,
                  error: 'Rental item category cannot be its own parent',
                });
                continue;
              }

              // Check if this would create a circular reference
              const wouldCreateCircle = async (
                parentId: string,
                childId: string,
              ): Promise<boolean> => {
                const parent = await tx
                  .select({ parentId: rentalItemCategories.parentId })
                  .from(rentalItemCategories)
                  .where(
                    and(
                      eq(rentalItemCategories.id, parentId),
                      eq(rentalItemCategories.businessId, businessId),
                      eq(rentalItemCategories.isDeleted, false),
                    ),
                  )
                  .limit(1);

                if (parent.length === 0) return false;
                if (parent[0].parentId === childId) return true;
                if (parent[0].parentId)
                  return wouldCreateCircle(parent[0].parentId, childId);
                return false;
              };

              if (await wouldCreateCircle(update.parentId, update.id)) {
                failed.push({
                  id: update.id,
                  error: 'Would create circular reference',
                });
                continue;
              }
            }

            // Update the rental item category
            await tx
              .update(rentalItemCategories)
              .set({
                parentId: update.parentId,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(rentalItemCategories.id, update.id),
                  eq(rentalItemCategories.businessId, businessId),
                  eq(rentalItemCategories.isDeleted, false),
                ),
              );

            updatedCount++;

            // Log the hierarchy update activity
            await this.activityLogService.logUpdate(
              update.id,
              EntityType.RENTAL_ITEM_CATEGORY,
              userId,
              businessId,
              {
                source: ActivitySource.WEB,
              },
            );
          } catch (error) {
            failed.push({
              id: update.id,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return { updated: updatedCount, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update rental item category hierarchy: ${error.message}`,
      );
    }
  }

  async bulkUpdateRentalItemCategoryStatus(
    userId: string,
    businessId: string | null,
    rentalItemCategoryIds: string[],
    status: RentalItemCategoryStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ rentalItemCategoryId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!rentalItemCategoryIds || rentalItemCategoryIds.length === 0) {
        throw new BadRequestException(
          'No rental item category IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ rentalItemCategoryId: string; error: string }> = [];

      // Process each rental item category ID
      await this.db.transaction(async (tx) => {
        for (const rentalItemCategoryId of rentalItemCategoryIds) {
          try {
            // Check if rental item category exists and belongs to the business
            const existingRentalItemCategory = await tx
              .select()
              .from(rentalItemCategories)
              .where(
                and(
                  eq(rentalItemCategories.id, rentalItemCategoryId),
                  eq(rentalItemCategories.businessId, businessId),
                  eq(rentalItemCategories.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingRentalItemCategory) {
              failed.push({
                rentalItemCategoryId,
                error: 'Rental item category not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingRentalItemCategory.status === status) {
              failed.push({
                rentalItemCategoryId,
                error: `Rental item category already has status: ${status}`,
              });
              continue;
            }

            // Update the rental item category status
            await tx
              .update(rentalItemCategories)
              .set({
                status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(rentalItemCategories.id, rentalItemCategoryId),
                  eq(rentalItemCategories.businessId, businessId),
                  eq(rentalItemCategories.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(rentalItemCategoryId);

            // Log the status update activity
            await this.activityLogService.logUpdate(
              rentalItemCategoryId,
              EntityType.RENTAL_ITEM_CATEGORY,
              userId,
              businessId,
              {
                source: ActivitySource.WEB,
              },
            );
          } catch (error) {
            failed.push({
              rentalItemCategoryId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update rental item category status: ${error.message}`,
      );
    }
  }

  private async getProductsCountForRentalItemCategory(
    rentalItemCategoryId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            or(
              eq(rentalItems.categoryId, rentalItemCategoryId),
              eq(rentalItems.subCategoryId, rentalItemCategoryId),
            ),
            eq(rentalItems.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get products count for rental item category ${rentalItemCategoryId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getProductsCountForRentalItemCategories(
    rentalItemCategoryIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (rentalItemCategoryIds.length === 0) {
        return new Map();
      }

      // Count products where categoryId matches
      const categoryResults = await this.db
        .select({
          rentalItemCategoryId: rentalItems.categoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            inArray(rentalItems.categoryId, rentalItemCategoryIds),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .groupBy(rentalItems.categoryId);

      // Count products where subCategoryId matches
      const subCategoryResults = await this.db
        .select({
          subRentalItemCategoryId: rentalItems.subCategoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            inArray(rentalItems.subCategoryId, rentalItemCategoryIds),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .groupBy(rentalItems.subCategoryId);

      // Combine the counts
      const countsMap = new Map<string, number>();

      // Initialize all category IDs with 0
      rentalItemCategoryIds.forEach((id) => countsMap.set(id, 0));

      // Add category counts
      categoryResults.forEach((result) => {
        if (result.rentalItemCategoryId) {
          countsMap.set(
            result.rentalItemCategoryId,
            (countsMap.get(result.rentalItemCategoryId) || 0) + result.count,
          );
        }
      });

      // Add subcategory counts
      subCategoryResults.forEach((result) => {
        if (result.subRentalItemCategoryId) {
          countsMap.set(
            result.subRentalItemCategoryId,
            (countsMap.get(result.subRentalItemCategoryId) || 0) + result.count,
          );
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get products count for rental item categorys:',
        error.message,
      );
      return new Map();
    }
  }

  private async mapToRentalItemCategoryDto(
    rentalItemCategory: typeof rentalItemCategories.$inferSelect,
  ): Promise<RentalItemCategoryDto> {
    // Get products count for this rental item category
    const productsCount = await this.getProductsCountForRentalItemCategory(
      rentalItemCategory.id,
      rentalItemCategory.businessId,
    );

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      rentalItemCategory.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (rentalItemCategory.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        rentalItemCategory.updatedBy.toString(),
      );
    }

    // Get category locations if not allocated to all locations
    let locations: { id: string; name: string }[] = [];
    if (!rentalItemCategory.isAllocatedToAllLocations) {
      locations = await this.getRentalItemCategoryLocations(
        rentalItemCategory.id,
      );
    }

    const rentalItemCategoryDto: RentalItemCategoryDto = {
      id: rentalItemCategory.id.toString(),
      businessId: rentalItemCategory.businessId.toString(),
      name: rentalItemCategory.name,
      shortCode: rentalItemCategory.shortCode,
      parentId: rentalItemCategory.parentId?.toString(),
      description: rentalItemCategory.description,
      slug: rentalItemCategory.slug,
      availableOnline: rentalItemCategory.availableOnline,
      position: rentalItemCategory.position,
      color: rentalItemCategory.color,
      isAllocatedToAllLocations: rentalItemCategory.isAllocatedToAllLocations,
      locations,
      seoTitle: rentalItemCategory.seoTitle,
      seoDescription: rentalItemCategory.seoDescription,
      seoKeywords: rentalItemCategory.seoKeywords,
      createdBy: createdByName,
      updatedBy: updatedByName,
      status: rentalItemCategory.status,
      productsCount,
      createdAt: rentalItemCategory.createdAt,
      updatedAt: rentalItemCategory.updatedAt,
    };

    // Fetch media information and generate signed URL if image exists
    if (rentalItemCategory.image) {
      try {
        const mediaData = await this.mediaService.findById(
          rentalItemCategory.image,
          rentalItemCategory.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the image
        rentalItemCategoryDto.image =
          await this.gcsUploadService.generateSignedUrl(
            mediaData.fileName,
            'rental-item-categories', // folder where rental item category images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for rental item category ${rentalItemCategory.id} image:`,
          error.message,
        );
      }
    }

    // Fetch OG image information and generate signed URL if ogImage exists
    if (rentalItemCategory.ogImage) {
      try {
        const ogMediaData = await this.mediaService.findById(
          rentalItemCategory.ogImage,
          rentalItemCategory.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the OG image
        rentalItemCategoryDto.ogImage =
          await this.gcsUploadService.generateSignedUrl(
            ogMediaData.fileName,
            'rental-item-categories/og-images', // folder where OG images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for rental item category ${rentalItemCategory.id} OG image:`,
          error.message,
        );
      }
    }

    return rentalItemCategoryDto;
  }

  /**
   * Conditionally normalize positions only when needed
   * This is much more efficient than always normalizing
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizePositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          parentId: rentalItemCategories.parentId,
          positions: sql<
            number[]
          >`array_agg(${rentalItemCategories.position} ORDER BY ${rentalItemCategories.position})`,
          count: sql<number>`count(*)`,
        })
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            eq(rentalItemCategories.isDeleted, false),
          ),
        )
        .groupBy(rentalItemCategories.parentId);

      const needsNormalization = positionCheck.some(
        (group: {
          parentId: string | null;
          positions: number[];
          count: number;
        }) => {
          const positions = group.positions;
          const expectedPositions = Array.from(
            { length: group.count },
            (_, i) => i + 1,
          );
          return (
            JSON.stringify(positions) !== JSON.stringify(expectedPositions)
          );
        },
      );

      if (needsNormalization) {
        await this.normalizePositions(tx, businessId);
      }
    } catch (error) {
      console.warn('Failed to check position normalization:', error.message);
      // Fallback to full normalization if check fails
      await this.normalizePositions(tx, businessId);
    }
  }

  /**
   * Normalize positions to ensure sequential ordering without gaps
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async normalizePositions(tx: any, businessId: string): Promise<void> {
    try {
      // Get all categories ordered by current position and createdAt
      const allCategories = await tx
        .select({
          id: rentalItemCategories.id,
          parentId: rentalItemCategories.parentId,
          position: rentalItemCategories.position,
        })
        .from(rentalItemCategories)
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            eq(rentalItemCategories.isDeleted, false),
          ),
        )
        .orderBy(
          asc(rentalItemCategories.position),
          asc(rentalItemCategories.createdAt),
        );

      // Group categories by parent level
      const categoryGroups = new Map<string, typeof allCategories>();

      for (const category of allCategories) {
        const parentKey = category.parentId || 'root';
        if (!categoryGroups.has(parentKey)) {
          categoryGroups.set(parentKey, []);
        }
        categoryGroups.get(parentKey)!.push(category);
      }

      // Batch normalize positions for each group using optimized queries
      for (const [, groupCategories] of categoryGroups) {
        const updates = groupCategories
          .map((category: { id: string; position: number }, index: number) => ({
            id: category.id,
            position: index + 1,
          }))
          .filter(
            (update: { id: string; position: number }, index: number) =>
              groupCategories[index].position !== update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdatePositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn('Failed to normalize positions:', error.message);
      // Don't throw error as this is a cleanup operation
    }
  }

  /**
   * Optimized reorder positions when inserting a category at a specific position
   * Uses batch updates and optimized queries for better performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param parentId - The parent category ID
   * @param insertPosition - The position where the new category will be inserted
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    parentId: string | null,
    insertPosition: number,
  ): Promise<void> {
    try {
      // Use a single optimized query with proper indexing
      // This query will be much faster with the composite index on (businessId, parentId, position, deletedAt)
      await tx
        .update(rentalItemCategories)
        .set({
          position: sql`${rentalItemCategories.position} + 1`,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(rentalItemCategories.businessId, businessId),
            parentId
              ? eq(rentalItemCategories.parentId, parentId)
              : isNull(rentalItemCategories.parentId),
            gte(rentalItemCategories.position, insertPosition),
            eq(rentalItemCategories.isDeleted, false),
          ),
        );
    } catch (error) {
      console.warn('Failed to reorder positions:', error.message);
      throw error; // This is critical for data consistency
    }
  }

  /**
   * Optimized batch position update method
   * Reduces the number of database operations and improves performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdatePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(rentalItemCategories)
            .set({
              position: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(rentalItemCategories.id, update.id),
                eq(rentalItemCategories.businessId, businessId),
                eq(rentalItemCategories.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      // This approach is more reliable than raw SQL with arrays
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(rentalItemCategories)
          .set({
            position: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(rentalItemCategories.id, update.id),
              eq(rentalItemCategories.businessId, businessId),
              eq(rentalItemCategories.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update positions:', error.message);
      throw error;
    }
  }

  /**
   * Validate location IDs belong to the business
   */
  private async validateLocationIds(
    businessId: string,
    locationIds: string[],
  ): Promise<void> {
    if (!locationIds || locationIds.length === 0) {
      return;
    }

    const validLocations = await this.db
      .select({ id: locations.id })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          inArray(locations.id, locationIds),
          eq(locations.isDeleted, false),
        ),
      );

    if (validLocations.length !== locationIds.length) {
      throw new BadRequestException(
        'Some location IDs do not exist or belong to this business',
      );
    }
  }

  /**
   * Manage rental item category location associations
   */
  private async manageRentalItemCategoryLocations(
    rentalItemCategoryId: string,
    businessId: string,
    isAllocatedToAllLocations: boolean,
    locationIds: string[] | undefined,
    userId: string,
  ): Promise<void> {
    // Delete existing location associations
    await this.db
      .delete(rentalItemCategoryLocations)
      .where(
        eq(
          rentalItemCategoryLocations.rentalItemCategoryId,
          rentalItemCategoryId,
        ),
      );

    // If allocated to all locations, no need to create specific associations
    if (isAllocatedToAllLocations) {
      return;
    }

    // If specific locations are provided, create associations
    if (locationIds && locationIds.length > 0) {
      await this.validateLocationIds(businessId, locationIds);

      const locationAssociations = locationIds.map((locationId) => ({
        rentalItemCategoryId,
        locationId,
        businessId,
        createdBy: userId,
      }));

      await this.db
        .insert(rentalItemCategoryLocations)
        .values(locationAssociations);
    }
  }

  /**
   * Get rental item category location associations
   */
  private async getRentalItemCategoryLocations(
    rentalItemCategoryId: string,
  ): Promise<
    {
      id: string;
      name: string;
    }[]
  > {
    const rentalItemCategoryLocationData = await this.db
      .select({
        id: locations.id,
        name: locations.name,
      })
      .from(rentalItemCategoryLocations)
      .innerJoin(
        locations,
        eq(rentalItemCategoryLocations.locationId, locations.id),
      )
      .where(
        and(
          eq(
            rentalItemCategoryLocations.rentalItemCategoryId,
            rentalItemCategoryId,
          ),
          eq(locations.isDeleted, false),
        ),
      )
      .orderBy(locations.name);

    return rentalItemCategoryLocationData;
  }
}
