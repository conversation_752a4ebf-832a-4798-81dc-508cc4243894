import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { RentalItemCategoriesService } from './rental-item-categories.service';
import { CreateRentalItemCategoryDto } from './dto/create-rental-item-categories.dto';
import { UpdateRentalItemCategoryDto } from './dto/update-rental-item-categories.dto';
import { RentalItemCategoryDto } from './dto/rental-item-categories.dto';
import { RentalItemCategorySlimDto } from './dto/rental-item-categories-slim.dto';
import { RentalItemCategoryIdResponseDto } from './dto/rental-item-categories-id-response.dto';
import { BulkRentalItemCategoryIdsResponseDto } from './dto/bulk-rental-rental-item-categories-response.dto';
import { BulkCreateRentalItemCategoryDto } from './dto/bulk-create-rental-item-categories.dto';
import { DeleteRentalItemCategoryResponseDto } from './dto/delete-rental-item-categories-response.dto';
import { BulkDeleteRentalItemCategoryDto } from './dto/bulk-delete-rental-item-categories.dto';
import { BulkDeleteRentalItemCategoryResponseDto } from './dto/bulk-delete-rental-item-categories-response.dto';
import { PaginatedRentalItemCategoriesResponseDto } from './dto/paginated-rental-item-categories-response.dto';
import {
  UpdateRentalItemCategoryPositionsDto,
  UpdateRentalItemCategoryPositionsResponseDto,
} from './dto/update-rental-item-categories-positions.dto';
import {
  BulkUpdateRentalItemCategoryHierarchyDto,
  BulkUpdateRentalItemCategoryHierarchyResponseDto,
} from './dto/bulk-update-rental-item-categories-hierarchy.dto';
import {
  BulkUpdateRentalItemCategoryStatusDto,
  BulkUpdateRentalItemCategoryStatusResponseDto,
} from './dto/bulk-update-rental-item-categories-status.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { CheckRentalItemCategoryNameAvailabilityResponseDto } from './dto/check-rental-item-categories-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('categories')
@Controller('categories')
@UseGuards(PermissionsGuard)
export class RentalItemsCategoriesController {
  constructor(
    private readonly rentalItemCategoriesService: RentalItemCategoriesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Create a new category with optional images' })
  @ApiBody({
    description: 'Category creation with optional image and ogImage upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Electronics',
          description: 'Category name',
        },
        shortCode: {
          type: 'string',
          example: 'ELEC',
          description: 'Short code for the category',
        },
        parentId: {
          type: 'string',
          format: 'uuid',
          description: 'Parent category ID',
        },
        description: {
          type: 'string',
          example: 'Electronic devices and gadgets',
          description: 'Category description',
        },
        slug: {
          type: 'string',
          example: 'electronics',
          description: 'URL-friendly version of the category name',
        },
        availableOnline: {
          type: 'boolean',
          example: true,
          description: 'Whether the category is available online',
          default: false,
        },
        color: {
          type: 'string',
          example: '#3B82F6',
          description: 'Category color in hex format',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive', 'deleted'],
          description: 'Category status',
          default: 'active',
        },
        locationIds: {
          type: 'array',
          items: {
            type: 'string',
            format: 'uuid',
          },
          description: 'Array of location IDs where this category is available',
          example: [
            '123e4567-e89b-12d3-a456-************',
            '123e4567-e89b-12d3-a456-************',
          ],
        },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Category image file',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The category has been successfully created',
    type: RentalItemCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  create(
    @Request() req,
    @Body() createRentalItemCategoryDto: CreateRentalItemCategoryDto,
    @UploadedFiles()
    files?: { image?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
  ): Promise<RentalItemCategoryIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.rentalItemCategoriesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createRentalItemCategoryDto,
      image,
      ogImage,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Bulk create categories with optional images' })
  @ApiBody({
    description:
      'Bulk category creation with optional image uploads. Images can be mapped to categories using field names like "image_0", "image_1", etc., or by using the categories array with imageIndex property.',
    schema: {
      type: 'object',
      properties: {
        categories: {
          type: 'string',
          description:
            'JSON string containing array of category objects. Each category can optionally include an "imageIndex" property to specify which image file to use.',
          example:
            '[{"name":"Electronics","availableOnline":true,"imageIndex":0},{"name":"Books","availableOnline":false},{"name":"Sports","availableOnline":true,"imageIndex":1}]',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Array of category image files (optional). Images are mapped to categories using the imageIndex property in the categories array.',
        },
      },
      required: ['categories'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The categories have been successfully created',
    type: BulkRentalItemCategoryIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data, duplicate names, or files',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Category names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateRentalItemCategoryDto: BulkCreateRentalItemCategoryDto,
    @UploadedFiles() images?: Express.Multer.File[],
  ): Promise<BulkRentalItemCategoryIdsResponseDto> {
    return this.rentalItemCategoriesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateRentalItemCategoryDto.rentalItemCategories,
      images,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({
    summary: 'Get all categories for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by category name or short code',
    required: false,
    type: String,
    example: 'Electronics',
  })
  @ApiQuery({
    name: 'slug',
    description: 'Filter by category slug',
    required: false,
    type: String,
    example: 'electronics',
  })
  @ApiQuery({
    name: 'shortCode',
    description: 'Filter by category short code',
    required: false,
    type: String,
    example: 'ELEC',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    required: false,
    type: String,
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'availableOnline',
    description:
      'Filter by availableOnline (comma-separated for multiple values). Supports URL encoding: availableOnline=true%2Cfalse',
    required: false,
    type: String,
    example: 'true,false',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"name","value":"Electronics","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"},{"id":"availableOnline","value":"true","operator":"eq","type":"select","rowId":"3"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: name, position, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"position","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active categories for the user's active business with pagination (optimized with only essential fields including products count)",
    type: PaginatedRentalItemCategoriesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('slug') slug?: string,
    @Query('shortCode') shortCode?: string,
    @Query('status') status?: string,
    @Query('availableOnline') availableOnline?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedRentalItemCategoriesResponseDto> {
    return this.rentalItemCategoriesService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      slug,
      shortCode,
      status,
      availableOnline,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Check if a category name is available' })
  @ApiQuery({
    name: 'name',
    description: 'Category name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the category name is available',
    type: CheckRentalItemCategoryNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.rentalItemCategoriesService.checkNameAvailability(
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('check-slug-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Check if a category slug is available' })
  @ApiQuery({
    name: 'slug',
    required: true,
    description: 'The slug to check for availability',
    example: 'electronics',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns slug availability',
    type: CheckRentalItemCategoryNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkSlugAvailability(
    @Request() req,
    @Query('slug') slug: string,
  ): Promise<{ available: boolean }> {
    return this.rentalItemCategoriesService.checkSlugAvailability(
      req.user.activeBusinessId,
      slug,
    );
  }

  @Get('check-short-code-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Check if a category short code is available' })
  @ApiQuery({
    name: 'shortCode',
    required: true,
    description: 'The short code to check for availability',
    example: 'ELEC',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns short code availability',
    type: CheckRentalItemCategoryNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkShortCodeAvailability(
    @Request() req,
    @Query('shortCode') shortCode: string,
  ): Promise<{ available: boolean }> {
    return this.rentalItemCategoriesService.checkShortCodeAvailability(
      req.user.activeBusinessId,
      shortCode,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Get all categories in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All categories returned successfully',
    type: [RentalItemCategorySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<RentalItemCategorySlimDto[]> {
    return this.rentalItemCategoriesService.findAllSlim(
      req.user.activeBusinessId,
    );
  }

  @Get('hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Get all categories in hierarchy format' })
  @ApiResponse({
    status: 200,
    description: 'Categories hierarchy returned successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          name: { type: 'string' },
          parentId: { type: 'string', format: 'uuid', nullable: true },
          position: { type: 'number' },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllHierarchy(@Request() req) {
    return this.rentalItemCategoriesService.findAllHierarchy(
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Get a category by ID' })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the category with products count',
    type: RentalItemCategoryDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this category',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<RentalItemCategoryDto> {
    return this.rentalItemCategoriesService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update a category with optional images' })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
  })
  @ApiBody({
    description: 'Category update with optional image and ogImage upload',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Electronics',
          description: 'Category name',
        },
        shortCode: {
          type: 'string',
          example: 'ELEC',
          description: 'Short code for the category',
        },
        parentId: {
          type: 'string',
          format: 'uuid',
          description: 'Parent category ID',
        },
        description: {
          type: 'string',
          example: 'Electronic devices and gadgets',
          description: 'Category description',
        },
        slug: {
          type: 'string',
          example: 'electronics',
          description: 'URL-friendly version of the category name',
        },
        availableOnline: {
          type: 'boolean',
          example: true,
          description: 'Whether the category is available online',
        },
        color: {
          type: 'string',
          example: '#3B82F6',
          description: 'Category color in hex format',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive', 'deleted'],
          description: 'Category status',
        },
        locationIds: {
          type: 'array',
          items: {
            type: 'string',
            format: 'uuid',
          },
          description: 'Array of location IDs where this category is available',
          example: [
            '123e4567-e89b-12d3-a456-************',
            '123e4567-e89b-12d3-a456-************',
          ],
        },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Category image file',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'The category has been successfully updated',
    type: RentalItemCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this category',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateRentalItemCategoryDto: UpdateRentalItemCategoryDto,
    @UploadedFiles()
    files?: { image?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
  ): Promise<RentalItemCategoryIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.rentalItemCategoriesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateRentalItemCategoryDto,
      image,
      ogImage,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_DELETE)
  @ApiOperation({ summary: 'Bulk delete categories' })
  @ApiBody({
    description: 'Array of category IDs to delete',
    type: BulkDeleteRentalItemCategoryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Categories have been successfully deleted',
    type: BulkDeleteRentalItemCategoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more categories not found',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteRentalItemCategoryDto: BulkDeleteRentalItemCategoryDto,
  ): Promise<BulkDeleteRentalItemCategoryResponseDto> {
    return this.rentalItemCategoriesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteRentalItemCategoryDto.rentalItemCategoryIds,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_DELETE)
  @ApiOperation({ summary: 'Delete a category' })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The category has been successfully deleted',
    type: DeleteRentalItemCategoryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Category not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this category',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteRentalItemCategoryResponseDto> {
    return this.rentalItemCategoriesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('batch/positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Batch update category positions' })
  @ApiBody({
    description: 'Array of category position updates',
    type: UpdateRentalItemCategoryPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Category positions have been successfully updated',
    type: UpdateRentalItemCategoryPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updatePositions(
    @Request() req,
    @Body()
    updateRentalItemTypePositionsDto: UpdateRentalItemCategoryPositionsDto,
  ): Promise<UpdateRentalItemCategoryPositionsResponseDto> {
    const result =
      await this.rentalItemCategoriesService.updateRentalItemCategoryPositions(
        req.user.id,
        req.user.activeBusinessId,
        updateRentalItemTypePositionsDto.updates,
      );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} rental item category positions`,
    };
  }

  @Patch('batch/hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Batch update category hierarchy' })
  @ApiBody({
    description: 'Array of category hierarchy updates',
    type: BulkUpdateRentalItemCategoryHierarchyDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Rental item type hierarchy has been successfully updated',
    type: BulkUpdateRentalItemCategoryHierarchyResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateHierarchy(
    @Request() req,
    @Body() bulkUpdateHierarchyDto: BulkUpdateRentalItemCategoryHierarchyDto,
  ): Promise<BulkUpdateRentalItemCategoryHierarchyResponseDto> {
    return this.rentalItemCategoriesService.bulkUpdateRentalItemCategoryHierarchy(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateHierarchyDto.updates,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Bulk update category status' })
  @ApiBody({
    description: 'Array of category IDs and status to update',
    type: BulkUpdateRentalItemCategoryStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Category status has been successfully updated',
    type: BulkUpdateRentalItemCategoryStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateRentalItemCategoryStatusDto,
  ): Promise<BulkUpdateRentalItemCategoryStatusResponseDto> {
    const result =
      await this.rentalItemCategoriesService.bulkUpdateRentalItemCategoryStatus(
        req.user.id,
        req.user.activeBusinessId,
        bulkUpdateStatusDto.rentalItemCategoryIds,
        bulkUpdateStatusDto.status,
      );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} rental item categorys`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }
}
