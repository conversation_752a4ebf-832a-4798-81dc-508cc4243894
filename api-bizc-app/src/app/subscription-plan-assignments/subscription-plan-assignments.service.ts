import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateSubscriptionPlanAssignmentDto } from './dto/create-subscription-plan-assignment.dto';
import { UpdateSubscriptionPlanAssignmentDto } from './dto/update-subscription-plan-assignment.dto';
import { SubscriptionPlanAssignmentDto } from './dto/subscription-plan-assignment.dto';
import {
  subscriptionPlans,
  subscriptionPlanCustomers,
  subscriptionPlanCustomerGroups,
} from '../drizzle/schema/subscription-plans.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { customerGroups } from '../drizzle/schema/customer-groups.schema';
import { users } from '../drizzle/schema/users.schema';
import { eq, and, sql, desc } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { UsersService } from '../users/users.service';

@Injectable()
export class SubscriptionPlanAssignmentsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async createCustomerAssignment(
    userId: string,
    businessId: string | null,
    createDto: CreateSubscriptionPlanAssignmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createDto.customerId) {
        throw new BadRequestException('Customer ID is required');
      }

      // Validate subscription plan exists and belongs to business
      const subscriptionPlan = await this.db
        .select()
        .from(subscriptionPlans)
        .where(
          and(
            eq(subscriptionPlans.id, createDto.subscriptionPlanId),
            eq(subscriptionPlans.businessId, businessId),
            eq(subscriptionPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!subscriptionPlan) {
        throw new NotFoundException('Subscription plan not found');
      }

      // Validate customer exists and belongs to business
      const customer = await this.db
        .select()
        .from(customers)
        .where(
          and(
            eq(customers.id, createDto.customerId),
            eq(customers.businessId, businessId),
            eq(customers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!customer) {
        throw new NotFoundException('Customer not found');
      }

      // Check if assignment already exists
      const existingAssignment = await this.db
        .select()
        .from(subscriptionPlanCustomers)
        .where(
          and(
            eq(
              subscriptionPlanCustomers.subscriptionPlanId,
              createDto.subscriptionPlanId,
            ),
            eq(subscriptionPlanCustomers.customerId, createDto.customerId),
            eq(subscriptionPlanCustomers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAssignment) {
        throw new ConflictException(
          'Customer is already assigned to this subscription plan',
        );
      }

      const [newAssignment] = await this.db
        .insert(subscriptionPlanCustomers)
        .values({
          subscriptionPlanId: createDto.subscriptionPlanId,
          customerId: createDto.customerId,
          notes: createDto.notes,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        newAssignment.id,
        EntityType.SUBSCRIPTION_PLAN_ASSIGNMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newAssignment.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create customer assignment: ${error.message}`,
      );
    }
  }

  async createCustomerGroupAssignment(
    userId: string,
    businessId: string | null,
    createDto: CreateSubscriptionPlanAssignmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createDto.customerGroupId) {
        throw new BadRequestException('Customer group ID is required');
      }

      // Validate subscription plan exists and belongs to business
      const subscriptionPlan = await this.db
        .select()
        .from(subscriptionPlans)
        .where(
          and(
            eq(subscriptionPlans.id, createDto.subscriptionPlanId),
            eq(subscriptionPlans.businessId, businessId),
            eq(subscriptionPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!subscriptionPlan) {
        throw new NotFoundException('Subscription plan not found');
      }

      // Validate customer group exists and belongs to business
      const customerGroup = await this.db
        .select()
        .from(customerGroups)
        .where(
          and(
            eq(customerGroups.id, createDto.customerGroupId),
            eq(customerGroups.businessId, businessId),
            eq(customerGroups.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!customerGroup) {
        throw new NotFoundException('Customer group not found');
      }

      // Check if assignment already exists
      const existingAssignment = await this.db
        .select()
        .from(subscriptionPlanCustomerGroups)
        .where(
          and(
            eq(
              subscriptionPlanCustomerGroups.subscriptionPlanId,
              createDto.subscriptionPlanId,
            ),
            eq(
              subscriptionPlanCustomerGroups.customerGroupId,
              createDto.customerGroupId,
            ),
            eq(subscriptionPlanCustomerGroups.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAssignment) {
        throw new ConflictException(
          'Customer group is already assigned to this subscription plan',
        );
      }

      const [newAssignment] = await this.db
        .insert(subscriptionPlanCustomerGroups)
        .values({
          subscriptionPlanId: createDto.subscriptionPlanId,
          customerGroupId: createDto.customerGroupId,
          notes: createDto.notes,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        newAssignment.id,
        EntityType.SUBSCRIPTION_PLAN_ASSIGNMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newAssignment.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create customer group assignment: ${error.message}`,
      );
    }
  }

  async findAllCustomerAssignments(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
  ): Promise<{
    data: SubscriptionPlanAssignmentDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const result = await this.db
      .select({
        id: subscriptionPlanCustomers.id,
        subscriptionPlanId: subscriptionPlanCustomers.subscriptionPlanId,
        subscriptionPlanName: subscriptionPlans.name,
        customerId: subscriptionPlanCustomers.customerId,
        customerName: customers.customerDisplayName,
        notes: subscriptionPlanCustomers.notes,
        createdBy: subscriptionPlanCustomers.createdBy,
        updatedBy: subscriptionPlanCustomers.updatedBy,
        createdAt: subscriptionPlanCustomers.createdAt,
        updatedAt: subscriptionPlanCustomers.updatedAt,
      })
      .from(subscriptionPlanCustomers)
      .innerJoin(
        subscriptionPlans,
        eq(subscriptionPlanCustomers.subscriptionPlanId, subscriptionPlans.id),
      )
      .innerJoin(
        customers,
        eq(subscriptionPlanCustomers.customerId, customers.id),
      )
      .where(
        and(
          eq(subscriptionPlans.businessId, businessId),
          eq(subscriptionPlanCustomers.isDeleted, false),
          eq(subscriptionPlans.isDeleted, false),
          eq(customers.isDeleted, false),
        ),
      )
      .orderBy(desc(subscriptionPlanCustomers.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(subscriptionPlanCustomers)
      .innerJoin(
        subscriptionPlans,
        eq(subscriptionPlanCustomers.subscriptionPlanId, subscriptionPlans.id),
      )
      .innerJoin(
        customers,
        eq(subscriptionPlanCustomers.customerId, customers.id),
      )
      .where(
        and(
          eq(subscriptionPlans.businessId, businessId),
          eq(subscriptionPlanCustomers.isDeleted, false),
          eq(subscriptionPlans.isDeleted, false),
          eq(customers.isDeleted, false),
        ),
      );

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    const data = await Promise.all(
      result.map(async (assignment) => {
        const createdByName = await this.usersService.getUserName(
          assignment.createdBy.toString(),
        );
        let updatedByName: string | undefined;
        if (assignment.updatedBy) {
          updatedByName = await this.usersService.getUserName(
            assignment.updatedBy.toString(),
          );
        }

        return {
          id: assignment.id.toString(),
          subscriptionPlanId: assignment.subscriptionPlanId.toString(),
          subscriptionPlanName: assignment.subscriptionPlanName,
          customerId: assignment.customerId.toString(),
          customerName: assignment.customerName,
          notes: assignment.notes,
          createdBy: createdByName,
          updatedBy: updatedByName,
          createdAt: assignment.createdAt,
          updatedAt: assignment.updatedAt,
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllCustomerGroupAssignments(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
  ): Promise<{
    data: SubscriptionPlanAssignmentDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const result = await this.db
      .select({
        id: subscriptionPlanCustomerGroups.id,
        subscriptionPlanId: subscriptionPlanCustomerGroups.subscriptionPlanId,
        subscriptionPlanName: subscriptionPlans.name,
        customerGroupId: subscriptionPlanCustomerGroups.customerGroupId,
        customerGroupName: customerGroups.name,
        notes: subscriptionPlanCustomerGroups.notes,
        createdBy: subscriptionPlanCustomerGroups.createdBy,
        updatedBy: subscriptionPlanCustomerGroups.updatedBy,
        createdAt: subscriptionPlanCustomerGroups.createdAt,
        updatedAt: subscriptionPlanCustomerGroups.updatedAt,
      })
      .from(subscriptionPlanCustomerGroups)
      .innerJoin(
        subscriptionPlans,
        eq(
          subscriptionPlanCustomerGroups.subscriptionPlanId,
          subscriptionPlans.id,
        ),
      )
      .innerJoin(
        customerGroups,
        eq(subscriptionPlanCustomerGroups.customerGroupId, customerGroups.id),
      )
      .where(
        and(
          eq(subscriptionPlans.businessId, businessId),
          eq(subscriptionPlanCustomerGroups.isDeleted, false),
          eq(subscriptionPlans.isDeleted, false),
          eq(customerGroups.isDeleted, false),
        ),
      )
      .orderBy(desc(subscriptionPlanCustomerGroups.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(subscriptionPlanCustomerGroups)
      .innerJoin(
        subscriptionPlans,
        eq(
          subscriptionPlanCustomerGroups.subscriptionPlanId,
          subscriptionPlans.id,
        ),
      )
      .innerJoin(
        customerGroups,
        eq(subscriptionPlanCustomerGroups.customerGroupId, customerGroups.id),
      )
      .where(
        and(
          eq(subscriptionPlans.businessId, businessId),
          eq(subscriptionPlanCustomerGroups.isDeleted, false),
          eq(subscriptionPlans.isDeleted, false),
          eq(customerGroups.isDeleted, false),
        ),
      );

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    const data = await Promise.all(
      result.map(async (assignment) => {
        const createdByName = await this.usersService.getUserName(
          assignment.createdBy.toString(),
        );
        let updatedByName: string | undefined;
        if (assignment.updatedBy) {
          updatedByName = await this.usersService.getUserName(
            assignment.updatedBy.toString(),
          );
        }

        return {
          id: assignment.id.toString(),
          subscriptionPlanId: assignment.subscriptionPlanId.toString(),
          subscriptionPlanName: assignment.subscriptionPlanName,
          customerGroupId: assignment.customerGroupId.toString(),
          customerGroupName: assignment.customerGroupName,
          notes: assignment.notes,
          createdBy: createdByName,
          updatedBy: updatedByName,
          createdAt: assignment.createdAt,
          updatedAt: assignment.updatedAt,
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    id: string,
    type: 'customer' | 'customer-group',
  ): Promise<SubscriptionPlanAssignmentDto> {
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (!user || !user.activeBusinessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (type === 'customer') {
      const assignment = await this.db
        .select({
          id: subscriptionPlanCustomers.id,
          subscriptionPlanId: subscriptionPlanCustomers.subscriptionPlanId,
          subscriptionPlanName: subscriptionPlans.name,
          customerId: subscriptionPlanCustomers.customerId,
          customerName: customers.customerDisplayName,
          notes: subscriptionPlanCustomers.notes,
          createdBy: subscriptionPlanCustomers.createdBy,
          updatedBy: subscriptionPlanCustomers.updatedBy,
          createdAt: subscriptionPlanCustomers.createdAt,
          updatedAt: subscriptionPlanCustomers.updatedAt,
        })
        .from(subscriptionPlanCustomers)
        .innerJoin(
          subscriptionPlans,
          eq(
            subscriptionPlanCustomers.subscriptionPlanId,
            subscriptionPlans.id,
          ),
        )
        .innerJoin(
          customers,
          eq(subscriptionPlanCustomers.customerId, customers.id),
        )
        .where(
          and(
            eq(subscriptionPlanCustomers.id, id),
            eq(subscriptionPlans.businessId, user.activeBusinessId),
            eq(subscriptionPlanCustomers.isDeleted, false),
            eq(subscriptionPlans.isDeleted, false),
            eq(customers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!assignment) {
        throw new NotFoundException(
          `Customer assignment with ID ${id} not found`,
        );
      }

      const createdByName = await this.usersService.getUserName(
        assignment.createdBy.toString(),
      );
      let updatedByName: string | undefined;
      if (assignment.updatedBy) {
        updatedByName = await this.usersService.getUserName(
          assignment.updatedBy.toString(),
        );
      }

      return {
        id: assignment.id.toString(),
        subscriptionPlanId: assignment.subscriptionPlanId.toString(),
        subscriptionPlanName: assignment.subscriptionPlanName,
        customerId: assignment.customerId.toString(),
        customerName: assignment.customerName,
        notes: assignment.notes,
        createdBy: createdByName,
        updatedBy: updatedByName,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
      };
    } else {
      const assignment = await this.db
        .select({
          id: subscriptionPlanCustomerGroups.id,
          subscriptionPlanId: subscriptionPlanCustomerGroups.subscriptionPlanId,
          subscriptionPlanName: subscriptionPlans.name,
          customerGroupId: subscriptionPlanCustomerGroups.customerGroupId,
          customerGroupName: customerGroups.name,
          notes: subscriptionPlanCustomerGroups.notes,
          createdBy: subscriptionPlanCustomerGroups.createdBy,
          updatedBy: subscriptionPlanCustomerGroups.updatedBy,
          createdAt: subscriptionPlanCustomerGroups.createdAt,
          updatedAt: subscriptionPlanCustomerGroups.updatedAt,
        })
        .from(subscriptionPlanCustomerGroups)
        .innerJoin(
          subscriptionPlans,
          eq(
            subscriptionPlanCustomerGroups.subscriptionPlanId,
            subscriptionPlans.id,
          ),
        )
        .innerJoin(
          customerGroups,
          eq(subscriptionPlanCustomerGroups.customerGroupId, customerGroups.id),
        )
        .where(
          and(
            eq(subscriptionPlanCustomerGroups.id, id),
            eq(subscriptionPlans.businessId, user.activeBusinessId),
            eq(subscriptionPlanCustomerGroups.isDeleted, false),
            eq(subscriptionPlans.isDeleted, false),
            eq(customerGroups.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!assignment) {
        throw new NotFoundException(
          `Customer group assignment with ID ${id} not found`,
        );
      }

      const createdByName = await this.usersService.getUserName(
        assignment.createdBy.toString(),
      );
      let updatedByName: string | undefined;
      if (assignment.updatedBy) {
        updatedByName = await this.usersService.getUserName(
          assignment.updatedBy.toString(),
        );
      }

      return {
        id: assignment.id.toString(),
        subscriptionPlanId: assignment.subscriptionPlanId.toString(),
        subscriptionPlanName: assignment.subscriptionPlanName,
        customerGroupId: assignment.customerGroupId.toString(),
        customerGroupName: assignment.customerGroupName,
        notes: assignment.notes,
        createdBy: createdByName,
        updatedBy: updatedByName,
        createdAt: assignment.createdAt,
        updatedAt: assignment.updatedAt,
      };
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    type: 'customer' | 'customer-group',
    updateDto: UpdateSubscriptionPlanAssignmentDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (type === 'customer') {
      const existingAssignment = await this.db
        .select()
        .from(subscriptionPlanCustomers)
        .innerJoin(
          subscriptionPlans,
          eq(
            subscriptionPlanCustomers.subscriptionPlanId,
            subscriptionPlans.id,
          ),
        )
        .where(
          and(
            eq(subscriptionPlanCustomers.id, id),
            eq(subscriptionPlans.businessId, businessId),
            eq(subscriptionPlanCustomers.isDeleted, false),
            eq(subscriptionPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAssignment) {
        throw new NotFoundException(
          `Customer assignment with ID ${id} not found`,
        );
      }

      await this.db
        .update(subscriptionPlanCustomers)
        .set({
          ...updateDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionPlanCustomers.id, id));

      await this.activityLogService.logUpdate(
        id,
        EntityType.SUBSCRIPTION_PLAN_ASSIGNMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    } else {
      const existingAssignment = await this.db
        .select()
        .from(subscriptionPlanCustomerGroups)
        .innerJoin(
          subscriptionPlans,
          eq(
            subscriptionPlanCustomerGroups.subscriptionPlanId,
            subscriptionPlans.id,
          ),
        )
        .where(
          and(
            eq(subscriptionPlanCustomerGroups.id, id),
            eq(subscriptionPlans.businessId, businessId),
            eq(subscriptionPlanCustomerGroups.isDeleted, false),
            eq(subscriptionPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAssignment) {
        throw new NotFoundException(
          `Customer group assignment with ID ${id} not found`,
        );
      }

      await this.db
        .update(subscriptionPlanCustomerGroups)
        .set({
          ...updateDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionPlanCustomerGroups.id, id));

      await this.activityLogService.logUpdate(
        id,
        EntityType.SUBSCRIPTION_PLAN_ASSIGNMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    type: 'customer' | 'customer-group',
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (type === 'customer') {
      const existingAssignment = await this.db
        .select()
        .from(subscriptionPlanCustomers)
        .innerJoin(
          subscriptionPlans,
          eq(
            subscriptionPlanCustomers.subscriptionPlanId,
            subscriptionPlans.id,
          ),
        )
        .where(
          and(
            eq(subscriptionPlanCustomers.id, id),
            eq(subscriptionPlans.businessId, businessId),
            eq(subscriptionPlanCustomers.isDeleted, false),
            eq(subscriptionPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAssignment) {
        throw new NotFoundException(
          `Customer assignment with ID ${id} not found`,
        );
      }

      await this.db
        .update(subscriptionPlanCustomers)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionPlanCustomers.id, id));

      await this.activityLogService.logDelete(
        id,
        EntityType.SUBSCRIPTION_PLAN_ASSIGNMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    } else {
      const existingAssignment = await this.db
        .select()
        .from(subscriptionPlanCustomerGroups)
        .innerJoin(
          subscriptionPlans,
          eq(
            subscriptionPlanCustomerGroups.subscriptionPlanId,
            subscriptionPlans.id,
          ),
        )
        .where(
          and(
            eq(subscriptionPlanCustomerGroups.id, id),
            eq(subscriptionPlans.businessId, businessId),
            eq(subscriptionPlanCustomerGroups.isDeleted, false),
            eq(subscriptionPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingAssignment) {
        throw new NotFoundException(
          `Customer group assignment with ID ${id} not found`,
        );
      }

      await this.db
        .update(subscriptionPlanCustomerGroups)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionPlanCustomerGroups.id, id));

      await this.activityLogService.logDelete(
        id,
        EntityType.SUBSCRIPTION_PLAN_ASSIGNMENT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      success: true,
      message: `Assignment with ID ${id} has been deleted`,
    };
  }
}
