import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateVehicleFineDto } from './dto/create-vehicle-fine.dto';
import { UpdateVehicleFineDto } from './dto/update-vehicle-fine.dto';
import { VehicleFineDto } from './dto/vehicle-fine.dto';
import { VehicleFineSlimDto } from './dto/vehicle-fine-slim.dto';
import { vehicleFines } from '../drizzle/schema/vehicle-fines.schema';
import { vehicles } from '../drizzle/schema/vehicles.schema';
import { media } from '../drizzle/schema/media.schema';
import { users } from '../drizzle/schema/users.schema';
import { eq, and, isNull, ilike, sql, gte, lte, desc, asc } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MediaService } from '../media/media.service';

@Injectable()
export class VehicleFinesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createVehicleFineDto: CreateVehicleFineDto,
    scannedFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<VehicleFineDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a vehicle fine with the same fine number already exists for this business
      const existingFine = await this.db
        .select()
        .from(vehicleFines)
        .where(
          and(
            eq(vehicleFines.businessId, businessId),
            ilike(vehicleFines.fineNumber, createVehicleFineDto.fineNumber),
            eq(vehicleFines.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingFine) {
        throw new ConflictException(
          `A vehicle fine with the number '${createVehicleFineDto.fineNumber}' already exists for this business`,
        );
      }

      // Verify that the vehicle exists and belongs to this business
      const vehicle = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.id, createVehicleFineDto.vehicleId),
            eq(vehicles.businessId, businessId),
            eq(vehicles.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!vehicle) {
        throw new BadRequestException(
          'Vehicle not found or does not belong to this business',
        );
      }

      let scannedFileId: string | undefined;

      // Upload scanned file if provided
      if (scannedFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          scannedFile,
          'vehicle-fines',
          businessId,
          userId,
        );
        scannedFileId = uploadedMedia.id;
      }

      // Insert new vehicle fine
      const [newVehicleFine] = await this.db
        .insert(vehicleFines)
        .values({
          businessId,
          fineNumber: createVehicleFineDto.fineNumber,
          fineType: createVehicleFineDto.fineType,
          vehicleId: createVehicleFineDto.vehicleId,
          fineAmount: createVehicleFineDto.fineAmount,
          dateOfFine: createVehicleFineDto.dateOfFine,
          scannedFileId,
          dateTimeOfOffense: createVehicleFineDto.dateTimeOfOffense
            ? new Date(createVehicleFineDto.dateTimeOfOffense)
            : undefined,
          notes: createVehicleFineDto.notes,
          paidToAuthority: createVehicleFineDto.paidToAuthority ?? false,
          expenseAccountId: createVehicleFineDto.expenseAccountId,
          paymentAccountId: createVehicleFineDto.paymentAccountId,
          paymentDate: createVehicleFineDto.paymentDate,
          paymentMethodId: createVehicleFineDto.paymentMethodId,
          referenceNumber: createVehicleFineDto.referenceNumber,
          dueDate: createVehicleFineDto.dueDate,
          lateFee: createVehicleFineDto.lateFee,
          totalAmount: createVehicleFineDto.totalAmount,
          issuingAuthority: createVehicleFineDto.issuingAuthority,
          authorityReference: createVehicleFineDto.authorityReference,
          location: createVehicleFineDto.location,
          createdBy: userId,
        })
        .returning();

      // Log the vehicle fine creation activity
      await this.activityLogService.logCreate(
        newVehicleFine.id,
        EntityType.EXPENSE,
        userId,
        businessId,
        {
          reason: `Created vehicle fine: ${createVehicleFineDto.fineNumber}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToVehicleFineDto(newVehicleFine);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create vehicle fine: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    fineNumber?: string,
    vehicleId?: string,
    fineType?: string,
    paidToAuthority?: string,
    sort?: string,
  ): Promise<{
    data: VehicleFineDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(vehicleFines.isDeleted, false),
      eq(vehicleFines.businessId, businessId),
    ];

    // Add filtering conditions
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(
          gte(vehicleFines.dateOfFine, fromDate.toISOString().split('T')[0]),
        );
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(
          lte(vehicleFines.dateOfFine, toDate.toISOString().split('T')[0]),
        );
      }
    }

    if (fineNumber) {
      whereConditions.push(ilike(vehicleFines.fineNumber, `%${fineNumber}%`));
    }

    if (vehicleId) {
      whereConditions.push(eq(vehicleFines.vehicleId, vehicleId));
    }

    if (fineType) {
      whereConditions.push(eq(vehicleFines.fineType, fineType as any));
    }

    if (paidToAuthority !== undefined) {
      whereConditions.push(
        eq(vehicleFines.paidToAuthority, paidToAuthority === 'true'),
      );
    }

    // Build order by clause
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      // Map field names to actual columns
      const sortableFields: Record<string, any> = {
        fineNumber: vehicleFines.fineNumber,
        fineAmount: vehicleFines.fineAmount,
        dateOfFine: vehicleFines.dateOfFine,
        dueDate: vehicleFines.dueDate,
        paidToAuthority: vehicleFines.paidToAuthority,
        createdAt: vehicleFines.createdAt,
        updatedAt: vehicleFines.updatedAt,
      };

      const column = sortableFields[field];
      if (column) {
        orderBy = direction === 'desc' ? desc(column) : asc(column);
      }
    }
    if (!orderBy) {
      orderBy = desc(vehicleFines.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicleFines)
      .where(and(...whereConditions))
      .then((results) => results[0]?.count || 0);

    // Get paginated data with joins
    const results = await this.db
      .select({
        vehicleFine: vehicleFines,
        vehicle: {
          id: vehicles.id,
          licensePlate: vehicles.licensePlate,
          make: vehicles.make,
          model: vehicles.model,
        },
        createdByUser: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        updatedByUser: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        scannedFile: {
          id: media.id,
          publicUrl: media.publicUrl,
        },
      })
      .from(vehicleFines)
      .leftJoin(vehicles, eq(vehicleFines.vehicleId, vehicles.id))
      .leftJoin(users, eq(vehicleFines.createdBy, users.id))
      .leftJoin(media, eq(vehicleFines.scannedFileId, media.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      results.map((result) =>
        this.mapToVehicleFineDto(result.vehicleFine, result),
      ),
    );

    return {
      data,
      meta: {
        total: totalResult,
        page,
        totalPages: Math.ceil(totalResult / limit),
      },
    };
  }

  async findOne(userId: string, id: string): Promise<VehicleFineDto> {
    const result = await this.db
      .select({
        vehicleFine: vehicleFines,
        vehicle: {
          id: vehicles.id,
          licensePlate: vehicles.licensePlate,
          make: vehicles.make,
          model: vehicles.model,
        },
        createdByUser: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        updatedByUser: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        scannedFile: {
          id: media.id,
          publicUrl: media.publicUrl,
        },
      })
      .from(vehicleFines)
      .leftJoin(vehicles, eq(vehicleFines.vehicleId, vehicles.id))
      .leftJoin(users, eq(vehicleFines.createdBy, users.id))
      .leftJoin(media, eq(vehicleFines.scannedFileId, media.id))
      .where(and(eq(vehicleFines.id, id), eq(vehicleFines.isDeleted, false)))
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Vehicle fine not found');
    }

    return await this.mapToVehicleFineDto(result.vehicleFine, result);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleFineDto: UpdateVehicleFineDto,
    scannedFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<VehicleFineDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if vehicle fine exists
      const existingFine = await this.db
        .select()
        .from(vehicleFines)
        .where(
          and(
            eq(vehicleFines.id, id),
            eq(vehicleFines.businessId, businessId),
            eq(vehicleFines.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingFine) {
        throw new NotFoundException('Vehicle fine not found');
      }

      // Check for duplicate fine number if it's being updated
      if (
        updateVehicleFineDto.fineNumber &&
        updateVehicleFineDto.fineNumber !== existingFine.fineNumber
      ) {
        const duplicateFine = await this.db
          .select()
          .from(vehicleFines)
          .where(
            and(
              eq(vehicleFines.businessId, businessId),
              ilike(vehicleFines.fineNumber, updateVehicleFineDto.fineNumber),
              eq(vehicleFines.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (duplicateFine) {
          throw new ConflictException(
            `A vehicle fine with the number '${updateVehicleFineDto.fineNumber}' already exists`,
          );
        }
      }

      let scannedFileId = existingFine.scannedFileId;

      // Upload new scanned file if provided
      if (scannedFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          scannedFile,
          'vehicle-fines',
          businessId,
          userId,
        );
        scannedFileId = uploadedMedia.id;
      }

      // Prepare update data with date conversion
      const updateData: any = { ...updateVehicleFineDto };
      if (updateData.dateTimeOfOffense) {
        updateData.dateTimeOfOffense = new Date(updateData.dateTimeOfOffense);
      }

      // Update vehicle fine
      const [updatedVehicleFine] = await this.db
        .update(vehicleFines)
        .set({
          ...updateData,
          scannedFileId,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(vehicleFines.id, id))
        .returning();

      // Log the update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.EXPENSE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToVehicleFineDto(updatedVehicleFine);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update vehicle fine: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingFine = await this.db
        .select()
        .from(vehicleFines)
        .where(
          and(
            eq(vehicleFines.id, id),
            eq(vehicleFines.businessId, businessId),
            eq(vehicleFines.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingFine) {
        throw new NotFoundException('Vehicle fine not found');
      }

      // Soft delete the vehicle fine
      await this.db
        .update(vehicleFines)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(vehicleFines.id, id));

      // Log the deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.EXPENSE,
        userId,
        businessId,
        {
          reason: `Deleted vehicle fine: ${existingFine.fineNumber}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        success: true,
        message: 'Vehicle fine deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete vehicle fine: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<VehicleFineSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        vehicleFine: vehicleFines,
        vehicle: {
          licensePlate: vehicles.licensePlate,
        },
      })
      .from(vehicleFines)
      .leftJoin(vehicles, eq(vehicleFines.vehicleId, vehicles.id))
      .where(
        and(
          eq(vehicleFines.businessId, businessId),
          eq(vehicleFines.isDeleted, false),
        ),
      )
      .orderBy(desc(vehicleFines.createdAt));

    return results.map((result) => ({
      id: result.vehicleFine.id,
      fineNumber: result.vehicleFine.fineNumber,
      fineType: result.vehicleFine.fineType,
      vehicleId: result.vehicleFine.vehicleId,
      fineAmount: result.vehicleFine.fineAmount,
      dateOfFine: result.vehicleFine.dateOfFine,
      paidToAuthority: result.vehicleFine.paidToAuthority,
      vehicleRegistrationNumber: result.vehicle?.licensePlate,
      dueDate: result.vehicleFine.dueDate,
      totalAmount: result.vehicleFine.totalAmount,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createVehicleFineDto: CreateVehicleFineDto,
    scannedFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const vehicleFine = await this.create(
      userId,
      businessId,
      createVehicleFineDto,
      scannedFile,
      metadata,
    );
    return { id: vehicleFine.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleFineDto: UpdateVehicleFineDto,
    scannedFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const vehicleFine = await this.update(
      userId,
      businessId,
      id,
      updateVehicleFineDto,
      scannedFile,
      metadata,
    );
    return { id: vehicleFine.id };
  }

  private async mapToVehicleFineDto(
    vehicleFine: typeof vehicleFines.$inferSelect,
    relatedData?: any,
  ): Promise<VehicleFineDto> {
    return {
      id: vehicleFine.id,
      businessId: vehicleFine.businessId,
      fineNumber: vehicleFine.fineNumber,
      fineType: vehicleFine.fineType,
      vehicleId: vehicleFine.vehicleId,
      fineAmount: vehicleFine.fineAmount,
      dateOfFine: vehicleFine.dateOfFine,
      expenseAccountId: vehicleFine.expenseAccountId,
      paymentAccountId: vehicleFine.paymentAccountId,
      paymentDate: vehicleFine.paymentDate,
      paymentMethodId: vehicleFine.paymentMethodId,
      referenceNumber: vehicleFine.referenceNumber || undefined,
      scannedFileId: vehicleFine.scannedFileId || undefined,
      dateTimeOfOffense:
        vehicleFine.dateTimeOfOffense?.toISOString() || undefined,
      notes: vehicleFine.notes || undefined,
      paidToAuthority: vehicleFine.paidToAuthority,
      dueDate: vehicleFine.dueDate || undefined,
      lateFee: vehicleFine.lateFee || undefined,
      totalAmount: vehicleFine.totalAmount || undefined,
      issuingAuthority: vehicleFine.issuingAuthority || undefined,
      authorityReference: vehicleFine.authorityReference || undefined,
      location: vehicleFine.location || undefined,
      createdBy: vehicleFine.createdBy,
      updatedBy: vehicleFine.updatedBy || undefined,
      createdAt: vehicleFine.createdAt.toISOString(),
      updatedAt: vehicleFine.updatedAt.toISOString(),
      // Related data
      vehicleRegistrationNumber:
        relatedData?.vehicle?.licensePlate || undefined,
      vehicleMakeModel: relatedData?.vehicle
        ? `${relatedData.vehicle.make || ''} ${relatedData.vehicle.model || ''}`.trim() ||
          undefined
        : undefined,
      createdByName: relatedData?.createdByUser
        ? `${relatedData.createdByUser.firstName || ''} ${relatedData.createdByUser.lastName || ''}`.trim() ||
          undefined
        : undefined,
      updatedByName: relatedData?.updatedByUser
        ? `${relatedData.updatedByUser.firstName || ''} ${relatedData.updatedByUser.lastName || ''}`.trim() ||
          undefined
        : undefined,
      scannedFileUrl: relatedData?.scannedFile?.publicUrl || undefined,
    };
  }
}
