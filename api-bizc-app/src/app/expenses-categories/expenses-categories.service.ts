import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateExpenseCategoryDto } from './dto/create-expense-category.dto';
import { UpdateExpenseCategoryDto } from './dto/update-expense-category.dto';
import { ExpenseCategoryDto } from './dto/expense-category.dto';
import { ExpenseCategorySlimDto } from './dto/expense-category-slim.dto';
import { ExpenseCategoryHierarchyDto } from './dto/expense-category-hierarchy.dto';
import { ExpenseCategoryListDto } from './dto/expense-category-list.dto';

import { accounts, AccountStatus } from '../drizzle/schema/accounts.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { UsersService } from '../users/users.service';
import { AccountCategory } from '../shared/types/account.enum';

@Injectable()
export class ExpenseCategoriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createExpenseCategoryDto: CreateExpenseCategoryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an account with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.businessId, businessId),
            ilike(accounts.accountName, createExpenseCategoryDto.accountName),
            eq(accounts.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAccount) {
        throw new ConflictException(
          `An expense category with the name '${createExpenseCategoryDto.accountName}' already exists for this business`,
        );
      }

      // Check if an account with the same account number already exists for this business
      if (createExpenseCategoryDto.accountNumber) {
        const existingAccountNumber = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(
                accounts.accountNumber,
                createExpenseCategoryDto.accountNumber,
              ),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingAccountNumber) {
          throw new ConflictException(
            `An expense category with the account number '${createExpenseCategoryDto.accountNumber}' already exists for this business`,
          );
        }
      }

      // Handle parent account if provided
      if (createExpenseCategoryDto.parentAccountId) {
        const parentAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, createExpenseCategoryDto.parentAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccount) {
          throw new BadRequestException('Parent expense category not found');
        }
      }

      // Validate default tax if provided
      if (createExpenseCategoryDto.defaultTaxId) {
        const tax = await this.db
          .select()
          .from(taxes)
          .where(
            and(
              eq(taxes.id, createExpenseCategoryDto.defaultTaxId),
              eq(taxes.businessId, businessId),
              eq(taxes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!tax) {
          throw new BadRequestException('Default tax not found');
        }
      }

      // Validate income account if provided
      if (createExpenseCategoryDto.incomeAccountId) {
        const incomeAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, createExpenseCategoryDto.incomeAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!incomeAccount) {
          throw new BadRequestException('Income account not found');
        }
      }

      // Insert new expense category
      const [newAccount] = await this.db
        .insert(accounts)
        .values({
          businessId: businessId,
          accountName: createExpenseCategoryDto.accountName,
          accountNumber: createExpenseCategoryDto.accountNumber,
          accountCategory: createExpenseCategoryDto.accountCategory as any,
          accountType: createExpenseCategoryDto.accountType as any,
          accountDetailType: createExpenseCategoryDto.accountDetailType as any,
          parentAccountId: createExpenseCategoryDto.parentAccountId,
          openingBalance: createExpenseCategoryDto.openingBalance ?? '0.00',
          openingBalanceDate: createExpenseCategoryDto.openingBalanceDate,
          description: createExpenseCategoryDto.description,
          defaultTaxId: createExpenseCategoryDto.defaultTaxId,
          useForBillableExpenses:
            createExpenseCategoryDto.useForBillableExpenses ?? false,
          incomeAccountId: createExpenseCategoryDto.incomeAccountId,
          isSystemAccount: false, // Expense categories are never system accounts
          status: createExpenseCategoryDto.status ?? AccountStatus.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Log the expense category creation activity
      await this.activityLogService.logCreate(
        newAccount.id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Expense category "${createExpenseCategoryDto.accountName}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newAccount.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create expense category: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ExpenseCategoryDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(accounts.isDeleted, false),
      eq(accounts.status, AccountStatus.ACTIVE),
      eq(accounts.businessId, businessId),
      // Filter for expense-related accounts only
      or(
        eq(accounts.accountCategory, AccountCategory.EXPENSES),
        eq(accounts.accountCategory, AccountCategory.REVENUE), // Include revenue for expense tracking
      ),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(accounts.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(accounts.createdAt, toDate));
      }
    }

    // Find all expense categories for the user's active business with pagination
    const result = await this.db
      .select()
      .from(accounts)
      .where(and(...whereConditions))
      .orderBy(desc(accounts.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(accounts)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: await Promise.all(
        result.map((account) => this.mapToExpenseCategoryDto(account)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    accountName?: string,
    accountNumber?: string,
    status?: string,
    accountCategory?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ExpenseCategoryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(accounts.isDeleted, false),
      eq(accounts.businessId, businessId),
      or(
        eq(accounts.accountCategory, AccountCategory.EXPENSES),
        eq(accounts.accountCategory, AccountCategory.REVENUE),
      ),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(accounts.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(accounts.createdAt, toDate));
      }
    }

    // Add account name filtering if provided
    if (accountName) {
      whereConditions.push(
        or(
          ilike(accounts.accountName, `%${accountName}%`),
          ilike(accounts.accountNumber, `%${accountName}%`),
        ),
      );
    }

    // Add account number filtering if provided
    if (accountNumber) {
      whereConditions.push(ilike(accounts.accountNumber, `%${accountNumber}%`));
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as AccountStatus);
      whereConditions.push(inArray(accounts.status, statusArray));
    }

    // Add account category filtering if provided
    if (accountCategory) {
      const decodedAccountCategory = decodeURIComponent(accountCategory);
      const accountCategoryArray = decodedAccountCategory
        .split(',')
        .map((s) => s.trim());
      whereConditions.push(
        inArray(accounts.accountCategory, accountCategoryArray as any),
      );
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'accountName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(accounts.accountName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountName, value));
                break;
              case 'ne':
                filterConditions.push(sql`${accounts.accountName} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${accounts.accountName} IS NULL OR ${accounts.accountName} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${accounts.accountName} IS NOT NULL AND ${accounts.accountName} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(accounts.status, value as AccountStatus[]),
                  );
                } else {
                  filterConditions.push(eq(accounts.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${accounts.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${accounts.status} != ${value}`);
                }
                break;
            }
          } else if (fieldId === 'accountNumber') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(accounts.accountNumber, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(accounts.accountNumber, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(accounts.accountNumber, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${accounts.accountNumber} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${accounts.accountNumber} IS NULL OR ${accounts.accountNumber} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${accounts.accountNumber} IS NOT NULL AND ${accounts.accountNumber} != ''`,
                );
                break;
            }
          } else if (fieldId === 'accountCategory') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(accounts.accountCategory, value as any),
                  );
                } else {
                  filterConditions.push(eq(accounts.accountCategory, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${accounts.accountCategory} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${accounts.accountCategory} != ${value}`,
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    let orderBy = [desc(accounts.createdAt), asc(accounts.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'accountName':
              orderBy = [
                isDesc ? desc(accounts.accountName) : asc(accounts.accountName),
                asc(accounts.id),
              ];
              break;
            case 'accountNumber':
              orderBy = [
                isDesc
                  ? desc(accounts.accountNumber)
                  : asc(accounts.accountNumber),
                asc(accounts.id),
              ];
              break;
            case 'status':
              orderBy = [
                isDesc ? desc(accounts.status) : asc(accounts.status),
                asc(accounts.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(accounts.createdAt) : asc(accounts.createdAt),
                asc(accounts.id),
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(accounts.updatedAt) : asc(accounts.updatedAt),
                asc(accounts.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: accounts.id,
        businessId: accounts.businessId,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
        parentAccountId: accounts.parentAccountId,
        openingBalance: accounts.openingBalance,
        openingBalanceDate: accounts.openingBalanceDate,
        description: accounts.description,
        useForBillableExpenses: accounts.useForBillableExpenses,
        isSystemAccount: accounts.isSystemAccount,
        status: accounts.status,
        createdAt: accounts.createdAt,
        updatedAt: accounts.updatedAt,
      })
      .from(accounts)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(accounts)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    // Transform to list DTOs
    const data: ExpenseCategoryListDto[] = result.map((account) => ({
      expenseCategoryId: account.id,
      accountName: account.accountName,
      accountNumber: account.accountNumber,
      accountCategory: account.accountCategory,
      accountType: account.accountType,
      accountDetailType: account.accountDetailType,
      parentAccountId: account.parentAccountId,
      openingBalance: account.openingBalance,
      description: account.description,
      useForBillableExpenses: account.useForBillableExpenses,
      isSystemAccount: account.isSystemAccount,
      status: account.status,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ExpenseCategoryDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const account = await this.db
      .select({
        id: accounts.id,
        businessId: accounts.businessId,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        accountCategory: accounts.accountCategory,
        accountType: accounts.accountType,
        accountDetailType: accounts.accountDetailType,
        parentAccountId: accounts.parentAccountId,
        openingBalance: accounts.openingBalance,
        openingBalanceDate: accounts.openingBalanceDate,
        description: accounts.description,
        defaultTaxId: accounts.defaultTaxId,
        useForBillableExpenses: accounts.useForBillableExpenses,
        incomeAccountId: accounts.incomeAccountId,
        isSystemAccount: accounts.isSystemAccount,
        status: accounts.status,
        createdAt: accounts.createdAt,
        updatedAt: accounts.updatedAt,
        createdByUser: {
          id: users.id,
          name: users.name,
        },
        updatedByUser: {
          id: sql<string>`${users.id}`,
          name: sql<string>`${users.name}`,
        },
      })
      .from(accounts)
      .leftJoin(users, eq(accounts.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(accounts.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(accounts.id, id),
          eq(accounts.businessId, businessId),
          eq(accounts.isDeleted, false),
          or(
            eq(accounts.accountCategory, AccountCategory.EXPENSES),
            eq(accounts.accountCategory, AccountCategory.REVENUE),
          ),
        ),
      )
      .then((results) => results[0]);

    if (!account) {
      throw new NotFoundException('Expense category not found');
    }

    return {
      expenseCategoryId: account.id,
      businessId: account.businessId,
      accountName: account.accountName,
      accountNumber: account.accountNumber,
      accountCategory: account.accountCategory,
      accountType: account.accountType,
      accountDetailType: account.accountDetailType,
      parentAccountId: account.parentAccountId,
      openingBalance: account.openingBalance,
      openingBalanceDate: account.openingBalanceDate,
      description: account.description,
      defaultTaxId: account.defaultTaxId,
      useForBillableExpenses: account.useForBillableExpenses,
      incomeAccountId: account.incomeAccountId,
      isSystemAccount: account.isSystemAccount,
      status: account.status,
      createdBy: account.createdByUser?.name || 'Unknown',
      updatedBy: account.updatedByUser?.name,
      createdAt: account.createdAt.toISOString(),
      updatedAt: account.updatedAt.toISOString(),
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateExpenseCategoryDto: UpdateExpenseCategoryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the expense category exists
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
            or(
              eq(accounts.accountCategory, AccountCategory.EXPENSES),
              eq(accounts.accountCategory, AccountCategory.REVENUE),
            ),
          ),
        )
        .then((results) => results[0]);

      if (!existingAccount) {
        throw new NotFoundException('Expense category not found');
      }

      // Check if another account with the same name already exists for this business
      if (updateExpenseCategoryDto.accountName) {
        const duplicateNameAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              ilike(accounts.accountName, updateExpenseCategoryDto.accountName),
              eq(accounts.isDeleted, false),
              sql`${accounts.id} != ${id}`, // Exclude current account
            ),
          )
          .then((results) => results[0]);

        if (duplicateNameAccount) {
          throw new ConflictException(
            `An expense category with the name '${updateExpenseCategoryDto.accountName}' already exists for this business`,
          );
        }
      }

      // Check if another account with the same account number already exists for this business
      if (updateExpenseCategoryDto.accountNumber) {
        const duplicateNumberAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.businessId, businessId),
              eq(
                accounts.accountNumber,
                updateExpenseCategoryDto.accountNumber,
              ),
              eq(accounts.isDeleted, false),
              sql`${accounts.id} != ${id}`, // Exclude current account
            ),
          )
          .then((results) => results[0]);

        if (duplicateNumberAccount) {
          throw new ConflictException(
            `An expense category with the account number '${updateExpenseCategoryDto.accountNumber}' already exists for this business`,
          );
        }
      }

      // Handle parent account validation if provided
      if (updateExpenseCategoryDto.parentAccountId) {
        const parentAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, updateExpenseCategoryDto.parentAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccount) {
          throw new BadRequestException('Parent expense category not found');
        }
      }

      // Validate default tax if provided
      if (updateExpenseCategoryDto.defaultTaxId) {
        const tax = await this.db
          .select()
          .from(taxes)
          .where(
            and(
              eq(taxes.id, updateExpenseCategoryDto.defaultTaxId),
              eq(taxes.businessId, businessId),
              eq(taxes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!tax) {
          throw new BadRequestException('Default tax not found');
        }
      }

      // Validate income account if provided
      if (updateExpenseCategoryDto.incomeAccountId) {
        const incomeAccount = await this.db
          .select()
          .from(accounts)
          .where(
            and(
              eq(accounts.id, updateExpenseCategoryDto.incomeAccountId),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!incomeAccount) {
          throw new BadRequestException('Income account not found');
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only include fields that are provided
      if (updateExpenseCategoryDto.accountName !== undefined) {
        updateData.accountName = updateExpenseCategoryDto.accountName;
      }
      if (updateExpenseCategoryDto.accountNumber !== undefined) {
        updateData.accountNumber = updateExpenseCategoryDto.accountNumber;
      }
      if (updateExpenseCategoryDto.accountCategory !== undefined) {
        updateData.accountCategory = updateExpenseCategoryDto.accountCategory;
      }
      if (updateExpenseCategoryDto.accountType !== undefined) {
        updateData.accountType = updateExpenseCategoryDto.accountType;
      }
      if (updateExpenseCategoryDto.accountDetailType !== undefined) {
        updateData.accountDetailType =
          updateExpenseCategoryDto.accountDetailType;
      }
      if (updateExpenseCategoryDto.parentAccountId !== undefined) {
        updateData.parentAccountId = updateExpenseCategoryDto.parentAccountId;
      }
      if (updateExpenseCategoryDto.openingBalance !== undefined) {
        updateData.openingBalance = updateExpenseCategoryDto.openingBalance;
      }
      if (updateExpenseCategoryDto.openingBalanceDate !== undefined) {
        updateData.openingBalanceDate =
          updateExpenseCategoryDto.openingBalanceDate;
      }
      if (updateExpenseCategoryDto.description !== undefined) {
        updateData.description = updateExpenseCategoryDto.description;
      }
      if (updateExpenseCategoryDto.defaultTaxId !== undefined) {
        updateData.defaultTaxId = updateExpenseCategoryDto.defaultTaxId;
      }
      if (updateExpenseCategoryDto.useForBillableExpenses !== undefined) {
        updateData.useForBillableExpenses =
          updateExpenseCategoryDto.useForBillableExpenses;
      }
      if (updateExpenseCategoryDto.incomeAccountId !== undefined) {
        updateData.incomeAccountId = updateExpenseCategoryDto.incomeAccountId;
      }
      if (updateExpenseCategoryDto.status !== undefined) {
        updateData.status = updateExpenseCategoryDto.status;
      }

      // Update the expense category
      await this.db.update(accounts).set(updateData).where(eq(accounts.id, id));

      // Log the expense category update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update expense category: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; accountName: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the expense category exists
      const existingAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, id),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
            or(
              eq(accounts.accountCategory, AccountCategory.EXPENSES),
              eq(accounts.accountCategory, AccountCategory.REVENUE),
            ),
          ),
        )
        .then((results) => results[0]);

      if (!existingAccount) {
        throw new NotFoundException('Expense category not found');
      }

      // Check if this is a system account (cannot be deleted)
      if (existingAccount.isSystemAccount) {
        throw new BadRequestException(
          'System expense categories cannot be deleted',
        );
      }

      // Soft delete the expense category
      await this.db
        .update(accounts)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(accounts.id, id));

      // Log the expense category deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Expense category "${existingAccount.accountName}" was deleted`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: id,
        accountName: existingAccount.accountName,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete expense category: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    _userId: string,
    businessId: string | null,
    accountName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; accountName: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(accounts.businessId, businessId),
      ilike(accounts.accountName, accountName),
      eq(accounts.isDeleted, false),
      or(
        eq(accounts.accountCategory, AccountCategory.EXPENSES),
        eq(accounts.accountCategory, AccountCategory.REVENUE),
      ),
    ];

    // Exclude current account if updating
    if (excludeId) {
      whereConditions.push(sql`${accounts.id} != ${excludeId}`);
    }

    const existingAccount = await this.db
      .select()
      .from(accounts)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return {
      available: !existingAccount,
      accountName,
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
    status?: AccountStatus,
  ): Promise<ExpenseCategorySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(accounts.isDeleted, false),
      eq(accounts.businessId, businessId),
      or(
        eq(accounts.accountCategory, AccountCategory.EXPENSES),
        eq(accounts.accountCategory, AccountCategory.REVENUE),
      ),
    ];

    // Add status filtering if provided
    if (status) {
      whereConditions.push(eq(accounts.status, status));
    } else {
      // Default to active accounts only
      whereConditions.push(eq(accounts.status, AccountStatus.ACTIVE));
    }

    const expenseCategories = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        accountNumber: accounts.accountNumber,
        status: accounts.status,
      })
      .from(accounts)
      .where(and(...whereConditions))
      .orderBy(asc(accounts.accountName));

    return expenseCategories.map((account) => ({
      expenseCategoryId: account.id,
      accountName: account.accountName,
      accountNumber: account.accountNumber,
      status: account.status,
    }));
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createExpenseCategoryDtos: CreateExpenseCategoryDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ expenseCategoryIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createExpenseCategoryDtos ||
        createExpenseCategoryDtos.length === 0
      ) {
        throw new BadRequestException('No expense categories provided');
      }

      // Check for duplicate names within the request
      const names = createExpenseCategoryDtos.map((dto) =>
        dto.accountName.toLowerCase(),
      );
      const duplicateNames = names.filter(
        (name, index) => names.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new ConflictException(
          `Duplicate expense category names in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for existing names in database
      const existingAccounts = await this.db
        .select({ accountName: accounts.accountName })
        .from(accounts)
        .where(
          and(
            eq(accounts.businessId, businessId),
            inArray(
              sql`LOWER(${accounts.accountName})`,
              names.map((name) => sql`${name}`),
            ),
            eq(accounts.isDeleted, false),
          ),
        );

      if (existingAccounts.length > 0) {
        const existingNames = existingAccounts.map(
          (account) => account.accountName,
        );
        throw new ConflictException(
          `Expense categories with these names already exist: ${existingNames.join(', ')}`,
        );
      }

      // Create all expense categories in a transaction
      const newAccountIds = await this.db.transaction(async (tx) => {
        const createdAccounts = [];

        for (const createDto of createExpenseCategoryDtos) {
          const [newAccount] = await tx
            .insert(accounts)
            .values({
              businessId: businessId,
              accountName: createDto.accountName,
              accountNumber: createDto.accountNumber,
              accountCategory: createDto.accountCategory as any,
              accountType: createDto.accountType as any,
              accountDetailType: createDto.accountDetailType as any,
              parentAccountId: createDto.parentAccountId,
              openingBalance: createDto.openingBalance ?? '0.00',
              openingBalanceDate: createDto.openingBalanceDate,
              description: createDto.description,
              defaultTaxId: createDto.defaultTaxId,
              useForBillableExpenses: createDto.useForBillableExpenses ?? false,
              incomeAccountId: createDto.incomeAccountId,
              isSystemAccount: false,
              status: createDto.status ?? AccountStatus.ACTIVE,
              createdBy: userId,
            })
            .returning({ id: accounts.id });

          createdAccounts.push(newAccount);
        }

        return createdAccounts.map((account) => account.id);
      });

      // Log bulk creation operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.ACCOUNT,
        newAccountIds,
        {
          names: createExpenseCategoryDtos.map((dto) => dto.accountName),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: newAccountIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        expenseCategoryIds: newAccountIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create expense categories: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    expenseCategoryIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedExpenseCategoryIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!expenseCategoryIds || expenseCategoryIds.length === 0) {
        throw new BadRequestException('No expense category IDs provided');
      }

      // Check which expense categories exist and can be deleted
      const existingAccounts = await this.db
        .select({
          id: accounts.id,
          accountName: accounts.accountName,
          isSystemAccount: accounts.isSystemAccount,
        })
        .from(accounts)
        .where(
          and(
            inArray(accounts.id, expenseCategoryIds),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
            or(
              eq(accounts.accountCategory, AccountCategory.EXPENSES),
              eq(accounts.accountCategory, AccountCategory.REVENUE),
            ),
          ),
        );

      if (existingAccounts.length === 0) {
        throw new NotFoundException('No expense categories found to delete');
      }

      // Check for system accounts that cannot be deleted
      const systemAccounts = existingAccounts.filter(
        (account) => account.isSystemAccount,
      );
      if (systemAccounts.length > 0) {
        const systemNames = systemAccounts.map(
          (account) => account.accountName,
        );
        throw new BadRequestException(
          `System expense categories cannot be deleted: ${systemNames.join(', ')}`,
        );
      }

      const deletableIds = existingAccounts.map((account) => account.id);

      // Soft delete the expense categories
      await this.db
        .update(accounts)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(accounts.id, deletableIds));

      // Log bulk deletion operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.ACCOUNT,
        deletableIds,
        {
          names: existingAccounts.map((account) => account.accountName),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: deletableIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedCount: deletableIds.length,
        deletedExpenseCategoryIds: deletableIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete expense categories: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    expenseCategoryIds: string[],
    status: AccountStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedCount: number; updatedExpenseCategoryIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!expenseCategoryIds || expenseCategoryIds.length === 0) {
        throw new BadRequestException('No expense category IDs provided');
      }

      // Check which expense categories exist
      const existingAccounts = await this.db
        .select({
          id: accounts.id,
          accountName: accounts.accountName,
        })
        .from(accounts)
        .where(
          and(
            inArray(accounts.id, expenseCategoryIds),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
            or(
              eq(accounts.accountCategory, AccountCategory.EXPENSES),
              eq(accounts.accountCategory, AccountCategory.REVENUE),
            ),
          ),
        );

      if (existingAccounts.length === 0) {
        throw new NotFoundException('No expense categories found to update');
      }

      const updatableIds = existingAccounts.map((account) => account.id);

      // Update the status of expense categories
      await this.db
        .update(accounts)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(accounts.id, updatableIds));

      // Log bulk status update operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.ACCOUNT,
        updatableIds,
        {
          status,
          names: existingAccounts.map((account) => account.accountName),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: updatableIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        updatedCount: updatableIds.length,
        updatedExpenseCategoryIds: updatableIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update expense categories status: ${error.message}`,
      );
    }
  }

  // Helper methods for controller
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createExpenseCategoryDto: CreateExpenseCategoryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ expenseCategoryId: string }> {
    const result = await this.create(
      userId,
      businessId,
      createExpenseCategoryDto,
      metadata,
    );
    return { expenseCategoryId: result.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateExpenseCategoryDto: UpdateExpenseCategoryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ expenseCategoryId: string }> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateExpenseCategoryDto,
      metadata,
    );
    return { expenseCategoryId: result.id };
  }

  async removeAndReturnDetails(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ expenseCategoryId: string; accountName: string }> {
    const result = await this.remove(userId, businessId, id, metadata);
    return { expenseCategoryId: result.id, accountName: result.accountName };
  }

  // Hierarchy methods
  async findAllHierarchy(
    userId: string,
    businessId: string | null,
  ): Promise<ExpenseCategoryHierarchyDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all expense categories with only the essential hierarchy fields
    const expenseCategoryResults = await this.db
      .select({
        id: accounts.id,
        accountName: accounts.accountName,
        parentAccountId: accounts.parentAccountId,
      })
      .from(accounts)
      .where(
        and(
          eq(accounts.isDeleted, false),
          eq(accounts.status, AccountStatus.ACTIVE),
          eq(accounts.businessId, businessId),
          or(
            eq(accounts.accountCategory, AccountCategory.EXPENSES),
            eq(accounts.accountCategory, AccountCategory.REVENUE),
          ),
        ),
      )
      .orderBy(asc(accounts.accountName));

    // Disable view logging for performance

    return expenseCategoryResults.map((account) => ({
      expenseCategoryId: account.id,
      accountName: account.accountName,
      parentAccountId: account.parentAccountId,
    }));
  }

  async bulkUpdateExpenseCategoryHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentAccountId: string | null }[],
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      const results = {
        updated: 0,
        failed: [] as Array<{ id: string; error: string }>,
      };

      // Process each update individually to handle partial failures
      for (const update of updates) {
        try {
          // Check if the expense category exists
          const existingAccount = await this.db
            .select()
            .from(accounts)
            .where(
              and(
                eq(accounts.id, update.id),
                eq(accounts.businessId, businessId),
                eq(accounts.isDeleted, false),
                or(
                  eq(accounts.accountCategory, AccountCategory.EXPENSES),
                  eq(accounts.accountCategory, AccountCategory.REVENUE),
                ),
              ),
            )
            .then((results) => results[0]);

          if (!existingAccount) {
            results.failed.push({
              id: update.id,
              error: 'Expense category not found',
            });
            continue;
          }

          // If parentAccountId is provided, validate it exists
          if (update.parentAccountId) {
            const parentAccount = await this.db
              .select()
              .from(accounts)
              .where(
                and(
                  eq(accounts.id, update.parentAccountId),
                  eq(accounts.businessId, businessId),
                  eq(accounts.isDeleted, false),
                  or(
                    eq(accounts.accountCategory, AccountCategory.EXPENSES),
                    eq(accounts.accountCategory, AccountCategory.REVENUE),
                  ),
                ),
              )
              .then((results) => results[0]);

            if (!parentAccount) {
              results.failed.push({
                id: update.id,
                error: 'Parent expense category not found',
              });
              continue;
            }

            // Check for circular reference
            if (update.parentAccountId === update.id) {
              results.failed.push({
                id: update.id,
                error: 'Cannot set expense category as its own parent',
              });
              continue;
            }

            // Check if the parent would create a circular reference
            const wouldCreateCircularReference =
              await this.checkCircularReference(
                update.id,
                update.parentAccountId,
                businessId,
              );

            if (wouldCreateCircularReference) {
              results.failed.push({
                id: update.id,
                error: 'Would create circular reference in hierarchy',
              });
              continue;
            }
          }

          // Update the parent relationship
          await this.db
            .update(accounts)
            .set({
              parentAccountId: update.parentAccountId,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(accounts.id, update.id));

          results.updated++;
        } catch (error) {
          results.failed.push({
            id: update.id,
            error: error.message || 'Unknown error occurred',
          });
        }
      }

      // Log bulk hierarchy update operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.ACCOUNT,
        updates.map((u) => u.id),
        {
          operationType: 'hierarchy_update',
          updated: results.updated,
          failed: results.failed.length,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: updates.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: ActivitySource.WEB,
        },
      );

      return results;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update expense category hierarchy: ${error.message}`,
      );
    }
  }

  // Helper method to check for circular references
  private async checkCircularReference(
    expenseCategoryId: string,
    newParentId: string,
    businessId: string,
  ): Promise<boolean> {
    let currentParentId = newParentId;
    const visited = new Set<string>();

    while (currentParentId && !visited.has(currentParentId)) {
      if (currentParentId === expenseCategoryId) {
        return true; // Circular reference detected
      }

      visited.add(currentParentId);

      const parent = await this.db
        .select({ parentAccountId: accounts.parentAccountId })
        .from(accounts)
        .where(
          and(
            eq(accounts.id, currentParentId),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      currentParentId = parent?.parentAccountId || null;
    }

    return false; // No circular reference
  }

  private async mapToExpenseCategoryDto(
    account: typeof accounts.$inferSelect,
  ): Promise<ExpenseCategoryDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      account.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (account.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        account.updatedBy.toString(),
      );
    }

    const expenseCategoryDto: ExpenseCategoryDto = {
      expenseCategoryId: account.id,
      businessId: account.businessId,
      accountName: account.accountName,
      accountNumber: account.accountNumber,
      accountCategory: account.accountCategory,
      accountType: account.accountType,
      accountDetailType: account.accountDetailType,
      parentAccountId: account.parentAccountId,
      openingBalance: account.openingBalance,
      openingBalanceDate: account.openingBalanceDate,
      description: account.description,
      defaultTaxId: account.defaultTaxId,
      useForBillableExpenses: account.useForBillableExpenses,
      incomeAccountId: account.incomeAccountId,
      isSystemAccount: account.isSystemAccount,
      status: account.status,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: account.createdAt.toISOString(),
      updatedAt: account.updatedAt.toISOString(),
    };

    return expenseCategoryDto;
  }
}
