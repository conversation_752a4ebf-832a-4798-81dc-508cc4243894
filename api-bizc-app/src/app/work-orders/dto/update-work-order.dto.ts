import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  IsUUID,
  IsBoolean,
  IsArray,
  IsDateString,
  IsEnum,
  ValidateNested,
  ValidateIf,
  IsNotEmpty,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { TaskPriority } from '../../drizzle/schema/tasks.schema';

class StaffAssignmentDto {
  @ApiPropertyOptional({ description: 'Staff member ID' })
  @IsOptional()
  @IsUUID()
  staffId?: string;

  @ApiPropertyOptional({
    description: 'Whether to create a task for this staff member',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return Boolean(value);
  })
  createTask?: boolean;

  @ApiPropertyOptional({
    description: 'Task title for this staff member',
    maxLength: 191,
  })
  @ValidateIf((o) => o.createTask === true)
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  taskTitle?: string;

  @ApiPropertyOptional({
    description: 'Task description for this staff member',
  })
  @IsOptional()
  @IsString()
  taskDescription?: string;

  @ApiPropertyOptional({
    description: 'Task due date for this staff member (YYYY-MM-DD)',
    example: '2024-01-20',
  })
  @IsOptional()
  @IsDateString()
  taskDueDate?: string;

  @ApiPropertyOptional({
    enum: TaskPriority,
    enumName: 'TaskPriority',
    description: 'Task priority for this staff member',
    default: TaskPriority.MEDIUM,
  })
  @IsOptional()
  @IsEnum(TaskPriority, {
    message: 'Task priority must be a valid task priority',
  })
  taskPriority?: TaskPriority;
}

export class UpdateWorkOrderDto {
  @ApiPropertyOptional({ description: 'Work order number', maxLength: 191 })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  workOrderNumber?: string;

  @ApiPropertyOptional({ description: 'Product ID for the work order' })
  @IsOptional()
  @IsUUID()
  productId?: string;

  @ApiPropertyOptional({ description: 'Product variant ID' })
  @IsOptional()
  @IsUUID()
  variantId?: string;

  @ApiPropertyOptional({ description: 'Bill of Materials ID' })
  @IsOptional()
  @IsUUID()
  bomId?: string;

  @ApiPropertyOptional({
    description: 'Quantity to produce',
    example: '100.000',
  })
  @IsOptional()
  @IsString()
  quantityToProduce?: string;

  @ApiPropertyOptional({
    description: 'Quantity produced',
    example: '50.000',
  })
  @IsOptional()
  @IsString()
  quantityProduced?: string;

  @ApiPropertyOptional({
    description: 'Quantity scrapped',
    example: '5.000',
  })
  @IsOptional()
  @IsString()
  quantityScrapped?: string;

  @ApiPropertyOptional({ description: 'Work order status ID' })
  @IsOptional()
  @IsUUID()
  statusId?: string;

  @ApiPropertyOptional({ description: 'Work order priority ID' })
  @IsOptional()
  @IsUUID()
  priorityId?: string;

  @ApiPropertyOptional({
    description: 'Planned start date (YYYY-MM-DD)',
    example: '2024-01-15',
  })
  @IsOptional()
  @IsDateString()
  plannedStartDate?: string;

  @ApiPropertyOptional({
    description: 'Planned end date (YYYY-MM-DD)',
    example: '2024-01-20',
  })
  @IsOptional()
  @IsDateString()
  plannedEndDate?: string;

  @ApiPropertyOptional({ description: 'Work order notes' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Whether quality inspection is required',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  qualityInspectionRequired?: boolean;

  @ApiPropertyOptional({
    description: 'Whether quality inspection is completed',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  qualityInspectionCompleted?: boolean;

  @ApiPropertyOptional({ description: 'Quality inspection result' })
  @IsOptional()
  @IsString()
  qualityInspectionResult?: string;

  @ApiPropertyOptional({ description: 'Quality inspection notes' })
  @IsOptional()
  @IsString()
  qualityInspectionNotes?: string;

  @ApiPropertyOptional({ description: 'Inspector staff member ID' })
  @IsOptional()
  @IsUUID()
  inspectedBy?: string;

  @ApiPropertyOptional({
    description: 'Array of sales order IDs to associate with this work order',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [value];
      }
    }
    if (Array.isArray(value)) {
      return value;
    }
    return value;
  })
  salesOrderIds?: string[];

  @ApiPropertyOptional({
    description: 'Array of staff member IDs to assign to this work order',
    type: [String],
    example: [
      '123e4567-e89b-12d3-a456-************',
      '123e4567-e89b-12d3-a456-************',
    ],
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : [value];
      } catch {
        return [value];
      }
    }
    if (Array.isArray(value)) {
      return value;
    }
    return value;
  })
  staffIds?: string[];

  @ApiPropertyOptional({
    description: 'Staff assignments with optional task creation per staff member',
    type: [StaffAssignmentDto],
    example: [
      {
        staffId: '123e4567-e89b-12d3-a456-************',
        createTask: true,
        taskTitle: 'Complete work order production',
        taskDescription: 'Handle the production process for this work order',
        taskDueDate: '2024-01-20',
        taskPriority: 'HIGH',
      },
      {
        staffId: '123e4567-e89b-12d3-a456-************',
        createTask: false,
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StaffAssignmentDto)
  staffAssignments?: StaffAssignmentDto[];

  // Task update fields
  @ApiPropertyOptional({
    description: 'Task title',
    maxLength: 191,
  })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  taskTitle?: string;

  @ApiPropertyOptional({
    description: 'Task description',
  })
  @IsOptional()
  @IsString()
  taskDescription?: string;

  @ApiPropertyOptional({
    description: 'Task due date (YYYY-MM-DD)',
    example: '2024-01-20',
  })
  @IsOptional()
  @IsDateString()
  taskDueDate?: string;

  @ApiPropertyOptional({
    enum: TaskPriority,
    enumName: 'TaskPriority',
    description: 'Task priority',
  })
  @IsOptional()
  @IsEnum(TaskPriority, {
    message: 'Task priority must be a valid task priority',
  })
  taskPriority?: TaskPriority;

  @ApiPropertyOptional({
    description: 'Staff member ID to assign the task to',
  })
  @IsOptional()
  @IsUUID()
  taskAssignedTo?: string;
}
