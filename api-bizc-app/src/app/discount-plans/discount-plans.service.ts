import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  discountPlans,
  ScheduleConfig,
  TimeType,
} from '../drizzle/schema/discount-plans.schema';
import { CreateDiscountPlanDto } from './dto/create-discount-plan.dto';
import { UpdateDiscountPlanDto } from './dto/update-discount-plan.dto';
import { DiscountPlanDto } from './dto/discount-plan.dto';
import { DiscountPlanSlimDto } from './dto/discount-plan-slim.dto';
import { DiscountPlanListDto } from './dto/discount-plan-list.dto';
import {
  and,
  eq,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { StatusType } from '../shared/types';
import {
  EntityType,
  ActivityType,
  ActivitySource,
  ExecutionStrategy,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class DiscountPlansService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  /**
   * Check if two schedule configurations have overlapping time periods
   */
  private schedulesOverlap(
    schedule1: ScheduleConfig[],
    schedule2: ScheduleConfig[],
  ): boolean {
    for (const s1 of schedule1) {
      for (const s2 of schedule2) {
        // Check if they have overlapping days
        const overlappingDays = s1.days.some((day) => s2.days.includes(day));
        if (!overlappingDays) continue;

        // If both are all day, they overlap
        if (
          s1.timeType === TimeType.ALL_DAY &&
          s2.timeType === TimeType.ALL_DAY
        ) {
          return true;
        }

        // If one is all day and the other has custom period, they overlap
        if (
          (s1.timeType === TimeType.ALL_DAY &&
            s2.timeType === TimeType.CUSTOM_PERIOD) ||
          (s1.timeType === TimeType.CUSTOM_PERIOD &&
            s2.timeType === TimeType.ALL_DAY)
        ) {
          return true;
        }

        // If both have custom periods, check time overlap
        if (
          s1.timeType === TimeType.CUSTOM_PERIOD &&
          s2.timeType === TimeType.CUSTOM_PERIOD &&
          s1.timePeriod &&
          s2.timePeriod
        ) {
          const start1 = s1.timePeriod.startTime;
          const end1 = s1.timePeriod.endTime;
          const start2 = s2.timePeriod.startTime;
          const end2 = s2.timePeriod.endTime;

          // Check if time periods overlap
          if (start1 < end2 && start2 < end1) {
            return true;
          }
        }
      }
    }
    return false;
  }

  /**
   * Check if two date ranges overlap
   */
  private dateRangesOverlap(
    start1: string,
    end1: string,
    start2: string,
    end2: string,
  ): boolean {
    const startDate1 = new Date(start1);
    const endDate1 = new Date(end1);
    const startDate2 = new Date(start2);
    const endDate2 = new Date(end2);

    return startDate1 <= endDate2 && startDate2 <= endDate1;
  }

  /**
   * Validate that the discount plan doesn't conflict with existing active plans
   */
  private async validateScheduleConflicts(
    businessId: string,
    createDiscountPlanDto: CreateDiscountPlanDto,
    excludeDiscountPlanId?: string,
  ): Promise<void> {
    // Get all active discount plans for this business
    const whereConditions = [
      eq(discountPlans.businessId, businessId),
      eq(discountPlans.status, StatusType.ACTIVE),
      eq(discountPlans.isDeleted, false),
    ];

    // Exclude the current discount plan if updating
    if (excludeDiscountPlanId) {
      whereConditions.push(
        sql`${discountPlans.id} != ${excludeDiscountPlanId}`,
      );
    }

    const existingPlans = await this.db
      .select({
        id: discountPlans.id,
        planName: discountPlans.planName,
        method: discountPlans.method,
        startDate: discountPlans.startDate,
        endDate: discountPlans.endDate,
        schedules: discountPlans.schedules,
        applyToAllProducts: discountPlans.applyToAllProducts,
        applyToAllServices: discountPlans.applyToAllServices,
        applyToAllCategories: discountPlans.applyToAllCategories,
        applyToAllCustomerGroups: discountPlans.applyToAllCustomerGroups,
        applyToAllCustomers: discountPlans.applyToAllCustomers,
      })
      .from(discountPlans)
      .where(and(...whereConditions));

    for (const existingPlan of existingPlans) {
      // Check if date ranges overlap
      if (
        !this.dateRangesOverlap(
          createDiscountPlanDto.startDate,
          createDiscountPlanDto.endDate,
          existingPlan.startDate,
          existingPlan.endDate,
        )
      ) {
        continue; // No date overlap, skip this plan
      }

      // Check if schedules overlap
      if (
        !this.schedulesOverlap(
          createDiscountPlanDto.schedules,
          existingPlan.schedules as ScheduleConfig[],
        )
      ) {
        continue; // No schedule overlap, skip this plan
      }

      // Now check for entity conflicts based on the new plan's method
      const hasConflict = await this.checkEntityConflicts(
        existingPlan,
        createDiscountPlanDto,
      );

      if (hasConflict) {
        throw new ConflictException(
          `Discount plan conflicts with existing plan "${existingPlan.planName}" due to overlapping schedules and target entities`,
        );
      }
    }
  }

  /**
   * Check if there are entity conflicts between existing and new discount plans
   */
  private async checkEntityConflicts(
    existingPlan: any,
    newPlan: CreateDiscountPlanDto,
  ): Promise<boolean> {
    // Check for "apply to all" conflicts
    if (
      (existingPlan.applyToAllProducts && newPlan.applyToAllProducts) ||
      (existingPlan.applyToAllServices && newPlan.applyToAllServices) ||
      (existingPlan.applyToAllCategories && newPlan.applyToAllCategories) ||
      (existingPlan.applyToAllCustomerGroups &&
        newPlan.applyToAllCustomerGroups) ||
      (existingPlan.applyToAllCustomers && newPlan.applyToAllCustomers)
    ) {
      return true;
    }

    // For specific entity attachments, we would need to check junction tables
    // This is a simplified implementation - in a full implementation, you would
    // check the actual junction table relationships to see if specific entities
    // are attached to both plans

    // Check if both plans target the same method types that could conflict
    const methodConflicts = [
      ['attached_to_products', 'attached_to_product_categories'],
      ['attached_to_services', 'attached_to_service_categories'],
      ['attached_to_customer_group', 'attached_to_customers'],
    ];

    for (const conflictGroup of methodConflicts) {
      if (
        conflictGroup.includes(existingPlan.method) &&
        conflictGroup.includes(newPlan.method)
      ) {
        // In a full implementation, you would check if they target the same specific entities
        // For now, we'll assume potential conflict exists
        return true;
      }
    }

    return false;
  }

  async create(
    userId: string,
    businessId: string | null,
    createDiscountPlanDto: CreateDiscountPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a discount plan with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingDiscountPlan = await this.db
        .select()
        .from(discountPlans)
        .where(
          and(
            eq(discountPlans.businessId, businessId),
            ilike(discountPlans.planName, createDiscountPlanDto.planName),
            eq(discountPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingDiscountPlan) {
        throw new ConflictException(
          `Discount plan with name "${createDiscountPlanDto.planName}" already exists`,
        );
      }

      // Validate date range
      const startDate = new Date(createDiscountPlanDto.startDate);
      const endDate = new Date(createDiscountPlanDto.endDate);

      if (startDate >= endDate) {
        throw new BadRequestException('Start date must be before end date');
      }

      // Validate schedule conflicts with existing active discount plans
      await this.validateScheduleConflicts(businessId, createDiscountPlanDto);

      // Create the discount plan
      const [newDiscountPlan] = await this.db
        .insert(discountPlans)
        .values({
          businessId,
          planName: createDiscountPlanDto.planName,
          method: createDiscountPlanDto.method,
          discountType: createDiscountPlanDto.discountType as any,
          discountAmount: createDiscountPlanDto.discountAmount,
          applyToReceiptAutomatically:
            createDiscountPlanDto.applyToReceiptAutomatically ?? false,
          applyToCustomer: createDiscountPlanDto.applyToCustomer ?? false,
          applyToAllProducts: createDiscountPlanDto.applyToAllProducts ?? false,
          applyToAllServices: createDiscountPlanDto.applyToAllServices ?? false,
          applyToAllCategories:
            createDiscountPlanDto.applyToAllCategories ?? false,
          applyToAllCustomerGroups:
            createDiscountPlanDto.applyToAllCustomerGroups ?? false,
          applyToAllCustomers:
            createDiscountPlanDto.applyToAllCustomers ?? false,
          startDate: createDiscountPlanDto.startDate,
          endDate: createDiscountPlanDto.endDate,
          schedules: createDiscountPlanDto.schedules,
          status:
            createDiscountPlanDto.status === StatusType.ACTIVE ||
            createDiscountPlanDto.status === StatusType.INACTIVE
              ? createDiscountPlanDto.status
              : StatusType.ACTIVE,
          createdBy: userId,
        })
        .returning();

      // Log the discount plan creation activity
      await this.activityLogService.logCreate(
        newDiscountPlan.id,
        EntityType.DISCOUNT_PLAN,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newDiscountPlan.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create discount plan');
    }
  }

  async findAll(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: DiscountPlanDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(discountPlans.isDeleted, false),
      eq(discountPlans.status, StatusType.ACTIVE),
      eq(discountPlans.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(discountPlans.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(discountPlans.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(discountPlans)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get discount plans with user information
    const discountPlanResults = await this.db
      .select({
        discountPlan: discountPlans,
        createdByUser: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
      })
      .from(discountPlans)
      .leftJoin(users, eq(discountPlans.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(discountPlans.createdAt))
      .limit(limit)
      .offset(offset);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    const data = await Promise.all(
      discountPlanResults.map((result) => this.mapToDiscountPlanDto(result)),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    planName?: string,
    method?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: DiscountPlanListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(discountPlans.isDeleted, false),
      eq(discountPlans.businessId, businessId),
    ];

    // Add individual filter conditions
    const filterConditions = [];

    if (planName) {
      filterConditions.push(ilike(discountPlans.planName, `%${planName}%`));
    }

    if (method) {
      filterConditions.push(eq(discountPlans.method, method as any));
    }

    if (status) {
      filterConditions.push(eq(discountPlans.status, status as any));
    }

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(discountPlans.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(discountPlans.createdAt, toDate));
      }
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        if (Array.isArray(parsedFilters)) {
          const advancedFilterConditions = parsedFilters
            .map((filter) => {
              switch (filter.field) {
                case 'planName':
                  return ilike(discountPlans.planName, `%${filter.value}%`);
                case 'method':
                  return eq(discountPlans.method, filter.value);
                case 'discountType':
                  return eq(discountPlans.discountType, filter.value);
                case 'status':
                  return eq(discountPlans.status, filter.value);
                default:
                  return null;
              }
            })
            .filter(Boolean);

          if (advancedFilterConditions.length > 0) {
            if (joinOperator === 'or') {
              filterConditions.push(or(...advancedFilterConditions));
            } else {
              filterConditions.push(...advancedFilterConditions);
            }
          }
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Combine all conditions
    const allConditions = [...whereConditions];
    if (filterConditions.length > 0) {
      if (joinOperator === 'or') {
        allConditions.push(or(...filterConditions));
      } else {
        allConditions.push(...filterConditions);
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(discountPlans)
      .leftJoin(users, eq(discountPlans.createdBy, users.id))
      .where(and(...allConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Build sort conditions
    let orderByClause: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'planName':
          orderByClause = isDesc
            ? desc(discountPlans.planName)
            : asc(discountPlans.planName);
          break;
        case 'method':
          orderByClause = isDesc
            ? desc(discountPlans.method)
            : asc(discountPlans.method);
          break;
        case 'discountAmount':
          orderByClause = isDesc
            ? desc(discountPlans.discountAmount)
            : asc(discountPlans.discountAmount);
          break;
        case 'startDate':
          orderByClause = isDesc
            ? desc(discountPlans.startDate)
            : asc(discountPlans.startDate);
          break;
        case 'endDate':
          orderByClause = isDesc
            ? desc(discountPlans.endDate)
            : asc(discountPlans.endDate);
          break;
        case 'status':
          orderByClause = isDesc
            ? desc(discountPlans.status)
            : asc(discountPlans.status);
          break;
        case 'createdAt':
          orderByClause = isDesc
            ? desc(discountPlans.createdAt)
            : asc(discountPlans.createdAt);
          break;
        default:
          orderByClause = desc(discountPlans.createdAt);
      }
    } else {
      orderByClause = desc(discountPlans.createdAt);
    }

    // Get discount plans with optimized fields
    const discountPlanResults = await this.db
      .select({
        id: discountPlans.id,
        planName: discountPlans.planName,
        method: discountPlans.method,
        discountType: discountPlans.discountType,
        discountAmount: discountPlans.discountAmount,
        applyToReceiptAutomatically: discountPlans.applyToReceiptAutomatically,
        applyToCustomer: discountPlans.applyToCustomer,
        startDate: discountPlans.startDate,
        endDate: discountPlans.endDate,
        status: discountPlans.status,
        createdAt: discountPlans.createdAt,
        updatedAt: discountPlans.updatedAt,
        createdByUser: {
          firstName: users.firstName,
          lastName: users.lastName,
        },
      })
      .from(discountPlans)
      .leftJoin(users, eq(discountPlans.createdBy, users.id))
      .where(and(...allConditions))
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    const data = discountPlanResults.map((result) => ({
      id: result.id,
      planName: result.planName,
      method: result.method,
      discountType: result.discountType,
      discountAmount: result.discountAmount,
      applyToReceiptAutomatically: result.applyToReceiptAutomatically,
      applyToCustomer: result.applyToCustomer,
      startDate: result.startDate,
      endDate: result.endDate,
      status: result.status,
      createdBy: result.createdByUser
        ? `${result.createdByUser.firstName} ${result.createdByUser.lastName}`.trim()
        : 'Unknown',
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    id: string,
    _metadata?: ActivityMetadata,
  ): Promise<DiscountPlanDto> {
    // Get the discount plan
    const discountPlan = await this.db
      .select()
      .from(discountPlans)
      .where(and(eq(discountPlans.id, id), eq(discountPlans.isDeleted, false)))
      .then((results) => results[0]);

    if (!discountPlan) {
      throw new NotFoundException(`Discount plan with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== discountPlan.businessId
    ) {
      throw new UnauthorizedException('Access denied to this discount plan');
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return this.mapToDiscountPlanDto({
      discountPlan,
      createdByUser: null, // Will be populated by mapToDiscountPlanDto
    });
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateDiscountPlanDto: UpdateDiscountPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<DiscountPlanDto> {
    // Get the discount plan
    const existingDiscountPlan = await this.db
      .select()
      .from(discountPlans)
      .where(and(eq(discountPlans.id, id), eq(discountPlans.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingDiscountPlan) {
      throw new NotFoundException(`Discount plan with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingDiscountPlan.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this discount plan',
      );
    }

    // Check for name conflicts if name is being updated
    if (
      updateDiscountPlanDto.planName &&
      updateDiscountPlanDto.planName !== existingDiscountPlan.planName
    ) {
      const existingWithSameName = await this.db
        .select()
        .from(discountPlans)
        .where(
          and(
            eq(discountPlans.businessId, businessId),
            ilike(discountPlans.planName, updateDiscountPlanDto.planName),
            eq(discountPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingWithSameName) {
        throw new ConflictException(
          `Discount plan with name "${updateDiscountPlanDto.planName}" already exists`,
        );
      }
    }

    // Validate date range if dates are being updated
    const startDate = updateDiscountPlanDto.startDate
      ? new Date(updateDiscountPlanDto.startDate)
      : new Date(existingDiscountPlan.startDate);
    const endDate = updateDiscountPlanDto.endDate
      ? new Date(updateDiscountPlanDto.endDate)
      : new Date(existingDiscountPlan.endDate);

    if (startDate >= endDate) {
      throw new BadRequestException('Start date must be before end date');
    }

    // Validate schedule conflicts if relevant fields are being updated
    if (
      updateDiscountPlanDto.startDate ||
      updateDiscountPlanDto.endDate ||
      updateDiscountPlanDto.schedules ||
      updateDiscountPlanDto.method ||
      updateDiscountPlanDto.applyToAllProducts ||
      updateDiscountPlanDto.applyToAllServices ||
      updateDiscountPlanDto.applyToAllCategories ||
      updateDiscountPlanDto.applyToAllCustomerGroups ||
      updateDiscountPlanDto.applyToAllCustomers
    ) {
      // Create a merged DTO for validation
      const mergedDto: CreateDiscountPlanDto = {
        planName:
          updateDiscountPlanDto.planName || existingDiscountPlan.planName,
        method: updateDiscountPlanDto.method || existingDiscountPlan.method,
        discountType:
          updateDiscountPlanDto.discountType ||
          existingDiscountPlan.discountType,
        discountAmount:
          updateDiscountPlanDto.discountAmount ||
          existingDiscountPlan.discountAmount,
        startDate:
          updateDiscountPlanDto.startDate || existingDiscountPlan.startDate,
        endDate: updateDiscountPlanDto.endDate || existingDiscountPlan.endDate,
        schedules:
          updateDiscountPlanDto.schedules ||
          (existingDiscountPlan.schedules as ScheduleConfig[]),
        applyToAllProducts:
          updateDiscountPlanDto.applyToAllProducts ??
          existingDiscountPlan.applyToAllProducts,
        applyToAllServices:
          updateDiscountPlanDto.applyToAllServices ??
          existingDiscountPlan.applyToAllServices,
        applyToAllCategories:
          updateDiscountPlanDto.applyToAllCategories ??
          existingDiscountPlan.applyToAllCategories,
        applyToAllCustomerGroups:
          updateDiscountPlanDto.applyToAllCustomerGroups ??
          existingDiscountPlan.applyToAllCustomerGroups,
        applyToAllCustomers:
          updateDiscountPlanDto.applyToAllCustomers ??
          existingDiscountPlan.applyToAllCustomers,
      };

      await this.validateScheduleConflicts(businessId, mergedDto, id);
    }

    try {
      // Update the discount plan
      const updateData: any = {
        ...updateDiscountPlanDto,
        updatedAt: new Date(),
        updatedBy: userId,
      };

      // Ensure status is valid if provided
      if (
        updateData.status &&
        updateData.status !== StatusType.ACTIVE &&
        updateData.status !== StatusType.INACTIVE
      ) {
        delete updateData.status;
      }

      const [updatedDiscountPlan] = await this.db
        .update(discountPlans)
        .set(updateData)
        .where(eq(discountPlans.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        updatedDiscountPlan.id,
        EntityType.DISCOUNT_PLAN,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.mapToDiscountPlanDto({
        discountPlan: updatedDiscountPlan,
        createdByUser: null, // Will be populated by mapToDiscountPlanDto
      });
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update discount plan');
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    // Get the discount plan
    const existingDiscountPlan = await this.db
      .select()
      .from(discountPlans)
      .where(and(eq(discountPlans.id, id), eq(discountPlans.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingDiscountPlan) {
      throw new NotFoundException(`Discount plan with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingDiscountPlan.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this discount plan',
      );
    }

    try {
      // Soft delete the discount plan
      await this.db
        .update(discountPlans)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(discountPlans.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        existingDiscountPlan.id,
        EntityType.DISCOUNT_PLAN,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete discount plan');
    }
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<DiscountPlanSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all discount plans with only essential fields
    const discountPlanResults = await this.db
      .select({
        id: discountPlans.id,
        planName: discountPlans.planName,
        method: discountPlans.method,
        discountType: discountPlans.discountType,
        discountAmount: discountPlans.discountAmount,
        startDate: discountPlans.startDate,
        endDate: discountPlans.endDate,
        status: discountPlans.status,
      })
      .from(discountPlans)
      .where(
        and(
          eq(discountPlans.isDeleted, false),
          eq(discountPlans.status, StatusType.ACTIVE),
          eq(discountPlans.businessId, businessId),
        ),
      )
      .orderBy(asc(discountPlans.planName), asc(discountPlans.id));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return discountPlanResults.map((result) => ({
      id: result.id,
      planName: result.planName,
      method: result.method,
      discountType: result.discountType,
      discountAmount: result.discountAmount,
      startDate: result.startDate,
      endDate: result.endDate,
      status: result.status,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createDiscountPlanDto: CreateDiscountPlanDto,
  ): Promise<{ id: string }> {
    const discountPlan = await this.create(
      userId,
      businessId,
      createDiscountPlanDto,
    );
    return { id: discountPlan.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateDiscountPlanDto: UpdateDiscountPlanDto,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateDiscountPlanDto);
    return { id };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    discountPlanIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedCount: number;
    deletedIds: string[];
    failedIds: string[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!discountPlanIds || discountPlanIds.length === 0) {
      throw new BadRequestException('No discount plan IDs provided');
    }

    // Verify all discount plans exist and belong to the business
    const existingDiscountPlans = await this.db
      .select({
        id: discountPlans.id,
        planName: discountPlans.planName,
      })
      .from(discountPlans)
      .where(
        and(
          inArray(discountPlans.id, discountPlanIds),
          eq(discountPlans.businessId, businessId),
          eq(discountPlans.isDeleted, false),
        ),
      );

    const existingIds = existingDiscountPlans.map((dp) => dp.id);
    const failedIds = discountPlanIds.filter((id) => !existingIds.includes(id));

    if (existingIds.length === 0) {
      return {
        deletedCount: 0,
        deletedIds: [],
        failedIds: discountPlanIds,
      };
    }

    try {
      // Soft delete all existing discount plans
      await this.db
        .update(discountPlans)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(discountPlans.id, existingIds));

      // Log bulk delete activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.DISCOUNT_PLAN,
        existingIds,
        { isDeleted: true },
        userId,
        businessId,
        {
          filterCriteria: { discountPlanIds },
          failures: failedIds.map((id) => ({
            id,
            error: 'Not found or already deleted',
          })),
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedCount: existingIds.length,
        deletedIds: existingIds,
        failedIds,
      };
    } catch {
      throw new BadRequestException('Failed to bulk delete discount plans');
    }
  }

  // Helper method to map database result to DiscountPlanDto
  private async mapToDiscountPlanDto(result: {
    discountPlan: any;
    createdByUser: any;
  }): Promise<DiscountPlanDto> {
    const { discountPlan } = result;
    let { createdByUser } = result;

    // If createdByUser is not provided, fetch it
    if (!createdByUser && discountPlan.createdBy) {
      const user = await this.db.query.users.findFirst({
        where: eq(users.id, discountPlan.createdBy),
        columns: {
          id: true,
          firstName: true,
          lastName: true,
        },
      });
      createdByUser = user;
    }

    // Get updatedBy user if exists
    let updatedByUser = null;
    if (discountPlan.updatedBy) {
      updatedByUser = await this.db.query.users.findFirst({
        where: eq(users.id, discountPlan.updatedBy),
        columns: {
          id: true,
          firstName: true,
          lastName: true,
        },
      });
    }

    return {
      id: discountPlan.id,
      businessId: discountPlan.businessId,
      planName: discountPlan.planName,
      method: discountPlan.method,
      discountType: discountPlan.discountType,
      discountAmount: discountPlan.discountAmount,
      applyToReceiptAutomatically: discountPlan.applyToReceiptAutomatically,
      applyToCustomer: discountPlan.applyToCustomer,
      applyToAllProducts: discountPlan.applyToAllProducts,
      applyToAllServices: discountPlan.applyToAllServices,
      applyToAllCategories: discountPlan.applyToAllCategories,
      applyToAllCustomerGroups: discountPlan.applyToAllCustomerGroups,
      applyToAllCustomers: discountPlan.applyToAllCustomers,
      startDate: discountPlan.startDate,
      endDate: discountPlan.endDate,
      schedules: discountPlan.schedules,
      status: discountPlan.status,
      createdBy: createdByUser
        ? `${createdByUser.firstName} ${createdByUser.lastName}`.trim()
        : 'Unknown',
      updatedBy: updatedByUser
        ? `${updatedByUser.firstName} ${updatedByUser.lastName}`.trim()
        : undefined,
      createdAt: discountPlan.createdAt,
      updatedAt: discountPlan.updatedAt,
    };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createDiscountPlanDtos: CreateDiscountPlanDto[],
  ): Promise<DiscountPlanDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!createDiscountPlanDtos || createDiscountPlanDtos.length === 0) {
      throw new BadRequestException('No discount plans provided');
    }

    // Check for duplicate names within the batch
    const planNames = createDiscountPlanDtos.map((dto) =>
      dto.planName.toLowerCase(),
    );
    const duplicateNames = planNames.filter(
      (name, index) => planNames.indexOf(name) !== index,
    );
    if (duplicateNames.length > 0) {
      throw new ConflictException(
        `Duplicate discount plan names in batch: ${duplicateNames.join(', ')}`,
      );
    }

    // Check for existing discount plans with the same names
    const existingDiscountPlans = await this.db
      .select({ planName: discountPlans.planName })
      .from(discountPlans)
      .where(
        and(
          eq(discountPlans.businessId, businessId),
          or(
            ...createDiscountPlanDtos.map((dto) =>
              ilike(discountPlans.planName, dto.planName),
            ),
          ),
          eq(discountPlans.isDeleted, false),
        ),
      );

    if (existingDiscountPlans.length > 0) {
      const existingNames = existingDiscountPlans.map((dp) => dp.planName);
      throw new ConflictException(
        `Discount plans with these names already exist: ${existingNames.join(', ')}`,
      );
    }

    const createdDiscountPlans: DiscountPlanDto[] = [];

    // Create discount plans in a transaction
    await this.db.transaction(async (tx) => {
      for (const createDiscountPlanDto of createDiscountPlanDtos) {
        // Validate date range
        const startDate = new Date(createDiscountPlanDto.startDate);
        const endDate = new Date(createDiscountPlanDto.endDate);

        if (startDate >= endDate) {
          throw new BadRequestException(
            `Start date must be before end date for discount plan "${createDiscountPlanDto.planName}"`,
          );
        }

        // Validate schedule conflicts for this discount plan
        await this.validateScheduleConflicts(businessId, createDiscountPlanDto);

        // Create the discount plan
        const [newDiscountPlan] = await tx
          .insert(discountPlans)
          .values({
            businessId,
            planName: createDiscountPlanDto.planName,
            method: createDiscountPlanDto.method,
            discountType: createDiscountPlanDto.discountType as any,
            discountAmount: createDiscountPlanDto.discountAmount,
            applyToReceiptAutomatically:
              createDiscountPlanDto.applyToReceiptAutomatically ?? false,
            applyToCustomer: createDiscountPlanDto.applyToCustomer ?? false,
            applyToAllProducts:
              createDiscountPlanDto.applyToAllProducts ?? false,
            applyToAllServices:
              createDiscountPlanDto.applyToAllServices ?? false,
            applyToAllCategories:
              createDiscountPlanDto.applyToAllCategories ?? false,
            applyToAllCustomerGroups:
              createDiscountPlanDto.applyToAllCustomerGroups ?? false,
            applyToAllCustomers:
              createDiscountPlanDto.applyToAllCustomers ?? false,
            startDate: createDiscountPlanDto.startDate,
            endDate: createDiscountPlanDto.endDate,
            schedules: createDiscountPlanDto.schedules,
            status:
              createDiscountPlanDto.status === StatusType.ACTIVE ||
              createDiscountPlanDto.status === StatusType.INACTIVE
                ? createDiscountPlanDto.status
                : StatusType.ACTIVE,
            createdBy: userId,
          })
          .returning();

        // Log the discount plan creation activity
        await this.activityLogService.logCreate(
          newDiscountPlan.id,
          EntityType.DISCOUNT_PLAN,
          userId,
          businessId,
          {
            reason: 'Bulk create operation',
            source: ActivitySource.WEB,
          },
        );

        createdDiscountPlans.push(
          await this.mapToDiscountPlanDto({
            discountPlan: newDiscountPlan,
            createdByUser: null,
          }),
        );
      }
    });

    return createdDiscountPlans;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createDiscountPlanDtos: CreateDiscountPlanDto[],
  ): Promise<{ ids: string[]; count: number }> {
    const createdDiscountPlans = await this.bulkCreate(
      userId,
      businessId,
      createDiscountPlanDtos,
    );

    return {
      ids: createdDiscountPlans.map((dp) => dp.id),
      count: createdDiscountPlans.length,
    };
  }
}
