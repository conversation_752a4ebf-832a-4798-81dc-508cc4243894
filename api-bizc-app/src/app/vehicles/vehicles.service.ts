import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { VehicleDto } from './dto/vehicle.dto';
import { VehicleSlimDto } from './dto/vehicle-slim.dto';
import { VehicleListDto } from './dto/vehicle-list.dto';
import { vehicles } from '../drizzle/schema/vehicles.schema';
import { media } from '../drizzle/schema/media.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { VehicleStatus } from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MediaService } from '../media/media.service';

@Injectable()
export class VehiclesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createVehicleDto: CreateVehicleDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check for unique constraints
      await this.validateUniqueFields(businessId, createVehicleDto);

      // Create slug from make/model if not provided
      if (
        !createVehicleDto.slug &&
        (createVehicleDto.make || createVehicleDto.model)
      ) {
        const identifier =
          `${createVehicleDto.make || ''} ${createVehicleDto.model || ''} ${createVehicleDto.year || ''}`.trim();
        createVehicleDto.slug = identifier
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');
      }

      // Validate rental pricing if available for rent
      if (createVehicleDto.availableForRent) {
        if (
          !createVehicleDto.rentalPriceWithDriver ||
          !createVehicleDto.rentalPriceWithoutDriver
        ) {
          throw new BadRequestException(
            'Rental prices are required when vehicle is available for rent',
          );
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'vehicles',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'vehicles/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Use a transaction to ensure position reordering and vehicle creation are atomic
      const newVehicle = await this.db.transaction(async (tx) => {
        // Shift all existing vehicles down by 1 position to make room at position 1 within the vehicle type
        if (createVehicleDto.vehicleTypeId) {
          await this.reorderPositions(
            tx,
            businessId,
            createVehicleDto.vehicleTypeId,
            1,
          );
        }

        // Insert new vehicle at position 1
        const [vehicle] = await tx
          .insert(vehicles)
          .values({
            businessId,
            vin: createVehicleDto.vin,
            vehicleKey: createVehicleDto.vehicleKey,
            licensePlate: createVehicleDto.licensePlate,
            make: createVehicleDto.make,
            model: createVehicleDto.model,
            vehicleTypeId: createVehicleDto.vehicleTypeId,
            subTypeId: createVehicleDto.subTypeId,
            year: createVehicleDto.year,
            color: createVehicleDto.color,
            images: createVehicleDto.images ?? [],
            availableDate: createVehicleDto.availableDate,
            availableUntil: createVehicleDto.availableUntil,
            odometer: createVehicleDto.odometer,
            fuelLevel: createVehicleDto.fuelLevel,
            vehicleEntryDate: createVehicleDto.vehicleEntryDate,
            vehicleExitDate: createVehicleDto.vehicleExitDate,
            currentLocationId: createVehicleDto.currentLocationId,
            image: mediaId,
            ogImage: ogImageId,
            slug: createVehicleDto.slug,
            seoTitle: createVehicleDto.seoTitle,
            seoDescription: createVehicleDto.seoDescription,
            seoKeywords: createVehicleDto.seoKeywords,
            position: 1, // Always create new vehicles at position 1 (first)
            availableForRent: createVehicleDto.availableForRent ?? false,
            rentalPriceWithDriver: createVehicleDto.rentalPriceWithDriver,
            rentalPriceWithoutDriver: createVehicleDto.rentalPriceWithoutDriver,
            hardwareId: createVehicleDto.hardwareId,
            coolantTemperatureThreshold:
              createVehicleDto.coolantTemperatureThreshold,
            lowBatteryVoltageThreshold:
              createVehicleDto.lowBatteryVoltageThreshold,
            purchasePrice: createVehicleDto.purchasePrice,
            expectedRestValue: createVehicleDto.expectedRestValue,
            odometerAtPurchase: createVehicleDto.odometerAtPurchase,
            purchaseDate: createVehicleDto.purchaseDate,
            fixedAssetAccountId: createVehicleDto.fixedAssetAccountId,
            depreciationAccountId: createVehicleDto.depreciationAccountId,
            expenseAccountId: createVehicleDto.expenseAccountId,
            createdBy: userId,
            status: createVehicleDto.status ?? VehicleStatus.AVAILABLE,
          })
          .returning();

        return vehicle;
      });

      // Log the vehicle creation activity
      await this.activityLogService.logCreate(
        newVehicle.id,
        EntityType.VEHICLE,
        userId,
        businessId,
        {
          reason: `Created vehicle: ${createVehicleDto.make} ${createVehicleDto.model}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newVehicle.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create vehicle: ${error.message}`,
      );
    }
  }

  private async validateUniqueFields(
    businessId: string,
    vehicleDto: CreateVehicleDto | UpdateVehicleDto,
    excludeId?: string,
  ): Promise<void> {
    // Check VIN uniqueness
    if (vehicleDto.vin) {
      const existingVin = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.businessId, businessId),
            ilike(vehicles.vin, vehicleDto.vin),
            isNull(vehicles.deletedAt),
            excludeId ? sql`${vehicles.id} != ${excludeId}` : undefined,
          ),
        )
        .then((results) => results[0]);

      if (existingVin) {
        throw new ConflictException(
          `A vehicle with VIN '${vehicleDto.vin}' already exists for this business`,
        );
      }
    }

    // Check license plate uniqueness
    if (vehicleDto.licensePlate) {
      const existingLicensePlate = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.businessId, businessId),
            ilike(vehicles.licensePlate, vehicleDto.licensePlate),
            isNull(vehicles.deletedAt),
            excludeId ? sql`${vehicles.id} != ${excludeId}` : undefined,
          ),
        )
        .then((results) => results[0]);

      if (existingLicensePlate) {
        throw new ConflictException(
          `A vehicle with license plate '${vehicleDto.licensePlate}' already exists for this business`,
        );
      }
    }

    // Check slug uniqueness
    if (vehicleDto.slug) {
      const existingSlug = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.businessId, businessId),
            eq(vehicles.slug, vehicleDto.slug),
            isNull(vehicles.deletedAt),
            excludeId ? sql`${vehicles.id} != ${excludeId}` : undefined,
          ),
        )
        .then((results) => results[0]);

      if (existingSlug) {
        throw new ConflictException(
          `A vehicle with slug '${vehicleDto.slug}' already exists for this business`,
        );
      }
    }
  }

  /**
   * Reorder positions to make room for a new vehicle at the specified position within a vehicle type
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    vehicleTypeId: string,
    insertPosition: number,
  ): Promise<void> {
    // Shift all vehicles at or after the insert position down by 1 within the same vehicle type
    await tx
      .update(vehicles)
      .set({
        position: sql`${vehicles.position} + 1`,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(vehicles.businessId, businessId),
          eq(vehicles.vehicleTypeId, vehicleTypeId),
          sql`${vehicles.position} >= ${insertPosition}`,
          isNull(vehicles.deletedAt),
        ),
      );
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createVehicleDto: CreateVehicleDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(
      userId,
      businessId,
      createVehicleDto,
      imageFile,
      ogImageFile,
      metadata,
    );
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: VehicleDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(vehicles.deletedAt),
      eq(vehicles.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(vehicles.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(vehicles.createdAt, toDate));
      }
    }

    // Find all vehicles for the user's active business with pagination
    const result = await this.db
      .select()
      .from(vehicles)
      .where(and(...whereConditions))
      .orderBy(
        asc(vehicles.vehicleTypeId),
        asc(vehicles.position),
        asc(vehicles.id),
      )
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicles)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: await Promise.all(
        result.map((vehicle) => this.mapToVehicleDto(vehicle)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    search?: string,
    slug?: string,
    status?: string,
    availableForRent?: string,
    sort?: string,
  ): Promise<{
    data: VehicleListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(vehicles.deletedAt),
      eq(vehicles.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(vehicles.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(vehicles.createdAt, toDate));
      }
    }

    // Add search filtering if provided (searches make, model, VIN, license plate)
    if (search) {
      whereConditions.push(
        or(
          ilike(vehicles.make, `%${search}%`),
          ilike(vehicles.model, `%${search}%`),
          ilike(vehicles.vin, `%${search}%`),
          ilike(vehicles.licensePlate, `%${search}%`),
        ),
      );
    }

    // Add slug filtering if provided
    if (slug) {
      whereConditions.push(ilike(vehicles.slug, `%${slug}%`));
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as VehicleStatus);
      whereConditions.push(inArray(vehicles.status, statusArray));
    }

    // Add availableForRent filtering if provided
    if (availableForRent) {
      const decodedAvailableForRent = decodeURIComponent(availableForRent);
      const availableForRentValues = decodedAvailableForRent
        .split(',')
        .map((s) => s.trim());
      if (availableForRentValues.length === 1) {
        const boolValue = availableForRentValues[0].toLowerCase() === 'true';
        whereConditions.push(eq(vehicles.availableForRent, boolValue));
      } else {
        const boolValues = availableForRentValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(vehicles.availableForRent, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(vehicles.availableForRent, false));
        }
      }
    }

    // Parse sort configuration
    let orderByClause = [
      asc(vehicles.vehicleTypeId),
      asc(vehicles.position),
      asc(vehicles.id),
    ]; // Default sort by vehicle type, then position
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        if (Array.isArray(sortConfig) && sortConfig.length > 0) {
          orderByClause = sortConfig.map((sortItem) => {
            const { id: fieldId, desc: isDesc } = sortItem;
            const direction = isDesc ? desc : asc;

            switch (fieldId) {
              case 'make':
                return direction(vehicles.make);
              case 'model':
                return direction(vehicles.model);
              case 'year':
                return direction(vehicles.year);
              case 'position':
                return direction(vehicles.position);
              case 'createdAt':
                return direction(vehicles.createdAt);
              case 'updatedAt':
                return direction(vehicles.updatedAt);
              default:
                return direction(vehicles.position);
            }
          });
        }
      } catch {
        // If sort parsing fails, use default sort
      }
    }

    // Execute query with joins for optimized data
    const result = await this.db
      .select({
        id: vehicles.id,
        businessId: vehicles.businessId,
        vin: vehicles.vin,
        licensePlate: vehicles.licensePlate,
        make: vehicles.make,
        model: vehicles.model,
        year: vehicles.year,
        color: vehicles.color,
        odometer: vehicles.odometer,
        fuelLevel: vehicles.fuelLevel,
        slug: vehicles.slug,
        position: vehicles.position,
        availableForRent: vehicles.availableForRent,
        rentalPriceWithDriver: vehicles.rentalPriceWithDriver,
        rentalPriceWithoutDriver: vehicles.rentalPriceWithoutDriver,
        status: vehicles.status,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        // Joined data
        currentLocationName: locations.name,
        imageUrl: media.publicUrl,
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
      })
      .from(vehicles)
      .leftJoin(locations, eq(vehicles.currentLocationId, locations.id))
      .leftJoin(media, eq(vehicles.image, media.id))
      .leftJoin(users, eq(vehicles.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(...orderByClause)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicles)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: result.map((vehicle) => this.mapToVehicleListDto(vehicle)),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<VehicleDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const vehicle = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          eq(vehicles.id, id),
          eq(vehicles.businessId, businessId),
          isNull(vehicles.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!vehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    // Disable view logging for performance

    return this.mapToVehicleDto(vehicle);
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<VehicleSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: vehicles.id,
        vin: vehicles.vin,
        licensePlate: vehicles.licensePlate,
        make: vehicles.make,
        model: vehicles.model,
        year: vehicles.year,
        status: vehicles.status,
        availableForRent: vehicles.availableForRent,
        position: vehicles.position,
      })
      .from(vehicles)
      .where(
        and(isNull(vehicles.deletedAt), eq(vehicles.businessId, businessId)),
      )
      .orderBy(
        asc(vehicles.vehicleTypeId),
        asc(vehicles.position),
        asc(vehicles.id),
      );

    // Disable view logging for performance in optimized queries

    return result.map((vehicle) => this.mapToVehicleSlimDto(vehicle));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleDto: UpdateVehicleDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if vehicle exists
      const existingVehicle = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.id, id),
            eq(vehicles.businessId, businessId),
            isNull(vehicles.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingVehicle) {
        throw new NotFoundException('Vehicle not found');
      }

      // Check for unique constraints (excluding current vehicle)
      await this.validateUniqueFields(businessId, updateVehicleDto, id);

      // Validate rental pricing if available for rent
      if (updateVehicleDto.availableForRent) {
        if (
          !updateVehicleDto.rentalPriceWithDriver ||
          !updateVehicleDto.rentalPriceWithoutDriver
        ) {
          throw new BadRequestException(
            'Rental prices are required when vehicle is available for rent',
          );
        }
      }

      let mediaId: string | undefined = existingVehicle.image;
      let ogImageId: string | undefined = existingVehicle.ogImage;

      // Upload new image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'vehicles',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload new OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'vehicles/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Update vehicle
      const [updatedVehicle] = await this.db
        .update(vehicles)
        .set({
          vin: updateVehicleDto.vin ?? existingVehicle.vin,
          vehicleKey: updateVehicleDto.vehicleKey ?? existingVehicle.vehicleKey,
          licensePlate:
            updateVehicleDto.licensePlate ?? existingVehicle.licensePlate,
          make: updateVehicleDto.make ?? existingVehicle.make,
          model: updateVehicleDto.model ?? existingVehicle.model,
          vehicleTypeId:
            updateVehicleDto.vehicleTypeId ?? existingVehicle.vehicleTypeId,
          subTypeId: updateVehicleDto.subTypeId ?? existingVehicle.subTypeId,
          year: updateVehicleDto.year ?? existingVehicle.year,
          color: updateVehicleDto.color ?? existingVehicle.color,
          images: updateVehicleDto.images ?? existingVehicle.images,
          availableDate:
            updateVehicleDto.availableDate ?? existingVehicle.availableDate,
          availableUntil:
            updateVehicleDto.availableUntil ?? existingVehicle.availableUntil,
          odometer: updateVehicleDto.odometer ?? existingVehicle.odometer,
          fuelLevel: updateVehicleDto.fuelLevel ?? existingVehicle.fuelLevel,
          vehicleEntryDate:
            updateVehicleDto.vehicleEntryDate ??
            existingVehicle.vehicleEntryDate,
          vehicleExitDate:
            updateVehicleDto.vehicleExitDate ?? existingVehicle.vehicleExitDate,
          currentLocationId:
            updateVehicleDto.currentLocationId ??
            existingVehicle.currentLocationId,
          image: mediaId,
          ogImage: ogImageId,
          slug: updateVehicleDto.slug ?? existingVehicle.slug,
          seoTitle: updateVehicleDto.seoTitle ?? existingVehicle.seoTitle,
          seoDescription:
            updateVehicleDto.seoDescription ?? existingVehicle.seoDescription,
          seoKeywords:
            updateVehicleDto.seoKeywords ?? existingVehicle.seoKeywords,
          availableForRent:
            updateVehicleDto.availableForRent ??
            existingVehicle.availableForRent,
          rentalPriceWithDriver:
            updateVehicleDto.rentalPriceWithDriver ??
            existingVehicle.rentalPriceWithDriver,
          rentalPriceWithoutDriver:
            updateVehicleDto.rentalPriceWithoutDriver ??
            existingVehicle.rentalPriceWithoutDriver,
          hardwareId: updateVehicleDto.hardwareId ?? existingVehicle.hardwareId,
          coolantTemperatureThreshold:
            updateVehicleDto.coolantTemperatureThreshold ??
            existingVehicle.coolantTemperatureThreshold,
          lowBatteryVoltageThreshold:
            updateVehicleDto.lowBatteryVoltageThreshold ??
            existingVehicle.lowBatteryVoltageThreshold,
          purchasePrice:
            updateVehicleDto.purchasePrice ?? existingVehicle.purchasePrice,
          expectedRestValue:
            updateVehicleDto.expectedRestValue ??
            existingVehicle.expectedRestValue,
          odometerAtPurchase:
            updateVehicleDto.odometerAtPurchase ??
            existingVehicle.odometerAtPurchase,
          purchaseDate:
            updateVehicleDto.purchaseDate ?? existingVehicle.purchaseDate,
          fixedAssetAccountId:
            updateVehicleDto.fixedAssetAccountId ??
            existingVehicle.fixedAssetAccountId,
          depreciationAccountId:
            updateVehicleDto.depreciationAccountId ??
            existingVehicle.depreciationAccountId,
          expenseAccountId:
            updateVehicleDto.expenseAccountId ??
            existingVehicle.expenseAccountId,
          status: updateVehicleDto.status ?? existingVehicle.status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(vehicles.id, id))
        .returning();

      // Log the vehicle update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.VEHICLE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedVehicle.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update vehicle: ${error.message}`,
      );
    }
  }

  // Mapping methods
  private async mapToVehicleDto(vehicle: any): Promise<VehicleDto> {
    // Get related data
    const [
      currentLocation,
      imageMedia,
      ogImageMedia,
      fixedAssetAccount,
      depreciationAccount,
      expenseAccount,
      createdByUser,
      updatedByUser,
    ] = await Promise.all([
      vehicle.currentLocationId
        ? this.db
            .select({ id: locations.id, name: locations.name })
            .from(locations)
            .where(eq(locations.id, vehicle.currentLocationId))
            .then((results) => results[0])
        : null,
      vehicle.image
        ? this.db
            .select({ id: media.id, publicUrl: media.publicUrl })
            .from(media)
            .where(eq(media.id, vehicle.image))
            .then((results) => results[0])
        : null,
      vehicle.ogImage
        ? this.db
            .select({ id: media.id, publicUrl: media.publicUrl })
            .from(media)
            .where(eq(media.id, vehicle.ogImage))
            .then((results) => results[0])
        : null,
      vehicle.fixedAssetAccountId
        ? this.db
            .select({ id: accounts.id, accountName: accounts.accountName })
            .from(accounts)
            .where(eq(accounts.id, vehicle.fixedAssetAccountId))
            .then((results) => results[0])
        : null,
      vehicle.depreciationAccountId
        ? this.db
            .select({ id: accounts.id, accountName: accounts.accountName })
            .from(accounts)
            .where(eq(accounts.id, vehicle.depreciationAccountId))
            .then((results) => results[0])
        : null,
      vehicle.expenseAccountId
        ? this.db
            .select({ id: accounts.id, accountName: accounts.accountName })
            .from(accounts)
            .where(eq(accounts.id, vehicle.expenseAccountId))
            .then((results) => results[0])
        : null,
      this.db
        .select({ firstName: users.firstName, lastName: users.lastName })
        .from(users)
        .where(eq(users.id, vehicle.createdBy))
        .then((results) => results[0]),
      vehicle.updatedBy
        ? this.db
            .select({ firstName: users.firstName, lastName: users.lastName })
            .from(users)
            .where(eq(users.id, vehicle.updatedBy))
            .then((results) => results[0])
        : null,
    ]);

    return {
      id: vehicle.id,
      businessId: vehicle.businessId,
      vin: vehicle.vin,
      vehicleKey: vehicle.vehicleKey,
      licensePlate: vehicle.licensePlate,
      make: vehicle.make,
      model: vehicle.model,
      vehicleTypeId: vehicle.vehicleTypeId,
      subTypeId: vehicle.subTypeId,
      year: vehicle.year,
      color: vehicle.color,
      images: vehicle.images,
      availableDate: vehicle.availableDate,
      availableUntil: vehicle.availableUntil,
      odometer: vehicle.odometer,
      fuelLevel: vehicle.fuelLevel,
      vehicleEntryDate: vehicle.vehicleEntryDate,
      vehicleExitDate: vehicle.vehicleExitDate,
      currentLocation: currentLocation,
      image: imageMedia?.publicUrl,
      ogImage: ogImageMedia?.publicUrl,
      slug: vehicle.slug,
      seoTitle: vehicle.seoTitle,
      seoDescription: vehicle.seoDescription,
      seoKeywords: vehicle.seoKeywords,
      position: vehicle.position,
      availableForRent: vehicle.availableForRent,
      rentalPriceWithDriver: vehicle.rentalPriceWithDriver,
      rentalPriceWithoutDriver: vehicle.rentalPriceWithoutDriver,
      hardwareId: vehicle.hardwareId,
      coolantTemperatureThreshold: vehicle.coolantTemperatureThreshold,
      lowBatteryVoltageThreshold: vehicle.lowBatteryVoltageThreshold,
      purchasePrice: vehicle.purchasePrice,
      expectedRestValue: vehicle.expectedRestValue,
      odometerAtPurchase: vehicle.odometerAtPurchase,
      purchaseDate: vehicle.purchaseDate,
      fixedAssetAccount: fixedAssetAccount,
      depreciationAccount: depreciationAccount,
      expenseAccount: expenseAccount,
      createdBy:
        `${createdByUser?.firstName || ''} ${createdByUser?.lastName || ''}`.trim(),
      updatedBy: updatedByUser
        ? `${updatedByUser.firstName || ''} ${updatedByUser.lastName || ''}`.trim()
        : undefined,
      status: vehicle.status,
      createdAt: vehicle.createdAt,
      updatedAt: vehicle.updatedAt,
    };
  }

  private mapToVehicleListDto(vehicle: any): VehicleListDto {
    return {
      id: vehicle.id,
      businessId: vehicle.businessId,
      vin: vehicle.vin,
      licensePlate: vehicle.licensePlate,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      color: vehicle.color,
      odometer: vehicle.odometer,
      fuelLevel: vehicle.fuelLevel,
      currentLocationName: vehicle.currentLocationName,
      imageUrl: vehicle.imageUrl,
      slug: vehicle.slug,
      position: vehicle.position,
      availableForRent: vehicle.availableForRent,
      rentalPriceWithDriver: vehicle.rentalPriceWithDriver,
      rentalPriceWithoutDriver: vehicle.rentalPriceWithoutDriver,
      status: vehicle.status,
      createdByName: vehicle.createdByName || 'Unknown',
      updatedByName: vehicle.updatedByName,
      createdAt: vehicle.createdAt,
      updatedAt: vehicle.updatedAt,
    };
  }

  private mapToVehicleSlimDto(vehicle: any): VehicleSlimDto {
    return {
      id: vehicle.id,
      vin: vehicle.vin,
      licensePlate: vehicle.licensePlate,
      make: vehicle.make,
      model: vehicle.model,
      year: vehicle.year,
      status: vehicle.status,
      availableForRent: vehicle.availableForRent,
      position: vehicle.position,
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if vehicle exists
    const existingVehicle = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          eq(vehicles.id, id),
          eq(vehicles.businessId, businessId),
          isNull(vehicles.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingVehicle) {
      throw new NotFoundException('Vehicle not found');
    }

    // Use a transaction to ensure position reordering and vehicle deletion are atomic
    await this.db.transaction(async (tx) => {
      // Soft delete the vehicle
      await tx
        .update(vehicles)
        .set({
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(vehicles.id, id));

      // Reorder positions to fill the gap within the vehicle type
      await this.reorderPositionsAfterDeletion(
        tx,
        businessId,
        existingVehicle.vehicleTypeId,
        existingVehicle.position,
      );
    });

    // Log the vehicle deletion activity
    await this.activityLogService.logDelete(
      id,
      EntityType.VEHICLE,
      userId,
      businessId,
      {
        reason: `Deleted vehicle: ${existingVehicle.make} ${existingVehicle.model}`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id: existingVehicle.id,
      message: 'Vehicle deleted successfully',
    };
  }

  /**
   * Reorder positions after a vehicle is deleted to fill the gap within a vehicle type
   */
  private async reorderPositionsAfterDeletion(
    tx: any,
    businessId: string,
    vehicleTypeId: string,
    deletedPosition: number,
  ): Promise<void> {
    // Shift all vehicles after the deleted position up by 1 within the same vehicle type
    await tx
      .update(vehicles)
      .set({
        position: sql`${vehicles.position} - 1`,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(vehicles.businessId, businessId),
          eq(vehicles.vehicleTypeId, vehicleTypeId),
          sql`${vehicles.position} > ${deletedPosition}`,
          isNull(vehicles.deletedAt),
        ),
      );
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createVehicleDtos: CreateVehicleDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; count: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!createVehicleDtos || createVehicleDtos.length === 0) {
      throw new BadRequestException('At least one vehicle is required');
    }

    try {
      // Validate all vehicles before creating any
      for (const dto of createVehicleDtos) {
        await this.validateUniqueFields(businessId, dto);

        // Validate rental pricing if available for rent
        if (dto.availableForRent) {
          if (!dto.rentalPriceWithDriver || !dto.rentalPriceWithoutDriver) {
            throw new BadRequestException(
              'Rental prices are required when vehicle is available for rent',
            );
          }
        }
      }

      const createdVehicles = await this.db.transaction(async (tx) => {
        const vehicleIds: string[] = [];

        for (let i = 0; i < createVehicleDtos.length; i++) {
          const dto = createVehicleDtos[i];

          // Create slug from make/model if not provided
          if (!dto.slug && (dto.make || dto.model)) {
            const identifier =
              `${dto.make || ''} ${dto.model || ''} ${dto.year || ''}`.trim();
            dto.slug = identifier
              .toLowerCase()
              .replace(/\s+/g, '-')
              .replace(/[^a-z0-9-]/g, '');
          }

          // Shift all existing vehicles down by 1 position to make room at position 1 within the vehicle type
          if (dto.vehicleTypeId) {
            await this.reorderPositions(tx, businessId, dto.vehicleTypeId, 1);
          }

          // Insert new vehicle at position 1
          const [vehicle] = await tx
            .insert(vehicles)
            .values({
              businessId,
              vin: dto.vin,
              vehicleKey: dto.vehicleKey,
              licensePlate: dto.licensePlate,
              make: dto.make,
              model: dto.model,
              vehicleTypeId: dto.vehicleTypeId,
              subTypeId: dto.subTypeId,
              year: dto.year,
              color: dto.color,
              images: dto.images ?? [],
              availableDate: dto.availableDate,
              availableUntil: dto.availableUntil,
              odometer: dto.odometer,
              fuelLevel: dto.fuelLevel,
              vehicleEntryDate: dto.vehicleEntryDate,
              vehicleExitDate: dto.vehicleExitDate,
              currentLocationId: dto.currentLocationId,
              slug: dto.slug,
              seoTitle: dto.seoTitle,
              seoDescription: dto.seoDescription,
              seoKeywords: dto.seoKeywords,
              position: 1, // Always create new vehicles at position 1 (first)
              availableForRent: dto.availableForRent ?? false,
              rentalPriceWithDriver: dto.rentalPriceWithDriver,
              rentalPriceWithoutDriver: dto.rentalPriceWithoutDriver,
              hardwareId: dto.hardwareId,
              coolantTemperatureThreshold: dto.coolantTemperatureThreshold,
              lowBatteryVoltageThreshold: dto.lowBatteryVoltageThreshold,
              purchasePrice: dto.purchasePrice,
              expectedRestValue: dto.expectedRestValue,
              odometerAtPurchase: dto.odometerAtPurchase,
              purchaseDate: dto.purchaseDate,
              fixedAssetAccountId: dto.fixedAssetAccountId,
              depreciationAccountId: dto.depreciationAccountId,
              expenseAccountId: dto.expenseAccountId,
              createdBy: userId,
              status: dto.status ?? VehicleStatus.AVAILABLE,
            })
            .returning();

          vehicleIds.push(vehicle.id);
        }

        return vehicleIds;
      });

      // Log bulk create activity
      for (const vehicleId of createdVehicles) {
        await this.activityLogService.logCreate(
          vehicleId,
          EntityType.VEHICLE,
          userId,
          businessId,
          {
            reason: `Bulk created vehicle`,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        ids: createdVehicles,
        count: createdVehicles.length,
        message: `Successfully created ${createdVehicles.length} vehicles`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create vehicles: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedIds: string[]; deletedCount: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('At least one vehicle ID is required');
    }

    // Check if all vehicles exist
    const existingVehicles = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          inArray(vehicles.id, ids),
          eq(vehicles.businessId, businessId),
          isNull(vehicles.deletedAt),
        ),
      );

    if (existingVehicles.length !== ids.length) {
      const foundIds = existingVehicles.map((v) => v.id);
      const notFoundIds = ids.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Vehicles not found: ${notFoundIds.join(', ')}`,
      );
    }

    // Use a transaction to ensure position reordering and vehicle deletion are atomic
    const deletedIds = await this.db.transaction(async (tx) => {
      // Soft delete all vehicles
      await tx
        .update(vehicles)
        .set({
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(vehicles.id, ids));

      // Reorder positions to fill gaps within each vehicle type (sort by position to handle correctly)
      const sortedVehicles = existingVehicles.sort(
        (a, b) => a.position - b.position,
      );
      for (const vehicle of sortedVehicles) {
        await this.reorderPositionsAfterDeletion(
          tx,
          businessId,
          vehicle.vehicleTypeId,
          vehicle.position,
        );
      }

      return ids;
    });

    // Log bulk delete activity
    for (const vehicleId of deletedIds) {
      await this.activityLogService.logDelete(
        vehicleId,
        EntityType.VEHICLE,
        userId,
        businessId,
        {
          reason: `Bulk deleted vehicle`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      deletedIds,
      deletedCount: deletedIds.length,
      message: `Successfully deleted ${deletedIds.length} vehicles`,
    };
  }

  async updatePositions(
    userId: string,
    businessId: string | null,
    updates: Array<{ id: string; position: number }>,
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!updates || updates.length === 0) {
      throw new BadRequestException('At least one position update is required');
    }

    // Validate that all vehicles exist and belong to the business
    const vehicleIds = updates.map((update) => update.id);
    const existingVehicles = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          inArray(vehicles.id, vehicleIds),
          eq(vehicles.businessId, businessId),
          isNull(vehicles.deletedAt),
        ),
      );

    if (existingVehicles.length !== vehicleIds.length) {
      const foundIds = existingVehicles.map((v) => v.id);
      const notFoundIds = vehicleIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `Vehicles not found: ${notFoundIds.join(', ')}`,
      );
    }

    // Group vehicles by vehicle type for validation
    const vehiclesByType = new Map<string, any[]>();
    for (const vehicle of existingVehicles) {
      if (!vehiclesByType.has(vehicle.vehicleTypeId)) {
        vehiclesByType.set(vehicle.vehicleTypeId, []);
      }
      vehiclesByType.get(vehicle.vehicleTypeId).push(vehicle);
    }

    // Validate position values within each vehicle type scope
    const positions = updates.map((update) => update.position);
    const minPosition = Math.min(...positions);

    if (minPosition < 1) {
      throw new BadRequestException('Position must be 1 or greater');
    }

    // Validate positions within each vehicle type scope
    for (const [vehicleTypeId, typeVehicles] of vehiclesByType) {
      // Get updates for this vehicle type
      const typeUpdates = updates.filter((update) =>
        typeVehicles.some((v) => v.id === update.id),
      );

      if (typeUpdates.length === 0) continue;

      // Get total count of vehicles in this vehicle type
      const totalVehiclesInType = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(vehicles)
        .where(
          and(
            eq(vehicles.businessId, businessId),
            eq(vehicles.vehicleTypeId, vehicleTypeId),
            isNull(vehicles.deletedAt),
          ),
        )
        .then((result) => Number(result[0].count));

      const maxPositionInType = Math.max(
        ...typeUpdates.map((update) => update.position),
      );

      if (maxPositionInType > totalVehiclesInType) {
        throw new BadRequestException(
          `Position cannot be greater than total vehicles in vehicle type (${totalVehiclesInType})`,
        );
      }
    }

    // Update positions in a transaction
    await this.db.transaction(async (tx) => {
      for (const update of updates) {
        await tx
          .update(vehicles)
          .set({
            position: update.position,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(vehicles.id, update.id));
      }
    });

    // Log position update activity
    for (const update of updates) {
      await this.activityLogService.logUpdate(
        update.id,
        EntityType.VEHICLE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      updated: updates.length,
      message: `Successfully updated ${updates.length} vehicle positions`,
    };
  }

  async checkVinAvailability(
    businessId: string | null,
    vin: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingVehicle = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          eq(vehicles.businessId, businessId),
          ilike(vehicles.vin, vin),
          isNull(vehicles.deletedAt),
          excludeId ? sql`${vehicles.id} != ${excludeId}` : undefined,
        ),
      )
      .then((results) => results[0]);

    return { available: !existingVehicle };
  }

  async checkLicensePlateAvailability(
    businessId: string | null,
    licensePlate: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingVehicle = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          eq(vehicles.businessId, businessId),
          ilike(vehicles.licensePlate, licensePlate),
          isNull(vehicles.deletedAt),
          excludeId ? sql`${vehicles.id} != ${excludeId}` : undefined,
        ),
      )
      .then((results) => results[0]);

    return { available: !existingVehicle };
  }

  async checkSlugAvailability(
    businessId: string | null,
    slug: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingVehicle = await this.db
      .select()
      .from(vehicles)
      .where(
        and(
          eq(vehicles.businessId, businessId),
          eq(vehicles.slug, slug),
          isNull(vehicles.deletedAt),
          excludeId ? sql`${vehicles.id} != ${excludeId}` : undefined,
        ),
      )
      .then((results) => results[0]);

    return { available: !existingVehicle };
  }
}
