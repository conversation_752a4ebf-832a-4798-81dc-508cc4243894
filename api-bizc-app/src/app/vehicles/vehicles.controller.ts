import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  ParseUUIDPipe,
  ValidationPipe,
  UsePipes,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiParam,
  ApiConsumes,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { VehiclesService } from './vehicles.service';
import { CreateVehicleDto } from './dto/create-vehicle.dto';
import { UpdateVehicleDto } from './dto/update-vehicle.dto';
import { VehicleDto } from './dto/vehicle.dto';
import { VehicleSlimDto } from './dto/vehicle-slim.dto';

import { VehicleIdResponseDto } from './dto/vehicle-id-response.dto';
import { PaginatedVehiclesResponseDto } from './dto/paginated-vehicles-response.dto';
import { BulkCreateVehicleDto } from './dto/bulk-create-vehicle.dto';
import { BulkVehicleIdsResponseDto } from './dto/bulk-vehicle-ids-response.dto';
import { BulkDeleteVehicleDto } from './dto/bulk-delete-vehicle.dto';
import { BulkDeleteVehicleResponseDto } from './dto/bulk-delete-vehicle-response.dto';
import { DeleteVehicleResponseDto } from './dto/delete-vehicle-response.dto';
import {
  UpdateVehiclePositionsDto,
  UpdateVehiclePositionsResponseDto,
} from './dto/update-vehicle-positions.dto';
import { VehicleAvailabilityResponseDto } from './dto/check-vehicle-availability.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

interface AuthenticatedRequest {
  user: {
    id: string;
    activeBusinessId: string | null;
  };
}

@ApiTags('Vehicles')
@Controller('vehicles')
@UseGuards(PermissionsGuard)
export class VehiclesController {
  constructor(private readonly vehiclesService: VehiclesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_CREATE)
  @ApiOperation({
    summary: 'Create a new vehicle',
    description: 'Creates a new vehicle with optional image uploads',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Vehicle data with optional image files',
    type: CreateVehicleDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Vehicle created successfully',
    type: VehicleIdResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 409,
    description: 'Conflict - duplicate field values',
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @UsePipes(new ValidationPipe({ transform: true }))
  async create(
    @Request() req: AuthenticatedRequest,
    @Body() createVehicleDto: CreateVehicleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @UploadedFiles()
    files?: {
      image?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    },
  ): Promise<VehicleIdResponseDto> {
    const imageFile = files?.image?.[0];
    const ogImageFile = files?.ogImage?.[0];

    return this.vehiclesService.create(
      req.user.id,
      req.user.activeBusinessId,
      createVehicleDto,
      imageFile,
      ogImageFile,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Get all vehicles',
    description: 'Retrieves a paginated list of vehicles for the business',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Start date for filtering (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'End date for filtering (YYYY-MM-DD)',
    example: '2023-12-31',
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicles retrieved successfully',
    type: PaginatedVehiclesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(
    @Request() req: AuthenticatedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<PaginatedVehiclesResponseDto> {
    return this.vehiclesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
    );
  }

  @Get('optimized')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Get optimized vehicles list',
    description:
      'Retrieves a paginated, optimized list of vehicles with search and filtering capabilities',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Start date for filtering (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'End date for filtering (YYYY-MM-DD)',
    example: '2023-12-31',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search term for make, model, VIN, or license plate',
    example: 'Toyota',
  })
  @ApiQuery({
    name: 'slug',
    required: false,
    type: String,
    description: 'Filter by slug',
    example: 'toyota-camry-2023',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status (comma-separated for multiple)',
    example: 'available,dirty',
  })
  @ApiQuery({
    name: 'availableForRent',
    required: false,
    type: String,
    description: 'Filter by rental availability (true/false)',
    example: 'true',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    description: 'Sort configuration as JSON string',
    example: '[{"id":"make","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description: 'Optimized vehicles list retrieved successfully',
    type: PaginatedVehiclesResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAllOptimized(
    @Request() req: AuthenticatedRequest,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('search') search?: string,
    @Query('slug') slug?: string,
    @Query('status') status?: string,
    @Query('availableForRent') availableForRent?: string,
    @Query('sort') sort?: string,
  ): Promise<PaginatedVehiclesResponseDto> {
    return this.vehiclesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      search,
      slug,
      status,
      availableForRent,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Get vehicles in slim format',
    description:
      'Retrieves all vehicles with minimal data for dropdowns and selections',
  })
  @ApiResponse({
    status: 200,
    description: 'Slim vehicles list retrieved successfully',
    type: [VehicleSlimDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAllSlim(
    @Request() req: AuthenticatedRequest,
  ): Promise<VehicleSlimDto[]> {
    return this.vehiclesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Get vehicle by ID',
    description: 'Retrieves a specific vehicle by its ID',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Vehicle ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle retrieved successfully',
    type: VehicleDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Vehicle not found' })
  async findOne(
    @Request() req: AuthenticatedRequest,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<VehicleDto> {
    return this.vehiclesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_UPDATE)
  @ApiOperation({
    summary: 'Update vehicle',
    description: 'Updates an existing vehicle with optional image uploads',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Vehicle ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Vehicle update data with optional image files',
    type: UpdateVehicleDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle updated successfully',
    type: VehicleIdResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Vehicle not found' })
  @ApiResponse({
    status: 409,
    description: 'Conflict - duplicate field values',
  })
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @UsePipes(new ValidationPipe({ transform: true }))
  async update(
    @Request() req: AuthenticatedRequest,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateVehicleDto: UpdateVehicleDto,
    @UploadedFiles()
    files?: {
      image?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    },
  ): Promise<VehicleIdResponseDto> {
    const imageFile = files?.image?.[0];
    const ogImageFile = files?.ogImage?.[0];

    return this.vehiclesService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateVehicleDto,
      imageFile,
      ogImageFile,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_DELETE)
  @ApiOperation({
    summary: 'Delete vehicle',
    description: 'Soft deletes a vehicle and reorders positions',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Vehicle ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle deleted successfully',
    type: DeleteVehicleResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Vehicle not found' })
  async remove(
    @Request() req: AuthenticatedRequest,
    @Param('id', ParseUUIDPipe) id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteVehicleResponseDto> {
    return this.vehiclesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_CREATE)
  @ApiOperation({
    summary: 'Create multiple vehicles',
    description: 'Creates multiple vehicles in a single operation',
  })
  @ApiBody({
    description: 'Array of vehicles to create',
    type: BulkCreateVehicleDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Vehicles created successfully',
    type: BulkVehicleIdsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({
    status: 409,
    description: 'Conflict - duplicate field values',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async bulkCreate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkCreateVehicleDto: BulkCreateVehicleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkVehicleIdsResponseDto> {
    return this.vehiclesService.bulkCreate(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateVehicleDto.vehicles,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_DELETE)
  @ApiOperation({
    summary: 'Delete multiple vehicles',
    description: 'Soft deletes multiple vehicles and reorders positions',
  })
  @ApiBody({
    description: 'Array of vehicle IDs to delete',
    type: BulkDeleteVehicleDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicles deleted successfully',
    type: BulkDeleteVehicleResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Some vehicles not found' })
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true }))
  async bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteVehicleDto: BulkDeleteVehicleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteVehicleResponseDto> {
    return this.vehiclesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteVehicleDto.ids,
      metadata,
    );
  }

  @Patch('positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_UPDATE)
  @ApiOperation({
    summary: 'Update vehicle positions',
    description: 'Updates the display order positions of multiple vehicles',
  })
  @ApiBody({
    description: 'Array of vehicle position updates',
    type: UpdateVehiclePositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Vehicle positions updated successfully',
    type: UpdateVehiclePositionsResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Some vehicles not found' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async updatePositions(
    @Request() req: AuthenticatedRequest,
    @Body() updatePositionsDto: UpdateVehiclePositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateVehiclePositionsResponseDto> {
    return this.vehiclesService.updatePositions(
      req.user.id,
      req.user.activeBusinessId,
      updatePositionsDto.updates,
      metadata,
    );
  }

  @Get('check-availability/vin/:vin')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Check VIN availability',
    description: 'Checks if a VIN is available for use in the business',
  })
  @ApiParam({
    name: 'vin',
    type: String,
    description: 'VIN to check',
    example: '1HGBH41JXMN109186',
  })
  @ApiQuery({
    name: 'excludeId',
    required: false,
    type: String,
    description: 'Vehicle ID to exclude from the check (for updates)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'VIN availability checked successfully',
    type: VehicleAvailabilityResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async checkVinAvailability(
    @Request() req: AuthenticatedRequest,
    @Param('vin') vin: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<VehicleAvailabilityResponseDto> {
    return this.vehiclesService.checkVinAvailability(
      req.user.activeBusinessId,
      vin,
      excludeId,
    );
  }

  @Get('check-availability/license-plate/:licensePlate')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Check license plate availability',
    description:
      'Checks if a license plate is available for use in the business',
  })
  @ApiParam({
    name: 'licensePlate',
    type: String,
    description: 'License plate to check',
    example: 'ABC-1234',
  })
  @ApiQuery({
    name: 'excludeId',
    required: false,
    type: String,
    description: 'Vehicle ID to exclude from the check (for updates)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'License plate availability checked successfully',
    type: VehicleAvailabilityResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async checkLicensePlateAvailability(
    @Request() req: AuthenticatedRequest,
    @Param('licensePlate') licensePlate: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<VehicleAvailabilityResponseDto> {
    return this.vehiclesService.checkLicensePlateAvailability(
      req.user.activeBusinessId,
      licensePlate,
      excludeId,
    );
  }

  @Get('check-availability/slug/:slug')
  @ApiBearerAuth()
  @RequirePermissions(Permission.VEHICLE_READ)
  @ApiOperation({
    summary: 'Check slug availability',
    description: 'Checks if a slug is available for use in the business',
  })
  @ApiParam({
    name: 'slug',
    type: String,
    description: 'Slug to check',
    example: 'toyota-camry-2023',
  })
  @ApiQuery({
    name: 'excludeId',
    required: false,
    type: String,
    description: 'Vehicle ID to exclude from the check (for updates)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Slug availability checked successfully',
    type: VehicleAvailabilityResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async checkSlugAvailability(
    @Request() req: AuthenticatedRequest,
    @Param('slug') slug: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<VehicleAvailabilityResponseDto> {
    return this.vehiclesService.checkSlugAvailability(
      req.user.activeBusinessId,
      slug,
      excludeId,
    );
  }
}
