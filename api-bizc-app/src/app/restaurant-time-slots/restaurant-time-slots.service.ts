import {
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateRestaurantTimeSlotDto } from './dto/create-restaurant-time-slot.dto';
import { UpdateRestaurantTimeSlotDto } from './dto/update-restaurant-time-slot.dto';
import { RestaurantTimeSlotDto } from './dto/restaurant-time-slot.dto';
import { RestaurantTimeSlotSlimDto } from './dto/restaurant-time-slot-slim.dto';
import { DeleteRestaurantTimeSlotResponseDto } from './dto/delete-restaurant-time-slot-response.dto';
import { RestaurantTimeSlotIdResponseDto } from './dto/restaurant-time-slot-id-response.dto';
import { restaurantTimeSlots } from '../drizzle/schema/restaurant-time-slots.schema';
import { business } from '../drizzle/schema/business.schema';
import { eq, and, isNull, sql } from 'drizzle-orm';
import { SlotStatus } from '../shared/types/time-slot.enum';

@Injectable()
export class RestaurantTimeSlotsService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async create(
    createRestaurantTimeSlotDto: CreateRestaurantTimeSlotDto,
    businessId: string,
    userId: string,
  ): Promise<RestaurantTimeSlotIdResponseDto> {
    // Check if business exists
    const businessExists = await this.db
      .select({ id: business.id })
      .from(business)
      .where(and(eq(business.id, businessId), isNull(business.deletedAt)))
      .limit(1);

    if (!businessExists.length) {
      throw new NotFoundException('Business not found');
    }

    // Check for duplicate name within the same business
    const existingSlot = await this.db
      .select({ id: restaurantTimeSlots.id })
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.businessId, businessId),
          eq(restaurantTimeSlots.name, createRestaurantTimeSlotDto.name),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      )
      .limit(1);

    if (existingSlot.length > 0) {
      throw new ConflictException(
        'Restaurant time slot with this name already exists',
      );
    }

    // Check for overlapping time slots of the same type
    const overlappingSlots = await this.db
      .select({ id: restaurantTimeSlots.id })
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.businessId, businessId),
          eq(
            restaurantTimeSlots.timeSlotType,
            createRestaurantTimeSlotDto.timeSlotType,
          ),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      );

    if (overlappingSlots.length > 0) {
      throw new ConflictException(
        'A time slot of this type already exists for this business',
      );
    }

    // Create the restaurant time slot
    const [newSlot] = await this.db
      .insert(restaurantTimeSlots)
      .values({
        businessId,
        name: createRestaurantTimeSlotDto.name,
        timeSlotType: createRestaurantTimeSlotDto.timeSlotType,
        startTime: createRestaurantTimeSlotDto.startTime,
        endTime: createRestaurantTimeSlotDto.endTime,
        status: (createRestaurantTimeSlotDto.status ||
          SlotStatus.AVAILABLE) as any,
        description: createRestaurantTimeSlotDto.description,
        availableDays: createRestaurantTimeSlotDto.availableDays,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning({ id: restaurantTimeSlots.id });

    return { id: newSlot.id };
  }

  async findAllForBusiness(
    businessId: string,
  ): Promise<RestaurantTimeSlotDto[]> {
    const slots = await this.db
      .select()
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.businessId, businessId),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      )
      .orderBy(restaurantTimeSlots.startTime);

    return slots.map((slot) => ({
      id: slot.id,
      businessId: slot.businessId,
      name: slot.name,
      timeSlotType: slot.timeSlotType,
      startTime: slot.startTime,
      endTime: slot.endTime,
      status: slot.status,
      isActive: slot.isActive,
      description: slot.description,
      availableDays: slot.availableDays,
    }));
  }

  async findOne(
    id: string,
    businessId: string,
  ): Promise<RestaurantTimeSlotDto> {
    const slot = await this.db
      .select()
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.id, id),
          eq(restaurantTimeSlots.businessId, businessId),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      )
      .limit(1);

    if (!slot.length) {
      throw new NotFoundException('Restaurant time slot not found');
    }

    const slotData = slot[0];

    return {
      id: slotData.id,
      businessId: slotData.businessId,
      name: slotData.name,
      timeSlotType: slotData.timeSlotType,
      startTime: slotData.startTime,
      endTime: slotData.endTime,
      status: slotData.status,
      isActive: slotData.isActive,
      description: slotData.description,
      availableDays: slotData.availableDays,
    };
  }

  async update(
    id: string,
    updateRestaurantTimeSlotDto: UpdateRestaurantTimeSlotDto,
    businessId: string,
    userId: string,
  ): Promise<RestaurantTimeSlotIdResponseDto> {
    // Check if slot exists
    const existingSlot = await this.db
      .select({ id: restaurantTimeSlots.id })
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.id, id),
          eq(restaurantTimeSlots.businessId, businessId),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      )
      .limit(1);

    if (!existingSlot.length) {
      throw new NotFoundException('Restaurant time slot not found');
    }

    // Check for duplicate name if name is being updated
    if (updateRestaurantTimeSlotDto.name) {
      const duplicateSlot = await this.db
        .select({ id: restaurantTimeSlots.id })
        .from(restaurantTimeSlots)
        .where(
          and(
            eq(restaurantTimeSlots.businessId, businessId),
            eq(restaurantTimeSlots.name, updateRestaurantTimeSlotDto.name),
            eq(restaurantTimeSlots.isDeleted, false),
            sql`${restaurantTimeSlots.id} != ${id}`,
          ),
        )
        .limit(1);

      if (duplicateSlot.length > 0) {
        throw new ConflictException(
          'Restaurant time slot with this name already exists',
        );
      }
    }

    // Update the slot
    await this.db
      .update(restaurantTimeSlots)
      .set({
        ...updateRestaurantTimeSlotDto,
        ...(updateRestaurantTimeSlotDto.status && {
          status: updateRestaurantTimeSlotDto.status as any,
        }),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(restaurantTimeSlots.id, id));

    return { id };
  }

  async remove(
    id: string,
    businessId: string,
    userId: string,
  ): Promise<DeleteRestaurantTimeSlotResponseDto> {
    // Check if slot exists
    const existingSlot = await this.db
      .select({ id: restaurantTimeSlots.id, name: restaurantTimeSlots.name })
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.id, id),
          eq(restaurantTimeSlots.businessId, businessId),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      )
      .limit(1);

    if (!existingSlot.length) {
      throw new NotFoundException('Restaurant time slot not found');
    }

    // Soft delete the slot
    await this.db
      .update(restaurantTimeSlots)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(restaurantTimeSlots.id, id));

    return {
      message: 'Restaurant time slot deleted successfully',
      id,
    };
  }

  async findSlim(businessId: string): Promise<RestaurantTimeSlotSlimDto[]> {
    const slots = await this.db
      .select({
        id: restaurantTimeSlots.id,
        name: restaurantTimeSlots.name,
        timeSlotType: restaurantTimeSlots.timeSlotType,
        startTime: restaurantTimeSlots.startTime,
        endTime: restaurantTimeSlots.endTime,
        status: restaurantTimeSlots.status,
        isActive: restaurantTimeSlots.isActive,
        availableDays: restaurantTimeSlots.availableDays,
      })
      .from(restaurantTimeSlots)
      .where(
        and(
          eq(restaurantTimeSlots.businessId, businessId),
          eq(restaurantTimeSlots.status, SlotStatus.AVAILABLE),
          eq(restaurantTimeSlots.isActive, true),
          eq(restaurantTimeSlots.isDeleted, false),
        ),
      )
      .orderBy(restaurantTimeSlots.startTime);

    return slots;
  }
}
