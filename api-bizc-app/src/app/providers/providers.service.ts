import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateProviderDto } from './dto/create-provider.dto';
import { UpdateProviderDto } from './dto/update-provider.dto';
import { ProviderDto } from './dto/provider.dto';
import { ProviderSlimDto } from './dto/provider-slim.dto';
import { ProviderListDto } from './dto/provider-list.dto';
import { providers } from '../drizzle/schema/providers.schema';
import { eq, and, isNull, ilike, gte, lte, desc, asc, or } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName, ProviderStatus } from '../shared/types';

@Injectable()
export class ProvidersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createProviderDto: CreateProviderDto,
  ): Promise<ProviderDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a provider with the same display name already exists for this business
      const existingProvider = await this.db
        .select()
        .from(providers)
        .where(
          and(
            eq(providers.businessId, businessId),
            eq(providers.displayName, createProviderDto.displayName),
            eq(providers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingProvider) {
        throw new ConflictException(
          `A provider with the display name '${createProviderDto.displayName}' already exists for this business`,
        );
      }

      // Check if there's already a default provider for this providerType
      const existingDefaultProvider = await this.findDefaultProvider(
        businessId,
        createProviderDto.providerType,
      );

      // Determine if this provider should be default
      let shouldBeDefault = createProviderDto.isDefault ?? false;

      // If no default exists for this providerType, automatically set this as default
      if (!existingDefaultProvider) {
        shouldBeDefault = true;
      }

      // If this provider is set as default, reset all other providers of the same type to non-default
      if (shouldBeDefault) {
        await this.db
          .update(providers)
          .set({ isDefault: false })
          .where(
            and(
              eq(providers.businessId, businessId),
              eq(providers.providerType, createProviderDto.providerType),
              eq(providers.isDeleted, false),
            ),
          );
      }

      // Insert new provider
      const [newProvider] = await this.db
        .insert(providers)
        .values({
          businessId,
          providerType: createProviderDto.providerType,
          providerName: createProviderDto.providerName,
          displayName: createProviderDto.displayName,
          key1: createProviderDto.key1,
          key2: createProviderDto.key2,
          key3: createProviderDto.key3,
          key4: createProviderDto.key4,
          key5: createProviderDto.key5,
          key6: createProviderDto.key6,
          key7: createProviderDto.key7,
          key8: createProviderDto.key8,
          key9: createProviderDto.key9,
          key10: createProviderDto.key10,
          isDefault: shouldBeDefault,
          createdBy: userId,
          status: createProviderDto.status ?? ProviderStatus.ACTIVE,
        })
        .returning();

      return await this.mapToProviderDto(newProvider);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create provider: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ProviderDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(providers.isDeleted, false),
      eq(providers.status, ProviderStatus.ACTIVE),
      eq(providers.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(providers.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(providers.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: providers.id })
      .from(providers)
      .where(and(...whereConditions));

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select()
      .from(providers)
      .where(and(...whereConditions))
      .orderBy(desc(providers.createdAt))
      .limit(limit)
      .offset(offset);

    const mappedProviders = await Promise.all(
      results.map((provider) => this.mapToProviderDto(provider)),
    );

    return {
      data: mappedProviders,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    displayName?: string,
    providerType?: string,
    status?: string,
    isDefault?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ProviderListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const baseConditions = [
      eq(providers.isDeleted, false),
      eq(providers.businessId, businessId),
    ];

    // Build additional filter conditions
    const filterConditions = [];

    // Display name filter
    if (displayName && displayName.trim()) {
      filterConditions.push(ilike(providers.displayName, `%${displayName}%`));
    }

    // Provider type filter
    if (providerType && providerType.trim()) {
      filterConditions.push(eq(providers.providerType, providerType as any));
    }

    // Status filter
    if (status && status.trim()) {
      filterConditions.push(eq(providers.status, status as any));
    }

    // Is default filter
    if (isDefault && isDefault.trim()) {
      filterConditions.push(eq(providers.isDefault, isDefault === 'true'));
    }

    // Date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        filterConditions.push(gte(providers.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        filterConditions.push(lte(providers.createdAt, toDate));
      }
    }

    // Combine conditions based on join operator
    let whereCondition;
    if (filterConditions.length > 0) {
      if (joinOperator === 'or') {
        whereCondition = and(...baseConditions, or(...filterConditions));
      } else {
        whereCondition = and(...baseConditions, ...filterConditions);
      }
    } else {
      whereCondition = and(...baseConditions);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: providers.id })
      .from(providers)
      .where(whereCondition);

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    // Determine sort order
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'displayName':
          orderBy = isDesc
            ? desc(providers.displayName)
            : asc(providers.displayName);
          break;
        case 'providerType':
          orderBy = isDesc
            ? desc(providers.providerType)
            : asc(providers.providerType);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(providers.createdAt)
            : asc(providers.createdAt);
          break;
        case 'updatedAt':
          orderBy = isDesc
            ? desc(providers.updatedAt)
            : asc(providers.updatedAt);
          break;
        default:
          orderBy = desc(providers.createdAt);
      }
    } else {
      orderBy = desc(providers.createdAt);
    }

    // Get paginated results
    const results = await this.db
      .select({
        id: providers.id,
        providerType: providers.providerType,
        providerName: providers.providerName,
        displayName: providers.displayName,
        isDefault: providers.isDefault,
        status: providers.status,
        createdAt: providers.createdAt,
        updatedAt: providers.updatedAt,
      })
      .from(providers)
      .where(whereCondition)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ProviderDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const provider = await this.db
        .select()
        .from(providers)
        .where(
          and(
            eq(providers.id, id),
            eq(providers.businessId, businessId),
            eq(providers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!provider) {
        throw new NotFoundException(`Provider with ID ${id} not found`);
      }

      return await this.mapToProviderDto(provider);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to find provider: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateProviderDto: UpdateProviderDto,
  ): Promise<ProviderDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if provider exists
      const existingProvider = await this.db
        .select()
        .from(providers)
        .where(
          and(
            eq(providers.id, id),
            eq(providers.businessId, businessId),
            eq(providers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProvider) {
        throw new NotFoundException(`Provider with ID ${id} not found`);
      }

      // If display name is being changed, check for conflicts
      if (
        updateProviderDto.displayName &&
        updateProviderDto.displayName !== existingProvider.displayName
      ) {
        const conflictingProvider = await this.db
          .select()
          .from(providers)
          .where(
            and(
              eq(providers.businessId, businessId),
              eq(providers.displayName, updateProviderDto.displayName),
              eq(providers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingProvider) {
          throw new ConflictException(
            `A provider with the display name '${updateProviderDto.displayName}' already exists for this business`,
          );
        }
      }

      // Handle default provider logic
      const providerType =
        updateProviderDto.providerType || existingProvider.providerType;
      let shouldBeDefault = updateProviderDto.isDefault;

      // Check if there's currently a default provider for this providerType
      const existingDefaultProvider = await this.findDefaultProvider(
        businessId,
        providerType,
      );

      // Prevent setting a default provider to non-default
      if (existingProvider.isDefault && shouldBeDefault === false) {
        throw new BadRequestException(
          'Cannot set a default provider to non-default. Only one provider can be default per type.',
        );
      }

      // If no default exists for this providerType and isDefault is not explicitly set to false, make this default
      if (!existingDefaultProvider && shouldBeDefault !== false) {
        shouldBeDefault = true;
      }

      // If this provider is being set as default, reset all other providers of the same type to non-default
      if (shouldBeDefault === true) {
        await this.db
          .update(providers)
          .set({ isDefault: false })
          .where(
            and(
              eq(providers.businessId, businessId),
              eq(providers.providerType, providerType),
              eq(providers.isDeleted, false),
            ),
          );
      }

      // Prepare update data
      const updateData = {
        ...updateProviderDto,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Override isDefault with our calculated value if needed
      if (shouldBeDefault !== undefined) {
        updateData.isDefault = shouldBeDefault;
      }

      // Update provider
      const [updatedProvider] = await this.db
        .update(providers)
        .set(updateData)
        .where(eq(providers.id, id))
        .returning();

      // Log the provider update activity
      // await this.activityLogService.logUpdate(
      //   ActivityLogName.UPDATE,
      //   `Provider "${updatedProvider.displayName}" was updated`,
      //   { id: updatedProvider.id.toString(), type: 'provider' },
      //   { id: userId, type: 'user' },
      //   { providerId: updatedProvider.id, businessId },
      // );

      return await this.mapToProviderDto(updatedProvider);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update provider: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if provider exists
      const existingProvider = await this.db
        .select()
        .from(providers)
        .where(
          and(
            eq(providers.id, id),
            eq(providers.businessId, businessId),
            eq(providers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProvider) {
        throw new NotFoundException(`Provider with ID ${id} not found`);
      }

      // Prevent deletion of default providers
      if (existingProvider.isDefault) {
        throw new BadRequestException(
          'Cannot delete a default provider. Please set another provider as default before deleting this one.',
        );
      }

      // Soft delete the provider
      await this.db
        .update(providers)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(providers.id, id));

      // Log the provider deletion activity
      // await this.activityLogService.log(
      //   ActivityLogName.DELETE,
      //   `Provider "${existingProvider.displayName}" was deleted`,
      //   { id: existingProvider.id.toString(), type: 'provider' },
      //   { id: userId, type: 'user' },
      //   { providerId: existingProvider.id, businessId },
      // );

      return {
        success: true,
        message: 'Provider deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete provider: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ProviderSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: providers.id,
        providerType: providers.providerType,
        providerName: providers.providerName,
        displayName: providers.displayName,
        isDefault: providers.isDefault,
      })
      .from(providers)
      .where(
        and(
          eq(providers.businessId, businessId),
          eq(providers.status, ProviderStatus.ACTIVE),
          eq(providers.isDeleted, false),
        ),
      )
      .orderBy(providers.displayName);

    return results;
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createProviderDto: CreateProviderDto,
  ): Promise<{ id: string }> {
    const provider = await this.create(userId, businessId, createProviderDto);
    return { id: provider.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateProviderDto: UpdateProviderDto,
  ): Promise<{ id: string }> {
    const provider = await this.update(
      userId,
      businessId,
      id,
      updateProviderDto,
    );
    return { id: provider.id };
  }

  private async findDefaultProvider(
    businessId: string,
    providerType: string,
  ): Promise<typeof providers.$inferSelect | undefined> {
    return await this.db
      .select()
      .from(providers)
      .where(
        and(
          eq(providers.businessId, businessId),
          eq(providers.providerType, providerType as any),
          eq(providers.isDefault, true),
          eq(providers.status, ProviderStatus.ACTIVE),
          eq(providers.isDeleted, false),
        ),
      )
      .then((results) => results[0]);
  }

  private async mapToProviderDto(
    provider: typeof providers.$inferSelect,
  ): Promise<ProviderDto> {
    return {
      id: provider.id,
      businessId: provider.businessId,
      providerType: provider.providerType,
      providerName: provider.providerName,
      displayName: provider.displayName,
      key1: provider.key1,
      key2: provider.key2,
      key3: provider.key3,
      key4: provider.key4,
      key5: provider.key5,
      key6: provider.key6,
      key7: provider.key7,
      key8: provider.key8,
      key9: provider.key9,
      key10: provider.key10,
      isDefault: provider.isDefault,
      createdBy: provider.createdBy,
      status: provider.status,
      createdAt: provider.createdAt,
      updatedAt: provider.updatedAt,
    };
  }
}
