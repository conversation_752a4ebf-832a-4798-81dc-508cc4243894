import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { units } from '@app/drizzle/schema/units.schema';
import { users } from '@app/drizzle/schema/users.schema';
import { unitConversions } from '../drizzle/schema/unit-conversions.schema';
import { CreateUnitDto } from './dto/create-unit.dto';
import { UpdateUnitDto } from './dto/update-unit.dto';
import { UnitDto } from './dto/unit.dto';
import { UnitSlimDto } from './dto/unit-slim.dto';
import { PaginatedUnitsResponseDto } from './dto/paginated-units-response.dto';
import { BulkCreateUnitDto } from './dto/bulk-create-unit.dto';
import { BulkDeleteUnitDto } from './dto/bulk-delete-unit.dto';
import { BulkDeleteUnitResponseDto } from './dto/bulk-delete-unit-response.dto';
import {
  BulkUpdateUnitStatusDto,
  BulkUpdateUnitStatusResponseDto,
} from './dto/bulk-update-unit-status.dto';
import { UnitNameAvailabilityResponseDto } from './dto/check-unit-name.dto';
import { CreateUnitConversionDto } from './dto/create-unit-conversion.dto';
import { UpdateUnitConversionDto } from './dto/update-unit-conversion.dto';
import { UnitConversionIdResponseDto } from './dto/unit-conversion-id-response.dto';
import { PaginatedUnitConversionsResponseDto } from './dto/paginated-unit-conversions-response.dto';
import { eq, and, ilike, desc, asc, count } from 'drizzle-orm';
import { UnitStatus } from '../shared/types';

@Injectable()
export class UnitsService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async create(
    createUnitDto: CreateUnitDto,
    businessId: string,
    userId: string,
  ): Promise<{ id: string }> {
    // Check if unit name already exists for this business
    const existingByName = await this.db
      .select({ id: units.id })
      .from(units)
      .where(
        and(
          eq(units.businessId, businessId),
          eq(units.name, createUnitDto.name),
          eq(units.isDeleted, false),
        ),
      )
      .limit(1);

    if (existingByName.length > 0) {
      throw new ConflictException('Unit with this name already exists');
    }

    // Check if unit code already exists for this business
    const existingByCode = await this.db
      .select({ id: units.id })
      .from(units)
      .where(
        and(
          eq(units.businessId, businessId),
          eq(units.code, createUnitDto.code),
          eq(units.isDeleted, false),
        ),
      )
      .limit(1);

    if (existingByCode.length > 0) {
      throw new ConflictException('Unit with this code already exists');
    }

    // If creating as multiple of other unit, validate base unit exists
    if (createUnitDto.isMultipleOfOtherUnit && createUnitDto.baseUnitId) {
      const baseUnit = await this.findOne(createUnitDto.baseUnitId, businessId);

      // Validate that units are in the same category
      if (baseUnit.category !== createUnitDto.category) {
        throw new BadRequestException(
          'Base unit must be in the same category as the new unit',
        );
      }
    }

    const [newUnit] = await this.db
      .insert(units)
      .values({
        businessId,
        name: createUnitDto.name,
        code: createUnitDto.code,
        category: createUnitDto.category,
        isBaseUnit: createUnitDto.isBaseUnit ?? false,
        status: createUnitDto.status ?? UnitStatus.ACTIVE,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning({ id: units.id });

    // Create unit conversion if specified
    if (
      createUnitDto.isMultipleOfOtherUnit &&
      createUnitDto.baseUnitId &&
      createUnitDto.conversionFactor
    ) {
      await this.db.insert(unitConversions).values({
        businessId,
        fromUnitId: newUnit.id,
        toUnitId: createUnitDto.baseUnitId,
        conversionFactor: createUnitDto.conversionFactor,
        createdBy: userId,
        updatedBy: userId,
      });
    }

    return { id: newUnit.id };
  }

  async findAll(
    businessId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: UnitStatus,
    category?: string,
    sortBy: string = 'createdAt',
    sortOrder: 'asc' | 'desc' = 'desc',
  ): Promise<PaginatedUnitsResponseDto> {
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(units.businessId, businessId),
      eq(units.isDeleted, false),
    ];

    if (search) {
      whereConditions.push(ilike(units.name, `%${search}%`));
    }

    if (status) {
      whereConditions.push(eq(units.status, status));
    }

    if (category) {
      whereConditions.push(eq(units.category, category));
    }

    // Build order by
    const orderBy =
      sortOrder === 'desc' ? desc(units[sortBy]) : asc(units[sortBy]);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(units)
      .where(and(...whereConditions));

    // Get units with creator information
    const unitsData = await this.db
      .select({
        id: units.id,
        businessId: units.businessId,
        name: units.name,
        code: units.code,
        category: units.category,
        isBaseUnit: units.isBaseUnit,
        status: units.status,
        createdBy: users.firstName,
        updatedBy: users.firstName,
        createdAt: units.createdAt,
        updatedAt: units.updatedAt,
      })
      .from(units)
      .leftJoin(users, eq(units.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      data: unitsData,
      page,
      limit,
      total,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async findOne(id: string, businessId: string): Promise<UnitDto> {
    const [unit] = await this.db
      .select({
        id: units.id,
        businessId: units.businessId,
        name: units.name,
        code: units.code,
        category: units.category,
        isBaseUnit: units.isBaseUnit,
        status: units.status,
        createdBy: users.firstName,
        updatedBy: users.firstName,
        createdAt: units.createdAt,
        updatedAt: units.updatedAt,
      })
      .from(units)
      .leftJoin(users, eq(units.createdBy, users.id))
      .where(
        and(
          eq(units.id, id),
          eq(units.businessId, businessId),
          eq(units.isDeleted, false),
        ),
      )
      .limit(1);

    if (!unit) {
      throw new NotFoundException('Unit not found');
    }

    // Get conversion relationships for this unit
    const fromConversions = await this.db
      .select({
        id: unitConversions.id,
        businessId: unitConversions.businessId,
        fromUnitId: unitConversions.fromUnitId,
        toUnitId: unitConversions.toUnitId,
        conversionFactor: unitConversions.conversionFactor,
        fromUnitName: units.name,
        fromUnitCode: units.code,
        toUnitName: units.name,
        toUnitCode: units.code,
        createdBy: users.firstName,
        updatedBy: users.firstName,
        createdAt: unitConversions.createdAt,
        updatedAt: unitConversions.updatedAt,
      })
      .from(unitConversions)
      .leftJoin(units, eq(unitConversions.toUnitId, units.id))
      .leftJoin(users, eq(unitConversions.createdBy, users.id))
      .where(
        and(
          eq(unitConversions.fromUnitId, id),
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.isDeleted, false),
        ),
      );

    const toConversions = await this.db
      .select({
        id: unitConversions.id,
        businessId: unitConversions.businessId,
        fromUnitId: unitConversions.fromUnitId,
        toUnitId: unitConversions.toUnitId,
        conversionFactor: unitConversions.conversionFactor,
        fromUnitName: units.name,
        fromUnitCode: units.code,
        toUnitName: units.name,
        toUnitCode: units.code,
        createdBy: users.firstName,
        updatedBy: users.firstName,
        createdAt: unitConversions.createdAt,
        updatedAt: unitConversions.updatedAt,
      })
      .from(unitConversions)
      .leftJoin(units, eq(unitConversions.fromUnitId, units.id))
      .leftJoin(users, eq(unitConversions.createdBy, users.id))
      .where(
        and(
          eq(unitConversions.toUnitId, id),
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.isDeleted, false),
        ),
      );

    return {
      ...unit,
      fromConversions,
      toConversions,
    };
  }

  async findAllSlim(
    businessId: string,
    status?: UnitStatus,
  ): Promise<UnitSlimDto[]> {
    const whereConditions = [
      eq(units.businessId, businessId),
      eq(units.isDeleted, false),
    ];

    if (status) {
      whereConditions.push(eq(units.status, status));
    }

    return await this.db
      .select({
        id: units.id,
        name: units.name,
        code: units.code,
        category: units.category,
        isBaseUnit: units.isBaseUnit,
        status: units.status,
      })
      .from(units)
      .where(and(...whereConditions))
      .orderBy(asc(units.name));
  }

  async update(
    id: string,
    updateUnitDto: UpdateUnitDto,
    businessId: string,
    userId: string,
  ): Promise<{ id: string }> {
    // Check if unit exists
    const existingUnit = await this.findOne(id, businessId);

    // Check for name conflicts if name is being updated
    if (updateUnitDto.name && updateUnitDto.name !== existingUnit.name) {
      const existingByName = await this.db
        .select({ id: units.id })
        .from(units)
        .where(
          and(
            eq(units.businessId, businessId),
            eq(units.name, updateUnitDto.name),
            eq(units.isDeleted, false),
          ),
        )
        .limit(1);

      if (existingByName.length > 0) {
        throw new ConflictException('Unit with this name already exists');
      }
    }

    // Check for code conflicts if code is being updated
    if (updateUnitDto.code && updateUnitDto.code !== existingUnit.code) {
      const existingByCode = await this.db
        .select({ id: units.id })
        .from(units)
        .where(
          and(
            eq(units.businessId, businessId),
            eq(units.code, updateUnitDto.code),
            eq(units.isDeleted, false),
          ),
        )
        .limit(1);

      if (existingByCode.length > 0) {
        throw new ConflictException('Unit with this code already exists');
      }
    }

    await this.db
      .update(units)
      .set({
        ...updateUnitDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(units.id, id),
          eq(units.businessId, businessId),
          eq(units.isDeleted, false),
        ),
      );

    return { id };
  }

  async remove(id: string, businessId: string, userId: string): Promise<void> {
    await this.findOne(id, businessId); // Verify unit exists

    await this.db
      .update(units)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(units.id, id),
          eq(units.businessId, businessId),
          eq(units.isDeleted, false),
        ),
      );
  }

  async bulkCreate(
    bulkCreateUnitDto: BulkCreateUnitDto,
    businessId: string,
    userId: string,
  ): Promise<{ ids: string[] }> {
    const createdIds: string[] = [];

    for (const unitDto of bulkCreateUnitDto.units) {
      const result = await this.create(unitDto, businessId, userId);
      createdIds.push(result.id);
    }

    return { ids: createdIds };
  }

  async bulkDelete(
    bulkDeleteUnitDto: BulkDeleteUnitDto,
    businessId: string,
    userId: string,
  ): Promise<BulkDeleteUnitResponseDto> {
    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of bulkDeleteUnitDto.ids) {
      try {
        await this.remove(id, businessId, userId);
        deletedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    return {
      deletedCount: deletedIds.length,
      deletedIds,
      failedIds,
    };
  }

  async bulkUpdateStatus(
    bulkUpdateStatusDto: BulkUpdateUnitStatusDto,
    businessId: string,
    userId: string,
  ): Promise<BulkUpdateUnitStatusResponseDto> {
    const updatedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of bulkUpdateStatusDto.ids) {
      try {
        await this.update(
          id,
          { status: bulkUpdateStatusDto.status },
          businessId,
          userId,
        );
        updatedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    return {
      updatedCount: updatedIds.length,
      updatedIds,
      failedIds,
    };
  }

  async checkNameAvailability(
    name: string,
    businessId: string,
    excludeId?: string,
  ): Promise<UnitNameAvailabilityResponseDto> {
    const whereConditions = [
      eq(units.businessId, businessId),
      eq(units.name, name),
      eq(units.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(eq(units.id, excludeId));
    }

    const existing = await this.db
      .select({ id: units.id })
      .from(units)
      .where(and(...whereConditions))
      .limit(1);

    const available = existing.length === 0;

    return {
      available,
      message: available
        ? 'Unit name is available'
        : 'Unit name is already taken',
    };
  }

  // Unit Conversion Methods

  async createConversion(
    createUnitConversionDto: CreateUnitConversionDto,
    businessId: string,
    userId: string,
  ): Promise<UnitConversionIdResponseDto> {
    // Validate that both units exist and belong to the business
    const fromUnit = await this.findOne(
      createUnitConversionDto.fromUnitId,
      businessId,
    );
    const toUnit = await this.findOne(
      createUnitConversionDto.toUnitId,
      businessId,
    );

    // Validate that units are in the same category
    if (fromUnit.category !== toUnit.category) {
      throw new BadRequestException(
        'Units must be in the same category to create a conversion',
      );
    }

    // Check if conversion already exists
    const existingConversion = await this.db
      .select()
      .from(unitConversions)
      .where(
        and(
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.fromUnitId, createUnitConversionDto.fromUnitId),
          eq(unitConversions.toUnitId, createUnitConversionDto.toUnitId),
          eq(unitConversions.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (existingConversion) {
      throw new ConflictException(
        'Conversion between these units already exists',
      );
    }

    const [newConversion] = await this.db
      .insert(unitConversions)
      .values({
        businessId,
        fromUnitId: createUnitConversionDto.fromUnitId,
        toUnitId: createUnitConversionDto.toUnitId,
        conversionFactor: createUnitConversionDto.conversionFactor,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning({ id: unitConversions.id });

    return { id: newConversion.id };
  }

  async updateConversion(
    id: string,
    updateUnitConversionDto: UpdateUnitConversionDto,
    businessId: string,
    userId: string,
  ): Promise<{ id: string }> {
    // Check if conversion exists
    const existingConversion = await this.db
      .select()
      .from(unitConversions)
      .where(
        and(
          eq(unitConversions.id, id),
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingConversion) {
      throw new NotFoundException('Unit conversion not found');
    }

    await this.db
      .update(unitConversions)
      .set({
        ...updateUnitConversionDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(unitConversions.id, id),
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.isDeleted, false),
        ),
      );

    return { id };
  }

  async deleteConversion(
    id: string,
    businessId: string,
    userId: string,
  ): Promise<void> {
    const existingConversion = await this.db
      .select()
      .from(unitConversions)
      .where(
        and(
          eq(unitConversions.id, id),
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingConversion) {
      throw new NotFoundException('Unit conversion not found');
    }

    await this.db
      .update(unitConversions)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(unitConversions.id, id),
          eq(unitConversions.businessId, businessId),
          eq(unitConversions.isDeleted, false),
        ),
      );
  }

  async findConversionsForUnit(
    unitId: string,
    businessId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedUnitConversionsResponseDto> {
    const offset = (page - 1) * limit;

    // Build where conditions for conversions involving this unit
    const whereConditions = [
      eq(unitConversions.businessId, businessId),
      eq(unitConversions.isDeleted, false),
    ];

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(unitConversions)
      .where(and(...whereConditions));

    // Get conversions with unit information
    const conversionsData = await this.db
      .select({
        id: unitConversions.id,
        businessId: unitConversions.businessId,
        fromUnitId: unitConversions.fromUnitId,
        toUnitId: unitConversions.toUnitId,
        conversionFactor: unitConversions.conversionFactor,
        fromUnitName: units.name,
        fromUnitCode: units.code,
        toUnitName: units.name,
        toUnitCode: units.code,
        createdBy: users.firstName,
        updatedBy: users.firstName,
        createdAt: unitConversions.createdAt,
        updatedAt: unitConversions.updatedAt,
      })
      .from(unitConversions)
      .leftJoin(units, eq(unitConversions.fromUnitId, units.id))
      .leftJoin(users, eq(unitConversions.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(unitConversions.createdAt))
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      data: conversionsData,
      total,
      page,
      limit,
      totalPages,
    };
  }
}
