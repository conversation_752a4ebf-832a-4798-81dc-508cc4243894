import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class WorkOrderPriorityListDto {
  @ApiProperty({
    description: 'Work order priority ID',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Priority code for the work order priority',
    example: 'HIGH',
  })
  priorityCode: string;

  @ApiProperty({
    description: 'Priority name for the work order priority',
    example: 'High Priority',
  })
  priorityName: string;

  @ApiPropertyOptional({
    description: 'Description of the work order priority',
    example: 'High priority work orders that require immediate attention',
    nullable: true,
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Color code in hex format for the priority',
    example: '#FF0000',
    nullable: true,
  })
  colorCode?: string;

  @ApiPropertyOptional({
    description: 'Icon name for the priority',
    example: 'alert-triangle',
    nullable: true,
  })
  iconName?: string;

  @ApiProperty({
    description: 'Severity level of the priority (1-10, where 10 is highest)',
    example: 8,
  })
  severityLevel: number;

  @ApiProperty({
    description: 'Display position of the priority',
    example: 1,
  })
  position: number;

  @ApiProperty({
    description: 'Whether the priority is active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Whether this is the default priority',
    example: false,
  })
  isDefault: boolean;

  @ApiPropertyOptional({
    description: 'Escalation time in hours for this priority',
    example: 24,
    nullable: true,
  })
  escalationHours?: number;

}
