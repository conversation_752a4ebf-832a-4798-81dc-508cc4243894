import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { WorkOrderPrioritiesService } from './work-order-priorities.service';
import { CreateWorkOrderPriorityDto } from './dto/create-work-order-priority.dto';
import { UpdateWorkOrderPriorityDto } from './dto/update-work-order-priority.dto';
import { BulkCreateWorkOrderPriorityDto } from './dto/bulk-create-work-order-priority.dto';
import { BulkDeleteWorkOrderPriorityDto } from './dto/bulk-delete-work-order-priority.dto';
import {
  UpdateWorkOrderPriorityPositionsDto,
  UpdateWorkOrderPriorityPositionsResponseDto,
} from './dto/update-work-order-priority-positions.dto';
import {
  BulkUpdateWorkOrderPriorityStatusDto,
  BulkUpdateWorkOrderPriorityStatusResponseDto,
} from './dto/bulk-update-work-order-priority-status.dto';
import { WorkOrderPriorityIdResponseDto } from './dto/work-order-priority-id-response.dto';
import { BulkWorkOrderPriorityIdsResponseDto } from './dto/bulk-work-order-priority-ids-response.dto';
import { BulkDeleteWorkOrderPriorityResponseDto } from './dto/bulk-delete-work-order-priority-response.dto';
import { DeleteWorkOrderPriorityResponseDto } from './dto/delete-work-order-priority-response.dto';
import { PaginatedWorkOrderPrioritiesResponseDto } from './dto/paginated-work-order-priorities-response.dto';
import { WorkOrderPriorityCodeAvailabilityResponseDto } from './dto/check-work-order-priority-code.dto';
import { WorkOrderPriorityNameAvailabilityResponseDto } from './dto/check-work-order-priority-name.dto';
import { WorkOrderPriorityDto } from './dto/work-order-priority.dto';
import { WorkOrderPrioritySlimDto } from './dto/work-order-priority-slim.dto';
import { FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('work-order-priorities')
@Controller('work-order-priorities')
@UseGuards(PermissionsGuard)
export class WorkOrderPrioritiesController {
  constructor(
    private readonly workOrderPrioritiesService: WorkOrderPrioritiesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_CREATE)
  @ApiOperation({
    summary: 'Create a new work order priority',
    description: 'Creates a new work order priority for the active business',
  })
  @ApiResponse({
    status: 201,
    description: 'Work order priority created successfully',
    type: WorkOrderPriorityIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Priority code or name already exists',
  })
  create(
    @Request() req,
    @Body() createWorkOrderPriorityDto: CreateWorkOrderPriorityDto,
  ): Promise<WorkOrderPriorityIdResponseDto> {
    return this.workOrderPrioritiesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createWorkOrderPriorityDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk create work order priorities',
    description:
      'Creates multiple work order priorities at once for the active business',
  })
  @ApiResponse({
    status: 201,
    description: 'Work order priorities created successfully',
    type: BulkWorkOrderPriorityIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Priority codes or names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body()
    bulkCreateWorkOrderPriorityDto: BulkCreateWorkOrderPriorityDto,
    @UploadedFiles() images?: Express.Multer.File[],
  ): Promise<BulkWorkOrderPriorityIdsResponseDto> {
    return this.workOrderPrioritiesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateWorkOrderPriorityDto.workOrderPriorities,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_READ)
  @ApiOperation({
    summary: 'Get all work order priorities for the active business',
    description:
      'Retrieves a paginated list of work order priorities with filtering and sorting options',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'priorityCode',
    description: 'Filter by priority code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'priorityName',
    description: 'Filter by priority name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'severityLevel',
    description: 'Filter by severity level',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isActive',
    description: 'Filter by active status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters in JSON format',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort fields and directions (e.g., "position:asc,priorityName:desc")',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Work order priorities retrieved successfully',
    type: PaginatedWorkOrderPrioritiesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('priorityCode') priorityCode?: string,
    @Query('priorityName') priorityName?: string,
    @Query('severityLevel') severityLevel?: string,
    @Query('isActive') isActive?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedWorkOrderPrioritiesResponseDto> {
    return this.workOrderPrioritiesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      priorityCode,
      priorityName,
      severityLevel,
      isActive,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_READ)
  @ApiOperation({
    summary: 'Get all active work order priorities in slim format',
    description:
      'Retrieves all active work order priorities with minimal data for dropdowns and selections',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order priorities retrieved successfully',
    type: [WorkOrderPrioritySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<WorkOrderPrioritySlimDto[]> {
    return this.workOrderPrioritiesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-priority-code/:priorityCode')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_READ)
  @ApiOperation({
    summary: 'Check if priority code is available',
    description:
      'Checks if a priority code is available for use in the active business',
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'ID to exclude from the check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Priority code availability checked',
    type: WorkOrderPriorityCodeAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkPriorityCodeAvailability(
    @Request() req,
    @Param('priorityCode') priorityCode: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<WorkOrderPriorityCodeAvailabilityResponseDto> {
    return this.workOrderPrioritiesService.checkPriorityCodeAvailability(
      req.user.id,
      req.user.activeBusinessId,
      priorityCode,
      excludeId,
    );
  }

  @Get('check-priority-name/:priorityName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_READ)
  @ApiOperation({
    summary: 'Check if priority name is available',
    description:
      'Checks if a priority name is available for use in the active business',
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'ID to exclude from the check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Priority name availability checked',
    type: WorkOrderPriorityNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkPriorityNameAvailability(
    @Request() req,
    @Param('priorityName') priorityName: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<WorkOrderPriorityNameAvailabilityResponseDto> {
    return this.workOrderPrioritiesService.checkPriorityNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      priorityName,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_READ)
  @ApiOperation({
    summary: 'Get a work order priority by ID',
    description: 'Retrieves a specific work order priority by its ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order priority retrieved successfully',
    type: WorkOrderPriorityDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Work order priority not found',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<WorkOrderPriorityDto> {
    return this.workOrderPrioritiesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_UPDATE)
  @ApiOperation({
    summary: 'Update work order priority positions',
    description:
      'Updates the display positions of multiple work order priorities',
  })
  @ApiBody({
    description: 'Array of position updates',
    type: UpdateWorkOrderPriorityPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Positions updated successfully',
    type: UpdateWorkOrderPriorityPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  updatePositions(
    @Request() req,
    @Body() updatePositionsDto: UpdateWorkOrderPriorityPositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateWorkOrderPriorityPositionsResponseDto> {
    return this.workOrderPrioritiesService.updatePositions(
      req.user.id,
      req.user.activeBusinessId,
      updatePositionsDto.updates,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_UPDATE)
  @ApiOperation({
    summary: 'Bulk update work order priority status',
    description: 'Updates the active status of multiple work order priorities',
  })
  @ApiBody({
    description: 'Array of status updates',
    type: BulkUpdateWorkOrderPriorityStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Status updated successfully',
    type: BulkUpdateWorkOrderPriorityStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateWorkOrderPriorityStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateWorkOrderPriorityStatusResponseDto> {
    return this.workOrderPrioritiesService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.updates,
      metadata,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_UPDATE)
  @ApiOperation({
    summary: 'Update a work order priority',
    description: 'Updates an existing work order priority',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order priority updated successfully',
    type: WorkOrderPriorityIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Work order priority not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Priority code or name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateWorkOrderPriorityDto: UpdateWorkOrderPriorityDto,
  ): Promise<WorkOrderPriorityIdResponseDto> {
    return this.workOrderPrioritiesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateWorkOrderPriorityDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_DELETE)
  @ApiOperation({ summary: 'Bulk delete work order priorities' })
  @ApiBody({
    description: 'Array of work order priority IDs to delete',
    type: BulkDeleteWorkOrderPriorityDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Work order priorities deleted successfully',
    type: BulkDeleteWorkOrderPriorityResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteDto: BulkDeleteWorkOrderPriorityDto,
  ): Promise<BulkDeleteWorkOrderPriorityResponseDto> {
    return this.workOrderPrioritiesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteDto.ids,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_PRIORITY_DELETE)
  @ApiOperation({
    summary: 'Delete a work order priority',
    description: 'Soft deletes a work order priority by its ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order priority deleted successfully',
    type: DeleteWorkOrderPriorityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Work order priority not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteWorkOrderPriorityResponseDto> {
    return this.workOrderPrioritiesService.removeAndReturnResponse(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }
}
