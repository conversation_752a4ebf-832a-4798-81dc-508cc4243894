import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateWorkOrderPriorityDto } from './dto/create-work-order-priority.dto';
import { UpdateWorkOrderPriorityDto } from './dto/update-work-order-priority.dto';
import { WorkOrderPriorityDto } from './dto/work-order-priority.dto';
import { WorkOrderPrioritySlimDto } from './dto/work-order-priority-slim.dto';
import { WorkOrderPriorityListDto } from './dto/work-order-priority-list.dto';
import { workOrderPriorities } from '../drizzle/schema/work-order-priorities.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  and,
  eq,
  ilike,
  desc,
  asc,
  or,
  gte,
  lte,
  sql,
  count,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { UsersService } from '../users/users.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import {
  UpdateWorkOrderPriorityPositionItemDto,
  UpdateWorkOrderPriorityPositionsFailureDto,
} from './dto/update-work-order-priority-positions.dto';
import {
  BulkUpdateWorkOrderPriorityStatusItemDto,
  BulkUpdateWorkOrderPriorityStatusFailureDto,
} from './dto/bulk-update-work-order-priority-status.dto';

@Injectable()
export class WorkOrderPrioritiesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createWorkOrderPriorityDto: CreateWorkOrderPriorityDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a priority with the same code already exists for this business
      const existingPriorityByCode = await this.db
        .select()
        .from(workOrderPriorities)
        .where(
          and(
            eq(workOrderPriorities.businessId, businessId),
            ilike(
              workOrderPriorities.priorityCode,
              createWorkOrderPriorityDto.priorityCode,
            ),
            eq(workOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingPriorityByCode) {
        throw new ConflictException(
          `Work order priority with code '${createWorkOrderPriorityDto.priorityCode}' already exists`,
        );
      }

      // Check if a priority with the same name already exists for this business
      const existingPriorityByName = await this.db
        .select()
        .from(workOrderPriorities)
        .where(
          and(
            eq(workOrderPriorities.businessId, businessId),
            ilike(
              workOrderPriorities.priorityName,
              createWorkOrderPriorityDto.priorityName,
            ),
            eq(workOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingPriorityByName) {
        throw new ConflictException(
          `Work order priority with name '${createWorkOrderPriorityDto.priorityName}' already exists`,
        );
      }

      // Get the next position
      const maxPositionResult = await this.db
        .select({
          maxPosition: sql<number>`COALESCE(MAX(${workOrderPriorities.position}), 0)`,
        })
        .from(workOrderPriorities)
        .where(
          and(
            eq(workOrderPriorities.businessId, businessId),
            eq(workOrderPriorities.isDeleted, false),
          ),
        );

      const nextPosition = (maxPositionResult[0]?.maxPosition || 0) + 1;

      // If this is set as default, unset other defaults
      if (createWorkOrderPriorityDto.isDefault) {
        await this.db
          .update(workOrderPriorities)
          .set({ isDefault: false })
          .where(
            and(
              eq(workOrderPriorities.businessId, businessId),
              eq(workOrderPriorities.isDefault, true),
              eq(workOrderPriorities.isDeleted, false),
            ),
          );
      }

      // Create the work order priority
      const result = await this.db
        .insert(workOrderPriorities)
        .values({
          businessId,
          priorityCode: createWorkOrderPriorityDto.priorityCode,
          priorityName: createWorkOrderPriorityDto.priorityName,
          description: createWorkOrderPriorityDto.description,
          colorCode: createWorkOrderPriorityDto.colorCode,
          iconName: createWorkOrderPriorityDto.iconName,
          severityLevel: createWorkOrderPriorityDto.severityLevel,
          position: nextPosition,
          isActive: createWorkOrderPriorityDto.isActive ?? true,
          isDefault: createWorkOrderPriorityDto.isDefault ?? false,
          escalationHours: createWorkOrderPriorityDto.escalationHours,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: workOrderPriorities.id });

      const workOrderPriorityId = result[0].id;

      // Log the activity
      await this.activityLogService.logCreate(
        workOrderPriorityId,
        EntityType.WORK_ORDER,
        userId,
        businessId,
        {
          reason: `Work order priority "${createWorkOrderPriorityDto.priorityName}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: workOrderPriorityId };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create work order priority');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createWorkOrderPriorityDto: CreateWorkOrderPriorityDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createWorkOrderPriorityDto,
    );
    return {
      id: result.id,
      message: 'Work order priority created successfully',
    };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    workOrderPrioritiesData: CreateWorkOrderPriorityDto[],
    metadata?: ActivityMetadata,
  ): Promise<string[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!workOrderPrioritiesData || workOrderPrioritiesData.length === 0) {
      throw new BadRequestException('No work order priorities provided');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    // Get the current max position
    const maxPositionResult = await this.db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${workOrderPriorities.position}), 0)`,
      })
      .from(workOrderPriorities)
      .where(
        and(
          eq(workOrderPriorities.businessId, businessId),
          eq(workOrderPriorities.isDeleted, false),
        ),
      );

    let nextPosition = (maxPositionResult[0]?.maxPosition || 0) + 1;

    for (const priorityDto of workOrderPrioritiesData) {
      try {
        // Check for duplicates
        const existingByCode = await this.db
          .select()
          .from(workOrderPriorities)
          .where(
            and(
              eq(workOrderPriorities.businessId, businessId),
              ilike(workOrderPriorities.priorityCode, priorityDto.priorityCode),
              eq(workOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByCode) {
          errors.push(
            `Priority code '${priorityDto.priorityCode}' already exists`,
          );
          continue;
        }

        const existingByName = await this.db
          .select()
          .from(workOrderPriorities)
          .where(
            and(
              eq(workOrderPriorities.businessId, businessId),
              ilike(workOrderPriorities.priorityName, priorityDto.priorityName),
              eq(workOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByName) {
          errors.push(
            `Priority name '${priorityDto.priorityName}' already exists`,
          );
          continue;
        }

        // If this is set as default, unset other defaults
        if (priorityDto.isDefault) {
          await this.db
            .update(workOrderPriorities)
            .set({ isDefault: false })
            .where(
              and(
                eq(workOrderPriorities.businessId, businessId),
                eq(workOrderPriorities.isDefault, true),
                eq(workOrderPriorities.isDeleted, false),
              ),
            );
        }

        const result = await this.db
          .insert(workOrderPriorities)
          .values({
            businessId,
            priorityCode: priorityDto.priorityCode,
            priorityName: priorityDto.priorityName,
            description: priorityDto.description,
            colorCode: priorityDto.colorCode,
            iconName: priorityDto.iconName,
            severityLevel: priorityDto.severityLevel,
            position: nextPosition++,
            isActive: priorityDto.isActive ?? true,
            isDefault: priorityDto.isDefault ?? false,
            escalationHours: priorityDto.escalationHours,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: workOrderPriorities.id });

        createdIds.push(result[0].id);

        // Log the activity
        await this.activityLogService.logCreate(
          result[0].id,
          EntityType.WORK_ORDER,
          userId,
          businessId,
          {
            reason: `Work order priority "${priorityDto.priorityName}" was created (bulk)`,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      } catch (error) {
        errors.push(
          `Failed to create priority '${priorityDto.priorityName}': ${error.message}`,
        );
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create work order priorities: ${errors.join(', ')}`,
      );
    }

    return createdIds;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    workOrderPrioritiesData: CreateWorkOrderPriorityDto[],
  ): Promise<{ ids: string[]; message: string; count: number }> {
    const ids = await this.bulkCreate(
      userId,
      businessId,
      workOrderPrioritiesData,
    );
    return {
      ids,
      message: 'Work order priorities created successfully',
      count: ids.length,
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    priorityCode?: string,
    priorityName?: string,
    severityLevel?: string,
    isActive?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: WorkOrderPriorityListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(workOrderPriorities.isDeleted, false),
      eq(workOrderPriorities.businessId, businessId),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(gte(workOrderPriorities.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(workOrderPriorities.createdAt, new Date(to)));
    }

    // Add field-specific filters
    if (priorityCode) {
      whereConditions.push(
        ilike(workOrderPriorities.priorityCode, `%${priorityCode}%`),
      );
    }
    if (priorityName) {
      whereConditions.push(
        ilike(workOrderPriorities.priorityName, `%${priorityName}%`),
      );
    }
    if (severityLevel) {
      whereConditions.push(
        eq(workOrderPriorities.severityLevel, parseInt(severityLevel)),
      );
    }
    if (isActive !== undefined) {
      whereConditions.push(
        eq(workOrderPriorities.isActive, isActive === 'true'),
      );
    }

    // Parse and add custom filters
    let customFilters = [];
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        customFilters = parsedFilters.map((filter: any) => {
          const { field, operator, value } = filter;
          switch (operator) {
            case 'equals':
              return eq(workOrderPriorities[field], value);
            case 'contains':
              return ilike(workOrderPriorities[field], `%${value}%`);
            case 'startsWith':
              return ilike(workOrderPriorities[field], `${value}%`);
            case 'endsWith':
              return ilike(workOrderPriorities[field], `%${value}`);
            case 'greaterThan':
              return gte(workOrderPriorities[field], value);
            case 'lessThan':
              return lte(workOrderPriorities[field], value);
            default:
              return eq(workOrderPriorities[field], value);
          }
        });
      } catch (error) {
        // Ignore invalid filter JSON
      }
    }

    // Combine conditions based on join operator
    let finalWhereCondition;
    if (customFilters.length > 0) {
      const baseCondition = and(...whereConditions);
      const customCondition =
        joinOperator === 'or' ? or(...customFilters) : and(...customFilters);
      finalWhereCondition = and(baseCondition, customCondition);
    } else {
      finalWhereCondition = and(...whereConditions);
    }

    // Parse sort parameter
    let orderBy = [
      asc(workOrderPriorities.position),
      asc(workOrderPriorities.id),
    ];
    if (sort) {
      try {
        const sortFields = sort.split(',').map((field) => {
          const [column, direction] = field.trim().split(':');
          const sortDirection =
            direction?.toLowerCase() === 'desc' ? desc : asc;
          return sortDirection(
            workOrderPriorities[column] || workOrderPriorities.position,
          );
        });
        orderBy = sortFields;
      } catch (error) {
        // Use default sorting if parsing fails
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(workOrderPriorities)
      .where(finalWhereCondition);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with user information
    const results = await this.db
      .select({
        id: workOrderPriorities.id,
        priorityCode: workOrderPriorities.priorityCode,
        priorityName: workOrderPriorities.priorityName,
        description: workOrderPriorities.description,
        colorCode: workOrderPriorities.colorCode,
        iconName: workOrderPriorities.iconName,
        severityLevel: workOrderPriorities.severityLevel,
        position: workOrderPriorities.position,
        isActive: workOrderPriorities.isActive,
        isDefault: workOrderPriorities.isDefault,
        escalationHours: workOrderPriorities.escalationHours,
      })
      .from(workOrderPriorities)
      .where(finalWhereCondition)
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    console.log('results    :::::  ///// :::: ', results);

    return {
      data: results,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<WorkOrderPriorityDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: workOrderPriorities.id,
        businessId: workOrderPriorities.businessId,
        priorityCode: workOrderPriorities.priorityCode,
        priorityName: workOrderPriorities.priorityName,
        description: workOrderPriorities.description,
        colorCode: workOrderPriorities.colorCode,
        iconName: workOrderPriorities.iconName,
        severityLevel: workOrderPriorities.severityLevel,
        position: workOrderPriorities.position,
        isActive: workOrderPriorities.isActive,
        isDefault: workOrderPriorities.isDefault,
        escalationHours: workOrderPriorities.escalationHours,
        createdBy: users.name,
        updatedBy: sql<string>`updated_user.name`,
        createdAt: workOrderPriorities.createdAt,
        updatedAt: workOrderPriorities.updatedAt,
        isDeleted: workOrderPriorities.isDeleted,
      })
      .from(workOrderPriorities)
      .leftJoin(users, eq(workOrderPriorities.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(workOrderPriorities.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(workOrderPriorities.id, id),
          eq(workOrderPriorities.businessId, businessId),
          eq(workOrderPriorities.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Work order priority not found');
    }

    return result;
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<WorkOrderPrioritySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: workOrderPriorities.id,
        priorityName: workOrderPriorities.priorityName,
        priorityCode: workOrderPriorities.priorityCode,
        severityLevel: workOrderPriorities.severityLevel,
        position: workOrderPriorities.position,
        colorCode: workOrderPriorities.colorCode,
        iconName: workOrderPriorities.iconName,
        isActive: workOrderPriorities.isActive,
      })
      .from(workOrderPriorities)
      .where(
        and(
          eq(workOrderPriorities.businessId, businessId),
          eq(workOrderPriorities.isActive, true),
          eq(workOrderPriorities.isDeleted, false),
        ),
      )
      .orderBy(asc(workOrderPriorities.position), asc(workOrderPriorities.id));

    return results;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateWorkOrderPriorityDto: UpdateWorkOrderPriorityDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the work order priority exists
      const existingPriority = await this.db
        .select()
        .from(workOrderPriorities)
        .where(
          and(
            eq(workOrderPriorities.id, id),
            eq(workOrderPriorities.businessId, businessId),
            eq(workOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPriority) {
        throw new NotFoundException('Work order priority not found');
      }

      // Check for conflicts if updating code or name
      if (updateWorkOrderPriorityDto.priorityCode) {
        const existingByCode = await this.db
          .select()
          .from(workOrderPriorities)
          .where(
            and(
              eq(workOrderPriorities.businessId, businessId),
              ilike(
                workOrderPriorities.priorityCode,
                updateWorkOrderPriorityDto.priorityCode,
              ),
              sql`${workOrderPriorities.id} != ${id}`,
              eq(workOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByCode) {
          throw new ConflictException(
            `Work order priority with code '${updateWorkOrderPriorityDto.priorityCode}' already exists`,
          );
        }
      }

      if (updateWorkOrderPriorityDto.priorityName) {
        const existingByName = await this.db
          .select()
          .from(workOrderPriorities)
          .where(
            and(
              eq(workOrderPriorities.businessId, businessId),
              ilike(
                workOrderPriorities.priorityName,
                updateWorkOrderPriorityDto.priorityName,
              ),
              sql`${workOrderPriorities.id} != ${id}`,
              eq(workOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingByName) {
          throw new ConflictException(
            `Work order priority with name '${updateWorkOrderPriorityDto.priorityName}' already exists`,
          );
        }
      }

      // If setting as default, unset other defaults
      if (updateWorkOrderPriorityDto.isDefault) {
        await this.db
          .update(workOrderPriorities)
          .set({ isDefault: false })
          .where(
            and(
              eq(workOrderPriorities.businessId, businessId),
              eq(workOrderPriorities.isDefault, true),
              sql`${workOrderPriorities.id} != ${id}`,
              eq(workOrderPriorities.isDeleted, false),
            ),
          );
      }

      // Update the work order priority
      await this.db
        .update(workOrderPriorities)
        .set({
          ...updateWorkOrderPriorityDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(workOrderPriorities.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.WORK_ORDER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update work order priority');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateWorkOrderPriorityDto: UpdateWorkOrderPriorityDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateWorkOrderPriorityDto,
    );
    return {
      id: result.id,
      message: 'Work order priority updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the work order priority exists
    const existingPriority = await this.db
      .select()
      .from(workOrderPriorities)
      .where(
        and(
          eq(workOrderPriorities.id, id),
          eq(workOrderPriorities.businessId, businessId),
          eq(workOrderPriorities.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingPriority) {
      throw new NotFoundException('Work order priority not found');
    }

    // Soft delete the work order priority
    await this.db
      .update(workOrderPriorities)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(workOrderPriorities.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.WORK_ORDER,
      userId,
      businessId,
      {
        reason: `Work order priority "${existingPriority.priorityName}" was deleted`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id };
  }

  async removeAndReturnResponse(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    const result = await this.remove(userId, businessId, id);
    return {
      id: result.id,
      message: 'Work order priority deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{
    deletedIds: string[];
    failedIds: string[];
    message: string;
    deletedCount: number;
    failedCount: number;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('No work order priority IDs provided');
    }

    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        await this.remove(userId, businessId, id);
        deletedIds.push(id);
      } catch (error) {
        failedIds.push(id);
      }
    }

    const deletedCount = deletedIds.length;
    const failedCount = failedIds.length;

    return {
      deletedIds,
      failedIds,
      message: `Bulk delete completed. ${deletedCount} work order priorities deleted, ${failedCount} failed.`,
      deletedCount,
      failedCount,
    };
  }

  async updatePositions(
    userId: string,
    businessId: string | null,
    updates: UpdateWorkOrderPriorityPositionItemDto[],
    metadata?: ActivityMetadata,
  ): Promise<{
    updatedIds: string[];
    failures: UpdateWorkOrderPriorityPositionsFailureDto[];
    message: string;
    updatedCount: number;
    failedCount: number;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!updates || updates.length === 0) {
      throw new BadRequestException('No position updates provided');
    }

    const updatedIds: string[] = [];
    const failures: UpdateWorkOrderPriorityPositionsFailureDto[] = [];

    for (const update of updates) {
      try {
        // Check if the work order priority exists
        const existingPriority = await this.db
          .select()
          .from(workOrderPriorities)
          .where(
            and(
              eq(workOrderPriorities.id, update.id),
              eq(workOrderPriorities.businessId, businessId),
              eq(workOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingPriority) {
          failures.push({
            id: update.id,
            error: 'Work order priority not found',
          });
          continue;
        }

        // Update the position
        await this.db
          .update(workOrderPriorities)
          .set({
            position: update.position,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(workOrderPriorities.id, update.id));

        updatedIds.push(update.id);

        // Log the activity
        await this.activityLogService.logUpdate(
          update.id,
          EntityType.WORK_ORDER,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      } catch (error) {
        failures.push({
          id: update.id,
          error: error.message || 'Failed to update position',
        });
      }
    }

    const updatedCount = updatedIds.length;
    const failedCount = failures.length;

    return {
      updatedIds,
      failures,
      message: `Position updates completed. ${updatedCount} work order priorities updated, ${failedCount} failed.`,
      updatedCount,
      failedCount,
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    updates: BulkUpdateWorkOrderPriorityStatusItemDto[],
    metadata?: ActivityMetadata,
  ): Promise<{
    updatedIds: string[];
    failures: BulkUpdateWorkOrderPriorityStatusFailureDto[];
    message: string;
    updatedCount: number;
    failedCount: number;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!updates || updates.length === 0) {
      throw new BadRequestException('No status updates provided');
    }

    const updatedIds: string[] = [];
    const failures: BulkUpdateWorkOrderPriorityStatusFailureDto[] = [];

    for (const update of updates) {
      try {
        // Check if the work order priority exists
        const existingPriority = await this.db
          .select()
          .from(workOrderPriorities)
          .where(
            and(
              eq(workOrderPriorities.id, update.id),
              eq(workOrderPriorities.businessId, businessId),
              eq(workOrderPriorities.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingPriority) {
          failures.push({
            id: update.id,
            error: 'Work order priority not found',
          });
          continue;
        }

        // Update the status
        await this.db
          .update(workOrderPriorities)
          .set({
            isActive: update.isActive,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(workOrderPriorities.id, update.id));

        updatedIds.push(update.id);

        // Log the activity
        await this.activityLogService.logUpdate(
          update.id,
          EntityType.WORK_ORDER,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      } catch (error) {
        failures.push({
          id: update.id,
          error: error.message || 'Failed to update status',
        });
      }
    }

    const updatedCount = updatedIds.length;
    const failedCount = failures.length;

    return {
      updatedIds,
      failures,
      message: `Status updates completed. ${updatedCount} work order priorities updated, ${failedCount} failed.`,
      updatedCount,
      failedCount,
    };
  }

  async checkPriorityCodeAvailability(
    userId: string,
    businessId: string | null,
    priorityCode: string,
    excludeId?: string,
  ): Promise<{ available: boolean; priorityCode: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(workOrderPriorities.businessId, businessId),
      ilike(workOrderPriorities.priorityCode, priorityCode),
      eq(workOrderPriorities.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${workOrderPriorities.id} != ${excludeId}`);
    }

    const existingPriority = await this.db
      .select()
      .from(workOrderPriorities)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existingPriority;

    return {
      available,
      priorityCode,
      message: available
        ? 'Priority code is available'
        : 'Priority code is already in use',
    };
  }

  async checkPriorityNameAvailability(
    userId: string,
    businessId: string | null,
    priorityName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; priorityName: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(workOrderPriorities.businessId, businessId),
      ilike(workOrderPriorities.priorityName, priorityName),
      eq(workOrderPriorities.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${workOrderPriorities.id} != ${excludeId}`);
    }

    const existingPriority = await this.db
      .select()
      .from(workOrderPriorities)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existingPriority;

    return {
      available,
      priorityName,
      message: available
        ? 'Priority name is available'
        : 'Priority name is already in use',
    };
  }
}
