import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  InviteBusinessUserDto,
  InviteBusinessUserResponseDto,
} from './dto/invite-business-user.dto';
import { BusinessUserSlimDto } from './dto/business-user-slim.dto';
import { PaginatedBusinessUsersResponseDto } from './dto/paginated-business-users-response.dto';
import { DeleteBusinessUserResponseDto } from './dto/delete-business-user-response.dto';
import { BulkDeleteBusinessUserResponseDto } from './dto/bulk-delete-business-user.dto';
import {
  UpdateBusinessUserStatusDto,
  BusinessUserIdResponseDto,
  BulkUpdateBusinessUserStatusResponseDto,
} from './dto/update-business-user-status.dto';
import { businessUsers } from '../drizzle/schema/business-users.schema';
import { users } from '../drizzle/schema/users.schema';
import { businessRoles } from '../drizzle/schema/business-roles.schema';
import { businessUserLocations } from '../drizzle/schema/business-user-locations.schema';
import { locations } from '../drizzle/schema/locations.schema';
import {
  BusinessUserRole,
  BusinessUserStatus,
} from '../shared/types/business.enum';
import { eq, and, isNull, ilike, desc, count, inArray } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { LocationStatus } from '@app/shared/types/location.enum';

@Injectable()
export class BusinessUsersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async findAll(
    userId: string,
    businessId: string | null,
    page?: number,
    limit?: number,
    email?: string,
    role?: string,
    status?: string,
  ): Promise<PaginatedBusinessUsersResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const pageNumber = page || 1;
      const pageSize = Math.min(limit || 10, 100);
      const offset = (pageNumber - 1) * pageSize;

      let whereConditions = and(
        eq(businessUsers.businessId, businessId),
        eq(businessUsers.isDeleted, false),
      );

      if (email) {
        whereConditions = and(
          whereConditions,
          ilike(users.email, `%${email}%`),
        );
      }

      if (role) {
        whereConditions = and(
          whereConditions,
          eq(businessUsers.role, role as BusinessUserRole),
        );
      }

      if (status) {
        whereConditions = and(
          whereConditions,
          eq(businessUsers.status, status as BusinessUserStatus),
        );
      }

      const [businessUsersData, totalCountResult] = await Promise.all([
        this.db
          .select({
            id: businessUsers.id,
            businessId: businessUsers.businessId,
            userId: businessUsers.userId,
            role: businessUsers.role,
            businessRoleId: businessUsers.businessRoleId,
            isActiveBusiness: businessUsers.isActiveBusiness,
            isAllowedAllLocation: businessUsers.isAllowedAllLocation,
            invitedBy: businessUsers.invitedBy,
            invitedAt: businessUsers.invitedAt,
            joinedAt: businessUsers.joinedAt,
            lastActiveAt: businessUsers.lastActiveAt,
            status: businessUsers.status,
            notes: businessUsers.notes,
            createdBy: businessUsers.createdBy,
            updatedBy: businessUsers.updatedBy,
            createdAt: businessUsers.createdAt,
            updatedAt: businessUsers.updatedAt,
            user: {
              id: users.id,
              email: users.email,
              firstName: users.firstName,
              lastName: users.lastName,
            },
            businessRole: {
              id: businessRoles.id,
              name: businessRoles.name,
              description: businessRoles.description,
            },
          })
          .from(businessUsers)
          .leftJoin(users, eq(businessUsers.userId, users.id))
          .leftJoin(
            businessRoles,
            eq(businessUsers.businessRoleId, businessRoles.id),
          )
          .where(whereConditions)
          .orderBy(desc(businessUsers.createdAt))
          .limit(pageSize)
          .offset(offset),
        this.db
          .select({ count: count() })
          .from(businessUsers)
          .leftJoin(users, eq(businessUsers.userId, users.id))
          .where(whereConditions)
          .then((result) => result[0]?.count || 0),
      ]);

      const totalPages = Math.ceil(totalCountResult / pageSize);

      // View activity logging removed for simplicity

      return {
        data: businessUsersData,
        meta: {
          total: totalCountResult,
          page: pageNumber,
          totalPages,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve business users: ${error.message}`,
      );
    }
  }

  async findAllSlim(businessId: string | null): Promise<BusinessUserSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const businessUsersData = await this.db
        .select({
          id: businessUsers.id,
          userId: businessUsers.userId,
          role: businessUsers.role,
          status: businessUsers.status,
          joinedAt: businessUsers.joinedAt,
          lastActiveAt: businessUsers.lastActiveAt,
          userEmail: users.email,
          userFirstName: users.firstName,
          userLastName: users.lastName,
        })
        .from(businessUsers)
        .leftJoin(users, eq(businessUsers.userId, users.id))
        .where(
          and(
            eq(businessUsers.businessId, businessId),
            eq(businessUsers.isDeleted, false),
          ),
        )
        .orderBy(desc(businessUsers.createdAt));

      return businessUsersData;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to retrieve business users: ${error.message}`,
      );
    }
  }

  async inviteUser(
    userId: string,
    businessId: string | null,
    inviteDto: InviteBusinessUserDto,
    metadata?: ActivityMetadata,
  ): Promise<InviteBusinessUserResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        inviteDto.role === BusinessUserRole.CUSTOM &&
        !inviteDto.businessRoleId
      ) {
        throw new BadRequestException(
          'Business role ID is required for CUSTOM role',
        );
      }

      let targetUser = await this.db
        .select()
        .from(users)
        .where(eq(users.email, inviteDto.email))
        .then((results) => results[0]);

      let userCreated = false;

      if (!targetUser) {
        const newUser = await this.db
          .insert(users)
          .values({
            email: inviteDto.email,
            createdBy: userId,
          })
          .returning({ id: users.id });

        targetUser = newUser[0];
        userCreated = true;
      }

      const existingBusinessUser = await this.db
        .select()
        .from(businessUsers)
        .where(
          and(
            eq(businessUsers.businessId, businessId),
            eq(businessUsers.userId, targetUser.id),
            eq(businessUsers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingBusinessUser) {
        throw new ConflictException(
          `User with email '${inviteDto.email}' is already part of this business`,
        );
      }

      if (inviteDto.businessRoleId) {
        const businessRole = await this.db
          .select()
          .from(businessRoles)
          .where(
            and(
              eq(businessRoles.id, inviteDto.businessRoleId),
              eq(businessRoles.businessId, businessId),
              eq(businessRoles.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!businessRole) {
          throw new NotFoundException(
            'Business role not found or does not belong to this business',
          );
        }
      }

      const newBusinessUser = await this.db
        .insert(businessUsers)
        .values({
          businessId,
          userId: targetUser.id,
          role: inviteDto.role,
          businessRoleId: inviteDto.businessRoleId,
          status: BusinessUserStatus.INVITED,
          notes: inviteDto.notes,
          isAllowedAllLocation: inviteDto.isAllowedAllLocation || false,
          invitedBy: userId,
          invitedAt: new Date(),
          createdBy: userId,
        })
        .returning({ id: businessUsers.id });

      // Handle location assignments if not allowed all locations
      if (
        !inviteDto.isAllowedAllLocation &&
        inviteDto.locationIds &&
        inviteDto.locationIds.length > 0
      ) {
        // Validate that all location IDs belong to the business
        const validLocations = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              inArray(locations.id, inviteDto.locationIds),
              eq(locations.isDeleted, false),
              eq(locations.status, LocationStatus.ACTIVE),
            ),
          );

        if (validLocations.length !== inviteDto.locationIds.length) {
          throw new BadRequestException(
            'One or more location IDs are invalid or do not belong to this business',
          );
        }

        // Create business user location relationships
        const locationAssignments = inviteDto.locationIds.map((locationId) => ({
          businessUserId: newBusinessUser[0].id,
          locationId,
          createdBy: userId,
        }));

        await this.db.insert(businessUserLocations).values(locationAssignments);
      }

      // Log the business user invitation activity
      await this.activityLogService.logCreate(
        newBusinessUser[0].id,
        EntityType.USER, // Using USER as closest match for business users
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newBusinessUser[0].id,
        email: inviteDto.email,
        role: inviteDto.role,
        message: userCreated
          ? 'User account created and invited to business'
          : 'User successfully invited to business',
        userCreated,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(`Failed to invite user: ${error.message}`);
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<DeleteBusinessUserResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const businessUser = await this.db
        .select({
          id: businessUsers.id,
          userId: businessUsers.userId,
          user: {
            email: users.email,
          },
        })
        .from(businessUsers)
        .leftJoin(users, eq(businessUsers.userId, users.id))
        .where(
          and(
            eq(businessUsers.id, id),
            eq(businessUsers.businessId, businessId),
            eq(businessUsers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!businessUser) {
        throw new NotFoundException('Business user not found');
      }

      await this.db
        .update(businessUsers)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(businessUsers.id, id));

      // Log the business user removal activity
      await this.activityLogService.logDelete(
        id,
        EntityType.USER, // Using USER as closest match for business users
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Business user successfully deleted',
        email: businessUser.user.email,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete business user: ${error.message}`,
      );
    }
  }

  async updateStatus(
    userId: string,
    businessId: string | null,
    id: string,
    updateStatusDto: UpdateBusinessUserStatusDto,
    metadata?: ActivityMetadata,
  ): Promise<BusinessUserIdResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const businessUser = await this.db
        .select()
        .from(businessUsers)
        .where(
          and(
            eq(businessUsers.id, id),
            eq(businessUsers.businessId, businessId),
            eq(businessUsers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!businessUser) {
        throw new NotFoundException('Business user not found');
      }

      await this.db
        .update(businessUsers)
        .set({
          status: updateStatusDto.status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(businessUsers.id, id));

      // Log the business user status update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.USER, // Using USER as closest match for business users
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Business user status updated successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update business user status: ${error.message}`,
      );
    }
  }

  async bulkRemove(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<BulkDeleteBusinessUserResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (ids.length === 0) {
        throw new BadRequestException('At least one ID is required');
      }

      const businessUsersToDelete = await this.db
        .select()
        .from(businessUsers)
        .where(
          and(
            inArray(businessUsers.id, ids),
            eq(businessUsers.businessId, businessId),
            eq(businessUsers.isDeleted, false),
          ),
        );

      if (businessUsersToDelete.length === 0) {
        throw new NotFoundException('No business users found to delete');
      }

      const foundIds = businessUsersToDelete.map((bu) => bu.id);

      await this.db
        .update(businessUsers)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(businessUsers.id, foundIds));

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.USER, // Using USER as closest match for business users
        foundIds,
        { deletedAt: new Date(), updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { businessUserIds: ids },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedCount: foundIds.length,
        deletedIds: foundIds,
        message: `${foundIds.length} business users successfully deleted`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete business users: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: BusinessUserStatus,
    metadata?: ActivityMetadata,
  ): Promise<BulkUpdateBusinessUserStatusResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (ids.length === 0) {
        throw new BadRequestException('At least one ID is required');
      }

      const businessUsersToUpdate = await this.db
        .select()
        .from(businessUsers)
        .where(
          and(
            inArray(businessUsers.id, ids),
            eq(businessUsers.businessId, businessId),
            eq(businessUsers.isDeleted, false),
          ),
        );

      if (businessUsersToUpdate.length === 0) {
        throw new NotFoundException('No business users found to update');
      }

      const foundIds = businessUsersToUpdate.map((bu) => bu.id);

      await this.db
        .update(businessUsers)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(businessUsers.id, foundIds));

      // Log bulk status update operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_STATUS_CHANGE,
        EntityType.USER, // Using USER as closest match for business users
        foundIds,
        { status },
        userId,
        businessId,
        {
          filterCriteria: { businessUserIds: ids, targetStatus: status },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        updatedCount: foundIds.length,
        updatedIds: foundIds,
        status,
        message: `${foundIds.length} business users status updated to ${status}`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update business users status: ${error.message}`,
      );
    }
  }
}
