/**
 * Common enums used across the application
 */

/**
 * Document Status
 * Represents the status of documents in the system
 */
export enum DocumentStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
}

/**
 * Account Type
 * The authentication provider used for the account
 */
export enum AccountType {
  EMAIL = 'email',
  GOOGLE = 'google',
  FACEBOOK = 'facebook',
}

/**
 * General Status Enum
 * Used for items that can be active/inactive/deleted
 */
export enum StatusType {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Bank Account Type
 * Types of bank accounts
 */
export enum BankAccountType {
  CHECKING = 'checking',
  SAVINGS = 'savings',
}

/**
 * Currency Code
 * ISO 4217 currency codes
 */
export enum CurrencyCode {
  // Major currencies (your existing ones)
  USD = 'USD', // US Dollar
  EUR = 'EUR', // Euro
  GBP = 'GBP', // British Pound Sterling
  JPY = 'JPY', // Japanese Yen
  AUD = 'AUD', // Australian Dollar
  CAD = 'CAD', // Canadian Dollar
  CHF = 'CHF', // Swiss Franc
  CNY = 'CNY', // Chinese Yuan
  INR = 'INR', // Indian Rupee
  LKR = 'LKR', // Sri Lankan Rupee

  // Additional major currencies
  KRW = 'KRW', // South Korean Won
  SGD = 'SGD', // Singapore Dollar
  HKD = 'HKD', // Hong Kong Dollar
  NOK = 'NOK', // Norwegian Krone
  SEK = 'SEK', // Swedish Krona
  DKK = 'DKK', // Danish Krone
  PLN = 'PLN', // Polish Zloty
  CZK = 'CZK', // Czech Koruna
  HUF = 'HUF', // Hungarian Forint
  RUB = 'RUB', // Russian Ruble
  BRL = 'BRL', // Brazilian Real
  MXN = 'MXN', // Mexican Peso
  ZAR = 'ZAR', // South African Rand
  TRY = 'TRY', // Turkish Lira
  ILS = 'ILS', // Israeli New Shekel
  THB = 'THB', // Thai Baht
  MYR = 'MYR', // Malaysian Ringgit
  IDR = 'IDR', // Indonesian Rupiah
  PHP = 'PHP', // Philippine Peso
  VND = 'VND', // Vietnamese Dong

  // Africa
  EGP = 'EGP', // Egyptian Pound
  NGN = 'NGN', // Nigerian Naira
  KES = 'KES', // Kenyan Shilling
  GHS = 'GHS', // Ghanaian Cedi
  MAD = 'MAD', // Moroccan Dirham
  TND = 'TND', // Tunisian Dinar
  ETB = 'ETB', // Ethiopian Birr
  UGX = 'UGX', // Ugandan Shilling
  TZS = 'TZS', // Tanzanian Shilling
  ZMW = 'ZMW', // Zambian Kwacha
  BWP = 'BWP', // Botswana Pula
  XOF = 'XOF', // West African CFA Franc
  XAF = 'XAF', // Central African CFA Franc

  // Middle East
  AED = 'AED', // UAE Dirham
  SAR = 'SAR', // Saudi Riyal
  QAR = 'QAR', // Qatari Riyal
  KWD = 'KWD', // Kuwaiti Dinar
  BHD = 'BHD', // Bahraini Dinar
  OMR = 'OMR', // Omani Rial
  JOD = 'JOD', // Jordanian Dinar
  LBP = 'LBP', // Lebanese Pound
  IRR = 'IRR', // Iranian Rial
  IQD = 'IQD', // Iraqi Dinar

  // Asia Pacific
  PKR = 'PKR', // Pakistani Rupee
  BDT = 'BDT', // Bangladeshi Taka
  NPR = 'NPR', // Nepalese Rupee
  AFN = 'AFN', // Afghan Afghani
  MMK = 'MMK', // Myanmar Kyat
  LAK = 'LAK', // Lao Kip
  KHR = 'KHR', // Cambodian Riel
  BND = 'BND', // Brunei Dollar
  TWD = 'TWD', // Taiwan Dollar
  MOP = 'MOP', // Macanese Pataca
  FJD = 'FJD', // Fijian Dollar
  PGK = 'PGK', // Papua New Guinean Kina
  WST = 'WST', // Samoan Tala
  TOP = 'TOP', // Tongan Paʻanga
  VUV = 'VUV', // Vanuatu Vatu
  SBD = 'SBD', // Solomon Islands Dollar

  // Europe
  RON = 'RON', // Romanian Leu
  BGN = 'BGN', // Bulgarian Lev
  HRK = 'HRK', // Croatian Kuna
  RSD = 'RSD', // Serbian Dinar
  BAM = 'BAM', // Bosnia and Herzegovina Convertible Mark
  MKD = 'MKD', // Macedonian Denar
  ALL = 'ALL', // Albanian Lek
  ISK = 'ISK', // Icelandic Krona
  MDL = 'MDL', // Moldovan Leu
  UAH = 'UAH', // Ukrainian Hryvnia
  BYN = 'BYN', // Belarusian Ruble
  GEL = 'GEL', // Georgian Lari
  AMD = 'AMD', // Armenian Dram
  AZN = 'AZN', // Azerbaijani Manat

  // Americas
  ARS = 'ARS', // Argentine Peso
  CLP = 'CLP', // Chilean Peso
  COP = 'COP', // Colombian Peso
  PEN = 'PEN', // Peruvian Sol
  UYU = 'UYU', // Uruguayan Peso
  PYG = 'PYG', // Paraguayan Guarani
  BOB = 'BOB', // Bolivian Boliviano
  VES = 'VES', // Venezuelan Bolívar Soberano
  GYD = 'GYD', // Guyanese Dollar
  SRD = 'SRD', // Surinamese Dollar
  TTD = 'TTD', // Trinidad and Tobago Dollar
  JMD = 'JMD', // Jamaican Dollar
  BBD = 'BBD', // Barbadian Dollar
  BSD = 'BSD', // Bahamian Dollar
  BZD = 'BZD', // Belize Dollar
  GTQ = 'GTQ', // Guatemalan Quetzal
  HNL = 'HNL', // Honduran Lempira
  NIO = 'NIO', // Nicaraguan Córdoba
  CRC = 'CRC', // Costa Rican Colón
  PAB = 'PAB', // Panamanian Balboa
  DOP = 'DOP', // Dominican Peso
  HTG = 'HTG', // Haitian Gourde
  CUP = 'CUP', // Cuban Peso

  // Central Asia
  KZT = 'KZT', // Kazakhstani Tenge
  UZS = 'UZS', // Uzbekistani Som
  KGS = 'KGS', // Kyrgyzstani Som
  TJS = 'TJS', // Tajikistani Somoni
  TMT = 'TMT', // Turkmenistani Manat
  MNT = 'MNT', // Mongolian Tugrik

  // Pacific
  NZD = 'NZD', // New Zealand Dollar
  XPF = 'XPF', // CFP Franc (French Pacific)

  // Special/Commodity currencies
  XAU = 'XAU', // Gold (troy ounce)
  XAG = 'XAG', // Silver (troy ounce)
  XPT = 'XPT', // Platinum (troy ounce)
  XPD = 'XPD', // Palladium (troy ounce)
}

/**
 * Bank Account Entity Type
 * Types of entities that can have bank accounts
 */
export enum BankAccountEntityType {
  CUSTOMER = 'customer',
  SUPPLIER = 'supplier',
  STAFF = 'staff',
  BUSINESS = 'business',
}

/**
 * Payment Status
 * Status of payments for reservations, orders, and other transactions
 */
export enum PaymentStatus {
  PENDING = 'pending', // Payment not yet made
  PARTIAL = 'partial', // Partial payment made
  PAID = 'paid', // Full payment completed
  REFUNDED = 'refunded', // Payment refunded
  CANCELLED = 'cancelled', // Payment cancelled
  PARTIALLY_REFUNDED = 'partially_refunded', // Partial refund processed
  PARTIALLY_PAID = 'partially_paid', // Partial payment received
  OVERDUE = 'overdue', // Payment is overdue
}

/**
 * Package Reservation Status
 * Status of package reservations throughout their lifecycle
 */
export enum PackageReservationStatus {
  INQUIRY = 'INQUIRY', // Initial inquiry, quotation stage
  PENDING = 'PENDING', // Reservation submitted, awaiting confirmation
  CONFIRMED = 'CONFIRMED', // Reservation confirmed, awaiting payment
  PARTIALLY_PAID = 'PARTIALLY_PAID', // Partial payment received
  PAID = 'PAID', // Full payment received
  IN_PROGRESS = 'IN_PROGRESS', // Package is currently being delivered/executed
  COMPLETED = 'COMPLETED', // Package successfully completed
  CANCELLED = 'CANCELLED', // Reservation cancelled
  NO_SHOW = 'NO_SHOW', // Customer failed to show up
  REFUNDED = 'REFUNDED', // Payment refunded after cancellation
}

/**
 * Tax Type
 * Defines how taxes are applied to prices
 */
export enum TaxType {
  INCLUSIVE = 'INCLUSIVE', // Tax is included in the displayed price
  EXCLUSIVE = 'EXCLUSIVE', // Tax is added to the displayed price
  OUT_OF_SCOPE = 'OUT_OF_SCOPE', // No tax applicable
}

/**
 * Discount Type Enum
 */
export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE', // Discount is a percentage of the price
  FIXED_AMOUNT = 'FIXED_AMOUNT', // Discount is a fixed monetary amount
  VALUE = 'VALUE', // Alternative name for fixed amount (used in service orders)
}

/**
 * Payment Method
 * Methods available for processing payments
 */
export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  BANK_TRANSFER = 'BANK_TRANSFER',
  CASH = 'CASH',
  PAYPAL = 'PAYPAL',
  MOBILE_PAYMENT = 'MOBILE_PAYMENT',
  GIFT_CARD = 'GIFT_CARD',
  STORE_CREDIT = 'STORE_CREDIT',
  CRYPTOCURRENCY = 'CRYPTOCURRENCY',
  CHECK = 'CHECK',
  OTHER = 'OTHER',
}
