/**
 * Custom Field Type Enum
 * Defines the different types of custom fields that can be created
 */
export enum CustomFieldType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  EMAIL = 'email',
  PHONE = 'phone',
  URL = 'url',
  SELECT = 'select',
  MULTI_SELECT = 'multi-select',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  TEXTAREA = 'textarea',
}

/**
 * Entity Type Enum
 * Defines the different entity types that can have custom fields
 */
export enum CustomFieldEntityType {
  CUSTOMERS = 'customers',
  EXPENSES = 'expenses',
  ITEMS = 'items',
  CAMPAIGNS = 'campaigns',
  BOOKINGS = 'bookings',
  RENTALS = 'rentals',
  ROOM_BOOKINGS = 'room-bookings',
  SMS_NEWSLETTERS = 'sms-newsletters',
}
