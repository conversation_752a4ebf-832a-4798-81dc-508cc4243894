/**
 * Asset-related enums used across the application
 */

/**
 * Asset Status
 * Status of an asset in the system
 */
export enum AssetStatus {
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  ASSIGNED = 'assigned',
  INACTIVE = 'inactive',
  DISCARDED = 'discarded',
  REPAIR = 'repair',
  LOST = 'lost',
  STOLEN = 'stolen',
  DAMAGED = 'damaged',
  MAINTENANCE = 'maintenance',
  ALLOCATED = 'allocated',
}

/**
 * Purchase Type
 * How the asset was acquired
 */
export enum PurchaseType {
  CASH = 'cash',
  CREDIT = 'credit',
  LEASE = 'lease',
  RENTAL = 'rental',
  DONATED = 'donated',
}

/**
 * Transaction Type
 * Types of asset transactions in the system
 */
export enum AssetsTransactionType {
  ALLOCATION = 'allocation',
  DEALLOCATION = 'deallocation',
  TRANSFER = 'transfer',
}

/**
 * Asset Transaction Status
 * Status of asset transactions in the system
 */
export enum AssetTransactionStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
}

/**
 * Maintenance Status
 * Status of asset maintenance in the system
 */
export enum MaintenanceStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * Maintenance Priority
 * Priority level for asset maintenance
 */

// LOW = 'LOW',
// NORMAL = 'NORMAL',
// HIGH = 'HIGH',
// URGENT = 'URGENT',
// EMERGENCY = 'EMERGENCY'

export enum MaintenancePriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  EMERGENCY = 'emergency',
}

/**
 * Depreciation Method
 * Methods for calculating asset depreciation
 */
export enum DepreciationMethod {
  STRAIGHT_LINE = 'straight_line',
  DECLINING_BALANCE = 'declining_balance',
  UNITS_OF_PRODUCTION = 'units_of_production',
  SUM_OF_YEARS_DIGITS = 'sum_of_years_digits',
}

/**
 * Accounting Category
 * Categories for financial reporting and accounting
 */
export enum AccountingCategory {
  CURRENT_ASSETS = 'current_assets',
  FIXED_ASSETS = 'fixed_assets',
  INTANGIBLE_ASSETS = 'intangible_assets',
  INVESTMENT_ASSETS = 'investment_assets',
  BIOLOGICAL_ASSETS = 'biological_assets',
}

/**
 * Asset Type Status
 * Status of asset types in the system
 */
export enum AssetTypeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted',
}

/**
 * Depreciation Computation
 * Methods for computing depreciation periods and amounts
 */
export enum DepreciationComputation {
  CONSTANT_PERIODS = 'constant_periods',
  ACCELERATED = 'accelerated',
  DECELERATED = 'decelerated',
  MANUAL = 'manual',
}

/**
 * Depreciation Duration Unit
 * Units for specifying depreciation duration
 */
export enum DepreciationDurationUnit {
  MONTHS = 'months',
  YEARS = 'years',
}

/**
 * Repair Status
 * Status of asset repair orders in the system
 */
export enum RepairStatus {
  REPORTED = 'reported',
  DIAGNOSED = 'diagnosed',
  APPROVED = 'approved',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
  CANCELLED = 'cancelled',
}

/**
 * Repair Priority
 * Priority level for asset repair orders
 */
export enum RepairPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

/**
 * Repair Type
 * Types of repair for asset repair orders
 */
export enum RepairType {
  EMERGENCY = 'emergency',
  SCHEDULED = 'scheduled',
  CORRECTIVE = 'corrective',
  BREAKDOWN = 'breakdown',
  WARRANTY = 'warranty',
}
