import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { locations } from '../drizzle/schema/locations.schema';
import { addresses } from '../drizzle/schema/address.schema';
import { userLocations } from '../drizzle/schema/user-locations.schema';
import { users } from '../drizzle/schema/users.schema';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { LocationDto } from './dto/location.dto';
import { LocationSlimDto } from './dto/location-slim.dto';
import { LocationListItemDto } from './dto/location-list-item.dto';
import { LocationListResponseDto } from './dto/location-list-response.dto';
import {
  and,
  eq,
  sql,
  gte,
  lte,
  desc,
  asc,
  ilike,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  ActivityLogName,
  LocationType,
  LocationStatus,
  AddressType,
} from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class LocationsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  // get location names by id array
  async getLocationNamesByIds(
    ids: string[],
  ): Promise<{ id: string; name: string }[]> {
    const result = await this.db
      .select({ id: locations.id, name: locations.name })
      .from(locations)
      .where(inArray(locations.id, ids));

    return result;
  }

  async create(
    userId: string,
    businessId: string | null,
    createLocationDto: CreateLocationDto,
  ): Promise<LocationDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Note: Multiple main site locations are now allowed per business

      // Check if a location with the same name already exists for this business
      const existingNameLocation = await this.db
        .select()
        .from(locations)
        .where(
          and(
            eq(locations.businessId, businessId),
            ilike(locations.name, createLocationDto.name),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingNameLocation) {
        throw new ConflictException(
          `A location with the name '${createLocationDto.name}' already exists for this business`,
        );
      }

      // Check if a location with the same code already exists for this business
      const existingCodeLocation = await this.db
        .select()
        .from(locations)
        .where(
          and(
            eq(locations.businessId, businessId),
            eq(locations.code, createLocationDto.code),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingCodeLocation) {
        throw new ConflictException(
          `A location with code '${createLocationDto.code}' already exists for this business`,
        );
      }

      // Validate linked locations if provided
      if (
        createLocationDto.linkedLocationIds &&
        createLocationDto.linkedLocationIds.length > 0
      ) {
        const linkedLocations = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              inArray(locations.id, createLocationDto.linkedLocationIds),
              eq(locations.isDeleted, false),
            ),
          );

        if (
          linkedLocations.length !== createLocationDto.linkedLocationIds.length
        ) {
          throw new BadRequestException(
            'Some linked locations do not exist or belong to this business',
          );
        }
      }

      // Create address first
      const addressData = {
        ...createLocationDto.address,
        addressType:
          createLocationDto.address.addressType || AddressType.LOCATION,
        businessId: businessId,
        isDefault: createLocationDto.address.isDefault || false,
      };

      const [newAddress] = await this.db
        .insert(addresses)
        .values(addressData)
        .returning();

      // Create location
      const [newLocation] = await this.db
        .insert(locations)
        .values({
          businessId: businessId,
          name: createLocationDto.name,
          code: createLocationDto.code,
          addressId: newAddress.id,
          type: createLocationDto.type || LocationType.MAINSITE,
          status: createLocationDto.status || LocationStatus.ACTIVE,
          linkedLocationIds: createLocationDto.linkedLocationIds || [],
          createdBy: userId,
        })
        .returning();

      // Log the location creation activity
      await this.activityLogService.logCreate(
        newLocation.id,
        EntityType.LOCATION,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return this.mapToLocationDto(newLocation, newAddress);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create location: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createLocationDto: CreateLocationDto,
  ): Promise<{ id: string }> {
    const location = await this.create(userId, businessId, createLocationDto);
    return { id: location.id };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    code?: string,
    type?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: LocationListResponseDto[];
    meta: {
      total: number;
      page: number;
      totalPages: number;
    };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const baseConditions = [
      eq(locations.businessId, businessId),
      eq(locations.isDeleted, false),
    ];

    // Build filter conditions
    const filterConditions = [];

    // Date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        filterConditions.push(gte(locations.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        filterConditions.push(lte(locations.createdAt, toDate));
      }
    }

    // Text filtering
    if (name) {
      filterConditions.push(ilike(locations.name, `%${name}%`));
    }

    if (code) {
      filterConditions.push(ilike(locations.code, `%${code}%`));
    }

    if (type) {
      filterConditions.push(eq(locations.type, type as any));
    }

    if (status) {
      filterConditions.push(eq(locations.status, status as any));
    }

    // Advanced filters support
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        if (Array.isArray(parsedFilters)) {
          parsedFilters.forEach((filter) => {
            const { id, value, operator } = filter;
            if (id && value !== undefined && operator) {
              switch (id) {
                case 'name':
                  if (operator === 'iLike') {
                    filterConditions.push(ilike(locations.name, `%${value}%`));
                  } else if (operator === 'eq') {
                    filterConditions.push(eq(locations.name, value));
                  }
                  break;
                case 'code':
                  if (operator === 'iLike') {
                    filterConditions.push(ilike(locations.code, `%${value}%`));
                  } else if (operator === 'eq') {
                    filterConditions.push(eq(locations.code, value));
                  }
                  break;
                case 'type':
                  if (operator === 'eq') {
                    filterConditions.push(eq(locations.type, value));
                  }
                  break;
                case 'status':
                  if (operator === 'eq') {
                    filterConditions.push(eq(locations.status, value));
                  }
                  break;
              }
            }
          });
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Combine conditions based on join operator
    let whereCondition;
    if (filterConditions.length > 0) {
      const combinedFilters =
        joinOperator === 'or'
          ? or(...filterConditions)
          : and(...filterConditions);
      whereCondition = and(...baseConditions, combinedFilters);
    } else {
      whereCondition = and(...baseConditions);
    }

    // Build sort order
    let orderBy;
    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (Array.isArray(parsedSort) && parsedSort.length > 0) {
          const { id, desc: descOrder } = parsedSort[0];
          const sortDirection = descOrder ? desc : asc;

          switch (id) {
            case 'name':
              orderBy = sortDirection(locations.name);
              break;
            case 'code':
              orderBy = sortDirection(locations.code);
              break;
            case 'type':
              orderBy = sortDirection(locations.type);
              break;
            case 'status':
              orderBy = sortDirection(locations.status);
              break;
            case 'createdAt':
              orderBy = sortDirection(locations.createdAt);
              break;
            default:
              orderBy = desc(locations.createdAt);
          }
        } else {
          orderBy = desc(locations.createdAt);
        }
      } catch {
        orderBy = desc(locations.createdAt);
      }
    } else {
      orderBy = desc(locations.createdAt);
    }

    // Find all locations - only select required fields
    const result = await this.db
      .select({
        id: locations.id,
        name: locations.name,
        code: locations.code,
        type: locations.type,
        status: locations.status,
      })
      .from(locations)
      .where(whereCondition)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(locations)
      .where(whereCondition);

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return {
      data: result.map((row) => ({
        id: row.id,
        name: row.name,
        code: row.code,
        type: row.type,
        status: row.status,
      })),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    code?: string,
    type?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: LocationListItemDto[];
    meta: {
      total: number;
      page: number;
      totalPages: number;
    };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const baseConditions = [
      eq(locations.businessId, businessId),
      eq(locations.isDeleted, false),
    ];

    // Build filter conditions
    const filterConditions = [];

    // Date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        filterConditions.push(gte(locations.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        filterConditions.push(lte(locations.createdAt, toDate));
      }
    }

    // Text filtering
    if (name) {
      filterConditions.push(ilike(locations.name, `%${name}%`));
    }

    if (code) {
      filterConditions.push(ilike(locations.code, `%${code}%`));
    }

    if (type) {
      filterConditions.push(eq(locations.type, type as any));
    }

    if (status) {
      filterConditions.push(eq(locations.status, status as any));
    }

    // Combine conditions based on join operator
    let whereCondition;
    if (filterConditions.length > 0) {
      const combinedFilters =
        joinOperator === 'or'
          ? or(...filterConditions)
          : and(...filterConditions);
      whereCondition = and(...baseConditions, combinedFilters);
    } else {
      whereCondition = and(...baseConditions);
    }

    // Build sort order
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const sortDirection = direction === 'desc' ? desc : asc;

      switch (field) {
        case 'name':
          orderBy = sortDirection(locations.name);
          break;
        case 'code':
          orderBy = sortDirection(locations.code);
          break;
        case 'type':
          orderBy = sortDirection(locations.type);
          break;
        case 'status':
          orderBy = sortDirection(locations.status);
          break;
        case 'createdAt':
          orderBy = sortDirection(locations.createdAt);
          break;
        case 'updatedAt':
          orderBy = sortDirection(locations.updatedAt);
          break;
        default:
          orderBy = desc(locations.createdAt);
      }
    } else {
      orderBy = desc(locations.createdAt);
    }

    // Find locations with addresses
    const result = await this.db
      .select({
        location: locations,
        address: addresses,
      })
      .from(locations)
      .leftJoin(addresses, eq(locations.addressId, addresses.id))
      .where(whereCondition)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(locations)
      .leftJoin(addresses, eq(locations.addressId, addresses.id))
      .where(whereCondition);

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result.map((row) =>
        this.mapToLocationListItemDto(row.location, row.address),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingLocation = await this.db
      .select({ id: locations.id })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          ilike(locations.name, name),
          eq(locations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingLocation };
  }

  async checkCodeAvailability(
    userId: string,
    businessId: string | null,
    code: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingLocation = await this.db
      .select({ id: locations.id })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          eq(locations.code, code),
          eq(locations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingLocation };
  }

  async findOne(userId: string, id: string): Promise<LocationDto> {
    // Get the location with address
    const result = await this.db
      .select({
        location: locations,
        address: addresses,
      })
      .from(locations)
      .leftJoin(addresses, eq(locations.addressId, addresses.id))
      .where(and(eq(locations.id, id), eq(locations.isDeleted, false)))
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(`Location with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== result.location.businessId
    ) {
      throw new UnauthorizedException('Access denied to this location');
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return this.mapToLocationDto(result.location, result.address);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateLocationDto: UpdateLocationDto,
  ): Promise<LocationDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Get existing location
      const existingResult = await this.db
        .select({
          location: locations,
          address: addresses,
        })
        .from(locations)
        .leftJoin(addresses, eq(locations.addressId, addresses.id))
        .where(
          and(
            eq(locations.id, id),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingResult) {
        throw new NotFoundException(`Location with ID ${id} not found`);
      }

      // Check if this update would leave business without any active main site locations
      const willHaveActiveMainSite = await this.checkActiveMainSiteWillRemain(
        businessId,
        id,
        {
          type: updateLocationDto.type,
          status: updateLocationDto.status,
        },
      );

      if (!willHaveActiveMainSite) {
        throw new BadRequestException(
          'Cannot perform this update as it would leave the business without any active main site locations. Each business must have at least one active main site location.',
        );
      }

      // Check name uniqueness if name is being updated
      if (
        updateLocationDto.name &&
        updateLocationDto.name !== existingResult.location.name
      ) {
        const existingNameLocation = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              ilike(locations.name, updateLocationDto.name),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingNameLocation) {
          throw new ConflictException(
            `A location with the name '${updateLocationDto.name}' already exists for this business`,
          );
        }
      }

      // Check code uniqueness if code is being updated
      if (
        updateLocationDto.code &&
        updateLocationDto.code !== existingResult.location.code
      ) {
        const existingCodeLocation = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              eq(locations.code, updateLocationDto.code),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingCodeLocation) {
          throw new ConflictException(
            `A location with code '${updateLocationDto.code}' already exists for this business`,
          );
        }
      }

      // Validate linked locations if provided
      if (updateLocationDto.linkedLocationIds) {
        const linkedLocations = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              inArray(locations.id, updateLocationDto.linkedLocationIds),
              eq(locations.isDeleted, false),
            ),
          );

        if (
          linkedLocations.length !== updateLocationDto.linkedLocationIds.length
        ) {
          throw new BadRequestException(
            'Some linked locations do not exist or belong to this business',
          );
        }
      }

      // Update address if provided
      if (updateLocationDto.address) {
        await this.db
          .update(addresses)
          .set({
            ...updateLocationDto.address,
            updatedAt: new Date(),
          })
          .where(eq(addresses.id, existingResult.location.addressId));
      }

      // Update location
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      if (updateLocationDto.name) updateData.name = updateLocationDto.name;
      if (updateLocationDto.code) updateData.code = updateLocationDto.code;
      if (updateLocationDto.type) updateData.type = updateLocationDto.type;
      if (updateLocationDto.status)
        updateData.status = updateLocationDto.status;
      if (updateLocationDto.linkedLocationIds !== undefined) {
        updateData.linkedLocationIds = updateLocationDto.linkedLocationIds;
      }

      const [updatedLocation] = await this.db
        .update(locations)
        .set(updateData)
        .where(eq(locations.id, id))
        .returning();

      // Get updated address
      const updatedAddress = await this.db
        .select()
        .from(addresses)
        .where(eq(addresses.id, updatedLocation.addressId))
        .then((results) => results[0]);

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.LOCATION,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return this.mapToLocationDto(updatedLocation, updatedAddress);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update location: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateLocationDto: UpdateLocationDto,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateLocationDto);
    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string; locationId: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if location exists and belongs to the business
      const location = await this.db
        .select()
        .from(locations)
        .where(
          and(
            eq(locations.id, id),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!location) {
        throw new NotFoundException(`Location with ID ${id} not found`);
      }

      // Check if deleting this location would leave business without any active main site locations
      if (
        location.type === LocationType.MAINSITE &&
        location.status === LocationStatus.ACTIVE
      ) {
        const willHaveActiveMainSite = await this.checkActiveMainSiteWillRemain(
          businessId,
          id, // exclude this location from the count
        );

        if (!willHaveActiveMainSite) {
          throw new BadRequestException(
            'Cannot delete this location as it would leave the business without any active main site locations. Each business must have at least one active main site location.',
          );
        }
      }

      // Soft delete the location
      await this.db
        .update(locations)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(locations.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        id,
        EntityType.LOCATION,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        success: true,
        message: 'Location deleted successfully',
        locationId: id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete location: ${error.message}`,
      );
    }
  }

  async findAllSlim(businessId: string | null): Promise<LocationSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: locations.id,
        name: locations.name,
        type: locations.type,
        status: locations.status,
      })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          eq(locations.isDeleted, false),
        ),
      )
      .orderBy(locations.name);

    return result;
  }

  async updateUserAssignment(
    adminUserId: string,
    businessId: string | null,
    locationId: string,
    userId: string,
    assigned: boolean,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify location exists and belongs to business
      const location = await this.db
        .select()
        .from(locations)
        .where(
          and(
            eq(locations.id, locationId),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!location) {
        throw new NotFoundException('Location not found');
      }

      // Check if user assignment already exists
      const existingAssignment = await this.db
        .select()
        .from(userLocations)
        .where(
          and(
            eq(userLocations.userId, userId),
            eq(userLocations.locationId, locationId),
            eq(userLocations.businessId, businessId),
          ),
        )
        .then((results) => results[0]);

      if (assigned) {
        if (!existingAssignment) {
          // Create new assignment
          await this.db.insert(userLocations).values({
            createdBy: adminUserId,
            userId,
            locationId,
            businessId,
            status: 'active' as any,
          });

          await this.activityLogService.logCreate(
            locationId,
            EntityType.LOCATION,
            adminUserId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );
        }
      } else {
        if (existingAssignment) {
          // Remove assignment
          await this.db
            .delete(userLocations)
            .where(
              and(
                eq(userLocations.userId, userId),
                eq(userLocations.locationId, locationId),
                eq(userLocations.businessId, businessId),
              ),
            );

          await this.activityLogService.logDelete(
            locationId,
            EntityType.LOCATION,
            adminUserId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );
        }
      }

      return {
        success: true,
        message: assigned
          ? 'User assigned to location successfully'
          : 'User unassigned from location successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update user assignment: ${error.message}`,
      );
    }
  }

  private mapToLocationDto(
    location: typeof locations.$inferSelect,
    address?: typeof addresses.$inferSelect | null,
  ): LocationDto {
    return {
      id: location.id,
      businessId: location.businessId,
      name: location.name,
      code: location.code,
      address: address
        ? {
            id: address.id,
            street: address.street,
            city: address.city,
            state: address.state,
            zipCode: address.zipCode,
            country: address.country,
            addressType: address.addressType,
            isDefault: address.isDefault,
          }
        : undefined,
      type: location.type,
      status: location.status,
      linkedLocationIds: location.linkedLocationIds,
      createdBy: location.createdBy,
      updatedBy: location.updatedBy,
      createdAt: location.createdAt,
      updatedAt: location.updatedAt,
    } as LocationDto;
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createLocationsDto: CreateLocationDto[],
  ): Promise<LocationDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createLocationsDto || createLocationsDto.length === 0) {
        throw new BadRequestException('No locations provided for creation');
      }

      // Validate unique names and codes within the batch
      const names = createLocationsDto.map((dto) => dto.name.toLowerCase());
      const codes = createLocationsDto.map((dto) => dto.code.toLowerCase());

      if (new Set(names).size !== names.length) {
        throw new ConflictException(
          'Duplicate location names found in the batch',
        );
      }

      if (new Set(codes).size !== codes.length) {
        throw new ConflictException(
          'Duplicate location codes found in the batch',
        );
      }

      // Check for existing names and codes in the database
      const existingLocations = await this.db
        .select({
          name: locations.name,
          code: locations.code,
        })
        .from(locations)
        .where(
          and(
            eq(locations.businessId, businessId),
            or(
              inArray(sql`LOWER(${locations.name})`, names),
              inArray(sql`LOWER(${locations.code})`, codes),
            ),
            eq(locations.isDeleted, false),
          ),
        );

      if (existingLocations.length > 0) {
        const existingNames = existingLocations.map((loc) =>
          loc.name.toLowerCase(),
        );
        const existingCodes = existingLocations.map((loc) =>
          loc.code.toLowerCase(),
        );

        const duplicateNames = names.filter((name) =>
          existingNames.includes(name),
        );
        const duplicateCodes = codes.filter((code) =>
          existingCodes.includes(code),
        );

        const duplicates = [...duplicateNames, ...duplicateCodes];
        throw new ConflictException(
          `The following location names/codes already exist: ${duplicates.join(', ')}`,
        );
      }

      // Validate linked locations if any are provided
      const allLinkedLocationIds = createLocationsDto
        .flatMap((dto) => dto.linkedLocationIds || [])
        .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates

      if (allLinkedLocationIds.length > 0) {
        const linkedLocations = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              inArray(locations.id, allLinkedLocationIds),
              eq(locations.isDeleted, false),
            ),
          );

        if (linkedLocations.length !== allLinkedLocationIds.length) {
          throw new BadRequestException(
            'Some linked locations do not exist or belong to this business',
          );
        }
      }

      const createdLocations: LocationDto[] = [];

      // Process each location in the batch
      for (const createLocationDto of createLocationsDto) {
        // Create address first
        const addressData = {
          ...createLocationDto.address,
          addressType:
            createLocationDto.address.addressType || AddressType.LOCATION,
          businessId: businessId,
          isDefault: createLocationDto.address.isDefault || false,
        };

        const [newAddress] = await this.db
          .insert(addresses)
          .values(addressData)
          .returning();

        // Create location
        const [newLocation] = await this.db
          .insert(locations)
          .values({
            businessId: businessId,
            name: createLocationDto.name,
            code: createLocationDto.code,
            addressId: newAddress.id,
            type: createLocationDto.type || LocationType.MAINSITE,
            status: createLocationDto.status || LocationStatus.ACTIVE,
            linkedLocationIds: createLocationDto.linkedLocationIds || [],
            createdBy: userId,
          })
          .returning();

        // Log the location creation activity
        await this.activityLogService.logCreate(
          newLocation.id,
          EntityType.LOCATION,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );

        createdLocations.push(this.mapToLocationDto(newLocation, newAddress));
      }

      return createdLocations;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create locations: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createLocationsDto: CreateLocationDto[],
  ): Promise<{ ids: string[] }> {
    const locations = await this.bulkCreate(
      userId,
      businessId,
      createLocationsDto,
    );
    return { ids: locations.map((location) => location.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    locationIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!locationIds || locationIds.length === 0) {
        throw new BadRequestException('No location IDs provided for deletion');
      }

      // Get all locations that exist and belong to the business
      const existingLocations = await this.db
        .select({
          id: locations.id,
          name: locations.name,
          businessId: locations.businessId,
          type: locations.type,
          status: locations.status,
        })
        .from(locations)
        .where(
          and(
            inArray(locations.id, locationIds),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        );

      if (existingLocations.length === 0) {
        throw new NotFoundException('No valid locations found for deletion');
      }

      // Check if any of the locations to be deleted are the only active main site
      const mainSiteLocationsToDelete = existingLocations.filter(
        (loc) =>
          loc.type === LocationType.MAINSITE &&
          loc.status === LocationStatus.ACTIVE,
      );

      if (mainSiteLocationsToDelete.length > 0) {
        // Check if there will be at least one active main site remaining after deletion
        for (const mainSiteLocation of mainSiteLocationsToDelete) {
          const willHaveActiveMainSite =
            await this.checkActiveMainSiteWillRemain(
              businessId,
              mainSiteLocation.id,
            );

          if (!willHaveActiveMainSite) {
            throw new BadRequestException(
              `Cannot delete location "${mainSiteLocation.name}" as it is the only active main site for this business`,
            );
          }
        }
      }

      const deletedIds: string[] = [];
      const now = new Date();

      // Perform soft delete for each location
      for (const location of existingLocations) {
        await this.db
          .update(locations)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: now,
          })
          .where(eq(locations.id, location.id));

        // Log the location deletion activity
        await this.activityLogService.logDelete(
          location.id,
          EntityType.LOCATION,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );

        deletedIds.push(location.id);
      }

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} locations`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete locations: ${error.message}`,
      );
    }
  }

  async bulkUpdateLocationStatus(
    userId: string,
    businessId: string | null,
    locationIds: string[],
    status: LocationStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ locationId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!locationIds || locationIds.length === 0) {
        throw new BadRequestException(
          'No location IDs provided for status update',
        );
      }

      // Get all locations that exist and belong to the business
      const existingLocations = await this.db
        .select({
          id: locations.id,
          name: locations.name,
          businessId: locations.businessId,
          type: locations.type,
          status: locations.status,
        })
        .from(locations)
        .where(
          and(
            inArray(locations.id, locationIds),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        );

      const updatedIds: string[] = [];
      const failed: Array<{ locationId: string; error: string }> = [];

      // Process each location
      for (const locationId of locationIds) {
        try {
          const location = existingLocations.find(
            (loc) => loc.id === locationId,
          );

          if (!location) {
            failed.push({
              locationId,
              error: 'Location not found or access denied',
            });
            continue;
          }

          // Skip if status is already the same
          if (location.status === status) {
            updatedIds.push(locationId);
            continue;
          }

          // Special validation for main site locations
          if (
            location.type === LocationType.MAINSITE &&
            location.status === LocationStatus.ACTIVE &&
            status === LocationStatus.INACTIVE
          ) {
            // Check if there will be at least one active main site remaining
            const willHaveActiveMainSite =
              await this.checkActiveMainSiteWillRemain(businessId, locationId, {
                status,
              });

            if (!willHaveActiveMainSite) {
              failed.push({
                locationId,
                error:
                  'Cannot deactivate the only active main site for this business',
              });
              continue;
            }
          }

          // Update the location status
          await this.db
            .update(locations)
            .set({
              status,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(locations.id, locationId));

          // Log the status update activity
          await this.activityLogService.logUpdate(
            locationId,
            EntityType.LOCATION,
            userId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );

          updatedIds.push(locationId);
        } catch (error) {
          failed.push({
            locationId,
            error: error.message || 'Unknown error occurred',
          });
        }
      }

      return {
        updated: updatedIds.length,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update location status: ${error.message}`,
      );
    }
  }

  private async checkActiveMainSiteWillRemain(
    businessId: string,
    excludeLocationId?: string,
    updatingLocationData?: { type?: LocationType; status?: LocationStatus },
  ): Promise<boolean> {
    // Get all main site locations for the business (excluding deleted ones)
    const mainSiteLocations = await this.db
      .select({
        id: locations.id,
        type: locations.type,
        status: locations.status,
      })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          eq(locations.type, LocationType.MAINSITE),
          eq(locations.isDeleted, false),
        ),
      );

    // Count active main sites after the proposed change
    let activeMainSiteCount = 0;

    for (const mainSite of mainSiteLocations) {
      // If this is the location being updated/deleted
      if (excludeLocationId && mainSite.id === excludeLocationId) {
        // If we're updating and changing type or status, use the new values
        if (updatingLocationData) {
          const newType = updatingLocationData.type ?? mainSite.type;
          const newStatus = updatingLocationData.status ?? mainSite.status;

          if (
            newType === LocationType.MAINSITE &&
            newStatus === LocationStatus.ACTIVE
          ) {
            activeMainSiteCount++;
          }
        }
        // If we're deleting (no updatingLocationData), don't count this location
      } else {
        // For other locations, count if they are active main sites
        if (mainSite.status === LocationStatus.ACTIVE) {
          activeMainSiteCount++;
        }
      }
    }

    return activeMainSiteCount > 0;
  }

  private mapToLocationListItemDto(
    location: typeof locations.$inferSelect,
    address?: typeof addresses.$inferSelect | null,
  ): LocationListItemDto {
    return {
      id: location.id,
      name: location.name,
      code: location.code,
      type: location.type,
      status: location.status,
      city: address?.city,
      state: address?.state,
      country: address?.country,
      linkedLocationsCount: location.linkedLocationIds?.length || 0,
      createdAt: location.createdAt,
    };
  }
}
