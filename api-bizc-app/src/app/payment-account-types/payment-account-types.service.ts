import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { paymentAccountTypes } from '../drizzle/schema/payment-account-types.schema';
import { CreatePaymentAccountTypeDto } from './dto/create-payment-account-type.dto';
import { UpdatePaymentAccountTypeDto } from './dto/update-payment-account-type.dto';
import { PaymentAccountTypeDto } from './dto/payment-account-type.dto';
import { PaymentAccountTypeListDto } from './dto/payment-account-type-list.dto';
import {
  and,
  eq,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  count,
  inArray,
  not,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { StatusType } from '../shared/types';

@Injectable()
export class PaymentAccountTypesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPaymentAccountTypeDto: CreatePaymentAccountTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<PaymentAccountTypeDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a payment account type with the same name already exists for this business
      const existingPaymentAccountType = await this.db
        .select()
        .from(paymentAccountTypes)
        .where(
          and(
            eq(paymentAccountTypes.businessId, businessId),
            ilike(paymentAccountTypes.name, createPaymentAccountTypeDto.name),
          ),
        )
        .then((results) => results[0]);

      if (existingPaymentAccountType) {
        throw new ConflictException(
          `A payment account type with the name '${createPaymentAccountTypeDto.name}' already exists for this business`,
        );
      }

      // If parentAccountTypeId is provided, verify it exists and belongs to the same business
      if (createPaymentAccountTypeDto.parentAccountTypeId) {
        const parentAccountType = await this.db
          .select()
          .from(paymentAccountTypes)
          .where(
            and(
              eq(
                paymentAccountTypes.id,
                createPaymentAccountTypeDto.parentAccountTypeId,
              ),
              eq(paymentAccountTypes.businessId, businessId),
            ),
          )
          .then((results) => results[0]);

        if (!parentAccountType) {
          throw new BadRequestException(
            'Parent account type not found or does not belong to this business',
          );
        }
      }

      // Create the payment account type
      const [newPaymentAccountType] = await this.db
        .insert(paymentAccountTypes)
        .values({
          businessId,
          name: createPaymentAccountTypeDto.name,
          parentAccountTypeId: createPaymentAccountTypeDto.parentAccountTypeId,
          status: (createPaymentAccountTypeDto.status ||
            StatusType.ACTIVE) as any,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        newPaymentAccountType.id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Created payment account type: ${createPaymentAccountTypeDto.name}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.mapToPaymentAccountTypeDto(newPaymentAccountType);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create payment account type: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: PaymentAccountTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;
      const whereConditions = [eq(paymentAccountTypes.businessId, businessId)];

      // Date range filtering
      if (from) {
        const fromDate = new Date(from);
        if (!isNaN(fromDate.getTime())) {
          whereConditions.push(gte(paymentAccountTypes.createdAt, fromDate));
        }
      }

      if (to) {
        const toDate = new Date(to);
        if (!isNaN(toDate.getTime())) {
          // Add 23:59:59 to include the entire day
          toDate.setHours(23, 59, 59, 999);
          whereConditions.push(lte(paymentAccountTypes.createdAt, toDate));
        }
      }

      // Name filtering
      if (name) {
        whereConditions.push(ilike(paymentAccountTypes.name, `%${name}%`));
      }

      // Status filtering
      if (status) {
        // Decode URL-encoded commas and split by comma
        const decodedStatus = decodeURIComponent(status);
        const statusValues = decodedStatus.split(',').map((s) => s.trim());

        // Use the proper Drizzle ORM method for IN clause
        if (statusValues.length === 1) {
          whereConditions.push(
            eq(paymentAccountTypes.status, statusValues[0] as any),
          );
        } else {
          whereConditions.push(
            inArray(paymentAccountTypes.status, statusValues as any),
          );
        }
      }

      // Advanced filters
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = parsedFilters.map((filter: any) => {
            const { id, value, operator } = filter;

            switch (id) {
              case 'name':
                switch (operator) {
                  case 'iLike':
                    return ilike(paymentAccountTypes.name, `%${value}%`);
                  case 'notILike':
                    return sql`NOT ${ilike(paymentAccountTypes.name, `%${value}%`)}`;
                  case 'eq':
                    return eq(paymentAccountTypes.name, value);
                  case 'ne':
                    return sql`${paymentAccountTypes.name} != ${value}`;
                  case 'isEmpty':
                    return sql`${paymentAccountTypes.name} IS NULL OR ${paymentAccountTypes.name} = ''`;
                  case 'isNotEmpty':
                    return sql`${paymentAccountTypes.name} IS NOT NULL AND ${paymentAccountTypes.name} != ''`;
                  default:
                    return ilike(paymentAccountTypes.name, `%${value}%`);
                }
              case 'status':
                switch (operator) {
                  case 'eq':
                    return eq(paymentAccountTypes.status, value);
                  case 'ne':
                    return sql`${paymentAccountTypes.status} != ${value}`;
                  default:
                    return eq(paymentAccountTypes.status, value);
                }
              default:
                return sql`1=1`;
            }
          });

          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              whereConditions.push(or(...filterConditions));
            } else {
              whereConditions.push(and(...filterConditions));
            }
          }
        } catch {
          // Invalid JSON, ignore filters
        }
      }

      // Sorting
      let orderBy = [desc(paymentAccountTypes.updatedAt)];
      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          orderBy = parsedSort.map((s: any) => {
            const direction = s.desc ? desc : asc;
            switch (s.id) {
              case 'name':
                return direction(paymentAccountTypes.name);
              case 'createdAt':
                return direction(paymentAccountTypes.createdAt);
              case 'updatedAt':
                return direction(paymentAccountTypes.updatedAt);
              default:
                return direction(paymentAccountTypes.updatedAt);
            }
          });
        } catch {
          // Invalid JSON, use default sorting
        }
      }

      const whereClause = and(...whereConditions);

      // Get total count
      const totalResult = await this.db
        .select({ count: count() })
        .from(paymentAccountTypes)
        .where(whereClause);

      const total = totalResult[0]?.count || 0;
      const totalPages = Math.ceil(total / limit);

      // Get paginated data
      const results = await this.db
        .select()
        .from(paymentAccountTypes)
        .where(whereClause)
        .orderBy(...orderBy)
        .limit(limit)
        .offset(offset);

      const data = results.map((result) =>
        this.mapToPaymentAccountTypeDto(result),
      );

      return {
        data,
        meta: {
          total,
          page,
          totalPages,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch payment account types: ${error.message}`,
      );
    }
  }

  async findOne(userId: string, id: string): Promise<PaymentAccountTypeDto> {
    try {
      const paymentAccountType = await this.db
        .select()
        .from(paymentAccountTypes)
        .where(eq(paymentAccountTypes.id, id))
        .then((results) => results[0]);

      if (!paymentAccountType) {
        throw new NotFoundException(
          `Payment account type with ID ${id} not found`,
        );
      }

      return this.mapToPaymentAccountTypeDto(paymentAccountType);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch payment account type: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePaymentAccountTypeDto: UpdatePaymentAccountTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<PaymentAccountTypeDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the payment account type exists and belongs to the business
      const existingPaymentAccountType = await this.db
        .select()
        .from(paymentAccountTypes)
        .where(
          and(
            eq(paymentAccountTypes.id, id),
            eq(paymentAccountTypes.businessId, businessId),
          ),
        )
        .then((results) => results[0]);

      if (!existingPaymentAccountType) {
        throw new NotFoundException(
          `Payment account type with ID ${id} not found for this business`,
        );
      }

      // If name is being updated, check for duplicates
      if (
        updatePaymentAccountTypeDto.name &&
        updatePaymentAccountTypeDto.name !== existingPaymentAccountType.name
      ) {
        const duplicatePaymentAccountType = await this.db
          .select()
          .from(paymentAccountTypes)
          .where(
            and(
              eq(paymentAccountTypes.businessId, businessId),
              ilike(paymentAccountTypes.name, updatePaymentAccountTypeDto.name),
              sql`${paymentAccountTypes.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (duplicatePaymentAccountType) {
          throw new ConflictException(
            `A payment account type with the name '${updatePaymentAccountTypeDto.name}' already exists for this business`,
          );
        }
      }

      // If parentAccountTypeId is being updated, verify it exists and belongs to the same business
      if (updatePaymentAccountTypeDto.parentAccountTypeId !== undefined) {
        if (updatePaymentAccountTypeDto.parentAccountTypeId) {
          const parentAccountType = await this.db
            .select()
            .from(paymentAccountTypes)
            .where(
              and(
                eq(
                  paymentAccountTypes.id,
                  updatePaymentAccountTypeDto.parentAccountTypeId,
                ),
                eq(paymentAccountTypes.businessId, businessId),
              ),
            )
            .then((results) => results[0]);

          if (!parentAccountType) {
            throw new BadRequestException(
              'Parent account type not found or does not belong to this business',
            );
          }

          // Prevent setting parent to itself or creating circular references
          if (updatePaymentAccountTypeDto.parentAccountTypeId === id) {
            throw new BadRequestException(
              'Payment account type cannot be its own parent',
            );
          }
        }
      }

      // Update the payment account type
      const updateData: any = {
        ...updatePaymentAccountTypeDto,
        updatedAt: new Date(),
      };

      const [updatedPaymentAccountType] = await this.db
        .update(paymentAccountTypes)
        .set(updateData)
        .where(eq(paymentAccountTypes.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.mapToPaymentAccountTypeDto(updatedPaymentAccountType);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update payment account type: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string; id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the payment account type exists and belongs to the business
      const existingPaymentAccountType = await this.db
        .select()
        .from(paymentAccountTypes)
        .where(
          and(
            eq(paymentAccountTypes.id, id),
            eq(paymentAccountTypes.businessId, businessId),
          ),
        )
        .then((results) => results[0]);

      if (!existingPaymentAccountType) {
        throw new NotFoundException(
          `Payment account type with ID ${id} not found for this business`,
        );
      }

      // Delete the payment account type
      await this.db
        .delete(paymentAccountTypes)
        .where(eq(paymentAccountTypes.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Deleted payment account type: ${existingPaymentAccountType.name}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        success: true,
        message: 'Payment account type successfully deleted',
        id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete payment account type: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean; name: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingPaymentAccountType = await this.db
        .select()
        .from(paymentAccountTypes)
        .where(
          and(
            eq(paymentAccountTypes.businessId, businessId),
            ilike(paymentAccountTypes.name, name),
          ),
        )
        .then((results) => results[0]);

      const available = !existingPaymentAccountType;
      const message = available
        ? 'Payment account type name is available'
        : 'Payment account type name is already taken';

      return { available, name, message };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to check name availability: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPaymentAccountTypeDto: CreatePaymentAccountTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const createdResult = await this.create(
      userId,
      businessId,
      createPaymentAccountTypeDto,
      metadata,
    );

    return { id: createdResult.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePaymentAccountTypeDto: UpdatePaymentAccountTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const updatedResult = await this.update(
      userId,
      businessId,
      id,
      updatePaymentAccountTypeDto,
      metadata,
    );
    return { id: updatedResult.id };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: PaymentAccountTypeListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;
      const whereConditions = [eq(paymentAccountTypes.businessId, businessId)];

      // Date range filtering
      if (from) {
        const fromDate = new Date(from);
        if (!isNaN(fromDate.getTime())) {
          whereConditions.push(gte(paymentAccountTypes.createdAt, fromDate));
        }
      }

      if (to) {
        const toDate = new Date(to);
        if (!isNaN(toDate.getTime())) {
          // Add 23:59:59 to include the entire day
          toDate.setHours(23, 59, 59, 999);
          whereConditions.push(lte(paymentAccountTypes.createdAt, toDate));
        }
      }

      // Name filtering
      if (name) {
        whereConditions.push(ilike(paymentAccountTypes.name, `%${name}%`));
      }

      // Status filtering
      if (status) {
        // Decode URL-encoded commas and split by comma
        const decodedStatus = decodeURIComponent(status);
        const statusValues = decodedStatus.split(',').map((s) => s.trim());

        // Use the proper Drizzle ORM method for IN clause
        if (statusValues.length === 1) {
          whereConditions.push(
            eq(paymentAccountTypes.status, statusValues[0] as any),
          );
        } else {
          whereConditions.push(
            inArray(paymentAccountTypes.status, statusValues as any),
          );
        }
      }

      // Advanced filters
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = parsedFilters
            .map((filter: any) => {
              const { id, value, operator } = filter;

              switch (id) {
                case 'name':
                  switch (operator) {
                    case 'iLike':
                      return ilike(paymentAccountTypes.name, `%${value}%`);
                    case 'notILike':
                      return sql`NOT ${ilike(paymentAccountTypes.name, `%${value}%`)}`;
                    case 'eq':
                      return eq(paymentAccountTypes.name, value);
                    case 'ne':
                      return sql`${paymentAccountTypes.name} != ${value}`;
                    case 'isEmpty':
                      return sql`${paymentAccountTypes.name} IS NULL OR ${paymentAccountTypes.name} = ''`;
                    case 'isNotEmpty':
                      return sql`${paymentAccountTypes.name} IS NOT NULL AND ${paymentAccountTypes.name} != ''`;
                    default:
                      return ilike(paymentAccountTypes.name, `%${value}%`);
                  }
                case 'status':
                  switch (operator) {
                    case 'eq':
                      if (Array.isArray(value)) {
                        return inArray(
                          paymentAccountTypes.status,
                          value as any,
                        );
                      } else {
                        return eq(paymentAccountTypes.status, value);
                      }
                    case 'ne':
                      if (Array.isArray(value)) {
                        return not(
                          inArray(paymentAccountTypes.status, value as any),
                        );
                      } else {
                        return sql`${paymentAccountTypes.status} != ${value}`;
                      }
                    case 'isEmpty':
                      return sql`${paymentAccountTypes.status} IS NULL`;
                    case 'isNotEmpty':
                      return sql`${paymentAccountTypes.status} IS NOT NULL`;
                    case 'iLike':
                      if (typeof value === 'string') {
                        const decodedValue = decodeURIComponent(value);
                        const statusValues = decodedValue
                          .split(',')
                          .map((s) => s.trim());
                        return inArray(
                          paymentAccountTypes.status,
                          statusValues as any,
                        );
                      } else {
                        return eq(paymentAccountTypes.status, value);
                      }
                    default:
                      if (Array.isArray(value)) {
                        return inArray(
                          paymentAccountTypes.status,
                          value as any,
                        );
                      } else {
                        return eq(paymentAccountTypes.status, value);
                      }
                  }
                default:
                  return null;
              }
            })
            .filter(Boolean);

          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              whereConditions.push(or(...filterConditions));
            } else {
              whereConditions.push(...filterConditions);
            }
          }
        } catch {
          // Invalid JSON, ignore filters
        }
      }

      // Sorting
      let orderByClause;
      if (sort) {
        try {
          const parsedSort = JSON.parse(sort);
          if (Array.isArray(parsedSort) && parsedSort.length > 0) {
            const sortConfig = parsedSort[0]; // Take first sort configuration
            if (sortConfig.id === 'name') {
              orderByClause = sortConfig.desc
                ? desc(paymentAccountTypes.name)
                : asc(paymentAccountTypes.name);
            } else if (sortConfig.id === 'createdAt') {
              orderByClause = sortConfig.desc
                ? desc(paymentAccountTypes.createdAt)
                : asc(paymentAccountTypes.createdAt);
            } else if (sortConfig.id === 'updatedAt') {
              orderByClause = sortConfig.desc
                ? desc(paymentAccountTypes.updatedAt)
                : asc(paymentAccountTypes.updatedAt);
            } else {
              orderByClause = desc(paymentAccountTypes.updatedAt);
            }
          } else {
            orderByClause = desc(paymentAccountTypes.updatedAt);
          }
        } catch {
          // Invalid JSON, use default sort
          orderByClause = desc(paymentAccountTypes.updatedAt);
        }
      } else {
        orderByClause = desc(paymentAccountTypes.updatedAt);
      }

      const whereClause = and(...whereConditions);

      // Get total count
      const totalResult = await this.db
        .select({ count: count() })
        .from(paymentAccountTypes)
        .where(whereClause);

      const total = totalResult[0]?.count || 0;
      const totalPages = Math.ceil(total / limit);

      // Get paginated data with parent type information (optimized)
      const results = await this.db
        .select({
          id: paymentAccountTypes.id,
          name: paymentAccountTypes.name,
          status: paymentAccountTypes.status,
          parentTypeId: paymentAccountTypes.parentAccountTypeId,
          parentTypeName: sql<string | null>`
            (SELECT name FROM ${paymentAccountTypes} AS parent_pat 
             WHERE parent_pat.id = ${paymentAccountTypes.parentAccountTypeId})
          `,
        })
        .from(paymentAccountTypes)
        .where(whereClause)
        .orderBy(orderByClause, desc(paymentAccountTypes.id)) // Add secondary sort by ID for stable ordering
        .limit(limit)
        .offset(offset);

      const data: PaymentAccountTypeListDto[] = results.map((result) => ({
        id: result.id.toString(),
        name: result.name,
        parentType:
          result.parentTypeId && result.parentTypeName
            ? {
                id: result.parentTypeId.toString(),
                name: result.parentTypeName,
              }
            : undefined,
        status: result.status as StatusType,
      }));

      // Disable view logging for performance in optimized queries

      return {
        data,
        meta: {
          total,
          page,
          totalPages,
        },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch payment account types: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<{ id: string; name: string; parentAccountTypeId?: string }[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Get only active payment account types with minimal fields
      const results = await this.db
        .select({
          id: paymentAccountTypes.id,
          name: paymentAccountTypes.name,
          parentAccountTypeId: paymentAccountTypes.parentAccountTypeId,
        })
        .from(paymentAccountTypes)
        .where(
          and(
            eq(paymentAccountTypes.businessId, businessId),
            // eq(paymentAccountTypes.status, StatusType.ACTIVE),
          ),
        )
        .orderBy(asc(paymentAccountTypes.name));

      return results.map((result) => ({
        id: result.id.toString(),
        name: result.name,
        parentAccountTypeId: result.parentAccountTypeId?.toString(),
      }));
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch payment account types: ${error.message}`,
      );
    }
  }

  private mapToPaymentAccountTypeDto(
    paymentAccountType: typeof paymentAccountTypes.$inferSelect,
  ): PaymentAccountTypeDto {
    return {
      id: paymentAccountType.id,
      businessId: paymentAccountType.businessId,
      name: paymentAccountType.name,
      parentAccountTypeId: paymentAccountType.parentAccountTypeId,
      status: paymentAccountType.status,
      createdBy: paymentAccountType.createdBy,
      createdAt: paymentAccountType.createdAt,
      updatedAt: paymentAccountType.updatedAt,
    };
  }
}
