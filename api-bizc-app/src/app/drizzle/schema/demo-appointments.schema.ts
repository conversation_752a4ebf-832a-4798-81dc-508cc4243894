import {
  boolean,
  date,
  index,
  pgEnum,
  pgTable,
  text,
  time,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { meetingTypeEnum } from './meetings.schema';
import { AppointmentStatus, MeetingType } from '../../shared/types';

export const demoAppointmentStatusEnum = pgEnum('demo_appointment_status', [
  AppointmentStatus.SCHEDULED,
  AppointmentStatus.COMPLETED,
  AppointmentStatus.CANCELLED,
  AppointmentStatus.EXPIRED,
]);

export const demoAppointments = pgTable(
  'demo_appointments',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    title: text('title').default('Demo Call'),
    description: text('description'),
    meetingType: meetingTypeEnum('meeting_type')
      .default(MeetingType.ZOOM_MEET)
      .notNull(),
    meetingLink: text('meeting_link'),
    appointmentDate: date('appointment_date').notNull(),
    startTime: time('start_time').notNull(),
    endTime: time('end_time').notNull(),
    durationMinutes: text('duration_minutes').default('60').notNull(),
    status: demoAppointmentStatusEnum('demo_appointment_status')
      .default(AppointmentStatus.SCHEDULED)
      .notNull(),
    isCancelled: boolean('is_cancelled').default(false),
    cancelReason: text('cancel_reason'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    assignedTo: uuid('assigned_to').references(() => users.id),
    cancelledAt: timestamp('cancelled_at'),
    rescheduledAt: timestamp('rescheduled_at'),
    completedAt: timestamp('completed_at'),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    businessIdIndex: index('demo_appointments_business_id_index').on(
      t.businessId,
    ),
    appointmentDateIndex: index('demo_appointments_appointment_date_index').on(
      t.appointmentDate,
    ),
    statusIndex: index('demo_appointments_status_index').on(t.status),
    assignedToIndex: index('demo_appointments_assigned_to_index').on(
      t.assignedTo,
    ),
  }),
);

// Relations
export const demoAppointmentsRelations = relations(
  demoAppointments,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [demoAppointments.businessId],
      references: [business.id],
    }),
    // User relations
    creator: one(users, {
      fields: [demoAppointments.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [demoAppointments.updatedBy],
      references: [users.id],
    }),
    deleter: one(users, {
      fields: [demoAppointments.deletedBy],
      references: [users.id],
    }),
    assignedUser: one(users, {
      fields: [demoAppointments.assignedTo],
      references: [users.id],
    }),
  }),
);
