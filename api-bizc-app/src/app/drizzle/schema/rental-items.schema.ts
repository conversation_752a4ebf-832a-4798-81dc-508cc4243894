import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  varchar,
  decimal,
  integer,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { rentalItemCategories } from './rental-item-categories.schema';
import { assetTypes } from './asset-types.schema';
import { accounts } from './accounts.schema';
import { media } from './media.schema';
import { createBaseEntityFields } from './common-fields.schema';
import { users } from './users.schema';
import { RentalItemStatus } from '../../shared/types';

export const rentalItemStatusEnum = pgEnum('rental_item_status', [
  RentalItemStatus.ACTIVE,
  RentalItemStatus.INACTIVE,
]);

export const rentalItems = pgTable(
  'rental_items',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Basic Info
    itemCode: varchar('item_code', { length: 50 }).notNull(),
    itemName: varchar('item_name', { length: 200 }).notNull(),
    description: text('description'),

    // Rental Rates
    rentalRatePerDay: decimal('rental_rate_per_day', {
      precision: 10,
      scale: 2,
    }).notNull(),
    rentalRatePerWeek: decimal('rental_rate_per_week', {
      precision: 10,
      scale: 2,
    }),
    rentalRatePerMonth: decimal('rental_rate_per_month', {
      precision: 10,
      scale: 2,
    }),
    minimumRentalPeriod: integer('minimum_rental_period').default(1).notNull(), // in days

    // Foreign Key References
    categoryId: uuid('category_id').references(() => rentalItemCategories.id),
    subCategoryId: uuid('sub_category_id').references(
      () => rentalItemCategories.id,
    ),
    assetCategoryId: uuid('asset_category_id').references(() => assetTypes.id),
    assetSubCategoryId: uuid('asset_sub_category_id').references(
      () => assetTypes.id,
    ),
    fixedAssetAccountId: uuid('fixed_asset_account_id').references(
      () => accounts.id,
    ),
    depreciationAccountId: uuid('depreciation_account_id').references(
      () => accounts.id,
    ),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    // Position management - category-specific positioning
    categoryPosition: integer('category_position').default(0).notNull(),
    subCategoryPosition: integer('sub_category_position').default(0).notNull(),
    globalPosition: integer('global_position').default(0).notNull(),

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    // Status field
    status: rentalItemStatusEnum('status')
      .default(RentalItemStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('rental_items_id_index').on(t.id),
    businessIdIndex: index('rental_items_business_id_index').on(t.businessId),
    itemCodeIndex: index('rental_items_item_code_index').on(t.itemCode),
    itemNameIndex: index('rental_items_item_name_index').on(t.itemName),
    categoryIdIndex: index('rental_items_category_id_index').on(t.categoryId),
    subCategoryIdIndex: index('rental_items_sub_category_id_index').on(
      t.subCategoryId,
    ),
    assetCategoryIdIndex: index('rental_items_asset_category_id_index').on(
      t.assetCategoryId,
    ),
    assetSubCategoryIdIndex: index(
      'rental_items_asset_sub_category_id_index',
    ).on(t.assetSubCategoryId),
    fixedAssetAccountIdIndex: index(
      'rental_items_fixed_asset_account_id_index',
    ).on(t.fixedAssetAccountId),
    depreciationAccountIdIndex: index(
      'rental_items_depreciation_account_id_index',
    ).on(t.depreciationAccountId),
    expenseAccountIdIndex: index('rental_items_expense_account_id_index').on(
      t.expenseAccountId,
    ),
    ogImageIndex: index('rental_items_og_image_index').on(t.ogImage),
    statusIndex: index('rental_items_status_index').on(t.status),
    createdByIndex: index('rental_items_created_by_index').on(t.createdBy),
    createdAtIndex: index('rental_items_created_at_index').on(t.createdAt),
    categoryPositionIndex: index('rental_items_category_position_index').on(
      t.categoryPosition,
    ),
    subCategoryPositionIndex: index(
      'rental_items_sub_category_position_index',
    ).on(t.subCategoryPosition),
    globalPositionIndex: index('rental_items_global_position_index').on(
      t.globalPosition,
    ),

    // Optimized composite indexes for performance
    businessGlobalPositionIndex: index(
      'rental_items_business_global_position_index',
    )
      .on(t.businessId, t.globalPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessCategoryPositionIndex: index(
      'rental_items_business_category_position_index',
    )
      .on(t.businessId, t.categoryId, t.categoryPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessSubCategoryPositionIndex: index(
      'rental_items_business_sub_category_position_index',
    )
      .on(t.businessId, t.subCategoryId, t.subCategoryPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'rental_items_business_status_position_index',
    )
      .on(t.businessId, t.status, t.globalPosition, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessItemCodeIndex: index('rental_items_business_item_code_index')
      .on(t.businessId, t.itemCode)
      .where(sql`${t.isDeleted} = false`),
    businessItemNameIndex: index('rental_items_business_item_name_index')
      .on(t.businessId, t.itemName)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessItemCode: uniqueIndex(
      'rental_items_business_item_code_unique',
    )
      .on(t.businessId, t.itemCode)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessItemName: uniqueIndex(
      'rental_items_business_item_name_unique',
    )
      .on(t.businessId, t.itemName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const rentalItemsRelations = relations(rentalItems, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [rentalItems.businessId],
    references: [business.id],
  }),
  // Category relations
  category: one(rentalItemCategories, {
    fields: [rentalItems.categoryId],
    references: [rentalItemCategories.id],
  }),
  subCategory: one(rentalItemCategories, {
    fields: [rentalItems.subCategoryId],
    references: [rentalItemCategories.id],
  }),
  // Asset category relations
  assetCategory: one(assetTypes, {
    fields: [rentalItems.assetCategoryId],
    references: [assetTypes.id],
  }),
  assetSubCategory: one(assetTypes, {
    fields: [rentalItems.assetSubCategoryId],
    references: [assetTypes.id],
  }),
  // Account relations
  fixedAssetAccount: one(accounts, {
    fields: [rentalItems.fixedAssetAccountId],
    references: [accounts.id],
  }),
  depreciationAccount: one(accounts, {
    fields: [rentalItems.depreciationAccountId],
    references: [accounts.id],
  }),
  expenseAccount: one(accounts, {
    fields: [rentalItems.expenseAccountId],
    references: [accounts.id],
  }),
  // Media relation
  ogImageFile: one(media, {
    fields: [rentalItems.ogImage],
    references: [media.id],
  }),
  // Audit relations
  creator: one(users, {
    fields: [rentalItems.createdBy],
    references: [users.id],
  }),
  updater: one(users, {
    fields: [rentalItems.updatedBy],
    references: [users.id],
  }),
}));
