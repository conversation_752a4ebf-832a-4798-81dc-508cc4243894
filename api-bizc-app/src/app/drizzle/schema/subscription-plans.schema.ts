import {
  boolean,
  date,
  decimal,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import { accounts } from './accounts.schema';
import {
  products,
  productVariants,
  standardUnitOfMeasureEnum,
} from './products.schema';
import { services } from './services.schema';
import { units } from './units.schema';
import { customers } from './customers.schema';
import { customerGroups } from './customer-groups.schema';
import {
  RecurrenceEndType,
  recurrenceEndTypeEnum,
  RecurrencePattern,
  recurrencePatternEnum,
  recurringDayOfWeekEnum,
} from './recurring-activities.schema';
import {
  pricingFields,
  auditFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { DayOfWeek, StandardUnitOfMeasure } from '../../shared/types';

/**
 * Subscription Plans Schema
 *
 * This schema defines subscription plans with recurring billing functionality.
 * It includes plan details, status management, and comprehensive recurrence settings
 * for flexible subscription billing patterns.
 */

// Subscription Plan Status Enum
export enum SubscriptionPlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export const subscriptionPlanStatusEnum = pgEnum('subscription_plan_status', [
  SubscriptionPlanStatus.ACTIVE,
  SubscriptionPlanStatus.INACTIVE,
]);

// Recurrence Pattern Enum imported from recurring-activities.schema.ts

// Recurrence End Type Enum imported from recurring-activities.schema.ts

// Day of Week Enum imported from recurring-activities.schema.ts

// Main subscription plans table
export const subscriptionPlans = pgTable(
  'subscription_plans',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Plan basic information
    name: text('name').notNull(),
    description: text('description'),
    status: subscriptionPlanStatusEnum('status')
      .default(SubscriptionPlanStatus.ACTIVE)
      .notNull(),

    // Financial fields
    ...pricingFields,
    standardCost: decimal('standard_cost', {
      precision: 15,
      scale: 2,
    }).notNull(),
    incomeAccountId: uuid('income_account_id')
      .notNull()
      .references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    // Payment terms
    netDays: integer('net_days').default(30).notNull(), // Payment due in X days

    // Core recurrence settings
    recurrencePattern: recurrencePatternEnum('recurrence_pattern').notNull(),
    recurrenceInterval: integer('recurrence_interval').default(1).notNull(), // Every X days/weeks/months/years

    // End conditions
    recurrenceEndType: recurrenceEndTypeEnum('recurrence_end_type')
      .default(RecurrenceEndType.NEVER)
      .notNull(),
    recurrenceEndDate: date('recurrence_end_date'),
    recurrenceEndAfterOccurrences: integer('recurrence_end_after_occurrences'),

    // Pattern-specific settings
    recurrenceDaysOfWeek: recurringDayOfWeekEnum(
      'recurrence_days_of_week',
    ).array(), // For weekly recurrence
    recurrenceDayOfMonth: integer('recurrence_day_of_month'), // For monthly/yearly recurrence (1-31)
    recurrenceMonthOfYear: integer('recurrence_month_of_year'), // For yearly recurrence (1-12: Jan-Dec)

    // Tracking fields
    nextOccurrenceDate: date('next_occurrence_date'), // When the next occurrence should happen
    lastOccurrenceDate: date('last_occurrence_date'), // When the last occurrence happened
    occurrenceCount: integer('occurrence_count').default(0).notNull(), // How many times it has occurred

    // Additional deletion fields
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'subscription_plans'),

    // Status and pattern indexes
    statusIndex: index('subscription_plans_status_index').on(t.status),
    recurrencePatternIndex: index(
      'subscription_plans_recurrence_pattern_index',
    ).on(t.recurrencePattern),

    // Financial indexes
    basePriceIndex: index('subscription_plans_base_price_index').on(
      t.basePrice,
    ),
    taxTypeIndex: index('subscription_plans_tax_type_index').on(t.taxType),
    taxIdIndex: index('subscription_plans_tax_id_index').on(t.taxId),
    incomeAccountIdIndex: index(
      'subscription_plans_income_account_id_index',
    ).on(t.incomeAccountId),
    expenseAccountIdIndex: index(
      'subscription_plans_expense_account_id_index',
    ).on(t.expenseAccountId),
    netDaysIndex: index('subscription_plans_net_days_index').on(t.netDays),

    // Date-based indexes for scheduling
    nextOccurrenceDateIndex: index(
      'subscription_plans_next_occurrence_date_index',
    ).on(t.nextOccurrenceDate),
    lastOccurrenceDateIndex: index(
      'subscription_plans_last_occurrence_date_index',
    ).on(t.lastOccurrenceDate),
    recurrenceEndDateIndex: index(
      'subscription_plans_recurrence_end_date_index',
    ).on(t.recurrenceEndDate),

    // Month/day indexes for yearly/monthly patterns
    recurrenceMonthOfYearIndex: index(
      'subscription_plans_recurrence_month_of_year_index',
    ).on(t.recurrenceMonthOfYear),
    recurrenceDayOfMonthIndex: index(
      'subscription_plans_recurrence_day_of_month_index',
    ).on(t.recurrenceDayOfMonth),

    // Composite indexes for common queries with soft deletion support
    businessStatusIndex: index('subscription_plans_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessNextOccurrenceIndex: index(
      'subscription_plans_business_next_occurrence_index',
    )
      .on(t.businessId, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),

    // Scheduling query optimization
    activeNextOccurrenceIndex: index(
      'subscription_plans_active_next_occurrence_index',
    )
      .on(t.status, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),
    statusNextOccurrenceIndex: index(
      'subscription_plans_status_next_occurrence_index',
    )
      .on(t.status, t.nextOccurrenceDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for subscription plan products (many-to-many relationship)
export const subscriptionPlanProducts = pgTable(
  'subscription_plan_products',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'cascade',
    }),

    // Quantity and unit information
    quantity: decimal('quantity', { precision: 10, scale: 3 }).notNull(),
    standardUnitOfMeasure: standardUnitOfMeasureEnum('standard_unit_of_measure')
      .default(StandardUnitOfMeasure.PIECES)
      .notNull(),
    customUnitId: uuid('custom_unit_id').references(() => units.id),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_products_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    productIdIndex: index('subscription_plan_products_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('subscription_plan_products_variant_id_index').on(
      t.variantId,
    ),
    customUnitIdIndex: index(
      'subscription_plan_products_custom_unit_id_index',
    ).on(t.customUnitId),
    uniqueSubscriptionPlanProduct: index(
      'subscription_plan_products_unique',
    ).on(t.subscriptionPlanId, t.productId, t.variantId),
  }),
);

// Junction table for subscription plan services (many-to-many relationship)
export const subscriptionPlanServices = pgTable(
  'subscription_plan_services',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_services_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    serviceIdIndex: index('subscription_plan_services_service_id_index').on(
      t.serviceId,
    ),
    uniqueSubscriptionPlanService: index(
      'subscription_plan_services_unique',
    ).on(t.subscriptionPlanId, t.serviceId),
  }),
);

// Junction table for subscription plan customers (many-to-many relationship)
export const subscriptionPlanCustomers = pgTable(
  'subscription_plan_customers',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),

    // Notes for this customer assignment
    notes: text('notes'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_customers_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    customerIdIndex: index('subscription_plan_customers_customer_id_index').on(
      t.customerId,
    ),
    uniqueSubscriptionPlanCustomer: index(
      'subscription_plan_customers_unique',
    ).on(t.subscriptionPlanId, t.customerId),
  }),
);

// Junction table for subscription plan customer groups (many-to-many relationship)
export const subscriptionPlanCustomerGroups = pgTable(
  'subscription_plan_customer_groups',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    subscriptionPlanId: uuid('subscription_plan_id')
      .notNull()
      .references(() => subscriptionPlans.id, { onDelete: 'cascade' }),
    customerGroupId: uuid('customer_group_id')
      .notNull()
      .references(() => customerGroups.id, { onDelete: 'cascade' }),

    // Notes for this customer group assignment
    notes: text('notes'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    subscriptionPlanIdIndex: index(
      'subscription_plan_customer_groups_subscription_plan_id_index',
    ).on(t.subscriptionPlanId),
    customerGroupIdIndex: index(
      'subscription_plan_customer_groups_customer_group_id_index',
    ).on(t.customerGroupId),
    uniqueSubscriptionPlanCustomerGroup: index(
      'subscription_plan_customer_groups_unique',
    ).on(t.subscriptionPlanId, t.customerGroupId),
  }),
);

// Subscription Plans Relations
export const subscriptionPlansRelations = relations(
  subscriptionPlans,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [subscriptionPlans.businessId],
      references: [business.id],
      relationName: 'businessSubscriptionPlans',
    }),

    // Account relations
    incomeAccount: one(accounts, {
      fields: [subscriptionPlans.incomeAccountId],
      references: [accounts.id],
      relationName: 'subscriptionPlanIncomeAccount',
    }),
    expenseAccount: one(accounts, {
      fields: [subscriptionPlans.expenseAccountId],
      references: [accounts.id],
      relationName: 'subscriptionPlanExpenseAccount',
    }),

    // Junction table relations
    subscriptionPlanProducts: many(subscriptionPlanProducts, {
      relationName: 'planProducts',
    }),
    subscriptionPlanServices: many(subscriptionPlanServices, {
      relationName: 'planServices',
    }),
    subscriptionPlanCustomers: many(subscriptionPlanCustomers, {
      relationName: 'planCustomers',
    }),
    subscriptionPlanCustomerGroups: many(subscriptionPlanCustomerGroups, {
      relationName: 'planCustomerGroups',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionPlans.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlans',
    }),
    updater: one(users, {
      fields: [subscriptionPlans.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlans',
    }),
    deletedByUser: one(users, {
      fields: [subscriptionPlans.deletedBy],
      references: [users.id],
      relationName: 'deletedSubscriptionPlans',
    }),
  }),
);

// Subscription Plan Products Relations
export const subscriptionPlanProductsRelations = relations(
  subscriptionPlanProducts,
  ({ one }) => ({
    // Plan relation
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanProducts.subscriptionPlanId],
      references: [subscriptionPlans.id],
      relationName: 'planProducts',
    }),

    // Product relation
    product: one(products, {
      fields: [subscriptionPlanProducts.productId],
      references: [products.id],
      relationName: 'productSubscriptionPlans',
    }),

    // Variant relation
    variant: one(productVariants, {
      fields: [subscriptionPlanProducts.variantId],
      references: [productVariants.id],
      relationName: 'variantSubscriptionPlans',
    }),

    // Unit relation
    customUnit: one(units, {
      fields: [subscriptionPlanProducts.customUnitId],
      references: [units.id],
      relationName: 'subscriptionPlanProductUnits',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionPlanProducts.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanProducts',
    }),
    updater: one(users, {
      fields: [subscriptionPlanProducts.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanProducts',
    }),
  }),
);

// Subscription Plan Services Relations
export const subscriptionPlanServicesRelations = relations(
  subscriptionPlanServices,
  ({ one }) => ({
    // Plan relation
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanServices.subscriptionPlanId],
      references: [subscriptionPlans.id],
      relationName: 'planServices',
    }),

    // Service relation
    service: one(services, {
      fields: [subscriptionPlanServices.serviceId],
      references: [services.id],
      relationName: 'serviceSubscriptionPlans',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionPlanServices.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanServices',
    }),
    updater: one(users, {
      fields: [subscriptionPlanServices.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanServices',
    }),
  }),
);

// Subscription Plan Customers Relations
export const subscriptionPlanCustomersRelations = relations(
  subscriptionPlanCustomers,
  ({ one }) => ({
    // Plan relation
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanCustomers.subscriptionPlanId],
      references: [subscriptionPlans.id],
      relationName: 'planCustomers',
    }),

    // Customer relation
    customer: one(customers, {
      fields: [subscriptionPlanCustomers.customerId],
      references: [customers.id],
      relationName: 'customerSubscriptionPlans',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionPlanCustomers.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanCustomers',
    }),
    updater: one(users, {
      fields: [subscriptionPlanCustomers.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanCustomers',
    }),
  }),
);

// Subscription Plan Customer Groups Relations
export const subscriptionPlanCustomerGroupsRelations = relations(
  subscriptionPlanCustomerGroups,
  ({ one }) => ({
    // Plan relation
    subscriptionPlan: one(subscriptionPlans, {
      fields: [subscriptionPlanCustomerGroups.subscriptionPlanId],
      references: [subscriptionPlans.id],
      relationName: 'planCustomerGroups',
    }),

    // Customer group relation
    customerGroup: one(customerGroups, {
      fields: [subscriptionPlanCustomerGroups.customerGroupId],
      references: [customerGroups.id],
      relationName: 'customerGroupSubscriptionPlans',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionPlanCustomerGroups.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPlanCustomerGroups',
    }),
    updater: one(users, {
      fields: [subscriptionPlanCustomerGroups.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPlanCustomerGroups',
    }),
  }),
);
