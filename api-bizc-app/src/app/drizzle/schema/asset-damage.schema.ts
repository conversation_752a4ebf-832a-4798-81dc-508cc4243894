import {
  index,
  pgTable,
  text,
  uuid,
  boolean,
  decimal,
  date,
  time,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { staffMembers } from './staff.schema';
import { business } from './business.schema';
import { assets } from './assets.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export const assetDamage = pgTable(
  'asset_damage',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id),

    // Damage information
    damageType: text('damage_type').notNull(),
    amount: decimal('amount', { precision: 12, scale: 2 }).notNull(),
    incidentDate: date('incident_date').notNull(),
    incidentTime: time('incident_time').notNull(),
    details: text('details'),

    // Fix information
    isFixed: boolean('is_fixed').default(false).notNull(),
    fixedDate: date('fixed_date'),
    fixedBy: uuid('fixed_by').references(() => staffMembers.id),
    checkedBy: uuid('checked_by').references(() => staffMembers.id),
    fixedDetails: text('fixed_details'),
    fixedCost: decimal('fixed_cost', { precision: 12, scale: 2 }),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'asset_damage'),

    // Entity-specific indexes
    assetIdIndex: index('asset_damage_asset_id_index').on(t.assetId),
    damageTypeIndex: index('asset_damage_type_index').on(t.damageType),
    incidentDateIndex: index('asset_damage_incident_date_index').on(
      t.incidentDate,
    ),
    isFixedIndex: index('asset_damage_is_fixed_index').on(t.isFixed),
    fixedByIndex: index('asset_damage_fixed_by_index').on(t.fixedBy),
    checkedByIndex: index('asset_damage_checked_by_index').on(t.checkedBy),

    // Composite indexes for common query patterns
    assetStatusIndex: index('asset_damage_asset_status_index')
      .on(t.assetId, t.isFixed)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('asset_damage_business_date_index')
      .on(t.businessId, t.incidentDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const assetDamageRelations = relations(assetDamage, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [assetDamage.businessId],
    references: [business.id],
  }),

  // Asset relation
  asset: one(assets, {
    fields: [assetDamage.assetId],
    references: [assets.id],
  }),

  // Staff relations
  fixer: one(staffMembers, {
    fields: [assetDamage.fixedBy],
    references: [staffMembers.id],
    relationName: 'fixedAssetDamage',
  }),
  checker: one(staffMembers, {
    fields: [assetDamage.checkedBy],
    references: [staffMembers.id],
    relationName: 'checkedAssetDamage',
  }),

  // Audit relations
  creator: one(users, {
    fields: [assetDamage.createdBy],
    references: [users.id],
    relationName: 'createdAssetDamage',
  }),
  updater: one(users, {
    fields: [assetDamage.updatedBy],
    references: [users.id],
    relationName: 'updatedAssetDamage',
  }),
}));
