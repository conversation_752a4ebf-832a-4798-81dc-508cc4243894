import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import {
  locationAllocationFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { CategoryStatus } from '../../shared/types';

export const categoryStatusEnum = pgEnum('category_status', [
  CategoryStatus.ACTIVE,
  CategoryStatus.INACTIVE,
]);

export const categories = pgTable(
  'categories',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    shortCode: text('short_code'),
    parentId: uuid('parent_id').references(() => categories.id),
    description: text('description'),
    slug: text('slug'),
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),
    color: text('color'),
    image: uuid('image').references(() => media.id),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    status: categoryStatusEnum('status')
      .default(CategoryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'categories'),
    nameIndex: index('categories_name_index').on(t.name),
    parentIdIndex: index('categories_parent_id_index').on(t.parentId),
    slugIndex: index('categories_slug_index').on(t.slug),
    imageIndex: index('categories_image_index').on(t.image),
    ogImageIndex: index('categories_og_image_index').on(t.ogImage),
    positionIndex: index('categories_position_index').on(t.position),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('categories_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentPositionIndex: index(
      'categories_business_parent_position_index',
    )
      .on(t.businessId, t.parentId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'categories_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentStatusPositionIndex: index(
      'categories_business_parent_status_position_index',
    )
      .on(t.businessId, t.parentId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('categories_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('categories_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessShortCodeIndex: index('categories_business_short_code_index')
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('categories_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('categories_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessShortCode: uniqueIndex(
      'categories_business_short_code_unique',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for category locations (many-to-many relationship)
export const categoryLocations = pgTable(
  'category_locations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    categoryId: uuid('category_id')
      .notNull()
      .references(() => categories.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'category_locations'),
    categoryIdIndex: index('category_locations_category_id_index').on(
      t.categoryId,
    ),
    locationIdIndex: index('category_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueCategoryLocation: uniqueIndex('category_locations_unique').on(
      t.categoryId,
      t.locationId,
    ),
  }),
);

// Relations
export const categoriesRelations = relations(categories, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [categories.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [categories.createdBy],
    references: [users.id],
    relationName: 'createdCategories',
  }),
  updater: one(users, {
    fields: [categories.updatedBy],
    references: [users.id],
    relationName: 'updatedCategories',
  }),

  // Media relations
  imageMedia: one(media, {
    fields: [categories.image],
    references: [media.id],
    relationName: 'categoryImage',
  }),
  ogImageMedia: one(media, {
    fields: [categories.ogImage],
    references: [media.id],
    relationName: 'categoryOgImage',
  }),

  // Self-referencing relations for hierarchical structure
  parentCategory: one(categories, {
    fields: [categories.parentId],
    references: [categories.id],
    relationName: 'parentCategory',
  }),
  childCategories: many(categories, {
    relationName: 'parentCategory',
  }),

  // Location relations through junction table
  categoryLocations: many(categoryLocations),
}));

export const categoryLocationsRelations = relations(
  categoryLocations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [categoryLocations.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [categoryLocations.createdBy],
      references: [users.id],
      relationName: 'createdCategoryLocations',
    }),
    updater: one(users, {
      fields: [categoryLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedCategoryLocations',
    }),

    // Category and location relations
    category: one(categories, {
      fields: [categoryLocations.categoryId],
      references: [categories.id],
    }),
    location: one(locations, {
      fields: [categoryLocations.locationId],
      references: [locations.id],
    }),
  }),
);
