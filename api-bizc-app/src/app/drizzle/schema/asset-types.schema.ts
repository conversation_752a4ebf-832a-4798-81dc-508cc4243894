import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { users } from './users.schema';
import { CategoryStatus, AssetTypeCategory } from '../../shared/types';

export const assetTypeStatusEnum = pgEnum('asset_type_status', [
  CategoryStatus.ACTIVE,
  CategoryStatus.INACTIVE,
]);

export const assetTypeReferenceTypeEnum = pgEnum('asset_type_reference_type', [
  'rental-item-category',
  'vehicle-category',
]);

export const assetTypeCategoryEnum = pgEnum('asset_type_category', [
  AssetTypeCategory.PHYSICAL,
  AssetTypeCategory.DIGITAL,
]);

export const assetTypes = pgTable(
  'asset_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    description: text('description'),
    parentId: uuid('parent_id').references(() => assetTypes.id),

    // Category field - new enum field
    category: assetTypeCategoryEnum('category').notNull(),

    // Reference tracking fields for automatic creation
    referenceId: uuid('reference_id'), // ID of the source category (rental-item-category or vehicle-category)
    referenceType: assetTypeReferenceTypeEnum('reference_type'), // Type of the source module

    // Status
    status: assetTypeStatusEnum('status')
      .default(CategoryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'asset_types'),

    // Entity-specific indexes
    nameIndex: index('asset_types_name_index').on(t.name),
    parentIdIndex: index('asset_types_parent_id_index').on(t.parentId),
    statusIndex: index('asset_types_status_index').on(t.status),
    categoryIndex: index('asset_types_category_index').on(t.category),
    referenceIdIndex: index('asset_types_reference_id_index').on(t.referenceId),
    referenceTypeIndex: index('asset_types_reference_type_index').on(
      t.referenceType,
    ),

    // Composite indexes for performance
    businessNameIndex: index('asset_types_business_name_index').on(
      t.businessId,
      t.name,
    ),
    businessStatusIndex: index('asset_types_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessCategoryIndex: index('asset_types_business_category_index').on(
      t.businessId,
      t.category,
    ),
    referenceIndex: index('asset_types_reference_index').on(
      t.referenceId,
      t.referenceType,
    ),

    // Unique constraints
    uniqueBusinessName: uniqueIndex('asset_types_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to ensure one asset-type per source category
    uniqueReference: uniqueIndex('asset_types_reference_unique')
      .on(t.referenceId, t.referenceType)
      .where(
        sql`${t.referenceId} IS NOT NULL AND ${t.referenceType} IS NOT NULL AND ${t.isDeleted} = false`,
      ),
  }),
);

// Relations
export const assetTypesRelations = relations(assetTypes, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [assetTypes.businessId],
    references: [business.id],
  }),

  // Parent-child relations (hierarchical)
  parent: one(assetTypes, {
    fields: [assetTypes.parentId],
    references: [assetTypes.id],
    relationName: 'parentAssetType',
  }),
  children: many(assetTypes, {
    relationName: 'parentAssetType',
  }),

  // Audit relations
  creator: one(users, {
    fields: [assetTypes.createdBy],
    references: [users.id],
    relationName: 'createdAssetTypes',
  }),
  updater: one(users, {
    fields: [assetTypes.updatedBy],
    references: [users.id],
    relationName: 'updatedAssetTypes',
  }),
}));
