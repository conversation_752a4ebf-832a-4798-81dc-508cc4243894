import {
  bigint,
  boolean,
  index,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { createBaseEntityFields } from './common-fields.schema';

import { users } from './users.schema';
export const notifications = pgTable(
  'notifications',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    type: text('type').notNull(),
    notifiableType: text('notifiable_type').notNull(),
    notifiableId: bigint('notifiable_id', { mode: 'number' }).notNull(),
    data: text('data').notNull(),
    readAt: timestamp('read_at'),
  },
  (t) => ({
    idIndex: index('notifications_id_index').on(t.id),
    businessIdIndex: index('notifications_business_id_index').on(t.businessId),
    typeIndex: index('notifications_type_index').on(t.type),
    notifiableIndex: index('notifications_notifiable_index').on(
      t.notifiableType,
      t.notifiableId,
    ),
    readAtIndex: index('notifications_read_at_index').on(t.readAt),
    createdAtIndex: index('notifications_created_at_index').on(t.createdAt),
  }),
);

// Relations
export const notificationsRelations = relations(notifications, ({ one }) => ({
  business: one(business, {
    fields: [notifications.businessId],
    references: [business.id],
  }),
  createdByUser: one(users, {
    fields: [notifications.createdBy],
    references: [users.id],
  }),
  updatedByUser: one(users, {
    fields: [notifications.updatedBy],
    references: [users.id],
  }),
}));
