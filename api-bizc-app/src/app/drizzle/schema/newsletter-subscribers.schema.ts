import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  personalNameFields,
  createBaseEntityFields,
} from './common-fields.schema';
import { users } from './users.schema';
import { SubscriptionSource } from '../../shared/types';

/**
 * Newsletter Subscriber Status TypeScript Enum
 * Used for type safety throughout the application
 */
export enum NewsletterSubscriberStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  UNSUBSCRIBED = 'UNSUBSCRIBED',
  BOUNCED = 'BOUNCED',
  BLOCKED = 'BLOCKED',
  PENDING = 'PENDING',
  INVALID = 'INVALID',
  DELETED = 'DELETED',
}

// Unified status enum combining both email and phone statuses
export const newsletterSubscriberStatusEnum = pgEnum(
  'newsletter_subscriber_status',
  [
    NewsletterSubscriberStatus.ACTIVE,
    NewsletterSubscriberStatus.INACTIVE,
    NewsletterSubscriberStatus.UNSUBSCRIBED,
    NewsletterSubscriberStatus.BOUNCED,
    NewsletterSubscriberStatus.BLOCKED,
    NewsletterSubscriberStatus.PENDING,
    NewsletterSubscriberStatus.INVALID,
    NewsletterSubscriberStatus.DELETED,
  ],
);

export const subscriptionSourceEnum = pgEnum('subscription_source', [
  SubscriptionSource.WEBSITE,
  SubscriptionSource.SOCIAL_MEDIA,
  SubscriptionSource.QR_CODE,
  SubscriptionSource.CHECKOUT,
  SubscriptionSource.MANUAL,
  SubscriptionSource.OTHER,
]);

// Combined Newsletter Subscribers Table
export const newsletterSubscribers = pgTable(
  'newsletter_subscribers',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Email fields
    email: text('email'),
    emailVerified: boolean('email_verified').default(false),
    subscriptionDate: timestamp('subscription_date'),
    unsubscriptionDate: timestamp('unsubscription_date'),

    // Phone fields
    phoneNumber: text('phone_number'),
    phoneVerified: boolean('phone_verified').default(false),
    languagePreference: text('language_preference').default('en'),

    // Unified status field
    status: newsletterSubscriberStatusEnum('status')
      .default(NewsletterSubscriberStatus.PENDING)
      .notNull(),

    // Personal Information (using reusable fields)
    ...personalNameFields,
    source: subscriptionSourceEnum('source'),
  },
  (t) => ({
    idIndex: index('newsletter_subscribers_id_index').on(t.id),
    businessIdIndex: index('newsletter_subscribers_business_id_index').on(
      t.businessId,
    ),
    emailIndex: index('newsletter_subscribers_email_index').on(t.email),
    phoneNumberIndex: index('newsletter_subscribers_phone_number_index').on(
      t.phoneNumber,
    ),
    statusIndex: index('newsletter_subscribers_status_index').on(t.status),
    sourceIndex: index('newsletter_subscribers_source_index').on(t.source),
    subscriptionDateIndex: index(
      'newsletter_subscribers_subscription_date_index',
    ).on(t.subscriptionDate),
    unsubscriptionDateIndex: index(
      'newsletter_subscribers_unsubscription_date_index',
    ).on(t.unsubscriptionDate),

    // Optimized composite indexes with soft deletion support
    businessEmailIndex: index('newsletter_subscribers_business_email_index')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
    businessPhoneIndex: index('newsletter_subscribers_business_phone_index')
      .on(t.businessId, t.phoneNumber)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('newsletter_subscribers_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessSourceIndex: index('newsletter_subscribers_business_source_index')
      .on(t.businessId, t.source)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessEmail: uniqueIndex(
      'newsletter_subscribers_business_email_unique',
    )
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessPhone: uniqueIndex(
      'newsletter_subscribers_business_phone_unique',
    )
      .on(t.businessId, t.phoneNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const newsletterSubscribersRelations = relations(
  newsletterSubscribers,
  ({ one }) => ({
    business: one(business, {
      fields: [newsletterSubscribers.businessId],
      references: [business.id],
    }),
    createdByUser: one(users, {
      fields: [newsletterSubscribers.createdBy],
      references: [users.id],
    }),
    updatedByUser: one(users, {
      fields: [newsletterSubscribers.updatedBy],
      references: [users.id],
    }),
  }),
);
