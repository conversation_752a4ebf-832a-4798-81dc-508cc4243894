import {
  boolean,
  date,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { staffMembers } from './staff.schema';
import { media } from './media.schema';
import { recurringActivities } from './recurring-activities.schema';

// Task Status Enum
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
  BLOCKED = 'blocked',
}

export const taskStatusEnum = pgEnum('task_status', [
  TaskStatus.PENDING,
  TaskStatus.IN_PROGRESS,
  TaskStatus.COMPLETED,
  TaskStatus.CANCELLED,
  TaskStatus.ON_HOLD,
  TaskStatus.BLOCKED,
]);

// Task Priority Enum
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export const taskPriorityEnum = pgEnum('task_priority', [
  TaskPriority.LOW,
  TaskPriority.MEDIUM,
  TaskPriority.HIGH,
  TaskPriority.URGENT,
]);

// Task Reference Type Enum
export enum TaskReferenceType {
  SUPPLIER = 'supplier',
  CUSTOMER = 'customer',
  PROJECT = 'project',
  TRANSACTION = 'transaction',
  SERVICE_ORDER = 'service_order',
  WORK_ORDER = 'work_order',
  EVENT_SPACE_RESERVATION = 'event_space_reservation',
}

export const taskReferenceTypeEnum = pgEnum('task_reference_type', [
  TaskReferenceType.SUPPLIER,
  TaskReferenceType.CUSTOMER,
  TaskReferenceType.PROJECT,
  TaskReferenceType.TRANSACTION,
  TaskReferenceType.SERVICE_ORDER,
  TaskReferenceType.WORK_ORDER,
  TaskReferenceType.EVENT_SPACE_RESERVATION,
]);

export const tasks = pgTable(
  'tasks',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Core task fields
    title: text('title').notNull(),
    description: text('description'),
    dueDate: date('due_date'),
    priority: taskPriorityEnum('priority')
      .default(TaskPriority.MEDIUM)
      .notNull(),
    status: taskStatusEnum('status').default(TaskStatus.PENDING).notNull(),

    // Assignment functionality
    assignedTo: uuid('assigned_to').references(() => staffMembers.id),

    // Entity reference (optional - tasks can be related to any entity)
    referenceId: uuid('reference_id'),
    referenceType: taskReferenceTypeEnum('reference_type'),

    // Recurring functionality (references the reusable recurring activities schema)
    recurringActivityId: uuid('recurring_activity_id').references(
      () => recurringActivities.id,
    ),
    parentTaskId: uuid('parent_task_id').references(() => tasks.id), // Reference to original recurring task

    // Additional deletion fields
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'tasks'),
    assignedToIndex: index('tasks_assigned_to_index').on(t.assignedTo),
    statusIndex: index('tasks_status_index').on(t.status),
    priorityIndex: index('tasks_priority_index').on(t.priority),
    dueDateIndex: index('tasks_due_date_index').on(t.dueDate),

    // Entity reference indexes
    referenceIdIndex: index('tasks_reference_id_index').on(t.referenceId),
    referenceTypeIndex: index('tasks_reference_type_index').on(t.referenceType),

    // Recurring task indexes
    recurringActivityIdIndex: index('tasks_recurring_activity_id_index').on(
      t.recurringActivityId,
    ),
    parentTaskIdIndex: index('tasks_parent_task_id_index').on(t.parentTaskId),

    // Optimized composite indexes with soft deletion support
    businessStatusIndex: index('tasks_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessPriorityIndex: index('tasks_business_priority_index')
      .on(t.businessId, t.priority)
      .where(sql`${t.isDeleted} = false`),
    businessAssignedIndex: index('tasks_business_assigned_index')
      .on(t.businessId, t.assignedTo)
      .where(sql`${t.isDeleted} = false`),
    businessDueDateIndex: index('tasks_business_due_date_index')
      .on(t.businessId, t.dueDate)
      .where(sql`${t.isDeleted} = false`),
    businessRecurringActivityIndex: index(
      'tasks_business_recurring_activity_index',
    )
      .on(t.businessId, t.recurringActivityId)
      .where(sql`${t.isDeleted} = false`),

    // Entity reference composite indexes with soft deletion support
    businessReferenceIndex: index('tasks_business_reference_index')
      .on(t.businessId, t.referenceId, t.referenceType)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for task attachments (many-to-many relationship)
export const taskAttachments = pgTable(
  'task_attachments',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    taskId: uuid('task_id')
      .notNull()
      .references(() => tasks.id, { onDelete: 'cascade' }),
    attachmentId: uuid('attachment_id')
      .notNull()
      .references(() => media.id, { onDelete: 'cascade' }),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    taskIdIndex: index('task_attachments_task_id_index').on(t.taskId),
    attachmentIdIndex: index('task_attachments_attachment_id_index').on(
      t.attachmentId,
    ),
    uniqueTaskAttachment: uniqueIndex('task_attachments_unique').on(
      t.taskId,
      t.attachmentId,
    ),
  }),
);

// Relations
export const tasksRelations = relations(tasks, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [tasks.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [tasks.createdBy],
    references: [users.id],
    relationName: 'createdTasks',
  }),
  updater: one(users, {
    fields: [tasks.updatedBy],
    references: [users.id],
    relationName: 'updatedTasks',
  }),
  deletedByUser: one(users, {
    fields: [tasks.deletedBy],
    references: [users.id],
    relationName: 'deletedTasks',
  }),

  // Assignment relation
  assignedStaff: one(staffMembers, {
    fields: [tasks.assignedTo],
    references: [staffMembers.id],
  }),

  // Recurring activity relation
  recurringActivity: one(recurringActivities, {
    fields: [tasks.recurringActivityId],
    references: [recurringActivities.id],
  }),

  // Parent task relation (self-referencing)
  parentTask: one(tasks, {
    fields: [tasks.parentTaskId],
    references: [tasks.id],
    relationName: 'parentTask',
  }),

  // One-to-many relations
  childTasks: many(tasks, {
    relationName: 'parentTask',
  }),
  taskAttachments: many(taskAttachments),
}));

export const taskAttachmentsRelations = relations(
  taskAttachments,
  ({ one }) => ({
    // Task and attachment relations
    task: one(tasks, {
      fields: [taskAttachments.taskId],
      references: [tasks.id],
    }),
    attachment: one(media, {
      fields: [taskAttachments.attachmentId],
      references: [media.id],
    }),
  }),
);
