import { pgEnum } from 'drizzle-orm/pg-core';

// Manufacturing-specific operation status enum
export enum ManufacturingOperationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
}

export const manufacturingOperationStatusEnum = pgEnum(
  'manufacturing_operation_status',
  [
    ManufacturingOperationStatus.PENDING,
    ManufacturingOperationStatus.IN_PROGRESS,
    ManufacturingOperationStatus.COMPLETED,
    ManufacturingOperationStatus.SKIPPED,
  ],
);
