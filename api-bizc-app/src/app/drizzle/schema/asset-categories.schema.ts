import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { CategoryStatus } from '../../shared/types';

export const assetCategoryStatusEnum = pgEnum('asset_category_status', [
  CategoryStatus.ACTIVE,
  CategoryStatus.INACTIVE,
]);

export const assetCategoryReferenceTypeEnum = pgEnum(
  'asset_category_reference_type',
  ['rental-item-category', 'vehicle-category'],
);

export const assetCategories = pgTable(
  'asset_categories',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    description: text('description'),
    parentId: uuid('parent_id').references(() => assetCategories.id),

    // Reference tracking fields for automatic creation
    referenceId: uuid('reference_id'), // ID of the source category (rental-item-category or vehicle-category)
    referenceType: assetCategoryReferenceTypeEnum('reference_type'), // Type of the source module

    // Status
    status: assetCategoryStatusEnum('status')
      .default(CategoryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'asset_categories'),

    // Entity-specific indexes
    nameIndex: index('asset_categories_name_index').on(t.name),
    parentIdIndex: index('asset_categories_parent_id_index').on(t.parentId),
    statusIndex: index('asset_categories_status_index').on(t.status),
    referenceIdIndex: index('asset_categories_reference_id_index').on(
      t.referenceId,
    ),
    referenceTypeIndex: index('asset_categories_reference_type_index').on(
      t.referenceType,
    ),

    // Composite indexes for performance
    businessNameIndex: index('asset_categories_business_name_index').on(
      t.businessId,
      t.name,
    ),
    businessStatusIndex: index('asset_categories_business_status_index').on(
      t.businessId,
      t.status,
    ),
    referenceIndex: index('asset_categories_reference_index').on(
      t.referenceId,
      t.referenceType,
    ),

    // Unique constraints
    uniqueBusinessName: uniqueIndex('asset_categories_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to ensure one asset-category per source category
    uniqueReference: uniqueIndex('asset_categories_reference_unique')
      .on(t.referenceId, t.referenceType)
      .where(
        sql`${t.referenceId} IS NOT NULL AND ${t.referenceType} IS NOT NULL AND ${t.isDeleted} = false`,
      ),
  }),
);

// Relations
export const assetCategoriesRelations = relations(
  assetCategories,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [assetCategories.businessId],
      references: [business.id],
    }),

    // Parent-child relations (hierarchical)
    parent: one(assetCategories, {
      fields: [assetCategories.parentId],
      references: [assetCategories.id],
      relationName: 'parentCategory',
    }),
    children: many(assetCategories, {
      relationName: 'parentCategory',
    }),

    // Audit relations
    creator: one(users, {
      fields: [assetCategories.createdBy],
      references: [users.id],
      relationName: 'createdAssetCategories',
    }),
    updater: one(users, {
      fields: [assetCategories.updatedBy],
      references: [users.id],
      relationName: 'updatedAssetCategories',
    }),
  }),
);
