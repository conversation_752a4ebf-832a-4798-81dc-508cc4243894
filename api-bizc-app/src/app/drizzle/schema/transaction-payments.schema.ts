import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { transactions } from './transactions.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
// Payment Method Enum - Reusing from transactions schema but defining locally for clarity
export const transactionPaymentMethodEnum = pgEnum(
  'transaction_payment_method',
  [
    'cash',
    'card',
    'bank_transfer',
    'cheque',
    'digital_wallet',
    'cryptocurrency',
    'mobile_payment',
    'wire_transfer',
    'ach_transfer',
    'direct_debit',
    'credit_note',
    'gift_card',
    'store_credit',
    'other',
  ],
);

// Reference Type Enum for transaction payments
export const transactionPaymentReferenceTypeEnum = pgEnum(
  'transaction_payment_reference_type',
  [
    'invoice',
    'bill',
    'expense',
    'purchase_order',
    'sales_order',
    'credit_note',
    'debit_note',
    'refund',
    'advance_payment',
    'deposit',
    'other',
  ],
);

export const transactionPayments = pgTable(
  'transaction_payments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Transaction reference
    transactionId: uuid('transaction_id')
      .notNull()
      .references(() => transactions.id, { onDelete: 'cascade' }),

    // Payment details
    isReturn: boolean('is_return').default(false).notNull(),
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    method: transactionPaymentMethodEnum('method').notNull(),
    transactionNo: text('transaction_no').notNull(),
    paidOn: timestamp('paid_on').notNull(),
    isAdvance: boolean('is_advance').default(false).notNull(),

    // Reference information
    referenceType: transactionPaymentReferenceTypeEnum('reference_type'),
    referenceId: uuid('reference_id'), // Nullable reference to related entity
    referenceNumber: text('reference_number'),

    // Additional payment information
    description: text('description'),
    notes: text('notes'),
    paymentReference: text('payment_reference'), // External payment reference
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'transaction_payments'),
    transactionIdIndex: index('transaction_payments_transaction_id_index').on(
      t.transactionId,
    ),

    // Payment details indexes
    methodIndex: index('transaction_payments_method_index').on(t.method),
    paidOnIndex: index('transaction_payments_paid_on_index').on(t.paidOn),
    transactionNoIndex: index('transaction_payments_transaction_no_index').on(
      t.transactionNo,
    ),
    amountIndex: index('transaction_payments_amount_index').on(t.amount),

    // Reference indexes
    referenceTypeIndex: index('transaction_payments_reference_type_index').on(
      t.referenceType,
    ),
    referenceIdIndex: index('transaction_payments_reference_id_index').on(
      t.referenceId,
    ),

    // Flags indexes
    isReturnIndex: index('transaction_payments_is_return_index').on(t.isReturn),
    isAdvanceIndex: index('transaction_payments_is_advance_index').on(
      t.isAdvance,
    ),

    // Composite indexes for common query patterns
    businessTransactionIndex: index(
      'transaction_payments_business_transaction_index',
    )
      .on(t.businessId, t.transactionId, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessPaidOnIndex: index('transaction_payments_business_paid_on_index')
      .on(t.businessId, t.paidOn, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessMethodIndex: index('transaction_payments_business_method_index')
      .on(t.businessId, t.method, t.paidOn, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessTransactionNo: uniqueIndex(
      'transaction_payments_business_transaction_no_unique',
    )
      .on(t.businessId, t.transactionNo)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const transactionPaymentsRelations = relations(
  transactionPayments,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [transactionPayments.businessId],
      references: [business.id],
    }),
    transaction: one(transactions, {
      fields: [transactionPayments.transactionId],
      references: [transactions.id],
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [transactionPayments.createdBy],
      references: [users.id],
      relationName: 'createdTransactionPayments',
    }),
    updatedByUser: one(users, {
      fields: [transactionPayments.updatedBy],
      references: [users.id],
      relationName: 'updatedTransactionPayments',
    }),
  }),
);
