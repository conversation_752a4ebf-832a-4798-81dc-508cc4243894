import {
  boolean,
  jsonb,
  pgTable,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
/**
 * Working Hours Schema
 *
 * Stores business working hours configuration with weekly schedule in JSONB format.
 * Each business can have one working hours configuration.
 *
 * Weekly schedule format:
 * {
 *   "monday": {
 *     "isWorkingDay": true,
 *     "workingHours": {
 *       "start": "09:00",
 *       "end": "17:00"
 *     },
 *     "breaks": [
 *       {
 *         "name": "Lunch Break",
 *         "start": "12:00",
 *         "end": "13:00",
 *         "isPaid": false
 *       }
 *     ],
 *     "totalWorkingHours": 7.0
 *   },
 *   // ... other days
 * }
 */

// TypeScript interfaces for the JSONB structure
export interface WorkingHoursBreak {
  name: string;
  start: string; // HH:MM format
  end: string; // HH:MM format
  isPaid: boolean;
}

export interface WorkingHoursDay {
  isWorkingDay: boolean;
  workingHours?: {
    start: string; // HH:MM format
    end: string; // HH:MM format
  };
  breaks: WorkingHoursBreak[];
  totalWorkingHours: number;
}

export interface WeeklySchedule {
  monday: WorkingHoursDay;
  tuesday: WorkingHoursDay;
  wednesday: WorkingHoursDay;
  thursday: WorkingHoursDay;
  friday: WorkingHoursDay;
  saturday: WorkingHoursDay;
  sunday: WorkingHoursDay;
}

export const workingHours = pgTable(
  'working_hours',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Weekly schedule stored as JSONB
    // Format: WeeklySchedule interface defined above
    weeklySchedule: jsonb('weekly_schedule').$type<WeeklySchedule>().notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'working_hours'),

    // Ensure one working hours record per business
    uniqueBusinessWorkingHours: uniqueIndex('working_hours_business_unique').on(
      t.businessId,
    ),
  }),
);

// Default working hours template
export const DEFAULT_WORKING_HOURS: WeeklySchedule = {
  monday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  tuesday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  wednesday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  thursday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  friday: {
    isWorkingDay: true,
    workingHours: {
      start: '09:00',
      end: '17:00',
    },
    breaks: [
      {
        name: 'Lunch Break',
        start: '12:00',
        end: '13:00',
        isPaid: false,
      },
    ],
    totalWorkingHours: 7.0,
  },
  saturday: {
    isWorkingDay: false,
    breaks: [],
    totalWorkingHours: 0,
  },
  sunday: {
    isWorkingDay: false,
    breaks: [],
    totalWorkingHours: 0,
  },
};

// Relations definitions

// Working Hours Relations
export const workingHoursRelations = relations(workingHours, ({ one }) => ({
  // One-to-one and many-to-one relations
  business: one(business, {
    fields: [workingHours.businessId],
    references: [business.id],
  }),
  creator: one(users, {
    fields: [workingHours.createdBy],
    references: [users.id],
    relationName: 'createdWorkingHours',
  }),
  updater: one(users, {
    fields: [workingHours.updatedBy],
    references: [users.id],
    relationName: 'updatedWorkingHours',
  }),
}));
