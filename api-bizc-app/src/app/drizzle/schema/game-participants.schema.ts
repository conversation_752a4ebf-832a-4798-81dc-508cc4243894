import {
  boolean,
  index,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { games } from './games.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export const gameParticipants = pgTable(
  'game_participants',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Game reference
    gameId: uuid('game_id')
      .notNull()
      .references(() => games.id, { onDelete: 'cascade' }),

    // Participant information
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    phone: text('phone').notNull(),
    promocode: text('promocode'), // Optional promo code

    // Winner information
    isWinner: boolean('is_winner').default(false).notNull(),
    winnerAt: timestamp('winner_at'), // Nullable timestamp for when they won
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'game_participants'),
    gameIdIndex: index('game_participants_game_id_index').on(t.gameId),
    phoneIndex: index('game_participants_phone_index').on(t.phone),
    isWinnerIndex: index('game_participants_is_winner_index').on(t.isWinner),
    createdAtIndex: index('game_participants_created_at_index').on(t.createdAt),
  }),
);

// Game Participants Relations
export const gameParticipantsRelations = relations(
  gameParticipants,
  ({ one }) => ({
    business: one(business, {
      fields: [gameParticipants.businessId],
      references: [business.id],
    }),
    game: one(games, {
      fields: [gameParticipants.gameId],
      references: [games.id],
    }),
    createdBy: one(users, {
      fields: [gameParticipants.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [gameParticipants.updatedBy],
      references: [users.id],
    }),
  }),
);

// Type exports for use in other files
export type GameParticipant = typeof gameParticipants.$inferSelect;
export type NewGameParticipant = typeof gameParticipants.$inferInsert;
