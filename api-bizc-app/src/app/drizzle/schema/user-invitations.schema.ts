import {
  boolean,
  index,
  pgEnum,
  pgTable,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { users } from './users.schema';
import { DocumentStatus } from '../../shared/types';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const userInvitationStatusEnum = pgEnum('user_invitation_status', [
  'PENDING',
  'ACCEPTED',
  'REJECTED',
]);

export const userInvitationDocumentStatusEnum = pgEnum(
  'user_invitation_document_status',
  [DocumentStatus.ACTIVE, DocumentStatus.ARCHIVED],
);

export const userInvitations = pgTable(
  'user_invitations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id),
    status: userInvitationStatusEnum('status').default('PENDING').notNull(),
    viewedAt: timestamp('viewed_at'),
    acceptedAt: timestamp('accepted_at'),
    rejectedAt: timestamp('rejected_at'),
    documentStatus: userInvitationDocumentStatusEnum('document_status')
      .default(DocumentStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'user_invitations'),

    // Entity-specific indexes
    userIdIndex: index('user_invitations_user_id_index').on(t.userId),
    staffMemberIdIndex: index('user_invitations_staff_member_id_index').on(
      t.staffMemberId,
    ),
    statusIndex: index('user_invitations_status_index').on(t.status),
    documentStatusIndex: index('user_invitations_document_status_index').on(
      t.documentStatus,
    ),
    viewedAtIndex: index('user_invitations_viewed_at_index').on(t.viewedAt),
    acceptedAtIndex: index('user_invitations_accepted_at_index').on(
      t.acceptedAt,
    ),
    rejectedAtIndex: index('user_invitations_rejected_at_index').on(
      t.rejectedAt,
    ),

    // Composite indexes for performance
    businessUserIndex: index('user_invitations_business_user_index').on(
      t.businessId,
      t.userId,
    ),
    businessStaffIndex: index('user_invitations_business_staff_index').on(
      t.businessId,
      t.staffMemberId,
    ),
    userStatusIndex: index('user_invitations_user_status_index').on(
      t.userId,
      t.status,
    ),
  }),
);

// Relations definitions

// User Invitations Relations
export const userInvitationsRelations = relations(
  userInvitations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [userInvitations.businessId],
      references: [business.id],
    }),

    // User relation
    user: one(users, {
      fields: [userInvitations.userId],
      references: [users.id],
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [userInvitations.staffMemberId],
      references: [staffMembers.id],
    }),

    // Audit field relations
    creator: one(users, {
      fields: [userInvitations.createdBy],
      references: [users.id],
      relationName: 'createdUserInvitations',
    }),
    updater: one(users, {
      fields: [userInvitations.updatedBy],
      references: [users.id],
      relationName: 'updatedUserInvitations',
    }),
  }),
);
