import {
  boolean,
  index,
  pgEnum,
  pgTable,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { businessRoles } from './business-roles.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

export const userBusinessRoleStatusEnum = pgEnum('user_business_role_status', [
  'active',
  'inactive',
  'deleted',
]);

export const userBusinessRoles = pgTable(
  'user_business_roles',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    roleId: uuid('role_id')
      .notNull()
      .references(() => businessRoles.id, { onDelete: 'cascade' }),
    assignedBy: uuid('assigned_by')
      .notNull()
      .references(() => users.id),
    status: userBusinessRoleStatusEnum('status').default('active').notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'user_business_roles'),

    // Entity-specific indexes
    userIdIndex: index('user_business_roles_user_id_index').on(t.userId),
    roleIdIndex: index('user_business_roles_role_id_index').on(t.roleId),
    assignedByIndex: index('user_business_roles_assigned_by_index').on(
      t.assignedBy,
    ),
    statusIndex: index('user_business_roles_status_index').on(t.status),

    // Composite indexes for performance
    businessUserIndex: index('user_business_roles_business_user_index').on(
      t.businessId,
      t.userId,
    ),
    businessRoleIndex: index('user_business_roles_business_role_index').on(
      t.businessId,
      t.roleId,
    ),
    userRoleIndex: index('user_business_roles_user_role_index').on(
      t.userId,
      t.roleId,
    ),
  }),
);

// Relations
export const userBusinessRolesRelations = relations(
  userBusinessRoles,
  ({ one }) => ({
    // User relation
    user: one(users, {
      fields: [userBusinessRoles.userId],
      references: [users.id],
      relationName: 'userBusinessRoles',
    }),

    // Business relation
    business: one(business, {
      fields: [userBusinessRoles.businessId],
      references: [business.id],
      relationName: 'businessUserRoles',
    }),

    // Role relation
    role: one(businessRoles, {
      fields: [userBusinessRoles.roleId],
      references: [businessRoles.id],
      relationName: 'roleAssignments',
    }),

    // Assignor relation
    assignedByUser: one(users, {
      fields: [userBusinessRoles.assignedBy],
      references: [users.id],
      relationName: 'assignedRoles',
    }),

    // Audit relations
    creator: one(users, {
      fields: [userBusinessRoles.createdBy],
      references: [users.id],
      relationName: 'createdUserBusinessRoles',
    }),
    updater: one(users, {
      fields: [userBusinessRoles.updatedBy],
      references: [users.id],
      relationName: 'updatedUserBusinessRoles',
    }),
  }),
);
