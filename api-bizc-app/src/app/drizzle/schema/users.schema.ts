import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import {
  UserRole,
  UserStatus,
  TwoFactorMethod,
  AccountType,
} from '../../shared/types';

// Enums
export const userAccountTypeEnum = pgEnum('account_type', [
  AccountType.EMAIL,
  AccountType.GOOGLE,
  AccountType.FACEBOOK,
]);

export const userRoleEnum = pgEnum('user_role', [
  UserRole.ADMIN,
  UserRole.USER,
  UserRole.SHOP,
]);

export const twoFactorMethodEnum = pgEnum('two_factor_method', [
  TwoFactorMethod.NONE,
  TwoFactorMethod.EMAIL,
  TwoFactorMethod.AUTHENTICATOR,
]);

export const userStatusEnum = pgEnum('user_status', [
  UserStatus.ACTIVE,
  UserStatus.INACTIVE,
  UserStatus.SUSPENDED,
]);

// Main users table (normalized)
export const users = pgTable(
  'users',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    email: text('email').notNull().unique(),
    firstName: text('first_name'),
    lastName: text('last_name'),
    title: text('title'),
    emailVerified: timestamp('email_verified'),
    role: userRoleEnum('role').default(UserRole.USER).notNull(),
    status: userStatusEnum('status').default(UserStatus.ACTIVE).notNull(),
    activeBusinessId: uuid('active_business_id'),
    avatar: text('avatar'),
    hasPendingInvitations: boolean('has_pending_invitations')
      .default(false)
      .notNull(),
    createdBy: uuid('created_by').references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    idIndex: index('users_id_index').on(t.id),
    emailIndex: index('users_email_index').on(t.email),
    activeBusinessIdIndex: index('users_active_business_id_index').on(
      t.activeBusinessId,
    ),
  }),
);

// Separate table for user authentication accounts
export const userAccounts = pgTable(
  'user_accounts',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    accountType: userAccountTypeEnum('account_type').notNull(),
    password: text('password'), // Only for email accounts
    providerId: text('provider_id'), // For OAuth accounts
    email: text('email').notNull(), // Account-specific email
    isPrimary: boolean('is_primary').default(false).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    userIdIndex: index('user_accounts_user_id_index').on(t.userId),
    accountTypeIndex: index('user_accounts_type_index').on(t.accountType),
    providerIndex: index('user_accounts_provider_index').on(
      t.accountType,
      t.providerId,
    ),
  }),
);

// Separate table for two-factor authentication
export const userTwoFactor = pgTable(
  'user_two_factor',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull()
      .unique(),
    method: twoFactorMethodEnum('method')
      .default(TwoFactorMethod.NONE)
      .notNull(),
    secret: text('secret'),
    enabled: boolean('enabled').default(false).notNull(),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    userIdIndex: index('user_two_factor_user_id_index').on(t.userId),
  }),
);

// Separate table for refresh tokens
export const userRefreshTokens = pgTable(
  'user_refresh_tokens',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    token: text('token').notNull().unique(),
    deviceId: text('device_id'), // Optional: track which device
    userAgent: text('user_agent'), // Optional: track user agent
    ipAddress: text('ip_address'), // Optional: track IP for security
    expiresAt: timestamp('expires_at').notNull(),
    lastUsedAt: timestamp('last_used_at'),
    revokedAt: timestamp('revoked_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    userIdIndex: index('user_refresh_tokens_user_id_index').on(t.userId),
    tokenIndex: index('user_refresh_tokens_token_index').on(t.token),
    expiresIndex: index('user_refresh_tokens_expires_index').on(t.expiresAt),
  }),
);

// Separate table for password reset tokens
export const userPasswordResetTokens = pgTable(
  'user_password_reset_tokens',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    token: text('token').notNull().unique(),
    expiresAt: timestamp('expires_at').notNull(),
    usedAt: timestamp('used_at'),
    ipAddress: text('ip_address'), // Optional: track IP for security
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    userIdIndex: index('user_password_reset_tokens_user_id_index').on(t.userId),
    tokenIndex: index('user_password_reset_tokens_token_index').on(t.token),
    expiresIndex: index('user_password_reset_tokens_expires_index').on(
      t.expiresAt,
    ),
  }),
);

// Separate table for email verification tokens
export const userEmailVerificationTokens = pgTable(
  'user_email_verification_tokens',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    userId: uuid('user_id')
      .references(() => users.id)
      .notNull(),
    email: text('email').notNull(), // The email being verified
    token: text('token').notNull().unique(),
    expiresAt: timestamp('expires_at').notNull(),
    verifiedAt: timestamp('verified_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
  },
  (t) => ({
    userIdIndex: index('user_email_verification_tokens_user_id_index').on(
      t.userId,
    ),
    tokenIndex: index('user_email_verification_tokens_token_index').on(t.token),
    emailIndex: index('user_email_verification_tokens_email_index').on(t.email),
    expiresIndex: index('user_email_verification_tokens_expires_index').on(
      t.expiresAt,
    ),
  }),
);

// Relations definitions

// Users Relations
export const usersRelations = relations(users, ({ one, many }) => ({
  // Self-referencing relations for audit fields
  creator: one(users, {
    fields: [users.createdBy],
    references: [users.id],
    relationName: 'createdUsers',
  }),
  updater: one(users, {
    fields: [users.updatedBy],
    references: [users.id],
    relationName: 'updatedUsers',
  }),
  deleter: one(users, {
    fields: [users.deletedBy],
    references: [users.id],
    relationName: 'deletedUsers',
  }),

  // One-to-many relations to user sub-tables
  accounts: many(userAccounts),
  twoFactor: one(userTwoFactor),
  refreshTokens: many(userRefreshTokens),
  passwordResetTokens: many(userPasswordResetTokens),
  emailVerificationTokens: many(userEmailVerificationTokens),
}));

// User Accounts Relations
export const userAccountsRelations = relations(userAccounts, ({ one }) => ({
  user: one(users, {
    fields: [userAccounts.userId],
    references: [users.id],
  }),
}));

// User Two Factor Relations
export const userTwoFactorRelations = relations(userTwoFactor, ({ one }) => ({
  user: one(users, {
    fields: [userTwoFactor.userId],
    references: [users.id],
  }),
}));

// User Refresh Tokens Relations
export const userRefreshTokensRelations = relations(
  userRefreshTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [userRefreshTokens.userId],
      references: [users.id],
    }),
  }),
);

// User Password Reset Tokens Relations
export const userPasswordResetTokensRelations = relations(
  userPasswordResetTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [userPasswordResetTokens.userId],
      references: [users.id],
    }),
  }),
);

// User Email Verification Tokens Relations
export const userEmailVerificationTokensRelations = relations(
  userEmailVerificationTokens,
  ({ one }) => ({
    user: one(users, {
      fields: [userEmailVerificationTokens.userId],
      references: [users.id],
    }),
  }),
);
