import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  date,
  uniqueIndex,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { assets } from './assets.schema';
import { locations } from './locations.schema';
import { staffMembers } from './staff.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { users } from './users.schema';
import {
  AssetsTransactionType,
  AssetTransactionStatus,
} from '@app/shared/types';

// Asset Transaction Type Enum
export const assetTransactionTypeEnum = pgEnum('asset_transaction_type', [
  AssetsTransactionType.ALLOCATION,
  AssetsTransactionType.DEALLOCATION,
  AssetsTransactionType.TRANSFER,
]);

// Transaction Status Enum
export const assetTransactionStatusEnum = pgEnum('asset_transaction_status', [
  AssetTransactionStatus.PENDING,
  AssetTransactionStatus.IN_PROGRESS,
  AssetTransactionStatus.COMPLETED,
  AssetTransactionStatus.CANCELLED,
  AssetTransactionStatus.FAILED,
]);

export const assetTransactions = pgTable(
  'asset_transactions',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    assetId: uuid('asset_id').references(() => assets.id, {
      onDelete: 'cascade',
    }),

    // Transaction details
    transactionType: assetTransactionTypeEnum(
      'asset_transaction_type',
    ).notNull(),
    status: assetTransactionStatusEnum('status')
      .default(AssetTransactionStatus.COMPLETED)
      .notNull(),
    refNo: text('ref_no').notNull(),
    transactionDatetime: timestamp('transaction_datetime').notNull(),

    // Movement tracking
    receiver: uuid('receiver').references(() => staffMembers.id),
    fromLocationId: uuid('from_location_id').references(() => locations.id),
    toLocationId: uuid('to_location_id').references(() => locations.id),

    // Time-based fields
    allocatedUpto: date('allocated_upto'),
    returnDueDate: date('return_due_date'),
    actualReturnDate: date('actual_return_date'),

    // Condition tracking
    conditionBefore: text('condition_before'),
    conditionAfter: text('condition_after'),

    // Additional metadata
    notes: text('notes'),
    parentId: uuid('parent_id').references(() => assetTransactions.id, {
      onDelete: 'cascade',
    }),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'asset_transactions'),

    // Entity-specific indexes
    assetIdIndex: index('asset_transactions_asset_id_index').on(t.assetId),
    receiverIndex: index('asset_transactions_receiver_index').on(t.receiver),
    fromLocationIdIndex: index('asset_transactions_from_location_id_index').on(
      t.fromLocationId,
    ),
    toLocationIdIndex: index('asset_transactions_to_location_id_index').on(
      t.toLocationId,
    ),
    parentIdIndex: index('asset_transactions_parent_id_index').on(t.parentId),
    transactionTypeIndex: index('asset_transactions_type_index').on(
      t.transactionType,
    ),
    statusIndex: index('asset_transactions_status_index').on(t.status),
    refNoIndex: index('asset_transactions_ref_no_index').on(t.refNo),
    transactionDatetimeIndex: index('asset_transactions_datetime_index').on(
      t.transactionDatetime,
    ),

    // Composite indexes for common query patterns
    businessAssetIndex: index('asset_transactions_business_asset_index')
      .on(t.businessId, t.assetId)
      .where(sql`${t.isDeleted} = false`),
    businessDateIndex: index('asset_transactions_business_date_index')
      .on(t.businessId, t.transactionDatetime)
      .where(sql`${t.isDeleted} = false`),
    assetTypeIndex: index('asset_transactions_asset_type_index')
      .on(t.assetId, t.transactionType)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('asset_transactions_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueBusinessRefNo: uniqueIndex(
      'asset_transactions_business_ref_no_unique',
    )
      .on(t.businessId, t.refNo)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const assetTransactionsRelations = relations(
  assetTransactions,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [assetTransactions.businessId],
      references: [business.id],
    }),

    // Asset relation
    asset: one(assets, {
      fields: [assetTransactions.assetId],
      references: [assets.id],
    }),

    // Staff relation (receiver)
    receiverStaff: one(staffMembers, {
      fields: [assetTransactions.receiver],
      references: [staffMembers.id],
    }),

    // Location relations
    fromLocation: one(locations, {
      fields: [assetTransactions.fromLocationId],
      references: [locations.id],
      relationName: 'fromLocationTransactions',
    }),
    toLocation: one(locations, {
      fields: [assetTransactions.toLocationId],
      references: [locations.id],
      relationName: 'toLocationTransactions',
    }),

    // Parent-child relations (for related transactions)
    parent: one(assetTransactions, {
      fields: [assetTransactions.parentId],
      references: [assetTransactions.id],
      relationName: 'parentTransaction',
    }),
    children: many(assetTransactions, {
      relationName: 'parentTransaction',
    }),

    // Audit relations
    creator: one(users, {
      fields: [assetTransactions.createdBy],
      references: [users.id],
      relationName: 'createdAssetTransactions',
    }),
    updater: one(users, {
      fields: [assetTransactions.updatedBy],
      references: [users.id],
      relationName: 'updatedAssetTransactions',
    }),
  }),
);
