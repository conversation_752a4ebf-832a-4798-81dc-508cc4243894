import {
  boolean,
  check,
  date,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { ReviewType, ReviewStatus, GoalsAchievement } from '../../shared/types';
import { createBaseEntityFields } from './common-fields.schema';

import { users } from './users.schema';
export const reviewTypeEnum = pgEnum('review_type', [
  ReviewType.ANNUAL,
  ReviewType.SEMI_ANNUAL,
  ReviewType.QUARTERLY,
  ReviewType.PROBATIONARY,
  ReviewType.PROJECT_BASED,
  ReviewType.PROMOTION,
  ReviewType.DISCIPLINARY,
  ReviewType.EXIT_INTERVIEW,
]);

export const reviewStatusEnum = pgEnum('review_status', [
  ReviewStatus.DRAFT,
  ReviewStatus.PENDING_EMPLOYEE_INPUT,
  ReviewStatus.PENDING_MANAGER_REVIEW,
  ReviewStatus.PENDING_HR_APPROVAL,
  ReviewStatus.COMPLETED,
  ReviewStatus.CANCELLED,
  ReviewStatus.OVERDUE,
]);

export const goalsAchievementEnum = pgEnum('goals_achievement', [
  GoalsAchievement.EXCEEDS_EXPECTATIONS,
  GoalsAchievement.MEETS_EXPECTATIONS,
  GoalsAchievement.PARTIALLY_MEETS_EXPECTATIONS,
  GoalsAchievement.BELOW_EXPECTATIONS,
  GoalsAchievement.NOT_APPLICABLE,
]);

export const performanceReviews = pgTable(
  'performance_reviews',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
    reviewerId: uuid('reviewer_id')
      .notNull()
      .references(() => staffMembers.id),
    reviewPeriodStart: date('review_period_start').notNull(),
    reviewPeriodEnd: date('review_period_end').notNull(),
    reviewType: reviewTypeEnum('review_type')
      .default(ReviewType.ANNUAL)
      .notNull(),
    overallRating: decimal('overall_rating', { precision: 3, scale: 2 }),
    goalsAchievement: goalsAchievementEnum('goals_achievement'),
    strengths: text('strengths'),
    areasForImprovement: text('areas_for_improvement'),
    developmentPlan: text('development_plan'),
    status: reviewStatusEnum('status').default(ReviewStatus.DRAFT).notNull(),
  },
  (t) => ({
    idIndex: index('performance_reviews_id_index').on(t.id),
    businessIdIndex: index('performance_reviews_business_id_index').on(
      t.businessId,
    ),
    staffIdIndex: index('performance_reviews_staff_id_index').on(t.staffId),
    reviewerIdIndex: index('performance_reviews_reviewer_id_index').on(
      t.reviewerId,
    ),
    reviewTypeIndex: index('performance_reviews_review_type_index').on(
      t.reviewType,
    ),
    statusIndex: index('performance_reviews_status_index').on(t.status),
    reviewPeriodStartIndex: index(
      'performance_reviews_review_period_start_index',
    ).on(t.reviewPeriodStart),
    reviewPeriodEndIndex: index(
      'performance_reviews_review_period_end_index',
    ).on(t.reviewPeriodEnd),

    // Optimized composite indexes with soft deletion support
    businessStaffIndex: index('performance_reviews_business_staff_index')
      .on(t.businessId, t.staffId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('performance_reviews_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessReviewTypeIndex: index(
      'performance_reviews_business_review_type_index',
    )
      .on(t.businessId, t.reviewType)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessStaffPeriodStart: uniqueIndex(
      'performance_reviews_business_staff_period_start_unique',
    )
      .on(t.businessId, t.staffId, t.reviewPeriodStart)
      .where(sql`${t.isDeleted} = false`),

    // Check constraints
    reviewPeriodEndAfterStart: check(
      'performance_reviews_review_period_end_after_start',
      sql`${t.reviewPeriodEnd} >= ${t.reviewPeriodStart}`,
    ),
    staffNotReviewer: check(
      'performance_reviews_staff_not_reviewer',
      sql`${t.staffId} != ${t.reviewerId}`,
    ),
    overallRatingRange: check(
      'performance_reviews_overall_rating_range',
      sql`${t.overallRating} >= 0.00 AND ${t.overallRating} <= 5.00`,
    ),
  }),
);

// Relations
export const performanceReviewsRelations = relations(
  performanceReviews,
  ({ one }) => ({
    business: one(business, {
      fields: [performanceReviews.businessId],
      references: [business.id],
    }),
    staff: one(staffMembers, {
      fields: [performanceReviews.staffId],
      references: [staffMembers.id],
    }),
    reviewer: one(staffMembers, {
      fields: [performanceReviews.reviewerId],
      references: [staffMembers.id],
    }),
    createdByUser: one(users, {
      fields: [performanceReviews.createdBy],
      references: [users.id],
    }),
    updatedByUser: one(users, {
      fields: [performanceReviews.updatedBy],
      references: [users.id],
    }),
  }),
);
