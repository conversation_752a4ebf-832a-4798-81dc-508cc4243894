import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { businessRoles } from './business-roles.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { BusinessUserStatus, BusinessUserRole } from '../../shared/types';

export const businessUserRoleEnum = pgEnum('business_user_role', [
  BusinessUserRole.ADMIN,
  BusinessUserRole.MANAGER,
  BusinessUserRole.CUSTOM,
]);

export const businessUserStatusEnum = pgEnum('business_user_status', [
  BusinessUserStatus.ACTIVE,
  BusinessUserStatus.INACTIVE,
  BusinessUserStatus.INVITED,
  BusinessUserStatus.SUSPENDED,
  BusinessUserStatus.DELETED,
]);

export const businessUsers = pgTable(
  'business_users',
  {
    ...createBaseEntityFields,
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    role: businessUserRoleEnum('role').notNull(),
    businessRoleId: uuid('business_role_id').references(
      () => businessRoles.id,
      { onDelete: 'cascade' },
    ),
    isActiveBusiness: boolean('is_active_business').default(false).notNull(), // Whether this business is the user's currently active business
    isAllowedAllLocation: boolean('is_allowed_all_location')
      .default(false)
      .notNull(), // Whether this user has access to all locations in the business
    invitedBy: uuid('invited_by').references(() => users.id),
    invitedAt: timestamp('invited_at'),
    joinedAt: timestamp('joined_at'),
    lastActiveAt: timestamp('last_active_at'),
    status: businessUserStatusEnum('status')
      .default(BusinessUserStatus.ACTIVE)
      .notNull(),
    notes: text('notes'), // Internal notes about this business user
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'business_users'),
    userIdIndex: index('business_users_user_id_index').on(t.userId),
    businessRoleIdIndex: index('business_users_business_role_id_index').on(
      t.businessRoleId,
    ),
    statusIndex: index('business_users_status_index').on(t.status),
    // Composite indexes for common queries
    businessUserIndex: index('business_users_business_user_index').on(
      t.businessId,
      t.userId,
    ),
    businessRoleIndex: index('business_users_business_role_index').on(
      t.businessId,
      t.businessRoleId,
    ),
    userBusinessIndex: index('business_users_user_business_index').on(
      t.userId,
      t.businessId,
    ),
  }),
);

// Business Users Relations
export const businessUsersRelations = relations(businessUsers, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [businessUsers.businessId],
    references: [business.id],
    relationName: 'businessUsers',
  }),

  // User relation
  user: one(users, {
    fields: [businessUsers.userId],
    references: [users.id],
    relationName: 'userBusinesses',
  }),

  // Business role relation
  businessRole: one(businessRoles, {
    fields: [businessUsers.businessRoleId],
    references: [businessRoles.id],
    relationName: 'businessRoleUsers',
  }),

  // Invited by relation
  invitedByUser: one(users, {
    fields: [businessUsers.invitedBy],
    references: [users.id],
    relationName: 'invitedBusinessUsers',
  }),

  // Audit relations
  creator: one(users, {
    fields: [businessUsers.createdBy],
    references: [users.id],
    relationName: 'createdBusinessUsers',
  }),
  updater: one(users, {
    fields: [businessUsers.updatedBy],
    references: [users.id],
    relationName: 'updatedBusinessUsers',
  }),
}));
