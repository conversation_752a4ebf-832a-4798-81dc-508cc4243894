import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  json,
  timestamp,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import {
  TemplateCategory,
  TemplateType,
} from '../../shared/types/template.enum';
import { StatusType } from '../../shared/types/common.enum';

// Template Category enum
export const templateCategoryEnum = pgEnum('template_category', [
  TemplateCategory.MARKETING,
  TemplateCategory.TRANSACTIONAL,
  TemplateCategory.NOTIFICATION,
  TemplateCategory.REMINDER,
  TemplateCategory.WELCOME,
  TemplateCategory.CONFIRMATION,
  TemplateCategory.INVOICE,
  TemplateCategory.RECEIPT,
  TemplateCategory.APPOINTMENT,
  TemplateCategory.PROMOTIONAL,
  TemplateCategory.NEWSLETTER,
  TemplateCategory.SUPPORT,
  TemplateCategory.ALERT,
  TemplateCategory.SURVEY,
  TemplateCategory.FEEDBACK,
  TemplateCategory.OTHER,
]);

// Template Type enum
export const templateTypeEnum = pgEnum('template_type', [
  TemplateType.PRODUCT_LAUNCH,
  TemplateType.SALE_ANNOUNCEMENT,
  TemplateType.DISCOUNT_OFFER,
  TemplateType.ORDER_CONFIRMATION,
  TemplateType.PAYMENT_CONFIRMATION,
  TemplateType.SHIPPING_NOTIFICATION,
  TemplateType.DELIVERY_CONFIRMATION,
  TemplateType.SYSTEM_UPDATE,
  TemplateType.MAINTENANCE_NOTICE,
  TemplateType.SECURITY_ALERT,
  TemplateType.APPOINTMENT_REMINDER,
  TemplateType.PAYMENT_DUE,
  TemplateType.SUBSCRIPTION_RENEWAL,
  TemplateType.CART_ABANDONMENT,
  TemplateType.NEW_USER_WELCOME,
  TemplateType.ONBOARDING_STEP,
  TemplateType.TICKET_CREATED,
  TemplateType.TICKET_RESOLVED,
  TemplateType.FAQ_RESPONSE,
  TemplateType.CUSTOM,
  TemplateType.GENERAL,
]);

// Template Status enum (using existing StatusType)
export const templateStatusEnum = pgEnum('template_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

// SMS Template Schema
export const smsTemplates = pgTable(
  'sms_templates',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    content: text('content').notNull(),
    category: templateCategoryEnum('category').notNull(),
    type: templateTypeEnum('type'),
    description: text('description'),
    status: templateStatusEnum('status').default(StatusType.ACTIVE).notNull(),
    language: text('language'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'sms_templates'),
    nameIndex: index('sms_templates_name_index').on(t.name),
    categoryIndex: index('sms_templates_category_index').on(t.category),
    statusIndex: index('sms_templates_status_index').on(t.status),
    uniqueNamePerBusiness: uniqueIndex('sms_templates_name_business_unique').on(
      t.name,
      t.businessId,
    ),
  }),
);

// WhatsApp Template Schema
export const whatsappTemplates = pgTable(
  'whatsapp_templates',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    content: text('content').notNull(),
    category: templateCategoryEnum('category').notNull(),
    type: templateTypeEnum('type'),
    description: text('description'),
    status: templateStatusEnum('status').default(StatusType.ACTIVE).notNull(),
    language: text('language'),
    mediaId: uuid('media_id').references(() => media.id),
    captionEnabled: boolean('caption_enabled').default(false).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'whatsapp_templates'),
    nameIndex: index('whatsapp_templates_name_index').on(t.name),
    categoryIndex: index('whatsapp_templates_category_index').on(t.category),
    statusIndex: index('whatsapp_templates_status_index').on(t.status),
    mediaIdIndex: index('whatsapp_templates_media_id_index').on(t.mediaId),
    uniqueNamePerBusiness: uniqueIndex(
      'whatsapp_templates_name_business_unique',
    ).on(t.name, t.businessId),
  }),
);

// Email Template Schema
export const emailTemplates = pgTable(
  'email_templates',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    subject: text('subject').notNull(),
    textContent: text('text_content'),
    htmlContent: text('html_content'),
    ampContent: text('amp_content'),
    fromEmail: text('from_email').notNull(),
    fromName: text('from_name'),
    replyToEmail: text('reply_to_email'),
    defaultToEmails: json('default_to_emails').$type<string[]>(),
    defaultCcEmails: json('default_cc_emails').$type<string[]>(),
    defaultBccEmails: json('default_bcc_emails').$type<string[]>(),
    category: templateCategoryEnum('category').notNull(),
    type: templateTypeEnum('type'),
    description: text('description'),
    status: templateStatusEnum('status').default(StatusType.ACTIVE).notNull(),
    language: text('language'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'email_templates'),
    nameIndex: index('email_templates_name_index').on(t.name),
    categoryIndex: index('email_templates_category_index').on(t.category),
    statusIndex: index('email_templates_status_index').on(t.status),
    fromEmailIndex: index('email_templates_from_email_index').on(t.fromEmail),
    uniqueNamePerBusiness: uniqueIndex(
      'email_templates_name_business_unique',
    ).on(t.name, t.businessId),
  }),
);

// SMS Templates Relations
export const smsTemplatesRelations = relations(smsTemplates, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [smsTemplates.businessId],
    references: [business.id],
    relationName: 'businessSmsTemplates',
  }),

  // Audit relations
  creator: one(users, {
    fields: [smsTemplates.createdBy],
    references: [users.id],
    relationName: 'createdSmsTemplates',
  }),
  updater: one(users, {
    fields: [smsTemplates.updatedBy],
    references: [users.id],
    relationName: 'updatedSmsTemplates',
  }),
}));

// WhatsApp Templates Relations
export const whatsappTemplatesRelations = relations(
  whatsappTemplates,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [whatsappTemplates.businessId],
      references: [business.id],
      relationName: 'businessWhatsappTemplates',
    }),

    // Media relation
    media: one(media, {
      fields: [whatsappTemplates.mediaId],
      references: [media.id],
      relationName: 'whatsappTemplateMedia',
    }),

    // Audit relations
    creator: one(users, {
      fields: [whatsappTemplates.createdBy],
      references: [users.id],
      relationName: 'createdWhatsappTemplates',
    }),
    updater: one(users, {
      fields: [whatsappTemplates.updatedBy],
      references: [users.id],
      relationName: 'updatedWhatsappTemplates',
    }),
  }),
);

// Email Templates Relations
export const emailTemplatesRelations = relations(emailTemplates, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [emailTemplates.businessId],
    references: [business.id],
    relationName: 'businessEmailTemplates',
  }),

  // Audit relations
  creator: one(users, {
    fields: [emailTemplates.createdBy],
    references: [users.id],
    relationName: 'createdEmailTemplates',
  }),
  updater: one(users, {
    fields: [emailTemplates.updatedBy],
    references: [users.id],
    relationName: 'updatedEmailTemplates',
  }),
}));
