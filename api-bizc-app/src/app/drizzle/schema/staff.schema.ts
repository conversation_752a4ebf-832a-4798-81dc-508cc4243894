import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
  integer,
  date,
  decimal,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { departments } from './departments.schema';
import { designations } from './designations.schema';
import { addresses } from './address.schema';
import { media } from './media.schema';
import { serviceStaff } from './services.schema';
import {
  EmploymentStatus,
  EmploymentType,
  MaritalStatus,
  Gender,
  BloodType,
  VisionCondition,
  HearingCondition,
  LimbCondition,
} from '../../shared/types';

// Enums for Staff Members
export const staffStatusEnum = pgEnum('staff_status', [
  EmploymentStatus.ACTIVE,
  EmploymentStatus.INACTIVE,
  EmploymentStatus.TERMINATED,
  EmploymentStatus.ON_LEAVE,
  EmploymentStatus.PROBATION,
]);

export const employmentTypeEnum = pgEnum('employment_type', [
  EmploymentType.FULL_TIME,
  EmploymentType.PART_TIME,
  EmploymentType.CONTRACT,
  EmploymentType.INTERN,
  EmploymentType.CONSULTANT,
]);

// Enums for Family Details
export const maritalStatusEnum = pgEnum('marital_status', [
  MaritalStatus.SINGLE,
  MaritalStatus.MARRIED,
  MaritalStatus.DIVORCED,
  MaritalStatus.WIDOWED,
  MaritalStatus.SEPARATED,
]);

export const genderEnum = pgEnum('gender', [
  Gender.MALE,
  Gender.FEMALE,
  Gender.OTHER,
]);

// Enums for Physical Info
export const bloodTypeEnum = pgEnum('blood_type', [
  BloodType.A_POSITIVE,
  BloodType.A_NEGATIVE,
  BloodType.B_POSITIVE,
  BloodType.B_NEGATIVE,
  BloodType.AB_POSITIVE,
  BloodType.AB_NEGATIVE,
  BloodType.O_POSITIVE,
  BloodType.O_NEGATIVE,
]);

export const visionConditionEnum = pgEnum('vision_condition', [
  VisionCondition.NORMAL,
  VisionCondition.SHORT_SIGHTED,
  VisionCondition.FAR_SIGHTED,
  VisionCondition.ASTIGMATIC,
  VisionCondition.SHORT_SIGHTED_AND_ASTIGMATIC,
  VisionCondition.FAR_SIGHTED_AND_ASTIGMATIC,
  VisionCondition.PARTIALLY_IMPAIRED,
  VisionCondition.BLIND,
]);

export const hearingConditionEnum = pgEnum('hearing_condition', [
  HearingCondition.NORMAL,
  HearingCondition.PARTIALLY_IMPAIRED,
  HearingCondition.DEAF,
]);

export const limbConditionEnum = pgEnum('limb_condition', [
  LimbCondition.NORMAL,
  LimbCondition.PARTIALLY_IMPAIRED,
  LimbCondition.HANDICAPPED,
]);

// Staff Members Table
export const staffMembers = pgTable(
  'staff_members',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    userId: uuid('user_id').references(() => users.id),
    departmentId: uuid('department_id').references(() => departments.id),
    designationId: uuid('designation_id').references(() => designations.id),
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    displayName: text('display_name').notNull(),
    email: text('email').notNull(),
    phone: text('phone'),
    isUser: boolean('is_user').default(false).notNull(),
    employeeId: text('employee_id'),
    dateOfBirth: timestamp('date_of_birth'),
    dateOfJoining: timestamp('date_of_joining'),
    employmentType: employmentTypeEnum('employment_type').default(
      EmploymentType.FULL_TIME,
    ),

    // References to other schemas
    addressId: uuid('address_id').references(() => addresses.id),
    profileImageId: uuid('profile_image_id').references(() => media.id),

    status: staffStatusEnum('status')
      .default(EmploymentStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'staff_members'),
    userIdIndex: index('staff_members_user_id_index').on(t.userId),
    departmentIdIndex: index('staff_members_department_id_index').on(
      t.departmentId,
    ),
    designationIdIndex: index('staff_members_designation_id_index').on(
      t.designationId,
    ),
    emailIndex: index('staff_members_email_index').on(t.email),
    displayNameIndex: index('staff_members_display_name_index').on(
      t.displayName,
    ),
    employmentTypeIndex: index('staff_members_employment_type_index').on(
      t.employmentType,
    ),
    addressIdIndex: index('staff_members_address_id_index').on(t.addressId),
    profileImageIdIndex: index('staff_members_profile_image_id_index').on(
      t.profileImageId,
    ),
    statusIndex: index('staff_members_status_index').on(t.status),

    // Optimized composite indexes with soft deletion support
    businessDisplayNameIndex: index('staff_members_business_display_name_index')
      .on(t.businessId, t.displayName)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('staff_members_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessEmploymentTypeIndex: index(
      'staff_members_business_employment_type_index',
    )
      .on(t.businessId, t.employmentType)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessDisplayName: uniqueIndex(
      'staff_members_business_display_name_unique',
    )
      .on(t.businessId, t.displayName)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessEmail: uniqueIndex('staff_members_business_email_unique')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessEmployeeId: uniqueIndex(
      'staff_members_business_employee_id_unique',
    )
      .on(t.businessId, t.employeeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Staff Emergency Contacts Table
export const staffEmergencyContacts = pgTable(
  'staff_emergency_contacts',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id),

    // Personal Details
    firstName: text('first_name').notNull(),
    middleName: text('middle_name'),
    lastName: text('last_name').notNull(),
    relationship: text('relationship').notNull(), // e.g., "Father", "Mother", "Spouse", "Sibling", "Friend"

    // Phone Numbers
    mobilePhone: text('mobile_phone'),
    housePhone: text('house_phone'),
    officePhone: text('office_phone'),

    // Address Reference
    addressId: uuid('address_id').references(() => addresses.id),

    // Priority/Order (for multiple emergency contacts)
    priority: integer('priority').default(1).notNull(), // 1 = primary, 2 = secondary, etc.

    // Status
    isActive: boolean('is_active').default(true).notNull(),

    // Additional Information
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'staff_emergency_contacts'),
    staffMemberIdIndex: index(
      'staff_emergency_contacts_staff_member_id_index',
    ).on(t.staffMemberId),
    firstNameIndex: index('staff_emergency_contacts_first_name_index').on(
      t.firstName,
    ),
    lastNameIndex: index('staff_emergency_contacts_last_name_index').on(
      t.lastName,
    ),
    relationshipIndex: index('staff_emergency_contacts_relationship_index').on(
      t.relationship,
    ),
    mobilePhoneIndex: index('staff_emergency_contacts_mobile_phone_index').on(
      t.mobilePhone,
    ),
    priorityIndex: index('staff_emergency_contacts_priority_index').on(
      t.priority,
    ),
    isActiveIndex: index('staff_emergency_contacts_is_active_index').on(
      t.isActive,
    ),
    addressIdIndex: index('staff_emergency_contacts_address_id_index').on(
      t.addressId,
    ),

    // Composite indexes for better performance
    businessStaffMemberIndex: index(
      'staff_emergency_contacts_business_staff_member_index',
    )
      .on(t.businessId, t.staffMemberId)
      .where(sql`${t.isDeleted} = false`),

    staffMemberPriorityIndex: index(
      'staff_emergency_contacts_staff_member_priority_index',
    )
      .on(t.staffMemberId, t.priority)
      .where(sql`${t.isDeleted} = false`),

    businessStaffMemberActiveIndex: index(
      'staff_emergency_contacts_business_staff_member_active_index',
    )
      .on(t.businessId, t.staffMemberId, t.isActive)
      .where(sql`${t.isDeleted} = false`),

    staffMemberNameIndex: index(
      'staff_emergency_contacts_staff_member_name_index',
    )
      .on(t.staffMemberId, t.firstName, t.lastName)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint for staff member priority to avoid duplicate priorities
    uniqueStaffMemberPriority: uniqueIndex(
      'staff_emergency_contacts_staff_member_priority_unique',
    )
      .on(t.staffMemberId, t.priority)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Staff Family Details Table
export const staffFamilyDetails = pgTable(
  'staff_family_details',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id),
    maritalStatus: maritalStatusEnum('marital_status').notNull(),

    // Children information
    numberOfChildren: integer('number_of_children').default(0).notNull(),

    // Spouse details (only if married)
    isSpouseWorking: boolean('is_spouse_working').default(false),
    spouseFirstName: text('spouse_first_name'),
    spouseMiddleName: text('spouse_middle_name'),
    spouseLastName: text('spouse_last_name'),
    spouseBirthDate: date('spouse_birth_date'),
    spouseGender: genderEnum('spouse_gender'),

    // Contact details
    spouseEmail: text('spouse_email'),
    spousePhone: text('spouse_phone'),
    // Additional information
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'staff_family_details'),
    staffMemberIdIndex: index('staff_family_details_staff_member_id_index').on(
      t.staffMemberId,
    ),
    maritalStatusIndex: index('staff_family_details_marital_status_index').on(
      t.maritalStatus,
    ),
    numberOfChildrenIndex: index(
      'staff_family_details_number_of_children_index',
    ).on(t.numberOfChildren),
    spouseEmailIndex: index('staff_family_details_spouse_email_index').on(
      t.spouseEmail,
    ),

    // Composite indexes for better performance
    businessStaffMemberIndex: index(
      'staff_family_details_business_staff_member_index',
    )
      .on(t.businessId, t.staffMemberId)
      .where(sql`${t.isDeleted} = false`),

    businessMaritalStatusIndex: index(
      'staff_family_details_business_marital_status_index',
    )
      .on(t.businessId, t.maritalStatus)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to ensure one family record per staff member
    uniqueStaffMemberFamily: uniqueIndex(
      'staff_family_details_staff_member_unique',
    )
      .on(t.staffMemberId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Staff Physical Information Table
export const staffPhysicalInfo = pgTable(
  'staff_physical_info',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id),

    // Physical Measurements
    heightCm: decimal('height_cm', { precision: 5, scale: 2 }), // e.g., 175.50 cm
    weightKg: decimal('weight_kg', { precision: 5, scale: 2 }), // e.g., 70.25 kg
    bloodType: bloodTypeEnum('blood_type'),

    // Vision Conditions
    visionLeft: visionConditionEnum('vision_left').default(
      VisionCondition.NORMAL,
    ),
    visionRight: visionConditionEnum('vision_right').default(
      VisionCondition.NORMAL,
    ),

    // Hearing Conditions
    hearingLeft: hearingConditionEnum('hearing_left').default(
      HearingCondition.NORMAL,
    ),
    hearingRight: hearingConditionEnum('hearing_right').default(
      HearingCondition.NORMAL,
    ),

    // Hand Conditions
    handLeft: limbConditionEnum('hand_left').default(LimbCondition.NORMAL),
    handRight: limbConditionEnum('hand_right').default(LimbCondition.NORMAL),

    // Leg Conditions
    legLeft: limbConditionEnum('leg_left').default(LimbCondition.NORMAL),
    legRight: limbConditionEnum('leg_right').default(LimbCondition.NORMAL),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'staff_physical_info'),
    staffMemberIdIndex: index('staff_physical_info_staff_member_id_index').on(
      t.staffMemberId,
    ),
    bloodTypeIndex: index('staff_physical_info_blood_type_index').on(
      t.bloodType,
    ),
    heightIndex: index('staff_physical_info_height_index').on(t.heightCm),
    weightIndex: index('staff_physical_info_weight_index').on(t.weightKg),

    // Vision indexes
    visionLeftIndex: index('staff_physical_info_vision_left_index').on(
      t.visionLeft,
    ),
    visionRightIndex: index('staff_physical_info_vision_right_index').on(
      t.visionRight,
    ),

    // Hearing indexes
    hearingLeftIndex: index('staff_physical_info_hearing_left_index').on(
      t.hearingLeft,
    ),
    hearingRightIndex: index('staff_physical_info_hearing_right_index').on(
      t.hearingRight,
    ),

    // Hand indexes
    handLeftIndex: index('staff_physical_info_hand_left_index').on(t.handLeft),
    handRightIndex: index('staff_physical_info_hand_right_index').on(
      t.handRight,
    ),

    // Leg indexes
    legLeftIndex: index('staff_physical_info_leg_left_index').on(t.legLeft),
    legRightIndex: index('staff_physical_info_leg_right_index').on(t.legRight),

    // Composite indexes for better performance
    businessStaffMemberIndex: index(
      'staff_physical_info_business_staff_member_index',
    )
      .on(t.businessId, t.staffMemberId)
      .where(sql`${t.isDeleted} = false`),

    // Indexes for filtering by physical conditions
    businessVisionImpairmentIndex: index(
      'staff_physical_info_business_vision_impairment_index',
    )
      .on(t.businessId, t.visionLeft, t.visionRight)
      .where(sql`${t.isDeleted} = false`),

    businessHearingImpairmentIndex: index(
      'staff_physical_info_business_hearing_impairment_index',
    )
      .on(t.businessId, t.hearingLeft, t.hearingRight)
      .where(sql`${t.isDeleted} = false`),

    businessMobilityImpairmentIndex: index(
      'staff_physical_info_business_mobility_impairment_index',
    )
      .on(t.businessId, t.handLeft, t.handRight, t.legLeft, t.legRight)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to ensure one physical info record per staff member
    uniqueStaffMemberPhysical: uniqueIndex(
      'staff_physical_info_staff_member_unique',
    )
      .on(t.staffMemberId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const staffMembersRelations = relations(
  staffMembers,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [staffMembers.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [staffMembers.createdBy],
      references: [users.id],
      relationName: 'createdStaffMembers',
    }),
    updater: one(users, {
      fields: [staffMembers.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffMembers',
    }),

    // User relation
    user: one(users, {
      fields: [staffMembers.userId],
      references: [users.id],
      relationName: 'staffMemberUser',
    }),

    // Department relation
    department: one(departments, {
      fields: [staffMembers.departmentId],
      references: [departments.id],
    }),

    // Designation relation
    designation: one(designations, {
      fields: [staffMembers.designationId],
      references: [designations.id],
    }),

    // Address relation
    address: one(addresses, {
      fields: [staffMembers.addressId],
      references: [addresses.id],
    }),

    // Profile image relation
    profileImage: one(media, {
      fields: [staffMembers.profileImageId],
      references: [media.id],
      relationName: 'staffMemberProfileImage',
    }),

    // One-to-many relations
    emergencyContacts: many(staffEmergencyContacts),
    familyDetails: many(staffFamilyDetails),
    physicalInfo: many(staffPhysicalInfo),
    serviceStaff: many(serviceStaff),
  }),
);

export const staffEmergencyContactsRelations = relations(
  staffEmergencyContacts,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [staffEmergencyContacts.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [staffEmergencyContacts.createdBy],
      references: [users.id],
      relationName: 'createdStaffEmergencyContacts',
    }),
    updater: one(users, {
      fields: [staffEmergencyContacts.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffEmergencyContacts',
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [staffEmergencyContacts.staffMemberId],
      references: [staffMembers.id],
    }),

    // Address relation
    address: one(addresses, {
      fields: [staffEmergencyContacts.addressId],
      references: [addresses.id],
    }),
  }),
);

export const staffFamilyDetailsRelations = relations(
  staffFamilyDetails,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [staffFamilyDetails.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [staffFamilyDetails.createdBy],
      references: [users.id],
      relationName: 'createdStaffFamilyDetails',
    }),
    updater: one(users, {
      fields: [staffFamilyDetails.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffFamilyDetails',
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [staffFamilyDetails.staffMemberId],
      references: [staffMembers.id],
    }),
  }),
);

export const staffPhysicalInfoRelations = relations(
  staffPhysicalInfo,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [staffPhysicalInfo.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [staffPhysicalInfo.createdBy],
      references: [users.id],
      relationName: 'createdStaffPhysicalInfo',
    }),
    updater: one(users, {
      fields: [staffPhysicalInfo.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffPhysicalInfo',
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [staffPhysicalInfo.staffMemberId],
      references: [staffMembers.id],
    }),
  }),
);
