import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { paymentAccountTypes } from './payment-account-types.schema';
import { StatusType } from '../../shared/types';
import { createBaseEntityFields } from './common-fields.schema';

import { users } from './users.schema';
export const paymentAccountStatusEnum = pgEnum('payment_account_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const paymentAccounts = pgTable(
  'payment_accounts',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    accountNumber: text('account_number').notNull(),
    accountTypeId: uuid('account_type_id').references(
      () => paymentAccountTypes.id,
    ),
    note: text('note'),
    status: paymentAccountStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('payment_accounts_id_index').on(t.id),
    businessIdIndex: index('payment_accounts_business_id_index').on(
      t.businessId,
    ),
    nameIndex: index('payment_accounts_name_index').on(t.name),
    accountNumberIndex: index('payment_accounts_account_number_index').on(
      t.accountNumber,
    ),
    accountTypeIdIndex: index('payment_accounts_account_type_id_index').on(
      t.accountTypeId,
    ),
    uniqueBusinessAccountNumber: uniqueIndex(
      'payment_accounts_business_account_number_unique',
    ).on(t.businessId, t.accountNumber),
  }),
);

// Relations
export const paymentAccountsRelations = relations(
  paymentAccounts,
  ({ one }) => ({
    business: one(business, {
      fields: [paymentAccounts.businessId],
      references: [business.id],
    }),
    accountType: one(paymentAccountTypes, {
      fields: [paymentAccounts.accountTypeId],
      references: [paymentAccountTypes.id],
    }),
    createdByUser: one(users, {
      fields: [paymentAccounts.createdBy],
      references: [users.id],
    }),
    updatedByUser: one(users, {
      fields: [paymentAccounts.updatedBy],
      references: [users.id],
    }),
  }),
);
