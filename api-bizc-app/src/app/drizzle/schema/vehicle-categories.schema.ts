import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import { assetCategories } from './asset-categories.schema';
import {
  locationAllocationFields,
  auditFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { VehicleCategoryStatus } from '../../shared/types';

export const vehicleCategoryStatusEnum = pgEnum('vehicle_category_status', [
  VehicleCategoryStatus.ACTIVE,
  VehicleCategoryStatus.INACTIVE,
]);

export const vehicleTypes = pgTable(
  'vehicle_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    shortCode: text('short_code'),
    parentId: uuid('parent_id').references(() => vehicleTypes.id),
    description: text('description'),
    slug: text('slug'),
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),
    color: text('color'),
    image: uuid('image').references(() => media.id),

    // Asset category reference (automatically created)
    assetCategoryId: uuid('asset_category_id').references(
      () => assetCategories.id,
    ),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),
    status: vehicleCategoryStatusEnum('status')
      .default(VehicleCategoryStatus.ACTIVE)
      .notNull(),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'vehicle_types'),
    nameIndex: index('vehicle_types_name_index').on(t.name),
    parentIdIndex: index('vehicle_types_parent_id_index').on(t.parentId),
    slugIndex: index('vehicle_types_slug_index').on(t.slug),
    imageIndex: index('vehicle_types_image_index').on(t.image),
    ogImageIndex: index('vehicle_types_og_image_index').on(t.ogImage),
    assetCategoryIdIndex: index('vehicle_types_asset_category_id_index').on(
      t.assetCategoryId,
    ),
    positionIndex: index('vehicle_types_position_index').on(t.position),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('vehicle_types_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentPositionIndex: index(
      'vehicle_types_business_parent_position_index',
    )
      .on(t.businessId, t.parentId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'vehicle_types_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentStatusPositionIndex: index(
      'vehicle_types_business_parent_status_position_index',
    )
      .on(t.businessId, t.parentId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('vehicle_types_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('vehicle_types_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessShortCodeIndex: index('vehicle_types_business_short_code_index')
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('vehicle_types_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('vehicle_types_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessShortCode: uniqueIndex(
      'vehicle_types_business_short_code_unique',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for vehicle type locations (many-to-many relationship)
export const vehicleTypeLocations = pgTable(
  'vehicle_type_locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    vehicleTypeId: uuid('vehicle_type_id')
      .notNull()
      .references(() => vehicleTypes.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    // Standard audit fields (using reusable fields)
    ...auditFields,
  },
  (t) => ({
    vehicleTypeIdIndex: index(
      'vehicle_type_locations_vehicle_type_id_index',
    ).on(t.vehicleTypeId),
    locationIdIndex: index('vehicle_type_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueVehicleTypeLocation: uniqueIndex('vehicle_type_locations_unique').on(
      t.vehicleTypeId,
      t.locationId,
    ),
  }),
);

// Relations
export const vehicleTypesRelations = relations(
  vehicleTypes,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [vehicleTypes.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [vehicleTypes.createdBy],
      references: [users.id],
      relationName: 'createdVehicleTypes',
    }),
    updater: one(users, {
      fields: [vehicleTypes.updatedBy],
      references: [users.id],
      relationName: 'updatedVehicleTypes',
    }),

    // Parent-child relations
    parent: one(vehicleTypes, {
      fields: [vehicleTypes.parentId],
      references: [vehicleTypes.id],
      relationName: 'vehicleTypeParent',
    }),
    children: many(vehicleTypes, {
      relationName: 'vehicleTypeParent',
    }),

    // Media relations
    imageMedia: one(media, {
      fields: [vehicleTypes.image],
      references: [media.id],
      relationName: 'vehicleTypeImage',
    }),
    ogImageMedia: one(media, {
      fields: [vehicleTypes.ogImage],
      references: [media.id],
      relationName: 'vehicleTypeOgImage',
    }),

    // Asset category relation
    assetCategory: one(assetCategories, {
      fields: [vehicleTypes.assetCategoryId],
      references: [assetCategories.id],
    }),

    // One-to-many relations
    vehicleTypeLocations: many(vehicleTypeLocations),
  }),
);

export const vehicleTypeLocationsRelations = relations(
  vehicleTypeLocations,
  ({ one }) => ({
    // Audit relations
    creator: one(users, {
      fields: [vehicleTypeLocations.createdBy],
      references: [users.id],
      relationName: 'createdVehicleTypeLocations',
    }),
    updater: one(users, {
      fields: [vehicleTypeLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedVehicleTypeLocations',
    }),

    // Vehicle type relation
    vehicleType: one(vehicleTypes, {
      fields: [vehicleTypeLocations.vehicleTypeId],
      references: [vehicleTypes.id],
    }),

    // Location relation
    location: one(locations, {
      fields: [vehicleTypeLocations.locationId],
      references: [locations.id],
    }),
  }),
);
