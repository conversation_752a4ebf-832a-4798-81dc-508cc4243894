import {
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  integer,
  decimal,
  date,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { businessSubscriptions } from './business-subscriptions.schema';
import { users } from './users.schema';
import {
  auditFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

/**
 * Subscription Usage Tracking Schema
 *
 * This schema tracks business usage metrics against their subscription limits.
 * It monitors features like users, storage, projects, and other plan-specific limits.
 */

// Usage Metric Type Enum
export enum UsageMetricType {
  USERS = 'users',
  STORAGE = 'storage',
  PROJECTS = 'projects',
  TRANSACTIONS = 'transactions',
  API_CALLS = 'api_calls',
  BANDWIDTH = 'bandwidth',
  EMAILS = 'emails',
  SMS = 'sms',
  REPORTS = 'reports',
  INTEGRATIONS = 'integrations',
  CUSTOM = 'custom',
}

export const usageMetricTypeEnum = pgEnum('usage_metric_type', [
  UsageMetricType.USERS,
  UsageMetricType.STORAGE,
  UsageMetricType.PROJECTS,
  UsageMetricType.TRANSACTIONS,
  UsageMetricType.API_CALLS,
  UsageMetricType.BANDWIDTH,
  UsageMetricType.EMAILS,
  UsageMetricType.SMS,
  UsageMetricType.REPORTS,
  UsageMetricType.INTEGRATIONS,
  UsageMetricType.CUSTOM,
]);

// Usage Unit Enum
export enum UsageUnit {
  COUNT = 'count',
  BYTES = 'bytes',
  KILOBYTES = 'kilobytes',
  MEGABYTES = 'megabytes',
  GIGABYTES = 'gigabytes',
  TERABYTES = 'terabytes',
  HOURS = 'hours',
  MINUTES = 'minutes',
  REQUESTS = 'requests',
  PERCENTAGE = 'percentage',
}

export const usageUnitEnum = pgEnum('usage_unit', [
  UsageUnit.COUNT,
  UsageUnit.BYTES,
  UsageUnit.KILOBYTES,
  UsageUnit.MEGABYTES,
  UsageUnit.GIGABYTES,
  UsageUnit.TERABYTES,
  UsageUnit.HOURS,
  UsageUnit.MINUTES,
  UsageUnit.REQUESTS,
  UsageUnit.PERCENTAGE,
]);

// Usage Period Type Enum
export enum UsagePeriodType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  BILLING_CYCLE = 'billing_cycle',
  REAL_TIME = 'real_time',
}

export const usagePeriodTypeEnum = pgEnum('usage_period_type', [
  UsagePeriodType.DAILY,
  UsagePeriodType.WEEKLY,
  UsagePeriodType.MONTHLY,
  UsagePeriodType.QUARTERLY,
  UsagePeriodType.YEARLY,
  UsagePeriodType.BILLING_CYCLE,
  UsagePeriodType.REAL_TIME,
]);

// Main subscription usage tracking table
export const subscriptionUsageTracking = pgTable(
  'subscription_usage_tracking',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    subscriptionId: uuid('subscription_id')
      .notNull()
      .references(() => businessSubscriptions.id),

    // Usage metric details
    metricType: usageMetricTypeEnum('metric_type').notNull(),
    metricName: text('metric_name').notNull(), // Human-readable name
    metricDescription: text('metric_description'),

    // Usage limits and current usage
    usageLimit: integer('usage_limit'), // null means unlimited
    currentUsage: integer('current_usage').default(0).notNull(),
    usageUnit: usageUnitEnum('usage_unit').default(UsageUnit.COUNT).notNull(),

    // Period tracking
    periodType: usagePeriodTypeEnum('period_type')
      .default(UsagePeriodType.BILLING_CYCLE)
      .notNull(),
    periodStart: date('period_start').notNull(),
    periodEnd: date('period_end').notNull(),

    // Usage percentage and status
    usagePercentage: decimal('usage_percentage', {
      precision: 5,
      scale: 2,
    }).default('0.00'),
    isOverLimit: boolean('is_over_limit').default(false).notNull(),
    isNearLimit: boolean('is_near_limit').default(false).notNull(), // 80% threshold

    // Overage tracking
    overageUsage: integer('overage_usage').default(0).notNull(),
    overageChargePerUnit: decimal('overage_charge_per_unit', {
      precision: 15,
      scale: 2,
    }),
    overageChargeTotal: decimal('overage_charge_total', {
      precision: 15,
      scale: 2,
    }).default('0.00'),

    // Alert and notification settings
    alertThresholds: integer('alert_thresholds').array().default([]), // Array of percentage thresholds
    alertsSent: text('alerts_sent').array().default([]), // Array of alert timestamps
    lastAlertDate: date('last_alert_date'),

    // Usage reset information
    lastResetDate: date('last_reset_date'),
    nextResetDate: date('next_reset_date'),
    autoReset: boolean('auto_reset').default(true).notNull(),

    // Measurement details
    lastMeasuredAt: timestamp('last_measured_at').defaultNow().notNull(),
    measurementFrequency: text('measurement_frequency').default('hourly'), // hourly, daily, real-time

    // Additional metadata
    metadata: text('metadata'), // JSON string for metric-specific data
    tags: text('tags').array().default([]),
    isActive: boolean('is_active').default(true).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'subscription_usage_tracking'),
    subscriptionIdIndex: index(
      'subscription_usage_tracking_subscription_id_index',
    ).on(t.subscriptionId),

    // Metric and type indexes
    metricTypeIndex: index('subscription_usage_tracking_metric_type_index').on(
      t.metricType,
    ),
    metricNameIndex: index('subscription_usage_tracking_metric_name_index').on(
      t.metricName,
    ),
    usageUnitIndex: index('subscription_usage_tracking_usage_unit_index').on(
      t.usageUnit,
    ),
    periodTypeIndex: index('subscription_usage_tracking_period_type_index').on(
      t.periodType,
    ),

    // Usage status indexes
    isOverLimitIndex: index(
      'subscription_usage_tracking_is_over_limit_index',
    ).on(t.isOverLimit),
    isNearLimitIndex: index(
      'subscription_usage_tracking_is_near_limit_index',
    ).on(t.isNearLimit),
    isActiveIndex: index('subscription_usage_tracking_is_active_index').on(
      t.isActive,
    ),

    // Date-based indexes
    periodStartIndex: index(
      'subscription_usage_tracking_period_start_index',
    ).on(t.periodStart),
    periodEndIndex: index('subscription_usage_tracking_period_end_index').on(
      t.periodEnd,
    ),
    lastResetDateIndex: index(
      'subscription_usage_tracking_last_reset_date_index',
    ).on(t.lastResetDate),
    nextResetDateIndex: index(
      'subscription_usage_tracking_next_reset_date_index',
    ).on(t.nextResetDate),
    lastMeasuredAtIndex: index(
      'subscription_usage_tracking_last_measured_at_index',
    ).on(t.lastMeasuredAt),

    // Composite indexes for common queries
    businessMetricTypeIndex: index(
      'subscription_usage_tracking_business_metric_type_index',
    )
      .on(t.businessId, t.metricType)
      .where(sql`${t.isDeleted} = false`),
    subscriptionActiveMetricsIndex: index(
      'subscription_usage_tracking_subscription_active_index',
    )
      .on(t.subscriptionId, t.isActive)
      .where(sql`${t.isDeleted} = false`),
    overLimitMetricsIndex: index('subscription_usage_tracking_over_limit_index')
      .on(t.isOverLimit, t.metricType)
      .where(sql`${t.isDeleted} = false`),
    nearLimitMetricsIndex: index('subscription_usage_tracking_near_limit_index')
      .on(t.isNearLimit, t.metricType)
      .where(sql`${t.isDeleted} = false`),
    currentPeriodIndex: index(
      'subscription_usage_tracking_current_period_index',
    )
      .on(t.periodStart, t.periodEnd, t.isActive)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Usage history table for historical tracking and analytics
export const subscriptionUsageHistory = pgTable(
  'subscription_usage_history',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    usageTrackingId: uuid('usage_tracking_id')
      .notNull()
      .references(() => subscriptionUsageTracking.id),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Snapshot of usage at a point in time
    recordedAt: timestamp('recorded_at').defaultNow().notNull(),
    usageValue: integer('usage_value').notNull(),
    usageLimit: integer('usage_limit'),
    usagePercentage: decimal('usage_percentage', { precision: 5, scale: 2 }),

    // Change tracking
    previousUsage: integer('previous_usage'),
    usageChange: integer('usage_change'),
    changeReason: text('change_reason'), // reset, increment, decrement, manual_adjustment

    // Period information
    periodStart: date('period_start').notNull(),
    periodEnd: date('period_end').notNull(),

    // Additional context
    notes: text('notes'),
    triggeredBy: uuid('triggered_by').references(() => users.id),

    // Standard audit fields (simplified for history)
    createdAt: timestamp('created_at').defaultNow().notNull(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
  },
  (t) => ({
    // Primary indexes
    idIndex: index('subscription_usage_history_id_index').on(t.id),
    usageTrackingIdIndex: index(
      'subscription_usage_history_usage_tracking_id_index',
    ).on(t.usageTrackingId),
    businessIdIndex: index('subscription_usage_history_business_id_index').on(
      t.businessId,
    ),

    // Time-based indexes for analytics
    recordedAtIndex: index('subscription_usage_history_recorded_at_index').on(
      t.recordedAt,
    ),
    periodStartIndex: index('subscription_usage_history_period_start_index').on(
      t.periodStart,
    ),
    periodEndIndex: index('subscription_usage_history_period_end_index').on(
      t.periodEnd,
    ),
    triggeredByIndex: index('subscription_usage_history_triggered_by_index').on(
      t.triggeredBy,
    ),

    // Composite indexes for analytics queries
    usageTrackingTimeIndex: index(
      'subscription_usage_history_usage_tracking_time_index',
    ).on(t.usageTrackingId, t.recordedAt),
    businessTimeIndex: index(
      'subscription_usage_history_business_time_index',
    ).on(t.businessId, t.recordedAt),
    periodIndex: index('subscription_usage_history_period_index').on(
      t.periodStart,
      t.periodEnd,
      t.recordedAt,
    ),
  }),
);

// Subscription Usage Tracking Relations
export const subscriptionUsageTrackingRelations = relations(
  subscriptionUsageTracking,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [subscriptionUsageTracking.businessId],
      references: [business.id],
      relationName: 'businessUsageTracking',
    }),

    // Subscription relation
    subscription: one(businessSubscriptions, {
      fields: [subscriptionUsageTracking.subscriptionId],
      references: [businessSubscriptions.id],
      relationName: 'subscriptionUsageTracking',
    }),

    // Usage history relation
    usageHistory: many(subscriptionUsageHistory, {
      relationName: 'usageTrackingHistory',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionUsageTracking.createdBy],
      references: [users.id],
      relationName: 'createdUsageTracking',
    }),
    updater: one(users, {
      fields: [subscriptionUsageTracking.updatedBy],
      references: [users.id],
      relationName: 'updatedUsageTracking',
    }),
  }),
);

// Subscription Usage History Relations
export const subscriptionUsageHistoryRelations = relations(
  subscriptionUsageHistory,
  ({ one }) => ({
    // Usage tracking relation
    usageTracking: one(subscriptionUsageTracking, {
      fields: [subscriptionUsageHistory.usageTrackingId],
      references: [subscriptionUsageTracking.id],
      relationName: 'usageTrackingHistory',
    }),

    // Business relation
    business: one(business, {
      fields: [subscriptionUsageHistory.businessId],
      references: [business.id],
      relationName: 'businessUsageHistory',
    }),

    // Triggered by user relation
    triggeredByUser: one(users, {
      fields: [subscriptionUsageHistory.triggeredBy],
      references: [users.id],
      relationName: 'triggeredUsageHistory',
    }),

    // Creator relation
    creator: one(users, {
      fields: [subscriptionUsageHistory.createdBy],
      references: [users.id],
      relationName: 'createdUsageHistory',
    }),
  }),
);
