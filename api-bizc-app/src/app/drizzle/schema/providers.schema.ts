import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  boolean,
  uuid,
  timestamp,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { ProviderStatus, ProviderType, ProviderName } from '../../shared/types';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export const providerStatusEnum = pgEnum('provider_status', [
  ProviderStatus.ACTIVE,
  ProviderStatus.INACTIVE,
  ProviderStatus.DELETED,
]);

export const providerTypeEnum = pgEnum('provider_type', [
  ProviderType.EMAIL,
  ProviderType.SMS,
  ProviderType.WHATSAPP,
  ProviderType.AI,
  ProviderType.OTHER,
]);

export const providerNameEnum = pgEnum('provider_name', [
  ProviderName.RESEND,
  ProviderName.SENDGRID,
  ProviderName.MAILGUN,
  ProviderName.SES,
  ProviderName.BREVO,
  ProviderName.MAILCHIMP,
  ProviderName.POSTMARK,
  ProviderName.SPARKPOST,
  ProviderName.TWILIO,
  ProviderName.NEXMO,
  ProviderName.CLICKSEND,
  ProviderName.TEXTLOCAL,
  ProviderName.MSG91,
  ProviderName.FAST2SMS,
  ProviderName.WHATSAPP_BUSINESS_API,
  ProviderName.META_WHATSAPP,
  ProviderName.TWILIO_WHATSAPP,
  ProviderName.CHATAPI,
  ProviderName.GREEN_API,
]);

export const providers = pgTable(
  'providers',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    providerType: providerTypeEnum('provider_type').notNull(),
    providerName: providerNameEnum('provider_name').notNull(),
    displayName: text('display_name').notNull(),
    key1: text('key_1'),
    key2: text('key_2'),
    key3: text('key_3'),
    key4: text('key_4'),
    key5: text('key_5'),
    key6: text('key_6'),
    key7: text('key_7'),
    key8: text('key_8'),
    key9: text('key_9'),
    key10: text('key_10'),
    isDefault: boolean('is_default').default(false).notNull(),

    status: providerStatusEnum('status')
      .default(ProviderStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'providers'),
    providerTypeIndex: index('providers_provider_type_index').on(
      t.providerType,
    ),
    providerNameIndex: index('providers_provider_name_index').on(
      t.providerName,
    ),
    displayNameIndex: index('providers_display_name_index').on(t.displayName),
    isDefaultIndex: index('providers_is_default_index').on(t.isDefault),
    uniqueBusinessDisplayName: uniqueIndex(
      'providers_business_display_name_unique',
    ).on(t.businessId, t.displayName),
  }),
);

// Relations
export const providersRelations = relations(providers, ({ one }) => ({
  business: one(business, {
    fields: [providers.businessId],
    references: [business.id],
  }),
  createdByUser: one(users, {
    fields: [providers.createdBy],
    references: [users.id],
  }),
  updatedByUser: one(users, {
    fields: [providers.updatedBy],
    references: [users.id],
  }),
}));
