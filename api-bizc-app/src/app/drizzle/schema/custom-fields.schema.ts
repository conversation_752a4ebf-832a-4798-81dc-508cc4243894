import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  boolean,
  jsonb,
  uuid,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { users } from './users.schema';
import {
  CustomFieldType,
  CustomFieldEntityType,
} from '../../shared/types/custom-field.enum';

export const customFieldTypeEnum = pgEnum('custom_field_type', [
  CustomFieldType.TEXT,
  CustomFieldType.NUMBER,
  CustomFieldType.DATE,
  CustomFieldType.EMAIL,
  CustomFieldType.PHONE,
  CustomFieldType.URL,
  CustomFieldType.SELECT,
  CustomFieldType.MULTI_SELECT,
  CustomFieldType.CHECKBOX,
  CustomFieldType.RADIO,
  CustomFieldType.TEXTAREA,
]);

export const customFieldEntityTypeEnum = pgEnum('custom_field_entity_type', [
  CustomFieldEntityType.CUSTOMERS,
  CustomFieldEntityType.EXPENSES,
  CustomFieldEntityType.ITEMS,
  CustomFieldEntityType.CAMPAIGNS,
  CustomFieldEntityType.BOOKINGS,
  CustomFieldEntityType.RENTALS,
  CustomFieldEntityType.ROOM_BOOKINGS,
  CustomFieldEntityType.SMS_NEWSLETTERS,
]);

export const customFields = pgTable(
  'custom_fields',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    type: customFieldTypeEnum('type').notNull(),
    entityType: customFieldEntityTypeEnum('entity_type').notNull(),
    required: boolean('required').default(false).notNull(),
    placeholder: text('placeholder'),
    options: jsonb('options').$type<string[]>(), // Array of strings for select/multi-select/radio
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'custom_fields'),
    // Entity-specific indexes
    entityTypeIndex: index('custom_fields_entity_type_index').on(t.entityType),
    nameIndex: index('custom_fields_name_index').on(t.name),
    typeIndex: index('custom_fields_type_index').on(t.type),
    requiredIndex: index('custom_fields_required_index').on(t.required),

    // Composite indexes for performance
    businessEntityTypeIndex: index('custom_fields_business_entity_type_index')
      .on(t.businessId, t.entityType)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('custom_fields_business_type_index')
      .on(t.businessId, t.type)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessEntityName: uniqueIndex(
      'custom_fields_business_entity_name_unique',
    )
      .on(t.businessId, t.entityType, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Custom Fields Relations
export const customFieldsRelations = relations(customFields, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [customFields.businessId],
    references: [business.id],
    relationName: 'businessCustomFields',
  }),

  // Audit relations
  creator: one(users, {
    fields: [customFields.createdBy],
    references: [users.id],
    relationName: 'createdCustomFields',
  }),
  updater: one(users, {
    fields: [customFields.updatedBy],
    references: [users.id],
    relationName: 'updatedCustomFields',
  }),
}));
