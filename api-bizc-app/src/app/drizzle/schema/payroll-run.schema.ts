import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  integer,
  date,
  decimal,
  uuid,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
// PayrollRunType TypeScript Enum
export enum PayrollRunType {
  REGULAR = 'regular',
  BONUS = 'bonus',
  CORRECTION = 'correction',
  SPECIAL = 'special',
  FINAL = 'final',
}

// PayrollRunStatus TypeScript Enum
export enum PayrollRunStatus {
  DRAFT = 'draft',
  PROCESSING = 'processing',
  CALCULATED = 'calculated',
  APPROVED = 'approved',
  POSTED = 'posted',
  CANCELLED = 'cancelled',
}

// Payroll Run Type Enum
export const payrollRunTypeEnum = pgEnum('payroll_run_type', [
  PayrollRunType.REGULAR,
  PayrollRunType.BONUS,
  PayrollRunType.CORRECTION,
  PayrollRunType.SPECIAL,
  PayrollRunType.FINAL,
]);

// Payroll Run Status Enum
export const payrollRunStatusEnum = pgEnum('payroll_run_status', [
  PayrollRunStatus.DRAFT,
  PayrollRunStatus.PROCESSING,
  PayrollRunStatus.CALCULATED,
  PayrollRunStatus.APPROVED,
  PayrollRunStatus.POSTED,
  PayrollRunStatus.CANCELLED,
]);

// Payroll Runs table
export const payrollRuns = pgTable(
  'payroll_runs',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Unique run identifier
    runCode: text('run_code').notNull(),

    // Run configuration
    runType: payrollRunTypeEnum('run_type')
      .default(PayrollRunType.REGULAR)
      .notNull(),
    payrollMonth: text('payroll_month').notNull(), // e.g., "January", "February"
    payrollYear: integer('payroll_year').notNull(),

    // Pay period information
    payPeriodStart: date('pay_period_start').notNull(),
    payPeriodEnd: date('pay_period_end').notNull(),
    paymentDate: date('payment_date').notNull(),

    // Summary statistics
    totalEmployees: integer('total_employees').default(0).notNull(),
    totalGrossPay: decimal('total_gross_pay', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalDeductions: decimal('total_deductions', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),
    totalNetPay: decimal('total_net_pay', { precision: 15, scale: 2 })
      .default('0.00')
      .notNull(),

    // Status and workflow
    runStatus: payrollRunStatusEnum('run_status')
      .default(PayrollRunStatus.DRAFT)
      .notNull(),

    // Additional information
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'payroll_runs'),

    // Core indexes for performance
    runCodeIndex: index('payroll_runs_run_code_index').on(t.runCode),
    runTypeIndex: index('payroll_runs_run_type_index').on(t.runType),
    payrollYearIndex: index('payroll_runs_payroll_year_index').on(
      t.payrollYear,
    ),
    payrollMonthIndex: index('payroll_runs_payroll_month_index').on(
      t.payrollMonth,
    ),
    paymentDateIndex: index('payroll_runs_payment_date_index').on(
      t.paymentDate,
    ),
    runStatusIndex: index('payroll_runs_run_status_index').on(t.runStatus),

    // Pay period indexes
    payPeriodStartIndex: index('payroll_runs_pay_period_start_index').on(
      t.payPeriodStart,
    ),
    payPeriodEndIndex: index('payroll_runs_pay_period_end_index').on(
      t.payPeriodEnd,
    ),

    // Composite indexes for common queries
    businessYearMonthIndex: index('payroll_runs_business_year_month_index')
      .on(t.businessId, t.payrollYear, t.payrollMonth)
      .where(sql`${t.isDeleted} = false`),

    businessStatusIndex: index('payroll_runs_business_status_index')
      .on(t.businessId, t.runStatus)
      .where(sql`${t.isDeleted} = false`),

    businessTypeYearIndex: index('payroll_runs_business_type_year_index')
      .on(t.businessId, t.runType, t.payrollYear)
      .where(sql`${t.isDeleted} = false`),

    payPeriodRangeIndex: index('payroll_runs_pay_period_range_index')
      .on(t.payPeriodStart, t.payPeriodEnd)
      .where(sql`${t.isDeleted} = false`),

    businessPaymentDateIndex: index('payroll_runs_business_payment_date_index')
      .on(t.businessId, t.paymentDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueBusinessRunCode: uniqueIndex('payroll_runs_business_run_code_unique')
      .on(t.businessId, t.runCode)
      .where(sql`${t.isDeleted} = false`),

    uniqueBusinessPeriod: uniqueIndex('payroll_runs_business_period_unique')
      .on(
        t.businessId,
        t.runType,
        t.payrollYear,
        t.payrollMonth,
        t.payPeriodStart,
        t.payPeriodEnd,
      )
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const payrollRunsRelations = relations(payrollRuns, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [payrollRuns.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [payrollRuns.createdBy],
    references: [users.id],
    relationName: 'createdPayrollRuns',
  }),
  updater: one(users, {
    fields: [payrollRuns.updatedBy],
    references: [users.id],
    relationName: 'updatedPayrollRuns',
  }),
}));
