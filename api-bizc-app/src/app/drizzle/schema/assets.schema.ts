import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  decimal,
  date,
  uniqueIndex,
  integer,
  boolean,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { locations } from './locations.schema';
import { suppliers } from './suppliers.schema';
import { accounts } from './accounts.schema';
import { assetCategories } from './asset-categories.schema';
import { media } from './media.schema';
import {
  locationAllocationFields,
  auditFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
// TypeScript enum for Asset Type
export enum AssetType {
  IT_EQUIPMENT = 'it_equipment',
  FURNITURE = 'furniture',
  VEHICLE = 'vehicle',
  MACHINERY = 'machinery',
  BUILDING = 'building',
  OFFICE_EQUIPMENT = 'office_equipment',
  SOFTWARE = 'software',
  OTHER = 'other',
}

// TypeScript enum for Maintenance Frequency
export enum MaintenanceFrequency {
  DAYS = 'days',
  WEEKS = 'weeks',
  MONTHS = 'months',
  YEARS = 'years',
}

// TypeScript enum for Asset Reference
export enum AssetReference {
  VEHICLE = 'vehicle',
  ACCOMMODATION = 'accommodation',
  EVENT_SPACE = 'event_space',
  RENTAL_ITEM = 'rental_item',
}

// TypeScript enum for Asset Status - for type safety and easier usage
export enum AssetStatus {
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  ASSIGNED = 'assigned',
  DISCARDED = 'discarded',
  LOST = 'lost',
  STOLEN = 'stolen',
  DAMAGED = 'damaged',
  MAINTENANCE = 'maintenance',
  ALLOCATED = 'allocated',
}

// Asset Type Enum
export const assetTypeEnum = pgEnum('asset_type', [
  AssetType.IT_EQUIPMENT,
  AssetType.FURNITURE,
  AssetType.VEHICLE,
  AssetType.MACHINERY,
  AssetType.BUILDING,
  AssetType.OFFICE_EQUIPMENT,
  AssetType.SOFTWARE,
  AssetType.OTHER,
]);

// Maintenance Frequency Enum
export const maintenanceFrequencyEnum = pgEnum('maintenance_frequency', [
  MaintenanceFrequency.DAYS,
  MaintenanceFrequency.WEEKS,
  MaintenanceFrequency.MONTHS,
  MaintenanceFrequency.YEARS,
]);

// Asset Reference Enum
export const assetReferenceEnum = pgEnum('asset_reference', [
  AssetReference.VEHICLE,
  AssetReference.ACCOMMODATION,
  AssetReference.EVENT_SPACE,
  AssetReference.RENTAL_ITEM,
]);

// Asset Status Enum - matching the database migration values
export const assetStatusEnum = pgEnum('asset_status', [
  AssetStatus.AVAILABLE,
  AssetStatus.UNAVAILABLE,
  AssetStatus.ASSIGNED,
  AssetStatus.DISCARDED,
  AssetStatus.LOST,
  AssetStatus.STOLEN,
  AssetStatus.DAMAGED,
  AssetStatus.MAINTENANCE,
  AssetStatus.ALLOCATED,
]);

// Helper to get all asset type values as an array for validation
export const ASSET_TYPE_VALUES = Object.values(AssetType) as string[];

// Helper to get all maintenance frequency values as an array for validation
export const MAINTENANCE_FREQUENCY_VALUES = Object.values(
  MaintenanceFrequency,
) as string[];

// Helper to get all asset reference values as an array for validation
export const ASSET_REFERENCE_VALUES = Object.values(AssetReference) as string[];

// Helper to get all asset status values as an array for validation
export const ASSET_STATUS_VALUES = Object.values(AssetStatus) as string[];

export const assets = pgTable(
  'assets',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    assetCode: text('asset_code').notNull(),
    name: text('name').notNull(),
    type: assetTypeEnum('type').default(AssetType.OTHER).notNull(),

    // Supplier Details
    supplierId: uuid('supplier_id').references(() => suppliers.id),

    purchaseDate: date('purchase_date'),
    purchasePrice: decimal('purchase_price', { precision: 12, scale: 2 }),
    purchaseOrderNumber: text('purchase_order_number'),

    // Maintenance Details
    maintenanceFrequencyValue: integer('maintenance_frequency_value'), // Numeric value (e.g., 1, 2, 6)
    maintenanceFrequency: maintenanceFrequencyEnum('maintenance_frequency'), // Time unit (days, weeks, months, years)
    lastMaintenanceDate: date('last_maintenance_date'), // Last maintenance date
    maintenanceDueDate: date('maintenance_due_date'), // Next scheduled maintenance date
    warrantyExpiryDate: date('warranty_expiry_date'), // When warranty expires
    warrantyPeriod: text('warranty_period'), // Warranty period (e.g., "1 year", "2 years")
    lifecycleExpiryDate: date('lifecycle_expiry_date'), // Expected end of useful life

    // Dynamic fields based on asset type
    key1: text('key1'),
    key2: text('key2'),
    key3: text('key3'),
    key4: text('key4'),
    key5: text('key5'),
    key6: text('key6'),
    key7: text('key7'),
    key8: text('key8'),
    key9: text('key9'),
    key10: text('key10'),

    categoryId: uuid('category_id').references(() => assetCategories.id),
    subCategoryId: uuid('sub_category_id').references(() => assetCategories.id),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    bookValue: decimal('book_value', { precision: 12, scale: 2 }),

    // Accounting Accounts
    fixedAssetAccountId: uuid('fixed_asset_account_id').references(
      () => accounts.id,
    ),
    depreciationAccountId: uuid('depreciation_account_id').references(
      () => accounts.id,
    ),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    description: text('description'),
    notes: text('notes'),

    // Reference fields
    reference: assetReferenceEnum('reference'),
    referenceId: uuid('reference_id'),

    status: assetStatusEnum('status').default(AssetStatus.AVAILABLE).notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'assets'),

    // Entity-specific indexes
    idIndex: index('assets_id_index').on(t.id),
    assetCodeIndex: index('assets_asset_code_index').on(t.assetCode),
    categoryIdIndex: index('assets_category_id_index').on(t.categoryId),
    subCategoryIdIndex: index('assets_sub_category_id_index').on(
      t.subCategoryId,
    ),
    typeIndex: index('assets_type_index').on(t.type),
    statusIndex: index('assets_status_index').on(t.status),
    referenceIndex: index('assets_reference_index').on(t.reference),
    referenceIdIndex: index('assets_reference_id_index').on(t.referenceId),

    supplierIdIndex: index('assets_supplier_id_index').on(t.supplierId),
    maintenanceFrequencyIndex: index('assets_maintenance_frequency_index').on(
      t.maintenanceFrequency,
    ),
    lastMaintenanceDateIndex: index('assets_last_maintenance_date_index').on(
      t.lastMaintenanceDate,
    ),
    maintenanceDueDateIndex: index('assets_maintenance_due_date_index').on(
      t.maintenanceDueDate,
    ),
    warrantyExpiryDateIndex: index('assets_warranty_expiry_date_index').on(
      t.warrantyExpiryDate,
    ),
    lifecycleExpiryDateIndex: index('assets_lifecycle_expiry_date_index').on(
      t.lifecycleExpiryDate,
    ),

    fixedAssetAccountIdIndex: index('assets_fixed_asset_account_id_index').on(
      t.fixedAssetAccountId,
    ),
    depreciationAccountIdIndex: index(
      'assets_depreciation_account_id_index',
    ).on(t.depreciationAccountId),
    expenseAccountIdIndex: index('assets_expense_account_id_index').on(
      t.expenseAccountId,
    ),

    // Optimized indexes for filtering and searching with soft deletion support
    businessNameIndex: index('assets_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessAssetCodeIndex: index('assets_business_asset_code_index')
      .on(t.businessId, t.assetCode)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('assets_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessCategoryIndex: index('assets_business_category_index')
      .on(t.businessId, t.categoryId)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('assets_business_type_index')
      .on(t.businessId, t.type)
      .where(sql`${t.isDeleted} = false`),
    businessReferenceIndex: index('assets_business_reference_index')
      .on(t.businessId, t.reference)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessAssetCode: uniqueIndex('assets_business_asset_code_unique')
      .on(t.businessId, t.assetCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for asset locations (many-to-many relationship)
export const assetLocations = pgTable(
  'asset_locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    // Standard audit fields (using reusable fields)
    ...auditFields,
  },
  (t) => ({
    assetIdIndex: index('asset_locations_asset_id_index').on(t.assetId),
    locationIdIndex: index('asset_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueAssetLocation: uniqueIndex('asset_locations_unique').on(
      t.assetId,
      t.locationId,
    ),
  }),
);

// Asset Images table (many-to-many with sort order)
export const assetImages = pgTable(
  'asset_images',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id, { onDelete: 'cascade' }),
    imageId: uuid('image_id')
      .notNull()
      .references(() => media.id, { onDelete: 'cascade' }),
    sortOrder: integer('sort_order').default(0).notNull(),
    isPrimary: boolean('is_primary').default(false).notNull(),
    isActive: boolean('is_active').default(true).notNull(),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    assetIdIndex: index('asset_images_asset_id_index').on(t.assetId),
    imageIdIndex: index('asset_images_image_id_index').on(t.imageId),
    sortOrderIndex: index('asset_images_sort_order_index').on(t.sortOrder),
    isPrimaryIndex: index('asset_images_is_primary_index').on(t.isPrimary),
    isActiveIndex: index('asset_images_is_active_index').on(t.isActive),

    // Composite indexes for performance
    assetSortIndex: index('asset_images_asset_sort_index').on(
      t.assetId,
      t.sortOrder,
      t.id,
    ),

    // Unique constraints
    uniqueAssetImage: uniqueIndex('asset_images_asset_image_unique').on(
      t.assetId,
      t.imageId,
    ),
  }),
);

// Relations
export const assetsRelations = relations(assets, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [assets.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [assets.createdBy],
    references: [users.id],
    relationName: 'createdAssets',
  }),
  updater: one(users, {
    fields: [assets.updatedBy],
    references: [users.id],
    relationName: 'updatedAssets',
  }),

  // Supplier relation
  supplier: one(suppliers, {
    fields: [assets.supplierId],
    references: [suppliers.id],
  }),

  // Category relations
  category: one(assetCategories, {
    fields: [assets.categoryId],
    references: [assetCategories.id],
    relationName: 'assetCategory',
  }),
  subCategory: one(assetCategories, {
    fields: [assets.subCategoryId],
    references: [assetCategories.id],
    relationName: 'assetSubCategory',
  }),

  // Account relations
  fixedAssetAccount: one(accounts, {
    fields: [assets.fixedAssetAccountId],
    references: [accounts.id],
    relationName: 'assetFixedAssetAccount',
  }),
  depreciationAccount: one(accounts, {
    fields: [assets.depreciationAccountId],
    references: [accounts.id],
    relationName: 'assetDepreciationAccount',
  }),
  expenseAccount: one(accounts, {
    fields: [assets.expenseAccountId],
    references: [accounts.id],
    relationName: 'assetExpenseAccount',
  }),

  // One-to-many relations
  assetLocations: many(assetLocations),
  assetImages: many(assetImages),
}));

export const assetLocationsRelations = relations(assetLocations, ({ one }) => ({
  // Audit relations
  creator: one(users, {
    fields: [assetLocations.createdBy],
    references: [users.id],
    relationName: 'createdAssetLocations',
  }),
  updater: one(users, {
    fields: [assetLocations.updatedBy],
    references: [users.id],
    relationName: 'updatedAssetLocations',
  }),

  // Asset and location relations
  asset: one(assets, {
    fields: [assetLocations.assetId],
    references: [assets.id],
  }),
  location: one(locations, {
    fields: [assetLocations.locationId],
    references: [locations.id],
  }),
}));

export const assetImagesRelations = relations(assetImages, ({ one }) => ({
  // Audit relations
  creator: one(users, {
    fields: [assetImages.createdBy],
    references: [users.id],
    relationName: 'createdAssetImages',
  }),
  updater: one(users, {
    fields: [assetImages.updatedBy],
    references: [users.id],
    relationName: 'updatedAssetImages',
  }),

  // Asset and image relations
  asset: one(assets, {
    fields: [assetImages.assetId],
    references: [assets.id],
  }),
  image: one(media, {
    fields: [assetImages.imageId],
    references: [media.id],
  }),
}));
