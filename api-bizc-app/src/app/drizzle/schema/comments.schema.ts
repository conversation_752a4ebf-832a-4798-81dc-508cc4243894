import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { StatusType } from '../../shared/types';

export const commentStatusEnum = pgEnum('comment_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const comments = pgTable(
  'comments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Reference to any entity this comment belongs to
    referenceId: uuid('reference_id').notNull(),

    // Self-referencing foreign key for comment replies
    parentId: uuid('parent_id').references(() => comments.id),

    // Comment content
    content: text('content').notNull(),

    // Optional attachment
    attachmentId: uuid('attachment_id').references(() => media.id),

    // Author reference to staff member
    authorId: uuid('author_id')
      .notNull()
      .references(() => staffMembers.id),

    // Status field
    status: commentStatusEnum('status').default(StatusType.ACTIVE).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'comments'),
    referenceIdIndex: index('comments_reference_id_index').on(t.referenceId),
    parentIdIndex: index('comments_parent_id_index').on(t.parentId),
    authorIdIndex: index('comments_author_id_index').on(t.authorId),
    attachmentIdIndex: index('comments_attachment_id_index').on(t.attachmentId),
    statusIndex: index('comments_status_index').on(t.status),

    // Composite indexes for common queries
    businessReferenceIndex: index('comments_business_reference_index')
      .on(t.businessId, t.referenceId)
      .where(sql`${t.isDeleted} = false`),
    businessAuthorIndex: index('comments_business_author_index')
      .on(t.businessId, t.authorId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('comments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Comments Relations
export const commentsRelations = relations(comments, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [comments.businessId],
    references: [business.id],
    relationName: 'businessComments',
  }),

  // Author relation
  author: one(staffMembers, {
    fields: [comments.authorId],
    references: [staffMembers.id],
    relationName: 'staffComments',
  }),

  // Attachment relation
  attachment: one(media, {
    fields: [comments.attachmentId],
    references: [media.id],
    relationName: 'commentAttachment',
  }),

  // Self-referencing relations for comment threads
  parentComment: one(comments, {
    fields: [comments.parentId],
    references: [comments.id],
    relationName: 'parentComment',
  }),
  childComments: many(comments, {
    relationName: 'parentComment',
  }),

  // Audit relations
  creator: one(users, {
    fields: [comments.createdBy],
    references: [users.id],
    relationName: 'createdComments',
  }),
  updater: one(users, {
    fields: [comments.updatedBy],
    references: [users.id],
    relationName: 'updatedComments',
  }),
}));
