import {
  boolean,
  check,
  decimal,
  index,
  pgTable,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql, gt, relations } from 'drizzle-orm';
import { units } from './units.schema';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
export const unitConversions = pgTable(
  'unit_conversions',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    fromUnitId: uuid('from_unit_id')
      .notNull()
      .references(() => units.id, { onDelete: 'cascade' }),
    toUnitId: uuid('to_unit_id')
      .notNull()
      .references(() => units.id, { onDelete: 'cascade' }),
    conversionFactor: decimal('conversion_factor', {
      precision: 18,
      scale: 6,
    }).notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'unit_conversions'),
    fromUnitIdIndex: index('unit_conversions_from_unit_id_index').on(
      t.fromUnitId,
    ),
    toUnitIdIndex: index('unit_conversions_to_unit_id_index').on(t.toUnitId),
    conversionFactorIndex: index('unit_conversions_conversion_factor_index').on(
      t.conversionFactor,
    ),

    // Optimized composite indexes for conversion lookups
    fromToUnitsIndex: index('unit_conversions_from_to_units_index')
      .on(t.fromUnitId, t.toUnitId)
      .where(sql`${t.isDeleted} = false`),
    toFromUnitsIndex: index('unit_conversions_to_from_units_index')
      .on(t.toUnitId, t.fromUnitId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate conversions
    uniqueFromToUnits: uniqueIndex('unit_conversions_from_to_unique')
      .on(t.fromUnitId, t.toUnitId)
      .where(sql`${t.isDeleted} = false`),

    // Check constraint to ensure conversion factor is positive
    positiveConversionFactor: check(
      'unit_conversions_positive_conversion_factor',
      sql`${t.conversionFactor} > 0`,
    ),
  }),
);

// Relations
export const unitConversionsRelations = relations(
  unitConversions,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [unitConversions.businessId],
      references: [business.id],
      relationName: 'businessUnitConversions',
    }),

    // From unit relation
    fromUnit: one(units, {
      fields: [unitConversions.fromUnitId],
      references: [units.id],
      relationName: 'fromUnitConversions',
    }),

    // To unit relation
    toUnit: one(units, {
      fields: [unitConversions.toUnitId],
      references: [units.id],
      relationName: 'toUnitConversions',
    }),

    // Audit relations
    creator: one(users, {
      fields: [unitConversions.createdBy],
      references: [users.id],
      relationName: 'createdUnitConversions',
    }),
    updater: one(users, {
      fields: [unitConversions.updatedBy],
      references: [users.id],
      relationName: 'updatedUnitConversions',
    }),
  }),
);
