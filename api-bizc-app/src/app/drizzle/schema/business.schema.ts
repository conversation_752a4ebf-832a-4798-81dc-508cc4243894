import { index, pgEnum, pgTable, text, uuid, jsonb } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { auditFields } from './common-fields.schema';
import { addresses } from './address.schema';
import { media } from './media.schema';
import {
  BusinessStatus,
  BusinessType,
  StandardUnitOfMeasure,
  StandardPaymentMethod,
} from '../../shared/types';

// Define pgEnum types using the shared enum string values
export const businessStatusEnum = pgEnum('business_status', [
  BusinessStatus.ACTIVE,
  BusinessStatus.INACTIVE,
  BusinessStatus.SUSPENDED,
  BusinessStatus.PENDING_APPROVAL,
]);

export const businessTypeEnum = pgEnum('business_type', [
  BusinessType.OTHER,
  BusinessType.RETAIL,
  BusinessType.RESTAURANT,
  BusinessType.SERVICE_PROVIDER,
]);

export const business = pgTable(
  'business',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    ownerId: uuid('owner_id')
      .notNull()
      .references(() => users.id),
    name: text('name').notNull(),
    email: text('email'),
    phone: text('phone'),
    website: text('website'),
    registrationNumber: text('registration_number'),
    addressId: uuid('address_id').references(() => addresses.id),
    logo: uuid('logo').references(() => media.id),
    businessType: businessTypeEnum('business_type')
      .default(BusinessType.OTHER)
      .notNull(),
    status: businessStatusEnum('status')
      .default(BusinessStatus.PENDING_APPROVAL)
      .notNull(),
    themeSettings: jsonb('theme_settings').$type<{
      primaryColor?: string;
      [key: string]: any;
    }>(),
    standardUnitsOfMeasure: jsonb('standard_units_of_measure')
      .$type<StandardUnitOfMeasure[]>()
      .default([]),
    standardPaymentMethods: jsonb('standard_payment_methods')
      .$type<StandardPaymentMethod[]>()
      .default([]),

    // Audit fields
    ...auditFields,
  },
  (t) => ({
    idIndex: index('business_id_index').on(t.id),
    ownerIdIndex: index('business_owner_id_index').on(t.ownerId),
    nameIndex: index('business_name_index').on(t.name),
  }),
);

// Relations definitions

// Business Relations
export const businessRelations = relations(business, ({ one }) => ({
  // One-to-one and many-to-one relations
  owner: one(users, {
    fields: [business.ownerId],
    references: [users.id],
  }),
  address: one(addresses, {
    fields: [business.addressId],
    references: [addresses.id],
  }),
  logoMedia: one(media, {
    fields: [business.logo],
    references: [media.id],
  }),
  creator: one(users, {
    fields: [business.createdBy],
    references: [users.id],
    relationName: 'createdBusinesses',
  }),
  updater: one(users, {
    fields: [business.updatedBy],
    references: [users.id],
    relationName: 'updatedBusinesses',
  }),
}));
