import {
  boolean,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { promoCodes } from './promo-codes.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { ReferralStatus } from '../../shared/types';

export const referralStatusEnum = pgEnum('referral_status', [
  ReferralStatus.ACTIVE,
  ReferralStatus.INACTIVE,
]);

export const referrals = pgTable(
  'referrals',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    title: text('title').notNull(),
    description: text('description').notNull(),
    link: text('link'),
    promoCode: text('promo_code').notNull(),
    promoCodeId: uuid('promo_code_id')
      .notNull()
      .references(() => promoCodes.id),
    linkClicks: integer('link_clicks').default(0).notNull(),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
    status: referralStatusEnum('status')
      .default(ReferralStatus.ACTIVE)
      .notNull(),
  },
  (table) => ({
    // Unique constraint for title per business
    titleBusinessIdx: uniqueIndex('referrals_title_business_idx')
      .on(table.title, table.businessId)
      .where(sql`${table.isDeleted} = false`),
    ...createBaseEntityBusinessIndexes(table, 'referrals'),
    // Index for staff queries
    staffIdx: index('referrals_staff_idx').on(table.staffId),
    // Index for promo code queries
    promoCodeIdx: index('referrals_promo_code_idx').on(table.promoCodeId),
    // Index for status queries
    statusIdx: index('referrals_status_idx').on(table.status),
    // Composite index for business and status (common query pattern)
    businessStatusIdx: index('referrals_business_status_idx').on(
      table.businessId,
      table.status,
    ),
  }),
);

// Relations
export const referralsRelations = relations(referrals, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [referrals.businessId],
    references: [business.id],
  }),
  // Promo code relation
  promoCodeReference: one(promoCodes, {
    fields: [referrals.promoCodeId],
    references: [promoCodes.id],
  }),
  // Staff member relation
  staff: one(staffMembers, {
    fields: [referrals.staffId],
    references: [staffMembers.id],
  }),
  // Audit relations
  creator: one(users, {
    fields: [referrals.createdBy],
    references: [users.id],
  }),
  updater: one(users, {
    fields: [referrals.updatedBy],
    references: [users.id],
  }),
}));

// TypeScript types for the referrals table
export type Referral = typeof referrals.$inferSelect;
export type NewReferral = typeof referrals.$inferInsert;
