import {
  boolean,
  date,
  index,
  integer,
  numeric,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { addresses } from './address.schema';
import { bankAccounts } from './bank-accounts.schema';
import { createBaseEntityFields } from './common-fields.schema';

import { users } from './users.schema';
export enum SupplierStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  SUSPENDED = 'SUSPENDED',
  BLOCKED = 'BLOCKED',
  TERMINATED = 'TERMINATED',
}

export const supplierStatusEnum = pgEnum('supplier_status', [
  SupplierStatus.ACTIVE,
  SupplierStatus.INACTIVE,
  SupplierStatus.PENDING_APPROVAL,
  SupplierStatus.SUSPENDED,
  SupplierStatus.BLOCKED,
  SupplierStatus.TERMINATED,
]);

export const suppliers = pgTable(
  'suppliers',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Personal Information
    title: text('title'), // Mr, Mrs, Ms, Dr, etc.
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    companyName: text('company_name').notNull(),
    displayName: text('display_name').notNull(),

    // Contact Information
    email: text('email').notNull(),
    website: text('website'),
    fax: text('fax'),
    phoneNumber: text('phone_number').notNull(),
    mobileNumber: text('mobile_number'),

    // Financial Information
    openingBalance: numeric('opening_balance', {
      precision: 15,
      scale: 2,
    }),
    openingBalanceDate: date('opening_balance_date'),
    netDays: integer('net_days').notNull(),
    creditLimit: numeric('credit_limit', {
      precision: 15,
      scale: 2,
    }),
    taxNumber: text('tax_number'),

    // Additional Information
    notes: text('notes'),

    // Position field for sorting (following categories pattern)
    position: integer('position').default(0).notNull(),

    // Media References
    attachments: uuid('attachments')
      .array()
      .default(sql`'{}'`),
    profileImage: uuid('profile_image').references(() => media.id),

    // Address and Bank Details (direct foreign key relationships)
    addressId: uuid('address_id').references(() => addresses.id),
    bankAccountId: uuid('bank_account_id').references(() => bankAccounts.id),

    // Status
    status: supplierStatusEnum('status')
      .default(SupplierStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('suppliers_id_index').on(t.id),
    businessIdIndex: index('suppliers_business_id_index').on(t.businessId),
    displayNameIndex: index('suppliers_display_name_index').on(t.displayName),
    companyNameIndex: index('suppliers_company_name_index').on(t.companyName),
    emailIndex: index('suppliers_email_index').on(t.email),
    phoneNumberIndex: index('suppliers_phone_number_index').on(t.phoneNumber),
    mobileNumberIndex: index('suppliers_mobile_number_index').on(
      t.mobileNumber,
    ),
    statusIndex: index('suppliers_status_index').on(t.status),
    profileImageIndex: index('suppliers_profile_image_index').on(
      t.profileImage,
    ),
    addressIdIndex: index('suppliers_address_id_index').on(t.addressId),
    bankAccountIdIndex: index('suppliers_bank_account_id_index').on(
      t.bankAccountId,
    ),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('suppliers_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'suppliers_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessDisplayNameIndex: index('suppliers_business_display_name_index')
      .on(t.businessId, t.displayName)
      .where(sql`${t.isDeleted} = false`),
    businessCompanyNameIndex: index('suppliers_business_company_name_index')
      .on(t.businessId, t.companyName)
      .where(sql`${t.isDeleted} = false`),
    businessEmailIndex: index('suppliers_business_email_index')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('suppliers_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessDisplayName: uniqueIndex(
      'suppliers_business_display_name_unique',
    )
      .on(t.businessId, t.displayName)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessEmail: uniqueIndex('suppliers_business_email_unique')
      .on(t.businessId, t.email)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const suppliersRelations = relations(suppliers, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [suppliers.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [suppliers.createdBy],
    references: [users.id],
    relationName: 'createdSuppliers',
  }),
  updater: one(users, {
    fields: [suppliers.updatedBy],
    references: [users.id],
    relationName: 'updatedSuppliers',
  }),

  // Media relations
  profileImageMedia: one(media, {
    fields: [suppliers.profileImage],
    references: [media.id],
    relationName: 'supplierProfileImage',
  }),

  // Address relation
  address: one(addresses, {
    fields: [suppliers.addressId],
    references: [addresses.id],
  }),

  // Bank account relation
  bankAccount: one(bankAccounts, {
    fields: [suppliers.bankAccountId],
    references: [bankAccounts.id],
  }),
}));
