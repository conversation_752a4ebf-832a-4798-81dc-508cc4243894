import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { StatusType } from '../../shared/types';
import { createBaseEntityFields } from './common-fields.schema';

import { users } from './users.schema';
export const paymentAccountTypeStatusEnum = pgEnum(
  'payment_account_type_status',
  [StatusType.ACTIVE, StatusType.INACTIVE],
);

export const paymentAccountTypes = pgTable(
  'payment_account_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    parentAccountTypeId: uuid('parent_account_type_id').references(
      () => paymentAccountTypes.id,
    ),
    status: paymentAccountTypeStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('payment_account_types_id_index').on(t.id),
    businessIdIndex: index('payment_account_types_business_id_index').on(
      t.businessId,
    ),
    nameIndex: index('payment_account_types_name_index').on(t.name),
    parentAccountTypeIdIndex: index('payment_account_types_parent_id_index').on(
      t.parentAccountTypeId,
    ),
    uniqueBusinessName: uniqueIndex(
      'payment_account_types_business_name_unique',
    ).on(t.businessId, t.name),
  }),
);

// Relations
export const paymentAccountTypesRelations = relations(
  paymentAccountTypes,
  ({ one, many }) => ({
    business: one(business, {
      fields: [paymentAccountTypes.businessId],
      references: [business.id],
    }),
    parentAccountType: one(paymentAccountTypes, {
      fields: [paymentAccountTypes.parentAccountTypeId],
      references: [paymentAccountTypes.id],
    }),
    childAccountTypes: many(paymentAccountTypes),
    createdByUser: one(users, {
      fields: [paymentAccountTypes.createdBy],
      references: [users.id],
    }),
    updatedByUser: one(users, {
      fields: [paymentAccountTypes.updatedBy],
      references: [users.id],
    }),
  }),
);
