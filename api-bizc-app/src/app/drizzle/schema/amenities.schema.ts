import {
  pgTable,
  uniqueIndex,
  jsonb,
  uuid,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { users } from './users.schema';
import { Amenity } from '../../shared/types/amenity.enum';

/**
 * Business Amenities table - Frontend-driven amenity management
 * This table tracks which amenities each business has available
 * Amenities are stored as an array of enum values
 */
export const businessAmenities = pgTable(
  'business_amenities',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    amenities: jsonb('amenities').$type<Amenity[]>().default([]),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'business_amenities'),

    // Unique constraint - one record per business
    uniqueBusinessAmenity: uniqueIndex('business_amenities_unique')
      .on(t.businessId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const businessAmenitiesRelations = relations(
  businessAmenities,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [businessAmenities.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [businessAmenities.createdBy],
      references: [users.id],
      relationName: 'createdBusinessAmenities',
    }),
    updater: one(users, {
      fields: [businessAmenities.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessAmenities',
    }),
  }),
);
