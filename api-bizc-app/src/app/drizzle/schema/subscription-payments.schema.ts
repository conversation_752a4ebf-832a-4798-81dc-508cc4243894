import {
  index,
  pgEnum,
  pgTable,
  text,
  uuid,
  decimal,
  date,
  boolean,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { businessSubscriptions } from './business-subscriptions.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { PaymentStatus } from '../../shared/types/common.enum';
import { paymentStatusEnum } from './common-fields.schema';

import { users } from './users.schema';
/**
 * Subscription Payments Schema
 *
 * This schema manages payment records for business subscriptions,
 * including payment status, methods, and transaction details.
 */

// Use the common PaymentStatus enum imported from common-fields.schema.ts

// Payment Method Enum
export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  CASH = 'cash',
  CHEQUE = 'cheque',
  ONLINE_BANKING = 'online_banking',
  MOBILE_PAYMENT = 'mobile_payment',
  CRYPTOCURRENCY = 'cryptocurrency',
  OTHER = 'other',
}

export const subscriptionPaymentMethodEnum = pgEnum(
  'subscription_payment_method',
  [
    PaymentMethod.BANK_TRANSFER,
    PaymentMethod.CREDIT_CARD,
    PaymentMethod.DEBIT_CARD,
    PaymentMethod.CASH,
    PaymentMethod.CHEQUE,
    PaymentMethod.ONLINE_BANKING,
    PaymentMethod.MOBILE_PAYMENT,
    PaymentMethod.CRYPTOCURRENCY,
    PaymentMethod.OTHER,
  ],
);

// Subscription Invoice Type Enum
export enum SubscriptionInvoiceType {
  SUBSCRIPTION = 'subscription',
  SETUP_FEE = 'setup_fee',
  UPGRADE = 'upgrade',
  DOWNGRADE = 'downgrade',
  PENALTY = 'penalty',
  REFUND = 'refund',
}

export const subscriptionInvoiceTypeEnum = pgEnum('subscription_invoice_type', [
  SubscriptionInvoiceType.SUBSCRIPTION,
  SubscriptionInvoiceType.SETUP_FEE,
  SubscriptionInvoiceType.UPGRADE,
  SubscriptionInvoiceType.DOWNGRADE,
  SubscriptionInvoiceType.PENALTY,
  SubscriptionInvoiceType.REFUND,
]);

// Main subscription payments table
export const subscriptionPayments = pgTable(
  'subscription_payments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    subscriptionId: uuid('subscription_id')
      .notNull()
      .references(() => businessSubscriptions.id),

    // Invoice information
    invoiceNumber: text('invoice_number').notNull(),
    invoiceType: subscriptionInvoiceTypeEnum('subscription_invoice_type')
      .default(SubscriptionInvoiceType.SUBSCRIPTION)
      .notNull(),

    // Payment details
    amount: decimal('amount', { precision: 15, scale: 2 }).notNull(),
    currency: text('currency').default('LKR').notNull(),
    status: paymentStatusEnum('status')
      .default(PaymentStatus.PENDING)
      .notNull(),

    // Payment method and transaction details
    paymentMethod: subscriptionPaymentMethodEnum('subscription_payment_method'),
    transactionId: text('transaction_id'), // External payment gateway transaction ID
    referenceNumber: text('reference_number'), // Bank reference or receipt number

    // Important dates
    invoiceDate: date('invoice_date').notNull(),
    dueDate: date('due_date').notNull(),
    paidDate: date('paid_date'),

    // Billing period this payment covers
    billingPeriodStart: date('billing_period_start').notNull(),
    billingPeriodEnd: date('billing_period_end').notNull(),

    // Payment gateway information
    gatewayProvider: text('gateway_provider'), // PayPal, Stripe, local banks, etc.
    gatewayTransactionId: text('gateway_transaction_id'),
    gatewayResponse: text('gateway_response'), // JSON string of gateway response

    // Additional fees and taxes
    taxAmount: decimal('tax_amount', { precision: 15, scale: 2 }).default(
      '0.00',
    ),
    processingFee: decimal('processing_fee', {
      precision: 15,
      scale: 2,
    }).default('0.00'),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).default('0.00'),

    // Refund information
    refundAmount: decimal('refund_amount', { precision: 15, scale: 2 }).default(
      '0.00',
    ),
    refundDate: date('refund_date'),
    refundReason: text('refund_reason'),

    // Late payment tracking
    isOverdue: boolean('is_overdue').default(false).notNull(),
    overdueDate: date('overdue_date'),
    lateFeeAmount: decimal('late_fee_amount', {
      precision: 15,
      scale: 2,
    }).default('0.00'),

    // Communication tracking
    remindersSent: text('reminders_sent').array().default([]), // Array of reminder dates
    lastReminderDate: date('last_reminder_date'),

    // Notes and additional information
    notes: text('notes'),
    internalNotes: text('internal_notes'), // Admin-only notes
    paymentInstructions: text('payment_instructions'),

    // Automatic retry information (for failed payments)
    retryAttempts: text('retry_attempts').array().default([]), // Array of retry timestamps
    nextRetryDate: date('next_retry_date'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'subscription_payments'),
    subscriptionIdIndex: index(
      'subscription_payments_subscription_id_index',
    ).on(t.subscriptionId),

    // Invoice and transaction indexes
    invoiceNumberIndex: index('subscription_payments_invoice_number_index').on(
      t.invoiceNumber,
    ),
    transactionIdIndex: index('subscription_payments_transaction_id_index').on(
      t.transactionId,
    ),
    referenceNumberIndex: index(
      'subscription_payments_reference_number_index',
    ).on(t.referenceNumber),
    gatewayTransactionIdIndex: index(
      'subscription_payments_gateway_transaction_id_index',
    ).on(t.gatewayTransactionId),

    // Status and type indexes
    statusIndex: index('subscription_payments_status_index').on(t.status),
    invoiceTypeIndex: index('subscription_payments_invoice_type_index').on(
      t.invoiceType,
    ),
    paymentMethodIndex: index('subscription_payments_payment_method_index').on(
      t.paymentMethod,
    ),
    gatewayProviderIndex: index(
      'subscription_payments_gateway_provider_index',
    ).on(t.gatewayProvider),

    // Date-based indexes
    invoiceDateIndex: index('subscription_payments_invoice_date_index').on(
      t.invoiceDate,
    ),
    dueDateIndex: index('subscription_payments_due_date_index').on(t.dueDate),
    paidDateIndex: index('subscription_payments_paid_date_index').on(
      t.paidDate,
    ),
    overdueDateIndex: index('subscription_payments_overdue_date_index').on(
      t.overdueDate,
    ),
    billingPeriodStartIndex: index(
      'subscription_payments_billing_period_start_index',
    ).on(t.billingPeriodStart),
    billingPeriodEndIndex: index(
      'subscription_payments_billing_period_end_index',
    ).on(t.billingPeriodEnd),
    nextRetryDateIndex: index('subscription_payments_next_retry_date_index').on(
      t.nextRetryDate,
    ),

    // Boolean flags indexes
    isOverdueIndex: index('subscription_payments_is_overdue_index').on(
      t.isOverdue,
    ),

    // Composite indexes for common queries
    businessStatusIndex: index('subscription_payments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    subscriptionStatusIndex: index(
      'subscription_payments_subscription_status_index',
    )
      .on(t.subscriptionId, t.status)
      .where(sql`${t.isDeleted} = false`),
    pendingPaymentsIndex: index('subscription_payments_pending_index')
      .on(t.status, t.dueDate)
      .where(sql`${t.isDeleted} = false`),
    overduePaymentsIndex: index('subscription_payments_overdue_index')
      .on(t.isOverdue, t.overdueDate)
      .where(sql`${t.isDeleted} = false`),
    businessInvoiceDateIndex: index(
      'subscription_payments_business_invoice_date_index',
    )
      .on(t.businessId, t.invoiceDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Subscription Payments Relations
export const subscriptionPaymentsRelations = relations(
  subscriptionPayments,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [subscriptionPayments.businessId],
      references: [business.id],
      relationName: 'businessSubscriptionPayments',
    }),

    // Subscription relation
    subscription: one(businessSubscriptions, {
      fields: [subscriptionPayments.subscriptionId],
      references: [businessSubscriptions.id],
      relationName: 'subscriptionPayments',
    }),

    // Audit relations
    creator: one(users, {
      fields: [subscriptionPayments.createdBy],
      references: [users.id],
      relationName: 'createdSubscriptionPayments',
    }),
    updater: one(users, {
      fields: [subscriptionPayments.updatedBy],
      references: [users.id],
      relationName: 'updatedSubscriptionPayments',
    }),
  }),
);
