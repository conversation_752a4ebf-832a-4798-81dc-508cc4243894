import { relations } from 'drizzle-orm';
import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { variationValueTemplates } from './variation-value-templates.schema';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { StatusType } from '../../shared/types';

export const variationTemplateStatusEnum = pgEnum('variation_template_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const variationTemplates = pgTable(
  'variation_templates',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    description: text('description'),
    status: variationTemplateStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'variation_templates'),
    nameIndex: index('variation_templates_name_index').on(t.name),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('variation_templates_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('variation_templates_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const variationTemplatesRelations = relations(
  variationTemplates,
  ({ many }) => ({
    variationValueTemplates: many(variationValueTemplates),
  }),
);

export type VariationTemplate = typeof variationTemplates.$inferSelect;
export type NewVariationTemplate = typeof variationTemplates.$inferInsert;
