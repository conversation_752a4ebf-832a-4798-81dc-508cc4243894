import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  date,
  time,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { vehicles } from './vehicles.schema';

export const vehicleBlockedPeriods = pgTable(
  'vehicle_blocked_periods',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    vehicleId: uuid('vehicle_id')
      .notNull()
      .references(() => vehicles.id),
    startDate: date('start_date').notNull(),
    startTime: time('start_time').notNull(),
    endDate: date('end_date').notNull(),
    endTime: time('end_time').notNull(),
    description: text('description'),
    color: text('color').notNull().default('#FF0000'), // Default to red color
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    idIndex: index('vehicle_blocked_periods_id_index').on(t.id),
    businessIdIndex: index('vehicle_blocked_periods_business_id_index').on(
      t.businessId,
    ),
    vehicleIdIndex: index('vehicle_blocked_periods_vehicle_id_index').on(
      t.vehicleId,
    ),
    startDateIndex: index('vehicle_blocked_periods_start_date_index').on(
      t.startDate,
    ),
    endDateIndex: index('vehicle_blocked_periods_end_date_index').on(t.endDate),
    createdByIndex: index('vehicle_blocked_periods_created_by_index').on(
      t.createdBy,
    ),
    // Composite index for date range queries
    dateRangeIndex: index('vehicle_blocked_periods_date_range_index').on(
      t.vehicleId,
      t.startDate,
      t.endDate,
    ),
  }),
);

// Relations
export const vehicleBlockedPeriodsRelations = relations(
  vehicleBlockedPeriods,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [vehicleBlockedPeriods.businessId],
      references: [business.id],
    }),

    // Vehicle relation
    vehicle: one(vehicles, {
      fields: [vehicleBlockedPeriods.vehicleId],
      references: [vehicles.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [vehicleBlockedPeriods.createdBy],
      references: [users.id],
      relationName: 'createdVehicleBlockedPeriods',
    }),
    updater: one(users, {
      fields: [vehicleBlockedPeriods.updatedBy],
      references: [users.id],
      relationName: 'updatedVehicleBlockedPeriods',
    }),
    deletedByUser: one(users, {
      fields: [vehicleBlockedPeriods.deletedBy],
      references: [users.id],
      relationName: 'deletedVehicleBlockedPeriods',
    }),
  }),
);
