import {
  boolean,
  index,
  integer,
  pgTable,
  timestamp,
  uuid,
  text,
  pgEnum,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { media, mediaReferenceTypeEnum } from './media.schema';
import { business } from './business.schema';

import { users } from './users.schema';

// TypeScript enum for Media Purpose Types
export enum MediaPurpose {
  PRIMARY = 'primary',
  GALLERY = 'gallery',
  ATTACHMENT = 'attachment',
  OG_IMAGE = 'og_image',
  BANNER = 'banner',
  ICON = 'icon',
  OTHER = 'other',
}

// Media purpose enum to categorize media usage
export const mediaPurposeEnum = pgEnum('media_purpose', [
  MediaPurpose.PRIMARY,
  MediaPurpose.GALLERY,
  MediaPurpose.ATTACHMENT,
  MediaPurpose.OG_IMAGE,
  MediaPurpose.BANNER,
  MediaPurpose.ICON,
  MediaPurpose.OTHER,
]);

// Helper to get all media purpose values as an array for validation
export const MEDIA_PURPOSE_VALUES = Object.values(
  MediaPurpose,
) as MediaPurpose[];

// Type alias for the enum values
export type MediaPurposeValue = MediaPurpose;

export const mediaAssociations = pgTable(
  'media_associations',
  {
    // Base entity fields
    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),

    // Business reference
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Reference fields - what entity this media belongs to
    referenceId: uuid('reference_id').notNull(),
    referenceType: mediaReferenceTypeEnum('reference_type').notNull(),

    // Media relationship
    mediaId: uuid('media_id')
      .notNull()
      .references(() => media.id, { onDelete: 'cascade' }),

    // Sorting and organization
    position: integer('position').notNull().default(0),
    purpose: mediaPurposeEnum('purpose').default(MediaPurpose.OTHER).notNull(),

    // Additional metadata
    title: text('title'), // Optional title for the attachment
    description: text('description'), // Optional description
    isPrimary: boolean('is_primary').default(false).notNull(), // Mark primary media
    isVisible: boolean('is_visible').default(true).notNull(), // Control visibility

    // Metadata for attachments
    metadata: text('metadata'), // JSON string for custom metadata
  },
  (t) => ({
    // Core indexes
    businessIdIndex: index('media_associations_business_id_index').on(
      t.businessId,
    ),
    idIndex: index('media_associations_id_index').on(t.id),

    // Reference indexes for faster lookups
    referenceIdIndex: index('media_associations_reference_id_index').on(
      t.referenceId,
    ),
    referenceTypeIndex: index('media_associations_reference_type_index').on(
      t.referenceType,
    ),
    referenceCompositeIndex: index(
      'media_associations_reference_composite_index',
    ).on(t.referenceId, t.referenceType),

    // Media and sorting indexes
    mediaIdIndex: index('media_associations_media_id_index').on(t.mediaId),
    positionIndex: index('media_associations_position_index').on(t.position),
    purposeIndex: index('media_associations_purpose_index').on(t.purpose),

    // Composite index for sorting within reference and purpose
    sortingCompositeIndex: index(
      'media_associations_sorting_composite_index',
    ).on(t.referenceId, t.referenceType, t.purpose, t.position),

    // Index for primary media lookup
    primaryMediaIndex: index('media_associations_primary_index').on(
      t.referenceId,
      t.referenceType,
      t.isPrimary,
    ),

    // Unique constraint: only one primary media per reference and purpose
    uniquePrimaryPerPurpose: uniqueIndex('unique_primary_per_purpose').on(
      t.referenceId,
      t.referenceType,
      t.purpose,
      t.isPrimary,
    ),
  }),
);

// Media Associations Relations
export const mediaAssociationsRelations = relations(
  mediaAssociations,
  ({ one }) => ({
    business: one(business, {
      fields: [mediaAssociations.businessId],
      references: [business.id],
    }),
    media: one(media, {
      fields: [mediaAssociations.mediaId],
      references: [media.id],
    }),
    createdBy: one(users, {
      fields: [mediaAssociations.createdBy],
      references: [users.id],
      relationName: 'mediaAssociationsCreatedBy',
    }),
    updatedBy: one(users, {
      fields: [mediaAssociations.updatedBy],
      references: [users.id],
      relationName: 'mediaAssociationsUpdatedBy',
    }),
  }),
);

// Helper types for TypeScript (using the enum)
export type MediaPurposeType = MediaPurpose;

// Helper functions for common queries
export const mediaAssociationsHelpers = {
  // Get media sorted by position for a specific reference
  getSortedMediaQuery: (
    referenceId: string,
    referenceType: string,
    purpose?: MediaPurpose,
  ) => {
    const conditions = {
      referenceId,
      referenceType,
      isDeleted: false,
      isVisible: true,
      ...(purpose && { purpose }),
    };
    return { where: conditions, orderBy: ['position', 'createdAt'] };
  },

  // Get primary media for a reference
  getPrimaryMediaQuery: (
    referenceId: string,
    referenceType: string,
    purpose?: MediaPurpose,
  ) => {
    return {
      where: {
        referenceId,
        referenceType,
        isPrimary: true,
        isDeleted: false,
        isVisible: true,
        ...(purpose && { purpose }),
      },
    };
  },

  // Get attachments only
  getAttachmentsQuery: (referenceId: string, referenceType: string) => {
    return {
      where: {
        referenceId,
        referenceType,
        purpose: MediaPurpose.ATTACHMENT,
        isDeleted: false,
        isVisible: true,
      },
      orderBy: ['position', 'createdAt'],
    };
  },

  // Get gallery images only
  getGalleryImagesQuery: (referenceId: string, referenceType: string) => {
    return {
      where: {
        referenceId,
        referenceType,
        purpose: MediaPurpose.GALLERY,
        isDeleted: false,
        isVisible: true,
      },
      orderBy: ['position', 'createdAt'],
    };
  },
};
