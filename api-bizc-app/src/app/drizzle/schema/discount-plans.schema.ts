import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  decimal,
  date,
  jsonb,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { categories } from './categories.schema';
import { serviceCategories } from './service-categories.schema';
import { services } from './services.schema';
import { products } from './products.schema';
import { customerGroups } from './customer-groups.schema';
import { customers } from './customers.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { StatusType, DiscountType } from '../../shared/types';

// Re-export DiscountType for DTOs
export { DiscountType };

// Enums for better type safety and consistency
export enum DiscountMethod {
  ATTACHED_TO_RECEIPT = 'attached_to_receipt',
  ATTACHED_TO_PRODUCTS = 'attached_to_products',
  ATTACHED_TO_SERVICES = 'attached_to_services',
  ATTACHED_TO_PRODUCT_CATEGORIES = 'attached_to_product_categories',
  ATTACHED_TO_SERVICE_CATEGORIES = 'attached_to_service_categories',
  ATTACHED_TO_CUSTOMER_GROUP = 'attached_to_customer_group',
  ATTACHED_TO_CUSTOMERS = 'attached_to_customers',
}

export enum DiscountDayOfWeek {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday',
}

export enum TimeType {
  ALL_DAY = 'all_day',
  CUSTOM_PERIOD = 'custom_period',
}

// TypeScript interfaces for JSON field structures
// These interfaces define the expected structure of JSON data stored in the database

/**
 * Interface for time period configuration
 * Used when timeType is CUSTOM_PERIOD
 */
export interface TimePeriod {
  startTime: string; // Format: "HH:MM" (24-hour format, e.g., "09:00", "14:30")
  endTime: string; // Format: "HH:MM" (24-hour format, e.g., "17:00", "23:59")
}

/**
 * Interface for schedule configuration
 * Defines when the discount plan is active
 */
export interface ScheduleConfig {
  days: DiscountDayOfWeek[]; // Array of days when discount is active, e.g., ["monday", "tuesday"]
  timeType: TimeType; // Either "all_day" or "custom_period"
  timePeriod?: TimePeriod; // Required when timeType is "custom_period", optional otherwise
}

// Simplified approach: Use junction tables for relationships and boolean flags for "apply to all" scenarios

// Database enums using the TypeScript enum values for consistency
export const discountMethodEnum = pgEnum('discount_method', [
  DiscountMethod.ATTACHED_TO_RECEIPT,
  DiscountMethod.ATTACHED_TO_PRODUCTS,
  DiscountMethod.ATTACHED_TO_SERVICES,
  DiscountMethod.ATTACHED_TO_PRODUCT_CATEGORIES,
  DiscountMethod.ATTACHED_TO_SERVICE_CATEGORIES,
  DiscountMethod.ATTACHED_TO_CUSTOMER_GROUP,
  DiscountMethod.ATTACHED_TO_CUSTOMERS,
]);

// Use unique name to avoid conflicts with common discount type enum
export const discountPlanTypeEnum = pgEnum('discount_plan_type', [
  DiscountType.PERCENTAGE,
  DiscountType.FIXED_AMOUNT,
]);

export const discountPlanStatusEnum = pgEnum('discount_plan_status', [
  StatusType.ACTIVE,
  StatusType.INACTIVE,
]);

export const discountDayOfWeekEnum = pgEnum('discount_day_of_week', [
  DiscountDayOfWeek.MONDAY,
  DiscountDayOfWeek.TUESDAY,
  DiscountDayOfWeek.WEDNESDAY,
  DiscountDayOfWeek.THURSDAY,
  DiscountDayOfWeek.FRIDAY,
  DiscountDayOfWeek.SATURDAY,
  DiscountDayOfWeek.SUNDAY,
]);

export const timeTypeEnum = pgEnum('time_type', [
  TimeType.ALL_DAY,
  TimeType.CUSTOM_PERIOD,
]);

// Main discount plans table
export const discountPlans = pgTable(
  'discount_plans',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Basic plan information
    planName: text('plan_name').notNull(),
    method: discountMethodEnum('method').notNull(),

    // Discount value configuration
    discountType: discountPlanTypeEnum('discount_plan_type').notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).notNull(),

    // Application settings
    applyToReceiptAutomatically: boolean('apply_to_receipt_automatically')
      .default(false)
      .notNull(),
    applyToCustomer: boolean('apply_to_customer').default(false).notNull(),

    // "Apply to All" flags for different discount methods
    applyToAllProducts: boolean('apply_to_all_products')
      .default(false)
      .notNull(),
    applyToAllServices: boolean('apply_to_all_services')
      .default(false)
      .notNull(),
    applyToAllCategories: boolean('apply_to_all_categories')
      .default(false)
      .notNull(),
    applyToAllCustomerGroups: boolean('apply_to_all_customer_groups')
      .default(false)
      .notNull(),
    applyToAllCustomers: boolean('apply_to_all_customers')
      .default(false)
      .notNull(),

    // Date range
    startDate: date('start_date').notNull(),
    endDate: date('end_date').notNull(),

    // Schedule configuration (stored as JSON)
    // Format: ScheduleConfig[] - Array of schedule configurations
    // Example: [{"days": ["monday", "tuesday"], "timeType": "all_day"}, {"days": ["friday"], "timeType": "custom_period", "timePeriod": {"startTime": "09:00", "endTime": "17:00"}}]
    schedules: jsonb('schedules').notNull(),

    // Status field
    status: discountPlanStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plans'),
    planNameIndex: index('discount_plans_plan_name_index').on(t.planName),
    methodIndex: index('discount_plans_method_index').on(t.method),
    statusIndex: index('discount_plans_status_index').on(t.status),
    startDateIndex: index('discount_plans_start_date_index').on(t.startDate),
    endDateIndex: index('discount_plans_end_date_index').on(t.endDate),

    // Optimized composite indexes
    businessStatusIndex: index('discount_plans_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessDateRangeIndex: index('discount_plans_business_date_range_index')
      .on(t.businessId, t.startDate, t.endDate)
      .where(sql`${t.isDeleted} = false`),
    businessMethodIndex: index('discount_plans_business_method_index')
      .on(t.businessId, t.method)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint for plan name within business
    uniqueBusinessPlanName: uniqueIndex(
      'discount_plans_business_plan_name_unique',
    )
      .on(t.businessId, t.planName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for discount plan products (many-to-many relationship)
// Includes specific discount configuration for each product
export const discountPlanProducts = pgTable(
  'discount_plan_products',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),

    // Product-specific discount configuration
    discountType: discountPlanTypeEnum('discount_plan_type').notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).notNull(),

    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_products'),
    discountPlanIdIndex: index(
      'discount_plan_products_discount_plan_id_index',
    ).on(t.discountPlanId),
    productIdIndex: index('discount_plan_products_product_id_index').on(
      t.productId,
    ),
    uniqueDiscountPlanProduct: uniqueIndex('discount_plan_products_unique').on(
      t.discountPlanId,
      t.productId,
    ),
  }),
);

// Junction table for discount plan services (many-to-many relationship)
// Includes specific discount configuration for each service
export const discountPlanServices = pgTable(
  'discount_plan_services',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),

    // Service-specific discount configuration
    discountType: discountPlanTypeEnum('discount_plan_type').notNull(),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).notNull(),

    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_services'),
    discountPlanIdIndex: index(
      'discount_plan_services_discount_plan_id_index',
    ).on(t.discountPlanId),
    serviceIdIndex: index('discount_plan_services_service_id_index').on(
      t.serviceId,
    ),
    uniqueDiscountPlanService: uniqueIndex('discount_plan_services_unique').on(
      t.discountPlanId,
      t.serviceId,
    ),
  }),
);

// Junction table for discount plan product categories (many-to-many relationship)
export const discountPlanProductCategories = pgTable(
  'discount_plan_product_categories',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    categoryId: uuid('category_id')
      .notNull()
      .references(() => categories.id, { onDelete: 'cascade' }),
    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_product_categories'),
    discountPlanIdIndex: index(
      'discount_plan_product_categories_discount_plan_id_index',
    ).on(t.discountPlanId),
    categoryIdIndex: index(
      'discount_plan_product_categories_category_id_index',
    ).on(t.categoryId),
    uniqueDiscountPlanProductCategory: uniqueIndex(
      'discount_plan_product_categories_unique',
    ).on(t.discountPlanId, t.categoryId),
  }),
);

// Junction table for discount plan service categories (many-to-many relationship)
export const discountPlanServiceCategories = pgTable(
  'discount_plan_service_categories',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    serviceCategoryId: uuid('service_category_id')
      .notNull()
      .references(() => serviceCategories.id, { onDelete: 'cascade' }),
    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_service_categories'),
    discountPlanIdIndex: index(
      'discount_plan_service_categories_discount_plan_id_index',
    ).on(t.discountPlanId),
    serviceCategoryIdIndex: index(
      'discount_plan_service_categories_service_category_id_index',
    ).on(t.serviceCategoryId),
    uniqueDiscountPlanServiceCategory: uniqueIndex(
      'discount_plan_service_categories_unique',
    ).on(t.discountPlanId, t.serviceCategoryId),
  }),
);

// Junction table for discount plan customer groups (many-to-many relationship)
export const discountPlanCustomerGroups = pgTable(
  'discount_plan_customer_groups',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    customerGroupId: uuid('customer_group_id')
      .notNull()
      .references(() => customerGroups.id, { onDelete: 'cascade' }),
    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_customer_groups'),
    discountPlanIdIndex: index(
      'discount_plan_customer_groups_discount_plan_id_index',
    ).on(t.discountPlanId),
    customerGroupIdIndex: index(
      'discount_plan_customer_groups_customer_group_id_index',
    ).on(t.customerGroupId),
    uniqueDiscountPlanCustomerGroup: uniqueIndex(
      'discount_plan_customer_groups_unique',
    ).on(t.discountPlanId, t.customerGroupId),
  }),
);

// Junction table for discount plan customers (many-to-many relationship)
export const discountPlanCustomers = pgTable(
  'discount_plan_customers',
  {
    discountPlanId: uuid('discount_plan_id')
      .notNull()
      .references(() => discountPlans.id, { onDelete: 'cascade' }),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'cascade' }),
    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'discount_plan_customers'),
    discountPlanIdIndex: index(
      'discount_plan_customers_discount_plan_id_index',
    ).on(t.discountPlanId),
    customerIdIndex: index('discount_plan_customers_customer_id_index').on(
      t.customerId,
    ),
    uniqueDiscountPlanCustomer: uniqueIndex(
      'discount_plan_customers_unique',
    ).on(t.discountPlanId, t.customerId),
  }),
);

// Relations
export const discountPlansRelations = relations(
  discountPlans,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlans.businessId],
      references: [business.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlans.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlans.updatedBy],
      references: [users.id],
    }),
    // Junction table relations
    discountPlanProducts: many(discountPlanProducts),
    discountPlanServices: many(discountPlanServices),
    discountPlanProductCategories: many(discountPlanProductCategories),
    discountPlanServiceCategories: many(discountPlanServiceCategories),
    discountPlanCustomerGroups: many(discountPlanCustomerGroups),
    discountPlanCustomers: many(discountPlanCustomers),
  }),
);

export const discountPlanProductsRelations = relations(
  discountPlanProducts,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlanProducts.businessId],
      references: [business.id],
    }),
    // Main relations
    discountPlan: one(discountPlans, {
      fields: [discountPlanProducts.discountPlanId],
      references: [discountPlans.id],
    }),
    product: one(products, {
      fields: [discountPlanProducts.productId],
      references: [products.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlanProducts.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlanProducts.updatedBy],
      references: [users.id],
    }),
  }),
);

export const discountPlanServicesRelations = relations(
  discountPlanServices,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlanServices.businessId],
      references: [business.id],
    }),
    // Main relations
    discountPlan: one(discountPlans, {
      fields: [discountPlanServices.discountPlanId],
      references: [discountPlans.id],
    }),
    service: one(services, {
      fields: [discountPlanServices.serviceId],
      references: [services.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlanServices.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlanServices.updatedBy],
      references: [users.id],
    }),
  }),
);

export const discountPlanProductCategoriesRelations = relations(
  discountPlanProductCategories,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlanProductCategories.businessId],
      references: [business.id],
    }),
    // Main relations
    discountPlan: one(discountPlans, {
      fields: [discountPlanProductCategories.discountPlanId],
      references: [discountPlans.id],
    }),
    category: one(categories, {
      fields: [discountPlanProductCategories.categoryId],
      references: [categories.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlanProductCategories.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlanProductCategories.updatedBy],
      references: [users.id],
    }),
  }),
);

export const discountPlanServiceCategoriesRelations = relations(
  discountPlanServiceCategories,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlanServiceCategories.businessId],
      references: [business.id],
    }),
    // Main relations
    discountPlan: one(discountPlans, {
      fields: [discountPlanServiceCategories.discountPlanId],
      references: [discountPlans.id],
    }),
    serviceCategory: one(serviceCategories, {
      fields: [discountPlanServiceCategories.serviceCategoryId],
      references: [serviceCategories.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlanServiceCategories.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlanServiceCategories.updatedBy],
      references: [users.id],
    }),
  }),
);

export const discountPlanCustomerGroupsRelations = relations(
  discountPlanCustomerGroups,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlanCustomerGroups.businessId],
      references: [business.id],
    }),
    // Main relations
    discountPlan: one(discountPlans, {
      fields: [discountPlanCustomerGroups.discountPlanId],
      references: [discountPlans.id],
    }),
    customerGroup: one(customerGroups, {
      fields: [discountPlanCustomerGroups.customerGroupId],
      references: [customerGroups.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlanCustomerGroups.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlanCustomerGroups.updatedBy],
      references: [users.id],
    }),
  }),
);

export const discountPlanCustomersRelations = relations(
  discountPlanCustomers,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [discountPlanCustomers.businessId],
      references: [business.id],
    }),
    // Main relations
    discountPlan: one(discountPlans, {
      fields: [discountPlanCustomers.discountPlanId],
      references: [discountPlans.id],
    }),
    customer: one(customers, {
      fields: [discountPlanCustomers.customerId],
      references: [customers.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [discountPlanCustomers.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [discountPlanCustomers.updatedBy],
      references: [users.id],
    }),
  }),
);
