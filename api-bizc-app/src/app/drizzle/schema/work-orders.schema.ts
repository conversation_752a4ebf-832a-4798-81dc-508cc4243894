import {
  date,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  uniqueIndex,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { staffMembers } from './staff.schema';
import { salesOrders } from './sales-orders.schema';
import { billOfMaterials } from './bom.schema';
import { workOrderPriorities } from './work-order-priorities.schema';
import { workOrderStatuses } from './work-order-statuses.schema';
import { tasks } from './tasks.schema';
import { products } from './products.schema';
import { productVariants } from './products.schema';

// BOM-related TypeScript enums imported from shared types

// Work Order Status Enum
export enum WorkOrderStatus {
  DRAFT = 'draft',
  PLANNED = 'planned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

// Work Order Priority Enum
export enum WorkOrderPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Operation Status Enum
export enum OperationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
}

// Work Center Type Enum
export enum WorkCenterType {
  MACHINE = 'machine',
  LABOR = 'labor',
  BOTH = 'both',
}

// Routing Status Enum
export enum RoutingStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

// Production Entry Type Enum
export enum ProductionEntryType {
  OUTPUT = 'output',
  CONSUMPTION = 'consumption',
  SCRAP = 'scrap',
  TIME = 'time',
}

// Quality Inspection Type Enum
export enum QualityInspectionType {
  INCOMING = 'incoming',
  IN_PROCESS = 'in_process',
  FINAL = 'final',
}

// Quality Inspection Reference Type Enum
export enum QualityInspectionReferenceType {
  PURCHASE_ORDER = 'purchase_order',
  WORK_ORDER = 'work_order',
  SALES_RETURN = 'sales_return',
}

// Quality Inspection Result Enum
export enum QualityInspectionResult {
  PASS = 'pass',
  FAIL = 'fail',
  CONDITIONAL = 'conditional',
}

// BOM-related enums imported from bom.schema.ts

export const workOrderStatusEnum = pgEnum('work_order_status', [
  WorkOrderStatus.DRAFT,
  WorkOrderStatus.PLANNED,
  WorkOrderStatus.IN_PROGRESS,
  WorkOrderStatus.COMPLETED,
  WorkOrderStatus.CANCELLED,
]);

export const workOrderPriorityEnum = pgEnum('work_order_priority', [
  WorkOrderPriority.LOW,
  WorkOrderPriority.MEDIUM,
  WorkOrderPriority.HIGH,
  WorkOrderPriority.URGENT,
]);

export const operationStatusEnum = pgEnum('operation_status', [
  OperationStatus.PENDING,
  OperationStatus.IN_PROGRESS,
  OperationStatus.COMPLETED,
  OperationStatus.SKIPPED,
]);

export const workCenterTypeEnum = pgEnum('work_center_type', [
  WorkCenterType.MACHINE,
  WorkCenterType.LABOR,
  WorkCenterType.BOTH,
]);

export const routingStatusEnum = pgEnum('routing_status', [
  RoutingStatus.ACTIVE,
  RoutingStatus.INACTIVE,
]);

export const productionEntryTypeEnum = pgEnum('production_entry_type', [
  ProductionEntryType.OUTPUT,
  ProductionEntryType.CONSUMPTION,
  ProductionEntryType.SCRAP,
  ProductionEntryType.TIME,
]);

export const qualityInspectionTypeEnum = pgEnum('quality_inspection_type', [
  QualityInspectionType.INCOMING,
  QualityInspectionType.IN_PROCESS,
  QualityInspectionType.FINAL,
]);

export const qualityInspectionReferenceTypeEnum = pgEnum(
  'quality_inspection_reference_type',
  [
    QualityInspectionReferenceType.PURCHASE_ORDER,
    QualityInspectionReferenceType.WORK_ORDER,
    QualityInspectionReferenceType.SALES_RETURN,
  ],
);

export const qualityInspectionResultEnum = pgEnum('quality_inspection_result', [
  QualityInspectionResult.PASS,
  QualityInspectionResult.FAIL,
  QualityInspectionResult.CONDITIONAL,
]);

// Work Orders table
export const workOrders = pgTable(
  'work_orders',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    workOrderNumber: text('work_order_number').notNull(),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    bomId: uuid('bom_id')
      .notNull()
      .references(() => billOfMaterials.id),
    quantityToProduce: decimal('quantity_to_produce', {
      precision: 15,
      scale: 3,
    }).notNull(),
    quantityProduced: decimal('quantity_produced', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    quantityScrapped: decimal('quantity_scrapped', { precision: 15, scale: 3 })
      .default('0.000')
      .notNull(),
    statusId: uuid('status_id')
      .notNull()
      .references(() => workOrderStatuses.id),
    priorityId: uuid('priority_id')
      .notNull()
      .references(() => workOrderPriorities.id),
    plannedStartDate: date('planned_start_date'),
    plannedEndDate: date('planned_end_date'),
    actualStartDate: timestamp('actual_start_date'),
    actualEndDate: timestamp('actual_end_date'),
    notes: text('notes'),

    // Quality inspection tracking
    qualityInspectionRequired: boolean('quality_inspection_required')
      .default(false)
      .notNull(),
    qualityInspectionCompleted: boolean('quality_inspection_completed')
      .default(false)
      .notNull(),
    qualityInspectionDate: timestamp('quality_inspection_date'),
    qualityInspectionResult: text('quality_inspection_result'), // 'pass', 'fail', 'conditional'
    qualityInspectionNotes: text('quality_inspection_notes'),
    inspectedBy: uuid('inspected_by').references(() => staffMembers.id),

    // Additional fields
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'work_orders'),

    // Foreign key indexes
    productIdIndex: index('work_orders_product_id_index').on(t.productId),
    variantIdIndex: index('work_orders_variant_id_index').on(t.variantId),
    bomIdIndex: index('work_orders_bom_id_index').on(t.bomId),

    // Performance indexes
    statusIdIndex: index('work_orders_status_id_index').on(t.statusId),
    priorityIdIndex: index('work_orders_priority_id_index').on(t.priorityId),
    plannedStartDateIndex: index('work_orders_planned_start_date_index').on(
      t.plannedStartDate,
    ),
    plannedEndDateIndex: index('work_orders_planned_end_date_index').on(
      t.plannedEndDate,
    ),
    actualStartDateIndex: index('work_orders_actual_start_date_index').on(
      t.actualStartDate,
    ),
    actualEndDateIndex: index('work_orders_actual_end_date_index').on(
      t.actualEndDate,
    ),

    // Unique constraint
    uniqueWorkOrderNumber: uniqueIndex('work_orders_work_order_number_unique')
      .on(t.businessId, t.workOrderNumber)
      .where(sql`${t.deletedAt} IS NULL`),

    // Composite indexes for common query patterns
    businessStatusIndex: index('work_orders_business_status_index')
      .on(t.businessId, t.statusId)
      .where(sql`${t.deletedAt} IS NULL`),
    businessPriorityIndex: index('work_orders_business_priority_index')
      .on(t.businessId, t.priorityId)
      .where(sql`${t.deletedAt} IS NULL`),
    businessProductIndex: index('work_orders_business_product_index')
      .on(t.businessId, t.productId)
      .where(sql`${t.deletedAt} IS NULL`),
    businessPlannedDateIndex: index('work_orders_business_planned_date_index')
      .on(t.businessId, t.plannedStartDate)
      .where(sql`${t.deletedAt} IS NULL`),
  }),
);

// Production Entries table
export const productionEntries = pgTable(
  'production_entries',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    workOrderId: uuid('work_order_id')
      .notNull()
      .references(() => workOrders.id),
    operationId: uuid('operation_id'),
    entryType: productionEntryTypeEnum('entry_type').notNull(),
    productId: uuid('product_id').notNull(), // References PRODUCTS table
    variantId: uuid('variant_id'), // References PRODUCT_VARIANTS table
    quantity: decimal('quantity', { precision: 15, scale: 3 }).notNull(),
    batchId: uuid('batch_id'), // References BATCH_NUMBERS table
    serialId: uuid('serial_id'), // References SERIAL_NUMBERS table
    laborHours: decimal('labor_hours', { precision: 8, scale: 2 })
      .default('0.00')
      .notNull(),
    machineHours: decimal('machine_hours', { precision: 8, scale: 2 })
      .default('0.00')
      .notNull(),
    employeeId: uuid('employee_id').references(() => staffMembers.id),
    entryDateTime: timestamp('entry_datetime').notNull(),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'production_entries'),

    // Foreign key indexes
    workOrderIdIndex: index('production_entries_work_order_id_index').on(
      t.workOrderId,
    ),
    operationIdIndex: index('production_entries_operation_id_index').on(
      t.operationId,
    ),
    productIdIndex: index('production_entries_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('production_entries_variant_id_index').on(
      t.variantId,
    ),
    batchIdIndex: index('production_entries_batch_id_index').on(t.batchId),
    serialIdIndex: index('production_entries_serial_id_index').on(t.serialId),
    employeeIdIndex: index('production_entries_employee_id_index').on(
      t.employeeId,
    ),

    // Performance indexes
    entryTypeIndex: index('production_entries_entry_type_index').on(
      t.entryType,
    ),
    entryDateTimeIndex: index('production_entries_entry_datetime_index').on(
      t.entryDateTime,
    ),

    // Composite indexes for common query patterns
    businessWorkOrderIndex: index(
      'production_entries_business_work_order_index',
    ).on(t.businessId, t.workOrderId),
    businessEntryTypeIndex: index(
      'production_entries_business_entry_type_index',
    ).on(t.businessId, t.entryType),
    businessDateIndex: index('production_entries_business_date_index').on(
      t.businessId,
      t.entryDateTime,
    ),
  }),
);

/**
 * Work Order Sales Orders Schema
 * Junction table for many-to-many relationship between work orders and sales orders
 */
export const workOrderSalesOrders = pgTable(
  'work_order_sales_orders',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    workOrderId: uuid('work_order_id')
      .notNull()
      .references(() => workOrders.id, { onDelete: 'cascade' }),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'cascade' }),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    // Primary indexes
    workOrderIdIndex: index('work_order_sales_orders_work_order_id_index').on(
      t.workOrderId,
    ),
    salesOrderIdIndex: index('work_order_sales_orders_sales_order_id_index').on(
      t.salesOrderId,
    ),

    // Composite indexes for common query patterns
    workOrderSalesOrderIndex: index(
      'work_order_sales_orders_work_order_sales_order_index',
    )
      .on(t.workOrderId, t.salesOrderId)
      .where(sql`${t.deletedAt} IS NULL`),

    // Audit indexes
    createdAtIndex: index('work_order_sales_orders_created_at_index').on(
      t.createdAt,
    ),
    updatedAtIndex: index('work_order_sales_orders_updated_at_index').on(
      t.updatedAt,
    ),

    // Unique constraint to prevent duplicate relationships
    uniqueWorkOrderSalesOrder: uniqueIndex(
      'work_order_sales_orders_work_order_sales_order_unique',
    )
      .on(t.workOrderId, t.salesOrderId)
      .where(sql`${t.deletedAt} IS NULL`),
  }),
);

/**
 * Work Order Staff Schema
 * Junction table for tracking staff assignments to work orders
 */
export const workOrderStaff = pgTable(
  'work_order_staff',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    workOrderId: uuid('work_order_id')
      .notNull()
      .references(() => workOrders.id, { onDelete: 'cascade' }),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id, { onDelete: 'cascade' }),
    taskId: uuid('task_id').references(() => tasks.id, {
      onDelete: 'set null',
    }),

    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
    deletedAt: timestamp('deleted_at'),
    isDeleted: boolean('is_deleted').default(false).notNull(),
  },
  (t) => ({
    // Primary indexes
    workOrderIdIndex: index('work_order_staff_work_order_id_index').on(
      t.workOrderId,
    ),
    staffIdIndex: index('work_order_staff_staff_id_index').on(t.staffId),
    taskIdIndex: index('work_order_staff_task_id_index').on(t.taskId),

    // Composite indexes for common query patterns
    workOrderStaffIndex: index('work_order_staff_work_order_staff_index')
      .on(t.workOrderId, t.staffId)
      .where(sql`${t.deletedAt} IS NULL`),

    // Audit indexes
    createdAtIndex: index('work_order_staff_created_at_index').on(t.createdAt),
    updatedAtIndex: index('work_order_staff_updated_at_index').on(t.updatedAt),

    // Unique constraint to prevent duplicate staff assignments
    uniqueWorkOrderStaff: uniqueIndex(
      'work_order_staff_work_order_staff_unique',
    )
      .on(t.workOrderId, t.staffId)
      .where(sql`${t.deletedAt} IS NULL`),
  }),
);

// Relations
export const workOrdersRelations = relations(workOrders, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [workOrders.businessId],
    references: [business.id],
  }),
  // Audit relations
  creator: one(users, {
    fields: [workOrders.createdBy],
    references: [users.id],
    relationName: 'createdWorkOrders',
  }),
  updater: one(users, {
    fields: [workOrders.updatedBy],
    references: [users.id],
    relationName: 'updatedWorkOrders',
  }),
  deleter: one(users, {
    fields: [workOrders.deletedBy],
    references: [users.id],
    relationName: 'deletedWorkOrders',
  }),
  // Reference relations
  product: one(products, {
    fields: [workOrders.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [workOrders.variantId],
    references: [productVariants.id],
  }),
  billOfMaterial: one(billOfMaterials, {
    fields: [workOrders.bomId],
    references: [billOfMaterials.id],
  }),
  status: one(workOrderStatuses, {
    fields: [workOrders.statusId],
    references: [workOrderStatuses.id],
  }),
  priority: one(workOrderPriorities, {
    fields: [workOrders.priorityId],
    references: [workOrderPriorities.id],
  }),
  inspector: one(staffMembers, {
    fields: [workOrders.inspectedBy],
    references: [staffMembers.id],
  }),
  // One-to-many relations
  productionEntries: many(productionEntries),
  workOrderSalesOrders: many(workOrderSalesOrders),
  workOrderStaff: many(workOrderStaff),
}));

export const productionEntriesRelations = relations(
  productionEntries,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [productionEntries.businessId],
      references: [business.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [productionEntries.createdBy],
      references: [users.id],
      relationName: 'createdProductionEntries',
    }),
    updater: one(users, {
      fields: [productionEntries.updatedBy],
      references: [users.id],
      relationName: 'updatedProductionEntries',
    }),
    // Reference relations
    workOrder: one(workOrders, {
      fields: [productionEntries.workOrderId],
      references: [workOrders.id],
    }),
    product: one(products, {
      fields: [productionEntries.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [productionEntries.variantId],
      references: [productVariants.id],
    }),
    employee: one(staffMembers, {
      fields: [productionEntries.employeeId],
      references: [staffMembers.id],
    }),
  }),
);

export const workOrderSalesOrdersRelations = relations(
  workOrderSalesOrders,
  ({ one }) => ({
    // Audit relations
    creator: one(users, {
      fields: [workOrderSalesOrders.createdBy],
      references: [users.id],
      relationName: 'createdWorkOrderSalesOrders',
    }),
    updater: one(users, {
      fields: [workOrderSalesOrders.updatedBy],
      references: [users.id],
      relationName: 'updatedWorkOrderSalesOrders',
    }),
    // Reference relations
    workOrder: one(workOrders, {
      fields: [workOrderSalesOrders.workOrderId],
      references: [workOrders.id],
    }),
    salesOrder: one(salesOrders, {
      fields: [workOrderSalesOrders.salesOrderId],
      references: [salesOrders.id],
    }),
  }),
);

export const workOrderStaffRelations = relations(workOrderStaff, ({ one }) => ({
  // Audit relations
  creator: one(users, {
    fields: [workOrderStaff.createdBy],
    references: [users.id],
    relationName: 'createdWorkOrderStaff',
  }),
  updater: one(users, {
    fields: [workOrderStaff.updatedBy],
    references: [users.id],
    relationName: 'updatedWorkOrderStaff',
  }),
  // Reference relations
  workOrder: one(workOrders, {
    fields: [workOrderStaff.workOrderId],
    references: [workOrders.id],
  }),
  staff: one(staffMembers, {
    fields: [workOrderStaff.staffId],
    references: [staffMembers.id],
  }),
  task: one(tasks, {
    fields: [workOrderStaff.taskId],
    references: [tasks.id],
  }),
}));
