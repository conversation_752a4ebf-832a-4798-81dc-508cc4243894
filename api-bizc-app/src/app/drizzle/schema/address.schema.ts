import {
  index,
  pgEnum,
  pgTable,
  text,
  boolean,
  uuid,
  timestamp,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { AddressType } from '../../shared/types';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { business } from './business.schema';

import { users } from './users.schema';
export const addressTypeEnum = pgEnum('address_type', [
  AddressType.BUSINESS,
  AddressType.SHIPPING,
  AddressType.LOCATION,
  AddressType.BILLING,
  AddressType.HOME,
  AddressType.EMERGENCY_CONTACT,
  AddressType.OTHER,
]);

export const addresses = pgTable(
  'addresses',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    street: text('street').notNull(),
    city: text('city').notNull(),
    state: text('state').notNull(),
    zipCode: text('zip_code').notNull(),
    country: text('country').notNull(),
    addressType: addressTypeEnum('address_type')
      .default(AddressType.BUSINESS)
      .notNull(),
    userId: uuid('user_id'),
    isDefault: boolean('is_default').default(false),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'addresses'),

    // Entity-specific indexes
    userIdIndex: index('addresses_user_id_index').on(t.userId),
    addressTypeIndex: index('addresses_address_type_index').on(t.addressType),
    isDefaultIndex: index('addresses_is_default_index').on(t.isDefault),
  }),
);

// Relations definitions
export const addressesRelations = relations(addresses, ({ one }) => ({
  // Foreign key relations
  business: one(business, {
    fields: [addresses.businessId],
    references: [business.id],
  }),
  user: one(users, {
    fields: [addresses.userId],
    references: [users.id],
  }),

  // Audit field relations
  creator: one(users, {
    fields: [addresses.createdBy],
    references: [users.id],
    relationName: 'createdAddresses',
  }),
  updater: one(users, {
    fields: [addresses.updatedBy],
    references: [users.id],
    relationName: 'updatedAddresses',
  }),
}));
