import {
  boolean,
  decimal,
  index,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';

import { business } from './business.schema';
import { employeeSalaries } from './employee-salary.schema';
import { allowanceTypes } from './allowance-types.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
/**
 * Employee Salary Allowances Table
 * Links employee salaries with their allowances and stores specific allowance amounts
 */
export const employeeSalaryAllowances = pgTable(
  'employee_salary_allowances',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    employeeSalaryId: uuid('employee_salary_id')
      .notNull()
      .references(() => employeeSalaries.id),
    allowanceTypeId: uuid('allowance_type_id')
      .notNull()
      .references(() => allowanceTypes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'employee_salary_allowances'),
    employeeSalaryIdIndex: index(
      'employee_salary_allowances_employee_salary_id_index',
    ).on(t.employeeSalaryId),
    allowanceTypeIdIndex: index(
      'employee_salary_allowances_allowance_type_id_index',
    ).on(t.allowanceTypeId),
    uniqueEmployeeSalaryAllowanceActive: uniqueIndex(
      'employee_salary_allowances_employee_salary_allowance_active_unique',
    )
      .on(t.employeeSalaryId, t.allowanceTypeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const employeeSalaryAllowancesRelations = relations(
  employeeSalaryAllowances,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [employeeSalaryAllowances.businessId],
      references: [business.id],
    }),
    // Employee salary relation
    employeeSalary: one(employeeSalaries, {
      fields: [employeeSalaryAllowances.employeeSalaryId],
      references: [employeeSalaries.id],
    }),
    // Allowance type relation
    allowanceType: one(allowanceTypes, {
      fields: [employeeSalaryAllowances.allowanceTypeId],
      references: [allowanceTypes.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [employeeSalaryAllowances.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [employeeSalaryAllowances.updatedBy],
      references: [users.id],
    }),
  }),
);
