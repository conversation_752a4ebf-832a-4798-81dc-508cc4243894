import {
  boolean,
  decimal,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  products,
  productVariants,
  standardUnitOfMeasureEnum,
} from './products.schema';
import { units } from './units.schema';
import { recipes } from './menu-items.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import {
  BomType,
  BomStatus,
  ComponentType,
  StandardUnitOfMeasure,
} from '../../shared/types';

// BOM Type Enum
export const bomTypeEnum = pgEnum('bom_type', [
  BomType.MANUFACTURING,
  BomType.ASSEMBLY,
  BomType.DISASSEMBLY,
]);

// BOM Status Enum
export const bomStatusEnum = pgEnum('bom_status', [
  BomStatus.DRAFT,
  BomStatus.ACTIVE,
  BomStatus.INACTIVE,
  BomStatus.OBSOLETE,
]);

// Component Type Enum
export const componentTypeEnum = pgEnum('component_type', [
  ComponentType.RAW_MATERIAL,
  ComponentType.SUB_ASSEMBLY,
  ComponentType.PACKAGING,
]);

// Bill of Materials table
export const billOfMaterials = pgTable(
  'bill_of_materials',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    bomCode: text('bom_code').notNull(),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    productVariantId: uuid('product_variant_id').references(
      () => productVariants.id,
      { onDelete: 'cascade' },
    ),
    bomName: text('bom_name').notNull(),
    description: text('description'),
    quantityToProduce: decimal('quantity_to_produce', {
      precision: 15,
      scale: 3,
    })
      .default('1.000')
      .notNull(),
    standardUnitOfMeasure: standardUnitOfMeasureEnum(
      'standard_unit_of_measure',
    ).default(StandardUnitOfMeasure.PIECES),
    unitOfMeasureId: uuid('unit_of_measure_id').references(() => units.id),
    bomType: bomTypeEnum('bom_type').default(BomType.MANUFACTURING).notNull(),
    recipeId: uuid('recipe_id').references(() => recipes.id),
    laborHours: decimal('labor_hours', { precision: 8, scale: 2 })
      .default('0.00')
      .notNull(),
    machineHours: decimal('machine_hours', { precision: 8, scale: 2 })
      .default('0.00')
      .notNull(),
    status: bomStatusEnum('status').default(BomStatus.DRAFT).notNull(),
    versionNumber: integer('version_number').default(1).notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'bill_of_materials'),

    // Entity-specific indexes
    idIndex: index('bill_of_materials_id_index').on(t.id),
    productIdIndex: index('bill_of_materials_product_id_index').on(t.productId),
    productVariantIdIndex: index(
      'bill_of_materials_product_variant_id_index',
    ).on(t.productVariantId),
    unitOfMeasureIdIndex: index(
      'bill_of_materials_unit_of_measure_id_index',
    ).on(t.unitOfMeasureId),
    recipeIdIndex: index('bill_of_materials_recipe_id_index').on(t.recipeId),

    // Performance indexes
    bomTypeIndex: index('bill_of_materials_bom_type_index').on(t.bomType),
    statusIndex: index('bill_of_materials_status_index').on(t.status),
    versionNumberIndex: index('bill_of_materials_version_number_index').on(
      t.versionNumber,
    ),

    // Unique constraint
    uniqueBomCode: uniqueIndex('bill_of_materials_bom_code_unique')
      .on(t.businessId, t.bomCode)
      .where(sql`${t.isDeleted} = false`),

    // Composite indexes for common query patterns
    businessProductIndex: index('bill_of_materials_business_product_index')
      .on(t.businessId, t.productId)
      .where(sql`${t.isDeleted} = false`),
    businessProductVariantIndex: index(
      'bill_of_materials_business_product_variant_index',
    )
      .on(t.businessId, t.productId, t.productVariantId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('bill_of_materials_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Audit indexes
    createdAtIndex: index('bill_of_materials_created_at_index').on(t.createdAt),
    updatedAtIndex: index('bill_of_materials_updated_at_index').on(t.updatedAt),
  }),
);

// BOM Lines table
export const bomLines = pgTable(
  'bom_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    bomId: uuid('bom_id')
      .notNull()
      .references(() => billOfMaterials.id, { onDelete: 'cascade' }),
    componentProductId: uuid('component_product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    componentVariantId: uuid('component_variant_id').references(
      () => productVariants.id,
      { onDelete: 'cascade' },
    ),
    quantityRequired: decimal('quantity_required', {
      precision: 15,
      scale: 3,
    }).notNull(),
    standardUnitOfMeasure: standardUnitOfMeasureEnum(
      'standard_unit_of_measure',
    ).default(StandardUnitOfMeasure.PIECES),
    unitOfMeasureId: uuid('unit_of_measure_id').references(() => units.id),
    componentType: componentTypeEnum('component_type')
      .default(ComponentType.RAW_MATERIAL)
      .notNull(),
    scrapPercentage: decimal('scrap_percentage', { precision: 5, scale: 2 })
      .default('0.00')
      .notNull(),
    sequenceNumber: integer('sequence_number').default(1).notNull(),
    notes: text('notes'),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'bom_lines'),

    // Entity-specific indexes
    idIndex: index('bom_lines_id_index').on(t.id),
    bomIdIndex: index('bom_lines_bom_id_index').on(t.bomId),
    componentProductIdIndex: index('bom_lines_component_product_id_index').on(
      t.componentProductId,
    ),
    componentVariantIdIndex: index('bom_lines_component_variant_id_index').on(
      t.componentVariantId,
    ),
    unitOfMeasureIdIndex: index('bom_lines_unit_of_measure_id_index').on(
      t.unitOfMeasureId,
    ),

    // Performance indexes
    componentTypeIndex: index('bom_lines_component_type_index').on(
      t.componentType,
    ),

    // Sequence order index
    sequenceNumberIndex: index('bom_lines_sequence_number_index').on(
      t.bomId,
      t.sequenceNumber,
    ),

    // Composite indexes for common query patterns
    businessBomIndex: index('bom_lines_business_bom_index').on(
      t.businessId,
      t.bomId,
    ),

    // Audit indexes
    createdAtIndex: index('bom_lines_created_at_index').on(t.createdAt),
    updatedAtIndex: index('bom_lines_updated_at_index').on(t.updatedAt),
  }),
);

// Bill of Materials Relations
export const billOfMaterialsRelations = relations(
  billOfMaterials,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [billOfMaterials.businessId],
      references: [business.id],
      relationName: 'businessBillOfMaterials',
    }),

    // Product relations
    product: one(products, {
      fields: [billOfMaterials.productId],
      references: [products.id],
      relationName: 'productBillOfMaterials',
    }),
    productVariant: one(productVariants, {
      fields: [billOfMaterials.productVariantId],
      references: [productVariants.id],
      relationName: 'variantBillOfMaterials',
    }),

    // Unit and recipe relations
    unitOfMeasure: one(units, {
      fields: [billOfMaterials.unitOfMeasureId],
      references: [units.id],
      relationName: 'bomUnitOfMeasure',
    }),
    recipe: one(recipes, {
      fields: [billOfMaterials.recipeId],
      references: [recipes.id],
      relationName: 'recipeBillOfMaterials',
    }),

    // BOM lines relation
    bomLines: many(bomLines, {
      relationName: 'bomToLines',
    }),

    // Audit relations
    creator: one(users, {
      fields: [billOfMaterials.createdBy],
      references: [users.id],
      relationName: 'createdBillOfMaterials',
    }),
    updater: one(users, {
      fields: [billOfMaterials.updatedBy],
      references: [users.id],
      relationName: 'updatedBillOfMaterials',
    }),
  }),
);

// BOM Lines Relations
export const bomLinesRelations = relations(bomLines, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [bomLines.businessId],
    references: [business.id],
    relationName: 'businessBomLines',
  }),

  // BOM relation
  billOfMaterials: one(billOfMaterials, {
    fields: [bomLines.bomId],
    references: [billOfMaterials.id],
    relationName: 'bomToLines',
  }),

  // Component product relations
  componentProduct: one(products, {
    fields: [bomLines.componentProductId],
    references: [products.id],
    relationName: 'productBomLines',
  }),
  componentVariant: one(productVariants, {
    fields: [bomLines.componentVariantId],
    references: [productVariants.id],
    relationName: 'variantBomLines',
  }),

  // Unit relation
  unitOfMeasure: one(units, {
    fields: [bomLines.unitOfMeasureId],
    references: [units.id],
    relationName: 'bomLineUnitOfMeasure',
  }),

  // Audit relations
  creator: one(users, {
    fields: [bomLines.createdBy],
    references: [users.id],
    relationName: 'createdBomLines',
  }),
  updater: one(users, {
    fields: [bomLines.updatedBy],
    references: [users.id],
    relationName: 'updatedBomLines',
  }),
}));
