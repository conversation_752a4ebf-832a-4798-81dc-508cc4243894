import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
  date,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { addresses } from './address.schema';
import { customers } from './customers.schema';
import { ProjectStatus } from '../../shared/types/project.enum';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export const projectStatusEnum = pgEnum('project_status', [
  ProjectStatus.NOT_STARTED,
  ProjectStatus.IN_PROGRESS,
  ProjectStatus.CANCELLED,
  ProjectStatus.COMPLETED,
]);

export const projects = pgTable(
  'projects',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Project Details
    name: text('name').notNull(),
    description: text('description'),
    tags: text('tags'),
    customerId: uuid('customer_id').references(() => customers.id),
    startDate: date('start_date'),
    endDate: date('end_date'),
    status: projectStatusEnum('status')
      .default(ProjectStatus.NOT_STARTED)
      .notNull(),
    notes: text('notes'),

    // Position field for sorting (following categories pattern)
    position: integer('position').default(0).notNull(),

    // Address References (optional - only if "Address for this project" is enabled)
    billingAddressId: uuid('billing_address_id').references(() => addresses.id),
    shippingAddressId: uuid('shipping_address_id').references(
      () => addresses.id,
    ),
    hasProjectAddress: boolean('has_project_address').default(false).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'projects'),
    nameIndex: index('projects_name_index').on(t.name),
    customerIdIndex: index('projects_customer_id_index').on(t.customerId),
    statusIndex: index('projects_status_index').on(t.status),
    positionIndex: index('projects_position_index').on(t.position),
    billingAddressIdIndex: index('projects_billing_address_id_index').on(
      t.billingAddressId,
    ),
    shippingAddressIdIndex: index('projects_shipping_address_id_index').on(
      t.shippingAddressId,
    ),
    startDateIndex: index('projects_start_date_index').on(t.startDate),
    endDateIndex: index('projects_end_date_index').on(t.endDate),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('projects_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'projects_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('projects_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessCustomerIndex: index('projects_business_customer_index')
      .on(t.businessId, t.customerId)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('projects_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('projects_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const projectsRelations = relations(projects, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [projects.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [projects.createdBy],
    references: [users.id],
    relationName: 'createdProjects',
  }),
  updater: one(users, {
    fields: [projects.updatedBy],
    references: [users.id],
    relationName: 'updatedProjects',
  }),

  // Customer relation
  customer: one(customers, {
    fields: [projects.customerId],
    references: [customers.id],
  }),

  // Address relations
  billingAddress: one(addresses, {
    fields: [projects.billingAddressId],
    references: [addresses.id],
    relationName: 'projectBillingAddress',
  }),
  shippingAddress: one(addresses, {
    fields: [projects.shippingAddressId],
    references: [addresses.id],
    relationName: 'projectShippingAddress',
  }),
}));
