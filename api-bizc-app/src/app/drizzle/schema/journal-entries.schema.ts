import {
  boolean,
  date,
  decimal,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
// TypeScript Enum for Journal Entry Status
export enum JournalEntryStatus {
  DRAFT = 'draft',
  POSTED = 'posted',
  VOIDED = 'voided',
}

// TypeScript Enum for Reference Type
export enum JournalReferenceType {
  MANUAL = 'manual',
  INVOICE = 'invoice',
  BILL = 'bill',
  PAYMENT = 'payment',
  TRANSFER = 'transfer',
  PURCHASE_ORDER = 'purchase_order',
  SALES_ORDER = 'sales_order',
  EXPENSE = 'expense',
  DEPRECIATION = 'depreciation',
  ADJUSTMENT = 'adjustment',
  PAYROLL = 'payroll',
  TAX = 'tax',
  BANK_RECONCILIATION = 'bank_reconciliation',
  OPENING_BALANCE = 'opening_balance',
  CLOSING_ENTRY = 'closing_entry',
}

// TypeScript Enum for Entity Type
export enum EntityType {
  CUSTOMER = 'customer',
  SUPPLIER = 'supplier',
  EMPLOYEE = 'employee',
}

// Journal Entry Status Enum
export const journalEntryStatusEnum = pgEnum('journal_entry_status', [
  JournalEntryStatus.DRAFT,
  JournalEntryStatus.POSTED,
  JournalEntryStatus.VOIDED,
]);

// Journal Reference Type Enum
export const journalReferenceTypeEnum = pgEnum('journal_reference_type', [
  JournalReferenceType.MANUAL,
  JournalReferenceType.INVOICE,
  JournalReferenceType.BILL,
  JournalReferenceType.PAYMENT,
  JournalReferenceType.TRANSFER,
  JournalReferenceType.PURCHASE_ORDER,
  JournalReferenceType.SALES_ORDER,
  JournalReferenceType.EXPENSE,
  JournalReferenceType.DEPRECIATION,
  JournalReferenceType.ADJUSTMENT,
  JournalReferenceType.PAYROLL,
  JournalReferenceType.TAX,
  JournalReferenceType.BANK_RECONCILIATION,
  JournalReferenceType.OPENING_BALANCE,
  JournalReferenceType.CLOSING_ENTRY,
]);

// Journal Entity Type Enum
export const journalEntityTypeEnum = pgEnum('journal_entity_type', [
  EntityType.CUSTOMER,
  EntityType.SUPPLIER,
  EntityType.EMPLOYEE,
]);

export const journalEntries = pgTable(
  'journal_entries',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Journal Entry identification
    journalNumber: text('journal_number').notNull(),
    journalDate: date('journal_date').notNull(),

    // Entry details
    description: text('description').notNull(),
    memo: text('memo'),

    // Reference information
    referenceType: journalReferenceTypeEnum('reference_type').notNull(),
    referenceId: uuid('reference_id'),

    // Status
    status: journalEntryStatusEnum('status')
      .default(JournalEntryStatus.DRAFT)
      .notNull(),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'journal_entries'),
    journalDateIndex: index('journal_entries_journal_date_index').on(
      t.journalDate,
    ),
    statusIndex: index('journal_entries_status_index').on(t.status),
    referenceTypeIndex: index('journal_entries_reference_type_index').on(
      t.referenceType,
    ),
    referenceIdIndex: index('journal_entries_reference_id_index').on(
      t.referenceId,
    ),

    // Unique constraints
    uniqueJournalNumberPerBusiness: uniqueIndex(
      'journal_entries_unique_journal_number_per_business',
    )
      .on(t.businessId, t.journalNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const journalEntryLines = pgTable(
  'journal_entry_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    journalId: uuid('journal_id')
      .notNull()
      .references(() => journalEntries.id, {
        onDelete: 'cascade',
      }),

    // Line identification
    lineNumber: integer('line_number').notNull(),
    sortOrder: integer('sort_order').notNull(),

    // Account information
    accountId: uuid('account_id')
      .notNull()
      .references(() => accounts.id),

    // Amounts
    debitAmount: decimal('debit_amount', {
      precision: 15,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
    creditAmount: decimal('credit_amount', {
      precision: 15,
      scale: 2,
    })
      .default('0.00')
      .notNull(),

    // Line details
    description: text('description'),
    nameReference: text('name_reference'),

    // Tax information
    taxId: uuid('tax_id').references(() => taxes.id),

    // Entity information
    entityType: journalEntityTypeEnum('entity_type'),
    entityId: uuid('entity_id'),
  },
  (t) => ({
    // Indexes for performance
    ...createBaseEntityBusinessIndexes(t, 'journal_entry_lines'),
    journalIdIndex: index('journal_entry_lines_journal_id_index').on(
      t.journalId,
    ),
    accountIdIndex: index('journal_entry_lines_account_id_index').on(
      t.accountId,
    ),
    lineNumberIndex: index('journal_entry_lines_line_number_index').on(
      t.lineNumber,
    ),
    sortOrderIndex: index('journal_entry_lines_sort_order_index').on(
      t.sortOrder,
    ),
    entityTypeIndex: index('journal_entry_lines_entity_type_index').on(
      t.entityType,
    ),
    entityIdIndex: index('journal_entry_lines_entity_id_index').on(t.entityId),
    taxIdIndex: index('journal_entry_lines_tax_id_index').on(t.taxId),

    // Unique constraints
    uniqueLineNumberPerJournal: uniqueIndex(
      'journal_entry_lines_unique_line_number_per_journal',
    )
      .on(t.journalId, t.lineNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueSortOrderPerJournal: uniqueIndex(
      'journal_entry_lines_unique_sort_order_per_journal',
    )
      .on(t.journalId, t.sortOrder)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Journal Entries Relations
export const journalEntriesRelations = relations(
  journalEntries,
  ({ one, many }) => ({
    business: one(business, {
      fields: [journalEntries.businessId],
      references: [business.id],
    }),
    createdBy: one(users, {
      fields: [journalEntries.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [journalEntries.updatedBy],
      references: [users.id],
    }),
    journalEntryLines: many(journalEntryLines),
  }),
);

// Journal Entry Lines Relations
export const journalEntryLinesRelations = relations(
  journalEntryLines,
  ({ one }) => ({
    business: one(business, {
      fields: [journalEntryLines.businessId],
      references: [business.id],
    }),
    journal: one(journalEntries, {
      fields: [journalEntryLines.journalId],
      references: [journalEntries.id],
    }),
    account: one(accounts, {
      fields: [journalEntryLines.accountId],
      references: [accounts.id],
    }),
    tax: one(taxes, {
      fields: [journalEntryLines.taxId],
      references: [taxes.id],
    }),
    createdBy: one(users, {
      fields: [journalEntryLines.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [journalEntryLines.updatedBy],
      references: [users.id],
    }),
  }),
);
