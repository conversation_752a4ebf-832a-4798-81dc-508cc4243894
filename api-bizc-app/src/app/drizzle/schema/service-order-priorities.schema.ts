import {
  boolean,
  index,
  integer,
  pgTable,
  text,
  uniqueIndex,
  uuid,
  varchar,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
/**
 * Service Order Priorities Schema
 * Defines priority levels for service orders with configurable properties
 * including severity levels, position, and escalation settings
 */
export const serviceOrderPriorities = pgTable(
  'service_order_priorities',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    priorityCode: varchar('priority_code', { length: 50 }).unique().notNull(),
    priorityName: varchar('priority_name', { length: 100 }).notNull(),
    description: text('description'),
    colorCode: varchar('color_code', { length: 7 }),
    iconName: varchar('icon_name', { length: 50 }),
    severityLevel: integer('severity_level').notNull(),
    position: integer('position').default(0).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    isDefault: boolean('is_default').default(false).notNull(),
    escalationMinutes: integer('escalation_minutes'),
  },
  (t) => ({
    // Indexes for performance optimization
    businessIdIndex: index('service_order_priorities_business_id_index').on(
      t.businessId,
    ),
    priorityCodeIndex: index('service_order_priorities_priority_code_index').on(
      t.priorityCode,
    ),
    severityLevelIndex: index(
      'service_order_priorities_severity_level_index',
    ).on(t.severityLevel),
    positionIndex: index('service_order_priorities_position_index').on(
      t.position,
    ),
    isActiveIndex: index('service_order_priorities_is_active_index').on(
      t.isActive,
    ),
    isDefaultIndex: index('service_order_priorities_is_default_index').on(
      t.isDefault,
    ),

    // Composite indexes for common query patterns
    businessActiveIndex: index(
      'service_order_priorities_business_active_index',
    ).on(t.businessId, t.isActive),
    businessPositionIndex: index(
      'service_order_priorities_business_position_index',
    ).on(t.businessId, t.position, t.id),
    businessSeverityIndex: index(
      'service_order_priorities_business_severity_index',
    ).on(t.businessId, t.severityLevel, t.id),

    // Unique constraints with soft deletion support
    uniqueBusinessPriorityCode: uniqueIndex(
      'service_order_priorities_business_priority_code_unique',
    )
      .on(t.businessId, t.priorityCode)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessPriorityName: uniqueIndex(
      'service_order_priorities_business_priority_name_unique',
    )
      .on(t.businessId, t.priorityName)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessPosition: uniqueIndex(
      'service_order_priorities_business_position_unique',
    )
      .on(t.businessId, t.position)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const serviceOrderPrioritiesRelations = relations(
  serviceOrderPriorities,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceOrderPriorities.businessId],
      references: [business.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [serviceOrderPriorities.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [serviceOrderPriorities.updatedBy],
      references: [users.id],
    }),
  }),
);
