import {
  boolean,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { MeetingStatus, MeetingType } from '../../shared/types';

export const meetingStatusEnum = pgEnum('meeting_status', [
  MeetingStatus.SCHEDULED,
  MeetingStatus.COMPLETED,
  MeetingStatus.CANCELLED,
  MeetingStatus.RESCHEDULED,
]);

export const meetingTypeEnum = pgEnum('meeting_type', [
  MeetingType.ZOOM_MEET,
  MeetingType.GOOGLE_MEET,
  MeetingType.PHYSICAL,
  MeetingType.MICROSOFT_TEAMS,
]);

export const meetings = pgTable(
  'meetings',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    title: text('title').notNull(),
    description: text('description'),
    scheduledDateTime: timestamp('scheduled_date_time').notNull(),
    duration: integer('duration').notNull(), // Duration in minutes
    meetingType: meetingTypeEnum('meeting_type')
      .default(MeetingType.ZOOM_MEET)
      .notNull(),
    status: meetingStatusEnum('status')
      .default(MeetingStatus.SCHEDULED)
      .notNull(),
    assignedAdminId: uuid('assigned_admin_id').references(() => users.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'meetings'),
    assignedAdminIdIndex: index('meetings_assigned_admin_id_index').on(
      t.assignedAdminId,
    ),
    scheduledDateTimeIndex: index('meetings_scheduled_date_time_index').on(
      t.scheduledDateTime,
    ),
    statusIndex: index('meetings_status_index').on(t.status),
  }),
);

// Meetings Relations
export const meetingsRelations = relations(meetings, ({ one }) => ({
  business: one(business, {
    fields: [meetings.businessId],
    references: [business.id],
  }),
  assignedAdmin: one(users, {
    fields: [meetings.assignedAdminId],
    references: [users.id],
  }),
  createdBy: one(users, {
    fields: [meetings.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [meetings.updatedBy],
    references: [users.id],
  }),
}));
