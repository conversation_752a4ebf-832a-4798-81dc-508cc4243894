import {
  boolean,
  index,
  integer,
  pgTable,
  text,
  uniqueIndex,
  uuid,
  varchar,
  timestamp,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  auditFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
import { workOrders } from './work-orders.schema';
/**
 * Work Order Priorities Schema
 * Defines configurable priority levels for work orders
 */
export const workOrderPriorities = pgTable(
  'work_order_priorities',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    priorityCode: varchar('priority_code', { length: 50 }).unique().notNull(),
    priorityName: varchar('priority_name', { length: 100 }).notNull(),
    description: text('description'),
    colorCode: varchar('color_code', { length: 7 }),
    iconName: varchar('icon_name', { length: 50 }),
    severityLevel: integer('severity_level').notNull(),
    position: integer('position').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    isDefault: boolean('is_default').default(false).notNull(),
    escalationHours: integer('escalation_hours'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'work_order_priorities'),
    priorityCodeIndex: index('work_order_priorities_priority_code_index').on(
      t.priorityCode,
    ),
    severityLevelIndex: index('work_order_priorities_severity_level_index').on(
      t.severityLevel,
    ),
    positionIndex: index('work_order_priorities_position_index').on(t.position),
    isActiveIndex: index('work_order_priorities_is_active_index').on(
      t.isActive,
    ),
    isDefaultIndex: index('work_order_priorities_is_default_index').on(
      t.isDefault,
    ),

    // Composite indexes for common query patterns
    businessActiveIndex: index(
      'work_order_priorities_business_active_index',
    ).on(t.businessId, t.isActive),
    businessDisplayOrderIndex: index(
      'work_order_priorities_business_position_index',
    ).on(t.businessId, t.position, t.id),
    businessSeverityLevelIndex: index(
      'work_order_priorities_business_severity_level_index',
    ).on(t.businessId, t.severityLevel, t.id),

    // Unique constraints
    uniqueBusinessPriorityCode: uniqueIndex(
      'work_order_priorities_business_priority_code_unique',
    ).on(t.businessId, t.priorityCode),
    uniqueBusinessPriorityName: uniqueIndex(
      'work_order_priorities_business_priority_name_unique',
    ).on(t.businessId, t.priorityName),
    uniqueBusinessDisplayOrder: uniqueIndex(
      'work_order_priorities_business_position_unique',
    ).on(t.businessId, t.position),
  }),
);

// Relations
export const workOrderPrioritiesRelations = relations(
  workOrderPriorities,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [workOrderPriorities.businessId],
      references: [business.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [workOrderPriorities.createdBy],
      references: [users.id],
      relationName: 'createdWorkOrderPriorities',
    }),
    updater: one(users, {
      fields: [workOrderPriorities.updatedBy],
      references: [users.id],
      relationName: 'updatedWorkOrderPriorities',
    }),
    // One-to-many relations
    workOrders: many(workOrders),
  }),
);
