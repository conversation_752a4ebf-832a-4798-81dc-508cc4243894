import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  boolean,
  integer,
  decimal,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import {
  products,
  productVariants,
  standardUnitOfMeasureEnum,
} from './products.schema';
import { assets } from './assets.schema';
import { units } from './units.schema';
import { business } from './business.schema';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { users } from './users.schema';
import {
  StandardUnitOfMeasure,
  RecipeStatus,
  FlavorProfile,
} from '../../shared/types';

export const recipeStatusEnum = pgEnum('recipe_status', [
  RecipeStatus.TESTING,
  RecipeStatus.APPROVED,
  RecipeStatus.RETIRED,
]);

export const flavorProfileEnum = pgEnum('flavor_profile', [
  FlavorProfile.SWEET,
  FlavorProfile.SAVORY,
  FlavorProfile.SPICY,
  FlavorProfile.SOUR,
  FlavorProfile.UMAMI,
  FlavorProfile.BITTER,
]);

export const menuItems = pgTable(
  'menu_items',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id),

    // Timing
    prepTimeMinutes: decimal('prep_time_minutes', { precision: 5, scale: 2 }),
    cookTimeMinutes: decimal('cook_time_minutes', { precision: 5, scale: 2 }),

    // Recipe reference
    recipeId: uuid('recipe_id').references(() => recipes.id),

    // Serving information
    servingsPerOrder: integer('servings_per_order').default(1),
    caloriesPerServing: integer('calories_per_serving'),

    // Meal availability
    availableBreakfast: boolean('available_breakfast').default(false).notNull(), // 6:00 AM - 11:00 AM
    availableBrunch: boolean('available_brunch').default(false).notNull(), // 10:00 AM - 2:00 PM
    availableLunch: boolean('available_lunch').default(false).notNull(), // 11:00 AM - 3:00 PM
    availableHappyHour: boolean('available_happy_hour')
      .default(false)
      .notNull(), // 3:00 PM - 6:00 PM
    availableDinner: boolean('available_dinner').default(false).notNull(), // 5:00 PM - 10:00 PM
    availableLateNight: boolean('available_late_night')
      .default(false)
      .notNull(), // 10:00 PM - close

    // Dietary attributes
    isVegetarian: boolean('is_vegetarian').default(false).notNull(),
    isVegan: boolean('is_vegan').default(false).notNull(),
    isGlutenFree: boolean('is_gluten_free').default(false).notNull(),
    isDairyFree: boolean('is_dairy_free').default(false).notNull(),
    isNutFree: boolean('is_nut_free').default(false).notNull(),
    isHalal: boolean('is_halal').default(false).notNull(),
    isKosher: boolean('is_kosher').default(false).notNull(),

    // Spice and flavor profile
    spiceLevel: integer('spice_level').default(0).notNull(), // 0-5 scale
    flavorProfile: flavorProfileEnum('flavor_profile')
      .default(FlavorProfile.SAVORY)
      .notNull(),
  },
  (t) => ({
    productIdIndex: index('menu_items_product_id_index').on(t.productId),
    recipeIdIndex: index('menu_items_recipe_id_index').on(t.recipeId),
    spiceLevelIndex: index('menu_items_spice_level_index').on(t.spiceLevel),
    flavorProfileIndex: index('menu_items_flavor_profile_index').on(
      t.flavorProfile,
    ),

    // Dietary filters
    vegetarianIndex: index('menu_items_vegetarian_index').on(t.isVegetarian),
    veganIndex: index('menu_items_vegan_index').on(t.isVegan),
    glutenFreeIndex: index('menu_items_gluten_free_index').on(t.isGlutenFree),
    dairyFreeIndex: index('menu_items_dairy_free_index').on(t.isDairyFree),
    nutFreeIndex: index('menu_items_nut_free_index').on(t.isNutFree),
    halalIndex: index('menu_items_halal_index').on(t.isHalal),
    kosherIndex: index('menu_items_kosher_index').on(t.isKosher),

    // Meal availability filters
    breakfastIndex: index('menu_items_breakfast_index').on(
      t.availableBreakfast,
    ),
    brunchIndex: index('menu_items_brunch_index').on(t.availableBrunch),
    lunchIndex: index('menu_items_lunch_index').on(t.availableLunch),
    happyHourIndex: index('menu_items_happy_hour_index').on(
      t.availableHappyHour,
    ),
    dinnerIndex: index('menu_items_dinner_index').on(t.availableDinner),
    lateNightIndex: index('menu_items_late_night_index').on(
      t.availableLateNight,
    ),

    // Unique constraint
    uniqueProductMenuItem: index('menu_items_product_unique')
      .on(t.productId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const recipes = pgTable(
  'recipes',
  {
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    recipeCode: text('recipe_code').notNull(),
    recipeName: text('recipe_name').notNull(),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),

    // Timing
    prepTimeMinutes: decimal('prep_time_minutes', { precision: 5, scale: 2 }),
    cookTimeMinutes: decimal('cook_time_minutes', { precision: 5, scale: 2 }),

    // Instructions
    cookingInstructions: text('cooking_instructions'),
    platingInstructions: text('plating_instructions'),

    // Status
    status: recipeStatusEnum('status').default(RecipeStatus.TESTING).notNull(),

    // Standard audit fields
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'recipes'),
    menuItemIdIndex: index('recipes_menu_item_id_index').on(t.productId),
    recipeCodeIndex: index('recipes_recipe_code_index').on(t.recipeCode),
    recipeNameIndex: index('recipes_recipe_name_index').on(t.recipeName),
    statusIndex: index('recipes_status_index').on(t.status),

    // Unique constraints
    uniqueRecipeCode: index('recipes_recipe_code_unique')
      .on(t.recipeCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const recipeIngredients = pgTable(
  'recipe_ingredients',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    recipeId: uuid('recipe_id')
      .notNull()
      .references(() => recipes.id, { onDelete: 'cascade' }),
    ingredientProductId: uuid('ingredient_product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    ingredientVariantId: uuid('ingredient_variant_id').references(
      () => productVariants.id,
      {
        onDelete: 'cascade',
      },
    ),

    // Quantity and measurement
    quantity: decimal('quantity', { precision: 10, scale: 3 }).notNull(),
    standardUnitOfMeasure: standardUnitOfMeasureEnum(
      'standard_unit_of_measure',
    ).default(StandardUnitOfMeasure.PIECES),
    customUnitId: uuid('custom_unit_id').references(() => units.id),
    notes: text('notes'),

    // Standard audit fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'recipe_ingredients'),
    recipeIdIndex: index('recipe_ingredients_recipe_id_index').on(t.recipeId),
    ingredientProductIdIndex: index(
      'recipe_ingredients_ingredient_product_id_index',
    ).on(t.ingredientProductId),
    ingredientVariantIdIndex: index(
      'recipe_ingredients_ingredient_variant_id_index',
    ).on(t.ingredientVariantId),
    standardUnitOfMeasureIndex: index(
      'recipe_ingredients_standard_unit_of_measure_index',
    ).on(t.standardUnitOfMeasure),
    customUnitIdIndex: index('recipe_ingredients_custom_unit_id_index').on(
      t.customUnitId,
    ),

    // Unique constraint to prevent duplicate ingredients in same recipe
    uniqueRecipeIngredient: index('recipe_ingredients_unique')
      .on(t.recipeId, t.ingredientProductId, t.ingredientVariantId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const recipeSteps = pgTable(
  'recipe_steps',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    recipeId: uuid('recipe_id')
      .notNull()
      .references(() => recipes.id, { onDelete: 'cascade' }),
    stepNumber: integer('step_number').notNull(),
    instruction: text('instruction').notNull(),
    timeMinutes: decimal('time_minutes', { precision: 5, scale: 2 }),

    // Standard audit fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'recipe_steps'),
    recipeIdIndex: index('recipe_steps_recipe_id_index').on(t.recipeId),
    stepNumberIndex: index('recipe_steps_step_number_index').on(t.stepNumber),

    // Unique constraint to prevent duplicate step numbers in same recipe
    uniqueRecipeStep: index('recipe_steps_unique')
      .on(t.recipeId, t.stepNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const recipeEquipment = pgTable(
  'recipe_equipment',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    recipeId: uuid('recipe_id')
      .notNull()
      .references(() => recipes.id, { onDelete: 'cascade' }),
    assetId: uuid('asset_id')
      .notNull()
      .references(() => assets.id, { onDelete: 'cascade' }),
    isRequired: boolean('is_required').default(true).notNull(),
    // Standard audit fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'recipe_equipment'),
    recipeIdIndex: index('recipe_equipment_recipe_id_index').on(t.recipeId),
    assetIdIndex: index('recipe_equipment_asset_id_index').on(t.assetId),
    isRequiredIndex: index('recipe_equipment_is_required_index').on(
      t.isRequired,
    ),

    // Unique constraint to prevent duplicate equipment in same recipe
    uniqueRecipeEquipment: index('recipe_equipment_unique')
      .on(t.recipeId, t.assetId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations definitions

// Menu Items Relations
export const menuItemsRelations = relations(menuItems, ({ one }) => ({
  business: one(business, {
    fields: [menuItems.businessId],
    references: [business.id],
  }),
  product: one(products, {
    fields: [menuItems.productId],
    references: [products.id],
  }),
  recipe: one(recipes, {
    fields: [menuItems.recipeId],
    references: [recipes.id],
  }),
  createdBy: one(users, {
    fields: [menuItems.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [menuItems.updatedBy],
    references: [users.id],
  }),
}));

// Recipes Relations
export const recipesRelations = relations(recipes, ({ one, many }) => ({
  business: one(business, {
    fields: [recipes.businessId],
    references: [business.id],
  }),
  product: one(products, {
    fields: [recipes.productId],
    references: [products.id],
  }),
  createdBy: one(users, {
    fields: [recipes.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [recipes.updatedBy],
    references: [users.id],
  }),
  menuItems: many(menuItems),
  recipeIngredients: many(recipeIngredients),
  recipeSteps: many(recipeSteps),
  recipeEquipment: many(recipeEquipment),
}));

// Recipe Ingredients Relations
export const recipeIngredientsRelations = relations(
  recipeIngredients,
  ({ one }) => ({
    business: one(business, {
      fields: [recipeIngredients.businessId],
      references: [business.id],
    }),
    recipe: one(recipes, {
      fields: [recipeIngredients.recipeId],
      references: [recipes.id],
    }),
    ingredientProduct: one(products, {
      fields: [recipeIngredients.ingredientProductId],
      references: [products.id],
    }),
    ingredientVariant: one(productVariants, {
      fields: [recipeIngredients.ingredientVariantId],
      references: [productVariants.id],
    }),
    customUnit: one(units, {
      fields: [recipeIngredients.customUnitId],
      references: [units.id],
    }),
    createdBy: one(users, {
      fields: [recipeIngredients.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [recipeIngredients.updatedBy],
      references: [users.id],
    }),
  }),
);

// Recipe Steps Relations
export const recipeStepsRelations = relations(recipeSteps, ({ one }) => ({
  business: one(business, {
    fields: [recipeSteps.businessId],
    references: [business.id],
  }),
  recipe: one(recipes, {
    fields: [recipeSteps.recipeId],
    references: [recipes.id],
  }),
  createdBy: one(users, {
    fields: [recipeSteps.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [recipeSteps.updatedBy],
    references: [users.id],
  }),
}));

// Recipe Equipment Relations
export const recipeEquipmentRelations = relations(
  recipeEquipment,
  ({ one }) => ({
    business: one(business, {
      fields: [recipeEquipment.businessId],
      references: [business.id],
    }),
    recipe: one(recipes, {
      fields: [recipeEquipment.recipeId],
      references: [recipes.id],
    }),
    asset: one(assets, {
      fields: [recipeEquipment.assetId],
      references: [assets.id],
    }),
    createdBy: one(users, {
      fields: [recipeEquipment.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [recipeEquipment.updatedBy],
      references: [users.id],
    }),
  }),
);
