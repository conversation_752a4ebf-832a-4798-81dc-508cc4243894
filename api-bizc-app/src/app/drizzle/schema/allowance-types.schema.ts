import {
  pgTable,
  text,
  uuid,
  boolean,
  decimal,
  pgEnum,
  index,
  uniqueIndex,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';

import { business } from './business.schema';
import { taxes } from './taxes.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import {
  AllowanceCalculationMethod,
  AllowanceStatus,
} from '../../shared/types';

export const calculationMethodEnum = pgEnum('calculation_method', [
  AllowanceCalculationMethod.FIXED,
  AllowanceCalculationMethod.PERCENTAGE,
  AllowanceCalculationMethod.FORMULA,
  AllowanceCalculationMethod.SLAB,
]);

export const allowanceStatusEnum = pgEnum('allowance_status', [
  AllowanceStatus.ACTIVE,
  AllowanceStatus.INACTIVE,
  AllowanceStatus.PENDING,
  AllowanceStatus.EXPIRED,
]);

export const allowanceTypes = pgTable(
  'allowance_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    allowanceName: text('allowance_name').notNull(),
    allowanceCode: text('allowance_code').notNull(),
    calculationMethod: calculationMethodEnum('calculation_method').notNull(),
    isTaxable: boolean('is_taxable').default(true).notNull(),
    taxRateId: uuid('tax_rate_id').references(() => taxes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    isActive: boolean('is_active').default(true).notNull(),
    isEPFETFEligible: boolean('is_epf_etf_eligible').default(false).notNull(),
    description: text('description'),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'allowance_types'),

    // Entity-specific indexes
    allowanceCodeIndex: index('allowance_types_allowance_code_index').on(
      t.allowanceCode,
    ),
    allowanceNameIndex: index('allowance_types_allowance_name_index').on(
      t.allowanceName,
    ),
    calculationMethodIndex: index(
      'allowance_types_calculation_method_index',
    ).on(t.calculationMethod),
    isActiveIndex: index('allowance_types_is_active_index').on(t.isActive),
    taxRateIdIndex: index('allowance_types_tax_rate_id_index').on(t.taxRateId),

    // Unique constraints
    uniqueBusinessAllowanceCode: uniqueIndex(
      'allowance_types_business_allowance_code_unique',
    )
      .on(t.businessId, t.allowanceCode)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessAllowanceName: uniqueIndex(
      'allowance_types_business_allowance_name_unique',
    )
      .on(t.businessId, t.allowanceName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const allowanceTypesRelations = relations(allowanceTypes, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [allowanceTypes.businessId],
    references: [business.id],
  }),

  // Tax relation
  taxRate: one(taxes, {
    fields: [allowanceTypes.taxRateId],
    references: [taxes.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [allowanceTypes.createdBy],
    references: [users.id],
    relationName: 'createdAllowanceTypes',
  }),
  updater: one(users, {
    fields: [allowanceTypes.updatedBy],
    references: [users.id],
    relationName: 'updatedAllowanceTypes',
  }),
}));
