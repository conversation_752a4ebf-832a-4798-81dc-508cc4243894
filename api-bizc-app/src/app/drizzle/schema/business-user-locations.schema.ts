import { index, pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { businessUsers } from './business-users.schema';
import { locations } from './locations.schema';
import { users } from './users.schema';

export const businessUserLocations = pgTable(
  'business_user_locations',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    businessUserId: uuid('business_user_id')
      .notNull()
      .references(() => businessUsers.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    businessUserIdIndex: index(
      'business_user_locations_business_user_id_index',
    ).on(t.businessUserId),
    locationIdIndex: index('business_user_locations_location_id_index').on(
      t.locationId,
    ),
    // Composite index for efficient queries
    businessUserLocationIndex: index(
      'business_user_locations_composite_index',
    ).on(t.businessUserId, t.locationId),
  }),
);

// Business User Locations Relations
export const businessUserLocationsRelations = relations(
  businessUserLocations,
  ({ one }) => ({
    // Business user relation
    businessUser: one(businessUsers, {
      fields: [businessUserLocations.businessUserId],
      references: [businessUsers.id],
      relationName: 'businessUserLocations',
    }),

    // Location relation
    location: one(locations, {
      fields: [businessUserLocations.locationId],
      references: [locations.id],
      relationName: 'locationBusinessUsers',
    }),

    // Audit relations
    creator: one(users, {
      fields: [businessUserLocations.createdBy],
      references: [users.id],
      relationName: 'createdBusinessUserLocations',
    }),
    updater: one(users, {
      fields: [businessUserLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessUserLocations',
    }),
    deletedByUser: one(users, {
      fields: [businessUserLocations.deletedBy],
      references: [users.id],
      relationName: 'deletedBusinessUserLocations',
    }),
  }),
);
