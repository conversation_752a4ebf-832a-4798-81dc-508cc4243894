import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { staffMembers } from './staff.schema';
import { DesignationStatus } from '../../designations/dto/designation.dto';

export const designationStatusEnum = pgEnum('designation_status', [
  DesignationStatus.ACTIVE,
  DesignationStatus.INACTIVE,
]);

export const designations = pgTable(
  'designations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    parentId: uuid('parent_id').references(() => designations.id),
    description: text('description'),
    status: designationStatusEnum('status')
      .default(DesignationStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'designations'),
    // Entity-specific indexes
    nameIndex: index('designations_name_index').on(t.name),
    parentIdIndex: index('designations_parent_id_index').on(t.parentId),
    statusIndex: index('designations_status_index').on(t.status),

    // Composite indexes for performance
    businessStatusIndex: index('designations_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessParentIndex: index('designations_business_parent_index')
      .on(t.businessId, t.parentId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('designations_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const designationsRelations = relations(
  designations,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [designations.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [designations.createdBy],
      references: [users.id],
      relationName: 'createdDesignations',
    }),
    updater: one(users, {
      fields: [designations.updatedBy],
      references: [users.id],
      relationName: 'updatedDesignations',
    }),

    // Self-referencing relations for hierarchical structure
    parentDesignation: one(designations, {
      fields: [designations.parentId],
      references: [designations.id],
      relationName: 'parentDesignation',
    }),
    childDesignations: many(designations, {
      relationName: 'parentDesignation',
    }),

    // One-to-many relations
    staffMembers: many(staffMembers),
  }),
);
