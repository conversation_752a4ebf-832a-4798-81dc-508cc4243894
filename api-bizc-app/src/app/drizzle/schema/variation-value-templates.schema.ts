import { relations } from 'drizzle-orm';
import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { variationTemplates } from './variation-templates.schema';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { StatusType } from '../../shared/types';

export const variationValueTemplateStatusEnum = pgEnum(
  'variation_value_template_status',
  [StatusType.ACTIVE, StatusType.INACTIVE],
);

export const variationValueTemplates = pgTable(
  'variation_value_templates',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    variationTemplateId: uuid('variation_template_id')
      .notNull()
      .references(() => variationTemplates.id, { onDelete: 'cascade' }),
    name: text('name').notNull(),
    description: text('description'),
    status: variationValueTemplateStatusEnum('status')
      .default(StatusType.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Standard base entity indexes
    ...createBaseEntityBusinessIndexes(t, 'variation_value_templates'),
    variationTemplateIdIndex: index(
      'variation_value_templates_variation_template_id_index',
    ).on(t.variationTemplateId),
    nameIndex: index('variation_value_templates_name_index').on(t.name),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('variation_value_templates_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    templateNameIndex: index('variation_value_templates_template_name_index')
      .on(t.variationTemplateId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueTemplateName: uniqueIndex(
      'variation_value_templates_template_name_unique',
    )
      .on(t.variationTemplateId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const variationValueTemplatesRelations = relations(
  variationValueTemplates,
  ({ one }) => ({
    variationTemplate: one(variationTemplates, {
      fields: [variationValueTemplates.variationTemplateId],
      references: [variationTemplates.id],
    }),
  }),
);

export type VariationValueTemplate =
  typeof variationValueTemplates.$inferSelect;
export type NewVariationValueTemplate =
  typeof variationValueTemplates.$inferInsert;
