import {
  decimal,
  index,
  integer,
  pgTable,
  uniqueIndex,
  uuid,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { leaveTypes } from './leave-types.schema';

import { users } from './users.schema';
export const leaveBalances = pgTable(
  'leave_balances',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    employeeId: uuid('employee_id')
      .notNull()
      .references(() => staffMembers.id),
    leaveTypeId: uuid('leave_type_id')
      .notNull()
      .references(() => leaveTypes.id),
    year: integer('year').notNull(),
    entitledDays: decimal('entitled_days', {
      precision: 5,
      scale: 2,
    }).notNull(),
    usedDays: decimal('used_days', {
      precision: 5,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
    remainingDays: decimal('remaining_days', {
      precision: 5,
      scale: 2,
    }).notNull(),
    carriedForward: decimal('carried_forward', {
      precision: 5,
      scale: 2,
    })
      .default('0.00')
      .notNull(),
  },
  (t) => ({
    idIndex: index('leave_balances_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leave_balances'),
    employeeIdIndex: index('leave_balances_employee_id_index').on(t.employeeId),
    leaveTypeIdIndex: index('leave_balances_leave_type_id_index').on(
      t.leaveTypeId,
    ),
    yearIndex: index('leave_balances_year_index').on(t.year),
    uniqueEmployeeLeaveTypeYear: uniqueIndex(
      'leave_balances_employee_leave_type_year_unique',
    )
      .on(t.businessId, t.employeeId, t.leaveTypeId, t.year)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Leave Balances Relations
export const leaveBalancesRelations = relations(leaveBalances, ({ one }) => ({
  business: one(business, {
    fields: [leaveBalances.businessId],
    references: [business.id],
  }),
  employee: one(staffMembers, {
    fields: [leaveBalances.employeeId],
    references: [staffMembers.id],
  }),
  leaveType: one(leaveTypes, {
    fields: [leaveBalances.leaveTypeId],
    references: [leaveTypes.id],
  }),
  createdBy: one(users, {
    fields: [leaveBalances.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [leaveBalances.updatedBy],
    references: [users.id],
  }),
}));
