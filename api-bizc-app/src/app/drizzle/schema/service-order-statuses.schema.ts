import {
  boolean,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  uniqueIndex,
  uuid,
  varchar,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { ServiceOrderStatusType } from '../../shared/types/service-order.enum';

export const statusTypeEnum = pgEnum('service_order_status_type', [
  ServiceOrderStatusType.INITIAL,
  ServiceOrderStatusType.IN_PROGRESS,
  ServiceOrderStatusType.COMPLETED,
  ServiceOrderStatusType.CANCELLED,
  ServiceOrderStatusType.ON_HOLD,
  ServiceOrderStatusType.PENDING_APPROVAL,
  ServiceOrderStatusType.REJECTED,
]);

/**
 * Service Order Statuses Schema
 * Defines configurable status levels for service orders with workflow properties
 */
export const serviceOrderStatuses = pgTable(
  'service_order_statuses',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    statusCode: varchar('status_code', { length: 50 }).unique().notNull(),
    statusName: varchar('status_name', { length: 100 }).notNull(),
    description: text('description'),
    colorCode: varchar('color_code', { length: 7 }),
    statusType: statusTypeEnum('status_type').notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    isDefault: boolean('is_default').default(false).notNull(),
    position: integer('position').default(0).notNull(),
  },
  (t) => ({
    // Indexes for performance optimization
    businessIdIndex: index('service_order_statuses_business_id_index').on(
      t.businessId,
    ),
    statusCodeIndex: index('service_order_statuses_status_code_index').on(
      t.statusCode,
    ),
    statusTypeIndex: index('service_order_statuses_status_type_index').on(
      t.statusType,
    ),
    positionIndex: index('service_order_statuses_position_index').on(
      t.position,
    ),
    isActiveIndex: index('service_order_statuses_is_active_index').on(
      t.isActive,
    ),
    isDefaultIndex: index('service_order_statuses_is_default_index').on(
      t.isDefault,
    ),

    // Composite indexes for common query patterns
    businessActiveIndex: index(
      'service_order_statuses_business_active_index',
    ).on(t.businessId, t.isActive),
    businessPositionIndex: index(
      'service_order_statuses_business_position_index',
    ).on(t.businessId, t.position, t.id),
    businessStatusTypeIndex: index(
      'service_order_statuses_business_status_type_index',
    ).on(t.businessId, t.statusType, t.id),

    // Unique constraints with soft deletion support
    uniqueBusinessStatusCode: uniqueIndex(
      'service_order_statuses_business_status_code_unique',
    )
      .on(t.businessId, t.statusCode)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessStatusName: uniqueIndex(
      'service_order_statuses_business_status_name_unique',
    )
      .on(t.businessId, t.statusName)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessPosition: uniqueIndex(
      'service_order_statuses_business_position_unique',
    )
      .on(t.businessId, t.position)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const serviceOrderStatusesRelations = relations(
  serviceOrderStatuses,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceOrderStatuses.businessId],
      references: [business.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [serviceOrderStatuses.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [serviceOrderStatuses.updatedBy],
      references: [users.id],
    }),
  }),
);
