import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import {
  locationAllocationFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { ServiceCategoryStatus } from '../../shared/types';

export const serviceCategoryStatusEnum = pgEnum('service_category_status', [
  ServiceCategoryStatus.ACTIVE,
  ServiceCategoryStatus.INACTIVE,
]);

export const serviceCategories = pgTable(
  'service_categories',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    shortCode: text('short_code'),
    parentId: uuid('parent_id').references(() => serviceCategories.id),
    description: text('description'),
    slug: text('slug'),
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),
    color: text('color'),
    image: uuid('image').references(() => media.id),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    status: serviceCategoryStatusEnum('status')
      .default(ServiceCategoryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('service_categories_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'service_categories'),
    nameIndex: index('service_categories_name_index').on(t.name),
    parentIdIndex: index('service_categories_parent_id_index').on(t.parentId),
    slugIndex: index('service_categories_slug_index').on(t.slug),
    imageIndex: index('service_categories_image_index').on(t.image),
    ogImageIndex: index('service_categories_og_image_index').on(t.ogImage),
    positionIndex: index('service_categories_position_index').on(t.position),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('service_categories_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentPositionIndex: index(
      'service_categories_business_parent_position_index',
    )
      .on(t.businessId, t.parentId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'service_categories_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentStatusPositionIndex: index(
      'service_categories_business_parent_status_position_index',
    )
      .on(t.businessId, t.parentId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('service_categories_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('service_categories_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessShortCodeIndex: index(
      'service_categories_business_short_code_index',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('service_categories_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('service_categories_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessShortCode: uniqueIndex(
      'service_categories_business_short_code_unique',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for service category locations (many-to-many relationship)
export const serviceCategoryLocations = pgTable(
  'service_category_locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    serviceCategoryId: uuid('service_category_id')
      .notNull()
      .references(() => serviceCategories.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    // Standard audit fields (using reusable fields)
    // Base entity fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    serviceCategoryIdIndex: index(
      'service_category_locations_service_category_id_index',
    ).on(t.serviceCategoryId),
    locationIdIndex: index('service_category_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueServiceCategoryLocation: uniqueIndex(
      'service_category_locations_unique',
    ).on(t.serviceCategoryId, t.locationId),
  }),
);

// Relations
export const serviceCategoriesRelations = relations(
  serviceCategories,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceCategories.businessId],
      references: [business.id],
    }),
    // Parent/child relations
    parent: one(serviceCategories, {
      fields: [serviceCategories.parentId],
      references: [serviceCategories.id],
    }),
    children: many(serviceCategories),
    // Media relations
    imageFile: one(media, {
      fields: [serviceCategories.image],
      references: [media.id],
    }),
    ogImageFile: one(media, {
      fields: [serviceCategories.ogImage],
      references: [media.id],
    }),
    // Junction table relation
    serviceCategoryLocations: many(serviceCategoryLocations),
    // Audit relations
    creator: one(users, {
      fields: [serviceCategories.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [serviceCategories.updatedBy],
      references: [users.id],
    }),
  }),
);

export const serviceCategoryLocationsRelations = relations(
  serviceCategoryLocations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceCategoryLocations.businessId],
      references: [business.id],
    }),
    // Service category relation
    serviceCategory: one(serviceCategories, {
      fields: [serviceCategoryLocations.serviceCategoryId],
      references: [serviceCategories.id],
    }),
    // Location relation
    location: one(locations, {
      fields: [serviceCategoryLocations.locationId],
      references: [locations.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [serviceCategoryLocations.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [serviceCategoryLocations.updatedBy],
      references: [users.id],
    }),
  }),
);
