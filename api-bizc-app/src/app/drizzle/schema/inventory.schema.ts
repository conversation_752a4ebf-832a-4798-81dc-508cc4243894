// src/drizzle/schema/inventory.schema.ts
import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
  decimal,
  timestamp,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { locations } from './locations.schema';
import { products, productVariants, batchNumbers } from './products.schema';

// Re-export batchNumbers for inventory services
export { batchNumbers };
import { users } from './users.schema';
import {
  locationAllocationFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Enums
export const inventoryTransactionTypeEnum = pgEnum(
  'inventory_transaction_type',
  ['receipt', 'issue', 'transfer_in', 'transfer_out', 'adjustment', 'count'],
);

export const stockAdjustmentTypeEnum = pgEnum('stock_adjustment_type', [
  'physical_count',
  'damage',
  'expiry',
  'correction',
  'other',
]);

export const stockAdjustmentStatusEnum = pgEnum('stock_adjustment_status', [
  'draft',
  'approved',
  'posted',
  'cancelled',
]);

export const stockTransferStatusEnum = pgEnum('stock_transfer_status', [
  'draft',
  'approved',
  'in_transit',
  'completed',
  'cancelled',
]);

export const inventoryReservationTypeEnum = pgEnum(
  'inventory_reservation_type',
  ['quotation', 'sales_order', 'transfer', 'hold'],
);

export const inventoryReservationStatusEnum = pgEnum(
  'inventory_reservation_status',
  ['active', 'expired', 'cancelled', 'fulfilled'],
);

// Main inventory table
export const inventory = pgTable(
  'inventory',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'restrict' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'restrict' }),

    // Quantity fields
    quantityOnHand: decimal('quantity_on_hand', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    quantityReserved: decimal('quantity_reserved', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    quantityAvailable: decimal('quantity_available', {
      precision: 10,
      scale: 2,
    })
      .default('0')
      .notNull(),

    // Stock levels
    minStockLevel: decimal('min_stock_level', { precision: 10, scale: 2 }),
    maxStockLevel: decimal('max_stock_level', { precision: 10, scale: 2 }),
    reorderPoint: decimal('reorder_point', { precision: 10, scale: 2 }),

    // Cost tracking
    unitCost: decimal('unit_cost', { precision: 10, scale: 2 }).default('0'),
    totalValue: decimal('total_value', { precision: 10, scale: 2 }).default(
      '0',
    ),

    // Tracking info
    lastCountDate: timestamp('last_count_date'),
    lastReceiptDate: timestamp('last_receipt_date'),
    lastIssueDate: timestamp('last_issue_date'),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'inventory'),
    productIdIndex: index('inventory_product_id_index').on(t.productId),
    variantIdIndex: index('inventory_variant_id_index').on(t.variantId),
    locationIdIndex: index('inventory_location_id_index').on(t.locationId),

    // Composite indexes for lookups
    productLocationIndex: index('inventory_product_location_index')
      .on(t.productId, t.locationId)
      .where(sql`${t.isDeleted} = false`),
    productVariantLocationIndex: index(
      'inventory_product_variant_location_index',
    )
      .on(t.productId, t.variantId, t.locationId)
      .where(sql`${t.isDeleted} = false`),

    // Low stock index
    lowStockIndex: index('inventory_low_stock_index')
      .on(t.businessId, t.locationId, t.quantityAvailable, t.reorderPoint)
      .where(
        sql`${t.isDeleted} = false AND ${t.quantityAvailable} <= ${t.reorderPoint}`,
      ),

    // Unique constraint
    uniqueProductLocationVariant: uniqueIndex(
      'inventory_product_location_variant_unique',
    )
      .on(t.productId, t.locationId, t.variantId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Inventory transactions table
export const inventoryTransactions = pgTable(
  'inventory_transactions',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    inventoryId: uuid('inventory_id')
      .notNull()
      .references(() => inventory.id, { onDelete: 'restrict' }),

    // Transaction details
    transactionType: inventoryTransactionTypeEnum('transaction_type').notNull(),
    quantity: decimal('quantity', { precision: 10, scale: 2 }).notNull(),
    unitCost: decimal('unit_cost', { precision: 10, scale: 2 }).default('0'),
    totalCost: decimal('total_cost', { precision: 10, scale: 2 }).default('0'),

    // Reference tracking
    referenceType: text('reference_type'),
    referenceId: uuid('reference_id'),

    // Additional tracking
    serialId: uuid('serial_id'),
    batchId: uuid('batch_id'),

    // Transaction metadata
    reasonCode: text('reason_code'),
    notes: text('notes'),
    transactionDate: timestamp('transaction_date').defaultNow().notNull(),

    // User tracking
    performedBy: uuid('performed_by')
      .notNull()
      .references(() => users.id, { onDelete: 'restrict' }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'inventory_transactions'),
    inventoryIdIndex: index('inventory_transactions_inventory_id_index').on(
      t.inventoryId,
    ),
    transactionTypeIndex: index('inventory_transactions_type_index').on(
      t.transactionType,
    ),
    transactionDateIndex: index('inventory_transactions_date_index').on(
      t.transactionDate,
    ),
    referenceIndex: index('inventory_transactions_reference_index').on(
      t.referenceType,
      t.referenceId,
    ),
    performedByIndex: index('inventory_transactions_performed_by_index').on(
      t.performedBy,
    ),

    // Composite indexes for reporting
    inventoryDateIndex: index('inventory_transactions_inventory_date_index')
      .on(t.inventoryId, t.transactionDate)
      .where(sql`${t.isDeleted} = false`),
    businessDateTypeIndex: index(
      'inventory_transactions_business_date_type_index',
    )
      .on(t.businessId, t.transactionDate, t.transactionType)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Stock adjustments table
export const stockAdjustments = pgTable(
  'stock_adjustments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    adjustmentNumber: text('adjustment_number').notNull(),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'restrict' }),

    // Adjustment details
    adjustmentDate: timestamp('adjustment_date').defaultNow().notNull(),
    adjustmentType: stockAdjustmentTypeEnum('adjustment_type').notNull(),
    status: stockAdjustmentStatusEnum('status').default('draft').notNull(),

    // Reason and notes
    reason: text('reason').notNull(),
    notes: text('notes'),

    // Approval workflow
    requestedBy: uuid('requested_by')
      .notNull()
      .references(() => users.id, { onDelete: 'restrict' }),
    approvedBy: uuid('approved_by').references(() => users.id, {
      onDelete: 'restrict',
    }),
    approvedDate: timestamp('approved_date'),
    postedDate: timestamp('posted_date'),

    // Summary fields
    totalItems: integer('total_items').default(0),
    totalValue: decimal('total_value', { precision: 10, scale: 2 }).default(
      '0',
    ),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'stock_adjustments'),
    adjustmentNumberIndex: index('stock_adjustments_number_index').on(
      t.adjustmentNumber,
    ),
    locationIdIndex: index('stock_adjustments_location_id_index').on(
      t.locationId,
    ),
    statusIndex: index('stock_adjustments_status_index').on(t.status),
    adjustmentDateIndex: index('stock_adjustments_date_index').on(
      t.adjustmentDate,
    ),
    requestedByIndex: index('stock_adjustments_requested_by_index').on(
      t.requestedBy,
    ),

    // Composite indexes
    businessStatusDateIndex: index(
      'stock_adjustments_business_status_date_index',
    )
      .on(t.businessId, t.status, t.adjustmentDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint
    uniqueAdjustmentNumber: uniqueIndex('stock_adjustments_number_unique')
      .on(t.businessId, t.adjustmentNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Stock adjustment lines table
export const stockAdjustmentLines = pgTable(
  'stock_adjustment_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    adjustmentId: uuid('adjustment_id')
      .notNull()
      .references(() => stockAdjustments.id, { onDelete: 'cascade' }),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'restrict' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),
    inventoryId: uuid('inventory_id')
      .notNull()
      .references(() => inventory.id, { onDelete: 'restrict' }),

    // Quantities
    currentQuantity: decimal('current_quantity', {
      precision: 10,
      scale: 2,
    }).notNull(),
    countedQuantity: decimal('counted_quantity', {
      precision: 10,
      scale: 2,
    }).notNull(),
    quantityDifference: decimal('quantity_difference', {
      precision: 10,
      scale: 2,
    }).notNull(),

    // Cost tracking
    unitCost: decimal('unit_cost', { precision: 10, scale: 2 }).default('0'),
    totalValue: decimal('total_value', { precision: 10, scale: 2 }).default(
      '0',
    ),

    // Additional tracking
    serialId: uuid('serial_id'),
    batchId: uuid('batch_id'),

    // Line metadata
    reason: text('reason'),
    notes: text('notes'),
    sortOrder: integer('sort_order').default(0),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'stock_adjustment_lines'),
    adjustmentIdIndex: index('stock_adjustment_lines_adjustment_id_index').on(
      t.adjustmentId,
    ),
    productIdIndex: index('stock_adjustment_lines_product_id_index').on(
      t.productId,
    ),
    inventoryIdIndex: index('stock_adjustment_lines_inventory_id_index').on(
      t.inventoryId,
    ),
  }),
);

// Stock transfers table
export const stockTransfers = pgTable(
  'stock_transfers',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    transferNumber: text('transfer_number').notNull(),

    // Locations
    fromLocationId: uuid('from_location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'restrict' }),
    toLocationId: uuid('to_location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'restrict' }),

    // Transfer details
    transferDate: timestamp('transfer_date').defaultNow().notNull(),
    status: stockTransferStatusEnum('status').default('draft').notNull(),
    transferReason: text('transfer_reason').notNull(),
    notes: text('notes'),

    // Workflow tracking
    requestedBy: uuid('requested_by')
      .notNull()
      .references(() => users.id, { onDelete: 'restrict' }),
    approvedBy: uuid('approved_by').references(() => users.id, {
      onDelete: 'restrict',
    }),
    shippedBy: uuid('shipped_by').references(() => users.id, {
      onDelete: 'restrict',
    }),
    receivedBy: uuid('received_by').references(() => users.id, {
      onDelete: 'restrict',
    }),

    // Dates
    approvedDate: timestamp('approved_date'),
    shippedDate: timestamp('shipped_date'),
    receivedDate: timestamp('received_date'),

    // Summary fields
    totalItems: integer('total_items').default(0),
    totalQuantity: decimal('total_quantity', {
      precision: 10,
      scale: 2,
    }).default('0'),
    totalValue: decimal('total_value', { precision: 10, scale: 2 }).default(
      '0',
    ),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'stock_transfers'),
    transferNumberIndex: index('stock_transfers_number_index').on(
      t.transferNumber,
    ),
    fromLocationIndex: index('stock_transfers_from_location_index').on(
      t.fromLocationId,
    ),
    toLocationIndex: index('stock_transfers_to_location_index').on(
      t.toLocationId,
    ),
    statusIndex: index('stock_transfers_status_index').on(t.status),
    transferDateIndex: index('stock_transfers_date_index').on(t.transferDate),

    // Composite indexes
    businessStatusDateIndex: index('stock_transfers_business_status_date_index')
      .on(t.businessId, t.status, t.transferDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint
    uniqueTransferNumber: uniqueIndex('stock_transfers_number_unique')
      .on(t.businessId, t.transferNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Stock transfer lines table
export const stockTransferLines = pgTable(
  'stock_transfer_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    transferId: uuid('transfer_id')
      .notNull()
      .references(() => stockTransfers.id, { onDelete: 'cascade' }),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'restrict' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),

    // Inventory tracking
    fromInventoryId: uuid('from_inventory_id')
      .notNull()
      .references(() => inventory.id, { onDelete: 'restrict' }),
    toInventoryId: uuid('to_inventory_id').references(() => inventory.id, {
      onDelete: 'restrict',
    }),

    // Quantities and cost
    quantity: decimal('quantity', { precision: 10, scale: 2 }).notNull(),
    unitCost: decimal('unit_cost', { precision: 10, scale: 2 }).default('0'),
    totalValue: decimal('total_value', { precision: 10, scale: 2 }).default(
      '0',
    ),

    // Additional tracking
    batchId: uuid('batch_id'),
    serialIds: uuid('serial_ids').array(),

    // Line metadata
    notes: text('notes'),
    sortOrder: integer('sort_order').default(0),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'stock_transfer_lines'),
    transferIdIndex: index('stock_transfer_lines_transfer_id_index').on(
      t.transferId,
    ),
    productIdIndex: index('stock_transfer_lines_product_id_index').on(
      t.productId,
    ),
    fromInventoryIndex: index('stock_transfer_lines_from_inventory_index').on(
      t.fromInventoryId,
    ),
    toInventoryIndex: index('stock_transfer_lines_to_inventory_index').on(
      t.toInventoryId,
    ),
  }),
);

// Inventory reservations table
export const inventoryReservations = pgTable(
  'inventory_reservations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'restrict' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'restrict' }),

    // Reservation details
    reservationType: inventoryReservationTypeEnum('reservation_type').notNull(),
    referenceId: uuid('reference_id').notNull(),
    quantityReserved: decimal('quantity_reserved', {
      precision: 10,
      scale: 2,
    }).notNull(),

    // Status and expiry
    status: inventoryReservationStatusEnum('status')
      .default('active')
      .notNull(),
    expiryDate: timestamp('expiry_date'),

    // Metadata
    notes: text('notes'),
    reservedBy: uuid('reserved_by')
      .notNull()
      .references(() => users.id, { onDelete: 'restrict' }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'inventory_reservations'),
    productIdIndex: index('inventory_reservations_product_id_index').on(
      t.productId,
    ),
    locationIdIndex: index('inventory_reservations_location_id_index').on(
      t.locationId,
    ),
    statusIndex: index('inventory_reservations_status_index').on(t.status),
    expiryDateIndex: index('inventory_reservations_expiry_date_index').on(
      t.expiryDate,
    ),
    referenceIndex: index('inventory_reservations_reference_index').on(
      t.reservationType,
      t.referenceId,
    ),

    // Composite indexes
    activeReservationsIndex: index('inventory_reservations_active_index')
      .on(t.businessId, t.status, t.expiryDate)
      .where(sql`${t.isDeleted} = false AND ${t.status} = 'active'`),
    productLocationStatusIndex: index(
      'inventory_reservations_product_location_status_index',
    )
      .on(t.productId, t.locationId, t.status)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Product locations table (for min/max levels per location)
export const productLocations = pgTable(
  'product_locations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'cascade',
    }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),

    // Stock level settings
    minStockLevel: decimal('min_stock_level', { precision: 10, scale: 2 }),
    maxStockLevel: decimal('max_stock_level', { precision: 10, scale: 2 }),
    reorderPoint: decimal('reorder_point', { precision: 10, scale: 2 }),
    reorderQuantity: decimal('reorder_quantity', { precision: 10, scale: 2 }),

    // Location-specific settings
    isActive: boolean('is_active').default(true).notNull(),
    binLocation: text('bin_location'),
    pickingPriority: integer('picking_priority').default(0),

    // Additional metadata
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'product_locations'),
    productIdIndex: index('product_locations_product_id_index').on(t.productId),
    locationIdIndex: index('product_locations_location_id_index').on(
      t.locationId,
    ),

    // Unique constraint
    uniqueProductLocationVariant: uniqueIndex('product_locations_unique')
      .on(t.productId, t.locationId, t.variantId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Serial numbers table
export const serialNumbers = pgTable(
  'serial_numbers',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    serialNumber: text('serial_number').notNull(),
    imeiNumber: text('imei_number'),
    imei2Number: text('imei2_number'),

    // Product tracking
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'restrict' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),

    // Status and location
    status: text('status').default('available').notNull(),
    currentLocationId: uuid('current_location_id').references(
      () => locations.id,
      { onDelete: 'restrict' },
    ),

    // Transaction references
    purchaseOrderId: uuid('purchase_order_id'),
    salesOrderId: uuid('sales_order_id'),
    customerId: uuid('customer_id'),

    // Dates
    purchaseDate: timestamp('purchase_date'),
    salesDate: timestamp('sales_date'),
    warrantyExpiry: timestamp('warranty_expiry'),

    // Cost tracking
    purchaseCost: decimal('purchase_cost', { precision: 10, scale: 2 }),
    sellingPrice: decimal('selling_price', { precision: 10, scale: 2 }),

    // Additional info
    metadata: jsonb('metadata'),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'serial_numbers'),
    serialNumberIndex: index('serial_numbers_serial_index').on(t.serialNumber),
    imeiNumberIndex: index('serial_numbers_imei_index').on(t.imeiNumber),
    productIdIndex: index('serial_numbers_product_id_index').on(t.productId),
    statusIndex: index('serial_numbers_status_index').on(t.status),
    locationIdIndex: index('serial_numbers_location_id_index').on(
      t.currentLocationId,
    ),

    // Unique constraints
    uniqueSerialNumber: uniqueIndex('serial_numbers_unique')
      .on(t.businessId, t.serialNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueImeiNumber: uniqueIndex('serial_numbers_imei_unique')
      .on(t.businessId, t.imeiNumber)
      .where(sql`${t.isDeleted} = false AND ${t.imeiNumber} IS NOT NULL`),
  }),
);

// Batch numbers table imported from products.schema.ts

// Relations
export const inventoryRelations = relations(inventory, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [inventory.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [inventory.createdBy],
    references: [users.id],
    relationName: 'createdInventory',
  }),
  updater: one(users, {
    fields: [inventory.updatedBy],
    references: [users.id],
    relationName: 'updatedInventory',
  }),

  // Product and variant relations
  product: one(products, {
    fields: [inventory.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [inventory.variantId],
    references: [productVariants.id],
  }),

  // Location relation
  location: one(locations, {
    fields: [inventory.locationId],
    references: [locations.id],
  }),

  // One-to-many relations
  transactions: many(inventoryTransactions),
  adjustmentLines: many(stockAdjustmentLines),
  transferFromLines: many(stockTransferLines, {
    relationName: 'fromInventory',
  }),
  transferToLines: many(stockTransferLines, {
    relationName: 'toInventory',
  }),
}));

export const inventoryTransactionsRelations = relations(
  inventoryTransactions,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [inventoryTransactions.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [inventoryTransactions.createdBy],
      references: [users.id],
      relationName: 'createdInventoryTransactions',
    }),
    updater: one(users, {
      fields: [inventoryTransactions.updatedBy],
      references: [users.id],
      relationName: 'updatedInventoryTransactions',
    }),

    // Inventory relation
    inventory: one(inventory, {
      fields: [inventoryTransactions.inventoryId],
      references: [inventory.id],
    }),

    // Performed by relation
    performedBy: one(users, {
      fields: [inventoryTransactions.performedBy],
      references: [users.id],
      relationName: 'performedInventoryTransactions',
    }),
  }),
);

export const stockAdjustmentsRelations = relations(
  stockAdjustments,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [stockAdjustments.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [stockAdjustments.createdBy],
      references: [users.id],
      relationName: 'createdStockAdjustments',
    }),
    updater: one(users, {
      fields: [stockAdjustments.updatedBy],
      references: [users.id],
      relationName: 'updatedStockAdjustments',
    }),

    // Location relation
    location: one(locations, {
      fields: [stockAdjustments.locationId],
      references: [locations.id],
    }),

    // User relations
    requestedBy: one(users, {
      fields: [stockAdjustments.requestedBy],
      references: [users.id],
      relationName: 'requestedStockAdjustments',
    }),
    approvedBy: one(users, {
      fields: [stockAdjustments.approvedBy],
      references: [users.id],
      relationName: 'approvedStockAdjustments',
    }),

    // One-to-many relations
    lines: many(stockAdjustmentLines),
  }),
);

export const stockAdjustmentLinesRelations = relations(
  stockAdjustmentLines,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [stockAdjustmentLines.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [stockAdjustmentLines.createdBy],
      references: [users.id],
      relationName: 'createdStockAdjustmentLines',
    }),
    updater: one(users, {
      fields: [stockAdjustmentLines.updatedBy],
      references: [users.id],
      relationName: 'updatedStockAdjustmentLines',
    }),

    // Adjustment relation
    adjustment: one(stockAdjustments, {
      fields: [stockAdjustmentLines.adjustmentId],
      references: [stockAdjustments.id],
    }),

    // Product and variant relations
    product: one(products, {
      fields: [stockAdjustmentLines.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [stockAdjustmentLines.variantId],
      references: [productVariants.id],
    }),

    // Inventory relation
    inventory: one(inventory, {
      fields: [stockAdjustmentLines.inventoryId],
      references: [inventory.id],
    }),
  }),
);

export const stockTransfersRelations = relations(
  stockTransfers,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [stockTransfers.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [stockTransfers.createdBy],
      references: [users.id],
      relationName: 'createdStockTransfers',
    }),
    updater: one(users, {
      fields: [stockTransfers.updatedBy],
      references: [users.id],
      relationName: 'updatedStockTransfers',
    }),

    // Location relations
    fromLocation: one(locations, {
      fields: [stockTransfers.fromLocationId],
      references: [locations.id],
      relationName: 'transfersFrom',
    }),
    toLocation: one(locations, {
      fields: [stockTransfers.toLocationId],
      references: [locations.id],
      relationName: 'transfersTo',
    }),

    // User workflow relations
    requestedBy: one(users, {
      fields: [stockTransfers.requestedBy],
      references: [users.id],
      relationName: 'requestedStockTransfers',
    }),
    approvedBy: one(users, {
      fields: [stockTransfers.approvedBy],
      references: [users.id],
      relationName: 'approvedStockTransfers',
    }),
    shippedBy: one(users, {
      fields: [stockTransfers.shippedBy],
      references: [users.id],
      relationName: 'shippedStockTransfers',
    }),
    receivedBy: one(users, {
      fields: [stockTransfers.receivedBy],
      references: [users.id],
      relationName: 'receivedStockTransfers',
    }),

    // One-to-many relations
    lines: many(stockTransferLines),
  }),
);

export const stockTransferLinesRelations = relations(
  stockTransferLines,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [stockTransferLines.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [stockTransferLines.createdBy],
      references: [users.id],
      relationName: 'createdStockTransferLines',
    }),
    updater: one(users, {
      fields: [stockTransferLines.updatedBy],
      references: [users.id],
      relationName: 'updatedStockTransferLines',
    }),

    // Transfer relation
    transfer: one(stockTransfers, {
      fields: [stockTransferLines.transferId],
      references: [stockTransfers.id],
    }),

    // Product and variant relations
    product: one(products, {
      fields: [stockTransferLines.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [stockTransferLines.variantId],
      references: [productVariants.id],
    }),

    // Inventory relations
    fromInventory: one(inventory, {
      fields: [stockTransferLines.fromInventoryId],
      references: [inventory.id],
      relationName: 'fromInventory',
    }),
    toInventory: one(inventory, {
      fields: [stockTransferLines.toInventoryId],
      references: [inventory.id],
      relationName: 'toInventory',
    }),
  }),
);

export const inventoryReservationsRelations = relations(
  inventoryReservations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [inventoryReservations.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [inventoryReservations.createdBy],
      references: [users.id],
      relationName: 'createdInventoryReservations',
    }),
    updater: one(users, {
      fields: [inventoryReservations.updatedBy],
      references: [users.id],
      relationName: 'updatedInventoryReservations',
    }),

    // Product and variant relations
    product: one(products, {
      fields: [inventoryReservations.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [inventoryReservations.variantId],
      references: [productVariants.id],
    }),

    // Location relation
    location: one(locations, {
      fields: [inventoryReservations.locationId],
      references: [locations.id],
    }),

    // Reserved by relation
    reservedBy: one(users, {
      fields: [inventoryReservations.reservedBy],
      references: [users.id],
      relationName: 'reservedInventory',
    }),
  }),
);

export const productLocationsRelations = relations(
  productLocations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [productLocations.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [productLocations.createdBy],
      references: [users.id],
      relationName: 'createdProductLocations',
    }),
    updater: one(users, {
      fields: [productLocations.updatedBy],
      references: [users.id],
      relationName: 'updatedProductLocations',
    }),

    // Product and variant relations
    product: one(products, {
      fields: [productLocations.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [productLocations.variantId],
      references: [productVariants.id],
    }),

    // Location relation
    location: one(locations, {
      fields: [productLocations.locationId],
      references: [locations.id],
    }),
  }),
);

export const serialNumbersRelations = relations(serialNumbers, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [serialNumbers.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [serialNumbers.createdBy],
    references: [users.id],
    relationName: 'createdSerialNumbers',
  }),
  updater: one(users, {
    fields: [serialNumbers.updatedBy],
    references: [users.id],
    relationName: 'updatedSerialNumbers',
  }),

  // Product and variant relations
  product: one(products, {
    fields: [serialNumbers.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [serialNumbers.variantId],
    references: [productVariants.id],
  }),

  // Location relation
  currentLocation: one(locations, {
    fields: [serialNumbers.currentLocationId],
    references: [locations.id],
  }),
}));

// Batch numbers relations imported from products.schema.ts

// Export all schemas
export const inventorySchemas = {
  inventory,
  inventoryTransactions,
  stockAdjustments,
  stockAdjustmentLines,
  stockTransfers,
  stockTransferLines,
  inventoryReservations,
  productLocations,
  serialNumbers,
  batchNumbers,
};
