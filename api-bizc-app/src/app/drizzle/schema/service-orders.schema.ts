import {
  boolean,
  decimal,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { business } from './business.schema';
import { customers } from './customers.schema';
import { appointments } from './appointments.schema';
import { serviceOrderStatuses } from './service-order-statuses.schema';
import { serviceOrderPriorities } from './service-order-priorities.schema';
import { serviceOrderTypes } from './service-order-types.schema';
import { locations } from './locations.schema';
import { staffMembers } from './staff.schema';
import { units } from './units.schema';
import { services } from './services.schema';
import { taxes } from './taxes.schema';
import { tasks } from './tasks.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
  discountTypeEnum,
  taxTypeEnum,
} from './common-fields.schema';
import { standardUnitOfMeasureEnum } from './products.schema';
import { users } from './users.schema';
import { StandardUnitOfMeasure, TaxType } from '../../shared/types';
import { DiscountType } from '../../shared/types/common.enum';

/**
 * Customer Approval Status Enum
 */
export enum CustomerApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REQUESTED = 'requested',
}

export const customerApprovalStatusEnum = pgEnum('customer_approval_status', [
  CustomerApprovalStatus.PENDING,
  CustomerApprovalStatus.APPROVED,
  CustomerApprovalStatus.REJECTED,
  CustomerApprovalStatus.REQUESTED,
]);

// Standard Unit of Measure Enum imported from products.schema.ts
// Tax Type Enum imported from common-fields.schema.ts

/**
 * Service Order Line Type Enum
 */
export enum ServiceOrderLineType {
  DIAGNOSIS = 'diagnosis',
  LABOR = 'labor',
  PART = 'part',
  SERVICE = 'service',
  WARRANTY = 'warranty',
}

export const serviceOrderLineTypeEnum = pgEnum('service_order_line_type', [
  ServiceOrderLineType.DIAGNOSIS,
  ServiceOrderLineType.LABOR,
  ServiceOrderLineType.PART,
  ServiceOrderLineType.SERVICE,
  ServiceOrderLineType.WARRANTY,
]);

/**
 * Service Order Line Staff Role Type Enum
 */
export enum ServiceOrderLineStaffRoleType {
  PRIMARY = 'Primary',
  ASSISTANT = 'Assistant',
  SUPERVISOR = 'Supervisor',
}

export const serviceOrderLineStaffRoleTypeEnum = pgEnum(
  'service_order_line_staff_role_type',
  [
    ServiceOrderLineStaffRoleType.PRIMARY,
    ServiceOrderLineStaffRoleType.ASSISTANT,
    ServiceOrderLineStaffRoleType.SUPERVISOR,
  ],
);

// Discount Type Enum is imported from common-fields.schema.ts

/**
 * Service Order Payment Status Enum
 */
export enum ServiceOrderPaymentStatus {
  PENDING = 'pending',
  PARTIAL = 'partial',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
}

export const serviceOrderPaymentStatusEnum = pgEnum(
  'service_order_payment_status',
  [
    ServiceOrderPaymentStatus.PENDING,
    ServiceOrderPaymentStatus.PARTIAL,
    ServiceOrderPaymentStatus.PAID,
    ServiceOrderPaymentStatus.OVERDUE,
    ServiceOrderPaymentStatus.CANCELLED,
  ],
);

/**
 * Service Orders Schema
 * Main table for managing service orders throughout their lifecycle
 * from creation to completion, including cost tracking and customer feedback
 */
export const serviceOrders = pgTable(
  'service_orders',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Business identifier
    orderNumber: varchar('order_number', { length: 50 }).notNull(),

    // Foreign key references
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),
    appointmentId: uuid('appointment_id').references(() => appointments.id),
    statusId: uuid('status_id')
      .notNull()
      .references(() => serviceOrderStatuses.id),
    priorityId: uuid('priority_id')
      .notNull()
      .references(() => serviceOrderPriorities.id),
    serviceTypeId: uuid('service_type_id')
      .notNull()
      .references(() => serviceOrderTypes.id),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),

    // Scheduling and timing
    orderDate: timestamp('order_date').notNull(),
    scheduledDate: timestamp('scheduled_date'),
    actualStartDate: timestamp('actual_start_date'),
    actualEndDate: timestamp('actual_end_date'),
    promisedDate: timestamp('promised_date'),

    // Order details
    description: text('description'),

    // Cost tracking
    estimatedCost: decimal('estimated_cost', { precision: 15, scale: 2 }),
    actualCost: decimal('actual_cost', { precision: 15, scale: 2 }),
    totalAmount: decimal('total_amount', { precision: 15, scale: 2 }),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),

    // Staff assignments
    receivedById: uuid('received_by_id')
      .notNull()
      .references(() => staffMembers.id),

    // Customer approval and feedback
    customerApprovalStatus: customerApprovalStatusEnum(
      'customer_approval_status',
    )
      .default(CustomerApprovalStatus.PENDING)
      .notNull(),
    customerApprovalDate: timestamp('customer_approval_date'),
    cancellationReason: text('cancellation_reason'),

    // Payment status
    paymentStatus: serviceOrderPaymentStatusEnum('payment_status')
      .default(ServiceOrderPaymentStatus.PENDING)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_orders'),
    // Primary indexes
    orderNumberIndex: index('service_orders_order_number_index').on(
      t.orderNumber,
    ),
    customerIdIndex: index('service_orders_customer_id_index').on(t.customerId),
    appointmentIdIndex: index('service_orders_appointment_id_index').on(
      t.appointmentId,
    ),
    statusIdIndex: index('service_orders_status_id_index').on(t.statusId),
    priorityIdIndex: index('service_orders_priority_id_index').on(t.priorityId),
    serviceTypeIdIndex: index('service_orders_service_type_id_index').on(
      t.serviceTypeId,
    ),
    locationIdIndex: index('service_orders_location_id_index').on(t.locationId),
    receivedByIdIndex: index('service_orders_received_by_id_index').on(
      t.receivedById,
    ),

    // Date indexes
    orderDateIndex: index('service_orders_order_date_index').on(t.orderDate),
    scheduledDateIndex: index('service_orders_scheduled_date_index').on(
      t.scheduledDate,
    ),
    actualStartDateIndex: index('service_orders_actual_start_date_index').on(
      t.actualStartDate,
    ),
    actualEndDateIndex: index('service_orders_actual_end_date_index').on(
      t.actualEndDate,
    ),
    promisedDateIndex: index('service_orders_promised_date_index').on(
      t.promisedDate,
    ),

    // Status and approval indexes
    customerApprovalStatusIndex: index(
      'service_orders_customer_approval_status_index',
    ).on(t.customerApprovalStatus),
    customerApprovalDateIndex: index(
      'service_orders_customer_approval_date_index',
    ).on(t.customerApprovalDate),

    // Composite indexes for common query patterns
    businessStatusIndex: index('service_orders_business_status_index')
      .on(t.businessId, t.statusId)
      .where(sql`${t.isDeleted} = false`),
    businessPriorityIndex: index('service_orders_business_priority_index')
      .on(t.businessId, t.priorityId)
      .where(sql`${t.isDeleted} = false`),
    businessLocationIndex: index('service_orders_business_location_index')
      .on(t.businessId, t.locationId)
      .where(sql`${t.isDeleted} = false`),
    businessCustomerIndex: index('service_orders_business_customer_index')
      .on(t.businessId, t.customerId)
      .where(sql`${t.isDeleted} = false`),
    businessOrderDateIndex: index('service_orders_business_order_date_index')
      .on(t.businessId, t.orderDate)
      .where(sql`${t.isDeleted} = false`),
    businessScheduledDateIndex: index(
      'service_orders_business_scheduled_date_index',
    )
      .on(t.businessId, t.scheduledDate)
      .where(sql`${t.isDeleted} = false`),

    // Staff and location composite indexes
    locationStatusIndex: index('service_orders_location_status_index')
      .on(t.locationId, t.statusId)
      .where(sql`${t.isDeleted} = false`),
    locationOrderDateIndex: index('service_orders_location_order_date_index')
      .on(t.locationId, t.orderDate)
      .where(sql`${t.isDeleted} = false`),

    // Customer and status composite indexes
    customerStatusIndex: index('service_orders_customer_status_index')
      .on(t.customerId, t.statusId)
      .where(sql`${t.isDeleted} = false`),
    customerOrderDateIndex: index('service_orders_customer_order_date_index')
      .on(t.customerId, t.orderDate)
      .where(sql`${t.isDeleted} = false`),

    // Staff workload indexes

    // Status and priority management
    statusPriorityIndex: index('service_orders_status_priority_index')
      .on(t.statusId, t.priorityId)
      .where(sql`${t.isDeleted} = false`),
    statusOrderDateIndex: index('service_orders_status_order_date_index')
      .on(t.statusId, t.orderDate)
      .where(sql`${t.isDeleted} = false`),

    // Customer approval workflow
    customerApprovalWorkflowIndex: index(
      'service_orders_customer_approval_workflow_index',
    )
      .on(t.customerId, t.customerApprovalStatus)
      .where(sql`${t.isDeleted} = false`),

    // Service type analysis
    serviceTypeStatusIndex: index('service_orders_service_type_status_index')
      .on(t.serviceTypeId, t.statusId)
      .where(sql`${t.isDeleted} = false`),
    serviceTypeLocationIndex: index(
      'service_orders_service_type_location_index',
    )
      .on(t.serviceTypeId, t.locationId)
      .where(sql`${t.isDeleted} = false`),

    // Financial reporting indexes
    totalAmountIndex: index('service_orders_total_amount_index').on(
      t.totalAmount,
    ),
    businessTotalAmountIndex: index(
      'service_orders_business_total_amount_index',
    )
      .on(t.businessId, t.totalAmount)
      .where(sql`${t.isDeleted} = false`),
    locationTotalAmountIndex: index(
      'service_orders_location_total_amount_index',
    )
      .on(t.locationId, t.totalAmount)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueBusinessOrderNumber: uniqueIndex(
      'service_orders_business_order_number_unique',
    )
      .on(t.businessId, t.orderNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

/**
 * Service Order Items Schema
 * Junction table for service orders and items/parts used
 */
export const serviceOrderItems = pgTable(
  'service_order_items',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    serviceOrderId: uuid('service_order_id')
      .notNull()
      .references(() => serviceOrders.id, { onDelete: 'cascade' }),
    itemId: uuid('item_id').notNull(), // Reference to items/products table
    quantity: decimal('quantity', { precision: 10, scale: 3 }).notNull(),
    unitPrice: decimal('unit_price', { precision: 15, scale: 2 }).notNull(),
    totalPrice: decimal('total_price', { precision: 15, scale: 2 }).notNull(),

    // Unit of measure
    standardUnitOfMeasure: standardUnitOfMeasureEnum('standard_unit_of_measure')
      .default(StandardUnitOfMeasure.PIECES)
      .notNull(),
    customUnitId: uuid('custom_unit_id').references(() => units.id),

    description: text('description'),

    // Standard audit and business fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_order_items'),
    serviceOrderIdIndex: index('service_order_items_service_order_id_index').on(
      t.serviceOrderId,
    ),
    itemIdIndex: index('service_order_items_item_id_index').on(t.itemId),
    standardUnitOfMeasureIndex: index(
      'service_order_items_standard_unit_of_measure_index',
    ).on(t.standardUnitOfMeasure),
    customUnitIdIndex: index('service_order_items_custom_unit_id_index').on(
      t.customUnitId,
    ),

    // Composite indexes
    serviceOrderItemIndex: index('service_order_items_service_order_item_index')
      .on(t.serviceOrderId, t.itemId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate items in same service order
    uniqueServiceOrderItem: uniqueIndex(
      'service_order_items_service_order_item_unique',
    )
      .on(t.serviceOrderId, t.itemId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

/**
 * Service Order Status History Schema
 * Tracks status changes throughout the service order lifecycle
 */
export const serviceOrderStatusHistory = pgTable(
  'service_order_status_history',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    serviceOrderId: uuid('service_order_id')
      .notNull()
      .references(() => serviceOrders.id, { onDelete: 'cascade' }),
    fromStatusId: uuid('from_status_id').references(
      () => serviceOrderStatuses.id,
    ),
    toStatusId: uuid('to_status_id')
      .notNull()
      .references(() => serviceOrderStatuses.id),
    changedById: uuid('changed_by_id')
      .notNull()
      .references(() => staffMembers.id),
    changedAt: timestamp('changed_at').notNull(),
    notes: text('notes'),

    // Standard audit and business fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_order_status_history'),
    serviceOrderIdIndex: index(
      'service_order_status_history_service_order_id_index',
    ).on(t.serviceOrderId),
    fromStatusIdIndex: index(
      'service_order_status_history_from_status_id_index',
    ).on(t.fromStatusId),
    toStatusIdIndex: index(
      'service_order_status_history_to_status_id_index',
    ).on(t.toStatusId),
    changedByIdIndex: index(
      'service_order_status_history_changed_by_id_index',
    ).on(t.changedById),
    changedAtIndex: index('service_order_status_history_changed_at_index').on(
      t.changedAt,
    ),

    // Composite indexes
    serviceOrderChangedAtIndex: index(
      'service_order_status_history_service_order_changed_at_index',
    )
      .on(t.serviceOrderId, t.changedAt)
      .where(sql`${t.isDeleted} = false`),
    statusTransitionIndex: index(
      'service_order_status_history_status_transition_index',
    )
      .on(t.fromStatusId, t.toStatusId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

/**
 * Service Order Diagnostics Schema
 * Tracks diagnostic tests and results performed on service orders
 */
export const serviceOrderDiagnostics = pgTable(
  'service_order_diagnostics',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    serviceOrderId: uuid('service_order_id')
      .notNull()
      .references(() => serviceOrders.id, { onDelete: 'cascade' }),

    // Diagnostic information
    testPerformed: text('test_performed').notNull(),
    testResult: text('test_result'),
    issueFound: text('issue_found'),
    recommendedAction: text('recommended_action'),

    // Parts and repair information
    requiresParts: boolean('requires_parts').default(false).notNull(),
    partsNeeded: text('parts_needed'),
    estimatedRepairTime: decimal('estimated_repair_time', {
      precision: 8,
      scale: 2,
    }), // in hours

    // Staff and timing
    diagnosedById: uuid('diagnosed_by_id')
      .notNull()
      .references(() => staffMembers.id),
    diagnosticDate: timestamp('diagnostic_date').notNull(),

    // Notes
    diagnosticNotes: text('diagnostic_notes'),

    // Standard audit and business fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_order_diagnostics'),
    serviceOrderIdIndex: index(
      'service_order_diagnostics_service_order_id_index',
    ).on(t.serviceOrderId),
    diagnosedByIdIndex: index(
      'service_order_diagnostics_diagnosed_by_id_index',
    ).on(t.diagnosedById),
    diagnosticDateIndex: index(
      'service_order_diagnostics_diagnostic_date_index',
    ).on(t.diagnosticDate),
    requiresPartsIndex: index(
      'service_order_diagnostics_requires_parts_index',
    ).on(t.requiresParts),
    testPerformedIndex: index(
      'service_order_diagnostics_test_performed_index',
    ).on(t.testPerformed),

    // Composite indexes for common query patterns
    serviceOrderDateIndex: index(
      'service_order_diagnostics_service_order_date_index',
    )
      .on(t.serviceOrderId, t.diagnosticDate)
      .where(sql`${t.isDeleted} = false`),
    diagnosedByDateIndex: index(
      'service_order_diagnostics_diagnosed_by_date_index',
    )
      .on(t.diagnosedById, t.diagnosticDate)
      .where(sql`${t.isDeleted} = false`),
    requiresPartsDateIndex: index(
      'service_order_diagnostics_requires_parts_date_index',
    )
      .on(t.requiresParts, t.diagnosticDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

/**
 * Service Order Lines Schema
 * Detailed line items for service orders including services, parts, labor, and warranty items
 */
export const serviceOrderLines = pgTable(
  'service_order_lines',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    serviceOrderId: uuid('service_order_id')
      .notNull()
      .references(() => serviceOrders.id, { onDelete: 'cascade' }),

    // Foreign key references
    serviceId: uuid('service_id').references(() => services.id),

    // Line details
    lineType: serviceOrderLineTypeEnum('line_type').notNull(),
    quantity: integer('quantity').notNull().default(1),
    unitPrice: decimal('unit_price', { precision: 15, scale: 2 }).notNull(),

    // Discount information
    discountType: discountTypeEnum('discount_type').default(
      DiscountType.PERCENTAGE,
    ),
    discountAmount: decimal('discount_amount', {
      precision: 15,
      scale: 2,
    }).default('0.00'),

    lineTotal: decimal('line_total', { precision: 15, scale: 2 }).notNull(),

    // Warranty and parts information
    isWarrantyItem: boolean('is_warranty_item').default(false).notNull(),

    // Additional information
    notes: text('notes'),

    // Standard audit and business fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_order_lines'),
    serviceOrderIdIndex: index('service_order_lines_service_order_id_index').on(
      t.serviceOrderId,
    ),
    serviceIdIndex: index('service_order_lines_service_id_index').on(
      t.serviceId,
    ),
    lineTypeIndex: index('service_order_lines_line_type_index').on(t.lineType),
    isWarrantyItemIndex: index('service_order_lines_is_warranty_item_index').on(
      t.isWarrantyItem,
    ),
    discountTypeIndex: index('service_order_lines_discount_type_index').on(
      t.discountType,
    ),

    // Composite indexes for common query patterns
    serviceOrderLineTypeIndex: index(
      'service_order_lines_service_order_line_type_index',
    )
      .on(t.serviceOrderId, t.lineType)
      .where(sql`${t.isDeleted} = false`),
    serviceOrderServiceIndex: index(
      'service_order_lines_service_order_service_index',
    )
      .on(t.serviceOrderId, t.serviceId)
      .where(sql`${t.isDeleted} = false`),
    lineTypeServiceIndex: index('service_order_lines_line_type_service_index')
      .on(t.lineType, t.serviceId)
      .where(sql`${t.isDeleted} = false`),
    warrantyItemsIndex: index('service_order_lines_warranty_items_index')
      .on(t.isWarrantyItem)
      .where(sql`${t.isDeleted} = false`),

    // Financial reporting indexes
    serviceOrderLineTotalIndex: index(
      'service_order_lines_service_order_line_total_index',
    )
      .on(t.serviceOrderId, t.lineTotal)
      .where(sql`${t.isDeleted} = false`),
    lineTypeLineTotalIndex: index(
      'service_order_lines_line_type_line_total_index',
    )
      .on(t.lineType, t.lineTotal)
      .where(sql`${t.isDeleted} = false`),
  }),
);

/**
 * Service Order Line Staff Schema
 * Junction table for tracking staff assignments to specific service order line items
 */
export const serviceOrderLineStaff = pgTable(
  'service_order_line_staff',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    lineId: uuid('line_id')
      .notNull()
      .references(() => serviceOrderLines.id, { onDelete: 'cascade' }),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id, { onDelete: 'cascade' }),
    taskId: uuid('task_id').references(() => tasks.id, {
      onDelete: 'set null',
    }),

    // Standard audit and business fields
    // Base entity fields

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'service_order_line_staff'),
    lineIdIndex: index('service_order_line_staff_line_id_index').on(t.lineId),
    staffIdIndex: index('service_order_line_staff_staff_id_index').on(
      t.staffId,
    ),
    taskIdIndex: index('service_order_line_staff_task_id_index').on(t.taskId),

    // Composite indexes for common query patterns
    lineStaffIndex: index('service_order_line_staff_line_staff_index')
      .on(t.lineId, t.staffId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate staff assignments per line
    uniqueLineStaff: uniqueIndex('service_order_line_staff_line_staff_unique')
      .on(t.lineId, t.staffId)
      .where(sql`${t.isDeleted} = false`),
  }),
);
