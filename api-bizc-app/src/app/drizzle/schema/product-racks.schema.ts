import {
  pgTable,
  uuid,
  text,
  index,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { products } from './products.schema';
import { locations } from './locations.schema';
import { business } from './business.schema';
import { createBaseEntityFields } from './common-fields.schema';

import { users } from './users.schema';
export const productRacks = pgTable(
  'product_racks',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id),
    rack: text('rack'),
    row: text('row'),
    position: text('position'),
  },
  (t) => ({
    businessIdIndex: index('product_racks_business_id_index').on(t.businessId),
    locationIdIndex: index('product_racks_location_id_index').on(t.locationId),
    productIdIndex: index('product_racks_product_id_index').on(t.productId),
  }),
);

// Relations
export const productRacksRelations = relations(productRacks, ({ one }) => ({
  business: one(business, {
    fields: [productRacks.businessId],
    references: [business.id],
  }),
  location: one(locations, {
    fields: [productRacks.locationId],
    references: [locations.id],
  }),
  product: one(products, {
    fields: [productRacks.productId],
    references: [products.id],
  }),
  createdByUser: one(users, {
    fields: [productRacks.createdBy],
    references: [users.id],
  }),
  updatedByUser: one(users, {
    fields: [productRacks.updatedBy],
    references: [users.id],
  }),
}));
