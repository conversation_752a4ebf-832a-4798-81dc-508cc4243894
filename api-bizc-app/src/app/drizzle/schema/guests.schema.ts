import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  boolean,
  date,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { customers } from './customers.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
// Guest Status Constants
export enum GuestStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BLOCKED = 'BLOCKED',
}

// Identification Type Constants
export enum IdentificationType {
  PASSPORT = 'PASSPORT',
  NATIONAL_ID = 'NATIONAL_ID',
  DRIVING_LICENSE = 'DRIVING_LICENSE',
  VISA = 'VISA',
  OTHER = 'OTHER',
}

// Guest Type Constants
export enum GuestType {
  ACCOMMODATION = 'ACCOMMODATION',
  VEHICLE = 'VEHICLE',
  PACKAGE = 'PACKAGE',
  EVENT_SPACE = 'EVENT_SPACE',
}

// Guest Relationship Constants
export enum GuestRelationship {
  SPOUSE = 'SPOUSE',
  PARTNER = 'PARTNER',
  CHILD = 'CHILD',
  PARENT = 'PARENT',
  SIBLING = 'SIBLING',
  FRIEND = 'FRIEND',
  COLLEAGUE = 'COLLEAGUE',
  RELATIVE = 'RELATIVE',
  OTHER = 'OTHER',
}

// Guest Status Enum
export const guestStatusEnum = pgEnum('guest_status', [
  GuestStatus.ACTIVE,
  GuestStatus.INACTIVE,
  GuestStatus.BLOCKED,
]);

// Identification Type Enum
export const identificationTypeEnum = pgEnum('identification_type', [
  IdentificationType.PASSPORT,
  IdentificationType.NATIONAL_ID,
  IdentificationType.DRIVING_LICENSE,
  IdentificationType.VISA,
  IdentificationType.OTHER,
]);

// Guest Type Enum - tracks the source of the guest reservation
export const guestTypeEnum = pgEnum('guest_type', [
  GuestType.ACCOMMODATION,
  GuestType.VEHICLE,
  GuestType.PACKAGE,
  GuestType.EVENT_SPACE,
]);

// Guest Relationship Enum - defines relationships between primary guest and companions
export const guestRelationshipEnum = pgEnum('guest_relationship', [
  GuestRelationship.SPOUSE,
  GuestRelationship.PARTNER,
  GuestRelationship.CHILD,
  GuestRelationship.PARENT,
  GuestRelationship.SIBLING,
  GuestRelationship.FRIEND,
  GuestRelationship.COLLEAGUE,
  GuestRelationship.RELATIVE,
  GuestRelationship.OTHER,
]);

// Guests table
export const guests = pgTable(
  'guests',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Customer Reference (guests are always customers)
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),

    // Guest-specific Information
    guestNumber: text('guest_number').notNull(), // Unique guest identifier for accommodation system

    // Accommodation-specific Basic Details (not in customer table)
    dateOfBirth: date('date_of_birth'), // Needed for age verification, room assignments
    gender: text('gender'), // Important for room assignments and preferences
    nationality: text('nationality'), // Required for legal/visa requirements

    // Identification (accommodation-specific requirements)
    identificationType: identificationTypeEnum('identification_type'),
    identificationNumber: text('identification_number'),
    identificationIssueDate: date('identification_issue_date'),
    identificationExpiryDate: date('identification_expiry_date'),
    identificationIssuingCountry: text('identification_issuing_country'),

    // Guest Type - tracks the source of the guest reservation
    guestType: guestTypeEnum('guest_type').notNull(),

    // Guest Status
    status: guestStatusEnum('status').default(GuestStatus.ACTIVE).notNull(),

    // Additional flexible key fields
    key1: text('key1'),
    key2: text('key2'),
    key3: text('key3'),
    key4: text('key4'),
    key5: text('key5'),
    key6: text('key6'),
    key7: text('key7'),
    key8: text('key8'),
    key9: text('key9'),
    key10: text('key10'),

    // Activity Tracking
    lastServiceDate: timestamp('last_service_date'),
    lastContactDate: timestamp('last_contact_date'),

    // Guest-specific Notes (separate from customer notes)
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'guests'),
    customerIdIndex: index('guests_customer_id_index').on(t.customerId),
    guestNumberIndex: index('guests_guest_number_index').on(t.guestNumber),

    // Guest-specific information indexes
    dateOfBirthIndex: index('guests_date_of_birth_index').on(t.dateOfBirth),
    genderIndex: index('guests_gender_index').on(t.gender),
    nationalityIndex: index('guests_nationality_index').on(t.nationality),

    // Identification indexes
    identificationTypeIndex: index('guests_identification_type_index').on(
      t.identificationType,
    ),
    identificationNumberIndex: index('guests_identification_number_index').on(
      t.identificationNumber,
    ),

    // Guest type indexes
    guestTypeIndex: index('guests_guest_type_index').on(t.guestType),

    // Status and activity indexes
    statusIndex: index('guests_status_index').on(t.status),
    lastServiceDateIndex: index('guests_last_service_date_index').on(
      t.lastServiceDate,
    ),
    lastContactDateIndex: index('guests_last_contact_date_index').on(
      t.lastContactDate,
    ),

    // Composite indexes for common queries
    businessStatusIndex: index('guests_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessCustomerIndex: index('guests_business_customer_index').on(
      t.businessId,
      t.customerId,
    ),
    businessIdentificationIndex: index(
      'guests_business_identification_index',
    ).on(t.businessId, t.identificationType, t.identificationNumber),
    businessLastServiceIndex: index('guests_business_last_service_index').on(
      t.businessId,
      t.lastServiceDate,
    ),
    businessGuestTypeIndex: index('guests_business_guest_type_index').on(
      t.businessId,
      t.guestType,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessGuestNumber: uniqueIndex(
      'guests_business_guest_number_unique',
    )
      .on(t.businessId, t.guestNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessCustomer: uniqueIndex('guests_business_customer_unique')
      .on(t.businessId, t.customerId)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessIdentification: uniqueIndex(
      'guests_business_identification_unique',
    )
      .on(t.businessId, t.identificationType, t.identificationNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Guest Emergency Contacts table (separate table for multiple emergency contacts)
export const guestEmergencyContacts = pgTable(
  'guest_emergency_contacts',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    guestId: uuid('guest_id')
      .notNull()
      .references(() => guests.id, { onDelete: 'cascade' }),

    // Contact Details
    name: text('name').notNull(),
    relationship: text('relationship').notNull(), // Spouse, Parent, Child, Friend, etc.
    phoneNumber: text('phone_number').notNull(),
    alternatePhoneNumber: text('alternate_phone_number'),
    email: text('email'),
    address: text('address'),

    // Priority and notes
    notes: text('notes'),
    priority: text('priority').default('PRIMARY').notNull(), // PRIMARY, SECONDARY

    // Verification status
    isVerified: boolean('is_verified').default(false).notNull(),
    lastVerifiedDate: timestamp('last_verified_date'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'guest_emergency_contacts'),
    guestIdIndex: index('guest_emergency_contacts_guest_id_index').on(
      t.guestId,
    ),
    nameIndex: index('guest_emergency_contacts_name_index').on(t.name),
    relationshipIndex: index('guest_emergency_contacts_relationship_index').on(
      t.relationship,
    ),
    phoneNumberIndex: index('guest_emergency_contacts_phone_number_index').on(
      t.phoneNumber,
    ),
    priorityIndex: index('guest_emergency_contacts_priority_index').on(
      t.priority,
    ),
    isVerifiedIndex: index('guest_emergency_contacts_is_verified_index').on(
      t.isVerified,
    ),

    // Composite indexes
    guestPriorityIndex: index(
      'guest_emergency_contacts_guest_priority_index',
    ).on(t.guestId, t.priority),
  }),
);

// Relations
export const guestsRelations = relations(guests, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [guests.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [guests.createdBy],
    references: [users.id],
    relationName: 'createdGuests',
  }),
  updater: one(users, {
    fields: [guests.updatedBy],
    references: [users.id],
    relationName: 'updatedGuests',
  }),

  // Customer relation
  customer: one(customers, {
    fields: [guests.customerId],
    references: [customers.id],
  }),

  // One-to-many relations
  guestEmergencyContacts: many(guestEmergencyContacts),
}));

export const guestEmergencyContactsRelations = relations(
  guestEmergencyContacts,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [guestEmergencyContacts.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [guestEmergencyContacts.createdBy],
      references: [users.id],
      relationName: 'createdGuestEmergencyContacts',
    }),
    updater: one(users, {
      fields: [guestEmergencyContacts.updatedBy],
      references: [users.id],
      relationName: 'updatedGuestEmergencyContacts',
    }),

    // Guest relation
    guest: one(guests, {
      fields: [guestEmergencyContacts.guestId],
      references: [guests.id],
    }),
  }),
);
