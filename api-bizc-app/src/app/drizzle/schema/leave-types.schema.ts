import {
  boolean,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  uniqueIndex,
  uuid,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { business } from './business.schema';
import { users } from './users.schema';
import { LeaveTypeStatus } from '../../shared/types';
import { leaveRequests } from './leave-requests.schema';
import { leaveBalances } from './leave-balances.schema';

export const leaveTypeStatusEnum = pgEnum('leave_type_status', [
  LeaveTypeStatus.ACTIVE,
  LeaveTypeStatus.INACTIVE,
]);

export const leaveTypes = pgTable(
  'leave_types',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    leaveCode: text('leave_code').notNull(),
    leaveName: text('leave_name').notNull(),
    daysAllowedPerYear: decimal('days_allowed_per_year', {
      precision: 5,
      scale: 2,
    }).notNull(),
    isPaid: boolean('is_paid').default(true).notNull(),
    carriesForward: boolean('carries_forward').default(false).notNull(),
    description: text('description'),
    status: leaveTypeStatusEnum('status')
      .default(LeaveTypeStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('leave_types_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leave_types'),
    leaveCodeIndex: index('leave_types_leave_code_index').on(t.leaveCode),
    leaveNameIndex: index('leave_types_leave_name_index').on(t.leaveName),
    statusIndex: index('leave_types_status_index').on(t.status),
    uniqueBusinessLeaveCode: uniqueIndex(
      'leave_types_business_leave_code_unique',
    )
      .on(t.businessId, t.leaveCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Leave Types Relations
export const leaveTypesRelations = relations(leaveTypes, ({ one, many }) => ({
  business: one(business, {
    fields: [leaveTypes.businessId],
    references: [business.id],
  }),
  createdBy: one(users, {
    fields: [leaveTypes.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [leaveTypes.updatedBy],
    references: [users.id],
  }),
  leaveRequests: many(leaveRequests),
  leaveBalances: many(leaveBalances),
}));
