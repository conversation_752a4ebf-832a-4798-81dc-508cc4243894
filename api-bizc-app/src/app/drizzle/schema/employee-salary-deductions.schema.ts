import {
  boolean,
  decimal,
  index,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';

import { business } from './business.schema';
import { employeeSalaries } from './employee-salary.schema';
import { deductionTypes } from './deduction-types.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
/**
 * Employee Salary Deductions Table
 * Links employee salaries with their deductions and stores specific deduction amounts
 */
export const employeeSalaryDeductions = pgTable(
  'employee_salary_deductions',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    employeeSalaryId: uuid('employee_salary_id')
      .notNull()
      .references(() => employeeSalaries.id),
    deductionTypeId: uuid('deduction_type_id')
      .notNull()
      .references(() => deductionTypes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'employee_salary_deductions'),
    employeeSalaryIdIndex: index(
      'employee_salary_deductions_employee_salary_id_index',
    ).on(t.employeeSalaryId),
    deductionTypeIdIndex: index(
      'employee_salary_deductions_deduction_type_id_index',
    ).on(t.deductionTypeId),
    uniqueEmployeeSalaryDeductionActive: uniqueIndex(
      'employee_salary_deductions_employee_salary_deduction_active_unique',
    )
      .on(t.employeeSalaryId, t.deductionTypeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const employeeSalaryDeductionsRelations = relations(
  employeeSalaryDeductions,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [employeeSalaryDeductions.businessId],
      references: [business.id],
    }),
    // Employee salary relation
    employeeSalary: one(employeeSalaries, {
      fields: [employeeSalaryDeductions.employeeSalaryId],
      references: [employeeSalaries.id],
    }),
    // Deduction type relation
    deductionType: one(deductionTypes, {
      fields: [employeeSalaryDeductions.deductionTypeId],
      references: [deductionTypes.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [employeeSalaryDeductions.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [employeeSalaryDeductions.updatedBy],
      references: [users.id],
    }),
  }),
);
