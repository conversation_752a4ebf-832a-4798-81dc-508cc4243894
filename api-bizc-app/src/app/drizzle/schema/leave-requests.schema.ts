import {
  boolean,
  date,
  decimal,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { leaveTypes } from './leave-types.schema';
import { users } from './users.schema';
import { LeaveRequestStatus } from '../../shared/types';

export const leaveRequestStatusEnum = pgEnum('leave_request_status', [
  LeaveRequestStatus.DRAFT,
  LeaveRequestStatus.SUBMITTED,
  LeaveRequestStatus.PENDING,
  LeaveRequestStatus.APPROVED,
  LeaveRequestStatus.REJECTED,
  LeaveRequestStatus.CANCELLED,
  LeaveRequestStatus.REVOKED,
  LeaveRequestStatus.EXPIRED,
  LeaveRequestStatus.PARTIALLY_APPROVED,
  LeaveRequestStatus.ON_HOLD,
]);

export const leaveRequests = pgTable(
  'leave_requests',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    employeeId: uuid('employee_id')
      .notNull()
      .references(() => staffMembers.id),
    leaveTypeId: uuid('leave_type_id')
      .notNull()
      .references(() => leaveTypes.id),
    startDate: date('start_date').notNull(),
    endDate: date('end_date').notNull(),
    daysRequested: decimal('days_requested', {
      precision: 5,
      scale: 2,
    }).notNull(),
    reason: text('reason'),
    status: leaveRequestStatusEnum('status')
      .default(LeaveRequestStatus.DRAFT)
      .notNull(),
    approvedBy: uuid('approved_by').references(() => staffMembers.id),
    approvalDate: timestamp('approval_date'),
    comments: text('comments'),
  },
  (t) => ({
    idIndex: index('leave_requests_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'leave_requests'),
    employeeIdIndex: index('leave_requests_employee_id_index').on(t.employeeId),
    leaveTypeIdIndex: index('leave_requests_leave_type_id_index').on(
      t.leaveTypeId,
    ),
    statusIndex: index('leave_requests_status_index').on(t.status),
    startDateIndex: index('leave_requests_start_date_index').on(t.startDate),
    endDateIndex: index('leave_requests_end_date_index').on(t.endDate),
    approvedByIndex: index('leave_requests_approved_by_index').on(t.approvedBy),
  }),
);

// Leave Requests Relations
export const leaveRequestsRelations = relations(leaveRequests, ({ one }) => ({
  business: one(business, {
    fields: [leaveRequests.businessId],
    references: [business.id],
  }),
  employee: one(staffMembers, {
    fields: [leaveRequests.employeeId],
    references: [staffMembers.id],
  }),
  leaveType: one(leaveTypes, {
    fields: [leaveRequests.leaveTypeId],
    references: [leaveTypes.id],
  }),
  approvedBy: one(staffMembers, {
    fields: [leaveRequests.approvedBy],
    references: [staffMembers.id],
  }),
  createdBy: one(users, {
    fields: [leaveRequests.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [leaveRequests.updatedBy],
    references: [users.id],
  }),
}));
