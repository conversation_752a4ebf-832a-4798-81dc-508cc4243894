import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  uniqueIndex,
  uuid,
  varchar,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import {
  ServiceCategory,
  ServiceOrderTypeStatus,
} from '../../shared/types/service-order.enum';

export const serviceCategoryEnum = pgEnum('service_category', [
  ServiceCategory.REPAIR,
  ServiceCategory.MAINTENANCE,
  ServiceCategory.INSTALLATION,
  ServiceCategory.CONSULTATION,
  ServiceCategory.WARRANTY,
]);

export const serviceOrderTypeStatusEnum = pgEnum('service_order_type_status', [
  ServiceOrderTypeStatus.ACTIVE,
  ServiceOrderTypeStatus.INACTIVE,
]);

/**
 * Service Order Types Schema
 * Defines different types of service orders with their characteristics
 * including category, requirements, and workflow templates
 */
export const serviceOrderTypes = pgTable(
  'service_order_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    typeCode: varchar('type_code', { length: 50 }).notNull(),
    typeName: varchar('type_name', { length: 100 }).notNull(),
    category: serviceCategoryEnum('category').notNull(),
    requiresItems: boolean('requires_items').default(false).notNull(),
    requiresDiagnostics: boolean('requires_diagnostics')
      .default(false)
      .notNull(),
    workflowTemplate: text('workflow_template'),
    status: serviceOrderTypeStatusEnum('status')
      .default(ServiceOrderTypeStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    // Indexes for performance optimization
    businessIdIndex: index('service_order_types_business_id_index').on(
      t.businessId,
    ),
    typeCodeIndex: index('service_order_types_type_code_index').on(t.typeCode),
    categoryIndex: index('service_order_types_category_index').on(t.category),
    statusIndex: index('service_order_types_status_index').on(t.status),

    // Composite indexes for common query patterns
    businessActiveIndex: index('service_order_types_business_active_index').on(
      t.businessId,
      t.status,
    ),
    businessCategoryIndex: index(
      'service_order_types_business_category_index',
    ).on(t.businessId, t.category),

    // Unique constraints with soft deletion support
    uniqueBusinessTypeCode: uniqueIndex(
      'service_order_types_business_type_code_unique',
    )
      .on(t.businessId, t.typeCode)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessTypeName: uniqueIndex(
      'service_order_types_business_type_name_unique',
    )
      .on(t.businessId, t.typeName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const serviceOrderTypesRelations = relations(
  serviceOrderTypes,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceOrderTypes.businessId],
      references: [business.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [serviceOrderTypes.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [serviceOrderTypes.updatedBy],
      references: [users.id],
    }),
  }),
);
