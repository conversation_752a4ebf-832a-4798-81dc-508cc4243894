import {
  index,
  pgEnum,
  pgTable,
  text,
  uuid,
  decimal,
  date,
  boolean,
  integer,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
/**
 * Business Subscriptions Schema
 *
 * This schema manages actual business subscriptions to various plans.
 * It tracks subscription lifecycle, billing details, and current status.
 */

// Subscription Status Enum
export enum SubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  CANCELLED = 'cancelled',
  PENDING = 'pending',
  TRIAL = 'trial',
}

export const subscriptionStatusEnum = pgEnum('subscription_status', [
  SubscriptionStatus.ACTIVE,
  SubscriptionStatus.INACTIVE,
  SubscriptionStatus.SUSPENDED,
  SubscriptionStatus.CANCELLED,
  SubscriptionStatus.PENDING,
  SubscriptionStatus.TRIAL,
]);

// Billing Type Enum
export enum BillingType {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
}

export const billingTypeEnum = pgEnum('billing_type', [
  BillingType.MANUAL,
  BillingType.AUTOMATIC,
]);

// Subscription Period Enum
export enum SubscriptionPeriod {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
}

export const subscriptionPeriodEnum = pgEnum('subscription_period', [
  SubscriptionPeriod.MONTHLY,
  SubscriptionPeriod.QUARTERLY,
  SubscriptionPeriod.YEARLY,
]);

// Main business subscriptions table
export const businessSubscriptions = pgTable(
  'business_subscriptions',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Subscription details
    planName: text('plan_name').notNull(),
    planDescription: text('plan_description'),
    status: subscriptionStatusEnum('status')
      .default(SubscriptionStatus.PENDING)
      .notNull(),

    // Billing information
    billingType: billingTypeEnum('billing_type')
      .default(BillingType.MANUAL)
      .notNull(),
    subscriptionPeriod: subscriptionPeriodEnum('subscription_period')
      .default(SubscriptionPeriod.MONTHLY)
      .notNull(),

    // Pricing
    monthlyPrice: decimal('monthly_price', {
      precision: 15,
      scale: 2,
    }).notNull(),
    quarterlyPrice: decimal('quarterly_price', { precision: 15, scale: 2 }),
    yearlyPrice: decimal('yearly_price', { precision: 15, scale: 2 }),
    currency: text('currency').default('LKR').notNull(),

    // Subscription lifecycle dates
    startDate: date('start_date').notNull(),
    endDate: date('end_date'),
    trialStartDate: date('trial_start_date'),
    trialEndDate: date('trial_end_date'),

    // Next billing information
    nextBillingDate: date('next_billing_date'),
    lastBillingDate: date('last_billing_date'),

    // Payment terms
    paymentDueDays: integer('payment_due_days').default(30).notNull(),

    // Feature limits and usage tracking
    maxUsers: integer('max_users'),
    maxStorage: integer('max_storage'), // in GB
    maxProjects: integer('max_projects'),

    // Additional settings
    autoRenewal: boolean('auto_renewal').default(true).notNull(),
    gracePeriodDays: integer('grace_period_days').default(7).notNull(),

    // Notes and metadata
    notes: text('notes'),
    metadata: text('metadata'), // JSON string for additional plan-specific data
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'business_subscriptions'),

    // Status and billing indexes
    statusIndex: index('business_subscriptions_status_index').on(t.status),
    billingTypeIndex: index('business_subscriptions_billing_type_index').on(
      t.billingType,
    ),
    subscriptionPeriodIndex: index(
      'business_subscriptions_subscription_period_index',
    ).on(t.subscriptionPeriod),

    // Date-based indexes for billing and lifecycle management
    nextBillingDateIndex: index(
      'business_subscriptions_next_billing_date_index',
    ).on(t.nextBillingDate),
    lastBillingDateIndex: index(
      'business_subscriptions_last_billing_date_index',
    ).on(t.lastBillingDate),
    startDateIndex: index('business_subscriptions_start_date_index').on(
      t.startDate,
    ),
    endDateIndex: index('business_subscriptions_end_date_index').on(t.endDate),
    trialEndDateIndex: index('business_subscriptions_trial_end_date_index').on(
      t.trialEndDate,
    ),

    // Composite indexes for common queries
    businessStatusIndex: index('business_subscriptions_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    activeSubscriptionsIndex: index('business_subscriptions_active_index')
      .on(t.status, t.nextBillingDate)
      .where(sql`${t.isDeleted} = false`),
    trialSubscriptionsIndex: index('business_subscriptions_trial_index')
      .on(t.status, t.trialEndDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Business Subscriptions Relations
export const businessSubscriptionsRelations = relations(
  businessSubscriptions,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [businessSubscriptions.businessId],
      references: [business.id],
      relationName: 'businessSubscriptions',
    }),

    // Audit relations
    creator: one(users, {
      fields: [businessSubscriptions.createdBy],
      references: [users.id],
      relationName: 'createdBusinessSubscriptions',
    }),
    updater: one(users, {
      fields: [businessSubscriptions.updatedBy],
      references: [users.id],
      relationName: 'updatedBusinessSubscriptions',
    }),
  }),
);
