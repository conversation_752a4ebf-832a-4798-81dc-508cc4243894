import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
  date,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { vehicleTypes } from './vehicle-categories.schema';
import { locations } from './locations.schema';
import { media } from './media.schema';
import { accounts } from './accounts.schema';
import { DepreciationDurationUnit, VehicleStatus } from '../../shared/types';

export const vehicleStatusEnum = pgEnum('vehicle_status', [
  VehicleStatus.AVAILABLE,
  VehicleStatus.DIRTY,
  VehicleStatus.RETURNED,
  VehicleStatus.OUT_OF_SERVICE,
  VehicleStatus.ON_SALE,
  VehicleStatus.SOLD,
  VehicleStatus.COMPLEMENTARY,
  VehicleStatus.NEW,
  VehicleStatus.RESERVED,
  VehicleStatus.STOLEN,
  VehicleStatus.RECOVERED,
  VehicleStatus.TOTALED,
  VehicleStatus.REPOSSESSED,
  VehicleStatus.INACTIVE,
  VehicleStatus.DELETED,
]);

// Depreciation Duration Unit Enum
export const depreciationDurationUnitEnum = pgEnum(
  'vehicle_depreciation_duration_unit',
  [DepreciationDurationUnit.MONTHS, DepreciationDurationUnit.YEARS],
);

export const vehicles = pgTable(
  'vehicles',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Vehicle Overview fields
    vin: text('vin'),
    vehicleKey: text('vehicle_key'),
    licensePlate: text('license_plate'),
    make: text('make'),
    model: text('model'),
    vehicleTypeId: uuid('vehicle_type_id').references(() => vehicleTypes.id),
    subTypeId: uuid('sub_type_id').references(() => vehicleTypes.id),
    year: integer('year'),
    color: text('color'),
    images: uuid('images').array(),
    availableDate: date('available_date'),
    availableUntil: date('available_until'),
    odometer: integer('odometer'),
    fuelLevel: integer('fuel_level'), // 0-100 percentage
    vehicleEntryDate: date('vehicle_entry_date'),
    vehicleExitDate: date('vehicle_exit_date'),
    currentLocationId: uuid('current_location_id').references(
      () => locations.id,
    ),

    // Media integration
    image: uuid('image').references(() => media.id),
    ogImage: uuid('og_image').references(() => media.id),

    // SEO and slug fields
    slug: text('slug'),
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),

    // Position for ordering
    position: integer('position').default(0).notNull(),

    // Rental availability and pricing
    availableForRent: boolean('available_for_rent').default(false).notNull(),
    rentalPriceWithDriver: decimal('rental_price_with_driver', {
      precision: 12,
      scale: 2,
    }),
    rentalPriceWithoutDriver: decimal('rental_price_without_driver', {
      precision: 12,
      scale: 2,
    }),

    // Fleet section fields
    hardwareId: text('hardware_id'),
    coolantTemperatureThreshold: decimal('coolant_temperature_threshold', {
      precision: 10,
      scale: 2,
    }),
    lowBatteryVoltageThreshold: decimal('low_battery_voltage_threshold', {
      precision: 10,
      scale: 2,
    }),

    // Depreciation Input fields
    purchasePrice: decimal('purchase_price', { precision: 12, scale: 2 }),
    expectedRestValue: decimal('expected_rest_value', {
      precision: 12,
      scale: 2,
    }),
    odometerAtPurchase: integer('odometer_at_purchase'),
    purchaseDate: date('purchase_date'),

    // Accounting Accounts
    fixedAssetAccountId: uuid('fixed_asset_account_id').references(
      () => accounts.id,
    ),
    depreciationAccountId: uuid('depreciation_account_id').references(
      () => accounts.id,
    ),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),

    // Additional fields
    deletedBy: uuid('deleted_by').references(() => users.id),
    status: vehicleStatusEnum('status')
      .default(VehicleStatus.AVAILABLE)
      .notNull(),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'vehicles'),
    vinIndex: index('vehicles_vin_index').on(t.vin),
    licensePlateIndex: index('vehicles_license_plate_index').on(t.licensePlate),
    vehicleTypeIdIndex: index('vehicles_vehicle_type_id_index').on(
      t.vehicleTypeId,
    ),
    subTypeIdIndex: index('vehicles_sub_type_id_index').on(t.subTypeId),
    currentLocationIdIndex: index('vehicles_current_location_id_index').on(
      t.currentLocationId,
    ),
    makeModelIndex: index('vehicles_make_model_index').on(t.make, t.model),
    imagesIndex: index('vehicles_images_index').on(t.images),
    fuelLevelIndex: index('vehicles_fuel_level_index').on(t.fuelLevel),

    // Media indexes
    imageIndex: index('vehicles_image_index').on(t.image),
    ogImageIndex: index('vehicles_og_image_index').on(t.ogImage),

    // SEO and slug indexes
    slugIndex: index('vehicles_slug_index').on(t.slug),

    // Position indexes
    positionIndex: index('vehicles_position_index').on(t.position),

    // Rental indexes
    availableForRentIndex: index('vehicles_available_for_rent_index').on(
      t.availableForRent,
    ),

    // Accounting indexes
    fixedAssetAccountIdIndex: index('vehicles_fixed_asset_account_id_index').on(
      t.fixedAssetAccountId,
    ),
    depreciationAccountIdIndex: index(
      'vehicles_depreciation_account_id_index',
    ).on(t.depreciationAccountId),
    expenseAccountIdIndex: index('vehicles_expense_account_id_index').on(
      t.expenseAccountId,
    ),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('vehicles_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'vehicles_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching with soft deletion support
    businessVinIndex: index('vehicles_business_vin_index')
      .on(t.businessId, t.vin)
      .where(sql`${t.isDeleted} = false`),
    businessLicensePlateIndex: index('vehicles_business_license_plate_index')
      .on(t.businessId, t.licensePlate)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('vehicles_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessAvailableForRentIndex: index(
      'vehicles_business_available_for_rent_index',
    )
      .on(t.businessId, t.availableForRent)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessVin: uniqueIndex('vehicles_business_vin_unique')
      .on(t.businessId, t.vin)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessLicensePlate: uniqueIndex(
      'vehicles_business_license_plate_unique',
    )
      .on(t.businessId, t.licensePlate)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('vehicles_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const vehiclesRelations = relations(vehicles, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [vehicles.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [vehicles.createdBy],
    references: [users.id],
    relationName: 'createdVehicles',
  }),
  updater: one(users, {
    fields: [vehicles.updatedBy],
    references: [users.id],
    relationName: 'updatedVehicles',
  }),
  deletedByUser: one(users, {
    fields: [vehicles.deletedBy],
    references: [users.id],
    relationName: 'deletedVehicles',
  }),

  // Vehicle type relations
  vehicleType: one(vehicleTypes, {
    fields: [vehicles.vehicleTypeId],
    references: [vehicleTypes.id],
    relationName: 'vehicleMainType',
  }),
  subType: one(vehicleTypes, {
    fields: [vehicles.subTypeId],
    references: [vehicleTypes.id],
    relationName: 'vehicleSubType',
  }),

  // Location relation
  currentLocation: one(locations, {
    fields: [vehicles.currentLocationId],
    references: [locations.id],
  }),

  // Media relations
  imageMedia: one(media, {
    fields: [vehicles.image],
    references: [media.id],
    relationName: 'vehicleImage',
  }),
  ogImageMedia: one(media, {
    fields: [vehicles.ogImage],
    references: [media.id],
    relationName: 'vehicleOgImage',
  }),

  // Account relations
  fixedAssetAccount: one(accounts, {
    fields: [vehicles.fixedAssetAccountId],
    references: [accounts.id],
    relationName: 'vehicleFixedAssetAccount',
  }),
  depreciationAccount: one(accounts, {
    fields: [vehicles.depreciationAccountId],
    references: [accounts.id],
    relationName: 'vehicleDepreciationAccount',
  }),
  expenseAccount: one(accounts, {
    fields: [vehicles.expenseAccountId],
    references: [accounts.id],
    relationName: 'vehicleExpenseAccount',
  }),
}));
