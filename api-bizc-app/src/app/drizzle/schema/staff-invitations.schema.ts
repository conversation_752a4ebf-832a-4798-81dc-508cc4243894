import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { DocumentStatus } from '../../shared/types';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export const invitationStatusEnum = pgEnum('invitation_status', [
  'PENDING',
  'ACCEPTED',
  'REJECTED',
  'EXPIRED',
]);

export const invitationDocumentStatusEnum = pgEnum(
  'invitation_document_status',
  [DocumentStatus.ACTIVE, DocumentStatus.ARCHIVED],
);

export const staffInvitations = pgTable(
  'staff_invitations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id),
    status: invitationStatusEnum('status').default('PENDING').notNull(),
    role: text('role'),
    message: text('message'),
    expiresAt: timestamp('expires_at').notNull(),
    acceptedAt: timestamp('accepted_at'),
    documentStatus: invitationDocumentStatusEnum('document_status')
      .default(DocumentStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('staff_invitations_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'staff_invitations'),
    staffMemberIdIndex: index('staff_invitations_staff_member_id_index').on(
      t.staffMemberId,
    ),
  }),
);

// Staff Invitations Relations
export const staffInvitationsRelations = relations(
  staffInvitations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [staffInvitations.businessId],
      references: [business.id],
      relationName: 'businessStaffInvitations',
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [staffInvitations.staffMemberId],
      references: [staffMembers.id],
      relationName: 'staffInvitations',
    }),

    // Audit relations
    creator: one(users, {
      fields: [staffInvitations.createdBy],
      references: [users.id],
      relationName: 'createdStaffInvitations',
    }),
    updater: one(users, {
      fields: [staffInvitations.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffInvitations',
    }),
  }),
);
