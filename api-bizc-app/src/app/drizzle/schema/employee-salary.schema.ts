import {
  boolean,
  decimal,
  index,
  pgEnum,
  pgTable,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { bankAccounts } from './bank-accounts.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
/**
 * Employee Salary Status Enum
 * Represents the status of employee salary records
 */
export enum EmployeeSalaryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

export const employeeSalaryStatusEnum = pgEnum('employee_salary_status', [
  EmployeeSalaryStatus.ACTIVE,
  EmployeeSalaryStatus.INACTIVE,
  EmployeeSalaryStatus.PENDING,
  EmployeeSalaryStatus.SUSPENDED,
]);

/**
 * Employee Salary Table
 * Stores salary information for employees with bank account relationships
 */
export const employeeSalaries = pgTable(
  'employee_salaries',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Employee reference (foreign key to staff members)
    employeeId: uuid('employee_id')
      .notNull()
      .references(() => staffMembers.id),

    // Salary information
    basicSalary: decimal('basic_salary', { precision: 15, scale: 2 }).notNull(),

    // Bank account reference (foreign key to bank accounts)
    bankAccountId: uuid('bank_account_id')
      .notNull()
      .references(() => bankAccounts.id),

    // Status
    status: employeeSalaryStatusEnum('status')
      .default(EmployeeSalaryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'employee_salaries'),
    employeeIdIndex: index('employee_salaries_employee_id_index').on(
      t.employeeId,
    ),
    bankAccountIdIndex: index('employee_salaries_bank_account_id_index').on(
      t.bankAccountId,
    ),

    // Status and filtering indexes
    statusIndex: index('employee_salaries_status_index').on(t.status),
    businessStatusIndex: index('employee_salaries_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessEmployee: uniqueIndex(
      'employee_salaries_business_employee_unique',
    )
      .on(t.businessId, t.employeeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const employeeSalariesRelations = relations(
  employeeSalaries,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [employeeSalaries.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [employeeSalaries.createdBy],
      references: [users.id],
      relationName: 'createdEmployeeSalaries',
    }),
    updater: one(users, {
      fields: [employeeSalaries.updatedBy],
      references: [users.id],
      relationName: 'updatedEmployeeSalaries',
    }),

    // Employee relation
    employee: one(staffMembers, {
      fields: [employeeSalaries.employeeId],
      references: [staffMembers.id],
    }),

    // Bank account relation
    bankAccount: one(bankAccounts, {
      fields: [employeeSalaries.bankAccountId],
      references: [bankAccounts.id],
    }),
  }),
);
