// src/drizzle/schema/sales-orders.schema.ts
import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
  timestamp,
  jsonb,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { customers } from './customers.schema';
import { products } from './products.schema';
import { productVariants } from './products.schema';
import { users } from './users.schema';
import { restaurantTables } from './restaurant-tables.schema';
import { menuItems } from './menu-items.schema';
import { warrantyTemplates } from './warranties.schema';
import { modifiers } from './modifiers.schema';
import {
  locationAllocationFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Enums
export const salesOrderSourceEnum = pgEnum('sales_order_source', [
  'pos',
  'online',
  'phone',
  'email',
  'sales_rep',
  'dine_in',
  'takeout',
  'delivery',
]);

export const salesOrderTypeEnum = pgEnum('sales_order_type', [
  'retail',
  'restaurant',
  'catering',
  'wholesale',
]);

export const salesOrderStatusEnum = pgEnum('sales_order_status', [
  'draft',
  'confirmed',
  'picking',
  'preparing',
  'ready',
  'shipped',
  'delivered',
  'completed',
  'cancelled',
]);

export const paymentStatusEnum = pgEnum('payment_status', [
  'pending',
  'partial',
  'paid',
  'refunded',
]);

export const orderLineStatusEnum = pgEnum('order_line_status', [
  'pending',
  'preparing',
  'ready',
  'served',
  'cancelled',
]);

export const ticketTypeEnum = pgEnum('ticket_type', [
  'KOT', // Kitchen Order Ticket
  'BOT', // Bar Order Ticket
]);

export const priceTaxTypeEnum = pgEnum('price_tax_type', [
  'inclusive',
  'exclusive',
]);

// Main sales orders table
export const salesOrders = pgTable(
  'sales_orders',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    orderNumber: text('order_number').notNull(),
    customerId: uuid('customer_id').references(() => customers.id, {
      onDelete: 'restrict',
    }),
    quotationId: uuid('quotation_id'),

    // Restaurant specific
    tableId: uuid('table_id').references(() => restaurantTables.id, {
      onDelete: 'set null',
    }),
    serverId: uuid('server_id').references(() => users.id, {
      onDelete: 'set null',
    }),

    // Order details
    orderDate: timestamp('order_date').defaultNow().notNull(),
    deliveryDate: timestamp('delivery_date'),
    orderSource: salesOrderSourceEnum('order_source').notNull(),
    orderType: salesOrderTypeEnum('order_type').notNull(),
    status: salesOrderStatusEnum('status').default('draft').notNull(),
    paymentStatus: paymentStatusEnum('payment_status')
      .default('pending')
      .notNull(),

    // Financial fields
    subtotal: decimal('subtotal', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    discountPercentage: decimal('discount_percentage', {
      precision: 5,
      scale: 2,
    }),
    shippingAmount: decimal('shipping_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    serviceCharge: decimal('service_charge', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    tipAmount: decimal('tip_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),

    // Payment and delivery
    paymentTerms: text('payment_terms'),
    billingAddress: jsonb('billing_address'),
    shippingAddress: jsonb('shipping_address'),
    trackingNumber: text('tracking_number'),
    shippingCarrier: text('shipping_carrier'),

    // E-commerce fields
    customerIpAddress: text('customer_ip_address'),
    sessionId: text('session_id'),
    couponCode: text('coupon_code'),

    // Additional info
    notes: text('notes'),
    internalNotes: text('internal_notes'),
    salespersonId: uuid('salesperson_id').references(() => users.id, {
      onDelete: 'set null',
    }),

    // Location allocation
    ...locationAllocationFields,
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'sales_orders'),
    orderNumberIndex: index('sales_orders_number_index').on(t.orderNumber),
    customerIdIndex: index('sales_orders_customer_id_index').on(t.customerId),
    quotationIdIndex: index('sales_orders_quotation_id_index').on(
      t.quotationId,
    ),
    tableIdIndex: index('sales_orders_table_id_index').on(t.tableId),
    serverIdIndex: index('sales_orders_server_id_index').on(t.serverId),
    statusIndex: index('sales_orders_status_index').on(t.status),
    paymentStatusIndex: index('sales_orders_payment_status_index').on(
      t.paymentStatus,
    ),
    orderDateIndex: index('sales_orders_date_index').on(t.orderDate),

    // Composite indexes for filtering
    businessStatusDateIndex: index('sales_orders_business_status_date_index')
      .on(t.businessId, t.status, t.orderDate)
      .where(sql`${t.isDeleted} = false`),
    businessCustomerDateIndex: index(
      'sales_orders_business_customer_date_index',
    )
      .on(t.businessId, t.customerId, t.orderDate)
      .where(sql`${t.isDeleted} = false`),
    businessPaymentStatusIndex: index(
      'sales_orders_business_payment_status_index',
    )
      .on(t.businessId, t.paymentStatus, t.orderDate)
      .where(sql`${t.isDeleted} = false`),

    // Restaurant specific indexes
    businessTableStatusIndex: index('sales_orders_business_table_status_index')
      .on(t.businessId, t.tableId, t.status)
      .where(sql`${t.isDeleted} = false AND ${t.tableId} IS NOT NULL`),
    businessServerDateIndex: index('sales_orders_business_server_date_index')
      .on(t.businessId, t.serverId, t.orderDate)
      .where(sql`${t.isDeleted} = false AND ${t.serverId} IS NOT NULL`),

    // Unique constraint
    uniqueOrderNumber: uniqueIndex('sales_orders_number_unique')
      .on(t.businessId, t.orderNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Sales order lines table
export const salesOrderLines = pgTable(
  'sales_order_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'cascade' }),

    // Product information
    productId: uuid('product_id').references(() => products.id, {
      onDelete: 'restrict',
    }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),
    menuItemId: uuid('menu_item_id').references(() => menuItems.id, {
      onDelete: 'restrict',
    }),

    // Quantities
    quantityOrdered: decimal('quantity_ordered', {
      precision: 10,
      scale: 2,
    }).notNull(),
    quantityDelivered: decimal('quantity_delivered', {
      precision: 10,
      scale: 2,
    })
      .default('0')
      .notNull(),
    quantityCancelled: decimal('quantity_cancelled', {
      precision: 10,
      scale: 2,
    })
      .default('0')
      .notNull(),

    // Pricing
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    priceTaxType: priceTaxTypeEnum('price_tax_type')
      .default('exclusive')
      .notNull(),
    discountPercentage: decimal('discount_percentage', {
      precision: 5,
      scale: 2,
    }).default('0'),
    discountAmount: decimal('discount_amount', {
      precision: 10,
      scale: 2,
    }).default('0'),
    lineSubtotal: decimal('line_subtotal', {
      precision: 10,
      scale: 2,
    }).notNull(),
    taxAmount: decimal('tax_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 10, scale: 2 }).notNull(),

    // Warranty
    warrantyTemplateId: uuid('warranty_template_id').references(
      () => warrantyTemplates.id,
      { onDelete: 'set null' },
    ),
    warrantyPrice: decimal('warranty_price', {
      precision: 10,
      scale: 2,
    }).default('0'),

    // Restaurant specific
    specialInstructions: text('special_instructions'),
    orderStatus: orderLineStatusEnum('order_status')
      .default('pending')
      .notNull(),
    ticketType: ticketTypeEnum('ticket_type'),
    kotNumber: text('kot_number'),
    botNumber: text('bot_number'),
    ticketPrintedAt: timestamp('ticket_printed_at'),
    prepStartedAt: timestamp('prep_started_at'),
    readyAt: timestamp('ready_at'),
    servedAt: timestamp('served_at'),
    preparedBy: uuid('prepared_by').references(() => users.id, {
      onDelete: 'set null',
    }),

    // Additional info
    notes: text('notes'),
    sortOrder: integer('sort_order').default(0),

    // For custom/manual entries
    productName: text('product_name'),
    productDescription: text('product_description'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'sales_order_lines'),
    salesOrderIdIndex: index('sales_order_lines_order_id_index').on(
      t.salesOrderId,
    ),
    productIdIndex: index('sales_order_lines_product_id_index').on(t.productId),
    variantIdIndex: index('sales_order_lines_variant_id_index').on(t.variantId),
    menuItemIdIndex: index('sales_order_lines_menu_item_id_index').on(
      t.menuItemId,
    ),
    orderStatusIndex: index('sales_order_lines_status_index').on(t.orderStatus),

    // Kitchen/bar ticket indexes
    kotNumberIndex: index('sales_order_lines_kot_number_index')
      .on(t.kotNumber)
      .where(sql`${t.kotNumber} IS NOT NULL`),
    botNumberIndex: index('sales_order_lines_bot_number_index')
      .on(t.botNumber)
      .where(sql`${t.botNumber} IS NOT NULL`),

    // Composite indexes
    orderProductIndex: index('sales_order_lines_order_product_index')
      .on(t.salesOrderId, t.productId)
      .where(sql`${t.isDeleted} = false`),
    orderStatusDateIndex: index('sales_order_lines_order_status_date_index')
      .on(t.salesOrderId, t.orderStatus, t.createdAt)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Order line modifiers (for restaurant orders)
export const orderLineModifiers = pgTable(
  'order_line_modifiers',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderLineId: uuid('sales_order_line_id')
      .notNull()
      .references(() => salesOrderLines.id, { onDelete: 'cascade' }),
    modifierId: uuid('modifier_id')
      .notNull()
      .references(() => modifiers.id, { onDelete: 'restrict' }),

    // Modifier details
    modifierValue: text('modifier_value'),
    quantity: integer('quantity').default(1).notNull(),
    priceAdjustment: decimal('price_adjustment', {
      precision: 10,
      scale: 2,
    }).default('0'),

    // Additional info
    notes: text('notes'),
    sortOrder: integer('sort_order').default(0),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'order_line_modifiers'),
    salesOrderLineIdIndex: index('order_line_modifiers_line_id_index').on(
      t.salesOrderLineId,
    ),
    modifierIdIndex: index('order_line_modifiers_modifier_id_index').on(
      t.modifierId,
    ),
  }),
);

// Order payments table
export const orderPayments = pgTable(
  'order_payments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'restrict' }),

    // Payment details
    paymentMethod: text('payment_method').notNull(),
    paymentGateway: text('payment_gateway'),
    transactionId: text('transaction_id'),
    amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
    status: text('status').default('pending').notNull(),

    // Gateway response
    gatewayResponse: jsonb('gateway_response'),
    paymentDate: timestamp('payment_date'),

    // Card details (PCI compliance - store only last 4)
    cardLastFour: text('card_last_four'),
    cardType: text('card_type'),

    // Additional info
    notes: text('notes'),
    processedBy: uuid('processed_by').references(() => users.id, {
      onDelete: 'set null',
    }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'order_payments'),
    salesOrderIdIndex: index('order_payments_order_id_index').on(
      t.salesOrderId,
    ),
    statusIndex: index('order_payments_status_index').on(t.status),
    paymentDateIndex: index('order_payments_date_index').on(t.paymentDate),
    transactionIdIndex: index('order_payments_transaction_id_index').on(
      t.transactionId,
    ),

    // Composite indexes
    orderStatusIndex: index('order_payments_order_status_index')
      .on(t.salesOrderId, t.status)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Order status history table
export const orderStatusHistory = pgTable(
  'order_status_history',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'cascade' }),

    // Status change
    previousStatus: salesOrderStatusEnum('previous_status'),
    newStatus: salesOrderStatusEnum('new_status').notNull(),

    // Change details
    changedBy: uuid('changed_by')
      .notNull()
      .references(() => users.id, { onDelete: 'restrict' }),
    changedAt: timestamp('changed_at').defaultNow().notNull(),
    reason: text('reason'),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'order_status_history'),
    salesOrderIdIndex: index('order_status_history_order_id_index').on(
      t.salesOrderId,
    ),
    changedAtIndex: index('order_status_history_changed_at_index').on(
      t.changedAt,
    ),
    changedByIndex: index('order_status_history_changed_by_index').on(
      t.changedBy,
    ),
  }),
);

// Order shipments table
export const orderShipments = pgTable(
  'order_shipments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesOrderId: uuid('sales_order_id')
      .notNull()
      .references(() => salesOrders.id, { onDelete: 'restrict' }),

    // Shipment details
    shipmentNumber: text('shipment_number').notNull(),
    carrier: text('carrier'),
    trackingNumber: text('tracking_number'),

    // Dates
    shippedDate: timestamp('shipped_date'),
    estimatedDeliveryDate: timestamp('estimated_delivery_date'),
    actualDeliveryDate: timestamp('actual_delivery_date'),

    // Shipping info
    shippingCost: decimal('shipping_cost', { precision: 10, scale: 2 }).default(
      '0',
    ),
    weight: decimal('weight', { precision: 10, scale: 2 }),
    weightUnit: text('weight_unit').default('kg'),

    // Status
    status: text('status').default('pending').notNull(),

    // Additional info
    notes: text('notes'),
    shippedBy: uuid('shipped_by').references(() => users.id, {
      onDelete: 'set null',
    }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'order_shipments'),
    salesOrderIdIndex: index('order_shipments_order_id_index').on(
      t.salesOrderId,
    ),
    shipmentNumberIndex: index('order_shipments_number_index').on(
      t.shipmentNumber,
    ),
    trackingNumberIndex: index('order_shipments_tracking_index').on(
      t.trackingNumber,
    ),
    statusIndex: index('order_shipments_status_index').on(t.status),
    shippedDateIndex: index('order_shipments_shipped_date_index').on(
      t.shippedDate,
    ),

    // Unique constraint
    uniqueShipmentNumber: uniqueIndex('order_shipments_number_unique')
      .on(t.businessId, t.shipmentNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Order shipment lines table
export const orderShipmentLines = pgTable(
  'order_shipment_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    shipmentId: uuid('shipment_id')
      .notNull()
      .references(() => orderShipments.id, { onDelete: 'cascade' }),
    salesOrderLineId: uuid('sales_order_line_id')
      .notNull()
      .references(() => salesOrderLines.id, { onDelete: 'restrict' }),

    // Quantities
    quantityShipped: decimal('quantity_shipped', {
      precision: 10,
      scale: 2,
    }).notNull(),

    // Serial/batch tracking
    serialNumbers: text('serial_numbers').array(),
    batchNumbers: text('batch_numbers').array(),

    // Additional info
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'order_shipment_lines'),
    shipmentIdIndex: index('order_shipment_lines_shipment_id_index').on(
      t.shipmentId,
    ),
    salesOrderLineIdIndex: index('order_shipment_lines_order_line_id_index').on(
      t.salesOrderLineId,
    ),
  }),
);

// Sales returns table
export const salesReturns = pgTable(
  'sales_returns',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    returnNumber: text('return_number').notNull(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'restrict' }),
    salesOrderId: uuid('sales_order_id').references(() => salesOrders.id, {
      onDelete: 'restrict',
    }),
    invoiceId: uuid('invoice_id'),

    // Return details
    returnDate: timestamp('return_date').defaultNow().notNull(),
    returnType: text('return_type').default('refund').notNull(), // refund, exchange, credit
    status: text('status').default('pending').notNull(),
    returnReason: text('return_reason').notNull(),

    // Financial fields
    subtotal: decimal('subtotal', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    shippingCost: decimal('shipping_cost', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    restockingFee: decimal('restocking_fee', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),

    // Workflow
    requestedBy: uuid('requested_by').references(() => users.id, {
      onDelete: 'set null',
    }),
    approvedBy: uuid('approved_by').references(() => users.id, {
      onDelete: 'set null',
    }),
    receivedBy: uuid('received_by').references(() => users.id, {
      onDelete: 'set null',
    }),

    // Dates
    approvedDate: timestamp('approved_date'),
    receivedDate: timestamp('received_date'),

    // Additional info
    notes: text('notes'),
    internalNotes: text('internal_notes'),

    // Location allocation
    ...locationAllocationFields,
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'sales_returns'),
    returnNumberIndex: index('sales_returns_number_index').on(t.returnNumber),
    customerIdIndex: index('sales_returns_customer_id_index').on(t.customerId),
    salesOrderIdIndex: index('sales_returns_order_id_index').on(t.salesOrderId),
    statusIndex: index('sales_returns_status_index').on(t.status),
    returnDateIndex: index('sales_returns_date_index').on(t.returnDate),

    // Composite indexes
    businessStatusDateIndex: index('sales_returns_business_status_date_index')
      .on(t.businessId, t.status, t.returnDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint
    uniqueReturnNumber: uniqueIndex('sales_returns_number_unique')
      .on(t.businessId, t.returnNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Sales return lines table
export const salesReturnLines = pgTable(
  'sales_return_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    salesReturnId: uuid('sales_return_id')
      .notNull()
      .references(() => salesReturns.id, { onDelete: 'cascade' }),
    salesOrderLineId: uuid('sales_order_line_id').references(
      () => salesOrderLines.id,
      { onDelete: 'set null' },
    ),

    // Product information
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'restrict' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),

    // Quantities
    quantityReturned: decimal('quantity_returned', {
      precision: 10,
      scale: 2,
    }).notNull(),
    quantityReceived: decimal('quantity_received', {
      precision: 10,
      scale: 2,
    }).default('0'),

    // Pricing
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    lineSubtotal: decimal('line_subtotal', {
      precision: 10,
      scale: 2,
    }).notNull(),
    taxAmount: decimal('tax_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 10, scale: 2 }).notNull(),

    // Return details
    returnReason: text('return_reason'),
    condition: text('condition'), // new, used, damaged

    // Serial/batch tracking
    serialNumbers: text('serial_numbers').array(),
    batchNumbers: text('batch_numbers').array(),

    // Additional info
    notes: text('notes'),
    sortOrder: integer('sort_order').default(0),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'sales_return_lines'),
    salesReturnIdIndex: index('sales_return_lines_return_id_index').on(
      t.salesReturnId,
    ),
    salesOrderLineIdIndex: index('sales_return_lines_order_line_id_index').on(
      t.salesOrderLineId,
    ),
    productIdIndex: index('sales_return_lines_product_id_index').on(
      t.productId,
    ),
  }),
);

// Relations
export const salesOrdersRelations = relations(salesOrders, ({ one, many }) => ({
  // Many-to-one relations
  business: one(business, {
    fields: [salesOrders.businessId],
    references: [business.id],
  }),
  customer: one(customers, {
    fields: [salesOrders.customerId],
    references: [customers.id],
  }),
  table: one(restaurantTables, {
    fields: [salesOrders.tableId],
    references: [restaurantTables.id],
  }),
  server: one(users, {
    fields: [salesOrders.serverId],
    references: [users.id],
    relationName: 'serverOrders',
  }),
  salesperson: one(users, {
    fields: [salesOrders.salespersonId],
    references: [users.id],
    relationName: 'salespersonOrders',
  }),
  // Audit relations
  createdByUser: one(users, {
    fields: [salesOrders.createdBy],
    references: [users.id],
    relationName: 'createdSalesOrders',
  }),
  updatedByUser: one(users, {
    fields: [salesOrders.updatedBy],
    references: [users.id],
    relationName: 'updatedSalesOrders',
  }),
  // One-to-many relations
  salesOrderLines: many(salesOrderLines),
  orderPayments: many(orderPayments),
  orderStatusHistory: many(orderStatusHistory),
  orderShipments: many(orderShipments),
  salesReturns: many(salesReturns),
}));

export const salesOrderLinesRelations = relations(
  salesOrderLines,
  ({ one, many }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [salesOrderLines.businessId],
      references: [business.id],
    }),
    salesOrder: one(salesOrders, {
      fields: [salesOrderLines.salesOrderId],
      references: [salesOrders.id],
    }),
    product: one(products, {
      fields: [salesOrderLines.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [salesOrderLines.variantId],
      references: [productVariants.id],
    }),
    menuItem: one(menuItems, {
      fields: [salesOrderLines.menuItemId],
      references: [menuItems.id],
    }),
    warrantyTemplate: one(warrantyTemplates, {
      fields: [salesOrderLines.warrantyTemplateId],
      references: [warrantyTemplates.id],
    }),
    preparedByUser: one(users, {
      fields: [salesOrderLines.preparedBy],
      references: [users.id],
      relationName: 'preparedOrderLines',
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [salesOrderLines.createdBy],
      references: [users.id],
      relationName: 'createdSalesOrderLines',
    }),
    updatedByUser: one(users, {
      fields: [salesOrderLines.updatedBy],
      references: [users.id],
      relationName: 'updatedSalesOrderLines',
    }),
    // One-to-many relations
    orderLineModifiers: many(orderLineModifiers),
    orderShipmentLines: many(orderShipmentLines),
    salesReturnLines: many(salesReturnLines),
  }),
);

export const orderLineModifiersRelations = relations(
  orderLineModifiers,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [orderLineModifiers.businessId],
      references: [business.id],
    }),
    salesOrderLine: one(salesOrderLines, {
      fields: [orderLineModifiers.salesOrderLineId],
      references: [salesOrderLines.id],
    }),
    modifier: one(modifiers, {
      fields: [orderLineModifiers.modifierId],
      references: [modifiers.id],
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [orderLineModifiers.createdBy],
      references: [users.id],
      relationName: 'createdOrderLineModifiers',
    }),
    updatedByUser: one(users, {
      fields: [orderLineModifiers.updatedBy],
      references: [users.id],
      relationName: 'updatedOrderLineModifiers',
    }),
  }),
);

export const orderPaymentsRelations = relations(orderPayments, ({ one }) => ({
  // Many-to-one relations
  business: one(business, {
    fields: [orderPayments.businessId],
    references: [business.id],
  }),
  salesOrder: one(salesOrders, {
    fields: [orderPayments.salesOrderId],
    references: [salesOrders.id],
  }),
  processedByUser: one(users, {
    fields: [orderPayments.processedBy],
    references: [users.id],
    relationName: 'processedOrderPayments',
  }),
  // Audit relations
  createdByUser: one(users, {
    fields: [orderPayments.createdBy],
    references: [users.id],
    relationName: 'createdOrderPayments',
  }),
  updatedByUser: one(users, {
    fields: [orderPayments.updatedBy],
    references: [users.id],
    relationName: 'updatedOrderPayments',
  }),
}));

export const orderStatusHistoryRelations = relations(
  orderStatusHistory,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [orderStatusHistory.businessId],
      references: [business.id],
    }),
    salesOrder: one(salesOrders, {
      fields: [orderStatusHistory.salesOrderId],
      references: [salesOrders.id],
    }),
    changedByUser: one(users, {
      fields: [orderStatusHistory.changedBy],
      references: [users.id],
      relationName: 'changedOrderStatus',
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [orderStatusHistory.createdBy],
      references: [users.id],
      relationName: 'createdOrderStatusHistory',
    }),
    updatedByUser: one(users, {
      fields: [orderStatusHistory.updatedBy],
      references: [users.id],
      relationName: 'updatedOrderStatusHistory',
    }),
  }),
);

export const orderShipmentsRelations = relations(
  orderShipments,
  ({ one, many }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [orderShipments.businessId],
      references: [business.id],
    }),
    salesOrder: one(salesOrders, {
      fields: [orderShipments.salesOrderId],
      references: [salesOrders.id],
    }),
    shippedByUser: one(users, {
      fields: [orderShipments.shippedBy],
      references: [users.id],
      relationName: 'shippedOrders',
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [orderShipments.createdBy],
      references: [users.id],
      relationName: 'createdOrderShipments',
    }),
    updatedByUser: one(users, {
      fields: [orderShipments.updatedBy],
      references: [users.id],
      relationName: 'updatedOrderShipments',
    }),
    // One-to-many relations
    orderShipmentLines: many(orderShipmentLines),
  }),
);

export const orderShipmentLinesRelations = relations(
  orderShipmentLines,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [orderShipmentLines.businessId],
      references: [business.id],
    }),
    shipment: one(orderShipments, {
      fields: [orderShipmentLines.shipmentId],
      references: [orderShipments.id],
    }),
    salesOrderLine: one(salesOrderLines, {
      fields: [orderShipmentLines.salesOrderLineId],
      references: [salesOrderLines.id],
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [orderShipmentLines.createdBy],
      references: [users.id],
      relationName: 'createdOrderShipmentLines',
    }),
    updatedByUser: one(users, {
      fields: [orderShipmentLines.updatedBy],
      references: [users.id],
      relationName: 'updatedOrderShipmentLines',
    }),
  }),
);

export const salesReturnsRelations = relations(
  salesReturns,
  ({ one, many }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [salesReturns.businessId],
      references: [business.id],
    }),
    customer: one(customers, {
      fields: [salesReturns.customerId],
      references: [customers.id],
    }),
    salesOrder: one(salesOrders, {
      fields: [salesReturns.salesOrderId],
      references: [salesOrders.id],
    }),
    requestedByUser: one(users, {
      fields: [salesReturns.requestedBy],
      references: [users.id],
      relationName: 'requestedSalesReturns',
    }),
    approvedByUser: one(users, {
      fields: [salesReturns.approvedBy],
      references: [users.id],
      relationName: 'approvedSalesReturns',
    }),
    receivedByUser: one(users, {
      fields: [salesReturns.receivedBy],
      references: [users.id],
      relationName: 'receivedSalesReturns',
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [salesReturns.createdBy],
      references: [users.id],
      relationName: 'createdSalesReturns',
    }),
    updatedByUser: one(users, {
      fields: [salesReturns.updatedBy],
      references: [users.id],
      relationName: 'updatedSalesReturns',
    }),
    // One-to-many relations
    salesReturnLines: many(salesReturnLines),
  }),
);

export const salesReturnLinesRelations = relations(
  salesReturnLines,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [salesReturnLines.businessId],
      references: [business.id],
    }),
    salesReturn: one(salesReturns, {
      fields: [salesReturnLines.salesReturnId],
      references: [salesReturns.id],
    }),
    salesOrderLine: one(salesOrderLines, {
      fields: [salesReturnLines.salesOrderLineId],
      references: [salesOrderLines.id],
    }),
    product: one(products, {
      fields: [salesReturnLines.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [salesReturnLines.variantId],
      references: [productVariants.id],
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [salesReturnLines.createdBy],
      references: [users.id],
      relationName: 'createdSalesReturnLines',
    }),
    updatedByUser: one(users, {
      fields: [salesReturnLines.updatedBy],
      references: [users.id],
      relationName: 'updatedSalesReturnLines',
    }),
  }),
);

// Export all schemas
export const salesOrderSchemas = {
  salesOrders,
  salesOrderLines,
  orderLineModifiers,
  orderPayments,
  orderStatusHistory,
  orderShipments,
  orderShipmentLines,
  salesReturns,
  salesReturnLines,
};
