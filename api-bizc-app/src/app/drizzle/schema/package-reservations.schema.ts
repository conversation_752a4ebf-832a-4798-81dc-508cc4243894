import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  integer,
  decimal,
  boolean,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { packages } from './packages.schema';
import { guests, guestRelationshipEnum } from './guests.schema';
import {
  auditFields,
  createBaseEntityFields,
  paymentStatusEnum,
  discountTypeEnum,
  taxTypeEnum,
} from './common-fields.schema';
import { ReservationSource } from '../../shared/types/accommodation.enum';
import {
  PaymentStatus,
  TaxType,
  PackageReservationStatus,
} from '../../shared/types/common.enum';

import { users } from './users.schema';
// Package Reservation Status Enum
export const packageReservationStatusEnum = pgEnum(
  'package_reservation_status',
  [
    PackageReservationStatus.INQUIRY,
    PackageReservationStatus.PENDING,
    PackageReservationStatus.CONFIRMED,
    PackageReservationStatus.PARTIALLY_PAID,
    PackageReservationStatus.PAID,
    PackageReservationStatus.IN_PROGRESS,
    PackageReservationStatus.COMPLETED,
    PackageReservationStatus.CANCELLED,
    PackageReservationStatus.NO_SHOW,
    PackageReservationStatus.REFUNDED,
  ],
);

// Payment Status Enum is imported from common-fields.schema.ts

// Create unique reservation source enum for packages to avoid conflicts
export const packageReservationSourceEnum = pgEnum(
  'package_reservation_source',
  [
    ReservationSource.ONLINE,
    ReservationSource.PHONE,
    ReservationSource.WALK_IN,
    ReservationSource.EMAIL,
    ReservationSource.AGENT,
    ReservationSource.CORPORATE,
    ReservationSource.REPEAT_GUEST,
    ReservationSource.REFERRAL,
    ReservationSource.OTHER,
  ],
);

// Tax Type Enum imported from common-fields.schema.ts

// Discount Type Enum is imported from common-fields.schema.ts

// Package Reservations table
export const packageReservations = pgTable(
  'package_reservations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Reservation identification
    reservationNumber: text('reservation_number').notNull(),
    referenceNumber: text('reference_number'), // External booking reference

    // Package reference
    packageId: uuid('package_id')
      .notNull()
      .references(() => packages.id),

    // Package dates
    startDate: timestamp('start_date').notNull(),
    endDate: timestamp('end_date').notNull(),

    // Guest information
    totalNumberOfGuests: integer('total_number_of_guests').notNull(),
    numberOfAdults: integer('number_of_adults').notNull(),
    numberOfChildren: integer('number_of_children').default(0).notNull(),

    // Reservation status and type
    status: packageReservationStatusEnum('status')
      .default(PackageReservationStatus.PENDING)
      .notNull(),
    reservationSource: packageReservationSourceEnum(
      'package_reservation_source',
    ),

    // Payment information
    paymentStatus: paymentStatusEnum('payment_status')
      .default(PaymentStatus.PENDING)
      .notNull(),
    subtotal: decimal('subtotal', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    total: decimal('total', { precision: 12, scale: 2 })
      .default('0.00')
      .notNull(),
    depositPaid: decimal('deposit_paid', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    balanceDue: decimal('balance_due', { precision: 12, scale: 2 }).default(
      '0.00',
    ),

    // Discount information
    discountType: discountTypeEnum('discount_type'),
    discountValue: decimal('discount_value', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    discountAmount: decimal('discount_amount', {
      precision: 12,
      scale: 2,
    }).default('0.00'),

    // Tax information
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),

    // Reservation metadata
    notes: text('notes'),
    cancellationReason: text('cancellation_reason'),
    cancellationDate: timestamp('cancellation_date'),

    // Confirmation and communication
    confirmationSent: boolean('confirmation_sent').default(false).notNull(),
    confirmationSentAt: timestamp('confirmation_sent_at'),
    reminderSent: boolean('reminder_sent').default(false).notNull(),
    reminderSentAt: timestamp('reminder_sent_at'),
  },
  (t) => ({
    // Primary indexes
    businessIdIndex: index('package_reservations_business_id_index').on(
      t.businessId,
    ),
    reservationNumberIndex: index('package_reservations_number_index').on(
      t.reservationNumber,
    ),
    referenceNumberIndex: index('package_reservations_reference_index').on(
      t.referenceNumber,
    ),
    packageIdIndex: index('package_reservations_package_id_index').on(
      t.packageId,
    ),

    // Date and time indexes for availability queries
    startDateIndex: index('package_reservations_start_date_index').on(
      t.startDate,
    ),
    endDateIndex: index('package_reservations_end_date_index').on(t.endDate),

    // Status and type indexes
    statusIndex: index('package_reservations_status_index').on(t.status),
    paymentStatusIndex: index('package_reservations_payment_status_index').on(
      t.paymentStatus,
    ),
    reservationSourceIndex: index('package_reservations_source_index').on(
      t.reservationSource,
    ),

    confirmationSentIndex: index(
      'package_reservations_confirmation_sent_index',
    ).on(t.confirmationSent),

    // Composite indexes for common queries
    businessStatusIndex: index('package_reservations_business_status_index').on(
      t.businessId,
      t.status,
    ),
    businessDateRangeIndex: index(
      'package_reservations_business_date_range_index',
    ).on(t.businessId, t.startDate, t.endDate),
    packageDateRangeIndex: index(
      'package_reservations_package_date_range_index',
    ).on(t.packageId, t.startDate, t.endDate),
    paymentDueIndex: index('package_reservations_payment_due_index').on(
      t.businessId,
      t.paymentStatus,
      t.startDate,
    ),

    // Unique constraints with soft deletion support
    uniqueBusinessReservationNumber: uniqueIndex(
      'package_reservations_business_number_unique',
    )
      .on(t.businessId, t.reservationNumber)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessReferenceNumber: uniqueIndex(
      'package_reservations_business_reference_unique',
    )
      .on(t.businessId, t.referenceNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations for packageReservations
export const packageReservationsRelations = relations(
  packageReservations,
  ({ one, many }) => ({
    business: one(business, {
      fields: [packageReservations.businessId],
      references: [business.id],
    }),
    package: one(packages, {
      fields: [packageReservations.packageId],
      references: [packages.id],
    }),
    packageReservationGuests: many(packageReservationGuests),
    createdByUser: one(users, {
      fields: [packageReservations.createdBy],
      references: [users.id],
    }),
    updatedByUser: one(users, {
      fields: [packageReservations.updatedBy],
      references: [users.id],
    }),
  }),
);

// Junction table for package reservation-guest relationships (many-to-many)
export const packageReservationGuests = pgTable(
  'package_reservation_guests',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    reservationId: uuid('reservation_id')
      .notNull()
      .references(() => packageReservations.id, {
        onDelete: 'cascade',
      }),
    guestId: uuid('guest_id')
      .notNull()
      .references(() => guests.id, {
        onDelete: 'cascade',
      }),

    // Guest role in this specific reservation
    isPrimaryGuest: boolean('is_primary_guest').default(false).notNull(),
    primaryGuestId: uuid('primary_guest_id').references(() => guests.id),
    relationshipToPrimary: guestRelationshipEnum('relationship_to_primary'),

    // Package-specific guest information
    participationStatus: text('participation_status')
      .default('CONFIRMED')
      .notNull(), // CONFIRMED, CANCELLED, NO_SHOW
    packageStarted: boolean('package_started').default(false).notNull(),
    packageCompleted: boolean('package_completed').default(false).notNull(),

    // Guest notes for this package
    notes: text('notes'),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    reservationIdIndex: index(
      'package_reservation_guests_reservation_id_index',
    ).on(t.reservationId),
    guestIdIndex: index('package_reservation_guests_guest_id_index').on(
      t.guestId,
    ),

    // Status and role indexes
    isPrimaryGuestIndex: index(
      'package_reservation_guests_is_primary_guest_index',
    ).on(t.isPrimaryGuest),
    primaryGuestIdIndex: index(
      'package_reservation_guests_primary_guest_id_index',
    ).on(t.primaryGuestId),
    relationshipToPrimaryIndex: index(
      'package_reservation_guests_relationship_to_primary_index',
    ).on(t.relationshipToPrimary),
    participationStatusIndex: index(
      'package_reservation_guests_participation_status_index',
    ).on(t.participationStatus),
    packageStartedIndex: index(
      'package_reservation_guests_package_started_index',
    ).on(t.packageStarted),
    packageCompletedIndex: index(
      'package_reservation_guests_package_completed_index',
    ).on(t.packageCompleted),

    // Composite indexes for common queries
    reservationPrimaryGuestIndex: index(
      'package_reservation_guests_reservation_primary_index',
    ).on(t.reservationId, t.isPrimaryGuest),
    reservationGuestRelationshipIndex: index(
      'package_reservation_guests_reservation_relationship_index',
    ).on(t.reservationId, t.relationshipToPrimary),
    primaryGuestCompanionsIndex: index(
      'package_reservation_guests_primary_companions_index',
    ).on(t.primaryGuestId, t.relationshipToPrimary),
    guestReservationStatusIndex: index(
      'package_reservation_guests_guest_status_index',
    ).on(t.guestId, t.participationStatus),

    // Unique constraints
    uniqueReservationGuest: uniqueIndex('package_reservation_guests_unique').on(
      t.reservationId,
      t.guestId,
    ),

    // Ensure only one primary guest per reservation
    uniquePrimaryGuestPerReservation: uniqueIndex(
      'package_reservation_guests_unique_primary',
    )
      .on(t.reservationId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations for packageReservationGuests
export const packageReservationGuestsRelations = relations(
  packageReservationGuests,
  ({ one }) => ({
    reservation: one(packageReservations, {
      fields: [packageReservationGuests.reservationId],
      references: [packageReservations.id],
    }),
    guest: one(guests, {
      fields: [packageReservationGuests.guestId],
      references: [guests.id],
    }),
    primaryGuest: one(guests, {
      fields: [packageReservationGuests.primaryGuestId],
      references: [guests.id],
    }),
    createdByUser: one(users, {
      fields: [packageReservationGuests.createdBy],
      references: [users.id],
    }),
    updatedByUser: one(users, {
      fields: [packageReservationGuests.updatedBy],
      references: [users.id],
    }),
  }),
);
