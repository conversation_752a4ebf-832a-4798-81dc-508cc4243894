import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  integer,
  decimal,
  date,
  time,
  boolean,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { vehicles } from './vehicles.schema';
import { staffMembers } from './staff.schema';

import { users } from './users.schema';
// Repair Order Status Enum
export enum RepairOrderStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
  WAITING_PARTS = 'waiting_parts',
  READY_FOR_PICKUP = 'ready_for_pickup',
}

export const repairOrderStatusEnum = pgEnum('repair_order_status', [
  RepairOrderStatus.PENDING,
  RepairOrderStatus.IN_PROGRESS,
  RepairOrderStatus.COMPLETED,
  RepairOrderStatus.CANCELLED,
  RepairOrderStatus.ON_HOLD,
  RepairOrderStatus.WAITING_PARTS,
  RepairOrderStatus.READY_FOR_PICKUP,
]);

// Maintenance Type Enum
export enum MaintenanceType {
  PREVENTIVE = 'preventive',
  CORRECTIVE = 'corrective',
  CONDITION_BASED = 'condition_based',
  EMERGENCY = 'emergency',
  ROUTINE = 'routine',
  SCHEDULED = 'scheduled',
  UNSCHEDULED = 'unscheduled',
}

export const maintenanceTypeEnum = pgEnum('maintenance_type', [
  MaintenanceType.PREVENTIVE,
  MaintenanceType.CORRECTIVE,
  MaintenanceType.CONDITION_BASED,
  MaintenanceType.EMERGENCY,
  MaintenanceType.ROUTINE,
  MaintenanceType.SCHEDULED,
  MaintenanceType.UNSCHEDULED,
]);

// Workshop Type Enum
export enum WorkshopType {
  INTERNAL = 'internal',
  EXTERNAL = 'external',
}

export const workshopTypeEnum = pgEnum('workshop_type', [
  WorkshopType.INTERNAL,
  WorkshopType.EXTERNAL,
]);

export const vehicleRepairOrders = pgTable(
  'vehicle_repair_orders',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    vehicleId: uuid('vehicle_id')
      .notNull()
      .references(() => vehicles.id),

    // General Information
    repairOrderNumber: text('repair_order_number'), // Auto-generated unique identifier
    maintenanceType: maintenanceTypeEnum('maintenance_type').notNull(),
    totalInParts: decimal('total_in_parts', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    totalInLabor: decimal('total_in_labor', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    damages: text('damages'), // Description of damages
    status: repairOrderStatusEnum('status')
      .default(RepairOrderStatus.PENDING)
      .notNull(),
    notes: text('notes'), // Rich text notes from the form
    images: uuid('images').array(), // Array of image IDs following vehicles pattern

    // Vehicle Out Information
    dateOut: date('date_out'),
    timeOut: time('time_out'),
    dateDue: date('date_due'),
    timeDue: time('time_due'),

    // Workshop Information
    workshopType: workshopTypeEnum('workshop_type')
      .default(WorkshopType.INTERNAL)
      .notNull(),

    // Internal Management (when workshopType is INTERNAL)
    mechanicId: uuid('mechanic_id').references(() => staffMembers.id), // Assigned mechanic
    internalComments: text('internal_comments'),

    // External Workshop (when workshopType is EXTERNAL)
    workshopName: text('workshop_name'),
    externalMechanicName: text('external_mechanic_name'),
    workshopPhoneNumber: text('workshop_phone_number'),
    workshopAddress: text('workshop_address'),
    externalComments: text('external_comments'),

    // Estimated and Actual Costs
    estimatedCost: decimal('estimated_cost', { precision: 12, scale: 2 }),
    actualCost: decimal('actual_cost', { precision: 12, scale: 2 }),
    laborCost: decimal('labor_cost', { precision: 12, scale: 2 }),
    partsCost: decimal('parts_cost', { precision: 12, scale: 2 }),

    // Completion Information
    completedDate: date('completed_date'),
    completedTime: time('completed_time'),
    workPerformed: text('work_performed'), // Description of work done

    // Priority and Additional Info
    priority: integer('priority').default(3), // 1=High, 2=Medium, 3=Low
    customerComplaint: text('customer_complaint'),
    odometerReading: integer('odometer_reading'),
    fuelLevel: integer('fuel_level'), // 0-100 percentage

    // Quality Control
    qualityCheck: boolean('quality_check').default(false),
    qualityCheckBy: uuid('quality_check_by').references(() => staffMembers.id),
    qualityCheckDate: date('quality_check_date'),
    qualityCheckNotes: text('quality_check_notes'),

    // Additional fields
    deletedBy: uuid('deleted_by').references(() => staffMembers.id),
    deletedAt: timestamp('deleted_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'vehicle_repair_orders'),
    vehicleIdIndex: index('vehicle_repair_orders_vehicle_id_index').on(
      t.vehicleId,
    ),
    repairOrderNumberIndex: index('vehicle_repair_orders_number_index').on(
      t.repairOrderNumber,
    ),
    statusIndex: index('vehicle_repair_orders_status_index').on(t.status),
    maintenanceTypeIndex: index(
      'vehicle_repair_orders_maintenance_type_index',
    ).on(t.maintenanceType),
    workshopTypeIndex: index('vehicle_repair_orders_workshop_type_index').on(
      t.workshopType,
    ),
    mechanicIdIndex: index('vehicle_repair_orders_mechanic_id_index').on(
      t.mechanicId,
    ),
    dateOutIndex: index('vehicle_repair_orders_date_out_index').on(t.dateOut),
    dateDueIndex: index('vehicle_repair_orders_date_due_index').on(t.dateDue),
    completedDateIndex: index('vehicle_repair_orders_completed_date_index').on(
      t.completedDate,
    ),
    priorityIndex: index('vehicle_repair_orders_priority_index').on(t.priority),
    qualityCheckIndex: index('vehicle_repair_orders_quality_check_index').on(
      t.qualityCheck,
    ),
    imagesIndex: index('vehicle_repair_orders_images_index').on(t.images),

    // Unique constraint for repair order number per business
    uniqueRepairOrderNumber: uniqueIndex(
      'vehicle_repair_orders_business_number_unique',
    ).on(t.businessId, t.repairOrderNumber),
  }),
);

// Relations
export const vehicleRepairOrdersRelations = relations(
  vehicleRepairOrders,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [vehicleRepairOrders.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [vehicleRepairOrders.createdBy],
      references: [users.id],
      relationName: 'createdVehicleRepairOrders',
    }),
    updater: one(users, {
      fields: [vehicleRepairOrders.updatedBy],
      references: [users.id],
      relationName: 'updatedVehicleRepairOrders',
    }),

    // Vehicle relation
    vehicle: one(vehicles, {
      fields: [vehicleRepairOrders.vehicleId],
      references: [vehicles.id],
    }),

    // Mechanic (staff member) relation
    mechanic: one(staffMembers, {
      fields: [vehicleRepairOrders.mechanicId],
      references: [staffMembers.id],
      relationName: 'mechanicRepairOrders',
    }),

    // Quality check staff relation
    qualityChecker: one(staffMembers, {
      fields: [vehicleRepairOrders.qualityCheckBy],
      references: [staffMembers.id],
      relationName: 'qualityCheckedRepairOrders',
    }),

    // Deleted by staff relation
    deletedByStaff: one(staffMembers, {
      fields: [vehicleRepairOrders.deletedBy],
      references: [staffMembers.id],
      relationName: 'deletedRepairOrders',
    }),
  }),
);
