import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { addresses } from './address.schema';
import { LocationType, LocationStatus } from '../../shared/types';
import { createBaseEntityBusinessIndexes } from './common-fields.schema';
import { users } from './users.schema';
import { leadLocations } from './leads.schema';
// Define pgEnum types for location types and status
export const locationTypeEnum = pgEnum('location_type', [
  LocationType.OFFICE,
  LocationType.RETAIL,
  LocationType.MANUFACTURING,
  LocationType.MAINSITE,
  LocationType.VEHICLE,
  LocationType.WAREHOUSE,
]);

export const locationStatusEnum = pgEnum('location_status', [
  LocationStatus.ACTIVE,
  LocationStatus.INACTIVE,
]);

export const locations = pgTable(
  'locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    code: text('code').notNull(),
    addressId: uuid('address_id')
      .notNull()
      .references(() => addresses.id),
    type: locationTypeEnum('type').default(LocationType.MAINSITE).notNull(),
    status: locationStatusEnum('status')
      .default(LocationStatus.ACTIVE)
      .notNull(),
    linkedLocationIds: uuid('linked_location_ids').array(),
  },
  (t) => ({
    idIndex: index('location_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'locations'),
    codeIndex: index('location_code_index').on(t.code),
    addressIdIndex: index('location_address_id_index').on(t.addressId),
    uniqueBusinessName: uniqueIndex('location_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessCode: uniqueIndex('location_business_code_unique')
      .on(t.businessId, t.code)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Locations Relations
export const locationsRelations = relations(locations, ({ one, many }) => ({
  business: one(business, {
    fields: [locations.businessId],
    references: [business.id],
  }),
  address: one(addresses, {
    fields: [locations.addressId],
    references: [addresses.id],
  }),
  createdBy: one(users, {
    fields: [locations.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [locations.updatedBy],
    references: [users.id],
  }),
  leadLocations: many(leadLocations),
}));
