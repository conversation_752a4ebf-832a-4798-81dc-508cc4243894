// src/drizzle/schema/quotations.schema.ts
import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
  decimal,
  timestamp,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { customers } from './customers.schema';
import { products } from './products.schema';
import { productVariants } from './products.schema';
import { users } from './users.schema';
import { salesOrders } from './sales-orders.schema';
import { media } from './media.schema';
import {
  locationAllocationFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Enums
export const quotationStatusEnum = pgEnum('quotation_status', [
  'draft',
  'sent',
  'accepted',
  'rejected',
  'expired',
  'converted',
  'revised',
]);

export const quotationSourceEnum = pgEnum('quotation_source', [
  'manual',
  'website',
  'email',
  'phone',
  'sales_rep',
  'trade_show',
  'referral',
]);

export const quotationPriorityEnum = pgEnum('quotation_priority', [
  'low',
  'medium',
  'high',
  'urgent',
]);

// Main quotations table
export const quotations = pgTable(
  'quotations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationNumber: text('quotation_number').notNull(),
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id, { onDelete: 'restrict' }),

    // Quotation details
    quotationDate: timestamp('quotation_date').defaultNow().notNull(),
    validUntilDate: timestamp('valid_until_date').notNull(),
    status: quotationStatusEnum('status').default('draft').notNull(),
    source: quotationSourceEnum('source').default('manual').notNull(),
    priority: quotationPriorityEnum('priority').default('medium').notNull(),

    // Financial fields
    subtotal: decimal('subtotal', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    taxAmount: decimal('tax_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    discountAmount: decimal('discount_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    discountPercentage: decimal('discount_percentage', {
      precision: 5,
      scale: 2,
    }),
    shippingAmount: decimal('shipping_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    totalAmount: decimal('total_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),

    // Currency support
    currencyCode: text('currency_code').default('USD').notNull(),
    exchangeRate: decimal('exchange_rate', { precision: 10, scale: 6 }).default(
      '1.000000',
    ),

    // Terms and conditions
    paymentTerms: text('payment_terms'),
    deliveryTerms: text('delivery_terms'),
    termsAndConditions: text('terms_and_conditions'),

    // Addresses
    billingAddress: jsonb('billing_address'),
    shippingAddress: jsonb('shipping_address'),

    // Sales tracking
    salespersonId: uuid('salesperson_id').references(() => users.id, {
      onDelete: 'set null',
    }),
    salesTeamId: uuid('sales_team_id'),
    commissionRate: decimal('commission_rate', { precision: 5, scale: 2 }),

    // Conversion tracking
    convertedToOrderId: uuid('converted_to_order_id').references(
      () => salesOrders.id,
      { onDelete: 'set null' },
    ),
    convertedDate: timestamp('converted_date'),
    conversionRate: decimal('conversion_rate', { precision: 5, scale: 2 }),

    // Version control
    versionNumber: integer('version_number').default(1).notNull(),
    parentQuotationId: uuid('parent_quotation_id').references(
      () => quotations.id,
      { onDelete: 'set null' },
    ),

    // Follow-up
    followUpDate: timestamp('follow_up_date'),
    lastContactDate: timestamp('last_contact_date'),

    // Additional info
    subject: text('subject'),
    notes: text('notes'),
    internalNotes: text('internal_notes'),
    rejectionReason: text('rejection_reason'),

    // Approval workflow
    requiresApproval: boolean('requires_approval').default(false),
    approvedBy: uuid('approved_by').references(() => users.id, {
      onDelete: 'set null',
    }),
    approvedDate: timestamp('approved_date'),

    // Customer acceptance
    acceptedBy: text('accepted_by'), // Customer name/signature
    acceptedDate: timestamp('accepted_date'),
    customerPO: text('customer_po'),

    // Location allocation
    ...locationAllocationFields,

    // SEO and sharing
    publicUrl: text('public_url'),
    publicToken: text('public_token'),
    viewCount: integer('view_count').default(0),
    lastViewedAt: timestamp('last_viewed_at'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotations'),
    quotationNumberIndex: index('quotations_number_index').on(
      t.quotationNumber,
    ),
    customerIdIndex: index('quotations_customer_id_index').on(t.customerId),
    statusIndex: index('quotations_status_index').on(t.status),
    quotationDateIndex: index('quotations_date_index').on(t.quotationDate),
    validUntilDateIndex: index('quotations_valid_until_index').on(
      t.validUntilDate,
    ),
    salespersonIdIndex: index('quotations_salesperson_index').on(
      t.salespersonId,
    ),
    convertedToOrderIdIndex: index('quotations_converted_order_index').on(
      t.convertedToOrderId,
    ),
    parentQuotationIdIndex: index('quotations_parent_index').on(
      t.parentQuotationId,
    ),

    // Composite indexes for filtering
    businessStatusDateIndex: index('quotations_business_status_date_index')
      .on(t.businessId, t.status, t.quotationDate)
      .where(sql`${t.isDeleted} = false`),
    businessCustomerDateIndex: index('quotations_business_customer_date_index')
      .on(t.businessId, t.customerId, t.quotationDate)
      .where(sql`${t.isDeleted} = false`),
    businessSalespersonDateIndex: index(
      'quotations_business_salesperson_date_index',
    )
      .on(t.businessId, t.salespersonId, t.quotationDate)
      .where(sql`${t.isDeleted} = false`),
    businessExpiryIndex: index('quotations_business_expiry_index')
      .on(t.businessId, t.validUntilDate, t.status)
      .where(sql`${t.isDeleted} = false AND ${t.status} IN ('draft', 'sent')`),

    // Public sharing
    publicTokenIndex: index('quotations_public_token_index')
      .on(t.publicToken)
      .where(sql`${t.publicToken} IS NOT NULL`),

    // Unique constraints
    uniqueQuotationNumber: uniqueIndex('quotations_number_unique')
      .on(t.businessId, t.quotationNumber)
      .where(sql`${t.isDeleted} = false`),
    uniquePublicToken: uniqueIndex('quotations_public_token_unique')
      .on(t.publicToken)
      .where(sql`${t.publicToken} IS NOT NULL`),
  }),
);

// Quotation lines table
export const quotationLines = pgTable(
  'quotation_lines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => quotations.id, { onDelete: 'cascade' }),

    // Product information
    productId: uuid('product_id').references(() => products.id, {
      onDelete: 'restrict',
    }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'restrict',
    }),

    // Line details
    productName: text('product_name').notNull(),
    productDescription: text('product_description'),
    productCode: text('product_code'),

    // Quantities
    quantity: decimal('quantity', { precision: 10, scale: 2 }).notNull(),
    unitOfMeasure: text('unit_of_measure'),

    // Pricing
    unitPrice: decimal('unit_price', { precision: 10, scale: 2 }).notNull(),
    listPrice: decimal('list_price', { precision: 10, scale: 2 }),
    priceTaxType: text('price_tax_type').default('exclusive').notNull(),
    discountPercentage: decimal('discount_percentage', {
      precision: 5,
      scale: 2,
    }).default('0'),
    discountAmount: decimal('discount_amount', {
      precision: 10,
      scale: 2,
    }).default('0'),
    lineSubtotal: decimal('line_subtotal', {
      precision: 10,
      scale: 2,
    }).notNull(),
    taxRate: decimal('tax_rate', { precision: 5, scale: 2 }).default('0'),
    taxAmount: decimal('tax_amount', { precision: 10, scale: 2 })
      .default('0')
      .notNull(),
    lineTotal: decimal('line_total', { precision: 10, scale: 2 }).notNull(),

    // Margin tracking
    costPrice: decimal('cost_price', { precision: 10, scale: 2 }),
    marginAmount: decimal('margin_amount', { precision: 10, scale: 2 }),
    marginPercentage: decimal('margin_percentage', { precision: 5, scale: 2 }),

    // Lead time
    leadTimeDays: integer('lead_time_days'),
    estimatedDeliveryDate: timestamp('estimated_delivery_date'),

    // Options and notes
    isOptional: boolean('is_optional').default(false),
    notes: text('notes'),
    internalNotes: text('internal_notes'),

    // Display
    sortOrder: integer('sort_order').default(0),
    groupName: text('group_name'),
    hidePrice: boolean('hide_price').default(false),

    // Images
    imageId: uuid('image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotation_lines'),
    quotationIdIndex: index('quotation_lines_quotation_id_index').on(
      t.quotationId,
    ),
    productIdIndex: index('quotation_lines_product_id_index').on(t.productId),
    variantIdIndex: index('quotation_lines_variant_id_index').on(t.variantId),
    sortOrderIndex: index('quotation_lines_sort_order_index').on(
      t.quotationId,
      t.sortOrder,
    ),
    groupNameIndex: index('quotation_lines_group_name_index').on(
      t.quotationId,
      t.groupName,
    ),
  }),
);

// Quotation terms table (for multiple terms per quotation)
export const quotationTerms = pgTable(
  'quotation_terms',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => quotations.id, { onDelete: 'cascade' }),

    // Term details
    termType: text('term_type').notNull(), // payment, delivery, warranty, service
    termTitle: text('term_title').notNull(),
    termDescription: text('term_description').notNull(),

    // Display
    sortOrder: integer('sort_order').default(0),
    isDefault: boolean('is_default').default(false),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotation_terms'),
    quotationIdIndex: index('quotation_terms_quotation_id_index').on(
      t.quotationId,
    ),
    termTypeIndex: index('quotation_terms_type_index').on(t.termType),
    sortOrderIndex: index('quotation_terms_sort_order_index').on(
      t.quotationId,
      t.sortOrder,
    ),
  }),
);

// Quotation attachments table
export const quotationAttachments = pgTable(
  'quotation_attachments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => quotations.id, { onDelete: 'cascade' }),
    mediaId: uuid('media_id')
      .notNull()
      .references(() => media.id, { onDelete: 'restrict' }),

    // Attachment details
    attachmentType: text('attachment_type'), // brochure, specification, terms, other
    title: text('title'),
    description: text('description'),

    // Display
    sortOrder: integer('sort_order').default(0),
    isPublic: boolean('is_public').default(true), // Show in customer view
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotation_attachments'),
    quotationIdIndex: index('quotation_attachments_quotation_id_index').on(
      t.quotationId,
    ),
    mediaIdIndex: index('quotation_attachments_media_id_index').on(t.mediaId),
    sortOrderIndex: index('quotation_attachments_sort_order_index').on(
      t.quotationId,
      t.sortOrder,
    ),
  }),
);

// Quotation history table
export const quotationHistory = pgTable(
  'quotation_history',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => quotations.id, { onDelete: 'cascade' }),

    // Event tracking
    eventType: text('event_type').notNull(), // created, updated, sent, viewed, accepted, rejected, etc.
    eventData: jsonb('event_data'),

    // Status change
    previousStatus: quotationStatusEnum('previous_status'),
    newStatus: quotationStatusEnum('new_status'),

    // User tracking
    performedBy: uuid('performed_by').references(() => users.id, {
      onDelete: 'set null',
    }),
    performedAt: timestamp('performed_at').defaultNow().notNull(),

    // Additional info
    ipAddress: text('ip_address'),
    userAgent: text('user_agent'),
    notes: text('notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotation_history'),
    quotationIdIndex: index('quotation_history_quotation_id_index').on(
      t.quotationId,
    ),
    eventTypeIndex: index('quotation_history_event_type_index').on(t.eventType),
    performedAtIndex: index('quotation_history_performed_at_index').on(
      t.performedAt,
    ),
    performedByIndex: index('quotation_history_performed_by_index').on(
      t.performedBy,
    ),

    // Composite index for history queries
    quotationEventDateIndex: index(
      'quotation_history_quotation_event_date_index',
    )
      .on(t.quotationId, t.eventType, t.performedAt)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Quotation templates table
export const quotationTemplates = pgTable(
  'quotation_templates',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    templateName: text('template_name').notNull(),
    templateCode: text('template_code').notNull(),

    // Template content
    description: text('description'),
    defaultValidityDays: integer('default_validity_days').default(30),

    // Default terms
    defaultPaymentTerms: text('default_payment_terms'),
    defaultDeliveryTerms: text('default_delivery_terms'),
    defaultTermsAndConditions: text('default_terms_and_conditions'),

    // Financial defaults
    defaultDiscountPercentage: decimal('default_discount_percentage', {
      precision: 5,
      scale: 2,
    }),
    defaultTaxRate: decimal('default_tax_rate', { precision: 5, scale: 2 }),

    // Template settings
    isActive: boolean('is_active').default(true).notNull(),
    requiresApproval: boolean('requires_approval').default(false),
    autoNumbering: boolean('auto_numbering').default(true),
    numberPrefix: text('number_prefix'),
    numberSuffix: text('number_suffix'),

    // Usage tracking
    usageCount: integer('usage_count').default(0),
    lastUsedAt: timestamp('last_used_at'),

    // Location allocation
    ...locationAllocationFields,
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotation_templates'),
    templateNameIndex: index('quotation_templates_name_index').on(
      t.templateName,
    ),
    templateCodeIndex: index('quotation_templates_code_index').on(
      t.templateCode,
    ),
    isActiveIndex: index('quotation_templates_active_index').on(t.isActive),

    // Unique constraint
    uniqueTemplateCode: uniqueIndex('quotation_templates_code_unique')
      .on(t.businessId, t.templateCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Quotation follow-ups table
export const quotationFollowUps = pgTable(
  'quotation_follow_ups',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    quotationId: uuid('quotation_id')
      .notNull()
      .references(() => quotations.id, { onDelete: 'cascade' }),

    // Follow-up details
    followUpType: text('follow_up_type').notNull(), // email, call, meeting, task
    scheduledDate: timestamp('scheduled_date').notNull(),
    completedDate: timestamp('completed_date'),

    // Assignment
    assignedTo: uuid('assigned_to')
      .notNull()
      .references(() => users.id, { onDelete: 'restrict' }),

    // Content
    subject: text('subject').notNull(),
    description: text('description'),
    outcome: text('outcome'),

    // Status
    status: text('status').default('pending').notNull(), // pending, completed, cancelled
    priority: quotationPriorityEnum('priority').default('medium').notNull(),

    // Next action
    nextActionRequired: boolean('next_action_required').default(false),
    nextActionDate: timestamp('next_action_date'),
    nextActionNotes: text('next_action_notes'),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'quotation_follow_ups'),
    quotationIdIndex: index('quotation_follow_ups_quotation_id_index').on(
      t.quotationId,
    ),
    assignedToIndex: index('quotation_follow_ups_assigned_to_index').on(
      t.assignedTo,
    ),
    scheduledDateIndex: index('quotation_follow_ups_scheduled_date_index').on(
      t.scheduledDate,
    ),
    statusIndex: index('quotation_follow_ups_status_index').on(t.status),

    // Composite index for task management
    assignedStatusDateIndex: index(
      'quotation_follow_ups_assigned_status_date_index',
    )
      .on(t.assignedTo, t.status, t.scheduledDate)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const quotationsRelations = relations(quotations, ({ one, many }) => ({
  // Many-to-one relations
  business: one(business, {
    fields: [quotations.businessId],
    references: [business.id],
  }),
  customer: one(customers, {
    fields: [quotations.customerId],
    references: [customers.id],
  }),
  salesperson: one(users, {
    fields: [quotations.salespersonId],
    references: [users.id],
    relationName: 'salespersonQuotations',
  }),
  convertedToOrder: one(salesOrders, {
    fields: [quotations.convertedToOrderId],
    references: [salesOrders.id],
  }),
  parentQuotation: one(quotations, {
    fields: [quotations.parentQuotationId],
    references: [quotations.id],
    relationName: 'childQuotations',
  }),
  approvedByUser: one(users, {
    fields: [quotations.approvedBy],
    references: [users.id],
    relationName: 'approvedQuotations',
  }),
  // Audit relations
  createdByUser: one(users, {
    fields: [quotations.createdBy],
    references: [users.id],
    relationName: 'createdQuotations',
  }),
  updatedByUser: one(users, {
    fields: [quotations.updatedBy],
    references: [users.id],
    relationName: 'updatedQuotations',
  }),
  // One-to-many relations
  quotationLines: many(quotationLines),
  quotationTerms: many(quotationTerms),
  quotationAttachments: many(quotationAttachments),
  quotationHistory: many(quotationHistory),
  quotationFollowUps: many(quotationFollowUps),
  childQuotations: many(quotations, {
    relationName: 'childQuotations',
  }),
}));

export const quotationLinesRelations = relations(quotationLines, ({ one }) => ({
  // Many-to-one relations
  business: one(business, {
    fields: [quotationLines.businessId],
    references: [business.id],
  }),
  quotation: one(quotations, {
    fields: [quotationLines.quotationId],
    references: [quotations.id],
  }),
  product: one(products, {
    fields: [quotationLines.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [quotationLines.variantId],
    references: [productVariants.id],
  }),
  image: one(media, {
    fields: [quotationLines.imageId],
    references: [media.id],
  }),
  // Audit relations
  createdByUser: one(users, {
    fields: [quotationLines.createdBy],
    references: [users.id],
    relationName: 'createdQuotationLines',
  }),
  updatedByUser: one(users, {
    fields: [quotationLines.updatedBy],
    references: [users.id],
    relationName: 'updatedQuotationLines',
  }),
}));

export const quotationTermsRelations = relations(quotationTerms, ({ one }) => ({
  // Many-to-one relations
  business: one(business, {
    fields: [quotationTerms.businessId],
    references: [business.id],
  }),
  quotation: one(quotations, {
    fields: [quotationTerms.quotationId],
    references: [quotations.id],
  }),
  // Audit relations
  createdByUser: one(users, {
    fields: [quotationTerms.createdBy],
    references: [users.id],
    relationName: 'createdQuotationTerms',
  }),
  updatedByUser: one(users, {
    fields: [quotationTerms.updatedBy],
    references: [users.id],
    relationName: 'updatedQuotationTerms',
  }),
}));

export const quotationAttachmentsRelations = relations(
  quotationAttachments,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [quotationAttachments.businessId],
      references: [business.id],
    }),
    quotation: one(quotations, {
      fields: [quotationAttachments.quotationId],
      references: [quotations.id],
    }),
    media: one(media, {
      fields: [quotationAttachments.mediaId],
      references: [media.id],
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [quotationAttachments.createdBy],
      references: [users.id],
      relationName: 'createdQuotationAttachments',
    }),
    updatedByUser: one(users, {
      fields: [quotationAttachments.updatedBy],
      references: [users.id],
      relationName: 'updatedQuotationAttachments',
    }),
  }),
);

export const quotationHistoryRelations = relations(
  quotationHistory,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [quotationHistory.businessId],
      references: [business.id],
    }),
    quotation: one(quotations, {
      fields: [quotationHistory.quotationId],
      references: [quotations.id],
    }),
    performedByUser: one(users, {
      fields: [quotationHistory.performedBy],
      references: [users.id],
      relationName: 'performedQuotationHistory',
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [quotationHistory.createdBy],
      references: [users.id],
      relationName: 'createdQuotationHistory',
    }),
    updatedByUser: one(users, {
      fields: [quotationHistory.updatedBy],
      references: [users.id],
      relationName: 'updatedQuotationHistory',
    }),
  }),
);

export const quotationTemplatesRelations = relations(
  quotationTemplates,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [quotationTemplates.businessId],
      references: [business.id],
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [quotationTemplates.createdBy],
      references: [users.id],
      relationName: 'createdQuotationTemplates',
    }),
    updatedByUser: one(users, {
      fields: [quotationTemplates.updatedBy],
      references: [users.id],
      relationName: 'updatedQuotationTemplates',
    }),
  }),
);

export const quotationFollowUpsRelations = relations(
  quotationFollowUps,
  ({ one }) => ({
    // Many-to-one relations
    business: one(business, {
      fields: [quotationFollowUps.businessId],
      references: [business.id],
    }),
    quotation: one(quotations, {
      fields: [quotationFollowUps.quotationId],
      references: [quotations.id],
    }),
    assignedToUser: one(users, {
      fields: [quotationFollowUps.assignedTo],
      references: [users.id],
      relationName: 'assignedQuotationFollowUps',
    }),
    // Audit relations
    createdByUser: one(users, {
      fields: [quotationFollowUps.createdBy],
      references: [users.id],
      relationName: 'createdQuotationFollowUps',
    }),
    updatedByUser: one(users, {
      fields: [quotationFollowUps.updatedBy],
      references: [users.id],
      relationName: 'updatedQuotationFollowUps',
    }),
  }),
);

// Export all schemas
export const quotationSchemas = {
  quotations,
  quotationLines,
  quotationTerms,
  quotationAttachments,
  quotationHistory,
  quotationTemplates,
  quotationFollowUps,
};
