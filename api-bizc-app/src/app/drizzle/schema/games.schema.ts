import {
  boolean,
  index,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { promoCodes } from './promo-codes.schema';
import { media } from './media.schema';
import { gameParticipants } from './game-participants.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { GameType, GameStatus } from '../../shared/types';

// Game types enum
export const gameEnum = pgEnum('game', [
  GameType.CLICK_CHALLENGE,
  GameType.LUCKY_SPINNER,
  GameType.MEMORY_GAME,
  GameType.QUIZ_GAME,
  GameType.SLIDING_PUZZLE,
  GameType.SPINNER_GAME,
]);

// Game status enum
export const gameStatusEnum = pgEnum('game_status', [
  GameStatus.ACTIVE,
  GameStatus.INACTIVE,
]);

export const games = pgTable(
  'games',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    description: text('description'),
    publicId: text('public_id').notNull(),
    game: gameEnum('game').notNull(),

    // Purchase and configuration
    configuration: jsonb('configuration'), // Store game-specific settings as JSON

    // Promo code relationship
    promoCode: uuid('promo_code').references(() => promoCodes.id),

    // Media
    ogImage: uuid('og_image').references(() => media.id),

    // Pricing and financial

    // Status and lifecycle
    status: gameStatusEnum('status').default(GameStatus.ACTIVE).notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'games'),
    nameIndex: index('games_name_index').on(t.name),
    gameTypeIndex: index('games_game_type_index').on(t.game),
    statusIndex: index('games_status_index').on(t.status),
    promoCodeIndex: index('games_promo_code_index').on(t.promoCode),
    ogImageIndex: index('games_og_image_index').on(t.ogImage),

    // Composite indexes for filtering and sorting
    businessGameTypeIndex: index('games_business_game_type_index')
      .on(t.businessId, t.game)
      .where(sql`${t.isDeleted} = false`),
    businessStatusIndex: index('games_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),

    // Search and filtering indexes
    businessNameIndex: index('games_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessPublicIdIndex: index('games_business_public_id_index')
      .on(t.businessId, t.publicId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('games_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessPublicId: uniqueIndex('games_business_public_id_unique')
      .on(t.businessId, t.publicId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Games Relations
export const gamesRelations = relations(games, ({ one, many }) => ({
  business: one(business, {
    fields: [games.businessId],
    references: [business.id],
  }),
  promoCode: one(promoCodes, {
    fields: [games.promoCode],
    references: [promoCodes.id],
  }),
  ogImage: one(media, {
    fields: [games.ogImage],
    references: [media.id],
  }),
  createdBy: one(users, {
    fields: [games.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [games.updatedBy],
    references: [users.id],
  }),
  gameParticipants: many(gameParticipants),
}));

// Export types for TypeScript usage
export type Game = typeof games.$inferSelect;
export type NewGame = typeof games.$inferInsert;
