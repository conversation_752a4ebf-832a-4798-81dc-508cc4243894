import {
  boolean,
  index,
  pgTable,
  uniqueIndex,
  uuid,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { business } from './business.schema';
import { staffMembers } from './staff.schema';
import { leaveTypes } from './leave-types.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export const staffLeaveTypes = pgTable(
  'staff_leave_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
    leaveTypeId: uuid('leave_type_id')
      .notNull()
      .references(() => leaveTypes.id),
    isActive: boolean('is_active').default(true).notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'staff_leave_types'),

    // Entity-specific indexes
    staffIdIndex: index('staff_leave_types_staff_id_index').on(t.staffId),
    leaveTypeIdIndex: index('staff_leave_types_leave_type_id_index').on(
      t.leaveTypeId,
    ),
    isActiveIndex: index('staff_leave_types_is_active_index').on(t.isActive),

    // Composite indexes for performance
    businessStaffIndex: index('staff_leave_types_business_staff_index')
      .on(t.businessId, t.staffId)
      .where(sql`${t.isDeleted} = false`),
    businessLeaveTypeIndex: index('staff_leave_types_business_leave_type_index')
      .on(t.businessId, t.leaveTypeId)
      .where(sql`${t.isDeleted} = false`),
    staffLeaveTypeActiveIndex: index(
      'staff_leave_types_staff_leave_type_active_index',
    )
      .on(t.staffId, t.leaveTypeId, t.isActive)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate assignments
    uniqueStaffLeaveType: uniqueIndex(
      'staff_leave_types_staff_leave_type_unique',
    )
      .on(t.businessId, t.staffId, t.leaveTypeId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Staff Leave Types Relations
export const staffLeaveTypesRelations = relations(
  staffLeaveTypes,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [staffLeaveTypes.businessId],
      references: [business.id],
      relationName: 'businessStaffLeaveTypes',
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [staffLeaveTypes.staffId],
      references: [staffMembers.id],
      relationName: 'staffLeaveTypes',
    }),

    // Leave type relation
    leaveType: one(leaveTypes, {
      fields: [staffLeaveTypes.leaveTypeId],
      references: [leaveTypes.id],
      relationName: 'leaveTypeStaffAssignments',
    }),

    // Audit relations
    creator: one(users, {
      fields: [staffLeaveTypes.createdBy],
      references: [users.id],
      relationName: 'createdStaffLeaveTypes',
    }),
    updater: one(users, {
      fields: [staffLeaveTypes.updatedBy],
      references: [users.id],
      relationName: 'updatedStaffLeaveTypes',
    }),
  }),
);
