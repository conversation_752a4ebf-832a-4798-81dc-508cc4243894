import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  decimal,
  date,
  boolean,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { vehicles } from './vehicles.schema';
import { media } from './media.schema';
import { accounts } from './accounts.schema';
import { paymentMethods } from './payment-methods.schema';
import {
  auditFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

import { users } from './users.schema';
export enum VehicleFineType {
  PARKING = 'parking',
  SPEEDING = 'speeding',
  TRAFFIC_VIOLATION = 'traffic_violation',
  TOLL = 'toll',
  REGISTRATION = 'registration',
  INSPECTION = 'inspection',
  FUEL_TAX = 'fuel_tax',
  OVERWEIGHT = 'overweight',
  OTHER = 'other',
}

export const vehicleFineTypeEnum = pgEnum('vehicle_fine_type', [
  VehicleFineType.PARKING,
  VehicleFineType.SPEEDING,
  VehicleFineType.TRAFFIC_VIOLATION,
  VehicleFineType.TOLL,
  VehicleFineType.REGISTRATION,
  VehicleFineType.INSPECTION,
  VehicleFineType.FUEL_TAX,
  VehicleFineType.OVERWEIGHT,
  VehicleFineType.OTHER,
]);

export const vehicleFines = pgTable(
  'vehicle_fines',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Registration section fields
    fineNumber: text('fine_number').notNull(),
    fineType: vehicleFineTypeEnum('fine_type').notNull(),
    vehicleId: uuid('vehicle_id')
      .notNull()
      .references(() => vehicles.id),
    fineAmount: decimal('fine_amount', { precision: 12, scale: 2 }).notNull(),
    dateOfFine: date('date_of_fine').notNull(),
    scannedFileId: uuid('scanned_file_id').references(() => media.id), // Reference to media table

    // Traffic Offense section fields
    dateTimeOfOffense: timestamp('date_time_of_offense'),
    notes: text('notes'),

    // Payment section fields - following expenses.schema.ts pattern
    expenseAccountId: uuid('expense_account_id')
      .notNull()
      .references(() => accounts.id),
    paymentAccountId: uuid('payment_account_id')
      .notNull()
      .references(() => accounts.id),
    paymentDate: date('payment_date').notNull(), // When it was paid to authority
    paymentMethodId: uuid('payment_method_id')
      .notNull()
      .references(() => paymentMethods.id),
    referenceNumber: text('reference_number'), // Payment reference/receipt number

    // Status section fields
    paidToAuthority: boolean('paid_to_authority').default(false).notNull(),

    // Additional tracking fields
    dueDate: date('due_date'), // When the fine is due
    lateFee: decimal('late_fee', { precision: 12, scale: 2 }), // Any late fees applied
    totalAmount: decimal('total_amount', { precision: 12, scale: 2 }), // Fine amount + late fees

    // Authority details
    issuingAuthority: text('issuing_authority'), // Police, parking authority, etc.
    authorityReference: text('authority_reference'), // Authority's reference number
    location: text('location'), // Where the offense occurred
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'vehicle_fines'),
    vehicleIdIndex: index('vehicle_fines_vehicle_id_index').on(t.vehicleId),
    fineNumberIndex: index('vehicle_fines_fine_number_index').on(t.fineNumber),
    fineTypeIndex: index('vehicle_fines_fine_type_index').on(t.fineType),
    dateOfFineIndex: index('vehicle_fines_date_of_fine_index').on(t.dateOfFine),
    dueDateIndex: index('vehicle_fines_due_date_index').on(t.dueDate),
    paidToAuthorityIndex: index('vehicle_fines_paid_to_authority_index').on(
      t.paidToAuthority,
    ),
    expenseAccountIdIndex: index('vehicle_fines_expense_account_id_index').on(
      t.expenseAccountId,
    ),
    paymentAccountIdIndex: index('vehicle_fines_payment_account_id_index').on(
      t.paymentAccountId,
    ),
    paymentMethodIdIndex: index('vehicle_fines_payment_method_id_index').on(
      t.paymentMethodId,
    ),
    paymentDateIndex: index('vehicle_fines_payment_date_index').on(
      t.paymentDate,
    ),
    referenceNumberIndex: index('vehicle_fines_reference_number_index').on(
      t.referenceNumber,
    ),
    dateTimeOfOffenseIndex: index(
      'vehicle_fines_date_time_of_offense_index',
    ).on(t.dateTimeOfOffense),
    issuingAuthorityIndex: index('vehicle_fines_issuing_authority_index').on(
      t.issuingAuthority,
    ),
    scannedFileIdIndex: index('vehicle_fines_scanned_file_id_index').on(
      t.scannedFileId,
    ),

    // Unique constraints
    uniqueFineNumber: uniqueIndex(
      'vehicle_fines_business_fine_number_unique',
    ).on(t.businessId, t.fineNumber),
  }),
);

// Relations
export const vehicleFinesRelations = relations(vehicleFines, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [vehicleFines.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [vehicleFines.createdBy],
    references: [users.id],
    relationName: 'createdVehicleFines',
  }),
  updater: one(users, {
    fields: [vehicleFines.updatedBy],
    references: [users.id],
    relationName: 'updatedVehicleFines',
  }),

  // Vehicle relation
  vehicle: one(vehicles, {
    fields: [vehicleFines.vehicleId],
    references: [vehicles.id],
  }),

  // Media relation (scanned file)
  scannedFile: one(media, {
    fields: [vehicleFines.scannedFileId],
    references: [media.id],
  }),

  // Account relations
  expenseAccount: one(accounts, {
    fields: [vehicleFines.expenseAccountId],
    references: [accounts.id],
    relationName: 'fineExpenseAccount',
  }),
  paymentAccount: one(accounts, {
    fields: [vehicleFines.paymentAccountId],
    references: [accounts.id],
    relationName: 'finePaymentAccount',
  }),

  // Payment method relation
  paymentMethod: one(paymentMethods, {
    fields: [vehicleFines.paymentMethodId],
    references: [paymentMethods.id],
  }),
}));
