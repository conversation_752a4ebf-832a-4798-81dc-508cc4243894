import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import { assetCategories } from './asset-categories.schema';
import {
  locationAllocationFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { RentalItemCategoryStatus } from '@app/shared/types/rental-item-category.enum';

export const rentalItemCategoryStatusEnum = pgEnum(
  'rental_item_category_status',
  [RentalItemCategoryStatus.ACTIVE, RentalItemCategoryStatus.INACTIVE],
);

export const rentalItemCategories = pgTable(
  'rental_item_categories',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    shortCode: text('short_code'),
    parentId: uuid('parent_id').references(() => rentalItemCategories.id),
    description: text('description'),
    slug: text('slug'),
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),
    color: text('color'),
    image: uuid('image').references(() => media.id),

    // Asset category reference (automatically created)
    assetCategoryId: uuid('asset_category_id').references(
      () => assetCategories.id,
    ),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    status: rentalItemCategoryStatusEnum('status')
      .default(RentalItemCategoryStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('rental_item_categories_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'rental_item_categories'),
    nameIndex: index('rental_item_categories_name_index').on(t.name),
    parentIdIndex: index('rental_item_categories_parent_id_index').on(
      t.parentId,
    ),
    slugIndex: index('rental_item_categories_slug_index').on(t.slug),
    imageIndex: index('rental_item_categories_image_index').on(t.image),
    ogImageIndex: index('rental_item_categories_og_image_index').on(t.ogImage),
    assetCategoryIdIndex: index(
      'rental_item_categories_asset_category_id_index',
    ).on(t.assetCategoryId),
    positionIndex: index('rental_item_categories_position_index').on(
      t.position,
    ),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index(
      'rental_item_categories_business_position_index',
    )
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentPositionIndex: index(
      'rental_item_categories_business_parent_position_index',
    )
      .on(t.businessId, t.parentId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'rental_item_categories_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentStatusPositionIndex: index(
      'rental_item_categories_business_parent_status_position_index',
    )
      .on(t.businessId, t.parentId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('rental_item_categories_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('rental_item_categories_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessShortCodeIndex: index(
      'rental_item_categories_business_short_code_index',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex(
      'rental_item_categories_business_name_unique',
    )
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex(
      'rental_item_categories_business_slug_unique',
    )
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessShortCode: uniqueIndex(
      'rental_item_categories_business_short_code_unique',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for rental item category locations (many-to-many relationship)
export const rentalItemCategoryLocations = pgTable(
  'rental_item_category_locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    rentalItemCategoryId: uuid('rental_item_category_id')
      .notNull()
      .references(() => rentalItemCategories.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    // Standard audit fields (using reusable fields)
    // Base entity fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    rentalItemCategoryIdIndex: index(
      'rental_item_category_locations_rental_item_category_id_index',
    ).on(t.rentalItemCategoryId),
    locationIdIndex: index(
      'rental_item_category_locations_location_id_index',
    ).on(t.locationId),
    uniqueRentalItemCategoryLocation: uniqueIndex(
      'rental_item_category_locations_unique',
    ).on(t.rentalItemCategoryId, t.locationId),
  }),
);

// Relations
export const rentalItemCategoriesRelations = relations(
  rentalItemCategories,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [rentalItemCategories.businessId],
      references: [business.id],
    }),
    // Parent/child relations
    parent: one(rentalItemCategories, {
      fields: [rentalItemCategories.parentId],
      references: [rentalItemCategories.id],
    }),
    children: many(rentalItemCategories),
    // Media relations
    imageFile: one(media, {
      fields: [rentalItemCategories.image],
      references: [media.id],
    }),
    ogImageFile: one(media, {
      fields: [rentalItemCategories.ogImage],
      references: [media.id],
    }),
    // Asset category relation
    assetCategory: one(assetCategories, {
      fields: [rentalItemCategories.assetCategoryId],
      references: [assetCategories.id],
    }),
    // Junction table relation
    rentalItemCategoryLocations: many(rentalItemCategoryLocations),
    // Audit relations
    creator: one(users, {
      fields: [rentalItemCategories.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [rentalItemCategories.updatedBy],
      references: [users.id],
    }),
  }),
);

export const rentalItemCategoryLocationsRelations = relations(
  rentalItemCategoryLocations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [rentalItemCategoryLocations.businessId],
      references: [business.id],
    }),
    // Rental item category relation
    rentalItemCategory: one(rentalItemCategories, {
      fields: [rentalItemCategoryLocations.rentalItemCategoryId],
      references: [rentalItemCategories.id],
    }),
    // Location relation
    location: one(locations, {
      fields: [rentalItemCategoryLocations.locationId],
      references: [locations.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [rentalItemCategoryLocations.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [rentalItemCategoryLocations.updatedBy],
      references: [users.id],
    }),
  }),
);
