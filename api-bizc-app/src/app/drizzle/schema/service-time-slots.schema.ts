import {
  boolean,
  index,
  integer,
  pgEnum,
  pgTable,
  text,
  time,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { locations } from './locations.schema';
import { services } from './services.schema';
import { staffMembers } from './staff.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { business } from './business.schema';

import { users } from './users.schema';
// Day of Week TypeScript Enum
export enum DayOfWeek {
  MONDAY = 'Mon',
  TUESDAY = 'Tue',
  WEDNESDAY = 'Wed',
  THURSDAY = 'Thu',
  FRIDAY = 'Fri',
  SATURDAY = 'Sat',
  SUNDAY = 'Sun',
  ALL = 'All',
}

// Schedule Type TypeScript Enum
export enum ScheduleType {
  WEEKLY = 'weekly',
  BI_WEEKLY = 'bi_weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  ONE_TIME = 'one_time',
}

// Service Day of Week Enum - specific to service time slots
export const serviceDayOfWeekEnum = pgEnum('service_day_of_week', [
  DayOfWeek.MONDAY,
  DayOfWeek.TUESDAY,
  DayOfWeek.WEDNESDAY,
  DayOfWeek.THURSDAY,
  DayOfWeek.FRIDAY,
  DayOfWeek.SATURDAY,
  DayOfWeek.SUNDAY,
  DayOfWeek.ALL,
]);

// Schedule Type Enum
export const scheduleTypeEnum = pgEnum('schedule_type', [
  ScheduleType.WEEKLY,
  ScheduleType.BI_WEEKLY,
  ScheduleType.MONTHLY,
  ScheduleType.YEARLY,
  ScheduleType.ONE_TIME,
]);

// Service Time Slots table
export const serviceTimeSlots = pgTable(
  'service_time_slots',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),

    // Foreign key references
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),

    // Basic slot information
    slotName: text('slot_name').notNull(),

    // Time information
    startTime: time('start_time').notNull(),
    endTime: time('end_time').notNull(),

    // Capacity management
    maxAppointments: integer('max_appointments').default(1).notNull(),
    reservedCount: integer('reserved_count').default(0).notNull(),

    // Scheduling configuration
    scheduleType: scheduleTypeEnum('schedule_type')
      .default(ScheduleType.WEEKLY)
      .notNull(),
    dayOfWeek: serviceDayOfWeekEnum('service_day_of_week')
      .default(DayOfWeek.ALL)
      .notNull(),
    dayOfMonth: integer('day_of_month'), // For monthly scheduling (1-31)
    weekOfMonth: integer('week_of_month'), // For bi-weekly scheduling (1-5: 1st week, 2nd week, etc.)
    monthOfYear: integer('month_of_year'), // For yearly scheduling (1-12: Jan-Dec)
  },
  (t) => ({
    // Indexes for performance
    locationIdIndex: index('service_time_slots_location_id_index').on(
      t.locationId,
    ),
    slotNameIndex: index('service_time_slots_slot_name_index').on(t.slotName),
    startTimeIndex: index('service_time_slots_start_time_index').on(
      t.startTime,
    ),
    endTimeIndex: index('service_time_slots_end_time_index').on(t.endTime),
    scheduleTypeIndex: index('service_time_slots_schedule_type_index').on(
      t.scheduleType,
    ),
    dayOfWeekIndex: index('service_time_slots_day_of_week_index').on(
      t.dayOfWeek,
    ),
    dayOfMonthIndex: index('service_time_slots_day_of_month_index').on(
      t.dayOfMonth,
    ),
    weekOfMonthIndex: index('service_time_slots_week_of_month_index').on(
      t.weekOfMonth,
    ),
    monthOfYearIndex: index('service_time_slots_month_of_year_index').on(
      t.monthOfYear,
    ),

    // Composite indexes for common queries
    scheduleTypeTimeIndex: index(
      'service_time_slots_schedule_type_time_index',
    ).on(t.scheduleType, t.startTime),
    dayTimeIndex: index('service_time_slots_day_time_index').on(
      t.dayOfWeek,
      t.startTime,
    ),
    monthlyScheduleIndex: index('service_time_slots_monthly_schedule_index').on(
      t.dayOfMonth,
      t.startTime,
    ),
    biWeeklyScheduleIndex: index(
      'service_time_slots_bi_weekly_schedule_index',
    ).on(t.weekOfMonth, t.dayOfWeek, t.startTime),
    yearlyScheduleIndex: index('service_time_slots_yearly_schedule_index').on(
      t.monthOfYear,
      t.dayOfMonth,
      t.startTime,
    ),

    // Unique constraints
    uniqueLocationSlotName: uniqueIndex(
      'service_time_slots_location_slot_name_unique',
    ).on(t.locationId, t.slotName),

    uniqueLocationDateTime: uniqueIndex(
      'service_time_slots_location_date_time_unique',
    ).on(t.locationId, t.startTime, t.dayOfWeek),
  }),
);

// Service Time Slot Staff Join Table
export const serviceTimeSlotStaff = pgTable(
  'service_time_slot_staff',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    timeSlotId: uuid('time_slot_id')
      .notNull()
      .references(() => serviceTimeSlots.id),
    staffId: uuid('staff_id')
      .notNull()
      .references(() => staffMembers.id),
  },
  (t) => ({
    // Indexes for performance
    timeSlotIdIndex: index('service_time_slot_staff_time_slot_id_index').on(
      t.timeSlotId,
    ),
    staffIdIndex: index('service_time_slot_staff_staff_id_index').on(t.staffId),

    // Unique constraint to prevent duplicate assignments
    uniqueTimeSlotStaff: uniqueIndex(
      'service_time_slot_staff_time_slot_staff_unique',
    ).on(t.timeSlotId, t.staffId),
  }),
);

// Service Time Slot Services Join Table
export const serviceTimeSlotServices = pgTable(
  'service_time_slot_services',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    timeSlotId: uuid('time_slot_id')
      .notNull()
      .references(() => serviceTimeSlots.id),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id),
  },
  (t) => ({
    // Indexes for performance
    timeSlotIdIndex: index('service_time_slot_services_time_slot_id_index').on(
      t.timeSlotId,
    ),
    serviceIdIndex: index('service_time_slot_services_service_id_index').on(
      t.serviceId,
    ),

    // Unique constraint to prevent duplicate service assignments
    uniqueTimeSlotService: uniqueIndex(
      'service_time_slot_services_time_slot_service_unique',
    ).on(t.timeSlotId, t.serviceId),
  }),
);

// Service Time Slots Relations
export const serviceTimeSlotsRelations = relations(
  serviceTimeSlots,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceTimeSlots.businessId],
      references: [business.id],
      relationName: 'businessServiceTimeSlots',
    }),

    // Location relation
    location: one(locations, {
      fields: [serviceTimeSlots.locationId],
      references: [locations.id],
      relationName: 'locationServiceTimeSlots',
    }),

    // Junction table relations
    serviceTimeSlotStaff: many(serviceTimeSlotStaff, {
      relationName: 'timeSlotStaff',
    }),
    serviceTimeSlotServices: many(serviceTimeSlotServices, {
      relationName: 'timeSlotServices',
    }),

    // Audit relations
    creator: one(users, {
      fields: [serviceTimeSlots.createdBy],
      references: [users.id],
      relationName: 'createdServiceTimeSlots',
    }),
    updater: one(users, {
      fields: [serviceTimeSlots.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceTimeSlots',
    }),
  }),
);

// Service Time Slot Staff Relations
export const serviceTimeSlotStaffRelations = relations(
  serviceTimeSlotStaff,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceTimeSlotStaff.businessId],
      references: [business.id],
      relationName: 'businessServiceTimeSlotStaff',
    }),

    // Time slot relation
    timeSlot: one(serviceTimeSlots, {
      fields: [serviceTimeSlotStaff.timeSlotId],
      references: [serviceTimeSlots.id],
      relationName: 'timeSlotStaff',
    }),

    // Staff member relation
    staffMember: one(staffMembers, {
      fields: [serviceTimeSlotStaff.staffId],
      references: [staffMembers.id],
      relationName: 'staffServiceTimeSlots',
    }),

    // Audit relations
    creator: one(users, {
      fields: [serviceTimeSlotStaff.createdBy],
      references: [users.id],
      relationName: 'createdServiceTimeSlotStaff',
    }),
    updater: one(users, {
      fields: [serviceTimeSlotStaff.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceTimeSlotStaff',
    }),
  }),
);

// Service Time Slot Services Relations
export const serviceTimeSlotServicesRelations = relations(
  serviceTimeSlotServices,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [serviceTimeSlotServices.businessId],
      references: [business.id],
      relationName: 'businessServiceTimeSlotServices',
    }),

    // Time slot relation
    timeSlot: one(serviceTimeSlots, {
      fields: [serviceTimeSlotServices.timeSlotId],
      references: [serviceTimeSlots.id],
      relationName: 'timeSlotServices',
    }),

    // Service relation
    service: one(services, {
      fields: [serviceTimeSlotServices.serviceId],
      references: [services.id],
      relationName: 'serviceTimeSlots',
    }),

    // Audit relations
    creator: one(users, {
      fields: [serviceTimeSlotServices.createdBy],
      references: [users.id],
      relationName: 'createdServiceTimeSlotServices',
    }),
    updater: one(users, {
      fields: [serviceTimeSlotServices.updatedBy],
      references: [users.id],
      relationName: 'updatedServiceTimeSlotServices',
    }),
  }),
);
