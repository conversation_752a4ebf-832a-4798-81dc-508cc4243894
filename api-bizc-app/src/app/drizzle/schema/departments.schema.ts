import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { staffMembers } from './staff.schema';
import { DepartmentStatus } from '@app/shared/types/department.enum';

export const departmentStatusEnum = pgEnum('department_status', [
  DepartmentStatus.ACTIVE,
  DepartmentStatus.INACTIVE,
]);

export const departments = pgTable(
  'departments',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    parentId: uuid('parent_id').references(() => departments.id),
    description: text('description'),
    status: departmentStatusEnum('status')
      .default(DepartmentStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'departments'),
    // Entity-specific indexes
    nameIndex: index('departments_name_index').on(t.name),
    parentIdIndex: index('departments_parent_id_index').on(t.parentId),
    statusIndex: index('departments_status_index').on(t.status),

    // Composite indexes for performance
    businessStatusIndex: index('departments_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessParentIndex: index('departments_business_parent_index')
      .on(t.businessId, t.parentId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('departments_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const departmentsRelations = relations(departments, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [departments.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [departments.createdBy],
    references: [users.id],
    relationName: 'createdDepartments',
  }),
  updater: one(users, {
    fields: [departments.updatedBy],
    references: [users.id],
    relationName: 'updatedDepartments',
  }),

  // Self-referencing relations for hierarchical structure
  parentDepartment: one(departments, {
    fields: [departments.parentId],
    references: [departments.id],
    relationName: 'parentDepartment',
  }),
  childDepartments: many(departments, {
    relationName: 'parentDepartment',
  }),

  // One-to-many relations
  staffMembers: many(staffMembers),
}));
