import {
  index,
  pgTable,
  text,
  uuid,
  pgEnum,
  uniqueIndex,
  timestamp,
  boolean,
  decimal,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { customers } from './customers.schema';
import { locations } from './locations.schema';
import { serviceTimeSlots } from './service-time-slots.schema';
import { services } from './services.schema';
import { staffMembers } from './staff.schema';
import { auditFields } from './common-fields.schema';
import { users } from './users.schema';

// Appointment Status TypeScript Enum
export enum AppointmentStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show',
}

// Booking Source TypeScript Enum
export enum BookingSource {
  ONLINE = 'online',
  PHONE = 'phone',
  WALK_IN = 'walk_in',
  MOBILE_APP = 'mobile_app',
}

// Appointment Status Enum
export const appointmentStatusEnum = pgEnum('appointment_status', [
  AppointmentStatus.SCHEDULED,
  AppointmentStatus.CONFIRMED,
  AppointmentStatus.IN_PROGRESS,
  AppointmentStatus.COMPLETED,
  AppointmentStatus.CANCELLED,
  AppointmentStatus.NO_SHOW,
]);

// Booking Source Enum
export const bookingSourceEnum = pgEnum('booking_source', [
  BookingSource.ONLINE,
  BookingSource.PHONE,
  BookingSource.WALK_IN,
  BookingSource.MOBILE_APP,
]);

// Appointments table
export const appointments = pgTable(
  'appointments',
  {
    id: uuid('id').defaultRandom().primaryKey(),

    // Business identifier
    appointmentNumber: text('appointment_number').notNull(),

    // Foreign key references
    customerId: uuid('customer_id')
      .notNull()
      .references(() => customers.id),
    locationId: uuid('location_id').references(() => locations.id),
    timeSlotId: uuid('time_slot_id').references(() => serviceTimeSlots.id),

    // Status and workflow
    status: appointmentStatusEnum('status').notNull(),

    // Scheduling information
    appointmentDate: timestamp('appointment_date').notNull(),

    // Administrative fields
    notes: text('notes'),
    cancellationReason: text('cancellation_reason'),

    // Process tracking
    confirmedAt: timestamp('confirmed_at'),
    reminderSentAt: timestamp('reminder_sent_at'),

    // Source tracking
    bookingSource: bookingSourceEnum('booking_source'),

    // Financial information
    totalEstimatedCost: decimal('total_estimated_cost', {
      precision: 10,
      scale: 2,
    }),
    depositAmount: decimal('deposit_amount', { precision: 10, scale: 2 }),
    depositPaid: boolean('deposit_paid').default(false),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    // Primary indexes
    idIndex: index('appointments_id_index').on(t.id),
    appointmentNumberIndex: index('appointments_appointment_number_index').on(
      t.appointmentNumber,
    ),
    customerIdIndex: index('appointments_customer_id_index').on(t.customerId),
    locationIdIndex: index('appointments_location_id_index').on(t.locationId),
    timeSlotIdIndex: index('appointments_time_slot_id_index').on(t.timeSlotId),
    statusIndex: index('appointments_status_index').on(t.status),
    appointmentDateIndex: index('appointments_appointment_date_index').on(
      t.appointmentDate,
    ),
    bookingSourceIndex: index('appointments_booking_source_index').on(
      t.bookingSource,
    ),
    confirmedAtIndex: index('appointments_confirmed_at_index').on(
      t.confirmedAt,
    ),
    reminderSentAtIndex: index('appointments_reminder_sent_at_index').on(
      t.reminderSentAt,
    ),
    depositPaidIndex: index('appointments_deposit_paid_index').on(
      t.depositPaid,
    ),

    // Composite indexes for common query patterns
    customerStatusIndex: index('appointments_customer_status_index')
      .on(t.customerId, t.status)
      .where(sql`${t.isDeleted} = false`),
    customerDateIndex: index('appointments_customer_date_index')
      .on(t.customerId, t.appointmentDate)
      .where(sql`${t.isDeleted} = false`),
    locationStatusIndex: index('appointments_location_status_index')
      .on(t.locationId, t.status)
      .where(sql`${t.isDeleted} = false`),
    locationDateIndex: index('appointments_location_date_index')
      .on(t.locationId, t.appointmentDate)
      .where(sql`${t.isDeleted} = false`),
    timeSlotDateIndex: index('appointments_time_slot_date_index')
      .on(t.timeSlotId, t.appointmentDate)
      .where(sql`${t.isDeleted} = false`),
    statusDateIndex: index('appointments_status_date_index')
      .on(t.status, t.appointmentDate)
      .where(sql`${t.isDeleted} = false`),
    bookingSourceDateIndex: index('appointments_booking_source_date_index')
      .on(t.bookingSource, t.appointmentDate)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints
    uniqueAppointmentNumber: uniqueIndex(
      'appointments_appointment_number_unique',
    )
      .on(t.appointmentNumber)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const appointmentsRelations = relations(
  appointments,
  ({ one, many }) => ({
    // Customer relation
    customer: one(customers, {
      fields: [appointments.customerId],
      references: [customers.id],
    }),

    // Location relation
    location: one(locations, {
      fields: [appointments.locationId],
      references: [locations.id],
    }),

    // Time slot relation
    timeSlot: one(serviceTimeSlots, {
      fields: [appointments.timeSlotId],
      references: [serviceTimeSlots.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [appointments.createdBy],
      references: [users.id],
      relationName: 'createdAppointments',
    }),
    updater: one(users, {
      fields: [appointments.updatedBy],
      references: [users.id],
      relationName: 'updatedAppointments',
    }),

    // Junction table relations
    appointmentServices: many(appointmentServices),
    appointmentStaff: many(appointmentStaff),
  }),
);

// Junction table for appointment services (many-to-many relationship)
export const appointmentServices = pgTable(
  'appointment_services',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    appointmentId: uuid('appointment_id')
      .notNull()
      .references(() => appointments.id, { onDelete: 'cascade' }),
    serviceId: uuid('service_id')
      .notNull()
      .references(() => services.id, { onDelete: 'cascade' }),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    appointmentIdIndex: index('appointment_services_appointment_id_index').on(
      t.appointmentId,
    ),
    serviceIdIndex: index('appointment_services_service_id_index').on(
      t.serviceId,
    ),

    // Composite indexes for common queries
    serviceAppointmentIndex: index(
      'appointment_services_service_appointment_index',
    )
      .on(t.serviceId, t.appointmentId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate service assignments
    uniqueAppointmentService: uniqueIndex(
      'appointment_services_appointment_service_unique',
    )
      .on(t.appointmentId, t.serviceId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const appointmentServicesRelations = relations(
  appointmentServices,
  ({ one }) => ({
    // Appointment relation
    appointment: one(appointments, {
      fields: [appointmentServices.appointmentId],
      references: [appointments.id],
    }),

    // Service relation
    service: one(services, {
      fields: [appointmentServices.serviceId],
      references: [services.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [appointmentServices.createdBy],
      references: [users.id],
      relationName: 'createdAppointmentServices',
    }),
    updater: one(users, {
      fields: [appointmentServices.updatedBy],
      references: [users.id],
      relationName: 'updatedAppointmentServices',
    }),
  }),
);

// Junction table for appointment staff (many-to-many relationship)
export const appointmentStaff = pgTable(
  'appointment_staff',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    appointmentId: uuid('appointment_id')
      .notNull()
      .references(() => appointments.id, { onDelete: 'cascade' }),
    staffMemberId: uuid('staff_member_id')
      .notNull()
      .references(() => staffMembers.id, { onDelete: 'cascade' }),

    // Standard audit fields
    ...auditFields,
  },
  (t) => ({
    appointmentIdIndex: index('appointment_staff_appointment_id_index').on(
      t.appointmentId,
    ),
    staffMemberIdIndex: index('appointment_staff_staff_member_id_index').on(
      t.staffMemberId,
    ),
    // Composite indexes for common queries
    staffAppointmentIndex: index('appointment_staff_staff_appointment_index')
      .on(t.staffMemberId, t.appointmentId)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraint to prevent duplicate staff assignments
    uniqueAppointmentStaff: uniqueIndex(
      'appointment_staff_appointment_staff_unique',
    )
      .on(t.appointmentId, t.staffMemberId)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export const appointmentStaffRelations = relations(
  appointmentStaff,
  ({ one }) => ({
    // Appointment relation
    appointment: one(appointments, {
      fields: [appointmentStaff.appointmentId],
      references: [appointments.id],
    }),

    // Staff relation
    staffMember: one(staffMembers, {
      fields: [appointmentStaff.staffMemberId],
      references: [staffMembers.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [appointmentStaff.createdBy],
      references: [users.id],
      relationName: 'createdAppointmentStaff',
    }),
    updater: one(users, {
      fields: [appointmentStaff.updatedBy],
      references: [users.id],
      relationName: 'updatedAppointmentStaff',
    }),
  }),
);
