import {
  boolean,
  index,
  pgEnum,
  pgTable,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { ModuleType } from '../../shared/types';
import { business } from './business.schema';
import { createBaseEntityFields } from './common-fields.schema';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
// Module types enum - combining previous module and submodule types
export const moduleTypeEnum = pgEnum('module_type', [
  // Main modules
  ModuleType.HRM,
  ModuleType.MARKETING,
  ModuleType.ASSETS,
  ModuleType.PAYMENT_ACCOUNT,
  ModuleType.INVENTORY,
  ModuleType.FINANCE,
  ModuleType.CRM,
  ModuleType.PROJECT,
  ModuleType.SUPPORT,

  // Former submodules now as modules
  ModuleType.ATTENDANCE,
  ModuleType.PAYROLL,
  ModuleType.RECRUITMENT,
  ModuleType.PERFORMANCE,
  ModuleType.LEAVE,
  ModuleType.TRAINING,
  ModuleType.CAMPAIGN,
  ModuleType.SOCIAL_MEDIA,
  ModuleType.EMAIL_MARKETING,
  ModuleType.SEO,
  ModuleType.FIXED_ASSETS,
  ModuleType.DIGITAL_ASSETS,
  ModuleType.MAINTENANCE,
  ModuleType.BANK_ACCOUNTS,
  ModuleType.PAYMENT_GATEWAYS,
  ModuleType.TRANSACTIONS,

  // Additional modules
  ModuleType.RETAIL_POS,
  ModuleType.RESTAURANT_POS,
  ModuleType.SALES,
  ModuleType.WORKORDERS,
  ModuleType.VEHICLES,
  ModuleType.VEHICLE_RESERVATIONS,
]);

// Junction table for business modules (many-to-many relationship)
export const businessModules = pgTable(
  'business_modules',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    moduleType: moduleTypeEnum('module_type').notNull(),
  },
  (t) => ({
    businessIdIndex: index('business_modules_business_id_index').on(
      t.businessId,
    ),
    moduleTypeIndex: index('business_modules_module_type_index').on(
      t.moduleType,
    ),
    uniqueBusinessModule: uniqueIndex('business_modules_unique').on(
      t.businessId,
      t.moduleType,
    ),
  }),
);

// Relations definitions

// Business Modules Relations
export const businessModulesRelations = relations(
  businessModules,
  ({ one }) => ({
    business: one(business, {
      fields: [businessModules.businessId],
      references: [business.id],
    }),
    createdBy: one(users, {
      fields: [businessModules.createdBy],
      references: [users.id],
    }),
    updatedBy: one(users, {
      fields: [businessModules.updatedBy],
      references: [users.id],
    }),
  }),
);
