import {
  boolean,
  index,
  pgEnum,
  pgTable,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import { users } from './users.schema';
import { locations } from './locations.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';

// Status enum for user location assignments
export const userLocationStatusEnum = pgEnum('user_location_status', [
  'active',
  'inactive',
]);

export const userLocations = pgTable(
  'user_locations',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    userId: uuid('user_id')
      .notNull()
      .references(() => users.id),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id),
    status: userLocationStatusEnum('status').default('active').notNull(),
  },
  (t) => ({
    // Standard indexes for base entity fields
    ...createBaseEntityBusinessIndexes(t, 'user_locations'),

    // Entity-specific indexes
    userIdIndex: index('user_locations_user_id_index').on(t.userId),
    locationIdIndex: index('user_locations_location_id_index').on(t.locationId),
    statusIndex: index('user_locations_status_index').on(t.status),

    // Composite indexes for performance
    businessUserIndex: index('user_locations_business_user_index').on(
      t.businessId,
      t.userId,
    ),
    businessLocationIndex: index('user_locations_business_location_index').on(
      t.businessId,
      t.locationId,
    ),
    userLocationIndex: index('user_locations_user_location_index').on(
      t.userId,
      t.locationId,
    ),
  }),
);

// Relations definitions

// User Locations Relations
export const userLocationsRelations = relations(userLocations, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [userLocations.businessId],
    references: [business.id],
  }),

  // User relation
  user: one(users, {
    fields: [userLocations.userId],
    references: [users.id],
  }),

  // Location relation
  location: one(locations, {
    fields: [userLocations.locationId],
    references: [locations.id],
  }),

  // Audit field relations
  creator: one(users, {
    fields: [userLocations.createdBy],
    references: [users.id],
    relationName: 'createdUserLocations',
  }),
  updater: one(users, {
    fields: [userLocations.updatedBy],
    references: [users.id],
    relationName: 'updatedUserLocations',
  }),
}));
