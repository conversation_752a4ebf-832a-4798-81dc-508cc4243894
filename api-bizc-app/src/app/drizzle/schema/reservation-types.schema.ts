import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import {
  locationAllocationFields,
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import {
  ReservationTypeStatus,
  ReservationTypeCategory,
} from '../../shared/types';

export const reservationTypeStatusEnum = pgEnum('reservation_type_status', [
  ReservationTypeStatus.ACTIVE,
  ReservationTypeStatus.INACTIVE,
]);

export const reservationTypeCategoryEnum = pgEnum('reservation_type_category', [
  ReservationTypeCategory.ACCOMMODATION,
  ReservationTypeCategory.EVENT,
]);

export const reservationTypes = pgTable(
  'reservation_types',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    shortCode: text('short_code'),
    parentId: uuid('parent_id').references(() => reservationTypes.id),
    category: reservationTypeCategoryEnum('category').notNull(),
    description: text('description'),
    slug: text('slug'),
    availableOnline: boolean('available_online').default(false).notNull(),
    position: integer('position').default(0).notNull(),
    image: uuid('image').references(() => media.id),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    status: reservationTypeStatusEnum('status')
      .default(ReservationTypeStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('reservation_types_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'reservation_types'),
    nameIndex: index('reservation_types_name_index').on(t.name),
    parentIdIndex: index('reservation_types_parent_id_index').on(t.parentId),
    categoryIndex: index('reservation_types_category_index').on(t.category),
    slugIndex: index('reservation_types_slug_index').on(t.slug),
    imageIndex: index('reservation_types_image_index').on(t.image),
    ogImageIndex: index('reservation_types_og_image_index').on(t.ogImage),
    positionIndex: index('reservation_types_position_index').on(t.position),

    // Optimized composite indexes for position-based sorting performance
    businessPositionIndex: index('reservation_types_business_position_index')
      .on(t.businessId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentPositionIndex: index(
      'reservation_types_business_parent_position_index',
    )
      .on(t.businessId, t.parentId, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'reservation_types_business_status_position_index',
    )
      .on(t.businessId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessParentStatusPositionIndex: index(
      'reservation_types_business_parent_status_position_index',
    )
      .on(t.businessId, t.parentId, t.status, t.position, t.id)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('reservation_types_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('reservation_types_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessShortCodeIndex: index('reservation_types_business_short_code_index')
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
    businessCategoryIndex: index('reservation_types_business_category_index')
      .on(t.businessId, t.category)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('reservation_types_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('reservation_types_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessShortCode: uniqueIndex(
      'reservation_types_business_short_code_unique',
    )
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Junction table for reservation type locations (many-to-many relationship)
export const reservationTypeLocations = pgTable(
  'reservation_type_locations',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    reservationTypeId: uuid('reservation_type_id')
      .notNull()
      .references(() => reservationTypes.id, { onDelete: 'cascade' }),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    // Standard audit fields (using reusable fields)

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
  },
  (t) => ({
    reservationTypeIdIndex: index(
      'reservation_type_locations_reservation_type_id_index',
    ).on(t.reservationTypeId),
    locationIdIndex: index('reservation_type_locations_location_id_index').on(
      t.locationId,
    ),
    uniqueReservationTypeLocation: uniqueIndex(
      'reservation_type_locations_unique',
    ).on(t.reservationTypeId, t.locationId),
  }),
);

// Relations
export const reservationTypesRelations = relations(
  reservationTypes,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [reservationTypes.businessId],
      references: [business.id],
    }),
    // Parent/child relations
    parent: one(reservationTypes, {
      fields: [reservationTypes.parentId],
      references: [reservationTypes.id],
    }),
    children: many(reservationTypes),
    // Media relations
    imageFile: one(media, {
      fields: [reservationTypes.image],
      references: [media.id],
    }),
    ogImageFile: one(media, {
      fields: [reservationTypes.ogImage],
      references: [media.id],
    }),
    // Junction table relation
    reservationTypeLocations: many(reservationTypeLocations),
    // Audit relations
    creator: one(users, {
      fields: [reservationTypes.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [reservationTypes.updatedBy],
      references: [users.id],
    }),
  }),
);

export const reservationTypeLocationsRelations = relations(
  reservationTypeLocations,
  ({ one }) => ({
    // Business relation
    business: one(business, {
      fields: [reservationTypeLocations.businessId],
      references: [business.id],
    }),
    // Reservation type relation
    reservationType: one(reservationTypes, {
      fields: [reservationTypeLocations.reservationTypeId],
      references: [reservationTypes.id],
    }),
    // Location relation
    location: one(locations, {
      fields: [reservationTypeLocations.locationId],
      references: [locations.id],
    }),
    // Audit relations
    creator: one(users, {
      fields: [reservationTypeLocations.createdBy],
      references: [users.id],
    }),
    updater: one(users, {
      fields: [reservationTypeLocations.updatedBy],
      references: [users.id],
    }),
  }),
);
