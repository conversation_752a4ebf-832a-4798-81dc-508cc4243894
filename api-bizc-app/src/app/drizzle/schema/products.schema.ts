import {
  index,
  pgTable,
  text,
  timestamp,
  uuid,
  pgEnum,
  uniqueIndex,
  boolean,
  integer,
  decimal,
  date,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { users } from './users.schema';
import { business } from './business.schema';
import { media } from './media.schema';
import { locations } from './locations.schema';
import { categories } from './categories.schema';
import { accounts } from './accounts.schema';
import { taxes } from './taxes.schema';
import { variationValueTemplates } from './variation-value-templates.schema';
import { variationTemplates } from './variation-templates.schema';
import { brands } from './brands.schema';
import { warrantyTemplates } from './warranties.schema';
import { suppliers } from './suppliers.schema';
import { units } from './units.schema';
import { customerGroups } from './customer-groups.schema';
import {
  locationAllocationFields,
  createBaseEntityBusinessIndexes,
  taxTypeEnum,
} from './common-fields.schema';
import {
  ProductStatus,
  ProductType,
  TaxType,
  TrackingType,
  SerialNumberStatus,
  StandardUnitOfMeasure,
  ExpiryPeriodType,
  PriceType,
} from '../../shared/types';

export const productStatusEnum = pgEnum('product_status', [
  ProductStatus.ACTIVE,
  ProductStatus.INACTIVE,
  ProductStatus.DRAFT,
  ProductStatus.OUT_OF_STOCK,
]);

export const productTypeEnum = pgEnum('product_type', [
  ProductType.SINGLE,
  ProductType.COMBO,
  ProductType.INGREDIENT,
  ProductType.MENU_ITEM,
  ProductType.RAW_MATERIAL,
  ProductType.WORK_IN_PROGRESS,
  ProductType.FINISHED_GOOD,
]);

// Tax Type Enum imported from common-fields.schema.ts

export const trackingTypeEnum = pgEnum('tracking_type', [
  TrackingType.NONE,
  TrackingType.SERIAL,
  TrackingType.IMEI,
  TrackingType.BATCH,
]);

export const serialNumberStatusEnum = pgEnum('serial_number_status', [
  SerialNumberStatus.AVAILABLE,
  SerialNumberStatus.SOLD,
  SerialNumberStatus.RESERVED,
  SerialNumberStatus.DAMAGED,
  SerialNumberStatus.RETURNED,
  SerialNumberStatus.WARRANTY,
]);

export const standardUnitOfMeasureEnum = pgEnum('standard_unit_of_measure', [
  StandardUnitOfMeasure.PIECES,
  StandardUnitOfMeasure.UNITS,
  StandardUnitOfMeasure.EACH,
  StandardUnitOfMeasure.DOZEN,
  StandardUnitOfMeasure.PAIR,
  StandardUnitOfMeasure.GRAMS,
  StandardUnitOfMeasure.KILOGRAMS,
  StandardUnitOfMeasure.POUNDS,
  StandardUnitOfMeasure.OUNCES,
  StandardUnitOfMeasure.TONS,
  StandardUnitOfMeasure.MILLILITERS,
  StandardUnitOfMeasure.LITERS,
  StandardUnitOfMeasure.GALLONS,
  StandardUnitOfMeasure.FLUID_OUNCES,
  StandardUnitOfMeasure.CUPS,
  StandardUnitOfMeasure.PINTS,
  StandardUnitOfMeasure.QUARTS,
  StandardUnitOfMeasure.MILLIMETERS,
  StandardUnitOfMeasure.CENTIMETERS,
  StandardUnitOfMeasure.METERS,
  StandardUnitOfMeasure.INCHES,
  StandardUnitOfMeasure.FEET,
  StandardUnitOfMeasure.YARDS,
  StandardUnitOfMeasure.SQUARE_METERS,
  StandardUnitOfMeasure.SQUARE_FEET,
  StandardUnitOfMeasure.SQUARE_INCHES,
  StandardUnitOfMeasure.HOURS,
  StandardUnitOfMeasure.DAYS,
  StandardUnitOfMeasure.WEEKS,
  StandardUnitOfMeasure.MONTHS,
]);

export const expiryPeriodTypeEnum = pgEnum('expiry_period_type', [
  ExpiryPeriodType.DAYS,
  ExpiryPeriodType.MONTHS,
]);

export const priceTypeEnum = pgEnum('price_type', [
  PriceType.FIXED,
  PriceType.PERCENT_DISCOUNT,
  PriceType.AMOUNT_DISCOUNT,
]);

export const products = pgTable(
  'products',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    shortCode: text('short_code'),
    sku: text('sku'),
    barcode: text('barcode'),
    description: text('description'),
    shortDescription: text('short_description'),
    slug: text('slug'),
    categoryId: uuid('category_id').references(() => categories.id),
    subCategoryId: uuid('sub_category_id').references(() => categories.id),
    brandId: uuid('brand_id').references(() => brands.id),
    warrantyId: uuid('warranty_id').references(() => warrantyTemplates.id),
    preferredSupplierId: uuid('preferred_supplier_id').references(
      () => suppliers.id,
    ),

    // Product type and characteristics
    productType: productTypeEnum('product_type')
      .default(ProductType.SINGLE)
      .notNull(),
    isSellable: boolean('is_sellable').default(true).notNull(),
    isIngredient: boolean('is_ingredient').default(false).notNull(),
    isManufacturable: boolean('is_manufacturable').default(false).notNull(),
    isPurchasable: boolean('is_purchasable').default(true).notNull(),

    // Pricing
    basePrice: decimal('base_price', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    standardCost: decimal('standard_cost', { precision: 12, scale: 2 }).default(
      '0.00',
    ),
    lastPurchasePrice: decimal('last_purchase_price', {
      precision: 12,
      scale: 2,
    }).default('0.00'),
    allowCustomerGroupPricing: boolean('allow_customer_group_pricing')
      .default(false)
      .notNull(),

    // Tax configuration
    taxType: taxTypeEnum('tax_type').default(TaxType.INCLUSIVE).notNull(),
    defaultTaxRateId: uuid('default_tax_rate_id').references(() => taxes.id),

    // Account references
    incomeAccountId: uuid('income_account_id').references(() => accounts.id),
    expenseAccountId: uuid('expense_account_id').references(() => accounts.id),
    inventoryAccountId: uuid('inventory_account_id').references(
      () => accounts.id,
    ),

    // Inventory management
    manageInventory: boolean('manage_inventory').default(true).notNull(),
    trackingType: trackingTypeEnum('tracking_type')
      .default(TrackingType.NONE)
      .notNull(),
    trackSerialNumbers: boolean('track_serial_numbers')
      .default(false)
      .notNull(),
    standardUnitOfMeasure: standardUnitOfMeasureEnum(
      'standard_unit_of_measure',
    ).default(StandardUnitOfMeasure.PIECES),
    customUnitId: uuid('custom_unit_id').references(() => units.id),
    leadTimeDays: decimal('lead_time_days', { precision: 5, scale: 2 }).default(
      '0.00',
    ),
    minStockLevel: decimal('min_stock_level', {
      precision: 10,
      scale: 3,
    }).default('0.000'),
    maxStockLevel: decimal('max_stock_level', {
      precision: 10,
      scale: 3,
    }).default('0.000'),
    reorderPoint: decimal('reorder_point', { precision: 10, scale: 3 }).default(
      '0.000',
    ),

    // Expiry information
    expiryPeriod: integer('expiry_period'),
    expiryPeriodType: expiryPeriodTypeEnum('expiry_period_type'),

    // Physical properties
    weight: decimal('weight', { precision: 8, scale: 3 }).default('0.000'),

    // Online presence
    availableOnline: boolean('available_online').default(false).notNull(),
    featured: boolean('featured').default(false).notNull(),

    // Position management - category-specific positioning
    categoryPosition: integer('category_position').default(0).notNull(),
    subCategoryPosition: integer('sub_category_position').default(0).notNull(),
    globalPosition: integer('global_position').default(0).notNull(),

    // Location allocation (using reusable fields)
    ...locationAllocationFields,

    // SEO fields
    seoTitle: text('seo_title'),
    seoDescription: text('seo_description'),
    seoKeywords: text('seo_keywords').array(),
    ogImage: uuid('og_image').references(() => media.id),

    // Additional fields
    tags: text('tags').array(),
    model: text('model'),

    status: productStatusEnum('status').default(ProductStatus.ACTIVE).notNull(),
  },
  (t) => ({
    idIndex: index('products_id_index').on(t.id),
    businessIdIndex: index('products_business_id_index').on(t.businessId),
    nameIndex: index('products_name_index').on(t.name),
    skuIndex: index('products_sku_index').on(t.sku),
    barcodeIndex: index('products_barcode_index').on(t.barcode),
    categoryIdIndex: index('products_category_id_index').on(t.categoryId),
    subCategoryIdIndex: index('products_sub_category_id_index').on(
      t.subCategoryId,
    ),
    brandIdIndex: index('products_brand_id_index').on(t.brandId),
    warrantyIdIndex: index('products_warranty_id_index').on(t.warrantyId),
    preferredSupplierIdIndex: index('products_preferred_supplier_id_index').on(
      t.preferredSupplierId,
    ),
    customUnitIdIndex: index('products_custom_unit_id_index').on(
      t.customUnitId,
    ),
    slugIndex: index('products_slug_index').on(t.slug),
    ogImageIndex: index('products_og_image_index').on(t.ogImage),
    categoryPositionIndex: index('products_category_position_index').on(
      t.categoryPosition,
    ),
    subCategoryPositionIndex: index('products_sub_category_position_index').on(
      t.subCategoryPosition,
    ),
    globalPositionIndex: index('products_global_position_index').on(
      t.globalPosition,
    ),

    // Optimized composite indexes for performance
    businessGlobalPositionIndex: index(
      'products_business_global_position_index',
    )
      .on(t.businessId, t.globalPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessCategoryPositionIndex: index(
      'products_business_category_position_index',
    )
      .on(t.businessId, t.categoryId, t.categoryPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessSubCategoryPositionIndex: index(
      'products_business_sub_category_position_index',
    )
      .on(t.businessId, t.subCategoryId, t.subCategoryPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessStatusPositionIndex: index(
      'products_business_status_position_index',
    )
      .on(t.businessId, t.status, t.globalPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessFeaturedPositionIndex: index(
      'products_business_featured_position_index',
    )
      .on(t.businessId, t.featured, t.globalPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessOnlinePositionIndex: index(
      'products_business_online_position_index',
    )
      .on(t.businessId, t.availableOnline, t.globalPosition, t.id)
      .where(sql`${t.isDeleted} = false`),
    businessTypeIndex: index('products_business_type_index')
      .on(t.businessId, t.productType)
      .where(sql`${t.isDeleted} = false`),

    // Optimized indexes for filtering and searching
    businessNameIndex: index('products_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
    businessSlugIndex: index('products_business_slug_index')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    businessSkuIndex: index('products_business_sku_index')
      .on(t.businessId, t.sku)
      .where(sql`${t.isDeleted} = false`),
    businessBarcodeIndex: index('products_business_barcode_index')
      .on(t.businessId, t.barcode)
      .where(sql`${t.isDeleted} = false`),
    businessShortCodeIndex: index('products_business_short_code_index')
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessSku: uniqueIndex('products_business_sku_unique')
      .on(t.businessId, t.sku)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessBarcode: uniqueIndex('products_business_barcode_unique')
      .on(t.businessId, t.barcode)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessSlug: uniqueIndex('products_business_slug_unique')
      .on(t.businessId, t.slug)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessShortCode: uniqueIndex('products_business_short_code_unique')
      .on(t.businessId, t.shortCode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Product variants table
export const productVariants = pgTable(
  'product_variants',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    name: text('name').notNull(),
    sku: text('sku').notNull(),
    barcode: text('barcode'),
    price: decimal('price', { precision: 12, scale: 2 }).default('0.00'),
    weight: decimal('weight', { precision: 8, scale: 3 }).default('0.000'),
    variationTemplateId: uuid('variation_template_id').references(
      () => variationTemplates.id,
      { onDelete: 'set null' },
    ),
    variationValueTemplateId: uuid('variation_value_template_id').references(
      () => variationValueTemplates.id,
      { onDelete: 'set null' },
    ),

    // Media
    image: uuid('primary_image').references(() => media.id),

    status: productStatusEnum('status').default(ProductStatus.ACTIVE).notNull(),
  },
  (t) => ({
    productIdIndex: index('product_variants_product_id_index').on(t.productId),
    businessIdIndex: index('product_variants_business_id_index').on(
      t.businessId,
    ),
    skuIndex: index('product_variants_sku_index').on(t.sku),
    barcodeIndex: index('product_variants_barcode_index').on(t.barcode),
    variationTemplateIdIndex: index(
      'product_variants_variation_template_id_index',
    ).on(t.variationTemplateId),
    variationValueTemplateIdIndex: index(
      'product_variants_variation_value_template_id_index',
    ).on(t.variationValueTemplateId),
    imageIndex: index('product_variants_image_index').on(t.image),

    // Unique constraints with soft deletion support
    uniqueBusinessSku: uniqueIndex('product_variants_business_sku_unique')
      .on(t.businessId, t.sku)
      .where(sql`${t.isDeleted} = false`),
    uniqueBusinessBarcode: uniqueIndex(
      'product_variants_business_barcode_unique',
    )
      .on(t.businessId, t.barcode)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Product locations table moved to inventory.schema.ts

// Combo products table (product bundles)
export const comboProducts = pgTable(
  'combo_products',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    parentProductId: uuid('parent_product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    childProductId: uuid('child_product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    childVariantId: uuid('child_variant_id').references(
      () => productVariants.id,
      { onDelete: 'cascade' },
    ),
    quantity: decimal('quantity', { precision: 10, scale: 3 })
      .notNull()
      .default('1.000'),
    isMandatory: boolean('is_mandatory').default(true).notNull(),

    // Standard audit fields (using reusable fields)
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'combo_products'),
    parentProductIdIndex: index('combo_products_parent_product_id_index').on(
      t.parentProductId,
    ),
    childProductIdIndex: index('combo_products_child_product_id_index').on(
      t.childProductId,
    ),
    uniqueCombo: uniqueIndex('combo_products_unique').on(
      t.parentProductId,
      t.childProductId,
      t.childVariantId,
    ),
  }),
);

// Inventory table is defined in inventory.schema.ts

// Serial Numbers table moved to inventory.schema.ts

// Batch Numbers table
export const batchNumbers = pgTable(
  'batch_numbers',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'cascade',
    }),
    batchNumber: text('batch_number').notNull(),
    manufactureDate: timestamp('manufacture_date'),
    expiryDate: timestamp('expiry_date'),
    quantity: decimal('quantity', { precision: 10, scale: 3 })
      .notNull()
      .default('0.000'),
    availableQuantity: decimal('available_quantity', {
      precision: 10,
      scale: 3,
    })
      .notNull()
      .default('0.000'),
    locationId: uuid('location_id')
      .notNull()
      .references(() => locations.id, { onDelete: 'cascade' }),
    notes: text('notes'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    productIdIndex: index('products_batch_numbers_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('products_batch_numbers_variant_id_index').on(
      t.variantId,
    ),
    batchNumberIndex: index('products_batch_numbers_batch_number_index').on(
      t.batchNumber,
    ),
    locationIdIndex: index('products_batch_numbers_location_id_index').on(
      t.locationId,
    ),
    expiryDateIndex: index('products_batch_numbers_expiry_date_index').on(
      t.expiryDate,
    ),
    uniqueBatchNumber: uniqueIndex(
      'products_batch_numbers_batch_number_unique',
    ).on(t.batchNumber),
  }),
);

// Product Group Prices table
export const productGroupPrices = pgTable(
  'product_group_prices',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'cascade',
    }),
    groupId: uuid('group_id')
      .notNull()
      .references(() => customerGroups.id, { onDelete: 'cascade' }),
    price: decimal('price', { precision: 12, scale: 2 }),
    minQuantity: decimal('min_quantity', { precision: 10, scale: 3 })
      .notNull()
      .default('1.000'),
    priceType: priceTypeEnum('price_type').default(PriceType.FIXED).notNull(),
    discountPercent: decimal('discount_percent', { precision: 5, scale: 2 }),
    discountAmount: decimal('discount_amount', { precision: 12, scale: 2 }),
    includeTax: boolean('include_tax').default(true).notNull(),
    validFrom: date('valid_from'),
    validTo: date('valid_to'),
    isActive: boolean('is_active').default(true).notNull(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    productIdIndex: index('product_group_prices_product_id_index').on(
      t.productId,
    ),
    variantIdIndex: index('product_group_prices_variant_id_index').on(
      t.variantId,
    ),
    groupIdIndex: index('product_group_prices_group_id_index').on(t.groupId),
    priceTypeIndex: index('product_group_prices_price_type_index').on(
      t.priceType,
    ),
    isActiveIndex: index('product_group_prices_is_active_index').on(t.isActive),
    validFromIndex: index('product_group_prices_valid_from_index').on(
      t.validFrom,
    ),
    validToIndex: index('product_group_prices_valid_to_index').on(t.validTo),
    uniqueProductGroupPrice: uniqueIndex(
      'product_group_prices_unique_product_group',
    ).on(t.productId, t.variantId, t.groupId, t.minQuantity),
  }),
);

// Product Images table (many-to-many with sort order)
export const productImages = pgTable(
  'product_images',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    productId: uuid('product_id')
      .notNull()
      .references(() => products.id, { onDelete: 'cascade' }),
    variantId: uuid('variant_id').references(() => productVariants.id, {
      onDelete: 'cascade',
    }),
    imageId: uuid('image_id')
      .notNull()
      .references(() => media.id, { onDelete: 'cascade' }),
    sortOrder: integer('sort_order').default(0).notNull(),
    isPrimary: boolean('is_primary').default(false).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
  },
  (t) => ({
    productIdIndex: index('product_images_product_id_index').on(t.productId),
    variantIdIndex: index('product_images_variant_id_index').on(t.variantId),
    imageIdIndex: index('product_images_image_id_index').on(t.imageId),
    sortOrderIndex: index('product_images_sort_order_index').on(t.sortOrder),
    isPrimaryIndex: index('product_images_is_primary_index').on(t.isPrimary),
    isActiveIndex: index('product_images_is_active_index').on(t.isActive),

    // Composite indexes for performance
    productSortIndex: index('product_images_product_sort_index').on(
      t.productId,
      t.sortOrder,
      t.id,
    ),
    variantSortIndex: index('product_images_variant_sort_index').on(
      t.variantId,
      t.sortOrder,
      t.id,
    ),

    // Unique constraints
    uniqueProductImage: uniqueIndex('product_images_product_image_unique').on(
      t.productId,
      t.variantId,
      t.imageId,
    ),
  }),
);

// Relations
export const productsRelations = relations(products, ({ one, many }) => ({
  // Business relation
  business: one(business, {
    fields: [products.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [products.createdBy],
    references: [users.id],
    relationName: 'createdProducts',
  }),
  updater: one(users, {
    fields: [products.updatedBy],
    references: [users.id],
    relationName: 'updatedProducts',
  }),

  // Category relations
  category: one(categories, {
    fields: [products.categoryId],
    references: [categories.id],
    relationName: 'categoryProducts',
  }),
  subCategory: one(categories, {
    fields: [products.subCategoryId],
    references: [categories.id],
    relationName: 'subCategoryProducts',
  }),

  // Brand relation
  brand: one(brands, {
    fields: [products.brandId],
    references: [brands.id],
  }),

  // Warranty relation
  warranty: one(warrantyTemplates, {
    fields: [products.warrantyId],
    references: [warrantyTemplates.id],
  }),

  // Supplier relation
  preferredSupplier: one(suppliers, {
    fields: [products.preferredSupplierId],
    references: [suppliers.id],
  }),

  // Tax relation
  defaultTaxRate: one(taxes, {
    fields: [products.defaultTaxRateId],
    references: [taxes.id],
  }),

  // Account relations
  incomeAccount: one(accounts, {
    fields: [products.incomeAccountId],
    references: [accounts.id],
    relationName: 'incomeProducts',
  }),
  expenseAccount: one(accounts, {
    fields: [products.expenseAccountId],
    references: [accounts.id],
    relationName: 'expenseProducts',
  }),
  inventoryAccount: one(accounts, {
    fields: [products.inventoryAccountId],
    references: [accounts.id],
    relationName: 'inventoryProducts',
  }),

  // Unit relation
  customUnit: one(units, {
    fields: [products.customUnitId],
    references: [units.id],
  }),

  // Media relation
  ogImageMedia: one(media, {
    fields: [products.ogImage],
    references: [media.id],
    relationName: 'productOgImage',
  }),

  // One-to-many relations
  variants: many(productVariants),
  // locations: many(productLocations), // productLocations moved to inventory.schema.ts
  comboChildren: many(comboProducts, {
    relationName: 'parentProduct',
  }),
  comboParents: many(comboProducts, {
    relationName: 'childProduct',
  }),
  // inventoryRecords: many(inventory), // inventory table moved to inventory.schema.ts
  // serialNumbers: many(serialNumbers), // serialNumbers moved to inventory.schema.ts
  batchNumbers: many(batchNumbers),
  groupPrices: many(productGroupPrices),
  images: many(productImages),
}));

export const productVariantsRelations = relations(
  productVariants,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [productVariants.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [productVariants.createdBy],
      references: [users.id],
      relationName: 'createdProductVariants',
    }),
    updater: one(users, {
      fields: [productVariants.updatedBy],
      references: [users.id],
      relationName: 'updatedProductVariants',
    }),

    // Product relation
    product: one(products, {
      fields: [productVariants.productId],
      references: [products.id],
    }),

    // Variation template relations
    variationTemplate: one(variationTemplates, {
      fields: [productVariants.variationTemplateId],
      references: [variationTemplates.id],
    }),
    variationValueTemplate: one(variationValueTemplates, {
      fields: [productVariants.variationValueTemplateId],
      references: [variationValueTemplates.id],
    }),

    // Media relation
    imageMedia: one(media, {
      fields: [productVariants.image],
      references: [media.id],
      relationName: 'productVariantImage',
    }),

    // One-to-many relations
    // locations: many(productLocations), // productLocations moved to inventory.schema.ts
    comboChildren: many(comboProducts, {
      relationName: 'childVariant',
    }),
    // inventoryRecords: many(inventory), // inventory table moved to inventory.schema.ts
    // serialNumbers: many(serialNumbers), // serialNumbers moved to inventory.schema.ts
    batchNumbers: many(batchNumbers),
    groupPrices: many(productGroupPrices),
    images: many(productImages),
  }),
);

// productLocationsRelations moved to inventory.schema.ts

export const comboProductsRelations = relations(comboProducts, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [comboProducts.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [comboProducts.createdBy],
    references: [users.id],
    relationName: 'createdComboProducts',
  }),
  updater: one(users, {
    fields: [comboProducts.updatedBy],
    references: [users.id],
    relationName: 'updatedComboProducts',
  }),

  // Product relations
  parentProduct: one(products, {
    fields: [comboProducts.parentProductId],
    references: [products.id],
    relationName: 'parentProduct',
  }),
  childProduct: one(products, {
    fields: [comboProducts.childProductId],
    references: [products.id],
    relationName: 'childProduct',
  }),

  // Variant relation
  childVariant: one(productVariants, {
    fields: [comboProducts.childVariantId],
    references: [productVariants.id],
    relationName: 'childVariant',
  }),
}));

// inventoryRelations moved to inventory.schema.ts

// serialNumbersRelations moved to inventory.schema.ts

export const batchNumbersRelations = relations(batchNumbers, ({ one }) => ({
  // Product and variant relations
  product: one(products, {
    fields: [batchNumbers.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [batchNumbers.variantId],
    references: [productVariants.id],
  }),

  // Location relation
  location: one(locations, {
    fields: [batchNumbers.locationId],
    references: [locations.id],
  }),
}));

export const productGroupPricesRelations = relations(
  productGroupPrices,
  ({ one }) => ({
    // Audit relation
    creator: one(users, {
      fields: [productGroupPrices.createdBy],
      references: [users.id],
      relationName: 'createdProductGroupPrices',
    }),

    // Product and variant relations
    product: one(products, {
      fields: [productGroupPrices.productId],
      references: [products.id],
    }),
    variant: one(productVariants, {
      fields: [productGroupPrices.variantId],
      references: [productVariants.id],
    }),

    // Customer group relation
    group: one(customerGroups, {
      fields: [productGroupPrices.groupId],
      references: [customerGroups.id],
    }),
  }),
);

export const productImagesRelations = relations(productImages, ({ one }) => ({
  // Business relation
  business: one(business, {
    fields: [productImages.businessId],
    references: [business.id],
  }),

  // Audit relations
  creator: one(users, {
    fields: [productImages.createdBy],
    references: [users.id],
    relationName: 'createdProductImages',
  }),
  updater: one(users, {
    fields: [productImages.updatedBy],
    references: [users.id],
    relationName: 'updatedProductImages',
  }),

  // Product and variant relations
  product: one(products, {
    fields: [productImages.productId],
    references: [products.id],
  }),
  variant: one(productVariants, {
    fields: [productImages.variantId],
    references: [productVariants.id],
  }),

  // Media relation
  image: one(media, {
    fields: [productImages.imageId],
    references: [media.id],
  }),
}));
