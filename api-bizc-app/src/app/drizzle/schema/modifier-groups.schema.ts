import {
  index,
  pgTable,
  text,
  pgEnum,
  uniqueIndex,
  boolean,
  uuid,
  timestamp,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { SelectionType, ModifierStatus } from '../../shared/types';

export const selectionTypeEnum = pgEnum('selection_type', [
  SelectionType.SINGLE,
  SelectionType.MULTIPLE,
]);

export const modifierGroupStatusEnum = pgEnum('modifier_group_status', [
  ModifierStatus.ACTIVE,
  ModifierStatus.INACTIVE,
]);

export const modifierGroups = pgTable(
  'modifier_groups',
  {
    id: uuid('id').defaultRandom().primary<PERSON>ey(),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    groupName: text('group_name').notNull(),
    description: text('description'),
    selectionType: selectionTypeEnum('selection_type')
      .default(SelectionType.SINGLE)
      .notNull(),
    isRequired: boolean('is_required').default(false).notNull(),
    status: modifierGroupStatusEnum('status')
      .default(ModifierStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    idIndex: index('modifier_groups_id_index').on(t.id),
    ...createBaseEntityBusinessIndexes(t, 'modifier_groups'),
    groupNameIndex: index('modifier_groups_group_name_index').on(t.groupName),

    // Optimized indexes for filtering and searching
    businessGroupNameIndex: index('modifier_groups_business_group_name_index')
      .on(t.businessId, t.groupName)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessGroupName: uniqueIndex(
      'modifier_groups_business_group_name_unique',
    )
      .on(t.businessId, t.groupName)
      .where(sql`${t.isDeleted} = false`),
  }),
);

export type ModifierGroup = typeof modifierGroups.$inferSelect;
export type NewModifierGroup = typeof modifierGroups.$inferInsert;

// Relations definitions

// Modifier Groups Relations
export const modifierGroupsRelations = relations(modifierGroups, ({ one }) => ({
  business: one(business, {
    fields: [modifierGroups.businessId],
    references: [business.id],
  }),
  createdBy: one(users, {
    fields: [modifierGroups.createdBy],
    references: [users.id],
  }),
  updatedBy: one(users, {
    fields: [modifierGroups.updatedBy],
    references: [users.id],
  }),
}));
