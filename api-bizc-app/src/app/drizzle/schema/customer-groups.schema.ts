import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { business } from './business.schema';
import {
  createBaseEntityFields,
  createBaseEntityBusinessIndexes,
} from './common-fields.schema';
import { users } from './users.schema';
import { customers } from './customers.schema';
import { CustomerGroupStatus } from '../../shared/types';

export const customerGroupStatusEnum = pgEnum('customer_group_status', [
  CustomerGroupStatus.ACTIVE,
  CustomerGroupStatus.INACTIVE,
]);

export const customerGroups = pgTable(
  'customer_groups',
  {
    // Base entity fields

    id: uuid('id').defaultRandom().primaryKey(),

    // Audit fields

    createdBy: uuid('created_by')
      .notNull()

      .references(() => users.id),

    updatedBy: uuid('updated_by').references(() => users.id),

    createdAt: timestamp('created_at').defaultNow().notNull(),

    updatedAt: timestamp('updated_at').defaultNow().notNull(),

    isDeleted: boolean('is_deleted').default(false).notNull(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    name: text('name').notNull(),
    description: text('description'),
    status: customerGroupStatusEnum('status')
      .default(CustomerGroupStatus.ACTIVE)
      .notNull(),
  },
  (t) => ({
    ...createBaseEntityBusinessIndexes(t, 'customer_groups'),
    // Entity-specific indexes
    nameIndex: index('customer_groups_name_index').on(t.name),
    statusIndex: index('customer_groups_status_index').on(t.status),

    // Composite indexes for performance
    businessStatusIndex: index('customer_groups_business_status_index')
      .on(t.businessId, t.status)
      .where(sql`${t.isDeleted} = false`),
    businessNameIndex: index('customer_groups_business_name_index')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),

    // Unique constraints with soft deletion support
    uniqueBusinessName: uniqueIndex('customer_groups_business_name_unique')
      .on(t.businessId, t.name)
      .where(sql`${t.isDeleted} = false`),
  }),
);

// Relations
export const customerGroupsRelations = relations(
  customerGroups,
  ({ one, many }) => ({
    // Business relation
    business: one(business, {
      fields: [customerGroups.businessId],
      references: [business.id],
    }),

    // Audit relations
    creator: one(users, {
      fields: [customerGroups.createdBy],
      references: [users.id],
      relationName: 'createdCustomerGroups',
    }),
    updater: one(users, {
      fields: [customerGroups.updatedBy],
      references: [users.id],
      relationName: 'updatedCustomerGroups',
    }),

    // One-to-many relations
    customers: many(customers),
  }),
);
