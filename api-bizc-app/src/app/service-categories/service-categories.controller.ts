import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { ServiceCategoriesService } from './service-categories.service';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { ServiceCategoryDto } from './dto/service-category.dto';
import { ServiceCategorySlimDto } from './dto/service-category-slim.dto';
import { ServiceCategoryIdResponseDto } from './dto/service-category-id-response.dto';
import { BulkServiceCategoryIdsResponseDto } from './dto/bulk-service-category-ids-response.dto';
import { BulkCreateServiceCategoryDto } from './dto/bulk-create-service-category.dto';
import { DeleteServiceCategoryResponseDto } from './dto/delete-service-category-response.dto';
import { BulkDeleteServiceCategoryDto } from './dto/bulk-delete-service-category.dto';
import { BulkDeleteServiceCategoryResponseDto } from './dto/bulk-delete-service-category-response.dto';
import { PaginatedServiceCategoriesResponseDto } from './dto/paginated-service-categories-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { ServiceCategoryNameAvailabilityResponseDto } from './dto/check-service-category-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('service-categories')
@Controller('service-categories')
@UseGuards(PermissionsGuard)
export class ServiceCategoriesController {
  constructor(
    private readonly serviceCategoriesService: ServiceCategoriesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiOperation({
    summary: 'Create a new service category',
    description:
      'Creates a new service category with optional image and OG image uploads',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Service category data with optional image files',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: 'Service category name' },
        shortCode: {
          type: 'string',
          description: 'Short code for the service category',
        },
        parentId: { type: 'string', description: 'Parent service category ID' },
        description: {
          type: 'string',
          description: 'Service category description',
        },
        slug: { type: 'string', description: 'URL-friendly slug' },
        availableOnline: {
          type: 'boolean',
          description: 'Whether available online',
        },
        color: { type: 'string', description: 'Color in hex format' },
        status: { type: 'string', enum: ['active', 'inactive'] },
        position: { type: 'number', description: 'Display order position' },
        seoTitle: { type: 'string', description: 'SEO title' },
        seoDescription: { type: 'string', description: 'SEO description' },
        seoKeywords: { type: 'array', items: { type: 'string' } },
        isAllocatedToAllLocations: {
          type: 'boolean',
          description: 'Allocated to all locations',
        },
        locationIds: { type: 'array', items: { type: 'string' } },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Service category image',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image',
        },
      },
      required: ['name'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Service category created successfully',
    type: ServiceCategoryIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Service category name already exists',
  })
  create(
    @Request() req: any,
    @Body() createServiceCategoryDto: CreateServiceCategoryDto,
    @UploadedFiles()
    files?: { image?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
  ): Promise<ServiceCategoryIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.serviceCategoriesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createServiceCategoryDto,
      image,
      ogImage,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiOperation({
    summary: 'Bulk create service categories',
    description:
      'Creates multiple service categories at once with optional images',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Bulk service category creation data',
    schema: {
      type: 'object',
      properties: {
        serviceCategories: {
          type: 'string',
          description:
            'JSON string containing array of service category objects',
        },
        images: {
          type: 'array',
          items: { type: 'string', format: 'binary' },
          description: 'Array of images for service categories',
        },
      },
      required: ['serviceCategories'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Service categories created successfully',
    type: BulkServiceCategoryIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Service category names already exist',
  })
  bulkCreate(
    @Request() req: any,
    @Body() bulkCreateServiceCategoryDto: BulkCreateServiceCategoryDto,
    @UploadedFiles() images?: Express.Multer.File[],
  ): Promise<BulkServiceCategoryIdsResponseDto> {
    return this.serviceCategoriesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateServiceCategoryDto.serviceCategories,
      images,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get all service categories for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by service category name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'slug',
    description: 'Filter by service category slug',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'shortCode',
    description: 'Filter by service category short code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by service category status',
    required: false,
    enum: ['active', 'inactive'],
  })
  @ApiQuery({
    name: 'availableOnline',
    description: 'Filter by online availability',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for multiple filters',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort field and direction (e.g., "name:asc", "createdAt:desc")',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service categories retrieved successfully',
    type: PaginatedServiceCategoriesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('slug') slug?: string,
    @Query('shortCode') shortCode?: string,
    @Query('status') status?: string,
    @Query('availableOnline') availableOnline?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedServiceCategoriesResponseDto> {
    return this.serviceCategoriesService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      slug,
      shortCode,
      status,
      availableOnline,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name/:name')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Check if service category name is available',
    description: 'Checks if a service category name is available for use',
  })
  @ApiParam({
    name: 'name',
    description: 'Service category name to check',
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Service category ID to exclude from the check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: ServiceCategoryNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkNameAvailability(
    @Request() req: any,
    @Param('name') name: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<ServiceCategoryNameAvailabilityResponseDto> {
    const result = await this.serviceCategoriesService.checkNameAvailability(
      name,
      req.user.activeBusinessId,
      excludeId,
    );

    return {
      available: result.available,
      message: result.available
        ? 'Service category name is available'
        : 'Service category name is already taken',
    };
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get a service category by ID',
    description: 'Retrieves a single service category by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Service category ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service category retrieved successfully',
    type: ServiceCategoryDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Service category not found',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<ServiceCategoryDto> {
    return this.serviceCategoriesService.findOne(id, req.user.activeBusinessId);
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get all service categories in slim format',
    description:
      'Retrieves all service categories in a hierarchical slim format for dropdowns and selectors',
  })
  @ApiResponse({
    status: 200,
    description: 'Service categories retrieved successfully',
    type: [ServiceCategorySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: any): Promise<ServiceCategorySlimDto[]> {
    return this.serviceCategoriesService.findAllSlim(req.user.activeBusinessId);
  }

  @Get('hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_READ)
  @ApiOperation({
    summary: 'Get all service categories in hierarchical format',
    description:
      'Retrieves all service categories organized in a hierarchical structure',
  })
  @ApiResponse({
    status: 200,
    description: 'Service categories hierarchy retrieved successfully',
    type: [ServiceCategorySlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllHierarchy(@Request() req: any): Promise<ServiceCategorySlimDto[]> {
    return this.serviceCategoriesService.findAllHierarchy(
      req.user.activeBusinessId,
    );
  }

  @Patch('positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_UPDATE)
  @ApiOperation({
    summary: 'Update service category positions',
    description: 'Updates the display positions of multiple service categories',
  })
  @ApiBody({
    description: 'Array of service category position updates',
    schema: {
      type: 'object',
      properties: {
        updates: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string', description: 'Service category ID' },
              position: { type: 'number', description: 'New position' },
            },
            required: ['id', 'position'],
          },
        },
      },
      required: ['updates'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Positions updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updatePositions(
    @Request() req: any,
    @Body() body: { updates: { id: string; position: number }[] },
  ): Promise<{ message: string }> {
    await this.serviceCategoriesService.updatePositions(
      body.updates,
      req.user.id,
      req.user.activeBusinessId,
    );
    return { message: 'Positions updated successfully' };
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiOperation({
    summary: 'Update a service category',
    description:
      'Updates an existing service category with optional image uploads',
  })
  @ApiConsumes('multipart/form-data')
  @ApiParam({
    name: 'id',
    description: 'Service category ID',
    type: String,
  })
  @ApiBody({
    description: 'Service category update data with optional image files',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: 'Service category name' },
        shortCode: {
          type: 'string',
          description: 'Short code for the service category',
        },
        parentId: { type: 'string', description: 'Parent service category ID' },
        description: {
          type: 'string',
          description: 'Service category description',
        },
        slug: { type: 'string', description: 'URL-friendly slug' },
        availableOnline: {
          type: 'boolean',
          description: 'Whether available online',
        },
        color: { type: 'string', description: 'Color in hex format' },
        status: { type: 'string', enum: ['active', 'inactive'] },
        seoTitle: { type: 'string', description: 'SEO title' },
        seoDescription: { type: 'string', description: 'SEO description' },
        seoKeywords: { type: 'array', items: { type: 'string' } },
        isAllocatedToAllLocations: {
          type: 'boolean',
          description: 'Allocated to all locations',
        },
        locationIds: { type: 'array', items: { type: 'string' } },
        image: {
          type: 'string',
          format: 'binary',
          description: 'Service category image',
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Service category updated successfully',
    type: ServiceCategoryDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Service category not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Service category name already exists',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateServiceCategoryDto: UpdateServiceCategoryDto,
    @UploadedFiles()
    files?: { image?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
  ): Promise<ServiceCategoryDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.serviceCategoriesService.update(
      id,
      req.user.id,
      req.user.activeBusinessId,
      updateServiceCategoryDto,
      image,
      ogImage,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_DELETE)
  @ApiOperation({
    summary: 'Delete a service category',
    description: 'Soft deletes a service category by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Service category ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Service category deleted successfully',
    type: DeleteServiceCategoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request - Service category has associated services or child service categories',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Service category not found',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<DeleteServiceCategoryResponseDto> {
    return this.serviceCategoriesService.remove(
      id,
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SERVICE_DELETE)
  @ApiOperation({
    summary: 'Bulk delete service categories',
    description: 'Soft deletes multiple service categories by their IDs',
  })
  @ApiBody({
    description: 'Array of service category IDs to delete',
    type: BulkDeleteServiceCategoryDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Service categories deleted successfully',
    type: BulkDeleteServiceCategoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad Request - Some service categories have associated services or child service categories',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - One or more service categories not found',
  })
  bulkDelete(
    @Request() req: any,
    @Body() bulkDeleteServiceCategoryDto: BulkDeleteServiceCategoryDto,
  ): Promise<BulkDeleteServiceCategoryResponseDto> {
    return this.serviceCategoriesService.bulkDelete(
      bulkDeleteServiceCategoryDto.ids,
      req.user.id,
      req.user.activeBusinessId,
    );
  }
}
