import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateNewsletterSubscriberDto } from './dto/create-newsletter-subscriber.dto';
import { UpdateNewsletterSubscriberDto } from './dto/update-newsletter-subscriber.dto';
import { NewsletterSubscriberDto } from './dto/newsletter-subscriber.dto';
import { NewsletterSubscriberSlimDto } from './dto/newsletter-subscriber-slim.dto';
import { NewsletterSubscriberListDto } from './dto/newsletter-subscriber-list.dto';
import {
  newsletterSubscribers,
  NewsletterSubscriberStatus,
} from '../drizzle/schema/newsletter-subscribers.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  and,
  eq,
  ilike,
  isNull,
  desc,
  asc,
  or,
  count,
  sql,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class NewsletterSubscribersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createNewsletterSubscriberDto: CreateNewsletterSubscriberDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate that at least email or phone number is provided
      if (
        !createNewsletterSubscriberDto.email &&
        !createNewsletterSubscriberDto.phoneNumber
      ) {
        throw new BadRequestException(
          'Either email or phone number must be provided',
        );
      }

      // Check if a newsletter subscriber with the same email already exists for this business
      if (createNewsletterSubscriberDto.email) {
        const existingEmailSubscriber = await this.db
          .select()
          .from(newsletterSubscribers)
          .where(
            and(
              eq(newsletterSubscribers.businessId, businessId),
              ilike(
                newsletterSubscribers.email,
                createNewsletterSubscriberDto.email,
              ),
              eq(newsletterSubscribers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingEmailSubscriber) {
          throw new ConflictException(
            `Newsletter subscriber with email "${createNewsletterSubscriberDto.email}" already exists`,
          );
        }
      }

      // Check if a newsletter subscriber with the same phone number already exists for this business
      if (createNewsletterSubscriberDto.phoneNumber) {
        const existingPhoneSubscriber = await this.db
          .select()
          .from(newsletterSubscribers)
          .where(
            and(
              eq(newsletterSubscribers.businessId, businessId),
              eq(
                newsletterSubscribers.phoneNumber,
                createNewsletterSubscriberDto.phoneNumber,
              ),
              eq(newsletterSubscribers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingPhoneSubscriber) {
          throw new ConflictException(
            `Newsletter subscriber with phone number "${createNewsletterSubscriberDto.phoneNumber}" already exists`,
          );
        }
      }

      const newNewsletterSubscriber = await this.db.transaction(async (tx) => {
        const newsletterSubscriber = await tx
          .insert(newsletterSubscribers)
          .values({
            businessId,
            email: createNewsletterSubscriberDto.email,
            emailVerified: createNewsletterSubscriberDto.emailVerified ?? false,
            phoneNumber: createNewsletterSubscriberDto.phoneNumber,
            phoneVerified: createNewsletterSubscriberDto.phoneVerified ?? false,
            languagePreference:
              createNewsletterSubscriberDto.languagePreference ?? 'en',
            subscriptionDate: createNewsletterSubscriberDto.subscriptionDate
              ? new Date(createNewsletterSubscriberDto.subscriptionDate)
              : new Date(),
            unsubscriptionDate: createNewsletterSubscriberDto.unsubscriptionDate
              ? new Date(createNewsletterSubscriberDto.unsubscriptionDate)
              : null,
            status:
              createNewsletterSubscriberDto.status ??
              NewsletterSubscriberStatus.PENDING,
            title: createNewsletterSubscriberDto.title,
            firstName: createNewsletterSubscriberDto.firstName,
            middleName: createNewsletterSubscriberDto.middleName,
            lastName: createNewsletterSubscriberDto.lastName,
            suffix: createNewsletterSubscriberDto.suffix,
            source: createNewsletterSubscriberDto.source as any,
            createdBy: userId,
          })
          .returning();

        return newsletterSubscriber;
      });

      // Log the newsletter subscriber creation activity
      const subscriberName =
        this.getFullName(
          createNewsletterSubscriberDto.firstName,
          createNewsletterSubscriberDto.lastName,
        ) ||
        createNewsletterSubscriberDto.email ||
        createNewsletterSubscriberDto.phoneNumber ||
        'Unknown';

      await this.activityLogService.logCreate(
        newNewsletterSubscriber[0].id,
        EntityType.CUSTOMER,
        userId,
        businessId,
        {
          reason: `Newsletter subscriber "${subscriberName}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newNewsletterSubscriber[0].id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to create newsletter subscriber: ' + error.message,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createNewsletterSubscriberDtos: CreateNewsletterSubscriberDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; count: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createNewsletterSubscriberDtos ||
        createNewsletterSubscriberDtos.length === 0
      ) {
        throw new BadRequestException('No newsletter subscribers provided');
      }

      // Validate each newsletter subscriber
      for (const dto of createNewsletterSubscriberDtos) {
        if (!dto.email && !dto.phoneNumber) {
          throw new BadRequestException(
            'Either email or phone number must be provided for each newsletter subscriber',
          );
        }
      }

      // Check for duplicate emails and phone numbers within the batch
      const emails = createNewsletterSubscriberDtos
        .map((dto) => dto.email)
        .filter((email) => email);
      const phoneNumbers = createNewsletterSubscriberDtos
        .map((dto) => dto.phoneNumber)
        .filter((phone) => phone);

      const duplicateEmails = emails.filter(
        (email, index) => emails.indexOf(email) !== index,
      );
      const duplicatePhones = phoneNumbers.filter(
        (phone, index) => phoneNumbers.indexOf(phone) !== index,
      );

      if (duplicateEmails.length > 0) {
        throw new ConflictException(
          `Duplicate emails found in batch: ${duplicateEmails.join(', ')}`,
        );
      }

      if (duplicatePhones.length > 0) {
        throw new ConflictException(
          `Duplicate phone numbers found in batch: ${duplicatePhones.join(', ')}`,
        );
      }

      // Check for existing newsletter subscribers
      const existingSubscribers = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(
          and(
            eq(newsletterSubscribers.businessId, businessId),
            or(
              emails.length > 0
                ? sql`${newsletterSubscribers.email} ILIKE ANY(${emails})`
                : sql`false`,
              phoneNumbers.length > 0
                ? sql`${newsletterSubscribers.phoneNumber} = ANY(${phoneNumbers})`
                : sql`false`,
            ),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        );

      if (existingSubscribers.length > 0) {
        const existingEmails = existingSubscribers
          .map((s) => s.email)
          .filter(Boolean);
        const existingPhones = existingSubscribers
          .map((s) => s.phoneNumber)
          .filter(Boolean);
        throw new ConflictException(
          `Newsletter subscribers already exist: ${[...existingEmails, ...existingPhones].join(', ')}`,
        );
      }

      const createdIds: string[] = [];

      await this.db.transaction(async (tx) => {
        for (const dto of createNewsletterSubscriberDtos) {
          const result = await tx
            .insert(newsletterSubscribers)
            .values({
              businessId,
              email: dto.email,
              emailVerified: dto.emailVerified ?? false,
              phoneNumber: dto.phoneNumber,
              phoneVerified: dto.phoneVerified ?? false,
              languagePreference: dto.languagePreference ?? 'en',
              subscriptionDate: dto.subscriptionDate
                ? new Date(dto.subscriptionDate)
                : new Date(),
              unsubscriptionDate: dto.unsubscriptionDate
                ? new Date(dto.unsubscriptionDate)
                : null,
              status: dto.status ?? NewsletterSubscriberStatus.PENDING,
              title: dto.title,
              firstName: dto.firstName,
              middleName: dto.middleName,
              lastName: dto.lastName,
              suffix: dto.suffix,
              source: dto.source as any,
              createdBy: userId,
            })
            .returning({ id: newsletterSubscribers.id });

          createdIds.push(result[0].id);
        }
      });

      // Log the bulk creation activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.CUSTOMER,
        createdIds,
        {
          names: createNewsletterSubscriberDtos.map(
            (dto) =>
              this.getFullName(dto.firstName, dto.lastName) ||
              dto.email ||
              dto.phoneNumber ||
              'Unknown',
          ),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createdIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        ids: createdIds,
        count: createdIds.length,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk create newsletter subscribers: ' + error.message,
      );
    }
  }

  async findAll(
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    email?: string,
    phoneNumber?: string,
    fullName?: string,
    status?: NewsletterSubscriberStatus,
    source?: string,
    emailVerified?: string,
    phoneVerified?: string,
    from?: string,
    to?: string,
    filters?: string,
    joinOperator: 'and' | 'or' = 'and',
    sort?: string,
  ): Promise<{
    data: NewsletterSubscriberListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const offset = (page - 1) * limit;

      // Build where conditions
      const whereConditions = [
        eq(newsletterSubscribers.businessId, businessId),
        eq(newsletterSubscribers.isDeleted, false),
      ];

      // Individual field filtering
      if (email) {
        whereConditions.push(ilike(newsletterSubscribers.email, `%${email}%`));
      }

      if (phoneNumber) {
        whereConditions.push(
          ilike(newsletterSubscribers.phoneNumber, `%${phoneNumber}%`),
        );
      }

      if (fullName) {
        whereConditions.push(
          or(
            ilike(newsletterSubscribers.firstName, `%${fullName}%`),
            ilike(newsletterSubscribers.lastName, `%${fullName}%`),
            sql`CONCAT(${newsletterSubscribers.firstName}, ' ', ${newsletterSubscribers.lastName}) ILIKE ${`%${fullName}%`}`,
          ),
        );
      }

      if (status) {
        whereConditions.push(eq(newsletterSubscribers.status, status));
      }

      if (source) {
        whereConditions.push(eq(newsletterSubscribers.source, source as any));
      }

      if (emailVerified) {
        whereConditions.push(
          eq(newsletterSubscribers.emailVerified, emailVerified === 'true'),
        );
      }

      if (phoneVerified) {
        whereConditions.push(
          eq(newsletterSubscribers.phoneVerified, phoneVerified === 'true'),
        );
      }

      // Date range filtering
      if (from) {
        whereConditions.push(
          sql`${newsletterSubscribers.createdAt} >= ${from}`,
        );
      }

      if (to) {
        whereConditions.push(sql`${newsletterSubscribers.createdAt} <= ${to}`);
      }

      // Advanced filters
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          const filterConditions = [];

          for (const filter of parsedFilters) {
            const { id: fieldId, value, operator } = filter;

            if (fieldId === 'email') {
              switch (operator) {
                case 'iLike':
                  filterConditions.push(
                    ilike(newsletterSubscribers.email, `%${value}%`),
                  );
                  break;
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(newsletterSubscribers.email, `%${value}%`)}`,
                  );
                  break;
                case 'eq':
                  filterConditions.push(eq(newsletterSubscribers.email, value));
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.email} != ${value}`,
                  );
                  break;
                case 'isEmpty':
                  filterConditions.push(
                    or(
                      isNull(newsletterSubscribers.email),
                      eq(newsletterSubscribers.email, ''),
                    ),
                  );
                  break;
                case 'isNotEmpty':
                  filterConditions.push(
                    and(
                      sql`${newsletterSubscribers.email} IS NOT NULL`,
                      sql`${newsletterSubscribers.email} != ''`,
                    ),
                  );
                  break;
              }
            } else if (fieldId === 'phoneNumber') {
              switch (operator) {
                case 'iLike':
                  filterConditions.push(
                    ilike(newsletterSubscribers.phoneNumber, `%${value}%`),
                  );
                  break;
                case 'notILike':
                  filterConditions.push(
                    sql`NOT ${ilike(newsletterSubscribers.phoneNumber, `%${value}%`)}`,
                  );
                  break;
                case 'eq':
                  filterConditions.push(
                    eq(newsletterSubscribers.phoneNumber, value),
                  );
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.phoneNumber} != ${value}`,
                  );
                  break;
                case 'isEmpty':
                  filterConditions.push(
                    or(
                      isNull(newsletterSubscribers.phoneNumber),
                      eq(newsletterSubscribers.phoneNumber, ''),
                    ),
                  );
                  break;
                case 'isNotEmpty':
                  filterConditions.push(
                    and(
                      sql`${newsletterSubscribers.phoneNumber} IS NOT NULL`,
                      sql`${newsletterSubscribers.phoneNumber} != ''`,
                    ),
                  );
                  break;
              }
            } else if (fieldId === 'fullName') {
              switch (operator) {
                case 'iLike':
                  filterConditions.push(
                    or(
                      ilike(newsletterSubscribers.firstName, `%${value}%`),
                      ilike(newsletterSubscribers.lastName, `%${value}%`),
                      sql`CONCAT(${newsletterSubscribers.firstName}, ' ', ${newsletterSubscribers.lastName}) ILIKE ${`%${value}%`}`,
                    ),
                  );
                  break;
                case 'notILike':
                  filterConditions.push(
                    and(
                      sql`NOT ${ilike(newsletterSubscribers.firstName, `%${value}%`)}`,
                      sql`NOT ${ilike(newsletterSubscribers.lastName, `%${value}%`)}`,
                      sql`NOT (CONCAT(${newsletterSubscribers.firstName}, ' ', ${newsletterSubscribers.lastName}) ILIKE ${`%${value}%`})`,
                    ),
                  );
                  break;
              }
            } else if (fieldId === 'status') {
              switch (operator) {
                case 'eq':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      inArray(newsletterSubscribers.status, value),
                    );
                  } else {
                    filterConditions.push(
                      eq(newsletterSubscribers.status, value),
                    );
                  }
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.status} != ${value}`,
                  );
                  break;
              }
            } else if (fieldId === 'source') {
              switch (operator) {
                case 'eq':
                  if (Array.isArray(value)) {
                    filterConditions.push(
                      inArray(newsletterSubscribers.source, value),
                    );
                  } else {
                    filterConditions.push(
                      eq(newsletterSubscribers.source, value),
                    );
                  }
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.source} != ${value}`,
                  );
                  break;
              }
            } else if (fieldId === 'emailVerified') {
              switch (operator) {
                case 'eq':
                  filterConditions.push(
                    eq(newsletterSubscribers.emailVerified, value === 'true'),
                  );
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.emailVerified} != ${value === 'true'}`,
                  );
                  break;
              }
            } else if (fieldId === 'phoneVerified') {
              switch (operator) {
                case 'eq':
                  filterConditions.push(
                    eq(newsletterSubscribers.phoneVerified, value === 'true'),
                  );
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.phoneVerified} != ${value === 'true'}`,
                  );
                  break;
              }
            } else if (fieldId === 'subscriptionDate') {
              switch (operator) {
                case 'eq':
                  filterConditions.push(
                    eq(newsletterSubscribers.subscriptionDate, value),
                  );
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.subscriptionDate} != ${value}`,
                  );
                  break;
                case 'lt':
                  filterConditions.push(
                    sql`${newsletterSubscribers.subscriptionDate} < ${value}`,
                  );
                  break;
                case 'lte':
                  filterConditions.push(
                    sql`${newsletterSubscribers.subscriptionDate} <= ${value}`,
                  );
                  break;
                case 'gt':
                  filterConditions.push(
                    sql`${newsletterSubscribers.subscriptionDate} > ${value}`,
                  );
                  break;
                case 'gte':
                  filterConditions.push(
                    sql`${newsletterSubscribers.subscriptionDate} >= ${value}`,
                  );
                  break;
              }
            } else if (fieldId === 'createdAt') {
              switch (operator) {
                case 'eq':
                  filterConditions.push(
                    eq(newsletterSubscribers.createdAt, value),
                  );
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.createdAt} != ${value}`,
                  );
                  break;
                case 'lt':
                  filterConditions.push(
                    sql`${newsletterSubscribers.createdAt} < ${value}`,
                  );
                  break;
                case 'lte':
                  filterConditions.push(
                    sql`${newsletterSubscribers.createdAt} <= ${value}`,
                  );
                  break;
                case 'gt':
                  filterConditions.push(
                    sql`${newsletterSubscribers.createdAt} > ${value}`,
                  );
                  break;
                case 'gte':
                  filterConditions.push(
                    sql`${newsletterSubscribers.createdAt} >= ${value}`,
                  );
                  break;
              }
            } else if (fieldId === 'updatedAt') {
              switch (operator) {
                case 'eq':
                  filterConditions.push(
                    eq(newsletterSubscribers.updatedAt, value),
                  );
                  break;
                case 'ne':
                  filterConditions.push(
                    sql`${newsletterSubscribers.updatedAt} != ${value}`,
                  );
                  break;
                case 'lt':
                  filterConditions.push(
                    sql`${newsletterSubscribers.updatedAt} < ${value}`,
                  );
                  break;
                case 'lte':
                  filterConditions.push(
                    sql`${newsletterSubscribers.updatedAt} <= ${value}`,
                  );
                  break;
                case 'gt':
                  filterConditions.push(
                    sql`${newsletterSubscribers.updatedAt} > ${value}`,
                  );
                  break;
                case 'gte':
                  filterConditions.push(
                    sql`${newsletterSubscribers.updatedAt} >= ${value}`,
                  );
                  break;
              }
            }
          }

          // Join filter conditions based on joinOperator
          if (filterConditions.length > 0) {
            if (joinOperator === 'or') {
              whereConditions.push(or(...filterConditions));
            } else {
              whereConditions.push(and(...filterConditions));
            }
          }
        } catch (error) {
          throw new BadRequestException(
            'Invalid filters JSON: ' + error.message,
          );
        }
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: count() })
        .from(newsletterSubscribers)
        .where(and(...whereConditions));

      const total = totalResult[0].count;

      // Build order by from sort configuration
      let orderByClause = desc(newsletterSubscribers.createdAt);
      if (sort) {
        try {
          const sortConfig = JSON.parse(sort);
          if (sortConfig.length > 0) {
            const { id: sortField, desc: isDesc } = sortConfig[0];
            switch (sortField) {
              case 'email':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.email)
                  : asc(newsletterSubscribers.email);
                break;
              case 'fullName':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.firstName)
                  : asc(newsletterSubscribers.firstName);
                break;
              case 'phoneNumber':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.phoneNumber)
                  : asc(newsletterSubscribers.phoneNumber);
                break;
              case 'status':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.status)
                  : asc(newsletterSubscribers.status);
                break;
              case 'source':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.source)
                  : asc(newsletterSubscribers.source);
                break;
              case 'subscriptionDate':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.subscriptionDate)
                  : asc(newsletterSubscribers.subscriptionDate);
                break;
              case 'createdAt':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.createdAt)
                  : asc(newsletterSubscribers.createdAt);
                break;
              case 'updatedAt':
                orderByClause = isDesc
                  ? desc(newsletterSubscribers.updatedAt)
                  : asc(newsletterSubscribers.updatedAt);
                break;
              default:
                orderByClause = desc(newsletterSubscribers.createdAt);
            }
          }
        } catch (error) {
          throw new BadRequestException('Invalid sort JSON: ' + error.message);
        }
      }

      // Get paginated data
      const data = await this.db
        .select({
          id: newsletterSubscribers.id,
          email: newsletterSubscribers.email,
          emailVerified: newsletterSubscribers.emailVerified,
          phoneNumber: newsletterSubscribers.phoneNumber,
          phoneVerified: newsletterSubscribers.phoneVerified,
          languagePreference: newsletterSubscribers.languagePreference,
          subscriptionDate: newsletterSubscribers.subscriptionDate,
          unsubscriptionDate: newsletterSubscribers.unsubscriptionDate,
          status: newsletterSubscribers.status,
          firstName: newsletterSubscribers.firstName,
          lastName: newsletterSubscribers.lastName,
          source: newsletterSubscribers.source,
          createdBy: users.firstName,
          createdAt: newsletterSubscribers.createdAt,
          updatedAt: newsletterSubscribers.updatedAt,
        })
        .from(newsletterSubscribers)
        .leftJoin(users, eq(newsletterSubscribers.createdBy, users.id))
        .where(and(...whereConditions))
        .orderBy(orderByClause, asc(newsletterSubscribers.id))
        .limit(limit)
        .offset(offset);

      const totalPages = Math.ceil(total / limit);

      return {
        data: data.map((item) => ({
          id: item.id,
          email: item.email,
          emailVerified: item.emailVerified,
          phoneNumber: item.phoneNumber,
          phoneVerified: item.phoneVerified,
          languagePreference: item.languagePreference,
          subscriptionDate: item.subscriptionDate,
          unsubscriptionDate: item.unsubscriptionDate,
          status: item.status,
          fullName: this.getFullName(item.firstName, item.lastName),
          source: item.source,
          createdBy: item.createdBy || 'Unknown',
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        })),
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to fetch newsletter subscribers: ' + error.message,
      );
    }
  }

  async findOne(
    id: string,
    businessId: string | null,
  ): Promise<NewsletterSubscriberDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const result = await this.db
        .select({
          id: newsletterSubscribers.id,
          businessId: newsletterSubscribers.businessId,
          email: newsletterSubscribers.email,
          emailVerified: newsletterSubscribers.emailVerified,
          phoneNumber: newsletterSubscribers.phoneNumber,
          phoneVerified: newsletterSubscribers.phoneVerified,
          languagePreference: newsletterSubscribers.languagePreference,
          subscriptionDate: newsletterSubscribers.subscriptionDate,
          unsubscriptionDate: newsletterSubscribers.unsubscriptionDate,
          status: newsletterSubscribers.status,
          title: newsletterSubscribers.title,
          firstName: newsletterSubscribers.firstName,
          middleName: newsletterSubscribers.middleName,
          lastName: newsletterSubscribers.lastName,
          suffix: newsletterSubscribers.suffix,
          source: newsletterSubscribers.source,
          createdBy: users.firstName,
          updatedBy: sql<string>`updated_user.first_name`,
          createdAt: newsletterSubscribers.createdAt,
          updatedAt: newsletterSubscribers.updatedAt,
        })
        .from(newsletterSubscribers)
        .leftJoin(users, eq(newsletterSubscribers.createdBy, users.id))
        .leftJoin(
          sql`users AS updated_user`,
          sql`newsletter_subscribers.updated_by = updated_user.id`,
        )
        .leftJoin(
          sql`users AS deleted_user`,
          sql`newsletter_subscribers.deleted_by = deleted_user.id`,
        )
        .where(
          and(
            eq(newsletterSubscribers.id, id),
            eq(newsletterSubscribers.businessId, businessId),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!result) {
        throw new NotFoundException('Newsletter subscriber not found');
      }

      return {
        id: result.id,
        businessId: result.businessId,
        email: result.email,
        emailVerified: result.emailVerified,
        phoneNumber: result.phoneNumber,
        phoneVerified: result.phoneVerified,
        languagePreference: result.languagePreference,
        subscriptionDate: result.subscriptionDate,
        unsubscriptionDate: result.unsubscriptionDate,
        status: result.status,
        title: result.title,
        firstName: result.firstName,
        middleName: result.middleName,
        lastName: result.lastName,
        suffix: result.suffix,
        source: result.source,
        createdBy: result.createdBy || 'Unknown',
        updatedBy: result.updatedBy,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to fetch newsletter subscriber: ' + error.message,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateNewsletterSubscriberDto: UpdateNewsletterSubscriberDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if newsletter subscriber exists
      const existingSubscriber = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(
          and(
            eq(newsletterSubscribers.id, id),
            eq(newsletterSubscribers.businessId, businessId),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingSubscriber) {
        throw new NotFoundException('Newsletter subscriber not found');
      }

      // Check for email conflicts if email is being updated
      if (
        updateNewsletterSubscriberDto.email &&
        updateNewsletterSubscriberDto.email !== existingSubscriber.email
      ) {
        const emailConflict = await this.db
          .select()
          .from(newsletterSubscribers)
          .where(
            and(
              eq(newsletterSubscribers.businessId, businessId),
              ilike(
                newsletterSubscribers.email,
                updateNewsletterSubscriberDto.email,
              ),
              eq(newsletterSubscribers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (emailConflict) {
          throw new ConflictException(
            `Newsletter subscriber with email "${updateNewsletterSubscriberDto.email}" already exists`,
          );
        }
      }

      // Check for phone number conflicts if phone number is being updated
      if (
        updateNewsletterSubscriberDto.phoneNumber &&
        updateNewsletterSubscriberDto.phoneNumber !==
          existingSubscriber.phoneNumber
      ) {
        const phoneConflict = await this.db
          .select()
          .from(newsletterSubscribers)
          .where(
            and(
              eq(newsletterSubscribers.businessId, businessId),
              eq(
                newsletterSubscribers.phoneNumber,
                updateNewsletterSubscriberDto.phoneNumber,
              ),
              eq(newsletterSubscribers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (phoneConflict) {
          throw new ConflictException(
            `Newsletter subscriber with phone number "${updateNewsletterSubscriberDto.phoneNumber}" already exists`,
          );
        }
      }

      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update fields that are provided
      if (updateNewsletterSubscriberDto.email !== undefined) {
        updateData.email = updateNewsletterSubscriberDto.email;
      }
      if (updateNewsletterSubscriberDto.emailVerified !== undefined) {
        updateData.emailVerified = updateNewsletterSubscriberDto.emailVerified;
      }
      if (updateNewsletterSubscriberDto.phoneNumber !== undefined) {
        updateData.phoneNumber = updateNewsletterSubscriberDto.phoneNumber;
      }
      if (updateNewsletterSubscriberDto.phoneVerified !== undefined) {
        updateData.phoneVerified = updateNewsletterSubscriberDto.phoneVerified;
      }
      if (updateNewsletterSubscriberDto.languagePreference !== undefined) {
        updateData.languagePreference =
          updateNewsletterSubscriberDto.languagePreference;
      }
      if (updateNewsletterSubscriberDto.subscriptionDate !== undefined) {
        updateData.subscriptionDate = new Date(
          updateNewsletterSubscriberDto.subscriptionDate,
        );
      }
      if (updateNewsletterSubscriberDto.unsubscriptionDate !== undefined) {
        updateData.unsubscriptionDate = new Date(
          updateNewsletterSubscriberDto.unsubscriptionDate,
        );
      }
      if (updateNewsletterSubscriberDto.status !== undefined) {
        updateData.status = updateNewsletterSubscriberDto.status;
      }
      if (updateNewsletterSubscriberDto.title !== undefined) {
        updateData.title = updateNewsletterSubscriberDto.title;
      }
      if (updateNewsletterSubscriberDto.firstName !== undefined) {
        updateData.firstName = updateNewsletterSubscriberDto.firstName;
      }
      if (updateNewsletterSubscriberDto.middleName !== undefined) {
        updateData.middleName = updateNewsletterSubscriberDto.middleName;
      }
      if (updateNewsletterSubscriberDto.lastName !== undefined) {
        updateData.lastName = updateNewsletterSubscriberDto.lastName;
      }
      if (updateNewsletterSubscriberDto.suffix !== undefined) {
        updateData.suffix = updateNewsletterSubscriberDto.suffix;
      }
      if (updateNewsletterSubscriberDto.source !== undefined) {
        updateData.source = updateNewsletterSubscriberDto.source as any;
      }

      await this.db
        .update(newsletterSubscribers)
        .set(updateData)
        .where(
          and(
            eq(newsletterSubscribers.id, id),
            eq(newsletterSubscribers.businessId, businessId),
          ),
        );

      // Log the newsletter subscriber update activity
      const subscriberName =
        this.getFullName(
          updateNewsletterSubscriberDto.firstName ||
            existingSubscriber.firstName,
          updateNewsletterSubscriberDto.lastName || existingSubscriber.lastName,
        ) ||
        updateNewsletterSubscriberDto.email ||
        existingSubscriber.email ||
        updateNewsletterSubscriberDto.phoneNumber ||
        existingSubscriber.phoneNumber ||
        'Unknown';

      await this.activityLogService.logUpdate(
        id,
        EntityType.CUSTOMER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update newsletter subscriber: ' + error.message,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if newsletter subscriber exists
      const existingSubscriber = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(
          and(
            eq(newsletterSubscribers.id, id),
            eq(newsletterSubscribers.businessId, businessId),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingSubscriber) {
        throw new NotFoundException('Newsletter subscriber not found');
      }

      // Soft delete the newsletter subscriber
      await this.db
        .update(newsletterSubscribers)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(newsletterSubscribers.id, id),
            eq(newsletterSubscribers.businessId, businessId),
          ),
        );

      // Log the newsletter subscriber deletion activity
      const subscriberName =
        this.getFullName(
          existingSubscriber.firstName,
          existingSubscriber.lastName,
        ) ||
        existingSubscriber.email ||
        existingSubscriber.phoneNumber ||
        'Unknown';

      await this.activityLogService.logDelete(
        id,
        EntityType.CUSTOMER,
        userId,
        businessId,
        {
          reason: `Newsletter subscriber "${subscriberName}" was deleted`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        success: true,
        message: 'Newsletter subscriber deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to delete newsletter subscriber: ' + error.message,
      );
    }
  }

  async bulkRemove(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedIds: string[]; count: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No newsletter subscriber IDs provided');
      }

      // Check which newsletter subscribers exist
      const existingSubscribers = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(
          and(
            sql`${newsletterSubscribers.id} = ANY(${ids})`,
            eq(newsletterSubscribers.businessId, businessId),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        );

      if (existingSubscribers.length === 0) {
        throw new NotFoundException(
          'No newsletter subscribers found to delete',
        );
      }

      const existingIds = existingSubscribers.map((s) => s.id);

      // Soft delete the newsletter subscribers
      await this.db
        .update(newsletterSubscribers)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            sql`${newsletterSubscribers.id} = ANY(${existingIds})`,
            eq(newsletterSubscribers.businessId, businessId),
          ),
        );

      // Log the bulk deletion activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.CUSTOMER,
        existingIds,
        {
          names: existingSubscribers.map(
            (s) =>
              this.getFullName(s.firstName, s.lastName) ||
              s.email ||
              s.phoneNumber ||
              'Unknown',
          ),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: existingIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedIds: existingIds,
        count: existingIds.length,
        message: 'Newsletter subscribers deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk delete newsletter subscribers: ' + error.message,
      );
    }
  }

  async bulkStatusUpdate(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: NewsletterSubscriberStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedIds: string[]; count: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No newsletter subscriber IDs provided');
      }

      // Check which newsletter subscribers exist
      const existingSubscribers = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(
          and(
            inArray(newsletterSubscribers.id, ids),
            eq(newsletterSubscribers.businessId, businessId),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        );

      if (existingSubscribers.length === 0) {
        throw new NotFoundException(
          'No newsletter subscribers found to update',
        );
      }

      const existingIds = existingSubscribers.map((s) => s.id);

      // Update the newsletter subscribers status
      await this.db
        .update(newsletterSubscribers)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            inArray(newsletterSubscribers.id, existingIds),
            eq(newsletterSubscribers.businessId, businessId),
          ),
        );

      // Log the bulk status update activity
      try {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.CUSTOMER,
          existingIds,
          {
            names: existingSubscribers.map(
              (s) =>
                this.getFullName(s.firstName, s.lastName) ||
                s.email ||
                s.phoneNumber ||
                'Unknown',
            ),
          },
          userId,
          businessId,
          {
            filterCriteria: { count: existingIds.length, statusUpdate: status },
            executionStrategy: ExecutionStrategy.PARALLEL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      } catch (logError) {
        // Log the error but don't fail the entire operation
        console.error('Failed to log bulk status update activity:', logError);
      }

      return {
        updatedIds: existingIds,
        count: existingIds.length,
        message: 'Newsletter subscribers status updated successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk update newsletter subscribers status: ' + error.message,
      );
    }
  }

  async bulkVerificationUpdate(
    userId: string,
    businessId: string | null,
    ids: string[],
    field: 'emailVerified' | 'phoneVerified',
    verified: boolean,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedIds: string[]; count: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No newsletter subscriber IDs provided');
      }

      // Check which newsletter subscribers exist
      const existingSubscribers = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(
          and(
            inArray(newsletterSubscribers.id, ids),
            eq(newsletterSubscribers.businessId, businessId),
            eq(newsletterSubscribers.isDeleted, false),
          ),
        );

      if (existingSubscribers.length === 0) {
        throw new NotFoundException(
          'No newsletter subscribers found to update',
        );
      }

      const existingIds = existingSubscribers.map((s) => s.id);

      // Update the newsletter subscribers verification
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };
      updateData[field] = verified;

      await this.db
        .update(newsletterSubscribers)
        .set(updateData)
        .where(
          and(
            inArray(newsletterSubscribers.id, existingIds),
            eq(newsletterSubscribers.businessId, businessId),
          ),
        );

      // Log the bulk verification update activity
      try {
        // const fieldLabel = field === 'emailVerified' ? 'email' : 'phone';
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.CUSTOMER,
          existingIds,
          {
            names: existingSubscribers.map(
              (s) =>
                this.getFullName(s.firstName, s.lastName) ||
                s.email ||
                s.phoneNumber ||
                'Unknown',
            ),
          },
          userId,
          businessId,
          {
            filterCriteria: {
              count: existingIds.length,
              verificationUpdate: { field, verified },
            },
            executionStrategy: ExecutionStrategy.PARALLEL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      } catch (logError) {
        // Log the error but don't fail the entire operation
        console.error(
          'Failed to log bulk verification update activity:',
          logError,
        );
      }

      return {
        updatedIds: existingIds,
        count: existingIds.length,
        message: 'Newsletter subscribers verification updated successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk update newsletter subscribers verification: ' +
          error.message,
      );
    }
  }

  async findSlim(
    businessId: string | null,
    search?: string,
    limit: number = 50,
  ): Promise<NewsletterSubscriberSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const whereConditions = [
        eq(newsletterSubscribers.businessId, businessId),
        eq(newsletterSubscribers.isDeleted, false),
      ];

      if (search) {
        whereConditions.push(
          or(
            ilike(newsletterSubscribers.email, `%${search}%`),
            ilike(newsletterSubscribers.phoneNumber, `%${search}%`),
            ilike(newsletterSubscribers.firstName, `%${search}%`),
            ilike(newsletterSubscribers.lastName, `%${search}%`),
          ),
        );
      }

      const data = await this.db
        .select({
          id: newsletterSubscribers.id,
          firstName: newsletterSubscribers.firstName,
          lastName: newsletterSubscribers.lastName,
          email: newsletterSubscribers.email,
          phoneNumber: newsletterSubscribers.phoneNumber,
        })
        .from(newsletterSubscribers)
        .where(and(...whereConditions))
        .orderBy(asc(newsletterSubscribers.firstName))
        .limit(limit);

      return data.map((item) => ({
        id: item.id,
        name: this.getFullName(item.firstName, item.lastName) || 'Unknown',
        email: item.email,
        phoneNumber: item.phoneNumber,
      }));
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to fetch newsletter subscribers: ' + error.message,
      );
    }
  }

  async checkEmailAvailability(
    businessId: string | null,
    email: string,
    excludeId?: string,
  ): Promise<{ available: boolean; email: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const whereConditions = [
        eq(newsletterSubscribers.businessId, businessId),
        ilike(newsletterSubscribers.email, email),
        eq(newsletterSubscribers.isDeleted, false),
      ];

      if (excludeId) {
        whereConditions.push(sql`${newsletterSubscribers.id} != ${excludeId}`);
      }

      const existingSubscriber = await this.db
        .select()
        .from(newsletterSubscribers)
        .where(and(...whereConditions))
        .then((results) => results[0]);

      const available = !existingSubscriber;

      return {
        available,
        email,
        message: available
          ? 'Email is available'
          : 'Email is already in use by another newsletter subscriber',
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to check email availability: ' + error.message,
      );
    }
  }

  private getFullName(firstName?: string, lastName?: string): string | null {
    const parts = [firstName, lastName].filter(Boolean);
    return parts.length > 0 ? parts.join(' ') : null;
  }
}
