import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskDto } from './dto/task.dto';
import { TaskSlimDto } from './dto/task-slim.dto';
import { TaskListDto } from './dto/task-list.dto';
import {
  tasks,
  taskAttachments,
  TaskReferenceType,
  TaskPriority,
  TaskStatus,
} from '../drizzle/schema/tasks.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { projects } from '../drizzle/schema/projects.schema';
import { transactions } from '../drizzle/schema/transactions.schema';
import { serviceOrders } from '../drizzle/schema/service-orders.schema';
import { media } from '../drizzle/schema/media.schema';
import { users } from '../drizzle/schema/users.schema';
import { RecurringEntityType } from '../drizzle/schema/recurring-activities.schema';
import {
  eq,
  and,
  isNull,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
  count,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
// import { RecurringActivitiesService } from '../recurring-activities/recurring-activities.service';

@Injectable()
export class TasksService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    // private readonly recurringActivitiesService: RecurringActivitiesService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createTaskDto: CreateTaskDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate assigned staff member if provided
      if (createTaskDto.assignedTo) {
        const staffMember = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createTaskDto.assignedTo),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'Assigned staff member not found or does not belong to this business',
          );
        }
      }

      // Validate entity relationships if provided
      await this.validateEntityReferences(businessId, createTaskDto);

      return await this.db.transaction(async (tx) => {
        const recurringActivityId = createTaskDto.recurringActivityId;

        // Create recurring activity if this is a recurring task
        // if (createTaskDto.isRecurring) {
        //   const recurringActivityResult =
        //     await this.recurringActivitiesService.create(userId, businessId, {
        //       entityType: RecurringEntityType.TASK,
        //       entityId: '', // Will be updated after task creation
        //       recurrencePattern: createTaskDto.recurrencePattern,
        //       recurrenceInterval: createTaskDto.recurrenceInterval,
        //       recurrenceEndType: createTaskDto.recurrenceEndType,
        //       recurrenceEndDate: createTaskDto.recurrenceEndDate,
        //       recurrenceEndAfterOccurrences:
        //         createTaskDto.recurrenceEndAfterOccurrences,
        //       recurrenceDaysOfWeek: createTaskDto.recurrenceDaysOfWeek,
        //       recurrenceDayOfMonth: createTaskDto.recurrenceDayOfMonth,
        //       recurrenceMonthOfYear: createTaskDto.recurrenceMonthOfYear,
        //       title: `Recurring: ${createTaskDto.title}`,
        //       description: createTaskDto.description,
        //     });

        //   recurringActivityId = recurringActivityResult.id;
        // }

        // Create the task
        const [task] = await tx
          .insert(tasks)
          .values({
            businessId,
            title: createTaskDto.title,
            description: createTaskDto.description,
            dueDate: createTaskDto.dueDate,
            priority: createTaskDto.priority,
            status: createTaskDto.status,
            assignedTo: createTaskDto.assignedTo,
            referenceId: createTaskDto.referenceId,
            referenceType: createTaskDto.referenceType,
            recurringActivityId,
            parentTaskId: createTaskDto.parentTaskId,
            createdBy: userId,
          })
          .returning();

        // Update recurring activity with the task ID if we created one
        // if (createTaskDto.isRecurring && recurringActivityId) {
        //   await this.recurringActivitiesService.update(
        //     userId,
        //     businessId,
        //     recurringActivityId,
        //     { entityId: task.id },
        //   );
        // }

        // Handle attachments if provided
        if (
          createTaskDto.attachmentIds &&
          createTaskDto.attachmentIds.length > 0
        ) {
          await this.handleTaskAttachments(
            tx,
            task.id,
            createTaskDto.attachmentIds,
            businessId,
          );
        }

        // Log activity
        await this.activityLogService.logCreate(
          task.id,
          EntityType.TASK,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        return { id: task.id };
      });
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create task');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createTaskDto: CreateTaskDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createTaskDto, metadata);
  }

  /**
   * Create a task with work order reference
   * This method is specifically designed for creating tasks associated with work orders
   */
  async createTaskForWorkOrder(
    userId: string,
    businessId: string | null,
    workOrderId: string,
    taskData: {
      title: string;
      description?: string;
      dueDate?: string;
      priority?: TaskPriority;
      assignedTo?: string;
    },
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate assigned staff member if provided
      if (taskData.assignedTo) {
        const staffMember = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, taskData.assignedTo),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'Assigned staff member not found or does not belong to this business',
          );
        }
      }

      // Create the task with work order reference
      const [task] = await this.db
        .insert(tasks)
        .values({
          businessId,
          title: taskData.title,
          description: taskData.description,
          dueDate: taskData.dueDate,
          priority: taskData.priority || TaskPriority.MEDIUM,
          status: TaskStatus.PENDING,
          assignedTo: taskData.assignedTo,
          referenceId: workOrderId,
          referenceType: TaskReferenceType.WORK_ORDER,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        task.id,
        EntityType.TASK,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: task.id };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create task for work order');
    }
  }

  /**
   * Create a task with service order reference
   * This method is specifically designed for creating tasks associated with service orders
   */
  async createTaskForServiceOrder(
    userId: string,
    businessId: string | null,
    serviceOrderId: string,
    taskData: {
      title: string;
      description?: string;
      dueDate?: string;
      priority?: TaskPriority;
      status?: TaskStatus;
      assignedTo?: string;
    },
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate assigned staff member if provided
      if (taskData.assignedTo) {
        const staffMember = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, taskData.assignedTo),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'Assigned staff member not found or does not belong to this business',
          );
        }
      }

      // Create the task with service order reference
      const [task] = await this.db
        .insert(tasks)
        .values({
          businessId,
          title: taskData.title,
          description: taskData.description,
          dueDate: taskData.dueDate,
          priority: taskData.priority || TaskPriority.MEDIUM,
          status: taskData.status || TaskStatus.PENDING,
          assignedTo: taskData.assignedTo,
          referenceId: serviceOrderId,
          referenceType: TaskReferenceType.SERVICE_ORDER,
          createdBy: userId,
        })
        .returning();

      // Log the activity
      await this.activityLogService.logCreate(
        task.id,
        EntityType.TASK,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: task.id };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create task for service order');
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createTaskDtos: CreateTaskDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!createTaskDtos || createTaskDtos.length === 0) {
      throw new BadRequestException('No tasks provided for bulk creation');
    }

    const createdIds: string[] = [];

    try {
      return await this.db.transaction(async (tx) => {
        for (const createTaskDto of createTaskDtos) {
          // Validate each task
          if (createTaskDto.assignedTo) {
            const staffMember = await tx
              .select()
              .from(staffMembers)
              .where(
                and(
                  eq(staffMembers.id, createTaskDto.assignedTo),
                  eq(staffMembers.businessId, businessId),
                  eq(staffMembers.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!staffMember) {
              throw new BadRequestException(
                `Assigned staff member ${createTaskDto.assignedTo} not found`,
              );
            }
          }

          const recurringActivityId = createTaskDto.recurringActivityId;

          // Create recurring activity if this is a recurring task
          // if (createTaskDto.isRecurring) {
          //   const recurringActivityResult =
          //     await this.recurringActivitiesService.create(userId, businessId, {
          //       entityType: RecurringEntityType.TASK,
          //       entityId: '', // Will be updated after task creation
          //       recurrencePattern: createTaskDto.recurrencePattern,
          //       recurrenceInterval: createTaskDto.recurrenceInterval,
          //       recurrenceEndType: createTaskDto.recurrenceEndType,
          //       recurrenceEndDate: createTaskDto.recurrenceEndDate,
          //       recurrenceEndAfterOccurrences:
          //         createTaskDto.recurrenceEndAfterOccurrences,
          //       recurrenceDaysOfWeek: createTaskDto.recurrenceDaysOfWeek,
          //       recurrenceDayOfMonth: createTaskDto.recurrenceDayOfMonth,
          //       recurrenceMonthOfYear: createTaskDto.recurrenceMonthOfYear,
          //       title: `Recurring: ${createTaskDto.title}`,
          //       description: createTaskDto.description,
          //     });

          //   recurringActivityId = recurringActivityResult.id;
          // }

          // Create task
          const [task] = await tx
            .insert(tasks)
            .values({
              businessId,
              title: createTaskDto.title,
              description: createTaskDto.description,
              dueDate: createTaskDto.dueDate,
              priority: createTaskDto.priority,
              status: createTaskDto.status,
              assignedTo: createTaskDto.assignedTo,
              referenceId: createTaskDto.referenceId,
              referenceType: createTaskDto.referenceType,
              recurringActivityId,
              parentTaskId: createTaskDto.parentTaskId,
              createdBy: userId,
            })
            .returning();

          // Update recurring activity with the task ID if we created one
          // if (createTaskDto.isRecurring && recurringActivityId) {
          //   await this.recurringActivitiesService.update(
          //     userId,
          //     businessId,
          //     recurringActivityId,
          //     { entityId: task.id },
          //   );
          // }

          // Handle attachments if provided
          if (
            createTaskDto.attachmentIds &&
            createTaskDto.attachmentIds.length > 0
          ) {
            await this.handleTaskAttachments(
              tx,
              task.id,
              createTaskDto.attachmentIds,
              businessId,
            );
          }

          createdIds.push(task.id);
        }

        // Log bulk activity
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_CREATE,
          EntityType.TASK,
          createdIds,
          { isCreated: true, createdBy: userId },
          userId,
          businessId,
          {
            filterCriteria: { taskCount: createdIds.length },
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        return { ids: createdIds };
      });
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to bulk create tasks');
    }
  }

  private async validateEntityReferences(
    businessId: string,
    taskDto: CreateTaskDto | UpdateTaskDto,
  ): Promise<void> {
    // Validate reference if provided
    if (taskDto.referenceId && taskDto.referenceType) {
      let entity: any;
      let entityName = '';

      switch (taskDto.referenceType) {
        case TaskReferenceType.SUPPLIER:
          entity = await this.db
            .select()
            .from(suppliers)
            .where(
              and(
                eq(suppliers.id, taskDto.referenceId),
                eq(suppliers.businessId, businessId),
                eq(suppliers.isDeleted, false),
              ),
            )
            .then((results) => results[0]);
          entityName = 'Supplier';
          break;

        case TaskReferenceType.CUSTOMER:
          entity = await this.db
            .select()
            .from(customers)
            .where(
              and(
                eq(customers.id, taskDto.referenceId),
                eq(customers.businessId, businessId),
                eq(customers.isDeleted, false),
              ),
            )
            .then((results) => results[0]);
          entityName = 'Customer';
          break;

        case TaskReferenceType.PROJECT:
          entity = await this.db
            .select()
            .from(projects)
            .where(
              and(
                eq(projects.id, taskDto.referenceId),
                eq(projects.businessId, businessId),
                eq(projects.isDeleted, false),
              ),
            )
            .then((results) => results[0]);
          entityName = 'Project';
          break;

        case TaskReferenceType.TRANSACTION:
          entity = await this.db
            .select()
            .from(transactions)
            .where(
              and(
                eq(transactions.id, taskDto.referenceId),
                eq(transactions.businessId, businessId),
                eq(transactions.isDeleted, false),
              ),
            )
            .then((results) => results[0]);
          entityName = 'Transaction';
          break;

        case TaskReferenceType.SERVICE_ORDER:
          entity = await this.db
            .select()
            .from(serviceOrders)
            .where(
              and(
                eq(serviceOrders.id, taskDto.referenceId),
                eq(serviceOrders.businessId, businessId),
                eq(serviceOrders.isDeleted, false),
              ),
            )
            .then((results) => results[0]);
          entityName = 'Service Order';
          break;

        default:
          throw new BadRequestException('Invalid reference type');
      }

      if (!entity) {
        throw new BadRequestException(
          `${entityName} not found or does not belong to this business`,
        );
      }
    }
  }

  private async handleTaskAttachments(
    tx: any,
    taskId: string,
    attachmentIds: string[],
    businessId: string,
  ): Promise<void> {
    // Validate all attachment IDs belong to the business
    const validAttachments = await tx
      .select({ id: media.id })
      .from(media)
      .where(
        and(inArray(media.id, attachmentIds), eq(media.businessId, businessId)),
      );

    if (validAttachments.length !== attachmentIds.length) {
      throw new BadRequestException(
        'One or more attachments not found or do not belong to this business',
      );
    }

    // Create task attachment relationships
    const attachmentValues = attachmentIds.map((attachmentId) => ({
      taskId,
      attachmentId,
    }));

    await tx.insert(taskAttachments).values(attachmentValues);
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    status?: string,
    priority?: string,
    assignedTo?: string,
    referenceId?: string,
    referenceType?: string,
    sort?: string,
  ): Promise<{
    data: TaskListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(tasks.isDeleted, false),
      eq(tasks.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(tasks.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(tasks.createdAt, toDate));
      }
    }

    // Add status filtering
    if (status) {
      whereConditions.push(eq(tasks.status, status as any));
    }

    // Add priority filtering
    if (priority) {
      whereConditions.push(eq(tasks.priority, priority as any));
    }

    // Add assigned staff member filtering
    if (assignedTo) {
      whereConditions.push(eq(tasks.assignedTo, assignedTo));
    }

    // Add entity reference filtering
    if (referenceId) {
      whereConditions.push(eq(tasks.referenceId, referenceId));
    }

    if (referenceType) {
      whereConditions.push(
        eq(tasks.referenceType, referenceType as TaskReferenceType),
      );
    }

    // Determine sort order
    let orderBy: any;
    switch (sort) {
      case 'title_asc':
        orderBy = asc(tasks.title);
        break;
      case 'title_desc':
        orderBy = desc(tasks.title);
        break;
      case 'due_date_asc':
        orderBy = asc(tasks.dueDate);
        break;
      case 'due_date_desc':
        orderBy = desc(tasks.dueDate);
        break;
      case 'priority_asc':
        orderBy = asc(tasks.priority);
        break;
      case 'priority_desc':
        orderBy = desc(tasks.priority);
        break;
      case 'status_asc':
        orderBy = asc(tasks.status);
        break;
      case 'status_desc':
        orderBy = desc(tasks.status);
        break;
      case 'created_at_asc':
        orderBy = asc(tasks.createdAt);
        break;
      default:
        orderBy = desc(tasks.createdAt);
        break;
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(tasks)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get tasks with related data
    const tasksData = await this.db
      .select({
        id: tasks.id,
        title: tasks.title,
        description: tasks.description,
        dueDate: tasks.dueDate,
        priority: tasks.priority,
        status: tasks.status,
        assignedTo: tasks.assignedTo,
        referenceId: tasks.referenceId,
        referenceType: tasks.referenceType,
        recurringActivityId: tasks.recurringActivityId,
        parentTaskId: tasks.parentTaskId,
        // Related data
        assignedStaffMemberName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        referencedEntityName: sql<string>`
          CASE
            WHEN ${tasks.referenceType} = 'supplier' THEN ${suppliers.displayName}
            WHEN ${tasks.referenceType} = 'customer' THEN ${customers.customerDisplayName}
            WHEN ${tasks.referenceType} = 'project' THEN ${projects.name}
            WHEN ${tasks.referenceType} = 'transaction' THEN ${transactions.transactionNumber}
            WHEN ${tasks.referenceType} = 'service_order' THEN ${serviceOrders.orderNumber}
            ELSE NULL
          END
        `,
      })
      .from(tasks)
      .leftJoin(staffMembers, eq(tasks.assignedTo, staffMembers.id))
      .leftJoin(
        suppliers,
        and(
          eq(tasks.referenceId, suppliers.id),
          eq(tasks.referenceType, TaskReferenceType.SUPPLIER),
        ),
      )
      .leftJoin(
        customers,
        and(
          eq(tasks.referenceId, customers.id),
          eq(tasks.referenceType, TaskReferenceType.CUSTOMER),
        ),
      )
      .leftJoin(
        projects,
        and(
          eq(tasks.referenceId, projects.id),
          eq(tasks.referenceType, TaskReferenceType.PROJECT),
        ),
      )
      .leftJoin(
        transactions,
        and(
          eq(tasks.referenceId, transactions.id),
          eq(tasks.referenceType, TaskReferenceType.TRANSACTION),
        ),
      )
      .leftJoin(
        serviceOrders,
        and(
          eq(tasks.referenceId, serviceOrders.id),
          eq(tasks.referenceType, TaskReferenceType.SERVICE_ORDER),
        ),
      )
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get attachment counts for each task
    const taskIds = tasksData.map((task) => task.id);
    const attachmentCounts =
      taskIds.length > 0
        ? await this.db
            .select({
              taskId: taskAttachments.taskId,
              count: count(),
            })
            .from(taskAttachments)
            .where(inArray(taskAttachments.taskId, taskIds))
            .groupBy(taskAttachments.taskId)
        : [];

    const attachmentCountMap = new Map(
      attachmentCounts.map((item) => [item.taskId, item.count]),
    );

    const data: TaskListDto[] = tasksData.map((task) => ({
      id: task.id,
      title: task.title,
      description: task.description,
      dueDate: task.dueDate,
      priority: task.priority,
      status: task.status,
      assignedTo: task.assignedTo,
      assignedStaffMemberName: task.assignedStaffMemberName,
      referenceId: task.referenceId,
      referenceType: task.referenceType,
      referencedEntityName: task.referencedEntityName,
      recurringActivityId: task.recurringActivityId,
      parentTaskId: task.parentTaskId,
      attachmentsCount: attachmentCountMap.get(task.id) || 0,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<TaskSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const tasksData = await this.db
      .select({
        id: tasks.id,
        title: tasks.title,
        status: tasks.status,
        priority: tasks.priority,
        dueDate: tasks.dueDate,
        assignedStaffMemberName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
      })
      .from(tasks)
      .leftJoin(staffMembers, eq(tasks.assignedTo, staffMembers.id))
      .where(and(eq(tasks.businessId, businessId), eq(tasks.isDeleted, false)))
      .orderBy(desc(tasks.createdAt));

    return tasksData.map((task) => ({
      id: task.id,
      title: task.title,
      status: task.status,
      priority: task.priority,
      dueDate: task.dueDate,
      assignedStaffMemberName: task.assignedStaffMemberName,
    }));
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<TaskDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const taskData = await this.db
      .select({
        id: tasks.id,
        businessId: tasks.businessId,
        title: tasks.title,
        description: tasks.description,
        dueDate: tasks.dueDate,
        priority: tasks.priority,
        status: tasks.status,
        assignedTo: tasks.assignedTo,
        referenceId: tasks.referenceId,
        referenceType: tasks.referenceType,
        recurringActivityId: tasks.recurringActivityId,
        parentTaskId: tasks.parentTaskId,
        createdAt: tasks.createdAt,
        updatedAt: tasks.updatedAt,
        // Creator and updater info
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        updatedByName: sql<string>`CONCAT(u2.first_name, ' ', u2.last_name)`,
        // Assigned staff member info
        assignedStaffMemberFirstName: staffMembers.firstName,
        assignedStaffMemberLastName: staffMembers.lastName,
        assignedStaffMemberDisplayName: staffMembers.displayName,
        assignedStaffMemberEmail: staffMembers.email,
        // Related entity info
        referencedEntityName: sql<string>`
          CASE
            WHEN ${tasks.referenceType} = 'supplier' THEN ${suppliers.displayName}
            WHEN ${tasks.referenceType} = 'customer' THEN ${customers.customerDisplayName}
            WHEN ${tasks.referenceType} = 'project' THEN ${projects.name}
            WHEN ${tasks.referenceType} = 'transaction' THEN ${transactions.transactionNumber}
            WHEN ${tasks.referenceType} = 'service_order' THEN ${serviceOrders.orderNumber}
            ELSE NULL
          END
        `,
      })
      .from(tasks)
      .leftJoin(users, eq(tasks.createdBy, users.id))
      .leftJoin(users.as('u2'), eq(tasks.updatedBy, users.id))
      .leftJoin(staffMembers, eq(tasks.assignedTo, staffMembers.id))
      .leftJoin(
        suppliers,
        and(
          eq(tasks.referenceId, suppliers.id),
          eq(tasks.referenceType, TaskReferenceType.SUPPLIER),
        ),
      )
      .leftJoin(
        customers,
        and(
          eq(tasks.referenceId, customers.id),
          eq(tasks.referenceType, TaskReferenceType.CUSTOMER),
        ),
      )
      .leftJoin(
        projects,
        and(
          eq(tasks.referenceId, projects.id),
          eq(tasks.referenceType, TaskReferenceType.PROJECT),
        ),
      )
      .leftJoin(
        transactions,
        and(
          eq(tasks.referenceId, transactions.id),
          eq(tasks.referenceType, TaskReferenceType.TRANSACTION),
        ),
      )
      .leftJoin(
        serviceOrders,
        and(
          eq(tasks.referenceId, serviceOrders.id),
          eq(tasks.referenceType, TaskReferenceType.SERVICE_ORDER),
        ),
      )
      .where(
        and(
          eq(tasks.id, id),
          eq(tasks.businessId, businessId),
          eq(tasks.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!taskData) {
      throw new NotFoundException('Task not found');
    }

    // Get task attachments
    const attachmentsData = await this.db
      .select({
        id: media.id,
        fileName: media.fileName,
        publicUrl: media.publicUrl,
        mimeType: media.mimeType,
      })
      .from(taskAttachments)
      .innerJoin(media, eq(taskAttachments.attachmentId, media.id))
      .where(eq(taskAttachments.taskId, id));

    return {
      id: taskData.id,
      businessId: taskData.businessId,
      title: taskData.title,
      description: taskData.description,
      dueDate: taskData.dueDate,
      priority: taskData.priority,
      status: taskData.status,
      assignedTo: taskData.assignedTo,
      assignedStaffMember: taskData.assignedTo
        ? {
            id: taskData.assignedTo,
            firstName: taskData.assignedStaffMemberFirstName,
            lastName: taskData.assignedStaffMemberLastName,
            displayName: taskData.assignedStaffMemberDisplayName,
            email: taskData.assignedStaffMemberEmail,
          }
        : undefined,
      referenceId: taskData.referenceId,
      referenceType: taskData.referenceType,
      referencedEntity:
        taskData.referenceId && taskData.referencedEntityName
          ? {
              id: taskData.referenceId,
              name: taskData.referencedEntityName,
            }
          : undefined,
      recurringActivityId: taskData.recurringActivityId,
      parentTaskId: taskData.parentTaskId,
      attachments: attachmentsData,
      createdBy: taskData.createdByName,
      updatedBy: taskData.updatedByName,
      createdAt: taskData.createdAt,
      updatedAt: taskData.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateTaskDto: UpdateTaskDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if task exists and belongs to the business
    const existingTask = await this.db
      .select()
      .from(tasks)
      .where(
        and(
          eq(tasks.id, id),
          eq(tasks.businessId, businessId),
          isNull(tasks.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingTask) {
      throw new NotFoundException('Task not found');
    }

    // Validate assigned staff member if provided
    if (updateTaskDto.assignedTo) {
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, updateTaskDto.assignedTo),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!staffMember) {
        throw new BadRequestException(
          'Assigned staff member not found or does not belong to this business',
        );
      }
    }

    // Validate entity relationships if provided
    await this.validateEntityReferences(businessId, updateTaskDto);

    return await this.db.transaction(async (tx) => {
      // Update the task
      const [updatedTask] = await tx
        .update(tasks)
        .set({
          title: updateTaskDto.title,
          description: updateTaskDto.description,
          dueDate: updateTaskDto.dueDate,
          priority: updateTaskDto.priority,
          status: updateTaskDto.status,
          assignedTo: updateTaskDto.assignedTo,
          referenceId: updateTaskDto.referenceId,
          referenceType: updateTaskDto.referenceType,
          recurringActivityId: updateTaskDto.recurringActivityId,
          parentTaskId: updateTaskDto.parentTaskId,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(tasks.id, id))
        .returning();

      // Handle attachments if provided
      if (updateTaskDto.attachmentIds !== undefined) {
        // Remove existing attachments
        await tx.delete(taskAttachments).where(eq(taskAttachments.taskId, id));

        // Add new attachments if any
        if (updateTaskDto.attachmentIds.length > 0) {
          await this.handleTaskAttachments(
            tx,
            id,
            updateTaskDto.attachmentIds,
            businessId,
          );
        }
      }

      // Log activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.TASK,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: updatedTask.id };
    });
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateTaskDto: UpdateTaskDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateTaskDto, metadata);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if task exists and belongs to the business
    const existingTask = await this.db
      .select()
      .from(tasks)
      .where(
        and(
          eq(tasks.id, id),
          eq(tasks.businessId, businessId),
          isNull(tasks.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingTask) {
      throw new NotFoundException('Task not found');
    }

    // Soft delete the task
    await this.db
      .update(tasks)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
      })
      .where(eq(tasks.id, id));

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.TASK,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      message: 'Task deleted successfully',
      id,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    taskIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!taskIds || taskIds.length === 0) {
      throw new BadRequestException('No task IDs provided for deletion');
    }

    // Check which tasks exist and belong to the business
    const existingTasks = await this.db
      .select({ id: tasks.id, title: tasks.title })
      .from(tasks)
      .where(
        and(
          inArray(tasks.id, taskIds),
          eq(tasks.businessId, businessId),
          isNull(tasks.deletedAt),
        ),
      );

    if (existingTasks.length === 0) {
      throw new NotFoundException('No tasks found for deletion');
    }

    const existingTaskIds = existingTasks.map((task) => task.id);

    // Soft delete the tasks
    await this.db
      .update(tasks)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
      })
      .where(inArray(tasks.id, existingTaskIds));

    // Log bulk activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.TASK,
      existingTaskIds,
      { isDeleted: true, deletedBy: userId },
      userId,
      businessId,
      {
        filterCriteria: { taskCount: existingTaskIds.length },
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deletedCount: existingTaskIds.length,
      deletedIds: existingTaskIds,
    };
  }
}
