import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateLeaveTypeDto } from './dto/create-leave-type.dto';
import { UpdateLeaveTypeDto } from './dto/update-leave-type.dto';
import { LeaveTypeDto } from './dto/leave-type.dto';
import { LeaveTypeSlimDto } from './dto/leave-type-slim.dto';
import { leaveTypes } from '../drizzle/schema/leave-types.schema';
import { eq, and, ilike, sql, gte, lte, asc } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { UsersService } from '../users/users.service';

@Injectable()
export class LeaveTypesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createLeaveTypeDto: CreateLeaveTypeDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a leave type with the same code already exists for this business
      const existingLeaveType = await this.db
        .select()
        .from(leaveTypes)
        .where(
          and(
            eq(leaveTypes.businessId, businessId),
            ilike(leaveTypes.leaveCode, createLeaveTypeDto.leaveCode),
            eq(leaveTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingLeaveType) {
        throw new ConflictException(
          `A leave type with the code '${createLeaveTypeDto.leaveCode}' already exists for this business`,
        );
      }

      // Create the leave type
      const [newLeaveType] = await this.db
        .insert(leaveTypes)
        .values({
          businessId,
          leaveCode: createLeaveTypeDto.leaveCode,
          leaveName: createLeaveTypeDto.leaveName,
          daysAllowedPerYear: createLeaveTypeDto.daysAllowedPerYear,
          isPaid: createLeaveTypeDto.isPaid ?? true,
          carriesForward: createLeaveTypeDto.carriesForward ?? false,
          description: createLeaveTypeDto.description,
          createdBy: userId,
        })
        .returning();

      // Log the leave type creation activity
      await this.activityLogService.logCreate(
        newLeaveType.id,
        EntityType.LEAVE_TYPE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id: newLeaveType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create leave type: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: LeaveTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(leaveTypes.isDeleted, false),
      eq(leaveTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(leaveTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(leaveTypes.createdAt, toDate));
      }
    }

    // Find all leave types for the user's active business with pagination
    const result = await this.db
      .select()
      .from(leaveTypes)
      .where(and(...whereConditions))
      .orderBy(asc(leaveTypes.leaveName))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(leaveTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return {
      data: await Promise.all(
        result.map((leaveType) => this.mapToLeaveTypeDto(leaveType)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkLeaveCodeAvailability(
    userId: string,
    businessId: string | null,
    leaveCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a leave type with the same code already exists for this business
    const existingLeaveType = await this.db
      .select()
      .from(leaveTypes)
      .where(
        and(
          eq(leaveTypes.businessId, businessId),
          ilike(leaveTypes.leaveCode, leaveCode),
          eq(leaveTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingLeaveType };
  }

  async findOne(userId: string, id: string): Promise<LeaveTypeDto> {
    // Get the leave type
    const leaveType = await this.db
      .select()
      .from(leaveTypes)
      .where(and(eq(leaveTypes.id, id), eq(leaveTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!leaveType) {
      throw new NotFoundException(`Leave type with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== leaveType.businessId
    ) {
      throw new UnauthorizedException('Access denied to this leave type');
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return await this.mapToLeaveTypeDto(leaveType);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeaveTypeDto: UpdateLeaveTypeDto,
  ): Promise<
    { id: string } | { error: string; message: string; statusCode: number }
  > {
    // Get the leave type
    const existingLeaveType = await this.db
      .select()
      .from(leaveTypes)
      .where(and(eq(leaveTypes.id, id), eq(leaveTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveType) {
      throw new NotFoundException(`Leave type with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingLeaveType.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this leave type',
      );
    }

    // Check for leave code conflict if leave code is being updated
    if (
      updateLeaveTypeDto.leaveCode &&
      updateLeaveTypeDto.leaveCode !== existingLeaveType.leaveCode
    ) {
      const codeConflict = await this.db
        .select()
        .from(leaveTypes)
        .where(
          and(
            eq(leaveTypes.businessId, businessId),
            ilike(leaveTypes.leaveCode, updateLeaveTypeDto.leaveCode),
            eq(leaveTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (codeConflict) {
        throw new ConflictException(
          `A leave type with the code '${updateLeaveTypeDto.leaveCode}' already exists for this business`,
        );
      }
    }

    // Check if daysAllowedPerYear is being changed
    const isDaysAllowedChanged =
      updateLeaveTypeDto.daysAllowedPerYear &&
      updateLeaveTypeDto.daysAllowedPerYear !==
        existingLeaveType.daysAllowedPerYear;

    try {
      // Update the leave type
      const [updatedLeaveType] = await this.db
        .update(leaveTypes)
        .set({
          ...updateLeaveTypeDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(leaveTypes.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.LEAVE_TYPE,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      // If daysAllowedPerYear changed, recalculate all employee balances for current year
      if (isDaysAllowedChanged) {
        // await this.recalculateLeaveBalances(
        //   userId,
        //   businessId,
        //   id,
        //   updateLeaveTypeDto.daysAllowedPerYear,
        // );
      }

      return {
        id: updatedLeaveType.id,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update leave type: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    // Get the leave type
    const existingLeaveType = await this.db
      .select()
      .from(leaveTypes)
      .where(and(eq(leaveTypes.id, id), eq(leaveTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveType) {
      throw new NotFoundException(`Leave type with ID ${id} not found`);
    }

    if (businessId !== existingLeaveType.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this leave type',
      );
    }

    // Soft delete the leave type
    await this.db
      .update(leaveTypes)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(leaveTypes.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.LEAVE_TYPE,
      userId,
      businessId,
      {
        source: ActivitySource.WEB,
      },
    );

    return {
      success: true,
      message: `Leave type with ID ${id} has been deleted`,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<LeaveTypeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all leave types with only essential fields
    const leaveTypeResults = await this.db
      .select({
        id: leaveTypes.id,
        leaveCode: leaveTypes.leaveCode,
        leaveName: leaveTypes.leaveName,
        daysAllowedPerYear: leaveTypes.daysAllowedPerYear,
        isPaid: leaveTypes.isPaid,
        status: leaveTypes.status,
      })
      .from(leaveTypes)
      .where(
        and(
          eq(leaveTypes.isDeleted, false),
          eq(leaveTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(leaveTypes.leaveName));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return leaveTypeResults.map((leaveType) => ({
      id: leaveType.id.toString(),
      leaveCode: leaveType.leaveCode,
      leaveName: leaveType.leaveName,
      daysAllowedPerYear: leaveType.daysAllowedPerYear,
      isPaid: leaveType.isPaid,
      status: leaveType.status,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createLeaveTypeDto: CreateLeaveTypeDto,
  ): Promise<{ id: string }> {
    const leaveType = await this.create(userId, businessId, createLeaveTypeDto);
    return { id: leaveType.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeaveTypeDto: UpdateLeaveTypeDto,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateLeaveTypeDto);
    return { id };
  }

  private async mapToLeaveTypeDto(
    leaveType: typeof leaveTypes.$inferSelect,
  ): Promise<LeaveTypeDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      leaveType.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (leaveType.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        leaveType.updatedBy.toString(),
      );
    }

    const leaveTypeDto: LeaveTypeDto = {
      id: leaveType.id.toString(),
      businessId: leaveType.businessId.toString(),
      leaveCode: leaveType.leaveCode,
      leaveName: leaveType.leaveName,
      daysAllowedPerYear: leaveType.daysAllowedPerYear,
      isPaid: leaveType.isPaid,
      carriesForward: leaveType.carriesForward,
      description: leaveType.description,
      status: leaveType.status,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: leaveType.createdAt,
      updatedAt: leaveType.updatedAt,
    };

    return leaveTypeDto;
  }
}
