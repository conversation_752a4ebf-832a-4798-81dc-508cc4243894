import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateEventSpaceReservationDto } from './dto/create-event-space-reservation.dto';
import { UpdateEventSpaceReservationDto } from './dto/update-event-space-reservation.dto';
import { EventSpaceReservationDto } from './dto/event-space-reservation.dto';
import { EventSpaceReservationSlimDto } from './dto/event-space-reservation-slim.dto';
import { EventSpaceReservationListDto } from './dto/event-space-reservation-list.dto';
import {
  eventSpaceReservations,
  eventSpaceReservationAssets,
  eventSpaceReservationTasks,
  EventReservationStatus,
} from '../drizzle/schema/event-space-reservations.schema';
import { eventSpaces } from '../drizzle/schema/event-spaces.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { assets as assetsTable } from '../drizzle/schema/assets.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import {
  tasks,
  TaskStatus,
  TaskPriority,
  TaskReferenceType,
} from '../drizzle/schema/tasks.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ActivitySource,
} from '../shared/types/activity.enum';

@Injectable()
export class EventSpaceReservationsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createEventSpaceReservationDto: CreateEventSpaceReservationDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a reservation with the same number already exists for this business
      const existingReservation = await this.db
        .select()
        .from(eventSpaceReservations)
        .where(
          and(
            eq(eventSpaceReservations.businessId, businessId),
            ilike(
              eventSpaceReservations.reservationNumber,
              createEventSpaceReservationDto.reservationNumber,
            ),
            eq(eventSpaceReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingReservation) {
        throw new ConflictException(
          `A reservation with the number '${createEventSpaceReservationDto.reservationNumber}' already exists for this business`,
        );
      }

      // Check if reference number is unique (if provided)
      if (createEventSpaceReservationDto.referenceNumber) {
        const existingReferenceReservation = await this.db
          .select()
          .from(eventSpaceReservations)
          .where(
            and(
              eq(eventSpaceReservations.businessId, businessId),
              eq(
                eventSpaceReservations.referenceNumber,
                createEventSpaceReservationDto.referenceNumber,
              ),
              eq(eventSpaceReservations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingReferenceReservation) {
          throw new ConflictException(
            `A reservation with the reference number '${createEventSpaceReservationDto.referenceNumber}' already exists for this business`,
          );
        }
      }

      // Verify event space exists and belongs to the business
      const eventSpace = await this.db
        .select()
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.id, createEventSpaceReservationDto.eventSpaceId),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!eventSpace) {
        throw new NotFoundException('Event space not found');
      }

      // Verify customer exists (if provided)
      if (createEventSpaceReservationDto.customerId) {
        const customer = await this.db
          .select()
          .from(customers)
          .where(
            and(
              eq(customers.id, createEventSpaceReservationDto.customerId),
              eq(customers.businessId, businessId),
              eq(customers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!customer) {
          throw new NotFoundException('Customer not found');
        }
      }

      // Check for overlapping reservations
      await this.checkForOverlappingReservations(
        businessId,
        createEventSpaceReservationDto.eventSpaceId,
        new Date(createEventSpaceReservationDto.setupStartTime),
        new Date(createEventSpaceReservationDto.breakdownEndTime),
      );

      const result = await this.db.transaction(async (tx) => {
        // Create the reservation
        const [reservation] = await tx
          .insert(eventSpaceReservations)
          .values({
            businessId,
            reservationNumber: createEventSpaceReservationDto.reservationNumber,
            referenceNumber: createEventSpaceReservationDto.referenceNumber,
            eventSpaceId: createEventSpaceReservationDto.eventSpaceId,
            customerId: createEventSpaceReservationDto.customerId,
            eventName: createEventSpaceReservationDto.eventName,
            eventType: createEventSpaceReservationDto.eventType,
            eventDescription: createEventSpaceReservationDto.eventDescription,
            eventStartTime: new Date(
              createEventSpaceReservationDto.eventStartTime,
            ),
            eventEndTime: new Date(createEventSpaceReservationDto.eventEndTime),
            setupStartTime: new Date(
              createEventSpaceReservationDto.setupStartTime,
            ),
            breakdownEndTime: new Date(
              createEventSpaceReservationDto.breakdownEndTime,
            ),
            actualSetupStartTime:
              createEventSpaceReservationDto.actualSetupStartTime
                ? new Date(createEventSpaceReservationDto.actualSetupStartTime)
                : undefined,
            actualEventStartTime:
              createEventSpaceReservationDto.actualEventStartTime
                ? new Date(createEventSpaceReservationDto.actualEventStartTime)
                : undefined,
            actualEventEndTime:
              createEventSpaceReservationDto.actualEventEndTime
                ? new Date(createEventSpaceReservationDto.actualEventEndTime)
                : undefined,
            actualBreakdownEndTime:
              createEventSpaceReservationDto.actualBreakdownEndTime
                ? new Date(
                    createEventSpaceReservationDto.actualBreakdownEndTime,
                  )
                : undefined,
            expectedGuestCount:
              createEventSpaceReservationDto.expectedGuestCount,
            confirmedGuestCount:
              createEventSpaceReservationDto.confirmedGuestCount || 0,
            requiresIndividualGuestTracking:
              createEventSpaceReservationDto.requiresIndividualGuestTracking ||
              false,
            guestListDeadline: createEventSpaceReservationDto.guestListDeadline
              ? new Date(createEventSpaceReservationDto.guestListDeadline)
              : undefined,
            allowWalkIns: createEventSpaceReservationDto.allowWalkIns || false,
            status:
              createEventSpaceReservationDto.status ||
              EventReservationStatus.INQUIRY,
            reservationSource: createEventSpaceReservationDto.reservationSource,
            paymentStatus: createEventSpaceReservationDto.paymentStatus,
            subtotal: createEventSpaceReservationDto.subtotal || '0.00',
            total: createEventSpaceReservationDto.total || '0.00',
            depositRequired:
              createEventSpaceReservationDto.depositRequired || '0.00',
            depositPaid: createEventSpaceReservationDto.depositPaid || '0.00',
            balanceDue: createEventSpaceReservationDto.balanceDue || '0.00',
            discountType: createEventSpaceReservationDto.discountType,
            discountValue:
              createEventSpaceReservationDto.discountValue || '0.00',
            discountAmount:
              createEventSpaceReservationDto.discountAmount || '0.00',
            discountReason: createEventSpaceReservationDto.discountReason,
            taxType: createEventSpaceReservationDto.taxType,
            defaultTaxRateId: createEventSpaceReservationDto.defaultTaxRateId,
            taxAmount: createEventSpaceReservationDto.taxAmount || '0.00',
            notes: createEventSpaceReservationDto.notes,
            cancellationReason:
              createEventSpaceReservationDto.cancellationReason,
            cancellationDate: createEventSpaceReservationDto.cancellationDate
              ? new Date(createEventSpaceReservationDto.cancellationDate)
              : undefined,
            confirmationSent:
              createEventSpaceReservationDto.confirmationSent || false,
            confirmationSentAt:
              createEventSpaceReservationDto.confirmationSentAt
                ? new Date(createEventSpaceReservationDto.confirmationSentAt)
                : undefined,
            reminderSent: createEventSpaceReservationDto.reminderSent || false,
            reminderSentAt: createEventSpaceReservationDto.reminderSentAt
              ? new Date(createEventSpaceReservationDto.reminderSentAt)
              : undefined,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: eventSpaceReservations.id });

        // Handle asset assignments
        if (
          createEventSpaceReservationDto.assetIds &&
          createEventSpaceReservationDto.assetIds.length > 0
        ) {
          await this.assignAssetsToReservation(
            tx,
            reservation.id,
            createEventSpaceReservationDto.assetIds,
            userId,
            businessId,
          );
        }

        // Handle task creation
        if (
          createEventSpaceReservationDto.tasks &&
          createEventSpaceReservationDto.tasks.length > 0
        ) {
          await this.createTasksForReservation(
            tx,
            reservation.id,
            createEventSpaceReservationDto.tasks,
            userId,
            businessId,
          );
        }

        return reservation;
      });

      // Log the event space reservation creation activity
      await this.activityLogService.logCreate(
        result.id,
        EntityType.RESERVATION,
        userId,
        businessId,
        {
          source: ActivitySource.SYSTEM,
        },
      );

      return { id: result.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create event space reservation');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createEventSpaceReservationDto: CreateEventSpaceReservationDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createEventSpaceReservationDto,
    );
    return {
      id: result.id,
      message: 'Event space reservation created successfully',
    };
  }

  private async checkForOverlappingReservations(
    businessId: string,
    eventSpaceId: string,
    setupStartTime: Date,
    breakdownEndTime: Date,
    excludeReservationId?: string,
  ): Promise<void> {
    const conditions = [
      eq(eventSpaceReservations.businessId, businessId),
      eq(eventSpaceReservations.eventSpaceId, eventSpaceId),
      eq(eventSpaceReservations.isDeleted, false),
      // Check for time overlap
      or(
        // New reservation starts during existing reservation
        and(
          lte(eventSpaceReservations.setupStartTime, setupStartTime),
          gte(eventSpaceReservations.breakdownEndTime, setupStartTime),
        ),
        // New reservation ends during existing reservation
        and(
          lte(eventSpaceReservations.setupStartTime, breakdownEndTime),
          gte(eventSpaceReservations.breakdownEndTime, breakdownEndTime),
        ),
        // New reservation completely contains existing reservation
        and(
          gte(eventSpaceReservations.setupStartTime, setupStartTime),
          lte(eventSpaceReservations.breakdownEndTime, breakdownEndTime),
        ),
      ),
      // Exclude cancelled and blocked reservations from overlap check
      or(
        eq(eventSpaceReservations.status, EventReservationStatus.CONFIRMED),
        eq(eventSpaceReservations.status, EventReservationStatus.TENTATIVE),
        eq(
          eventSpaceReservations.status,
          EventReservationStatus.SETUP_IN_PROGRESS,
        ),
        eq(
          eventSpaceReservations.status,
          EventReservationStatus.EVENT_IN_PROGRESS,
        ),
        eq(
          eventSpaceReservations.status,
          EventReservationStatus.BREAKDOWN_IN_PROGRESS,
        ),
      ),
    ];

    if (excludeReservationId) {
      conditions.push(
        sql`${eventSpaceReservations.id} != ${excludeReservationId}`,
      );
    }

    const overlapping = await this.db
      .select({ id: eventSpaceReservations.id })
      .from(eventSpaceReservations)
      .where(and(...conditions))
      .then((results) => results[0]);

    if (overlapping) {
      throw new ConflictException(
        'The selected time slot overlaps with an existing reservation for this event space',
      );
    }
  }

  private async assignAssetsToReservation(
    tx: any,
    reservationId: string,
    assetIds: string[],
    userId: string,
    businessId: string,
  ): Promise<void> {
    // Verify all assets exist and belong to the business
    const assets = await tx
      .select({ id: assetsTable.id })
      .from(assetsTable)
      .where(
        and(
          inArray(assetsTable.id, assetIds),
          eq(assetsTable.businessId, businessId),
          eq(assetsTable.isDeleted, false),
        ),
      );

    if (assets.length !== assetIds.length) {
      throw new NotFoundException('One or more assets not found');
    }

    // Create asset assignments
    const assetAssignments = assetIds.map((assetId) => ({
      reservationId,
      assetId,
      assignedById: userId,
      createdBy: userId,
      updatedBy: userId,
    }));

    await tx.insert(eventSpaceReservationAssets).values(assetAssignments);
  }

  private async createTasksForReservation(
    tx: any,
    reservationId: string,
    taskData: Array<{
      // For creating new tasks
      title?: string;
      description?: string;
      dueDate?: string;
      priority?: TaskPriority;
      status?: TaskStatus;
      assignedTo?: string;
      // For linking existing tasks
      taskId?: string;
      staffMemberId?: string;
    }>,
    userId: string,
    businessId: string,
  ): Promise<void> {
    // Separate new tasks from existing task assignments
    const newTasks = taskData.filter((task) => task.title && !task.taskId);
    const existingTaskAssignments = taskData.filter(
      (task) => task.taskId && task.staffMemberId,
    );

    // Verify all assigned staff members exist and belong to the business
    const allStaffIds = [
      ...newTasks
        .filter((task) => task.assignedTo)
        .map((task) => task.assignedTo),
      ...existingTaskAssignments.map((task) => task.staffMemberId),
    ];

    if (allStaffIds.length > 0) {
      const staffMembersResult = await tx
        .select({ id: staffMembers.id })
        .from(staffMembers)
        .where(
          and(
            inArray(staffMembers.id, allStaffIds),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        );

      if (staffMembersResult.length !== allStaffIds.length) {
        throw new NotFoundException(
          'One or more assigned staff members not found',
        );
      }
    }

    // Verify existing tasks exist and belong to the business
    if (existingTaskAssignments.length > 0) {
      const existingTaskIds = existingTaskAssignments.map(
        (task) => task.taskId,
      );
      const existingTasksResult = await tx
        .select({ id: tasks.id })
        .from(tasks)
        .where(
          and(
            inArray(tasks.id, existingTaskIds),
            eq(tasks.businessId, businessId),
            eq(tasks.isDeleted, false),
          ),
        );

      if (existingTasksResult.length !== existingTaskIds.length) {
        throw new NotFoundException('One or more tasks not found');
      }
    }

    // Create new tasks
    for (const taskInfo of newTasks) {
      const [createdTask] = await tx
        .insert(tasks)
        .values({
          businessId,
          title: taskInfo.title,
          description: taskInfo.description,
          dueDate: taskInfo.dueDate ? new Date(taskInfo.dueDate) : undefined,
          priority: taskInfo.priority || TaskPriority.MEDIUM,
          status: taskInfo.status || TaskStatus.PENDING,
          assignedTo: taskInfo.assignedTo,
          referenceId: reservationId,
          referenceType: TaskReferenceType.EVENT_SPACE_RESERVATION,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: tasks.id });

      // Create task assignment in the junction table
      await tx.insert(eventSpaceReservationTasks).values({
        reservationId,
        staffMemberId: taskInfo.assignedTo || userId, // Default to creator if no assignee
        taskId: createdTask.id,
        createdBy: userId,
        updatedBy: userId,
      });
    }

    // Link existing tasks to staff members
    for (const taskAssignment of existingTaskAssignments) {
      await tx.insert(eventSpaceReservationTasks).values({
        reservationId,
        staffMemberId: taskAssignment.staffMemberId,
        taskId: taskAssignment.taskId,
        createdBy: userId,
        updatedBy: userId,
      });
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    reservations: CreateEventSpaceReservationDto[],
  ): Promise<{ ids: string[]; created: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!reservations || reservations.length === 0) {
      throw new BadRequestException('No reservations provided');
    }

    // Check for duplicate reservation numbers within the batch
    const reservationNumbers = reservations.map((r) => r.reservationNumber);
    const uniqueNumbers = new Set(reservationNumbers);
    if (uniqueNumbers.size !== reservationNumbers.length) {
      throw new ConflictException(
        'Duplicate reservation numbers found in the batch',
      );
    }

    // Check for existing reservation numbers
    const existingReservations = await this.db
      .select({ reservationNumber: eventSpaceReservations.reservationNumber })
      .from(eventSpaceReservations)
      .where(
        and(
          eq(eventSpaceReservations.businessId, businessId),
          inArray(eventSpaceReservations.reservationNumber, reservationNumbers),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      );

    if (existingReservations.length > 0) {
      const existingNumbers = existingReservations.map(
        (r) => r.reservationNumber,
      );
      throw new ConflictException(
        `The following reservation numbers already exist: ${existingNumbers.join(', ')}`,
      );
    }

    const result = await this.db.transaction(async (tx) => {
      const createdIds: string[] = [];

      for (const reservationDto of reservations) {
        // Verify event space exists
        const eventSpace = await tx
          .select()
          .from(eventSpaces)
          .where(
            and(
              eq(eventSpaces.id, reservationDto.eventSpaceId),
              eq(eventSpaces.businessId, businessId),
              eq(eventSpaces.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!eventSpace) {
          throw new NotFoundException(
            `Event space not found: ${reservationDto.eventSpaceId}`,
          );
        }

        // Check for overlapping reservations
        await this.checkForOverlappingReservations(
          businessId,
          reservationDto.eventSpaceId,
          new Date(reservationDto.setupStartTime),
          new Date(reservationDto.breakdownEndTime),
        );

        // Create the reservation
        const [reservation] = await tx
          .insert(eventSpaceReservations)
          .values({
            businessId,
            reservationNumber: reservationDto.reservationNumber,
            referenceNumber: reservationDto.referenceNumber,
            eventSpaceId: reservationDto.eventSpaceId,
            customerId: reservationDto.customerId,
            eventName: reservationDto.eventName,
            eventType: reservationDto.eventType,
            eventDescription: reservationDto.eventDescription,
            eventStartTime: new Date(reservationDto.eventStartTime),
            eventEndTime: new Date(reservationDto.eventEndTime),
            setupStartTime: new Date(reservationDto.setupStartTime),
            breakdownEndTime: new Date(reservationDto.breakdownEndTime),
            expectedGuestCount: reservationDto.expectedGuestCount,
            confirmedGuestCount: reservationDto.confirmedGuestCount || 0,
            requiresIndividualGuestTracking:
              reservationDto.requiresIndividualGuestTracking || false,
            guestListDeadline: reservationDto.guestListDeadline
              ? new Date(reservationDto.guestListDeadline)
              : undefined,
            allowWalkIns: reservationDto.allowWalkIns || false,
            status: reservationDto.status || EventReservationStatus.INQUIRY,
            reservationSource: reservationDto.reservationSource,
            paymentStatus: reservationDto.paymentStatus,
            subtotal: reservationDto.subtotal || '0.00',
            total: reservationDto.total || '0.00',
            depositRequired: reservationDto.depositRequired || '0.00',
            depositPaid: reservationDto.depositPaid || '0.00',
            balanceDue: reservationDto.balanceDue || '0.00',
            discountType: reservationDto.discountType,
            discountValue: reservationDto.discountValue || '0.00',
            discountAmount: reservationDto.discountAmount || '0.00',
            discountReason: reservationDto.discountReason,
            taxType: reservationDto.taxType,
            defaultTaxRateId: reservationDto.defaultTaxRateId,
            taxAmount: reservationDto.taxAmount || '0.00',
            notes: reservationDto.notes,
            confirmationSent: reservationDto.confirmationSent || false,
            reminderSent: reservationDto.reminderSent || false,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: eventSpaceReservations.id });

        createdIds.push(reservation.id);

        // Handle asset assignments
        if (reservationDto.assetIds && reservationDto.assetIds.length > 0) {
          await this.assignAssetsToReservation(
            tx,
            reservation.id,
            reservationDto.assetIds,
            userId,
            businessId,
          );
        }

        // Handle task creation
        if (reservationDto.tasks && reservationDto.tasks.length > 0) {
          await this.createTasksForReservation(
            tx,
            reservation.id,
            reservationDto.tasks,
            userId,
            businessId,
          );
        }
      }

      return { ids: createdIds, created: createdIds.length };
    });

    return result;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    reservations: CreateEventSpaceReservationDto[],
  ): Promise<{ ids: string[]; created: number; message: string }> {
    const result = await this.bulkCreate(userId, businessId, reservations);
    return {
      ...result,
      message: `Successfully created ${result.created} event space reservations`,
    };
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<EventSpaceReservationSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const reservations = await this.db
      .select({
        id: eventSpaceReservations.id,
        reservationNumber: eventSpaceReservations.reservationNumber,
        referenceNumber: eventSpaceReservations.referenceNumber,
        eventSpaceName: eventSpaces.name,
        customerName: customers.customerDisplayName,
        eventName: eventSpaceReservations.eventName,
        eventType: eventSpaceReservations.eventType,
        eventStartTime: eventSpaceReservations.eventStartTime,
        eventEndTime: eventSpaceReservations.eventEndTime,
        expectedGuestCount: eventSpaceReservations.expectedGuestCount,
        status: eventSpaceReservations.status,
        paymentStatus: eventSpaceReservations.paymentStatus,
        total: eventSpaceReservations.total,
        createdAt: eventSpaceReservations.createdAt,
      })
      .from(eventSpaceReservations)
      .leftJoin(
        eventSpaces,
        eq(eventSpaceReservations.eventSpaceId, eventSpaces.id),
      )
      .leftJoin(customers, eq(eventSpaceReservations.customerId, customers.id))
      .where(
        and(
          eq(eventSpaceReservations.businessId, businessId),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      )
      .orderBy(desc(eventSpaceReservations.createdAt));

    return reservations as EventSpaceReservationSlimDto[];
  }

  async findAllOptimized(
    businessId: string | null,
    page?: number,
    limit?: number,
    from?: string,
    to?: string,
    reservationNumber?: string,
    eventName?: string,
    eventType?: string,
    status?: string,
    paymentStatus?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: EventSpaceReservationListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const pageNumber = page || 1;
    const pageSize = limit || 10;
    const offset = (pageNumber - 1) * pageSize;

    // Build conditions
    const conditions = [
      eq(eventSpaceReservations.businessId, businessId),
      eq(eventSpaceReservations.isDeleted, false),
    ];

    // Apply additional filters
    if (from) {
      conditions.push(
        gte(eventSpaceReservations.eventStartTime, new Date(from)),
      );
    }

    if (to) {
      conditions.push(lte(eventSpaceReservations.eventEndTime, new Date(to)));
    }

    if (reservationNumber) {
      conditions.push(
        ilike(
          eventSpaceReservations.reservationNumber,
          `%${reservationNumber}%`,
        ),
      );
    }

    if (eventName) {
      conditions.push(
        ilike(eventSpaceReservations.eventName, `%${eventName}%`),
      );
    }

    if (eventType) {
      const eventTypes = eventType.split(',').map((s) => s.trim()) as any[];
      conditions.push(inArray(eventSpaceReservations.eventType, eventTypes));
    }

    if (status) {
      const statuses = status.split(',').map((s) => s.trim()) as any[];
      conditions.push(inArray(eventSpaceReservations.status, statuses));
    }

    if (paymentStatus) {
      const paymentStatuses = paymentStatus
        .split(',')
        .map((s) => s.trim()) as any[];
      conditions.push(
        inArray(eventSpaceReservations.paymentStatus, paymentStatuses),
      );
    }

    // Apply advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = this.buildAdvancedFilters(parsedFilters);
        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            conditions.push(or(...filterConditions));
          } else {
            conditions.push(...filterConditions);
          }
        }
      } catch {
        // Ignore invalid JSON filters
      }
    }

    // Build order by
    let orderByClause = [desc(eventSpaceReservations.createdAt)];
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        const orderBy = this.buildOrderBy(sortConfig);
        if (orderBy.length > 0) {
          orderByClause = orderBy;
        }
      } catch {
        // Use default sorting if sort config is invalid
      }
    }

    // Execute query with pagination
    const query = this.db
      .select({
        id: eventSpaceReservations.id,
        reservationNumber: eventSpaceReservations.reservationNumber,
        referenceNumber: eventSpaceReservations.referenceNumber,
        eventSpaceName: eventSpaces.name,
        customerName: customers.customerDisplayName,
        eventName: eventSpaceReservations.eventName,
        eventType: eventSpaceReservations.eventType,
        eventStartTime: eventSpaceReservations.eventStartTime,
        eventEndTime: eventSpaceReservations.eventEndTime,
        expectedGuestCount: eventSpaceReservations.expectedGuestCount,
        status: eventSpaceReservations.status,
        paymentStatus: eventSpaceReservations.paymentStatus,
        total: eventSpaceReservations.total,
        createdBy: sql<string>`COALESCE(${users.name}, 'Unknown')`,
        updatedBy: sql<string>`COALESCE(updated_user.name, NULL)`,
        createdAt: eventSpaceReservations.createdAt,
        updatedAt: eventSpaceReservations.updatedAt,
        assetsCount: sql<number>`0`,
        tasksCount: sql<number>`0`,
      })
      .from(eventSpaceReservations)
      .leftJoin(
        eventSpaces,
        eq(eventSpaceReservations.eventSpaceId, eventSpaces.id),
      )
      .leftJoin(customers, eq(eventSpaceReservations.customerId, customers.id))
      .leftJoin(users, eq(eventSpaceReservations.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(eventSpaceReservations.updatedBy, sql`updated_user.id`),
      )
      .where(and(...conditions))
      .orderBy(...orderByClause)
      .limit(pageSize)
      .offset(offset);

    // Get total count
    const totalQuery = this.db
      .select({ count: sql<number>`count(*)` })
      .from(eventSpaceReservations)
      .where(and(...conditions));

    const [data, totalResult] = await Promise.all([query, totalQuery]);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / pageSize);

    return {
      data: data as EventSpaceReservationListDto[],
      total,
      page: pageNumber,
      limit: pageSize,
      totalPages,
      hasNextPage: pageNumber < totalPages,
      hasPrevPage: pageNumber > 1,
    };
  }

  async findOne(userId: string, id: string): Promise<EventSpaceReservationDto> {
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });
    if (!user || !user.activeBusinessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const reservation = await this.db
      .select({
        id: eventSpaceReservations.id,
        businessId: eventSpaceReservations.businessId,
        reservationNumber: eventSpaceReservations.reservationNumber,
        referenceNumber: eventSpaceReservations.referenceNumber,
        eventSpaceId: eventSpaceReservations.eventSpaceId,
        eventSpaceName: eventSpaces.name,
        customerId: eventSpaceReservations.customerId,
        customerName: customers.customerDisplayName,
        eventName: eventSpaceReservations.eventName,
        eventType: eventSpaceReservations.eventType,
        eventDescription: eventSpaceReservations.eventDescription,
        eventStartTime: eventSpaceReservations.eventStartTime,
        eventEndTime: eventSpaceReservations.eventEndTime,
        setupStartTime: eventSpaceReservations.setupStartTime,
        breakdownEndTime: eventSpaceReservations.breakdownEndTime,
        actualSetupStartTime: eventSpaceReservations.actualSetupStartTime,
        actualEventStartTime: eventSpaceReservations.actualEventStartTime,
        actualEventEndTime: eventSpaceReservations.actualEventEndTime,
        actualBreakdownEndTime: eventSpaceReservations.actualBreakdownEndTime,
        expectedGuestCount: eventSpaceReservations.expectedGuestCount,
        confirmedGuestCount: eventSpaceReservations.confirmedGuestCount,
        requiresIndividualGuestTracking:
          eventSpaceReservations.requiresIndividualGuestTracking,
        guestListDeadline: eventSpaceReservations.guestListDeadline,
        allowWalkIns: eventSpaceReservations.allowWalkIns,
        status: eventSpaceReservations.status,
        reservationSource: eventSpaceReservations.reservationSource,
        paymentStatus: eventSpaceReservations.paymentStatus,
        subtotal: eventSpaceReservations.subtotal,
        total: eventSpaceReservations.total,
        depositRequired: eventSpaceReservations.depositRequired,
        depositPaid: eventSpaceReservations.depositPaid,
        balanceDue: eventSpaceReservations.balanceDue,
        discountType: eventSpaceReservations.discountType,
        discountValue: eventSpaceReservations.discountValue,
        discountAmount: eventSpaceReservations.discountAmount,
        discountReason: eventSpaceReservations.discountReason,
        taxType: eventSpaceReservations.taxType,
        defaultTaxRateId: eventSpaceReservations.defaultTaxRateId,
        taxAmount: eventSpaceReservations.taxAmount,
        notes: eventSpaceReservations.notes,
        cancellationReason: eventSpaceReservations.cancellationReason,
        cancellationDate: eventSpaceReservations.cancellationDate,
        confirmationSent: eventSpaceReservations.confirmationSent,
        confirmationSentAt: eventSpaceReservations.confirmationSentAt,
        reminderSent: eventSpaceReservations.reminderSent,
        reminderSentAt: eventSpaceReservations.reminderSentAt,
        createdBy: sql<string>`COALESCE(${users.name}, 'Unknown')`,
        updatedBy: sql<string>`COALESCE(updated_user.name, NULL)`,
        createdAt: eventSpaceReservations.createdAt,
        updatedAt: eventSpaceReservations.updatedAt,
      })
      .from(eventSpaceReservations)
      .leftJoin(
        eventSpaces,
        eq(eventSpaceReservations.eventSpaceId, eventSpaces.id),
      )
      .leftJoin(customers, eq(eventSpaceReservations.customerId, customers.id))
      .leftJoin(users, eq(eventSpaceReservations.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(eventSpaceReservations.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(eventSpaceReservations.id, id),
          eq(eventSpaceReservations.businessId, user.activeBusinessId),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!reservation) {
      throw new NotFoundException('Event space reservation not found');
    }

    // Get asset assignments
    const assets = await this.db
      .select({
        id: eventSpaceReservationAssets.id,
        assetId: eventSpaceReservationAssets.assetId,
        assetName: assetsTable.name,
        notes: eventSpaceReservationAssets.notes,
        assignedById: eventSpaceReservationAssets.assignedById,
        assignedByName: sql<string>`COALESCE(${staffMembers.displayName}, 'Unknown')`,
        assignedAt: eventSpaceReservationAssets.assignedAt,
        returnedAt: eventSpaceReservationAssets.returnedAt,
        conditionOnAssignment:
          eventSpaceReservationAssets.conditionOnAssignment,
        conditionOnReturn: eventSpaceReservationAssets.conditionOnReturn,
      })
      .from(eventSpaceReservationAssets)
      .leftJoin(
        assetsTable,
        eq(eventSpaceReservationAssets.assetId, assetsTable.id),
      )
      .leftJoin(
        staffMembers,
        eq(eventSpaceReservationAssets.assignedById, staffMembers.id),
      )
      .where(
        and(
          eq(eventSpaceReservationAssets.reservationId, id),
          eq(eventSpaceReservationAssets.isDeleted, false),
        ),
      );

    // Get task assignments with full task details
    const taskAssignments = await this.db
      .select({
        id: eventSpaceReservationTasks.id,
        staffMemberId: eventSpaceReservationTasks.staffMemberId,
        staffMemberName: sql<string>`COALESCE(${staffMembers.displayName}, 'Unknown')`,
        taskId: eventSpaceReservationTasks.taskId,
        taskTitle: tasks.title,
        taskDescription: tasks.description,
        taskDueDate: tasks.dueDate,
        taskPriority: tasks.priority,
        taskStatus: tasks.status,
        taskAssignedTo: tasks.assignedTo,
        taskAssignedToName: sql<string>`COALESCE(assigned_staff.display_name, NULL)`,
      })
      .from(eventSpaceReservationTasks)
      .leftJoin(
        staffMembers,
        eq(eventSpaceReservationTasks.staffMemberId, staffMembers.id),
      )
      .leftJoin(tasks, eq(eventSpaceReservationTasks.taskId, tasks.id))
      .leftJoin(
        sql`${staffMembers} AS assigned_staff`,
        eq(tasks.assignedTo, sql`assigned_staff.id`),
      )
      .where(
        and(
          eq(eventSpaceReservationTasks.reservationId, id),
          eq(eventSpaceReservationTasks.isDeleted, false),
          eq(tasks.isDeleted, false),
        ),
      );

    return {
      ...reservation,
      assets: assets as any[],
      tasks: taskAssignments as any[],
    } as EventSpaceReservationDto;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateEventSpaceReservationDto: UpdateEventSpaceReservationDto,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(eventSpaceReservations)
      .where(
        and(
          eq(eventSpaceReservations.id, id),
          eq(eventSpaceReservations.businessId, businessId),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException('Event space reservation not found');
    }

    // Check for reservation number conflicts (if being updated)
    if (
      updateEventSpaceReservationDto.reservationNumber &&
      updateEventSpaceReservationDto.reservationNumber !==
        existingReservation.reservationNumber
    ) {
      const conflictingReservation = await this.db
        .select()
        .from(eventSpaceReservations)
        .where(
          and(
            eq(eventSpaceReservations.businessId, businessId),
            ilike(
              eventSpaceReservations.reservationNumber,
              updateEventSpaceReservationDto.reservationNumber,
            ),
            sql`${eventSpaceReservations.id} != ${id}`,
            eq(eventSpaceReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (conflictingReservation) {
        throw new ConflictException(
          `A reservation with the number '${updateEventSpaceReservationDto.reservationNumber}' already exists`,
        );
      }
    }

    // Check for overlapping reservations (if time is being updated)
    if (
      updateEventSpaceReservationDto.setupStartTime ||
      updateEventSpaceReservationDto.breakdownEndTime
    ) {
      const setupStartTime = updateEventSpaceReservationDto.setupStartTime
        ? new Date(updateEventSpaceReservationDto.setupStartTime)
        : existingReservation.setupStartTime;
      const breakdownEndTime = updateEventSpaceReservationDto.breakdownEndTime
        ? new Date(updateEventSpaceReservationDto.breakdownEndTime)
        : existingReservation.breakdownEndTime;
      const eventSpaceId =
        updateEventSpaceReservationDto.eventSpaceId ||
        existingReservation.eventSpaceId;

      await this.checkForOverlappingReservations(
        businessId,
        eventSpaceId,
        setupStartTime,
        breakdownEndTime,
        id, // Exclude current reservation from overlap check
      );
    }

    const result = await this.db.transaction(async (tx) => {
      // Update the reservation
      const [updatedReservation] = await tx
        .update(eventSpaceReservations)
        .set({
          reservationNumber: updateEventSpaceReservationDto.reservationNumber,
          referenceNumber: updateEventSpaceReservationDto.referenceNumber,
          eventSpaceId: updateEventSpaceReservationDto.eventSpaceId,
          customerId: updateEventSpaceReservationDto.customerId,
          eventName: updateEventSpaceReservationDto.eventName,
          eventType: updateEventSpaceReservationDto.eventType,
          eventDescription: updateEventSpaceReservationDto.eventDescription,
          eventStartTime: updateEventSpaceReservationDto.eventStartTime
            ? new Date(updateEventSpaceReservationDto.eventStartTime)
            : undefined,
          eventEndTime: updateEventSpaceReservationDto.eventEndTime
            ? new Date(updateEventSpaceReservationDto.eventEndTime)
            : undefined,
          setupStartTime: updateEventSpaceReservationDto.setupStartTime
            ? new Date(updateEventSpaceReservationDto.setupStartTime)
            : undefined,
          breakdownEndTime: updateEventSpaceReservationDto.breakdownEndTime
            ? new Date(updateEventSpaceReservationDto.breakdownEndTime)
            : undefined,
          actualSetupStartTime:
            updateEventSpaceReservationDto.actualSetupStartTime
              ? new Date(updateEventSpaceReservationDto.actualSetupStartTime)
              : undefined,
          actualEventStartTime:
            updateEventSpaceReservationDto.actualEventStartTime
              ? new Date(updateEventSpaceReservationDto.actualEventStartTime)
              : undefined,
          actualEventEndTime: updateEventSpaceReservationDto.actualEventEndTime
            ? new Date(updateEventSpaceReservationDto.actualEventEndTime)
            : undefined,
          actualBreakdownEndTime:
            updateEventSpaceReservationDto.actualBreakdownEndTime
              ? new Date(updateEventSpaceReservationDto.actualBreakdownEndTime)
              : undefined,
          expectedGuestCount: updateEventSpaceReservationDto.expectedGuestCount,
          confirmedGuestCount:
            updateEventSpaceReservationDto.confirmedGuestCount,
          requiresIndividualGuestTracking:
            updateEventSpaceReservationDto.requiresIndividualGuestTracking,
          guestListDeadline: updateEventSpaceReservationDto.guestListDeadline
            ? new Date(updateEventSpaceReservationDto.guestListDeadline)
            : undefined,
          allowWalkIns: updateEventSpaceReservationDto.allowWalkIns,
          status: updateEventSpaceReservationDto.status,
          reservationSource: updateEventSpaceReservationDto.reservationSource,
          paymentStatus: updateEventSpaceReservationDto.paymentStatus,
          subtotal: updateEventSpaceReservationDto.subtotal,
          total: updateEventSpaceReservationDto.total,
          depositRequired: updateEventSpaceReservationDto.depositRequired,
          depositPaid: updateEventSpaceReservationDto.depositPaid,
          balanceDue: updateEventSpaceReservationDto.balanceDue,
          discountType: updateEventSpaceReservationDto.discountType,
          discountValue: updateEventSpaceReservationDto.discountValue,
          discountAmount: updateEventSpaceReservationDto.discountAmount,
          discountReason: updateEventSpaceReservationDto.discountReason,
          taxType: updateEventSpaceReservationDto.taxType,
          defaultTaxRateId: updateEventSpaceReservationDto.defaultTaxRateId,
          taxAmount: updateEventSpaceReservationDto.taxAmount,
          notes: updateEventSpaceReservationDto.notes,
          cancellationReason: updateEventSpaceReservationDto.cancellationReason,
          cancellationDate: updateEventSpaceReservationDto.cancellationDate
            ? new Date(updateEventSpaceReservationDto.cancellationDate)
            : undefined,
          confirmationSent: updateEventSpaceReservationDto.confirmationSent,
          confirmationSentAt: updateEventSpaceReservationDto.confirmationSentAt
            ? new Date(updateEventSpaceReservationDto.confirmationSentAt)
            : undefined,
          reminderSent: updateEventSpaceReservationDto.reminderSent,
          reminderSentAt: updateEventSpaceReservationDto.reminderSentAt
            ? new Date(updateEventSpaceReservationDto.reminderSentAt)
            : undefined,
          updatedBy: userId,
        })
        .where(eq(eventSpaceReservations.id, id))
        .returning({ id: eventSpaceReservations.id });

      // Handle asset assignments update
      if (updateEventSpaceReservationDto.assetIds !== undefined) {
        // Remove existing asset assignments
        await tx
          .update(eventSpaceReservationAssets)
          .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
          .where(eq(eventSpaceReservationAssets.reservationId, id));

        // Add new asset assignments
        if (updateEventSpaceReservationDto.assetIds.length > 0) {
          await this.assignAssetsToReservation(
            tx,
            id,
            updateEventSpaceReservationDto.assetIds,
            userId,
            businessId,
          );
        }
      }

      // Handle task updates
      if (updateEventSpaceReservationDto.tasks !== undefined) {
        // Remove existing task assignments and soft delete related tasks
        const existingTaskAssignments = await tx
          .select({ taskId: eventSpaceReservationTasks.taskId })
          .from(eventSpaceReservationTasks)
          .where(
            and(
              eq(eventSpaceReservationTasks.reservationId, id),
              eq(eventSpaceReservationTasks.isDeleted, false),
            ),
          );

        if (existingTaskAssignments.length > 0) {
          const taskIds = existingTaskAssignments.map((ta) => ta.taskId);

          // Soft delete the tasks
          await tx
            .update(tasks)
            .set({ isDeleted: true, updatedBy: userId })
            .where(inArray(tasks.id, taskIds));

          // Soft delete task assignments
          await tx
            .update(eventSpaceReservationTasks)
            .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
            .where(eq(eventSpaceReservationTasks.reservationId, id));
        }

        // Create new tasks
        if (updateEventSpaceReservationDto.tasks.length > 0) {
          await this.createTasksForReservation(
            tx,
            id,
            updateEventSpaceReservationDto.tasks,
            userId,
            businessId,
          );
        }
      }

      return updatedReservation;
    });

    // Log the event space reservation update activity
    await this.activityLogService.logUpdate(
      result.id,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: ActivitySource.SYSTEM,
      },
    );

    return { id: result.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateEventSpaceReservationDto: UpdateEventSpaceReservationDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateEventSpaceReservationDto,
    );
    return {
      id: result.id,
      message: 'Event space reservation updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if reservation exists and belongs to the business
    const existingReservation = await this.db
      .select()
      .from(eventSpaceReservations)
      .where(
        and(
          eq(eventSpaceReservations.id, id),
          eq(eventSpaceReservations.businessId, businessId),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingReservation) {
      throw new NotFoundException('Event space reservation not found');
    }

    await this.db.transaction(async (tx) => {
      // Soft delete the reservation
      await tx
        .update(eventSpaceReservations)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(eventSpaceReservations.id, id));

      // Soft delete associated asset assignments
      await tx
        .update(eventSpaceReservationAssets)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(eventSpaceReservationAssets.reservationId, id));

      // Soft delete associated task assignments
      await tx
        .update(eventSpaceReservationTasks)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(eventSpaceReservationTasks.reservationId, id));
    });

    // Log the event space reservation deletion activity
    await this.activityLogService.logDelete(
      id,
      EntityType.RESERVATION,
      userId,
      businessId,
      {
        source: ActivitySource.SYSTEM,
      },
    );

    return {
      id,
      message: 'Event space reservation deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    reservationIds: string[],
  ): Promise<{
    deleted: number;
    deletedIds: string[];
    failed: Array<{ id: string; reason: string }>;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!reservationIds || reservationIds.length === 0) {
      throw new BadRequestException('No reservation IDs provided');
    }

    // Check which reservations exist and belong to the business
    const existingReservations = await this.db
      .select({
        id: eventSpaceReservations.id,
        reservationNumber: eventSpaceReservations.reservationNumber,
      })
      .from(eventSpaceReservations)
      .where(
        and(
          inArray(eventSpaceReservations.id, reservationIds),
          eq(eventSpaceReservations.businessId, businessId),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      );

    const existingIds = existingReservations.map((r) => r.id);
    const notFoundIds = reservationIds.filter(
      (id) => !existingIds.includes(id),
    );

    const result = await this.db.transaction(async (tx) => {
      // Soft delete the reservations
      if (existingIds.length > 0) {
        await tx
          .update(eventSpaceReservations)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(inArray(eventSpaceReservations.id, existingIds));

        // Soft delete associated asset assignments
        await tx
          .update(eventSpaceReservationAssets)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(
            inArray(eventSpaceReservationAssets.reservationId, existingIds),
          );

        // Soft delete associated task assignments
        await tx
          .update(eventSpaceReservationTasks)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(
            inArray(eventSpaceReservationTasks.reservationId, existingIds),
          );
      }

      return {
        deleted: existingIds.length,
        deletedIds: existingIds,
        failed: notFoundIds.map((id) => ({
          id,
          reason: 'Reservation not found',
        })),
      };
    });

    // Log bulk deletion operation
    if (result.deleted > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.RESERVATION,
        result.deletedIds,
        {
          names: existingReservations.map((r) => r.reservationNumber),
        },
        userId,
        businessId,
        {
          filterCriteria: { reservationIds: reservationIds },
          source: ActivitySource.SYSTEM,
        },
      );
    }

    return result;
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    reservationIds: string[],
    status: EventReservationStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ id: string; reason: string }>;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!reservationIds || reservationIds.length === 0) {
      throw new BadRequestException('No reservation IDs provided');
    }

    // Check which reservations exist and belong to the business
    const existingReservations = await this.db
      .select({
        id: eventSpaceReservations.id,
        reservationNumber: eventSpaceReservations.reservationNumber,
      })
      .from(eventSpaceReservations)
      .where(
        and(
          inArray(eventSpaceReservations.id, reservationIds),
          eq(eventSpaceReservations.businessId, businessId),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      );

    const existingIds = existingReservations.map((r) => r.id);
    const notFoundIds = reservationIds.filter(
      (id) => !existingIds.includes(id),
    );

    let updatedCount = 0;
    if (existingIds.length > 0) {
      const updateResult = await this.db
        .update(eventSpaceReservations)
        .set({
          status,
          updatedBy: userId,
        })
        .where(inArray(eventSpaceReservations.id, existingIds))
        .returning({ id: eventSpaceReservations.id });

      updatedCount = updateResult.length;
    }

    const result = {
      updated: updatedCount,
      updatedIds: existingIds,
      failed: notFoundIds.map((id) => ({
        id,
        reason: 'Reservation not found',
      })),
    };

    return result;
  }

  async checkReservationNumberAvailability(
    businessId: string | null,
    reservationNumber: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingReservation = await this.db
      .select({ id: eventSpaceReservations.id })
      .from(eventSpaceReservations)
      .where(
        and(
          eq(eventSpaceReservations.businessId, businessId),
          ilike(eventSpaceReservations.reservationNumber, reservationNumber),
          eq(eventSpaceReservations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReservation };
  }

  private buildAdvancedFilters(filters: any[]): any[] {
    const conditions = [];

    for (const filter of filters) {
      const { id, value, operator } = filter;

      if (!id || !operator || value === undefined || value === null) {
        continue;
      }

      switch (id) {
        case 'reservationNumber':
          if (operator === 'iLike') {
            conditions.push(
              ilike(eventSpaceReservations.reservationNumber, `%${value}%`),
            );
          } else if (operator === 'eq') {
            conditions.push(
              eq(eventSpaceReservations.reservationNumber, value),
            );
          }
          break;
        case 'eventName':
          if (operator === 'iLike') {
            conditions.push(
              ilike(eventSpaceReservations.eventName, `%${value}%`),
            );
          } else if (operator === 'eq') {
            conditions.push(eq(eventSpaceReservations.eventName, value));
          }
          break;
        case 'status':
          if (operator === 'eq') {
            conditions.push(eq(eventSpaceReservations.status, value));
          }
          break;
        case 'paymentStatus':
          if (operator === 'eq') {
            conditions.push(eq(eventSpaceReservations.paymentStatus, value));
          }
          break;
        case 'eventType':
          if (operator === 'eq') {
            conditions.push(eq(eventSpaceReservations.eventType, value));
          }
          break;
      }
    }

    return conditions;
  }

  private buildOrderBy(sortConfig: any[]): any[] {
    const orderBy = [];

    for (const sort of sortConfig) {
      const { id, desc: isDesc } = sort;

      switch (id) {
        case 'reservationNumber':
          orderBy.push(
            isDesc
              ? desc(eventSpaceReservations.reservationNumber)
              : asc(eventSpaceReservations.reservationNumber),
          );
          break;
        case 'eventName':
          orderBy.push(
            isDesc
              ? desc(eventSpaceReservations.eventName)
              : asc(eventSpaceReservations.eventName),
          );
          break;
        case 'eventStartTime':
          orderBy.push(
            isDesc
              ? desc(eventSpaceReservations.eventStartTime)
              : asc(eventSpaceReservations.eventStartTime),
          );
          break;
        case 'status':
          orderBy.push(
            isDesc
              ? desc(eventSpaceReservations.status)
              : asc(eventSpaceReservations.status),
          );
          break;
        case 'createdAt':
          orderBy.push(
            isDesc
              ? desc(eventSpaceReservations.createdAt)
              : asc(eventSpaceReservations.createdAt),
          );
          break;
        case 'updatedAt':
          orderBy.push(
            isDesc
              ? desc(eventSpaceReservations.updatedAt)
              : asc(eventSpaceReservations.updatedAt),
          );
          break;
      }
    }

    return orderBy;
  }
}
