import { Injectable, Inject, BadRequestException } from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  customerRewards,
  pointTransactions,
} from '../drizzle/schema/customers.schema';
import { eq, and, desc } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { PointTransactionType } from '../shared/types/reward.enum';

@Injectable()
export class CustomerRewardsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  /**
   * Get or create customer reward record
   */
  async getOrCreateCustomerReward(
    customerId: string,
    userId: string,
    tx?: any,
  ): Promise<{ id: string; totalPoints: number; pointsEarnedYtd: number }> {
    const dbInstance = tx || this.db;

    // Try to find existing reward record
    const existingReward = await dbInstance
      .select()
      .from(customerRewards)
      .where(
        and(
          eq(customerRewards.customerId, customerId),
          eq(customerRewards.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (existingReward) {
      return {
        id: existingReward.id,
        totalPoints: existingReward.totalPoints,
        pointsEarnedYtd: existingReward.pointsEarnedYtd,
      };
    }

    // Create new reward record
    const [newReward] = await dbInstance
      .insert(customerRewards)
      .values({
        customerId,
        totalPoints: 0,
        pointsEarnedYtd: 0,
        lastActivityDate: new Date(),
        createdBy: userId,
        updatedBy: userId,
      })
      .returning();

    return {
      id: newReward.id,
      totalPoints: newReward.totalPoints,
      pointsEarnedYtd: newReward.pointsEarnedYtd,
    };
  }

  /**
   * Add points to customer account
   */
  async addPoints(
    customerId: string,
    points: number,
    userId: string,
    orderId?: string,
    tx?: any,
  ): Promise<{ success: boolean; newBalance: number }> {
    if (points <= 0) {
      throw new BadRequestException('Points must be greater than 0');
    }

    const dbInstance = tx || this.db;

    // Get or create customer reward record
    const customerReward = await this.getOrCreateCustomerReward(
      customerId,
      userId,
      dbInstance,
    );

    // Create point transaction
    await dbInstance.insert(pointTransactions).values({
      customerId,
      orderId,
      pointsEarned: points,
      pointsUsed: 0,
      transactionType: PointTransactionType.EARNED,
      createdBy: userId,
      updatedBy: userId,
    });

    // Update customer reward totals
    const newTotalPoints = customerReward.totalPoints + points;
    const newPointsEarnedYtd = customerReward.pointsEarnedYtd + points;

    await dbInstance
      .update(customerRewards)
      .set({
        totalPoints: newTotalPoints,
        pointsEarnedYtd: newPointsEarnedYtd,
        lastActivityDate: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(customerRewards.id, customerReward.id));

    return {
      success: true,
      newBalance: newTotalPoints,
    };
  }

  /**
   * Redeem points from customer account
   */
  async redeemPoints(
    customerId: string,
    points: number,
    userId: string,
    orderId?: string,
    tx?: any,
  ): Promise<{ success: boolean; newBalance: number }> {
    if (points <= 0) {
      throw new BadRequestException('Points must be greater than 0');
    }

    const dbInstance = tx || this.db;

    // Get customer reward record
    const customerReward = await this.getOrCreateCustomerReward(
      customerId,
      userId,
      dbInstance,
    );

    if (customerReward.totalPoints < points) {
      throw new BadRequestException('Insufficient points balance');
    }

    // Create point transaction
    await dbInstance.insert(pointTransactions).values({
      customerId,
      orderId,
      pointsEarned: 0,
      pointsUsed: points,
      transactionType: PointTransactionType.REDEEMED,
      createdBy: userId,
      updatedBy: userId,
    });

    // Update customer reward totals
    const newTotalPoints = customerReward.totalPoints - points;

    await dbInstance
      .update(customerRewards)
      .set({
        totalPoints: newTotalPoints,
        lastActivityDate: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(customerRewards.id, customerReward.id));

    return {
      success: true,
      newBalance: newTotalPoints,
    };
  }

  /**
   * Get customer reward balance
   */
  async getCustomerBalance(customerId: string): Promise<{
    totalPoints: number;
    pointsEarnedYtd: number;
    lastActivityDate?: Date;
  }> {
    const reward = await this.db
      .select()
      .from(customerRewards)
      .where(
        and(
          eq(customerRewards.customerId, customerId),
          eq(customerRewards.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!reward) {
      return {
        totalPoints: 0,
        pointsEarnedYtd: 0,
        lastActivityDate: undefined,
      };
    }

    return {
      totalPoints: reward.totalPoints,
      pointsEarnedYtd: reward.pointsEarnedYtd,
      lastActivityDate: reward.lastActivityDate,
    };
  }

  /**
   * Get customer point transaction history
   */
  async getTransactionHistory(
    customerId: string,
    limit = 50,
  ): Promise<
    Array<{
      id: string;
      pointsEarned: number;
      pointsUsed: number;
      transactionType: PointTransactionType;
      orderId?: string;
      createdAt: Date;
    }>
  > {
    const transactions = await this.db
      .select()
      .from(pointTransactions)
      .where(
        and(
          eq(pointTransactions.customerId, customerId),
          eq(pointTransactions.isDeleted, false),
        ),
      )
      .orderBy(desc(pointTransactions.createdAt))
      .limit(limit);

    return transactions.map((transaction) => ({
      id: transaction.id,
      pointsEarned: transaction.pointsEarned,
      pointsUsed: transaction.pointsUsed,
      transactionType: transaction.transactionType as PointTransactionType,
      orderId: transaction.orderId,
      createdAt: transaction.createdAt,
    }));
  }

  /**
   * Adjust customer points (for admin corrections)
   */
  async adjustPoints(
    customerId: string,
    pointsAdjustment: number,
    userId: string,
    reason?: string,
    tx?: any,
  ): Promise<{ success: boolean; newBalance: number }> {
    if (pointsAdjustment === 0) {
      throw new BadRequestException('Points adjustment cannot be zero');
    }

    const dbInstance = tx || this.db;

    // Get customer reward record
    const customerReward = await this.getOrCreateCustomerReward(
      customerId,
      userId,
      dbInstance,
    );

    // Create point transaction
    await dbInstance.insert(pointTransactions).values({
      customerId,
      pointsEarned: pointsAdjustment > 0 ? pointsAdjustment : 0,
      pointsUsed: pointsAdjustment < 0 ? Math.abs(pointsAdjustment) : 0,
      transactionType: PointTransactionType.ADJUSTED,
      createdBy: userId,
      updatedBy: userId,
    });

    // Update customer reward totals
    const newTotalPoints = customerReward.totalPoints + pointsAdjustment;
    const newPointsEarnedYtd =
      pointsAdjustment > 0
        ? customerReward.pointsEarnedYtd + pointsAdjustment
        : customerReward.pointsEarnedYtd;

    await dbInstance
      .update(customerRewards)
      .set({
        totalPoints: Math.max(0, newTotalPoints), // Ensure balance doesn't go negative
        pointsEarnedYtd: newPointsEarnedYtd,
        lastActivityDate: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(customerRewards.id, customerReward.id));

    return {
      success: true,
      newBalance: Math.max(0, newTotalPoints),
    };
  }
}
