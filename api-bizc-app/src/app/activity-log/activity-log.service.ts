import {
  Injectable,
  Inject,
  NotFoundException,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { activityLogs } from '../drizzle/schema/activity-log.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  ActivityType,
  EntityType,
  ActivitySource,
  OperationType,
  DeleteType,
  TriggeredBy,
  PriorityImpact,
  ExecutionStrategy,
  FileFormat,
} from '../shared/types/activity.enum';
import {
  eq,
  and,
  asc,
  gte,
  count,
  desc,
  lte,
  sql,
  or,
  ilike,
  inArray,
} from 'drizzle-orm';
import { CreateActivityLogDto } from './dto/create-activity-log.dto';
import { ActivityLogDto } from './dto/activity-log.dto';
import { ActivityLogListDto } from './dto/activity-log-list.dto';

// Import our typed interfaces
import {
  BulkOperationChanges,
  StatusChangeChanges,
  PriorityChangeChanges,
  CreateChanges,
  AuthMetadata,
  BulkOperationMetadata,
  StatusChangeMetadata,
  PriorityChangeMetadata,
  ImportExportMetadata,
} from './interfaces/activity-log.interfaces';

@Injectable()
export class ActivityLogService {
  private readonly logger = new Logger(ActivityLogService.name);

  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  /**
   * Type-safe activity log creation for different activity types
   */
  async createTypedLog<T extends ActivityType>(
    activityType: T,
    data: {
      entityType: EntityType;
      entityId?: string;
      entityIds?: string[];
      userId: string;
      businessId?: string;
      source?: ActivitySource;
      changes: any; // Simplified type to avoid complex union issues
      metadata: any; // Simplified type to avoid complex union issues
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
      affectedCount?: number;
    },
  ): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .insert(activityLogs)
        .values({
          entityType: data.entityType as any,
          entityId: data.entityId,
          entityIds: data.entityIds,
          businessId: data.businessId,
          activityType,
          source: data.source || ActivitySource.WEB,
          userId: data.userId,
          changes: data.changes,
          metadata: data.metadata,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          sessionId: data.sessionId,
          duration: data.duration,
          affectedCount: data.affectedCount,
        })
        .returning();

      return this.mapToActivityLogDto(result);
    } catch (error) {
      this.logger.error(`Failed to create ${activityType} activity log`, {
        error: error.message,
        activityType,
        entityType: data.entityType,
      });
      throw error;
    }
  }

  /**
   * Log a create operation with proper typing
   */
  async logCreate(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      entityName?: string;
      reason?: string;
      source?: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const changes = {
      operation: OperationType.CREATE,
    };

    const metadata = {
      reason: options?.reason || 'Record created',
      entityName: options?.entityName,
      status: 'success',
    };

    return this.createTypedLog(ActivityType.CREATE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log an update operation with proper typing
   */
  async logUpdate(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      entityName?: string;
      changes?: any;
      reason?: string;
      source?: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    return this.createTypedLog(ActivityType.UPDATE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes: {
        operation: OperationType.UPDATE,
        ...options?.changes,
      },
      metadata: {
        timestamp: new Date(),
        entityName: options?.entityName,
        reason: options?.reason || 'Record updated',
        status: 'success',
      },
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log a delete operation with proper typing
   */
  async logDelete(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      entityName?: string;
      reason?: string;
      source?: ActivitySource;
      deleteType?: DeleteType;
      relatedEntities?: Array<{ type: string; id: string; action: string }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const isHardDelete = options?.deleteType === DeleteType.HARD;

    const changes = {
      operation: OperationType.DELETE,
      deleteType: options?.deleteType || DeleteType.SOFT,
      permanent: isHardDelete,
    };

    const metadata = {
      reason: options?.reason || 'Record deleted',
      entityName: options?.entityName,
      deleteType: options?.deleteType || DeleteType.SOFT,
      relatedEntities: options?.relatedEntities,
      timestamp: new Date(),
      permanentDelete: isHardDelete,
      status: 'success',
    };

    return this.createTypedLog(ActivityType.DELETE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log a restore operation with proper typing
   */
  async logRestore(
    entityId: string,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      source?: ActivitySource;
      relatedEntities?: Array<{ type: string; id: string; action: string }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const changes = {
      operation: OperationType.RESTORE,
      restored: true,
    };

    const metadata = {
      reason: options?.reason || 'Record restored',
      relatedEntities: options?.relatedEntities,
      timestamp: new Date(),
    };

    return this.createTypedLog(ActivityType.RESTORE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: options?.source || ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
      duration: options?.duration,
      affectedCount: 1,
    });
  }

  /**
   * Log a status change with proper typing
   */
  async logStatusChange(
    entityId: string,
    entityType: EntityType,
    previousStatus: string,
    newStatus: string,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      triggeredBy?: TriggeredBy;
      relatedEntities?: Array<{ type: string; id: string; action: string }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    },
  ): Promise<ActivityLogDto> {
    const changes: StatusChangeChanges = {
      before: { status: previousStatus },
      after: { status: newStatus },
      fields: ['status'],
    };

    const metadata: StatusChangeMetadata = {
      statusTransition: `${previousStatus} -> ${newStatus}`,
      previousStatus,
      newStatus,
      reason: options?.reason || 'Status updated',
      triggeredBy: options?.triggeredBy || TriggeredBy.MANUAL,
      source: ActivitySource.WEB,
      relatedEntities: options?.relatedEntities,
    };

    return this.createTypedLog(ActivityType.STATUS_CHANGE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
    });
  }

  /**
   * Log a priority change with proper typing
   */
  async logPriorityChange(
    entityId: string,
    entityType: EntityType,
    previousPriority: number,
    newPriority: number,
    userId: string,
    businessId: string,
    options?: {
      reason?: string;
      affectedHierarchy?: boolean;
      cascadedTo?: string[];
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
    },
  ): Promise<ActivityLogDto> {
    const changes: PriorityChangeChanges = {
      before: { priority: previousPriority },
      after: { priority: newPriority },
      fields: ['priority'],
    };

    const metadata: PriorityChangeMetadata = {
      priorityChange: `${previousPriority} -> ${newPriority}`,
      previousPriority,
      newPriority,
      impact:
        newPriority > previousPriority
          ? PriorityImpact.INCREASED
          : PriorityImpact.DECREASED,
      reason: options?.reason || 'Priority adjusted',
      affectedHierarchy: options?.affectedHierarchy,
      cascadedTo: options?.cascadedTo,
      source: ActivitySource.WEB,
    };

    return this.createTypedLog(ActivityType.PRIORITY_CHANGE, {
      entityType,
      entityId,
      userId,
      businessId,
      source: ActivitySource.WEB,
      changes,
      metadata,
      ipAddress: options?.ipAddress,
      userAgent: options?.userAgent,
      sessionId: options?.sessionId,
    });
  }

  /**
   * Log bulk operations with proper typing
   */
  async logBulkOperation(
    operationType: ActivityType,
    entityType: EntityType,
    affectedIds: string[],
    updates: any,
    userId: string,
    businessId: string,
    options: {
      filterCriteria: Record<string, any>;
      beforeSample?: any[];
      failures?: Array<{ id: string; error: string }>;
      executionStrategy?: ExecutionStrategy;
      source?: ActivitySource;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const bulkOperationId = crypto.randomUUID();
    const successCount = affectedIds.length - (options.failures?.length || 0);

    const changes: BulkOperationChanges = {
      before: options.beforeSample
        ? { sample: options.beforeSample.slice(0, 3) }
        : undefined,
      after: updates,
      fields: Object.keys(updates),
    };

    const metadata: BulkOperationMetadata = {
      bulkOperationId,
      filterCriteria: options.filterCriteria,
      totalProcessed: affectedIds.length,
      successCount,
      failureCount: options.failures?.length || 0,
      failures: options.failures,
      updateFields: Object.keys(updates),
      executionStrategy:
        options.executionStrategy || ExecutionStrategy.PARALLEL,
      source: ActivitySource.WEB,
    };

    // Add specific metadata based on operation type
    let finalMetadata: any = metadata;
    if (operationType === ActivityType.BULK_STATUS_CHANGE && updates.status) {
      finalMetadata = {
        ...metadata,
        statusTransition: `* -> ${updates.status}`,
        newStatus: updates.status,
      };
    } else if (
      operationType === ActivityType.BULK_PRIORITY_CHANGE &&
      updates.priority !== undefined
    ) {
      finalMetadata = {
        ...metadata,
        priorityChange: `* -> ${updates.priority}`,
        newPriority: updates.priority,
        impact: PriorityImpact.BULK_UPDATE,
      };
    }

    return this.createTypedLog(operationType, {
      entityType,
      entityIds: affectedIds,
      userId,
      businessId,
      source: options.source || ActivitySource.WEB,
      changes,
      metadata: finalMetadata,
      affectedCount: affectedIds.length,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
      duration: options.duration,
    });
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    eventType: ActivityType,
    userId: string,
    options: {
      ipAddress: string;
      userAgent: string;
      sessionId?: string;
      failureReason?: string;
      mfaUsed?: boolean;
      sessionDuration?: number;
      deviceInfo?: {
        os?: string;
        browser?: string;
        device?: string;
      };
    },
  ): Promise<ActivityLogDto> {
    const metadata: AuthMetadata = {
      deviceInfo: options.deviceInfo,
      failureReason:
        eventType === ActivityType.LOGIN_FAILED
          ? options.failureReason
          : undefined,
      mfaUsed: eventType === ActivityType.LOGIN ? options.mfaUsed : undefined,
      sessionDuration:
        eventType === ActivityType.LOGOUT ? options.sessionDuration : undefined,
    };

    return this.createTypedLog(eventType, {
      entityType: EntityType.AUTH,
      entityId: userId,
      userId,
      source: this.detectSource(options.userAgent),
      changes: undefined as never,
      metadata,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
    });
  }

  /**
   * Log import/export operations
   */
  async logImportExport(
    operationType: ActivityType,
    entityType: EntityType,
    userId: string,
    businessId: string,
    options: {
      fileName: string;
      fileSize: number;
      format: FileFormat;
      recordCount: number;
      importedData?: any[];
      exportFilters?: Record<string, any>;
      validationErrors?: Array<{
        row: number;
        field: string;
        error: string;
      }>;
      ipAddress?: string;
      userAgent?: string;
      sessionId?: string;
      duration?: number;
    },
  ): Promise<ActivityLogDto> {
    const metadata: ImportExportMetadata = {
      fileName: options.fileName,
      fileSize: options.fileSize,
      format: options.format,
      recordCount: options.recordCount,
      validationErrors: options.validationErrors,
      exportFilters: options.exportFilters,
      importId:
        operationType === ActivityType.IMPORT ? crypto.randomUUID() : undefined,
      source: ActivitySource.WEB,
    };

    const changes =
      operationType === ActivityType.IMPORT && options.importedData
        ? options.importedData.map((data) => ({ after: data }) as CreateChanges)
        : undefined;

    return this.createTypedLog(operationType, {
      entityType,
      userId,
      businessId,
      source: ActivitySource.WEB,
      changes: changes as any,
      metadata,
      affectedCount: options.recordCount,
      ipAddress: options.ipAddress,
      userAgent: options.userAgent,
      sessionId: options.sessionId,
      duration: options.duration,
    });
  }

  /**
   * Get session activities
   */
  async getSessionActivities(sessionId: string): Promise<ActivityLogDto[]> {
    const results = await this.db
      .select()
      .from(activityLogs)
      .where(eq(activityLogs.sessionId, sessionId))
      .orderBy(asc(activityLogs.createdAt));

    return results.map((row) => this.mapToActivityLogDto(row));
  }

  /**
   * Detect suspicious activities
   */
  async detectSuspiciousActivities(
    userId: string,
    timeWindowMinutes: number = 60,
  ): Promise<{
    rapidBulkDeletes: boolean;
    failedLogins: number;
    unusualActivity: boolean;
    suspiciousPatterns: string[];
  }> {
    const since = new Date(Date.now() - timeWindowMinutes * 60 * 1000);

    const recentLogs = await this.db
      .select()
      .from(activityLogs)
      .where(
        and(
          eq(activityLogs.userId, userId),
          gte(activityLogs.createdAt, since),
        ),
      );

    const bulkDeletes = recentLogs.filter(
      (log) => log.activityType === ActivityType.BULK_DELETE,
    );
    const failedLogins = recentLogs.filter(
      (log) => log.activityType === ActivityType.LOGIN_FAILED,
    );
    const patterns: string[] = [];

    if (bulkDeletes.length > 3) {
      patterns.push(
        `${bulkDeletes.length} bulk delete operations in ${timeWindowMinutes} minutes`,
      );
    }

    if (failedLogins.length > 5) {
      patterns.push(`${failedLogins.length} failed login attempts`);
    }

    if (recentLogs.length > 100) {
      patterns.push(`High volume activity: ${recentLogs.length} actions`);
    }

    // Check for rapid status changes
    const statusChanges = recentLogs.filter(
      (log) =>
        log.activityType === ActivityType.STATUS_CHANGE ||
        log.activityType === ActivityType.BULK_STATUS_CHANGE,
    );
    if (statusChanges.length > 10) {
      patterns.push(`Frequent status changes: ${statusChanges.length} changes`);
    }

    return {
      rapidBulkDeletes: bulkDeletes.length > 3,
      failedLogins: failedLogins.length,
      unusualActivity: patterns.length > 0,
      suspiciousPatterns: patterns,
    };
  }

  /**
   * Get activity counts by entity type
   */
  async getActivityCounts(
    businessId: string,
  ): Promise<Record<EntityType, number>> {
    const results = await this.db
      .select({
        entityType: activityLogs.entityType,
        count: count(),
      })
      .from(activityLogs)
      .where(eq(activityLogs.businessId, businessId))
      .groupBy(activityLogs.entityType);

    const counts: Record<string, number> = {};
    for (const result of results) {
      counts[result.entityType] = result.count;
    }

    return counts as Record<EntityType, number>;
  }

  /**
   * Find all activity logs with pagination and filtering
   */
  async findAll(
    currentUserId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    entityType?: string,
    activityType?: string,
    from?: string,
    to?: string,
    filterUserId?: string,
    source?: string,
    ipAddress?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ActivityLogListDto[];
    meta: { total: number; page: number; limit: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [eq(activityLogs.businessId, businessId)];

    // Add user filtering - if filterUserId is provided, use it; otherwise filter by current user
    if (filterUserId) {
      whereConditions.push(eq(activityLogs.userId, filterUserId));
    } else {
      whereConditions.push(eq(activityLogs.userId, currentUserId));
    }

    // Add basic filtering if provided
    if (entityType) {
      whereConditions.push(eq(activityLogs.entityType, entityType as any));
    }

    if (activityType) {
      whereConditions.push(eq(activityLogs.activityType, activityType as any));
    }

    if (source) {
      whereConditions.push(eq(activityLogs.source, source as any));
    }

    if (ipAddress) {
      whereConditions.push(ilike(activityLogs.ipAddress, `%${ipAddress}%`));
    }

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(activityLogs.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(activityLogs.createdAt, toDate));
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'entityType') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(activityLogs.entityType, value),
                  );
                } else {
                  filterConditions.push(eq(activityLogs.entityType, value));
                }
                break;
              case 'ne':
                filterConditions.push(
                  sql`${activityLogs.entityType} != ${value}`,
                );
                break;
              case 'iLike':
                filterConditions.push(
                  ilike(activityLogs.entityType, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(activityLogs.entityType, `%${value}%`)}`,
                );
                break;
            }
          } else if (fieldId === 'activityType') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(activityLogs.activityType, value),
                  );
                } else {
                  filterConditions.push(eq(activityLogs.activityType, value));
                }
                break;
              case 'ne':
                filterConditions.push(
                  sql`${activityLogs.activityType} != ${value}`,
                );
                break;
              case 'iLike':
                filterConditions.push(
                  ilike(activityLogs.activityType, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(activityLogs.activityType, `%${value}%`)}`,
                );
                break;
            }
          } else if (fieldId === 'source') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(inArray(activityLogs.source, value));
                } else {
                  filterConditions.push(eq(activityLogs.source, value));
                }
                break;
              case 'ne':
                filterConditions.push(sql`${activityLogs.source} != ${value}`);
                break;
            }
          } else if (fieldId === 'ipAddress') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(activityLogs.ipAddress, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(activityLogs.ipAddress, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(activityLogs.ipAddress, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${activityLogs.ipAddress} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${activityLogs.ipAddress} IS NULL OR ${activityLogs.ipAddress} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${activityLogs.ipAddress} IS NOT NULL AND ${activityLogs.ipAddress} != ''`,
                );
                break;
            }
          } else if (fieldId === 'userId') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(activityLogs.userId, value));
                break;
              case 'ne':
                filterConditions.push(sql`${activityLogs.userId} != ${value}`);
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    let orderBy = [desc(activityLogs.createdAt), asc(activityLogs.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'entityType':
              orderBy = [
                isDesc
                  ? desc(activityLogs.entityType)
                  : asc(activityLogs.entityType),
                asc(activityLogs.id),
              ];
              break;
            case 'activityType':
              orderBy = [
                isDesc
                  ? desc(activityLogs.activityType)
                  : asc(activityLogs.activityType),
                asc(activityLogs.id),
              ];
              break;
            case 'userId':
              orderBy = [
                isDesc ? desc(activityLogs.userId) : asc(activityLogs.userId),
                asc(activityLogs.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(activityLogs.createdAt)
                  : asc(activityLogs.createdAt),
                asc(activityLogs.id),
              ];
              break;
            case 'duration':
              orderBy = [
                isDesc
                  ? desc(activityLogs.duration)
                  : asc(activityLogs.duration),
                asc(activityLogs.id),
              ];
              break;
            case 'affectedCount':
              orderBy = [
                isDesc
                  ? desc(activityLogs.affectedCount)
                  : asc(activityLogs.affectedCount),
                asc(activityLogs.id),
              ];
              break;
            case 'source':
              orderBy = [
                isDesc ? desc(activityLogs.source) : asc(activityLogs.source),
                asc(activityLogs.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Find all activity logs with pagination and user information
    const result = await this.db
      .select({
        // Activity log fields
        id: activityLogs.id,
        entityType: activityLogs.entityType,
        entityId: activityLogs.entityId,
        entityIds: activityLogs.entityIds,
        activityType: activityLogs.activityType,
        source: activityLogs.source,
        userId: activityLogs.userId,
        businessId: activityLogs.businessId,
        changes: activityLogs.changes,
        metadata: activityLogs.metadata,
        affectedCount: activityLogs.affectedCount,
        duration: activityLogs.duration,
        ipAddress: activityLogs.ipAddress,
        userAgent: activityLogs.userAgent,
        sessionId: activityLogs.sessionId,
        createdAt: activityLogs.createdAt,
        // User fields
        userName: sql<string>`CONCAT(COALESCE(${users.firstName}, ''), ' ', COALESCE(${users.lastName}, ''))`,
        userEmail: users.email,
        userAvatar: users.avatar,
        userFirstName: users.firstName,
        userLastName: users.lastName,
      })
      .from(activityLogs)
      .leftJoin(users, eq(activityLogs.userId, users.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(activityLogs)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result.map((row) => this.mapToActivityLogListDto(row)),
      meta: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  /**
   * Generic log method for backward compatibility
   */

  /**
   * Original methods maintained for backward compatibility
   */
  async create(
    createActivityLogDto: CreateActivityLogDto,
  ): Promise<ActivityLogDto> {
    return this.createTypedLog(createActivityLogDto.activityType, {
      entityType: createActivityLogDto.entityType,
      entityId: createActivityLogDto.entityId,
      entityIds: createActivityLogDto.entityIds,
      userId: createActivityLogDto.userId,
      businessId: createActivityLogDto.businessId,
      source: ActivitySource.WEB,
      changes: createActivityLogDto.changes || {},
      metadata: createActivityLogDto.metadata || {},
      ipAddress: createActivityLogDto.ipAddress,
      userAgent: createActivityLogDto.userAgent,
      sessionId: createActivityLogDto.sessionId,
      duration: createActivityLogDto.duration,
      affectedCount: createActivityLogDto.affectedCount,
    });
  }

  async remove(id: string, businessId: string): Promise<ActivityLogDto> {
    try {
      const [result] = await this.db
        .delete(activityLogs)
        .where(
          and(eq(activityLogs.id, id), eq(activityLogs.businessId, businessId)),
        )
        .returning();

      if (!result) {
        throw new NotFoundException('Activity log not found');
      }

      return this.mapToActivityLogDto(result);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Failed to delete activity log', {
        error: error.message,
        id,
        businessId,
      });
      throw error;
    }
  }

  /**
   * Utility methods
   */
  private detectSource(userAgent?: string): ActivitySource {
    if (!userAgent) return ActivitySource.API;
    if (userAgent.includes('Mobile')) return ActivitySource.MOBILE;
    if (userAgent.includes('Mozilla')) return ActivitySource.WEB;
    return ActivitySource.API;
  }

  private mapToActivityLogDto(row: any): ActivityLogDto {
    return {
      id: row.id,
      entityType: row.entityType,
      entityId: row.entityId,
      entityIds: row.entityIds,
      businessId: row.businessId,
      activityType: row.activityType,
      source: row.source,
      userId: row.userId,
      changes: row.changes,
      metadata: row.metadata,
      ipAddress: row.ipAddress,
      userAgent: row.userAgent,
      sessionId: row.sessionId,
      duration: row.duration,
      affectedCount: row.affectedCount,
      createdAt: row.createdAt,
    };
  }

  private mapToActivityLogListDto(row: any): ActivityLogListDto {
    const user = {
      id: row.userId,
      name: row.userName?.trim() || 'Unknown User',
      email: row.userEmail || '',
      avatar: row.userAvatar,
    };

    // Generate description based on activity type and entity
    const description = this.generateActivityDescription(
      row.activityType,
      row.entityType,
      row.changes,
      row.metadata,
      user.name,
    );

    // Determine status from metadata or changes
    let status = 'success';
    if (row.metadata?.status) {
      status = row.metadata.status;
    } else if (row.changes?.failed) {
      status = 'failed';
    } else if (row.changes?.pending) {
      status = 'pending';
    }

    // Extract entity name from metadata
    const entityName = row.metadata?.entityName || undefined;

    return {
      id: row.id,
      description,
      activityType: row.activityType,
      entityType: row.entityType,
      entityName,
      status,
      user,
      source: row.source,
      ipAddress: row.ipAddress,
      affectedCount: row.affectedCount,
      duration: row.duration,
      createdAt: row.createdAt,
      changes: row.changes,
      metadata: row.metadata,
      entityId: row.entityId,
      entityIds: row.entityIds,
      userId: row.userId,
      businessId: row.businessId,
    };
  }

  /**
   * Generate human-readable description for activity
   */
  private generateActivityDescription(
    activityType: ActivityType,
    entityType: EntityType,
    changes: any,
    metadata: any,
    userName: string,
  ): string {
    const entityName = metadata?.entityName || `${entityType.toLowerCase()}`;

    switch (activityType) {
      case ActivityType.CREATE:
        return `${userName} created ${entityName}`;
      case ActivityType.UPDATE:
        return `${userName} updated ${entityName}`;
      case ActivityType.DELETE:
        return `${userName} deleted ${entityName}`;
      case ActivityType.RESTORE:
        return `${userName} restored ${entityName}`;
      case ActivityType.STATUS_CHANGE: {
        const statusChange = metadata?.statusTransition || '';
        return `${userName} changed status of ${entityName}${statusChange ? ` (${statusChange})` : ''}`;
      }
      case ActivityType.PRIORITY_CHANGE: {
        const priorityChange = metadata?.priorityChange || '';
        return `${userName} changed priority of ${entityName}${priorityChange ? ` (${priorityChange})` : ''}`;
      }
      case ActivityType.BULK_CREATE: {
        const createCount = metadata?.successCount || 'multiple';
        return `${userName} created ${createCount} ${entityType.toLowerCase()}s`;
      }
      case ActivityType.BULK_UPDATE: {
        const updateCount = metadata?.successCount || 'multiple';
        return `${userName} updated ${updateCount} ${entityType.toLowerCase()}s`;
      }
      case ActivityType.BULK_DELETE: {
        const deleteCount = metadata?.successCount || 'multiple';
        return `${userName} deleted ${deleteCount} ${entityType.toLowerCase()}s`;
      }
      case ActivityType.LOGIN:
        return `${userName} logged in`;
      case ActivityType.LOGOUT:
        return `${userName} logged out`;
      case ActivityType.LOGIN_FAILED:
        return `Failed login attempt for ${userName}`;
      case ActivityType.EXPORT:
        return `${userName} exported ${entityType.toLowerCase()} data`;
      case ActivityType.IMPORT: {
        const importCount = metadata?.recordCount || 'multiple';
        return `${userName} imported ${importCount} ${entityType.toLowerCase()}s`;
      }
      case ActivityType.VIEW:
        return `${userName} viewed ${entityName}`;
      case ActivityType.SEARCH:
        return `${userName} searched ${entityType.toLowerCase()}s`;
      default:
        return `${userName} performed ${activityType.toLowerCase()} on ${entityName}`;
    }
  }
}
