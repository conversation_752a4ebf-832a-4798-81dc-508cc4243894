import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  activityTypeEnum,
  entityTypeEnum,
  activitySourceEnum,
} from '../../drizzle/schema/activity-log.schema';
import {
  ActivityType,
  EntityType,
  ActivitySource,
} from '../../shared/types/activity.enum';

export class ActivityLogListDto {
  @ApiProperty({
    description: 'Unique identifier for the activity log',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Human-readable description of the activity',
    example: '<PERSON> created product "iPhone 15 Pro"',
  })
  description: string;

  @ApiProperty({
    description: 'Type of activity performed',
    enum: activityTypeEnum.enumValues,
    example: 'CREATE',
  })
  activityType: ActivityType;

  @ApiProperty({
    description: 'Type of entity being acted upon',
    enum: entityTypeEnum.enumValues,
    example: 'PRODUCT',
  })
  entityType: EntityType;

  @ApiPropertyOptional({
    description: 'Name of the entity being acted upon',
    example: 'iPhone 15 Pro',
  })
  entityName?: string;

  @ApiProperty({
    description: 'Activity status',
    example: 'success',
    enum: ['success', 'failed', 'pending'],
  })
  status: string;

  @ApiProperty({
    description: 'User who performed the activity',
    example: {
      id: '550e8400-e29b-41d4-a716-************',
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg',
    },
  })
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };

  @ApiProperty({
    description: 'Source of the activity',
    enum: activitySourceEnum.enumValues,
    example: 'WEB',
  })
  source: ActivitySource;

  @ApiPropertyOptional({
    description: 'IP address of the user who performed the activity',
    example: '***********',
  })
  ipAddress?: string;

  @ApiPropertyOptional({
    description: 'Number of records affected (for bulk operations)',
    example: 5,
  })
  affectedCount?: number;

  @ApiPropertyOptional({
    description: 'Operation duration in milliseconds',
    example: 250,
  })
  duration?: number;

  @ApiProperty({
    description: 'When the activity was performed',
    example: '2024-01-01T12:00:00.000Z',
  })
  createdAt: Date;

  @ApiPropertyOptional({
    description: 'Additional changes made (for detailed view)',
    example: { status: { from: 'draft', to: 'published' } },
  })
  changes?: any;

  @ApiPropertyOptional({
    description: 'Additional metadata',
    example: { reason: 'User request' },
  })
  metadata?: any;

  @ApiPropertyOptional({
    description: 'UUID of the entity being acted upon',
    example: '550e8400-e29b-41d4-a716-************',
  })
  entityId?: string;

  @ApiPropertyOptional({
    description: 'Array of entity IDs for bulk operations',
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
  })
  entityIds?: string[];

  @ApiProperty({
    description: 'UUID of the user who performed the activity',
    example: '550e8400-e29b-41d4-a716-************',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Business ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  businessId?: string;
}
