import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { ActivityLogService } from './activity-log.service';
import { CreateActivityLogDto } from './dto/create-activity-log.dto';
import { ActivityLogDto } from './dto/activity-log.dto';
import { PaginatedActivityLogsResponseDto } from './dto/paginated-activity-logs-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { EntityType } from '../shared/types/activity.enum';

@ApiTags('Activity Logs')
@ApiBearerAuth()
@Controller('activity-logs')
@UseGuards(PermissionsGuard)
export class ActivityLogController {
  constructor(private readonly activityLogService: ActivityLogService) {}

  @Post()
  @RequirePermissions(Permission.ACTIVITY_LOG_CREATE)
  @ApiOperation({
    summary: 'Create a new activity log entry',
    description: 'Creates a new activity log entry for audit trail purposes',
  })
  @ApiResponse({
    status: 201,
    description: 'Activity log has been successfully created',
    type: ActivityLogDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async create(
    @Body() createActivityLogDto: CreateActivityLogDto,
  ): Promise<ActivityLogDto> {
    return this.activityLogService.create(createActivityLogDto);
  }

  @Get()
  @RequirePermissions(Permission.ACTIVITY_LOG_READ)
  @ApiOperation({
    summary: 'Get all activity logs for the active business',
    description:
      'Retrieves paginated activity logs filtered by business ID and user ID',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'entityType',
    description: 'Filter by entity type',
    required: false,
    type: String,
    example: 'CATEGORY',
  })
  @ApiQuery({
    name: 'activityType',
    description: 'Filter by activity type',
    required: false,
    type: String,
    example: 'CREATE',
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-01-31',
  })
  @ApiQuery({
    name: 'userId',
    description: 'Filter by specific user ID',
    required: false,
    type: String,
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiQuery({
    name: 'source',
    description: 'Filter by activity source (WEB, MOBILE, API)',
    required: false,
    type: String,
    example: 'WEB',
  })
  @ApiQuery({
    name: 'ipAddress',
    description: 'Filter by IP address',
    required: false,
    type: String,
    example: '***********',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"entityType","value":"CATEGORY","operator":"eq","type":"select","rowId":"1"},{"id":"activityType","value":"CREATE","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: entityType, activityType, userId, createdAt, duration, affectedCount',
    required: false,
    type: String,
    example: '[{"id":"createdAt","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns paginated activity logs for the user's active business",
    type: PaginatedActivityLogsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAll(
    @Req() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('entityType') entityType?: string,
    @Query('activityType') activityType?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('userId') filterUserId?: string,
    @Query('source') source?: string,
    @Query('ipAddress') ipAddress?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedActivityLogsResponseDto> {
    const currentUserId = req.user.id;
    const businessId = req.user.activeBusinessId;

    return this.activityLogService.findAll(
      currentUserId,
      businessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      entityType,
      activityType,
      from,
      to,
      filterUserId,
      source,
      ipAddress,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('counts')
  @RequirePermissions(Permission.ACTIVITY_LOG_READ)
  @ApiOperation({
    summary: 'Get activity counts by entity type',
    description: 'Retrieves count of activities grouped by entity type',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved activity counts',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'number',
      },
      example: {
        PRODUCT: 145,
        CUSTOMER: 89,
        ORDER: 234,
      },
    },
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async getActivityCounts(
    @Req() req: any,
  ): Promise<Record<EntityType, number>> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.getActivityCounts(businessId);
  }

  @Delete(':id')
  @RequirePermissions(Permission.ACTIVITY_LOG_DELETE)
  @ApiOperation({
    summary: 'Delete activity log',
    description: 'Permanently deletes an activity log entry (use with caution)',
  })
  @ApiParam({
    name: 'id',
    description: 'Activity log ID',
    type: 'string',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Activity log has been successfully deleted',
    type: ActivityLogDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Activity log not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async remove(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<ActivityLogDto> {
    const businessId = req.user.activeBusinessId;
    return this.activityLogService.remove(id, businessId);
  }
}
