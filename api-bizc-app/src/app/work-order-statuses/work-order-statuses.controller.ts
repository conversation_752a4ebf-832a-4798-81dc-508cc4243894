import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { WorkOrderStatusesService } from './work-order-statuses.service';
import { CreateWorkOrderStatusDto } from './dto/create-work-order-status.dto';
import { UpdateWorkOrderStatusDto } from './dto/update-work-order-status.dto';
import { WorkOrderStatusDto } from './dto/work-order-status.dto';
import { WorkOrderStatusSlimDto } from './dto/work-order-status-slim.dto';
import { WorkOrderStatusIdResponseDto } from './dto/work-order-status-id-response.dto';
import { BulkWorkOrderStatusIdsResponseDto } from './dto/bulk-work-order-status-ids-response.dto';
import { BulkCreateWorkOrderStatusDto } from './dto/bulk-create-work-order-status.dto';
import { BulkDeleteWorkOrderStatusDto } from './dto/bulk-delete-work-order-status.dto';
import { BulkDeleteWorkOrderStatusResponseDto } from './dto/bulk-delete-work-order-status-response.dto';
import { BulkUpdateWorkOrderStatusStatusDto } from './dto/bulk-update-work-order-status-status.dto';
import { BulkUpdateWorkOrderStatusStatusResponseDto } from './dto/bulk-update-work-order-status-status-response.dto';
import { UpdateWorkOrderStatusPositionsDto } from './dto/update-work-order-status-positions.dto';
import { UpdateWorkOrderStatusPositionsResponseDto } from './dto/update-work-order-status-positions-response.dto';
import { PaginatedWorkOrderStatusesResponseDto } from './dto/paginated-work-order-statuses-response.dto';
import { WorkOrderStatusNameAvailabilityResponseDto } from './dto/check-work-order-status-name.dto';
import { DeleteWorkOrderStatusResponseDto } from './dto/delete-work-order-status-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('work-order-statuses')
@Controller('work-order-statuses')
@UseGuards(PermissionsGuard)
export class WorkOrderStatusesController {
  constructor(
    private readonly workOrderStatusesService: WorkOrderStatusesService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_CREATE)
  @ApiOperation({
    summary: 'Create a new work order status',
    description: 'Creates a new work order status for the active business',
  })
  @ApiBody({
    description: 'Work order status creation data',
    type: CreateWorkOrderStatusDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Work order status created successfully',
    type: WorkOrderStatusIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Work order status code or name already exists',
  })
  create(
    @Request() req,
    @Body() createWorkOrderStatusDto: CreateWorkOrderStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderStatusIdResponseDto> {
    return this.workOrderStatusesService.create(
      req.user.id,
      req.user.activeBusinessId,
      createWorkOrderStatusDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_CREATE)
  @ApiOperation({
    summary: 'Bulk create work order statuses',
    description: 'Creates multiple work order statuses at once',
  })
  @ApiBody({
    description: 'Bulk work order status creation data',
    type: BulkCreateWorkOrderStatusDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Work order statuses created successfully',
    type: BulkWorkOrderStatusIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Work order status codes or names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateWorkOrderStatusDto: BulkCreateWorkOrderStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkWorkOrderStatusIdsResponseDto> {
    return this.workOrderStatusesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateWorkOrderStatusDto.workOrderStatuses,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_READ)
  @ApiOperation({
    summary: 'Get all work order statuses for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO 8601 format)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'statusCode',
    description: 'Filter by status code',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'statusName',
    description: 'Filter by status name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'statusType',
    description: 'Filter by status type',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isActive',
    description: 'Filter by active status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isDefault',
    description: 'Filter by default status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'description',
    description: 'Filter by description',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort field and direction (e.g., statusName:asc, position:desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Work order statuses retrieved successfully',
    type: PaginatedWorkOrderStatusesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('statusCode') statusCode?: string,
    @Query('statusName') statusName?: string,
    @Query('statusType') statusType?: string,
    @Query('isActive') isActive?: string,
    @Query('isDefault') isDefault?: string,
    @Query('description') description?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedWorkOrderStatusesResponseDto> {
    return this.workOrderStatusesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      statusCode,
      statusName,
      statusType,
      isActive,
      isDefault,
      description,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_READ)
  @ApiOperation({
    summary: 'Get all active work order statuses in slim format',
    description:
      'Returns a simplified list of active work order statuses for dropdowns and selections',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order statuses retrieved successfully',
    type: [WorkOrderStatusSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<WorkOrderStatusSlimDto[]> {
    return this.workOrderStatusesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-name/:statusName')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_READ)
  @ApiOperation({
    summary: 'Check work order status name availability',
    description: 'Checks if a work order status name is available for use',
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: WorkOrderStatusNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkStatusNameAvailability(
    @Request() req,
    @Param('statusName') statusName: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<WorkOrderStatusNameAvailabilityResponseDto> {
    return this.workOrderStatusesService.checkStatusNameAvailability(
      req.user.activeBusinessId,
      statusName,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_READ)
  @ApiOperation({
    summary: 'Get a work order status by ID',
    description: 'Retrieves a specific work order status by its ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order status retrieved successfully',
    type: WorkOrderStatusDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Work order status not found',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<WorkOrderStatusDto> {
    return this.workOrderStatusesService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_UPDATE)
  @ApiOperation({
    summary: 'Update work order status positions',
    description:
      'Updates the display positions of multiple work order statuses',
  })
  @ApiBody({
    description: 'Position update data',
    type: UpdateWorkOrderStatusPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Positions updated successfully',
    type: UpdateWorkOrderStatusPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  updatePositions(
    @Request() req,
    @Body() updatePositionsDto: UpdateWorkOrderStatusPositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateWorkOrderStatusPositionsResponseDto> {
    return this.workOrderStatusesService.updatePositions(
      req.user.id,
      req.user.activeBusinessId,
      updatePositionsDto.positionUpdates,
      metadata,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_UPDATE)
  @ApiOperation({
    summary: 'Bulk update work order status active status',
    description: 'Updates the active status of multiple work order statuses',
  })
  @ApiBody({
    description: 'Bulk status update data',
    type: BulkUpdateWorkOrderStatusStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Work order statuses updated successfully',
    type: BulkUpdateWorkOrderStatusStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Some work order statuses not found',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateStatusDto: BulkUpdateWorkOrderStatusStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateWorkOrderStatusStatusResponseDto> {
    return this.workOrderStatusesService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.workOrderStatusIds,
      bulkUpdateStatusDto.isActive,
      metadata,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_UPDATE)
  @ApiOperation({
    summary: 'Update a work order status',
    description: 'Updates an existing work order status',
  })
  @ApiBody({
    description: 'Work order status update data',
    type: UpdateWorkOrderStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Work order status updated successfully',
    type: WorkOrderStatusIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Work order status not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Work order status code or name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateWorkOrderStatusDto: UpdateWorkOrderStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<WorkOrderStatusIdResponseDto> {
    return this.workOrderStatusesService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateWorkOrderStatusDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_DELETE)
  @ApiOperation({ summary: 'Bulk delete work order statuses' })
  @ApiBody({
    description: 'Array of work order status IDs to delete',
    type: BulkDeleteWorkOrderStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Work order statuses deleted successfully',
    type: BulkDeleteWorkOrderStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Some work order statuses not found',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteDto: BulkDeleteWorkOrderStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteWorkOrderStatusResponseDto> {
    return this.workOrderStatusesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteDto.ids,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.WORK_ORDER_STATUS_DELETE)
  @ApiOperation({
    summary: 'Delete a work order status',
    description: 'Soft deletes a work order status by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Work order status deleted successfully',
    type: DeleteWorkOrderStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Work order status not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteWorkOrderStatusResponseDto> {
    return this.workOrderStatusesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
