import { Module, Provider } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CoreModule } from '../core/core.module';
import { AuthModule } from './auth/auth.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { UsersModule } from './users/users.module';
import { AiChatModule } from './ai-chat/ai-chat.module';
import { SmsModule } from './sms/sms.module';
import { DrizzleModule } from './drizzle/drizzle.module';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { BusinessModule } from './business/business.module';
import { WarrantyTemplatesModule } from './warranty-templates/warranty-templates.module';
import { CategoriesModule } from './categories/categories.module';
import { GamesModule } from './games/games.module';
import { VehicleCategoriesModule } from './vehicle-categories/vehicle-categories.module';
import { VehiclesModule } from './vehicles/vehicles.module';
import { VehicleFinesModule } from './vehicle-fines/vehicle-fines.module';
import { VehicleBlockedPeriodsModule } from './vehicle-blocked-periods/vehicle-blocked-periods.module';
import { AssetDamagesModule } from './asset-damages/asset-damages.module';
import { ServicesModule } from './services/services.module';
import { ServiceCategoriesModule } from './service-categories/service-categories.module';
import { ServiceOrderPrioritiesModule } from './service-order-priorities/service-order-priorities.module';
import { ServiceOrderStatusesModule } from './service-order-statuses/service-order-statuses.module';
import { ServiceOrdersModule } from './service-orders/service-orders.module';
import { ServiceTimeSlotsModule } from './service-time-slots/service-time-slots.module';
import { WorkOrderPrioritiesModule } from './work-order-priorities/work-order-priorities.module';
import { WorkOrderStatusesModule } from './work-order-statuses/work-order-statuses.module';
import { WorkOrdersModule } from './work-orders/work-orders.module';
import { BusinessRolesModule } from './business-roles/business-roles.module';
import { DesignationsModule } from './designations/designations.module';
import { LocationsModule } from './locations/locations.module';
import { DemoAppointmentsModule } from './demo-appointments/demo-appointments.module';
import { AppointmentsModule } from './appointments/appointments.module';
import { ModifiersModule } from './modifiers/modifiers.module';
import { GcsUploadModule } from './gcs-upload/gcs-upload.module';
import { CustomFieldsModule } from './custom-fields/custom-fields.module';
import { PaymentAccountTypesModule } from './payment-account-types/payment-account-types.module';
import { AssetTypesModule } from './asset-types/asset-types.module';
import { AssetTransactionsModule } from './asset-transactions/asset-transactions.module';
import { AssetsModule } from './assets/assets.module';
import { DepartmentsModule } from './departments/departments.module';
import { CustomerGroupsModule } from './customer-groups/customer-groups.module';
import { TemplatesModule } from './templates/templates.module';
import { ProvidersModule } from './providers/providers.module';
import { BrandsModule } from './brands/brands.module';
// staff
import { StaffModule } from './staff/staff.module';
import { SuppliersModule } from './suppliers/suppliers.module';
import { TaxesModule } from './taxes/taxes.module';
import { AccountsModule } from './accounts/accounts.module';
import { CustomersModule } from './customers/customers.module';
import { ProjectsModule } from './projects/projects.module';
import { TasksModule } from './tasks/tasks.module';
import { LeadsModule } from './leads/leads.module';
import { UnifiedSearchModule } from './unified-search/unified-search.module';
import { DiscountPlansModule } from './discount-plans/discount-plans.module';
import { SubscriptionPlansModule } from './subscription-plans/subscription-plans.module';
import { SubscriptionModule } from './subscriptions/subscription.module';
import { ReservationTypesModule } from './reservation-types/reservation-types.module';
import { PromoCodesModule } from './promo-codes/promo-codes.module';
import { ReferralsModule } from './referrals/referrals.module';
import { PaymentMethodsModule } from './payment-methods/payment-methods.module';
import { MeetingsModule } from './meetings/meetings.module';
import { NewsletterSubscribersModule } from './newsletter-subscribers/newsletter-subscribers.module';
import { UnitsModule } from './units/units.module';
import { AttendanceModule } from './attendance/attendance.module';
import { LeaveTypesModule } from './leave-types/leave-types.module';
import { LeaveRequestsModule } from './leave-requests/leave-requests.module';
import { LeaveBalancesModule } from './leave-balances/leave-balances.module';
import { RestaurantTablesModule } from './restaurant-tables/restaurant-tables.module';
import { FloorPlansModule } from './floor-plans/floor-plans.module';
import { DeductionTypesModule } from './deduction-types/deduction-types.module';
import { TimeSlotsModule } from './time-slots/time-slots.module';
import { MenuItemsModule } from './menu-items/menu-items.module';
import { BillOfMaterialsModule } from './bill-of-materials/bill-of-materials.module';
import { AccommodationUnitsModule } from './accommodation-units/accommodation-units.module';
import { EventSpacesModule } from './event-spaces/event-spaces.module';
import { PackagesModule } from './packages/packages.module';
import { PackageReservationsModule } from './package-reservations/package-reservations.module';
import { HousekeepingModule } from './housekeeping/housekeeping.module';
import { ExpenseCategoriesModule } from './expenses-categories/expenses-categories.module';
import { EmployeeSalariesModule } from './employee-salaries/employee-salaries.module';
import { ActivityLogModule } from './activity-log/activity-log.module';
import { ServiceOrderTypesModule } from './service-order-types/service-order-types.module';

const globalProviders: Provider[] = [
  {
    provide: APP_GUARD,
    useClass: JwtAuthGuard,
  },
];

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    CoreModule,
    AuthModule,
    ThrottlerModule.forRoot([{ ttl: 60, limit: 10 }]),
    UsersModule,
    AiChatModule,
    SmsModule,
    DrizzleModule,
    BusinessModule,
    WarrantyTemplatesModule,
    CategoriesModule,
    GamesModule,
    VehicleCategoriesModule,
    VehiclesModule,
    VehicleFinesModule,
    VehicleBlockedPeriodsModule,
    AssetDamagesModule,
    ServicesModule,
    ServiceCategoriesModule,
    ServiceOrderPrioritiesModule,
    ServiceOrderStatusesModule,
    ServiceOrdersModule,
    ServiceTimeSlotsModule,
    WorkOrderPrioritiesModule,
    WorkOrderStatusesModule,
    WorkOrdersModule,
    BusinessRolesModule,
    DesignationsModule,
    LocationsModule,
    DemoAppointmentsModule,
    AppointmentsModule,
    ModifiersModule,
    GcsUploadModule,
    CustomFieldsModule,
    // RedisModule,
    PaymentAccountTypesModule,
    AssetTypesModule,
    AssetTransactionsModule,
    AssetsModule,
    DepartmentsModule,
    CustomerGroupsModule,
    TemplatesModule,
    ProvidersModule,
    BrandsModule,
    StaffModule,
    SuppliersModule,
    TaxesModule,
    AccountsModule,
    CustomersModule,
    ProjectsModule,
    TasksModule,
    LeadsModule,
    UnifiedSearchModule,
    DiscountPlansModule,
    SubscriptionPlansModule,
    SubscriptionModule,
    PromoCodesModule,
    ReferralsModule,
    MeetingsModule,
    NewsletterSubscribersModule,
    UnitsModule,
    AttendanceModule,
    LeaveTypesModule,
    LeaveRequestsModule,
    LeaveBalancesModule,
    RestaurantTablesModule,
    DeductionTypesModule,
    TimeSlotsModule,
    MenuItemsModule,
    BillOfMaterialsModule,
    AccommodationUnitsModule,
    EventSpacesModule,
    PackagesModule,
    PackageReservationsModule,
    HousekeepingModule,
    ExpenseCategoriesModule,
    EmployeeSalariesModule,
    // Add more modules as needed
    ServicesModule,
    ServiceCategoriesModule,
    ReservationTypesModule,
    TaxesModule,
    PaymentMethodsModule,

    FloorPlansModule,
    ActivityLogModule,
    ServiceOrderStatusesModule,
    ServiceOrderPrioritiesModule,
    ServiceOrdersModule,
    ServiceTimeSlotsModule,
    WorkOrderPrioritiesModule,
    WorkOrderStatusesModule,
    WorkOrdersModule,
    ServiceOrderTypesModule,
  ],
  controllers: [],
  providers: [...globalProviders],
})
export class AppModule {}
