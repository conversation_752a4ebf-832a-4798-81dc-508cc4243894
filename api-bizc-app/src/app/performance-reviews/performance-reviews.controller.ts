import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { PerformanceReviewsService } from './performance-reviews.service';
import { CreatePerformanceReviewDto } from './dto/create-performance-review.dto';
import { UpdatePerformanceReviewDto } from './dto/update-performance-review.dto';
import { PerformanceReviewDto } from './dto/performance-review.dto';
import { PerformanceReviewListDto } from './dto/performance-review-list.dto';
import { PerformanceReviewIdResponseDto } from './dto/performance-review-id-response.dto';
import { BulkDeletePerformanceReviewDto } from './dto/bulk-delete-performance-review.dto';
import { BulkDeletePerformanceReviewResponseDto } from './dto/bulk-delete-performance-review-response.dto';
import { PaginatedPerformanceReviewsResponseDto } from './dto/paginated-performance-reviews-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('performance-reviews')
@Controller('performance-reviews')
@UseGuards(PermissionsGuard)
export class PerformanceReviewsController {
  constructor(
    private readonly performanceReviewsService: PerformanceReviewsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_CREATE)
  @ApiOperation({ summary: 'Create a new performance review' })
  @ApiResponse({
    status: 201,
    description: 'Performance review created successfully',
    type: PerformanceReviewIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - performance review already exists',
  })
  async create(
    @Request() req: any,
    @Body() createPerformanceReviewDto: CreatePerformanceReviewDto,
  ): Promise<PerformanceReviewIdResponseDto> {
    const result = await this.performanceReviewsService.create(
      req.user.id,
      req.user.activeBusinessId,
      createPerformanceReviewDto,
    );
    return result;
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_READ)
  @ApiOperation({
    summary: 'Get all performance reviews with pagination and filtering',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'from',
    required: false,
    type: String,
    description: 'Filter by review period start date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'to',
    required: false,
    type: String,
    description: 'Filter by review period end date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'staffId',
    required: false,
    type: String,
    description: 'Filter by staff member ID',
  })
  @ApiQuery({
    name: 'reviewerId',
    required: false,
    type: String,
    description: 'Filter by reviewer ID',
  })
  @ApiQuery({
    name: 'reviewType',
    required: false,
    type: String,
    description: 'Filter by review type',
    example: 'annual',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by review status',
    example: 'draft',
  })
  @ApiQuery({
    name: 'sort',
    required: false,
    type: String,
    description: 'Sort order',
    example: 'createdAt_desc',
    enum: [
      'reviewPeriodStart_asc',
      'reviewPeriodStart_desc',
      'reviewPeriodEnd_asc',
      'reviewPeriodEnd_desc',
      'overallRating_asc',
      'overallRating_desc',
      'status_asc',
      'status_desc',
      'createdAt_asc',
      'createdAt_desc',
    ],
  })
  @ApiResponse({
    status: 200,
    description: 'Performance reviews retrieved successfully',
    type: PaginatedPerformanceReviewsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('staffId') staffId?: string,
    @Query('reviewerId') reviewerId?: string,
    @Query('reviewType') reviewType?: string,
    @Query('status') status?: string,
    @Query('sort') sort?: string,
  ): Promise<PaginatedPerformanceReviewsResponseDto> {
    const result = await this.performanceReviewsService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      undefined, // search parameter
      staffId,
      from,
      to,
    );

    // Transform PerformanceReviewDto[] to PerformanceReviewListDto[]
    const transformedData = result.data.map((review) => ({
      id: review.id,
      staffName: review.staffName,
      reviewerName: review.createdBy, // Use createdBy as a fallback for reviewerName
      reviewPeriodStart: review.reviewPeriodStart,
      reviewPeriodEnd: review.reviewPeriodEnd,
      reviewType: review.reviewType,
      overallRating: review.overallRating,
      goalsAchievement: review.goalsAchievement,
      status: review.status,
      createdAt: review.createdAt,
      updatedAt: review.updatedAt,
    }));

    return {
      data: transformedData,
      meta: result.meta,
    };
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_READ)
  @ApiOperation({ summary: 'Get a performance review by ID' })
  @ApiParam({
    name: 'id',
    description: 'Performance review ID',
    type: String,
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Performance review retrieved successfully',
    type: PerformanceReviewDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Performance review not found',
  })
  async findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<PerformanceReviewDto> {
    return this.performanceReviewsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_UPDATE)
  @ApiOperation({ summary: 'Update a performance review' })
  @ApiParam({
    name: 'id',
    description: 'Performance review ID',
    type: String,
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Performance review updated successfully',
    type: PerformanceReviewIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Performance review not found',
  })
  async update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updatePerformanceReviewDto: UpdatePerformanceReviewDto,
  ): Promise<PerformanceReviewIdResponseDto> {
    const result = await this.performanceReviewsService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updatePerformanceReviewDto,
    );
    return result;
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_DELETE)
  @ApiOperation({ summary: 'Delete a performance review' })
  @ApiParam({
    name: 'id',
    description: 'Performance review ID',
    type: String,
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Performance review deleted successfully',
    type: PerformanceReviewIdResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Performance review not found',
  })
  async remove(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<PerformanceReviewIdResponseDto> {
    const result = await this.performanceReviewsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
    return { id: result.deletedId };
  }

  @Post('bulk-delete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_DELETE)
  @ApiOperation({ summary: 'Bulk delete performance reviews' })
  @ApiResponse({
    status: 200,
    description: 'Performance reviews deleted successfully',
    type: BulkDeletePerformanceReviewResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - validation failed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No performance reviews found to delete',
  })
  async bulkDelete(
    @Request() req: any,
    @Body() bulkDeleteDto: BulkDeletePerformanceReviewDto,
  ): Promise<BulkDeletePerformanceReviewResponseDto> {
    const result = await this.performanceReviewsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteDto.ids,
    );
    return {
      deletedCount: result.deleted,
      deletedIds: result.deletedIds,
    };
  }

  @Get('staff/:staffId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_READ)
  @ApiOperation({
    summary: 'Get performance reviews for a specific staff member',
  })
  @ApiParam({
    name: 'staffId',
    description: 'Staff member ID',
    type: String,
    format: 'uuid',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Maximum number of reviews to return (default: 10)',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Performance reviews retrieved successfully',
    type: [PerformanceReviewListDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async getByStaff(
    @Request() req: any,
    @Param('staffId') staffId: string,
    @Query('limit') limit?: number,
  ): Promise<PerformanceReviewListDto[]> {
    return this.performanceReviewsService.getPerformanceReviewsByStaff(
      req.user.id,
      req.user.activeBusinessId,
      staffId,
      limit,
    );
  }

  @Get('reviewer/:reviewerId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PERFORMANCE_REVIEW_READ)
  @ApiOperation({
    summary: 'Get performance reviews conducted by a specific reviewer',
  })
  @ApiParam({
    name: 'reviewerId',
    description: 'Reviewer ID',
    type: String,
    format: 'uuid',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Maximum number of reviews to return (default: 10)',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Performance reviews retrieved successfully',
    type: [PerformanceReviewListDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - insufficient permissions',
  })
  async getByReviewer(
    @Request() req: any,
    @Param('reviewerId') reviewerId: string,
    @Query('limit') limit?: number,
  ): Promise<PerformanceReviewListDto[]> {
    return this.performanceReviewsService.getPerformanceReviewsByReviewer(
      req.user.id,
      req.user.activeBusinessId,
      reviewerId,
      limit,
    );
  }
}
