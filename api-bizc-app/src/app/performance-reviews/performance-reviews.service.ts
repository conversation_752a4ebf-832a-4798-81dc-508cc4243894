import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreatePerformanceReviewDto } from './dto/create-performance-review.dto';
import { UpdatePerformanceReviewDto } from './dto/update-performance-review.dto';
import { PerformanceReviewDto } from './dto/performance-review.dto';
import { PerformanceReviewSlimDto } from './dto/performance-review-slim.dto';
import { PerformanceReviewListDto } from './dto/performance-review-list.dto';
import { performanceReviews } from '../drizzle/schema/performance-reviews.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { eq, and, ilike, sql, gte, lte, desc, inArray, or } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class PerformanceReviewsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPerformanceReviewDto: CreatePerformanceReviewDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate that the staff member belongs to the business
      if (createPerformanceReviewDto.staffId) {
        const staffMember = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createPerformanceReviewDto.staffId),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'Staff member not found or does not belong to this business',
          );
        }
      }

      // Validate reviewer if provided
      if (createPerformanceReviewDto.reviewerId) {
        const reviewer = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createPerformanceReviewDto.reviewerId),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!reviewer) {
          throw new BadRequestException(
            'Reviewer not found or does not belong to this business',
          );
        }
      }

      // Check for duplicate review period for the same staff member
      if (createPerformanceReviewDto.staffId) {
        const existingReview = await this.db
          .select({ id: performanceReviews.id })
          .from(performanceReviews)
          .where(
            and(
              eq(performanceReviews.businessId, businessId),
              eq(
                performanceReviews.staffId,
                createPerformanceReviewDto.staffId,
              ),
              eq(
                performanceReviews.reviewPeriodStart,
                createPerformanceReviewDto.reviewPeriodStart,
              ),
              eq(
                performanceReviews.reviewPeriodEnd,
                createPerformanceReviewDto.reviewPeriodEnd,
              ),
              eq(performanceReviews.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingReview) {
          throw new ConflictException(
            'A performance review already exists for this staff member in the specified period',
          );
        }
      }

      // Create the performance review
      const [newPerformanceReview] = await this.db
        .insert(performanceReviews)
        .values({
          businessId,
          staffId: createPerformanceReviewDto.staffId,
          reviewerId: createPerformanceReviewDto.reviewerId,
          reviewPeriodStart: createPerformanceReviewDto.reviewPeriodStart,
          reviewPeriodEnd: createPerformanceReviewDto.reviewPeriodEnd,
          reviewType: createPerformanceReviewDto.reviewType,
          overallRating: createPerformanceReviewDto.overallRating?.toString(),
          goalsAchievement: createPerformanceReviewDto.goalsAchievement,
          strengths: createPerformanceReviewDto.strengths,
          areasForImprovement: createPerformanceReviewDto.areasForImprovement,
          developmentPlan: createPerformanceReviewDto.developmentPlan,
          status: createPerformanceReviewDto.status,
          createdBy: userId,
        })
        .returning();

      // Log the performance review creation activity
      await this.activityLogService.logCreate(
        newPerformanceReview.id,
        EntityType.PERFORMANCE_REVIEW,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id: newPerformanceReview.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create performance review: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    search?: string,
    staffId?: string,
    from?: string,
    to?: string,
  ): Promise<{
    data: PerformanceReviewDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(performanceReviews.businessId, businessId),
      eq(performanceReviews.isDeleted, false),
    ];

    // Add search conditions
    if (search) {
      whereConditions.push(
        or(
          ilike(performanceReviews.strengths, `%${search}%`),
          ilike(performanceReviews.areasForImprovement, `%${search}%`),
          ilike(performanceReviews.developmentPlan, `%${search}%`),
        ),
      );
    }

    // Add staff filter
    if (staffId) {
      whereConditions.push(eq(performanceReviews.staffId, staffId));
    }

    // Add date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(
          gte(
            performanceReviews.reviewPeriodStart,
            fromDate.toISOString().split('T')[0],
          ),
        );
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(
          lte(
            performanceReviews.reviewPeriodEnd,
            toDate.toISOString().split('T')[0],
          ),
        );
      }
    }

    // Get performance reviews with staff and user information
    const result = await this.db
      .select({
        id: performanceReviews.id,
        businessId: performanceReviews.businessId,
        staffId: performanceReviews.staffId,
        reviewerId: performanceReviews.reviewerId,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        reviewType: performanceReviews.reviewType,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        goalsAchievement: performanceReviews.goalsAchievement,
        strengths: performanceReviews.strengths,
        areasForImprovement: performanceReviews.areasForImprovement,
        developmentPlan: performanceReviews.developmentPlan,
        status: performanceReviews.status,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: sql<string>`${performanceReviews.createdAt}::text`,
        updatedAt: sql<string>`${performanceReviews.updatedAt}::text`,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .leftJoin(users, eq(performanceReviews.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(performanceReviews.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(
        desc(performanceReviews.reviewPeriodEnd),
        desc(performanceReviews.createdAt),
      )
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(performanceReviews)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
    staffId?: string,
  ): Promise<PerformanceReviewSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(performanceReviews.businessId, businessId),
      eq(performanceReviews.isDeleted, false),
    ];

    if (staffId) {
      whereConditions.push(eq(performanceReviews.staffId, staffId));
    }

    const result = await this.db
      .select({
        id: performanceReviews.id,
        staffId: performanceReviews.staffId,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        status: performanceReviews.status,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .where(and(...whereConditions))
      .orderBy(
        desc(performanceReviews.reviewPeriodEnd),
        desc(performanceReviews.createdAt),
      );

    return result;
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<PerformanceReviewDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: performanceReviews.id,
        businessId: performanceReviews.businessId,
        staffId: performanceReviews.staffId,
        reviewerId: performanceReviews.reviewerId,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        reviewType: performanceReviews.reviewType,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        goalsAchievement: performanceReviews.goalsAchievement,
        strengths: performanceReviews.strengths,
        areasForImprovement: performanceReviews.areasForImprovement,
        developmentPlan: performanceReviews.developmentPlan,
        status: performanceReviews.status,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: sql<string>`${performanceReviews.createdAt}::text`,
        updatedAt: sql<string>`${performanceReviews.updatedAt}::text`,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .leftJoin(users, eq(performanceReviews.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(performanceReviews.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(performanceReviews.id, id),
          eq(performanceReviews.businessId, businessId),
          eq(performanceReviews.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(`Performance review with ID ${id} not found`);
    }

    return result;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePerformanceReviewDto: UpdatePerformanceReviewDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if performance review exists and belongs to the business
      const existingPerformanceReview = await this.db
        .select()
        .from(performanceReviews)
        .where(
          and(
            eq(performanceReviews.id, id),
            eq(performanceReviews.businessId, businessId),
            eq(performanceReviews.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPerformanceReview) {
        throw new NotFoundException(
          `Performance review with ID ${id} not found`,
        );
      }

      // Validate staff if being updated
      if (
        updatePerformanceReviewDto.staffId &&
        updatePerformanceReviewDto.staffId !== existingPerformanceReview.staffId
      ) {
        const staffMember = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updatePerformanceReviewDto.staffId),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'Staff member not found or does not belong to this business',
          );
        }
      }

      // Validate reviewer if being updated
      if (
        updatePerformanceReviewDto.reviewerId &&
        updatePerformanceReviewDto.reviewerId !==
          existingPerformanceReview.reviewerId
      ) {
        const reviewer = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updatePerformanceReviewDto.reviewerId),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!reviewer) {
          throw new BadRequestException(
            'Reviewer not found or does not belong to this business',
          );
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update fields that are provided
      if (updatePerformanceReviewDto.staffId !== undefined)
        updateData.staffId = updatePerformanceReviewDto.staffId;
      if (updatePerformanceReviewDto.reviewerId !== undefined)
        updateData.reviewerId = updatePerformanceReviewDto.reviewerId;
      if (updatePerformanceReviewDto.reviewPeriodStart !== undefined)
        updateData.reviewPeriodStart =
          updatePerformanceReviewDto.reviewPeriodStart;
      if (updatePerformanceReviewDto.reviewPeriodEnd !== undefined)
        updateData.reviewPeriodEnd = updatePerformanceReviewDto.reviewPeriodEnd;
      if (updatePerformanceReviewDto.reviewType !== undefined)
        updateData.reviewType = updatePerformanceReviewDto.reviewType;
      if (updatePerformanceReviewDto.overallRating !== undefined)
        updateData.overallRating =
          updatePerformanceReviewDto.overallRating?.toString();
      if (updatePerformanceReviewDto.goalsAchievement !== undefined)
        updateData.goalsAchievement =
          updatePerformanceReviewDto.goalsAchievement;
      if (updatePerformanceReviewDto.strengths !== undefined)
        updateData.strengths = updatePerformanceReviewDto.strengths;
      if (updatePerformanceReviewDto.areasForImprovement !== undefined)
        updateData.areasForImprovement =
          updatePerformanceReviewDto.areasForImprovement;
      if (updatePerformanceReviewDto.developmentPlan !== undefined)
        updateData.developmentPlan = updatePerformanceReviewDto.developmentPlan;
      if (updatePerformanceReviewDto.status !== undefined)
        updateData.status = updatePerformanceReviewDto.status;

      // Update the performance review
      await this.db
        .update(performanceReviews)
        .set(updateData)
        .where(eq(performanceReviews.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PERFORMANCE_REVIEW,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update performance review: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; deletedId: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if performance review exists and belongs to the business
      const existingPerformanceReview = await this.db
        .select({ id: performanceReviews.id })
        .from(performanceReviews)
        .where(
          and(
            eq(performanceReviews.id, id),
            eq(performanceReviews.businessId, businessId),
            eq(performanceReviews.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPerformanceReview) {
        throw new NotFoundException(
          `Performance review with ID ${id} not found`,
        );
      }

      // Soft delete the performance review
      await this.db
        .update(performanceReviews)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(performanceReviews.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        id,
        EntityType.PERFORMANCE_REVIEW,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Performance review deleted successfully',
        deletedId: id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete performance review: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    performanceReviewIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!performanceReviewIds || performanceReviewIds.length === 0) {
        throw new BadRequestException(
          'No performance review IDs provided for deletion',
        );
      }

      // Get all performance reviews that exist and belong to the business
      const existingPerformanceReviews = await this.db
        .select({
          id: performanceReviews.id,
          businessId: performanceReviews.businessId,
        })
        .from(performanceReviews)
        .where(
          and(
            inArray(performanceReviews.id, performanceReviewIds),
            eq(performanceReviews.businessId, businessId),
            eq(performanceReviews.isDeleted, false),
          ),
        );

      if (existingPerformanceReviews.length === 0) {
        throw new NotFoundException(
          'No valid performance reviews found for deletion',
        );
      }

      const deletedIds: string[] = [];
      const now = new Date();

      // Perform soft delete for each performance review
      for (const performanceReview of existingPerformanceReviews) {
        await this.db
          .update(performanceReviews)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: now,
          })
          .where(eq(performanceReviews.id, performanceReview.id));

        // Log the performance review deletion activity
        await this.activityLogService.logDelete(
          performanceReview.id,
          EntityType.PERFORMANCE_REVIEW,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );

        deletedIds.push(performanceReview.id);
      }

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} performance reviews`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete performance reviews: ${error.message}`,
      );
    }
  }

  async findByStaff(
    userId: string,
    businessId: string | null,
    staffId: string,
    page = 1,
    limit = 10,
  ): Promise<{
    data: PerformanceReviewDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const whereConditions = [
      eq(performanceReviews.businessId, businessId),
      eq(performanceReviews.staffId, staffId),
      eq(performanceReviews.isDeleted, false),
    ];

    // Get performance reviews for specific staff member
    const result = await this.db
      .select({
        id: performanceReviews.id,
        businessId: performanceReviews.businessId,
        staffId: performanceReviews.staffId,
        reviewerId: performanceReviews.reviewerId,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        reviewType: performanceReviews.reviewType,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        goalsAchievement: performanceReviews.goalsAchievement,
        strengths: performanceReviews.strengths,
        areasForImprovement: performanceReviews.areasForImprovement,
        developmentPlan: performanceReviews.developmentPlan,
        status: performanceReviews.status,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: sql<string>`${performanceReviews.createdAt}::text`,
        updatedAt: sql<string>`${performanceReviews.updatedAt}::text`,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .leftJoin(users, eq(performanceReviews.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(performanceReviews.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(
        desc(performanceReviews.reviewPeriodEnd),
        desc(performanceReviews.createdAt),
      )
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(performanceReviews)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findByReviewer(
    userId: string,
    businessId: string | null,
    reviewerId: string,
    page = 1,
    limit = 10,
  ): Promise<{
    data: PerformanceReviewDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const whereConditions = [
      eq(performanceReviews.businessId, businessId),
      eq(performanceReviews.reviewerId, reviewerId),
      eq(performanceReviews.isDeleted, false),
    ];

    // Get performance reviews by specific reviewer
    const result = await this.db
      .select({
        id: performanceReviews.id,
        businessId: performanceReviews.businessId,
        staffId: performanceReviews.staffId,
        reviewerId: performanceReviews.reviewerId,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        reviewType: performanceReviews.reviewType,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        goalsAchievement: performanceReviews.goalsAchievement,
        strengths: performanceReviews.strengths,
        areasForImprovement: performanceReviews.areasForImprovement,
        developmentPlan: performanceReviews.developmentPlan,
        status: performanceReviews.status,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: sql<string>`${performanceReviews.createdAt}::text`,
        updatedAt: sql<string>`${performanceReviews.updatedAt}::text`,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .leftJoin(users, eq(performanceReviews.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(performanceReviews.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(
        desc(performanceReviews.reviewPeriodEnd),
        desc(performanceReviews.createdAt),
      )
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(performanceReviews)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: result,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async getPerformanceReviewsByStaff(
    userId: string,
    businessId: string | null,
    staffId: string,
    limit?: number,
  ): Promise<PerformanceReviewListDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(performanceReviews.businessId, businessId),
      eq(performanceReviews.staffId, staffId),
      eq(performanceReviews.isDeleted, false),
    ];

    const result = await this.db
      .select({
        id: performanceReviews.id,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewerName: sql<string>`COALESCE(reviewer.first_name || ' ' || reviewer.last_name, reviewer.email)`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        reviewType: performanceReviews.reviewType,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        goalsAchievement: performanceReviews.goalsAchievement,
        status: performanceReviews.status,
        createdAt: sql<string>`${performanceReviews.createdAt}::text`,
        updatedAt: sql<string>`${performanceReviews.updatedAt}::text`,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .leftJoin(
        sql`${staffMembers} AS reviewer`,
        eq(performanceReviews.reviewerId, sql`reviewer.id`),
      )
      .where(and(...whereConditions))
      .orderBy(
        desc(performanceReviews.reviewPeriodEnd),
        desc(performanceReviews.createdAt),
      )
      .limit(limit || 10);

    return result;
  }

  async getPerformanceReviewsByReviewer(
    userId: string,
    businessId: string | null,
    reviewerId: string,
    limit?: number,
  ): Promise<PerformanceReviewListDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(performanceReviews.businessId, businessId),
      eq(performanceReviews.reviewerId, reviewerId),
      eq(performanceReviews.isDeleted, false),
    ];

    const result = await this.db
      .select({
        id: performanceReviews.id,
        staffName: sql<string>`COALESCE(${staffMembers.firstName} || ' ' || ${staffMembers.lastName}, ${staffMembers.email})`,
        reviewerName: sql<string>`COALESCE(reviewer.first_name || ' ' || reviewer.last_name, reviewer.email)`,
        reviewPeriodStart: performanceReviews.reviewPeriodStart,
        reviewPeriodEnd: performanceReviews.reviewPeriodEnd,
        reviewType: performanceReviews.reviewType,
        overallRating: sql<number>`CASE WHEN ${performanceReviews.overallRating} IS NULL THEN NULL ELSE CAST(${performanceReviews.overallRating} AS NUMERIC) END`,
        goalsAchievement: performanceReviews.goalsAchievement,
        status: performanceReviews.status,
        createdAt: sql<string>`${performanceReviews.createdAt}::text`,
        updatedAt: sql<string>`${performanceReviews.updatedAt}::text`,
      })
      .from(performanceReviews)
      .leftJoin(staffMembers, eq(performanceReviews.staffId, staffMembers.id))
      .leftJoin(
        sql`${staffMembers} AS reviewer`,
        eq(performanceReviews.reviewerId, sql`reviewer.id`),
      )
      .where(and(...whereConditions))
      .orderBy(
        desc(performanceReviews.reviewPeriodEnd),
        desc(performanceReviews.createdAt),
      )
      .limit(limit || 10);

    return result;
  }
}
