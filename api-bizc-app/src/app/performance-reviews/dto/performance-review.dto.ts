import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReviewType, ReviewStatus, GoalsAchievement } from '../../shared/types';

export class PerformanceReviewDto {
  @ApiProperty({
    description: 'Unique identifier for the performance review',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Business ID the performance review belongs to',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  businessId: string;

  @ApiProperty({
    description: 'Staff member ID being reviewed',
    example: '123e4567-e89b-12d3-a456-426614174002',
  })
  staffId: string;

  @ApiProperty({
    description: 'Reviewer staff member ID',
    example: '123e4567-e89b-12d3-a456-426614174003',
  })
  reviewerId: string;

  @ApiProperty({
    description: 'Review period start date',
    example: '2024-01-01',
  })
  reviewPeriodStart: string;

  @ApiProperty({
    description: 'Review period end date',
    example: '2024-12-31',
  })
  reviewPeriodEnd: string;

  @ApiProperty({
    enum: ReviewType,
    enumName: 'ReviewType',
    description: 'Type of performance review',
    example: ReviewType.ANNUAL,
  })
  reviewType: ReviewType;

  @ApiPropertyOptional({
    description: 'Overall rating (0.00 to 5.00)',
    example: 4.25,
    nullable: true,
  })
  overallRating?: number;

  @ApiPropertyOptional({
    enum: GoalsAchievement,
    enumName: 'GoalsAchievement',
    description: 'Level of goals achievement',
    example: GoalsAchievement.MEETS_EXPECTATIONS,
    nullable: true,
  })
  goalsAchievement?: GoalsAchievement;

  @ApiPropertyOptional({
    description: 'Employee strengths and positive feedback',
    example: 'Excellent communication skills and leadership qualities',
    nullable: true,
  })
  strengths?: string;

  @ApiPropertyOptional({
    description: 'Areas identified for improvement',
    example: 'Could benefit from additional technical training',
    nullable: true,
  })
  areasForImprovement?: string;

  @ApiPropertyOptional({
    description: 'Development plan and next steps',
    example: 'Enroll in advanced project management course by Q2',
    nullable: true,
  })
  developmentPlan?: string;

  @ApiProperty({
    enum: ReviewStatus,
    enumName: 'ReviewStatus',
    description: 'Review status',
    example: ReviewStatus.DRAFT,
  })
  status: ReviewStatus;

  @ApiProperty({
    description: 'Staff member name',
    example: 'John Doe',
  })
  staffName: string;

  @ApiPropertyOptional({
    description: 'User who created the review',
    example: 'Jane Smith',
    nullable: true,
  })
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'User who last updated the review',
    example: 'John Manager',
    nullable: true,
  })
  updatedBy?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt: string;
}
