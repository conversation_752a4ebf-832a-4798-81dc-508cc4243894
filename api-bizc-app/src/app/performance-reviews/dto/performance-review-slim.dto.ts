import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PerformanceReviewSlimDto {
  @ApiProperty({
    description: 'Unique identifier for the performance review',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Staff member ID being reviewed',
    example: '123e4567-e89b-12d3-a456-426614174002',
    nullable: true,
  })
  staffId?: string;

  @ApiProperty({
    description: 'Staff member name',
    example: '<PERSON>',
  })
  staffName: string;

  @ApiProperty({
    description: 'Review period start date',
    example: '2024-01-01',
  })
  reviewPeriodStart: string;

  @ApiProperty({
    description: 'Review period end date',
    example: '2024-12-31',
  })
  reviewPeriodEnd: string;

  @ApiPropertyOptional({
    description: 'Overall rating (0.00 to 5.00)',
    example: 4.25,
    nullable: true,
  })
  overallRating?: number;

  @ApiProperty({
    description: 'Review status',
    example: 'draft',
  })
  status: string;
}
