import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateBankAccountDto } from './dto/create-bank-account.dto';
import { UpdateBankAccountDto } from './dto/update-bank-account.dto';
import { BankAccountDto } from './dto/bank-account.dto';
import { bankAccounts } from '../drizzle/schema/bank-accounts.schema';
import { eq, and, isNull } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import {
  BankAccountType,
  CurrencyCode,
  BankAccountEntityType,
} from '../shared/types';

@Injectable()
export class BankAccountsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    createBankAccountDto: CreateBankAccountDto,
    metadata?: ActivityMetadata,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string }> {
    try {
      const dbInstance = tx || this.db;

      // Check for duplicate bank account (same bank name + account number within business)
      const existingBankAccount = await dbInstance
        .select()
        .from(bankAccounts)
        .where(
          and(
            eq(bankAccounts.businessId, createBankAccountDto.businessId),
            eq(bankAccounts.bankName, createBankAccountDto.bankName),
            eq(bankAccounts.accountNumber, createBankAccountDto.accountNumber),
            isNull(bankAccounts.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingBankAccount) {
        throw new ConflictException(
          `A bank account with bank name '${createBankAccountDto.bankName}' and account number '${createBankAccountDto.accountNumber}' already exists for this business`,
        );
      }

      // If this is set as primary, ensure no other primary account exists for the same entity
      if (
        createBankAccountDto.isPrimary &&
        createBankAccountDto.entityId &&
        createBankAccountDto.entityType
      ) {
        const existingPrimary = await dbInstance
          .select()
          .from(bankAccounts)
          .where(
            and(
              eq(bankAccounts.businessId, createBankAccountDto.businessId),
              eq(bankAccounts.entityType, createBankAccountDto.entityType),
              eq(bankAccounts.entityId, createBankAccountDto.entityId),
              eq(bankAccounts.isPrimary, true),
              isNull(bankAccounts.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingPrimary) {
          // Update existing primary to false
          await dbInstance
            .update(bankAccounts)
            .set({ isPrimary: false, updatedBy: userId, updatedAt: new Date() })
            .where(eq(bankAccounts.id, existingPrimary.id));
        }
      }

      // Insert new bank account
      const [bankAccount] = await dbInstance
        .insert(bankAccounts)
        .values({
          businessId: createBankAccountDto.businessId,
          entityId: createBankAccountDto.entityId,
          entityType:
            createBankAccountDto.entityType ?? BankAccountEntityType.BUSINESS,
          bankName: createBankAccountDto.bankName,
          accountHolderName: createBankAccountDto.accountHolderName,
          branchName: createBankAccountDto.branchName,
          bankCode: createBankAccountDto.bankCode,
          branchCode: createBankAccountDto.branchCode,
          accountName: createBankAccountDto.accountName,
          accountNumber: createBankAccountDto.accountNumber,
          routingNumber: createBankAccountDto.routingNumber,
          iban: createBankAccountDto.iban,
          swiftCode: createBankAccountDto.swiftCode,
          accountType:
            createBankAccountDto.accountType ?? BankAccountType.CHECKING,
          currency: createBankAccountDto.currency ?? CurrencyCode.USD,
          isPrimary: createBankAccountDto.isPrimary ?? false,
          isActive: createBankAccountDto.isActive ?? true,
          createdBy: userId,
        })
        .returning();

      // Log the bank account creation activity (only if not in transaction)
      if (!tx) {
        await this.activityLogService.logCreate(
          bankAccount.id,
          EntityType.ACCOUNT,
          userId,
          createBankAccountDto.businessId,
          {
            reason: `Bank account "${createBankAccountDto.bankName} - ${createBankAccountDto.accountNumber}" was created`,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        id: bankAccount.id,
      };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create bank account: ${error.message}`,
      );
    }
  }

  async findById(
    userId: string,
    id: string,
    businessId?: string,
  ): Promise<BankAccountDto> {
    const whereConditions = [
      eq(bankAccounts.id, id),
      isNull(bankAccounts.deletedAt),
    ];

    // Add business scope if provided
    if (businessId) {
      whereConditions.push(eq(bankAccounts.businessId, businessId));
    }

    const bankAccount = await this.db
      .select()
      .from(bankAccounts)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!bankAccount) {
      throw new NotFoundException('Bank account not found');
    }

    // Disable view logging for performance

    return this.mapToBankAccountDto(bankAccount);
  }

  async findByEntityId(
    userId: string,
    entityId: string,
    entityType: BankAccountEntityType,
    businessId?: string,
  ): Promise<BankAccountDto[]> {
    const whereConditions = [
      eq(bankAccounts.entityId, entityId),
      eq(bankAccounts.entityType, entityType),
      isNull(bankAccounts.deletedAt),
    ];

    if (businessId) {
      whereConditions.push(eq(bankAccounts.businessId, businessId));
    }

    const result = await this.db
      .select()
      .from(bankAccounts)
      .where(and(...whereConditions))
      .orderBy(bankAccounts.isPrimary, bankAccounts.createdAt);

    // Disable view logging for performance

    return result.map((bankAccount) => this.mapToBankAccountDto(bankAccount));
  }

  async findByBusinessId(
    userId: string,
    businessId: string,
    entityType?: BankAccountEntityType,
  ): Promise<BankAccountDto[]> {
    const whereConditions = [
      eq(bankAccounts.businessId, businessId),
      isNull(bankAccounts.deletedAt),
    ];

    if (entityType) {
      whereConditions.push(eq(bankAccounts.entityType, entityType));
    }

    const result = await this.db
      .select()
      .from(bankAccounts)
      .where(and(...whereConditions))
      .orderBy(
        bankAccounts.entityType,
        bankAccounts.isPrimary,
        bankAccounts.createdAt,
      );

    // Disable view logging for performance

    return result.map((bankAccount) => this.mapToBankAccountDto(bankAccount));
  }

  async update(
    userId: string,
    id: string,
    updateBankAccountDto: UpdateBankAccountDto,
    metadata?: ActivityMetadata,
    businessId?: string,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string }> {
    try {
      const dbInstance = tx || this.db;

      const whereConditions = [
        eq(bankAccounts.id, id),
        isNull(bankAccounts.deletedAt),
      ];

      // Add business scope if provided
      if (businessId) {
        whereConditions.push(eq(bankAccounts.businessId, businessId));
      }

      // Check if bank account exists
      const existingBankAccount = await dbInstance
        .select()
        .from(bankAccounts)
        .where(and(...whereConditions))
        .then((results) => results[0]);

      if (!existingBankAccount) {
        throw new NotFoundException('Bank account not found');
      }

      // Update the bank account
      const [updatedBankAccount] = await dbInstance
        .update(bankAccounts)
        .set({
          entityId: updateBankAccountDto.entityId,
          entityType: updateBankAccountDto.entityType,
          bankName: updateBankAccountDto.bankName,
          accountHolderName: updateBankAccountDto.accountHolderName,
          branchName: updateBankAccountDto.branchName,
          bankCode: updateBankAccountDto.bankCode,
          branchCode: updateBankAccountDto.branchCode,
          accountName: updateBankAccountDto.accountName,
          accountNumber: updateBankAccountDto.accountNumber,
          routingNumber: updateBankAccountDto.routingNumber,
          iban: updateBankAccountDto.iban,
          swiftCode: updateBankAccountDto.swiftCode,
          accountType: updateBankAccountDto.accountType,
          currency: updateBankAccountDto.currency,
          isPrimary: updateBankAccountDto.isPrimary,
          isActive: updateBankAccountDto.isActive,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(and(...whereConditions))
        .returning();

      if (!updatedBankAccount) {
        throw new NotFoundException('Bank account not found');
      }

      // Log the bank account update activity (only if not in transaction)
      if (!tx) {
        await this.activityLogService.logUpdate(
          updatedBankAccount.id,
          EntityType.ACCOUNT,
          userId,
          updatedBankAccount.businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        id: updatedBankAccount.id,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update bank account: ${error.message}`,
      );
    }
  }

  async updateEntityId(
    userId: string,
    id: string,
    entityId: string,
    businessId?: string,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string }> {
    const dbInstance = tx || this.db;

    const whereConditions = [
      eq(bankAccounts.id, id),
      isNull(bankAccounts.deletedAt),
    ];

    if (businessId) {
      whereConditions.push(eq(bankAccounts.businessId, businessId));
    }

    const [updatedBankAccount] = await dbInstance
      .update(bankAccounts)
      .set({
        entityId,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(and(...whereConditions))
      .returning();

    if (!updatedBankAccount) {
      throw new NotFoundException('Bank account not found');
    }

    return {
      id: updatedBankAccount.id,
    };
  }

  async delete(
    userId: string,
    id: string,
    metadata?: ActivityMetadata,
    businessId?: string,
    tx?: any, // Optional transaction parameter
  ): Promise<{ id: string; message: string }> {
    const dbInstance = tx || this.db;

    const whereConditions = [
      eq(bankAccounts.id, id),
      isNull(bankAccounts.deletedAt),
    ];

    // Add business scope if provided
    if (businessId) {
      whereConditions.push(eq(bankAccounts.businessId, businessId));
    }

    const bankAccount = await dbInstance
      .select()
      .from(bankAccounts)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!bankAccount) {
      throw new NotFoundException('Bank account not found');
    }

    // Soft delete the bank account
    await dbInstance
      .update(bankAccounts)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
      })
      .where(and(...whereConditions));

    // Log the bank account deletion activity (only if not in transaction)
    if (!tx) {
      await this.activityLogService.logDelete(
        bankAccount.id,
        EntityType.ACCOUNT,
        userId,
        bankAccount.businessId,
        {
          reason: `Bank account "${bankAccount.bankName} - ${bankAccount.accountNumber}" was deleted`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      id: bankAccount.id,
      message: 'Bank account deleted successfully',
    };
  }

  /**
   * Map database bank account record to BankAccountDto
   */
  private mapToBankAccountDto(bankAccount: any): BankAccountDto {
    return {
      id: bankAccount.id,
      businessId: bankAccount.businessId,
      entityId: bankAccount.entityId,
      entityType: bankAccount.entityType,
      bankName: bankAccount.bankName,
      accountHolderName: bankAccount.accountHolderName,
      branchName: bankAccount.branchName,
      bankCode: bankAccount.bankCode,
      branchCode: bankAccount.branchCode,
      accountName: bankAccount.accountName,
      accountNumber: bankAccount.accountNumber,
      routingNumber: bankAccount.routingNumber,
      iban: bankAccount.iban,
      swiftCode: bankAccount.swiftCode,
      accountType: bankAccount.accountType,
      currency: bankAccount.currency,
      isPrimary: bankAccount.isPrimary,
      isActive: bankAccount.isActive,
      createdBy: bankAccount.createdBy,
      updatedBy: bankAccount.updatedBy,
      deletedBy: bankAccount.deletedBy,
      deletedAt: bankAccount.deletedAt,
      createdAt: bankAccount.createdAt,
      updatedAt: bankAccount.updatedAt,
    };
  }
}
