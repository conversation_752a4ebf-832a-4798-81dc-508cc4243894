import { Module } from '@nestjs/common';
import { BusinessService } from './business.service';
import { BusinessController } from './business.controller';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { SharedModule } from '../shared/shared.module';
import { ActivityLogModule } from '../activity-log/activity-log.module';
import { MediaModule } from '../media/media.module';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { ConfigModule } from '@core/config/config.module';

@Module({
  imports: [
    AuthModule,
    DrizzleModule,
    SharedModule,
    ActivityLogModule,
    MediaModule,
    JwtModule.register({
      secret: process.env.JWT_ACCESS_SECRET || 'your-access-secret-key',
      signOptions: { expiresIn: '1d' },
    }),
    ConfigModule,
  ],
  controllers: [BusinessController],
  providers: [
    BusinessService,
    {
      provide: 'JWT_REFRESH_TOKEN_SERVICE',
      useFactory: () => {
        return new JwtService({
          secret: process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key',
          signOptions: { expiresIn: '7d' },
        });
      },
    },
  ],
  exports: [BusinessService],
})
export class BusinessModule {}
