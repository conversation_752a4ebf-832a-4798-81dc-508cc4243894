import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateFloorPlanDto } from './dto/create-floor-plan.dto';
import { UpdateFloorPlanDto } from './dto/update-floor-plan.dto';
import { FloorPlanDto } from './dto/floor-plan.dto';
import { FloorPlanSlimDto } from './dto/floor-plan-slim.dto';
import { floorPlans } from '../drizzle/schema/restaurant-tables.schema';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  inArray,
  desc,
  asc,
  count,
} from 'drizzle-orm';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { BulkCreateFloorPlanDto } from './dto/bulk-create-floor-plan.dto';
import { BulkDeleteFloorPlanDto } from './dto/bulk-delete-floor-plan.dto';

@Injectable()
export class FloorPlansService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createFloorPlanDto: CreateFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a floor plan with the same name already exists for this business
      const existingFloorPlan = await this.db
        .select()
        .from(floorPlans)
        .where(
          and(
            eq(floorPlans.businessId, businessId),
            ilike(floorPlans.name, createFloorPlanDto.name),
            eq(floorPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingFloorPlan) {
        throw new ConflictException(
          `Floor plan with name "${createFloorPlanDto.name}" already exists`,
        );
      }

      // Create the floor plan
      const newFloorPlan = await this.db
        .insert(floorPlans)
        .values({
          businessId,
          name: createFloorPlanDto.name,
          dimensions: createFloorPlanDto.dimensions,
          kitchen: createFloorPlanDto.kitchen,
          reception: createFloorPlanDto.reception,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: floorPlans.id })
        .then((results) => results[0]);

      // Log the floor plan creation activity
      await this.activityLogService.logCreate(
        newFloorPlan.id,
        EntityType.LOCATION,
        userId,
        businessId,
        {
          reason: `Floor plan "${createFloorPlanDto.name}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newFloorPlan.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      console.error('Error creating floor plan:', error);
      throw new BadRequestException('Failed to create floor plan');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createFloorPlanDto: CreateFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createFloorPlanDto, metadata);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    bulkCreateDto: BulkCreateFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const { floorPlans: floorPlansData } = bulkCreateDto;

      if (!floorPlansData || floorPlansData.length === 0) {
        throw new BadRequestException('At least one floor plan is required');
      }

      // Check for duplicate names within the request
      const floorPlanNames = floorPlansData.map((fp) => fp.name.toLowerCase());
      const uniqueNames = new Set(floorPlanNames);
      if (floorPlanNames.length !== uniqueNames.size) {
        throw new ConflictException(
          'Duplicate floor plan names found in the request',
        );
      }

      // Check for existing floor plan names in the database
      const existingFloorPlans = await this.db
        .select({
          name: floorPlans.name,
        })
        .from(floorPlans)
        .where(
          and(
            eq(floorPlans.businessId, businessId),
            eq(floorPlans.isDeleted, false),
          ),
        );

      const existingNames = new Set(
        existingFloorPlans.map((fp) => fp.name.toLowerCase()),
      );

      const conflictingNames = floorPlansData.filter((fp) =>
        existingNames.has(fp.name.toLowerCase()),
      );

      if (conflictingNames.length > 0) {
        const conflictingNamesStr = conflictingNames
          .map((fp) => fp.name)
          .join(', ');
        throw new ConflictException(
          `Floor plans with names [${conflictingNamesStr}] already exist`,
        );
      }

      // Create all floor plans
      const newFloorPlans = await this.db
        .insert(floorPlans)
        .values(
          floorPlansData.map((fp) => ({
            businessId,
            name: fp.name,
            dimensions: fp.dimensions,
            kitchen: fp.kitchen,
            reception: fp.reception,
            createdBy: userId,
            updatedBy: userId,
          })),
        )
        .returning({ id: floorPlans.id });

      const ids = newFloorPlans.map((fp) => fp.id);

      // Log bulk creation operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.LOCATION,
        ids,
        {
          names: floorPlansData.map((fp) => fp.name),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: ids.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { ids };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error bulk creating floor plans:', error);
      throw new BadRequestException('Failed to bulk create floor plans');
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    bulkCreateDto: BulkCreateFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(userId, businessId, bulkCreateDto, metadata);
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: FloorPlanDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(floorPlans.isDeleted, false),
      eq(floorPlans.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(floorPlans.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(floorPlans.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(floorPlans)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const data = await this.db
      .select({
        id: floorPlans.id,
        businessId: floorPlans.businessId,
        name: floorPlans.name,
        dimensions: floorPlans.dimensions,
        kitchen: floorPlans.kitchen,
        reception: floorPlans.reception,
        createdBy: floorPlans.createdBy,
        createdAt: floorPlans.createdAt,
        updatedAt: floorPlans.updatedAt,
      })
      .from(floorPlans)
      .where(and(...whereConditions))
      .orderBy(desc(floorPlans.createdAt))
      .limit(limit)
      .offset(offset);

    return {
      data: data as FloorPlanDto[],
      meta: { total, page, totalPages },
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    filters?: string,
    sort?: string,
  ): Promise<{
    data: FloorPlanDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(floorPlans.isDeleted, false),
      eq(floorPlans.businessId, businessId),
    ];

    // Add search filters
    if (name) {
      whereConditions.push(ilike(floorPlans.name, `%${name}%`));
    }

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(floorPlans.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(floorPlans.createdAt, toDate));
      }
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        if (Array.isArray(parsedFilters)) {
          for (const filter of parsedFilters) {
            if (filter.id && filter.value !== undefined && filter.operator) {
              this.applyFilter(whereConditions, filter);
            }
          }
        }
      } catch (error) {
        console.warn('Invalid filters JSON:', error);
      }
    }

    // Determine sort order
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'name':
          orderBy = isDesc ? desc(floorPlans.name) : asc(floorPlans.name);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(floorPlans.createdAt)
            : asc(floorPlans.createdAt);
          break;
        case 'updatedAt':
          orderBy = isDesc
            ? desc(floorPlans.updatedAt)
            : asc(floorPlans.updatedAt);
          break;
        default:
          orderBy = desc(floorPlans.createdAt);
      }
    } else {
      orderBy = desc(floorPlans.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(floorPlans)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const data = await this.db
      .select({
        id: floorPlans.id,
        businessId: floorPlans.businessId,
        name: floorPlans.name,
        dimensions: floorPlans.dimensions,
        kitchen: floorPlans.kitchen,
        reception: floorPlans.reception,
        createdBy: floorPlans.createdBy,
        createdAt: floorPlans.createdAt,
        updatedAt: floorPlans.updatedAt,
      })
      .from(floorPlans)
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: data as FloorPlanDto[],
      meta: { total, page, totalPages },
    };
  }

  private applyFilter(whereConditions: any[], filter: any): void {
    const { id, value, operator } = filter;

    switch (id) {
      case 'name':
        if (operator === 'like' || operator === 'iLike') {
          whereConditions.push(ilike(floorPlans.name, `%${value}%`));
        } else if (operator === 'eq') {
          whereConditions.push(eq(floorPlans.name, value));
        }
        break;
    }
  }

  async findAllSlim(businessId: string | null): Promise<FloorPlanSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const data = await this.db
      .select({
        id: floorPlans.id,
        name: floorPlans.name,
      })
      .from(floorPlans)
      .where(
        and(
          eq(floorPlans.businessId, businessId),
          eq(floorPlans.isDeleted, false),
        ),
      )
      .orderBy(asc(floorPlans.name));

    return data as FloorPlanSlimDto[];
  }

  async findOne(businessId: string | null, id: string): Promise<FloorPlanDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const floorPlan = await this.db
      .select({
        id: floorPlans.id,
        businessId: floorPlans.businessId,
        name: floorPlans.name,
        dimensions: floorPlans.dimensions,
        kitchen: floorPlans.kitchen,
        reception: floorPlans.reception,
        createdBy: floorPlans.createdBy,
        createdAt: floorPlans.createdAt,
        updatedAt: floorPlans.updatedAt,
      })
      .from(floorPlans)
      .where(
        and(
          eq(floorPlans.id, id),
          eq(floorPlans.businessId, businessId),
          eq(floorPlans.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!floorPlan) {
      throw new NotFoundException(`Floor plan with ID ${id} not found`);
    }

    return floorPlan as FloorPlanDto;
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(floorPlans.businessId, businessId),
      ilike(floorPlans.name, name),
      eq(floorPlans.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(and(eq(floorPlans.id, excludeId)));
    }

    const existingFloorPlan = await this.db
      .select({ id: floorPlans.id })
      .from(floorPlans)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingFloorPlan };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateFloorPlanDto: UpdateFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<FloorPlanDto> {
    // Get the floor plan
    const existingFloorPlan = await this.db
      .select()
      .from(floorPlans)
      .where(and(eq(floorPlans.id, id), eq(floorPlans.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingFloorPlan) {
      throw new NotFoundException(`Floor plan with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingFloorPlan.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this floor plan',
      );
    }

    // Check for name conflicts if name is being updated
    if (updateFloorPlanDto.name) {
      const availability = await this.checkNameAvailability(
        businessId,
        updateFloorPlanDto.name,
        id,
      );

      if (!availability.available) {
        throw new ConflictException(
          `Floor plan with name "${updateFloorPlanDto.name}" already exists`,
        );
      }
    }

    // Update the floor plan
    const updatedFloorPlan = await this.db
      .update(floorPlans)
      .set({
        ...updateFloorPlanDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(floorPlans.id, id))
      .returning({
        id: floorPlans.id,
        businessId: floorPlans.businessId,
        name: floorPlans.name,
        dimensions: floorPlans.dimensions,
        kitchen: floorPlans.kitchen,
        reception: floorPlans.reception,
        createdBy: floorPlans.createdBy,
        createdAt: floorPlans.createdAt,
        updatedAt: floorPlans.updatedAt,
      })
      .then((results) => results[0]);

    // Log the floor plan update activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.LOCATION,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return updatedFloorPlan as FloorPlanDto;
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateFloorPlanDto: UpdateFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateFloorPlanDto, metadata);
    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    // Get the floor plan
    const existingFloorPlan = await this.db
      .select()
      .from(floorPlans)
      .where(and(eq(floorPlans.id, id), eq(floorPlans.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingFloorPlan) {
      throw new NotFoundException(`Floor plan with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingFloorPlan.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this floor plan',
      );
    }

    // Soft delete the floor plan
    await this.db
      .update(floorPlans)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(floorPlans.id, id));

    // Log the floor plan deletion activity
    await this.activityLogService.logDelete(
      id,
      EntityType.LOCATION,
      userId,
      businessId,
      {
        reason: `Floor plan "${existingFloorPlan.name}" was deleted`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    bulkDeleteDto: BulkDeleteFloorPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string; deletedIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const { floorPlanIds } = bulkDeleteDto;

      if (!floorPlanIds || floorPlanIds.length === 0) {
        throw new BadRequestException('At least one floor plan ID is required');
      }

      // Verify all floor plans exist and belong to the business
      const existingFloorPlans = await this.db
        .select({
          id: floorPlans.id,
          name: floorPlans.name,
        })
        .from(floorPlans)
        .where(
          and(
            inArray(floorPlans.id, floorPlanIds),
            eq(floorPlans.businessId, businessId),
            eq(floorPlans.isDeleted, false),
          ),
        );

      if (existingFloorPlans.length !== floorPlanIds.length) {
        const foundIds = existingFloorPlans.map((fp) => fp.id);
        const missingIds = floorPlanIds.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Floor plans with IDs [${missingIds.join(', ')}] not found`,
        );
      }

      // Soft delete all floor plans
      await this.db
        .update(floorPlans)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(floorPlans.id, floorPlanIds));

      // Log bulk deletion operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.LOCATION,
        floorPlanIds,
        {
          names: existingFloorPlans.map((fp) => fp.name),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: existingFloorPlans.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: existingFloorPlans.length,
        message: `Successfully deleted ${existingFloorPlans.length} floor plan(s)`,
        deletedIds: floorPlanIds,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error bulk deleting floor plans:', error);
      throw new BadRequestException('Failed to bulk delete floor plans');
    }
  }
}
