import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateTimeSlotDto } from './dto/create-time-slot.dto';
import { UpdateTimeSlotDto } from './dto/update-time-slot.dto';
import { TimeSlotDto } from './dto/time-slot.dto';
import { TimeSlotSlimDto } from './dto/time-slot-slim.dto';
import { TimeSlotListDto } from './dto/time-slot-list.dto';
import { BulkTimeSlotIdsResponseDto } from './dto/bulk-time-slot-ids-response.dto';
import { BulkDeleteTimeSlotResponseDto } from './dto/bulk-delete-time-slot-response.dto';
import { BulkUpdateTimeSlotStatusResponseDto } from './dto/bulk-update-time-slot-status-response.dto';
import { timeSlots } from '../drizzle/schema/time-slots.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { users } from '../drizzle/schema/users.schema';
import { eq, and, sql, gte, lte, desc, asc, inArray } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { RecurrenceType, SlotStatus } from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class TimeSlotsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createTimeSlotDto: CreateTimeSlotDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify staff member belongs to the business
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createTimeSlotDto.staffMemberId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!staffMember) {
        throw new BadRequestException(
          'Staff member not found or does not belong to this business',
        );
      }

      // Check for conflicting time slots if specific date/time is provided
      if (
        createTimeSlotDto.slotDate &&
        createTimeSlotDto.slotStartTime &&
        createTimeSlotDto.slotEndTime
      ) {
        const conflictingSlot = await this.db
          .select()
          .from(timeSlots)
          .where(
            and(
              eq(timeSlots.staffMemberId, createTimeSlotDto.staffMemberId),
              eq(timeSlots.slotDate, createTimeSlotDto.slotDate),
              eq(timeSlots.slotStartTime, createTimeSlotDto.slotStartTime),
              eq(timeSlots.slotEndTime, createTimeSlotDto.slotEndTime),
              eq(timeSlots.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingSlot) {
          throw new ConflictException(
            'A time slot already exists for this staff member at the specified date and time',
          );
        }
      }

      const newTimeSlot = await this.db
        .insert(timeSlots)
        .values({
          businessId,
          staffMemberId: createTimeSlotDto.staffMemberId,
          recurrenceType:
            createTimeSlotDto.recurrenceType ?? RecurrenceType.NONE,
          recurrencePattern: createTimeSlotDto.recurrencePattern,
          recurrenceStartDate: createTimeSlotDto.recurrenceStartDate,
          recurrenceEndDate: createTimeSlotDto.recurrenceEndDate,
          recurrenceInterval: createTimeSlotDto.recurrenceInterval ?? 1,
          recurrenceDaysOfWeek: createTimeSlotDto.recurrenceDaysOfWeek,
          recurrenceDaysOfMonth: createTimeSlotDto.recurrenceDaysOfMonth,
          recurrenceWeekOfMonth: createTimeSlotDto.recurrenceWeekOfMonth,
          occurrenceCount: createTimeSlotDto.occurrenceCount,
          isRecurringTemplate: createTimeSlotDto.isRecurringTemplate ?? false,
          slotDate: createTimeSlotDto.slotDate,
          slotStartTime: createTimeSlotDto.slotStartTime,
          slotEndTime: createTimeSlotDto.slotEndTime,
          slotStart: createTimeSlotDto.slotStart
            ? new Date(createTimeSlotDto.slotStart)
            : undefined,
          slotEnd: createTimeSlotDto.slotEnd
            ? new Date(createTimeSlotDto.slotEnd)
            : undefined,
          durationMinutes: createTimeSlotDto.durationMinutes,
          maxAppointments: createTimeSlotDto.maxAppointments ?? 1,
          bookedAppointments: 0,
          tentativeAppointments: 0,
          availableAppointments: createTimeSlotDto.maxAppointments ?? 1,
          slotStatus: createTimeSlotDto.slotStatus ?? SlotStatus.AVAILABLE,
          isAvailableOnline: createTimeSlotDto.isAvailableOnline ?? true,
          notes: createTimeSlotDto.notes,
          createdBy: userId,
        })
        .returning()
        .then((results) => results[0]);

      // Log the time slot creation activity
      await this.activityLogService.logCreate(
        newTimeSlot.id,
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newTimeSlot.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create time slot: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    staffMemberId?: string,
    slotStatus?: string,
    isAvailableOnline?: string,
  ): Promise<{
    data: TimeSlotListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions - filter by business through staff members
    const whereConditions = [
      eq(timeSlots.isDeleted, false),
      eq(staffMembers.businessId, businessId),
      eq(staffMembers.isDeleted, false),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(timeSlots.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(timeSlots.createdAt, toDate));
      }
    }

    // Add staff member filtering if provided
    if (staffMemberId) {
      whereConditions.push(eq(timeSlots.staffMemberId, staffMemberId));
    }

    // Add slot status filtering if provided
    if (
      slotStatus &&
      Object.values(SlotStatus).includes(slotStatus as SlotStatus)
    ) {
      whereConditions.push(eq(timeSlots.slotStatus, slotStatus as SlotStatus));
    }

    // Add online availability filtering if provided
    if (isAvailableOnline !== undefined) {
      const isOnline = isAvailableOnline === 'true';
      whereConditions.push(eq(timeSlots.isAvailableOnline, isOnline));
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(timeSlots)
      .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = Number(totalResult.count);
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: timeSlots.id,
        staffMemberId: timeSlots.staffMemberId,
        staffMemberName: staffMembers.displayName,
        recurrenceType: timeSlots.recurrenceType,
        isRecurringTemplate: timeSlots.isRecurringTemplate,
        slotDate: timeSlots.slotDate,
        slotStartTime: timeSlots.slotStartTime,
        slotEndTime: timeSlots.slotEndTime,
        durationMinutes: timeSlots.durationMinutes,
        maxAppointments: timeSlots.maxAppointments,
        bookedAppointments: timeSlots.bookedAppointments,
        availableAppointments: timeSlots.availableAppointments,
        slotStatus: timeSlots.slotStatus,
        isAvailableOnline: timeSlots.isAvailableOnline,
        createdBy: users.firstName,
        createdAt: timeSlots.createdAt,
        updatedAt: timeSlots.updatedAt,
      })
      .from(timeSlots)
      .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
      .leftJoin(users, eq(timeSlots.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(timeSlots.createdAt))
      .limit(limit)
      .offset(offset);

    // Log the activity - View operations are typically not logged for performance
    // await this.activityLogService.logView(...);

    return {
      data: results.map((result) => ({
        id: result.id,
        staffMemberId: result.staffMemberId,
        staffMemberName: result.staffMemberName,
        recurrenceType: result.recurrenceType as RecurrenceType,
        isRecurringTemplate: result.isRecurringTemplate,
        slotDate: result.slotDate,
        slotStartTime: result.slotStartTime,
        slotEndTime: result.slotEndTime,
        durationMinutes: result.durationMinutes,
        maxAppointments: result.maxAppointments,
        bookedAppointments: result.bookedAppointments,
        availableAppointments: result.availableAppointments,
        slotStatus: result.slotStatus as SlotStatus,
        isAvailableOnline: result.isAvailableOnline,
        createdBy: result.createdBy || 'Unknown',
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
      })),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<TimeSlotSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all time slots with only essential fields
    const results = await this.db
      .select({
        id: timeSlots.id,
        staffMemberId: timeSlots.staffMemberId,
        staffMemberName: staffMembers.displayName,
        slotDate: timeSlots.slotDate,
        slotStartTime: timeSlots.slotStartTime,
        slotEndTime: timeSlots.slotEndTime,
        availableAppointments: timeSlots.availableAppointments,
        slotStatus: timeSlots.slotStatus,
        isAvailableOnline: timeSlots.isAvailableOnline,
      })
      .from(timeSlots)
      .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
      .where(
        and(
          eq(timeSlots.isDeleted, false),
          eq(staffMembers.businessId, businessId),
          eq(staffMembers.isDeleted, false),
          eq(timeSlots.slotStatus, SlotStatus.AVAILABLE),
          eq(timeSlots.isAvailableOnline, true),
        ),
      )
      .orderBy(asc(timeSlots.slotDate), asc(timeSlots.slotStartTime));

    // Log the activity - View operations are typically not logged for performance
    // await this.activityLogService.logView(...);

    return results.map((result) => ({
      id: result.id,
      staffMemberId: result.staffMemberId,
      staffMemberName: result.staffMemberName,
      slotDate: result.slotDate,
      slotStartTime: result.slotStartTime,
      slotEndTime: result.slotEndTime,
      availableAppointments: result.availableAppointments,
      slotStatus: result.slotStatus as SlotStatus,
      isAvailableOnline: result.isAvailableOnline,
    }));
  }

  async findOne(userId: string, timeSlotId: string): Promise<TimeSlotDto> {
    // Get time slot with staff member and user details
    const result = await this.db
      .select({
        id: timeSlots.id,
        staffMemberId: timeSlots.staffMemberId,
        staffMemberFirstName: staffMembers.firstName,
        staffMemberLastName: staffMembers.lastName,
        staffMemberDisplayName: staffMembers.displayName,
        staffMemberEmail: staffMembers.email,
        recurrenceType: timeSlots.recurrenceType,
        recurrencePattern: timeSlots.recurrencePattern,
        recurrenceStartDate: timeSlots.recurrenceStartDate,
        recurrenceEndDate: timeSlots.recurrenceEndDate,
        recurrenceInterval: timeSlots.recurrenceInterval,
        recurrenceDaysOfWeek: timeSlots.recurrenceDaysOfWeek,
        recurrenceDaysOfMonth: timeSlots.recurrenceDaysOfMonth,
        recurrenceWeekOfMonth: timeSlots.recurrenceWeekOfMonth,
        occurrenceCount: timeSlots.occurrenceCount,
        isRecurringTemplate: timeSlots.isRecurringTemplate,
        slotDate: timeSlots.slotDate,
        slotStartTime: timeSlots.slotStartTime,
        slotEndTime: timeSlots.slotEndTime,
        slotStart: timeSlots.slotStart,
        slotEnd: timeSlots.slotEnd,
        durationMinutes: timeSlots.durationMinutes,
        maxAppointments: timeSlots.maxAppointments,
        bookedAppointments: timeSlots.bookedAppointments,
        tentativeAppointments: timeSlots.tentativeAppointments,
        availableAppointments: timeSlots.availableAppointments,
        slotStatus: timeSlots.slotStatus,
        isAvailableOnline: timeSlots.isAvailableOnline,
        notes: timeSlots.notes,
        createdBy: users.firstName,
        updatedBy: sql<string>`COALESCE(updated_user.first_name, NULL)`,
        createdAt: timeSlots.createdAt,
        updatedAt: timeSlots.updatedAt,
      })
      .from(timeSlots)
      .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
      .leftJoin(users, eq(timeSlots.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(timeSlots.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(timeSlots.id, timeSlotId),
          eq(timeSlots.isDeleted, false),
          eq(staffMembers.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Time slot not found');
    }

    // Log the activity - View operations are typically not logged for performance
    // await this.activityLogService.logView(...);

    return {
      id: result.id,
      staffMemberId: result.staffMemberId,
      staffMember: {
        id: result.staffMemberId,
        firstName: result.staffMemberFirstName,
        lastName: result.staffMemberLastName,
        displayName: result.staffMemberDisplayName,
        email: result.staffMemberEmail,
      },
      recurrenceType: result.recurrenceType as RecurrenceType,
      recurrencePattern: result.recurrencePattern,
      recurrenceStartDate: result.recurrenceStartDate,
      recurrenceEndDate: result.recurrenceEndDate,
      recurrenceInterval: result.recurrenceInterval,
      recurrenceDaysOfWeek: result.recurrenceDaysOfWeek,
      recurrenceDaysOfMonth: result.recurrenceDaysOfMonth,
      recurrenceWeekOfMonth: result.recurrenceWeekOfMonth,
      occurrenceCount: result.occurrenceCount,
      isRecurringTemplate: result.isRecurringTemplate,
      slotDate: result.slotDate,
      slotStartTime: result.slotStartTime,
      slotEndTime: result.slotEndTime,
      slotStart: result.slotStart,
      slotEnd: result.slotEnd,
      durationMinutes: result.durationMinutes,
      maxAppointments: result.maxAppointments,
      bookedAppointments: result.bookedAppointments,
      tentativeAppointments: result.tentativeAppointments,
      availableAppointments: result.availableAppointments,
      slotStatus: result.slotStatus as SlotStatus,
      isAvailableOnline: result.isAvailableOnline,
      notes: result.notes,
      createdBy: result.createdBy || 'Unknown',
      updatedBy: result.updatedBy,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    timeSlotId: string,
    updateTimeSlotDto: UpdateTimeSlotDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if time slot exists and belongs to the business
      const existingTimeSlot = await this.db
        .select()
        .from(timeSlots)
        .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
        .where(
          and(
            eq(timeSlots.id, timeSlotId),
            eq(staffMembers.businessId, businessId),
            eq(timeSlots.isDeleted, false),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingTimeSlot) {
        throw new NotFoundException(
          'Time slot not found or does not belong to this business',
        );
      }

      // If staffMemberId is being updated, verify the new staff member belongs to the business
      if (updateTimeSlotDto.staffMemberId) {
        const staffMember = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateTimeSlotDto.staffMemberId),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'Staff member not found or does not belong to this business',
          );
        }
      }

      // Check for conflicting time slots if date/time is being updated
      if (
        updateTimeSlotDto.slotDate &&
        updateTimeSlotDto.slotStartTime &&
        updateTimeSlotDto.slotEndTime
      ) {
        const conflictingSlot = await this.db
          .select()
          .from(timeSlots)
          .where(
            and(
              eq(
                timeSlots.staffMemberId,
                updateTimeSlotDto.staffMemberId ||
                  existingTimeSlot.time_slots.staffMemberId,
              ),
              eq(timeSlots.slotDate, updateTimeSlotDto.slotDate),
              eq(timeSlots.slotStartTime, updateTimeSlotDto.slotStartTime),
              eq(timeSlots.slotEndTime, updateTimeSlotDto.slotEndTime),
              sql`${timeSlots.id} != ${timeSlotId}`,
              eq(timeSlots.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingSlot) {
          throw new ConflictException(
            'A time slot already exists for this staff member at the specified date and time',
          );
        }
      }

      // Calculate available appointments if booking counts are updated
      let availableAppointments = updateTimeSlotDto.availableAppointments;
      if (
        updateTimeSlotDto.maxAppointments !== undefined ||
        updateTimeSlotDto.bookedAppointments !== undefined ||
        updateTimeSlotDto.tentativeAppointments !== undefined
      ) {
        const maxAppts =
          updateTimeSlotDto.maxAppointments ??
          existingTimeSlot.time_slots.maxAppointments;
        const bookedAppts =
          updateTimeSlotDto.bookedAppointments ??
          existingTimeSlot.time_slots.bookedAppointments;
        const tentativeAppts =
          updateTimeSlotDto.tentativeAppointments ??
          existingTimeSlot.time_slots.tentativeAppointments;
        availableAppointments = maxAppts - bookedAppts - tentativeAppts;
      }

      const updatedTimeSlot = await this.db
        .update(timeSlots)
        .set({
          ...(updateTimeSlotDto.staffMemberId && {
            staffMemberId: updateTimeSlotDto.staffMemberId,
          }),
          ...(updateTimeSlotDto.recurrenceType && {
            recurrenceType: updateTimeSlotDto.recurrenceType,
          }),
          ...(updateTimeSlotDto.recurrencePattern !== undefined && {
            recurrencePattern: updateTimeSlotDto.recurrencePattern,
          }),
          ...(updateTimeSlotDto.recurrenceStartDate !== undefined && {
            recurrenceStartDate: updateTimeSlotDto.recurrenceStartDate,
          }),
          ...(updateTimeSlotDto.recurrenceEndDate !== undefined && {
            recurrenceEndDate: updateTimeSlotDto.recurrenceEndDate,
          }),
          ...(updateTimeSlotDto.recurrenceInterval !== undefined && {
            recurrenceInterval: updateTimeSlotDto.recurrenceInterval,
          }),
          ...(updateTimeSlotDto.recurrenceDaysOfWeek !== undefined && {
            recurrenceDaysOfWeek: updateTimeSlotDto.recurrenceDaysOfWeek,
          }),
          ...(updateTimeSlotDto.recurrenceDaysOfMonth !== undefined && {
            recurrenceDaysOfMonth: updateTimeSlotDto.recurrenceDaysOfMonth,
          }),
          ...(updateTimeSlotDto.recurrenceWeekOfMonth !== undefined && {
            recurrenceWeekOfMonth: updateTimeSlotDto.recurrenceWeekOfMonth,
          }),
          ...(updateTimeSlotDto.occurrenceCount !== undefined && {
            occurrenceCount: updateTimeSlotDto.occurrenceCount,
          }),
          ...(updateTimeSlotDto.isRecurringTemplate !== undefined && {
            isRecurringTemplate: updateTimeSlotDto.isRecurringTemplate,
          }),
          ...(updateTimeSlotDto.slotDate !== undefined && {
            slotDate: updateTimeSlotDto.slotDate,
          }),
          ...(updateTimeSlotDto.slotStartTime !== undefined && {
            slotStartTime: updateTimeSlotDto.slotStartTime,
          }),
          ...(updateTimeSlotDto.slotEndTime !== undefined && {
            slotEndTime: updateTimeSlotDto.slotEndTime,
          }),
          ...(updateTimeSlotDto.slotStart !== undefined && {
            slotStart: updateTimeSlotDto.slotStart
              ? new Date(updateTimeSlotDto.slotStart)
              : null,
          }),
          ...(updateTimeSlotDto.slotEnd !== undefined && {
            slotEnd: updateTimeSlotDto.slotEnd
              ? new Date(updateTimeSlotDto.slotEnd)
              : null,
          }),
          ...(updateTimeSlotDto.durationMinutes !== undefined && {
            durationMinutes: updateTimeSlotDto.durationMinutes,
          }),
          ...(updateTimeSlotDto.maxAppointments !== undefined && {
            maxAppointments: updateTimeSlotDto.maxAppointments,
          }),
          ...(updateTimeSlotDto.bookedAppointments !== undefined && {
            bookedAppointments: updateTimeSlotDto.bookedAppointments,
          }),
          ...(updateTimeSlotDto.tentativeAppointments !== undefined && {
            tentativeAppointments: updateTimeSlotDto.tentativeAppointments,
          }),
          ...(availableAppointments !== undefined && { availableAppointments }),
          ...(updateTimeSlotDto.slotStatus && {
            slotStatus: updateTimeSlotDto.slotStatus,
          }),
          ...(updateTimeSlotDto.isAvailableOnline !== undefined && {
            isAvailableOnline: updateTimeSlotDto.isAvailableOnline,
          }),
          ...(updateTimeSlotDto.notes !== undefined && {
            notes: updateTimeSlotDto.notes,
          }),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(timeSlots.id, timeSlotId))
        .returning()
        .then((results) => results[0]);

      // Log the time slot update activity
      await this.activityLogService.logUpdate(
        timeSlotId,
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedTimeSlot.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update time slot: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    timeSlotId: string,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if time slot exists and belongs to the business
      const existingTimeSlot = await this.db
        .select()
        .from(timeSlots)
        .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
        .where(
          and(
            eq(timeSlots.id, timeSlotId),
            eq(staffMembers.businessId, businessId),
            eq(timeSlots.isDeleted, false),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingTimeSlot) {
        throw new NotFoundException(
          'Time slot not found or does not belong to this business',
        );
      }

      // Soft delete the time slot
      await this.db
        .update(timeSlots)
        .set({
          isDeleted: true,
          updatedAt: new Date(),
        })
        .where(eq(timeSlots.id, timeSlotId));

      // Log the time slot deletion activity
      await this.activityLogService.logDelete(
        timeSlotId,
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        message: 'Time slot deleted successfully',
        id: timeSlotId,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete time slot: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createTimeSlotsDto: CreateTimeSlotDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createTimeSlotsDto || createTimeSlotsDto.length === 0) {
        throw new BadRequestException('No time slots provided for creation');
      }

      const createdTimeSlots: { id: string }[] = [];

      // Use transaction to ensure all creations succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const createTimeSlotDto of createTimeSlotsDto) {
          // Validate staff member exists and belongs to the business
          const staffMember = await tx
            .select({
              id: staffMembers.id,
              businessId: staffMembers.businessId,
              name: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`.as(
                'name',
              ),
            })
            .from(staffMembers)
            .where(
              and(
                eq(staffMembers.id, createTimeSlotDto.staffMemberId),
                eq(staffMembers.businessId, businessId),
                eq(staffMembers.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (!staffMember) {
            throw new BadRequestException(
              `Staff member not found or access denied for ID: ${createTimeSlotDto.staffMemberId}`,
            );
          }

          // Check for conflicting time slots
          const conflictingSlot = await tx
            .select()
            .from(timeSlots)
            .where(
              and(
                eq(timeSlots.staffMemberId, createTimeSlotDto.staffMemberId),
                eq(timeSlots.slotDate, createTimeSlotDto.slotDate),
                sql`${timeSlots.slotStartTime} < ${createTimeSlotDto.slotEndTime}`,
                sql`${timeSlots.slotEndTime} > ${createTimeSlotDto.slotStartTime}`,
                eq(timeSlots.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (conflictingSlot) {
            throw new ConflictException(
              `Time slot conflict detected for staff member ${createTimeSlotDto.staffMemberId} on ${createTimeSlotDto.slotDate} between ${createTimeSlotDto.slotStartTime} and ${createTimeSlotDto.slotEndTime}`,
            );
          }

          // Calculate duration and slot times
          const slotStart = new Date(
            `${createTimeSlotDto.slotDate}T${createTimeSlotDto.slotStartTime}`,
          );
          const slotEnd = new Date(
            `${createTimeSlotDto.slotDate}T${createTimeSlotDto.slotEndTime}`,
          );
          const durationMinutes = Math.round(
            (slotEnd.getTime() - slotStart.getTime()) / (1000 * 60),
          );

          // Create the time slot
          const newTimeSlot = await tx
            .insert(timeSlots)
            .values({
              businessId,
              staffMemberId: createTimeSlotDto.staffMemberId,
              recurrenceType:
                createTimeSlotDto.recurrenceType ?? RecurrenceType.NONE,
              recurrencePattern: createTimeSlotDto.recurrencePattern,
              recurrenceStartDate: createTimeSlotDto.recurrenceStartDate,
              recurrenceEndDate: createTimeSlotDto.recurrenceEndDate,
              recurrenceInterval: createTimeSlotDto.recurrenceInterval,
              recurrenceDaysOfWeek: createTimeSlotDto.recurrenceDaysOfWeek,
              recurrenceDaysOfMonth: createTimeSlotDto.recurrenceDaysOfMonth,
              recurrenceWeekOfMonth: createTimeSlotDto.recurrenceWeekOfMonth,
              occurrenceCount: createTimeSlotDto.occurrenceCount,
              isRecurringTemplate:
                createTimeSlotDto.isRecurringTemplate ?? false,
              slotDate: createTimeSlotDto.slotDate,
              slotStartTime: createTimeSlotDto.slotStartTime,
              slotEndTime: createTimeSlotDto.slotEndTime,
              slotStart,
              slotEnd,
              durationMinutes,
              maxAppointments: createTimeSlotDto.maxAppointments,
              bookedAppointments: 0,
              tentativeAppointments: 0,
              availableAppointments: createTimeSlotDto.maxAppointments,
              slotStatus: createTimeSlotDto.slotStatus ?? SlotStatus.AVAILABLE,
              isAvailableOnline: createTimeSlotDto.isAvailableOnline ?? true,
              notes: createTimeSlotDto.notes,
              createdBy: userId,
              updatedBy: userId,
            })
            .returning()
            .then((results) => results[0]);

          // Log the activity
          await this.activityLogService.logCreate(
            newTimeSlot.id,
            EntityType.SERVICE_TIME_SLOT,
            userId,
            businessId,
            {
              source: metadata?.source || ActivitySource.WEB,
              ipAddress: metadata?.ipAddress,
              userAgent: metadata?.userAgent,
              sessionId: metadata?.sessionId,
            },
          );

          createdTimeSlots.push({ id: newTimeSlot.id });
        }
      });

      return createdTimeSlots;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create time slots: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createTimeSlotsDto: CreateTimeSlotDto[],
    metadata?: ActivityMetadata,
  ): Promise<BulkTimeSlotIdsResponseDto> {
    const timeSlots = await this.bulkCreate(
      userId,
      businessId,
      createTimeSlotsDto,
      metadata,
    );
    return { ids: timeSlots.map((timeSlot) => timeSlot.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    timeSlotIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<BulkDeleteTimeSlotResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!timeSlotIds || timeSlotIds.length === 0) {
        throw new BadRequestException('No time slot IDs provided for deletion');
      }

      // Get all time slots that exist and belong to the business (through staff members)
      const existingTimeSlots = await this.db
        .select({
          id: timeSlots.id,
          staffMemberId: timeSlots.staffMemberId,
          slotDate: timeSlots.slotDate,
          slotStartTime: timeSlots.slotStartTime,
          slotEndTime: timeSlots.slotEndTime,
          staffMemberName:
            sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`.as(
              'staffMemberName',
            ),
        })
        .from(timeSlots)
        .innerJoin(staffMembers, eq(timeSlots.staffMemberId, staffMembers.id))
        .where(
          and(
            inArray(timeSlots.id, timeSlotIds),
            eq(staffMembers.businessId, businessId),
            eq(timeSlots.isDeleted, false),
            eq(staffMembers.isDeleted, false),
          ),
        );

      if (existingTimeSlots.length === 0) {
        throw new BadRequestException('No valid time slots found for deletion');
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const timeSlot of existingTimeSlots) {
          // Soft delete the time slot
          await tx
            .update(timeSlots)
            .set({
              isDeleted: true,
              updatedAt: currentTime,
            })
            .where(eq(timeSlots.id, timeSlot.id));

          deletedIds.push(timeSlot.id);

          // Log the activity for each deleted time slot
          await this.activityLogService.logDelete(
            timeSlot.id,
            EntityType.SERVICE_TIME_SLOT,
            userId,
            businessId,
            {
              source: metadata?.source || ActivitySource.WEB,
              ipAddress: metadata?.ipAddress,
              userAgent: metadata?.userAgent,
              sessionId: metadata?.sessionId,
            },
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} time slot${deletedIds.length === 1 ? '' : 's'}`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete time slots: ${error.message}`,
      );
    }
  }

  async bulkUpdateTimeSlotStatus(
    userId: string,
    businessId: string | null,
    timeSlotIds: string[],
    status: SlotStatus,
    metadata?: ActivityMetadata,
  ): Promise<BulkUpdateTimeSlotStatusResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!timeSlotIds || timeSlotIds.length === 0) {
        throw new BadRequestException(
          'No time slot IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ timeSlotId: string; error: string }> = [];

      // Process each time slot ID
      await this.db.transaction(async (tx) => {
        for (const timeSlotId of timeSlotIds) {
          try {
            // Check if time slot exists and belongs to the business (through staff member)
            const existingTimeSlot = await tx
              .select({
                id: timeSlots.id,
                slotStatus: timeSlots.slotStatus,
                staffMemberId: timeSlots.staffMemberId,
                slotDate: timeSlots.slotDate,
                slotStartTime: timeSlots.slotStartTime,
                slotEndTime: timeSlots.slotEndTime,
                staffMemberName:
                  sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`.as(
                    'staffMemberName',
                  ),
              })
              .from(timeSlots)
              .innerJoin(
                staffMembers,
                eq(timeSlots.staffMemberId, staffMembers.id),
              )
              .where(
                and(
                  eq(timeSlots.id, timeSlotId),
                  eq(staffMembers.businessId, businessId),
                  eq(timeSlots.isDeleted, false),
                  eq(staffMembers.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingTimeSlot) {
              failed.push({
                timeSlotId,
                error: 'Time slot not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingTimeSlot.slotStatus === status) {
              failed.push({
                timeSlotId,
                error: `Time slot already has status: ${status}`,
              });
              continue;
            }

            // Update the time slot status
            await tx
              .update(timeSlots)
              .set({
                slotStatus: status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(timeSlots.id, timeSlotId),
                  eq(timeSlots.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(timeSlotId);

            // Log the status update activity
            await this.activityLogService.logUpdate(
              timeSlotId,
              EntityType.SERVICE_TIME_SLOT,
              userId,
              businessId,
              {
                source: metadata?.source || ActivitySource.WEB,
                ipAddress: metadata?.ipAddress,
                userAgent: metadata?.userAgent,
                sessionId: metadata?.sessionId,
              },
            );
          } catch (error) {
            failed.push({
              timeSlotId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      const response: BulkUpdateTimeSlotStatusResponseDto = {
        updated: updatedCount,
        message: `Successfully updated status for ${updatedCount} time slot${updatedCount === 1 ? '' : 's'}`,
        updatedIds,
      };

      if (failed.length > 0) {
        response.failed = failed;
      }

      return response;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update time slot status: ${error.message}`,
      );
    }
  }
}
