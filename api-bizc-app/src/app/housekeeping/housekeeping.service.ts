import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateHousekeepingDto } from './dto/create-housekeeping.dto';
import { UpdateHousekeepingDto } from './dto/update-housekeeping.dto';
import { HousekeepingDto } from './dto/housekeeping.dto';
import { HousekeepingSlimDto } from './dto/housekeeping-slim.dto';
import { HousekeepingListDto } from './dto/housekeeping-list.dto';
import {
  housekeeping,
  HousekeepingType,
  HousekeepingStatus,
} from '../drizzle/schema/housekeeping.schema';
import { accommodationUnits } from '../drizzle/schema/accommodation-units.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import {
  tasks,
  TaskStatus,
  TaskPriority,
} from '../drizzle/schema/tasks.schema';
// TODO: Import recurring activities when service is implemented
// import {
//   recurringActivities,
//   RecurringEntityType,
//   RecurrenceEndType,
// } from '../drizzle/schema/recurring-activities.schema';
import { users } from '../drizzle/schema/users.schema';
import { eq, and, ilike, sql, gte, lte, desc, asc, or } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { TasksService } from '../tasks/tasks.service';

@Injectable()
export class HousekeepingService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly tasksService: TasksService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createHousekeepingDto: CreateHousekeepingDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if accommodation unit exists
      const accommodationUnit = await this.db
        .select()
        .from(accommodationUnits)
        .where(
          and(
            eq(
              accommodationUnits.id,
              createHousekeepingDto.accommodationUnitId,
            ),
            eq(accommodationUnits.businessId, businessId),
            eq(accommodationUnits.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!accommodationUnit) {
        throw new NotFoundException('Accommodation unit not found');
      }

      // Check for duplicate housekeeping record (same type and unit)
      const existingHousekeeping = await this.db
        .select()
        .from(housekeeping)
        .where(
          and(
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.type, createHousekeepingDto.type),
            eq(
              housekeeping.accommodationUnitId,
              createHousekeepingDto.accommodationUnitId,
            ),
            eq(housekeeping.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingHousekeeping) {
        throw new ConflictException(
          `A ${createHousekeepingDto.type} housekeeping record already exists for this accommodation unit`,
        );
      }

      let taskId: string | undefined;
      let recurringActivityId: string | undefined;

      // Use transaction to ensure data consistency
      const newHousekeeping = await this.db.transaction(async (tx) => {
        // Create task if requested
        if (
          createHousekeepingDto.createTask &&
          createHousekeepingDto.taskTitle
        ) {
          const taskResult = await this.tasksService.create(
            userId,
            businessId,
            {
              title: createHousekeepingDto.taskTitle,
              description: createHousekeepingDto.taskDescription,
              dueDate: createHousekeepingDto.taskDueDate,
              priority:
                createHousekeepingDto.taskPriority || TaskPriority.MEDIUM,
              status: TaskStatus.PENDING,
              assignedTo: createHousekeepingDto.taskAssignedTo,
            },
          );
          taskId = taskResult.id;
        }

        // TODO: Recurring activities not yet implemented
        // Skip recurring activity creation for now

        // Create housekeeping record
        const [housekeepingRecord] = await tx
          .insert(housekeeping)
          .values({
            businessId,
            type: createHousekeepingDto.type,
            status: createHousekeepingDto.status || HousekeepingStatus.PENDING,
            actualStartTime: createHousekeepingDto.actualStartTime
              ? new Date(createHousekeepingDto.actualStartTime)
              : undefined,
            actualEndTime: createHousekeepingDto.actualEndTime
              ? new Date(createHousekeepingDto.actualEndTime)
              : undefined,
            accommodationUnitId: createHousekeepingDto.accommodationUnitId,
            taskId,
            recurringActivityId: undefined, // TODO: Set when recurring activities implemented
            checklist: createHousekeepingDto.checklist
              ? { items: createHousekeepingDto.checklist }
              : undefined,
            notes: createHousekeepingDto.notes,
            issuesFound: createHousekeepingDto.issuesFound,
            inspectedBy: createHousekeepingDto.inspectedBy,
            inspectionNotes: createHousekeepingDto.inspectionNotes,
            inspectionDate: createHousekeepingDto.inspectionDate
              ? new Date(createHousekeepingDto.inspectionDate)
              : undefined,
            qualityScore: createHousekeepingDto.qualityScore,
            createdBy: userId,
          })
          .returning();

        return housekeepingRecord;
      });

      // TODO: Update recurring activity with the housekeeping ID if we created one
      // (Recurring activities not yet implemented)

      // Log the housekeeping creation activity
      await this.activityLogService.logCreate(
        newHousekeeping.id,
        EntityType.TASK,
        userId,
        businessId,
        {
          reason: `Housekeeping record "${createHousekeepingDto.type}" was created for ${accommodationUnit.name}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newHousekeeping.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create housekeeping record');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createHousekeepingDto: CreateHousekeepingDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createHousekeepingDto, metadata);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    housekeepingRecords: CreateHousekeepingDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (const record of housekeepingRecords) {
      try {
        const result = await this.create(userId, businessId, record, metadata);
        createdIds.push(result.id);
      } catch (error) {
        errors.push(`Failed to create housekeeping record: ${error.message}`);
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `All housekeeping records failed to create: ${errors.join(', ')}`,
      );
    }

    // Log bulk creation operation if any records were created
    if (createdIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.TASK,
        createdIds,
        {
          types: housekeepingRecords.map((record) => record.type),
          successCount: createdIds.length,
          errorCount: errors.length,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createdIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return { ids: createdIds };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    housekeepingRecords: CreateHousekeepingDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(userId, businessId, housekeepingRecords, metadata);
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    type?: string,
    status?: string,
    accommodationUnitId?: string,
    qualityScore?: string,
    inspectedBy?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: HousekeepingListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(housekeeping.isDeleted, false),
      eq(housekeeping.businessId, businessId),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(gte(housekeeping.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(housekeeping.createdAt, new Date(to)));
    }

    // Add type filter
    if (
      type &&
      Object.values(HousekeepingType).includes(type as HousekeepingType)
    ) {
      whereConditions.push(eq(housekeeping.type, type as HousekeepingType));
    }

    // Add status filter
    if (
      status &&
      Object.values(HousekeepingStatus).includes(status as HousekeepingStatus)
    ) {
      whereConditions.push(
        eq(housekeeping.status, status as HousekeepingStatus),
      );
    }

    // Add accommodation unit filter
    if (accommodationUnitId) {
      whereConditions.push(
        eq(housekeeping.accommodationUnitId, accommodationUnitId),
      );
    }

    // Add quality score filter
    if (qualityScore) {
      whereConditions.push(eq(housekeeping.qualityScore, qualityScore));
    }

    // Add inspected by filter
    if (inspectedBy) {
      whereConditions.push(eq(housekeeping.inspectedBy, inspectedBy));
    }

    // Parse additional filters
    const additionalConditions = [];
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const filter of parsedFilters) {
          if (filter.field && filter.operator && filter.value !== undefined) {
            const field = housekeeping[filter.field];
            if (field) {
              switch (filter.operator) {
                case 'eq':
                  additionalConditions.push(eq(field, filter.value));
                  break;
                case 'like':
                  additionalConditions.push(ilike(field, `%${filter.value}%`));
                  break;
                case 'gte':
                  additionalConditions.push(gte(field, filter.value));
                  break;
                case 'lte':
                  additionalConditions.push(lte(field, filter.value));
                  break;
              }
            }
          }
        }
      } catch {
        // Ignore invalid filter JSON
      }
    }

    // Combine conditions based on join operator
    let finalConditions: any;
    if (additionalConditions.length > 0) {
      const baseCondition = and(...whereConditions);
      const additionalCondition =
        joinOperator === 'or'
          ? or(...additionalConditions)
          : and(...additionalConditions);
      finalConditions = and(baseCondition, additionalCondition);
    } else {
      finalConditions = and(...whereConditions);
    }

    // Build sort order
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const sortableFields: Record<string, any> = {
        createdAt: housekeeping.createdAt,
        updatedAt: housekeeping.updatedAt,
        type: housekeeping.type,
        status: housekeeping.status,
        actualStartTime: housekeeping.actualStartTime,
        actualEndTime: housekeeping.actualEndTime,
        qualityScore: housekeeping.qualityScore,
      };
      const sortField = sortableFields[field];
      if (sortField) {
        orderBy = direction === 'desc' ? desc(sortField) : asc(sortField);
      }
    }
    if (!orderBy) {
      orderBy = desc(housekeeping.createdAt);
    }

    // Execute query with joins
    const housekeepingResults = await this.db
      .select({
        id: housekeeping.id,
        type: housekeeping.type,
        status: housekeeping.status,
        actualStartTime: housekeeping.actualStartTime,
        actualEndTime: housekeeping.actualEndTime,
        qualityScore: housekeeping.qualityScore,
        issuesFound: housekeeping.issuesFound,
        inspectionDate: housekeeping.inspectionDate,
        createdAt: housekeeping.createdAt,
        updatedAt: housekeeping.updatedAt,
        // Accommodation unit info
        accommodationUnitId: accommodationUnits.id,
        accommodationUnitName: accommodationUnits.name,
        accommodationUnitNumber: accommodationUnits.roomNumber,
        // Task info
        taskStatus: tasks.status,
        // Recurring info (TODO: Implement when recurring activities available)
        isRecurring: sql<boolean>`false`,
        // Inspector info
        inspectedByName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
      })
      .from(housekeeping)
      .leftJoin(
        accommodationUnits,
        eq(housekeeping.accommodationUnitId, accommodationUnits.id),
      )
      .leftJoin(tasks, eq(housekeeping.taskId, tasks.id))
      .leftJoin(staffMembers, eq(housekeeping.inspectedBy, staffMembers.id))
      .where(finalConditions)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(housekeeping)
      .leftJoin(
        accommodationUnits,
        eq(housekeeping.accommodationUnitId, accommodationUnits.id),
      )
      .leftJoin(tasks, eq(housekeeping.taskId, tasks.id))
      .leftJoin(staffMembers, eq(housekeeping.inspectedBy, staffMembers.id))
      .where(finalConditions);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Transform results
    const data: HousekeepingListDto[] = housekeepingResults.map((row) => ({
      id: row.id,
      type: row.type,
      status: row.status,
      actualStartTime: row.actualStartTime,
      actualEndTime: row.actualEndTime,
      accommodationUnit: {
        id: row.accommodationUnitId,
        name: row.accommodationUnitName,
        unitNumber: row.accommodationUnitNumber,
      },
      taskStatus: row.taskStatus,
      isRecurring: row.isRecurring,
      qualityScore: row.qualityScore,
      inspectedByName: row.inspectedByName,
      inspectionDate: row.inspectionDate,
      issuesFound: row.issuesFound,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<HousekeepingDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const housekeepingResult = await this.db
      .select({
        id: housekeeping.id,
        businessId: housekeeping.businessId,
        type: housekeeping.type,
        status: housekeeping.status,
        actualStartTime: housekeeping.actualStartTime,
        actualEndTime: housekeeping.actualEndTime,
        checklist: housekeeping.checklist,
        notes: housekeeping.notes,
        issuesFound: housekeeping.issuesFound,
        inspectionNotes: housekeeping.inspectionNotes,
        inspectionDate: housekeeping.inspectionDate,
        qualityScore: housekeeping.qualityScore,
        createdAt: housekeeping.createdAt,
        updatedAt: housekeeping.updatedAt,
        // Accommodation unit info
        accommodationUnitId: accommodationUnits.id,
        accommodationUnitName: accommodationUnits.name,
        accommodationUnitNumber: accommodationUnits.roomNumber,
        // Task info
        taskId: tasks.id,
        taskTitle: tasks.title,
        taskStatus: tasks.status,
        // Recurring activity info (TODO: Implement when recurring activities available)
        recurringActivityId: sql<string | null>`null`,
        recurringActivityTitle: sql<string | null>`null`,
        recurrencePattern: sql<string | null>`null`,
        // Parent housekeeping info
        parentHousekeepingId: housekeeping.parentHousekeepingId,
        // Inspector info
        inspectedById: staffMembers.id,
        inspectedByDisplayName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        inspectedByProfileImageId: staffMembers.profileImageId,
        // Creator info
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        updatedByName: sql<string>`CONCAT(updatedByUser.first_name, ' ', updatedByUser.last_name)`,
      })
      .from(housekeeping)
      .leftJoin(
        accommodationUnits,
        eq(housekeeping.accommodationUnitId, accommodationUnits.id),
      )
      .leftJoin(tasks, eq(housekeeping.taskId, tasks.id))
      // TODO: Add recurring activities join when implemented
      // .leftJoin(
      //   recurringActivities,
      //   eq(housekeeping.recurringActivityId, recurringActivities.id),
      // )
      .leftJoin(staffMembers, eq(housekeeping.inspectedBy, staffMembers.id))
      .leftJoin(users, eq(housekeeping.createdBy, users.id))
      .leftJoin(
        sql`${users} as updatedByUser`,
        sql`${housekeeping.updatedBy} = updatedByUser.id`,
      )
      .where(
        and(
          eq(housekeeping.id, id),
          eq(housekeeping.businessId, businessId),
          eq(housekeeping.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!housekeepingResult) {
      throw new NotFoundException('Housekeeping record not found');
    }

    // Get parent housekeeping info if exists
    let parentHousekeeping: { id: string; type: string } | undefined;
    if (housekeepingResult.parentHousekeepingId) {
      const parentResult = await this.db
        .select({
          id: housekeeping.id,
          type: housekeeping.type,
        })
        .from(housekeeping)
        .where(eq(housekeeping.id, housekeepingResult.parentHousekeepingId))
        .then((results) => results[0]);

      if (parentResult) {
        parentHousekeeping = {
          id: parentResult.id,
          type: parentResult.type,
        };
      }
    }

    return {
      id: housekeepingResult.id,
      businessId: housekeepingResult.businessId,
      type: housekeepingResult.type,
      status: housekeepingResult.status,
      actualStartTime: housekeepingResult.actualStartTime,
      actualEndTime: housekeepingResult.actualEndTime,
      accommodationUnit: {
        id: housekeepingResult.accommodationUnitId,
        name: housekeepingResult.accommodationUnitName,
        unitNumber: housekeepingResult.accommodationUnitNumber,
      },
      task: housekeepingResult.taskId
        ? {
            id: housekeepingResult.taskId,
            title: housekeepingResult.taskTitle,
            status: housekeepingResult.taskStatus,
          }
        : undefined,
      recurringActivity: undefined, // TODO: Implement when recurring activities available
      parentHousekeeping,
      checklist: housekeepingResult.checklist,
      notes: housekeepingResult.notes,
      issuesFound: housekeepingResult.issuesFound,
      inspectedBy: housekeepingResult.inspectedById
        ? {
            id: housekeepingResult.inspectedById,
            displayName: housekeepingResult.inspectedByDisplayName,
            profileImageUrl: housekeepingResult.inspectedByProfileImageId,
          }
        : undefined,
      inspectionNotes: housekeepingResult.inspectionNotes,
      inspectionDate: housekeepingResult.inspectionDate,
      qualityScore: housekeepingResult.qualityScore,
      createdBy: housekeepingResult.createdByName,
      updatedBy: housekeepingResult.updatedByName,
      createdAt: housekeepingResult.createdAt,
      updatedAt: housekeepingResult.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateHousekeepingDto: UpdateHousekeepingDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if housekeeping record exists
      const existingHousekeeping = await this.db
        .select()
        .from(housekeeping)
        .where(
          and(
            eq(housekeeping.id, id),
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingHousekeeping) {
        throw new NotFoundException('Housekeeping record not found');
      }

      // Check if accommodation unit exists (if being updated)
      if (updateHousekeepingDto.accommodationUnitId) {
        const accommodationUnit = await this.db
          .select()
          .from(accommodationUnits)
          .where(
            and(
              eq(
                accommodationUnits.id,
                updateHousekeepingDto.accommodationUnitId,
              ),
              eq(accommodationUnits.businessId, businessId),
              eq(accommodationUnits.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!accommodationUnit) {
          throw new NotFoundException('Accommodation unit not found');
        }
      }

      // Update housekeeping record
      const [updatedHousekeeping] = await this.db
        .update(housekeeping)
        .set({
          type: updateHousekeepingDto.type,
          status: updateHousekeepingDto.status,
          actualStartTime: updateHousekeepingDto.actualStartTime
            ? new Date(updateHousekeepingDto.actualStartTime)
            : undefined,
          actualEndTime: updateHousekeepingDto.actualEndTime
            ? new Date(updateHousekeepingDto.actualEndTime)
            : undefined,
          accommodationUnitId: updateHousekeepingDto.accommodationUnitId,
          checklist: updateHousekeepingDto.checklist
            ? { items: updateHousekeepingDto.checklist }
            : undefined,
          notes: updateHousekeepingDto.notes,
          issuesFound: updateHousekeepingDto.issuesFound,
          inspectedBy: updateHousekeepingDto.inspectedBy,
          inspectionNotes: updateHousekeepingDto.inspectionNotes,
          inspectionDate: updateHousekeepingDto.inspectionDate
            ? new Date(updateHousekeepingDto.inspectionDate)
            : undefined,
          qualityScore: updateHousekeepingDto.qualityScore,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(housekeeping.id, id),
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.isDeleted, false),
          ),
        )
        .returning();

      // Log the housekeeping update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.TASK,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedHousekeeping.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update housekeeping record');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateHousekeepingDto: UpdateHousekeepingDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateHousekeepingDto, metadata);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if housekeeping record exists
      const existingHousekeeping = await this.db
        .select()
        .from(housekeeping)
        .where(
          and(
            eq(housekeeping.id, id),
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingHousekeeping) {
        throw new NotFoundException('Housekeeping record not found');
      }

      // Soft delete the housekeeping record
      await this.db
        .update(housekeeping)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(housekeeping.id, id),
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.isDeleted, false),
          ),
        );

      // Log the housekeeping deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.TASK,
        userId,
        businessId,
        {
          reason: `Housekeeping record "${existingHousekeeping.type}" was deleted`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Housekeeping record deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete housekeeping record');
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedIds: string[];
    failedIds: string[];
    totalProcessed: number;
    successCount: number;
    failureCount: number;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        await this.remove(userId, businessId, id, metadata);
        deletedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    // Log bulk deletion operation if any records were deleted
    if (deletedIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.TASK,
        deletedIds,
        {
          successCount: deletedIds.length,
          errorCount: failedIds.length,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: deletedIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      deletedIds,
      failedIds,
      totalProcessed: ids.length,
      successCount: deletedIds.length,
      failureCount: failedIds.length,
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<HousekeepingSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const housekeepingResults = await this.db
      .select({
        id: housekeeping.id,
        type: housekeeping.type,
        actualStartTime: housekeeping.actualStartTime,
        qualityScore: housekeeping.qualityScore,
        accommodationUnitName: accommodationUnits.name,
      })
      .from(housekeeping)
      .leftJoin(
        accommodationUnits,
        eq(housekeeping.accommodationUnitId, accommodationUnits.id),
      )
      .where(
        and(
          eq(housekeeping.businessId, businessId),
          eq(housekeeping.isDeleted, false),
        ),
      )
      .orderBy(desc(housekeeping.createdAt));

    return housekeepingResults.map((row) => ({
      id: row.id,
      type: row.type,
      accommodationUnitName: row.accommodationUnitName,
      actualStartTime: row.actualStartTime,
      qualityScore: row.qualityScore,
    }));
  }

  // Utility method to get housekeeping records by accommodation unit
  async findByAccommodationUnit(
    _userId: string,
    businessId: string | null,
    accommodationUnitId: string,
  ): Promise<HousekeepingSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const housekeepingResults = await this.db
      .select({
        id: housekeeping.id,
        type: housekeeping.type,
        actualStartTime: housekeeping.actualStartTime,
        qualityScore: housekeeping.qualityScore,
        accommodationUnitName: accommodationUnits.name,
      })
      .from(housekeeping)
      .leftJoin(
        accommodationUnits,
        eq(housekeeping.accommodationUnitId, accommodationUnits.id),
      )
      .where(
        and(
          eq(housekeeping.businessId, businessId),
          eq(housekeeping.accommodationUnitId, accommodationUnitId),
          eq(housekeeping.isDeleted, false),
        ),
      )
      .orderBy(desc(housekeeping.createdAt));

    return housekeepingResults.map((row) => ({
      id: row.id,
      type: row.type,
      accommodationUnitName: row.accommodationUnitName,
      actualStartTime: row.actualStartTime,
      qualityScore: row.qualityScore,
    }));
  }

  // Utility method to get housekeeping records by type
  async findByType(
    _userId: string,
    businessId: string | null,
    type: HousekeepingType,
  ): Promise<HousekeepingSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const housekeepingResults = await this.db
      .select({
        id: housekeeping.id,
        type: housekeeping.type,
        actualStartTime: housekeeping.actualStartTime,
        qualityScore: housekeeping.qualityScore,
        accommodationUnitName: accommodationUnits.name,
      })
      .from(housekeeping)
      .leftJoin(
        accommodationUnits,
        eq(housekeeping.accommodationUnitId, accommodationUnits.id),
      )
      .where(
        and(
          eq(housekeeping.businessId, businessId),
          eq(housekeeping.type, type),
          eq(housekeeping.isDeleted, false),
        ),
      )
      .orderBy(desc(housekeeping.createdAt));

    return housekeepingResults.map((row) => ({
      id: row.id,
      type: row.type,
      accommodationUnitName: row.accommodationUnitName,
      actualStartTime: row.actualStartTime,
      qualityScore: row.qualityScore,
    }));
  }

  // Status management methods
  async updateStatus(
    userId: string,
    businessId: string | null,
    id: string,
    status: HousekeepingStatus,
    notes?: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; status: HousekeepingStatus; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if housekeeping record exists
      const existingHousekeeping = await this.db
        .select()
        .from(housekeeping)
        .where(
          and(
            eq(housekeeping.id, id),
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingHousekeeping) {
        throw new NotFoundException('Housekeeping record not found');
      }

      // Update status
      const [updatedHousekeeping] = await this.db
        .update(housekeeping)
        .set({
          status,
          notes: notes || existingHousekeeping.notes,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(housekeeping.id, id),
            eq(housekeeping.businessId, businessId),
            eq(housekeeping.isDeleted, false),
          ),
        )
        .returning();

      // Log the status update activity
      await this.activityLogService.logStatusChange(
        id,
        EntityType.TASK,
        existingHousekeeping.status,
        status,
        userId,
        businessId,
        {
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedHousekeeping.id,
        status: updatedHousekeeping.status,
        message: 'Status updated successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update housekeeping status');
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: HousekeepingStatus,
    notes?: string,
    metadata?: ActivityMetadata,
  ): Promise<{
    updatedIds: string[];
    failedIds: string[];
    totalProcessed: number;
    successCount: number;
    failureCount: number;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const updatedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        await this.updateStatus(
          userId,
          businessId,
          id,
          status,
          notes,
          metadata,
        );
        updatedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    // Log bulk status update operation if any records were updated
    if (updatedIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_STATUS_CHANGE,
        EntityType.TASK,
        updatedIds,
        {
          status,
          successCount: updatedIds.length,
          errorCount: failedIds.length,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: updatedIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      updatedIds,
      failedIds,
      totalProcessed: ids.length,
      successCount: updatedIds.length,
      failureCount: failedIds.length,
    };
  }
}
