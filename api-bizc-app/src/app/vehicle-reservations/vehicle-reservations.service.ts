import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateVehicleReservationDto } from './dto/create-vehicle-reservation.dto';
import { UpdateVehicleReservationDto } from './dto/update-vehicle-reservation.dto';
import { VehicleReservationDto } from './dto/vehicle-reservation.dto';
import { VehicleReservationSlimDto } from './dto/vehicle-reservation-slim.dto';
import { VehicleReservationListDto } from './dto/vehicle-reservation-list.dto';
import {
  vehicleReservations,
  vehicleReservationVehicles,
} from '../drizzle/schema/vehicle-reservations.schema';
import { vehicles } from '../drizzle/schema/vehicles.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
  SQL,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { VehicleReservationStatus } from '../shared/types/vehicle.enum';
import { DiscountType } from '@shared/types/index';

@Injectable()
export class VehicleReservationsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createVehicleReservationDto: CreateVehicleReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a reservation with the same number already exists for this business
      const existingReservation = await this.db
        .select()
        .from(vehicleReservations)
        .where(
          and(
            eq(vehicleReservations.businessId, businessId),
            ilike(
              vehicleReservations.reservationNumber,
              createVehicleReservationDto.reservationNumber,
            ),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingReservation) {
        throw new ConflictException(
          `A vehicle reservation with the number '${createVehicleReservationDto.reservationNumber}' already exists for this business`,
        );
      }

      // Check if reference number is unique (if provided)
      if (createVehicleReservationDto.referenceNumber) {
        const existingReferenceReservation = await this.db
          .select()
          .from(vehicleReservations)
          .where(
            and(
              eq(vehicleReservations.businessId, businessId),
              ilike(
                vehicleReservations.referenceNumber,
                createVehicleReservationDto.referenceNumber,
              ),
              eq(vehicleReservations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingReferenceReservation) {
          throw new ConflictException(
            `A vehicle reservation with the reference number '${createVehicleReservationDto.referenceNumber}' already exists for this business`,
          );
        }
      }

      // Validate vehicles exist and belong to the business
      const vehicleIds = createVehicleReservationDto.vehicles.map(
        (v) => v.vehicleId,
      );
      const existingVehicles = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.businessId, businessId),
            inArray(vehicles.id, vehicleIds),
            eq(vehicles.isDeleted, false),
          ),
        );

      if (existingVehicles.length !== vehicleIds.length) {
        throw new BadRequestException(
          'One or more vehicles do not exist or do not belong to this business',
        );
      }

      // Calculate totals
      let subtotal = 0;
      let total = 0;
      const pickUpDate = new Date(createVehicleReservationDto.pickUpDate);
      const returnDate = new Date(createVehicleReservationDto.returnDate);
      const days = Math.ceil(
        (returnDate.getTime() - pickUpDate.getTime()) / (1000 * 60 * 60 * 24),
      );

      if (days <= 0) {
        throw new BadRequestException('Return date must be after pick up date');
      }

      // Calculate vehicle totals
      const vehicleData = createVehicleReservationDto.vehicles.map(
        (vehicle, index) => {
          const vehicleDailyRate = parseFloat(vehicle.vehicleDailyRate);
          const vehicleSubtotal = vehicleDailyRate * days;
          let vehicleTotal = vehicleSubtotal;

          // Apply vehicle-specific discount
          let vehicleDiscountAmount = 0;
          if (vehicle.vehicleDiscountType && vehicle.vehicleDiscountValue) {
            const discountValue = parseFloat(vehicle.vehicleDiscountValue);
            if (vehicle.vehicleDiscountType === DiscountType.PERCENTAGE) {
              vehicleDiscountAmount = (vehicleSubtotal * discountValue) / 100;
            } else {
              vehicleDiscountAmount = discountValue;
            }
            vehicleTotal = vehicleSubtotal - vehicleDiscountAmount;
          }

          subtotal += vehicleSubtotal;
          total += vehicleTotal;

          return {
            ...vehicle,
            subtotal: vehicleSubtotal.toFixed(2),
            total: vehicleTotal.toFixed(2),
            vehicleDiscountAmount: vehicleDiscountAmount.toFixed(2),
            vehicleOrder: vehicle.vehicleOrder || index + 1,
          };
        },
      );

      // Apply reservation-level discount
      let reservationDiscountAmount = 0;
      if (
        createVehicleReservationDto.discountType &&
        createVehicleReservationDto.discountValue
      ) {
        const discountValue = parseFloat(
          createVehicleReservationDto.discountValue,
        );
        if (
          createVehicleReservationDto.discountType === DiscountType.PERCENTAGE
        ) {
          reservationDiscountAmount = (total * discountValue) / 100;
        } else {
          reservationDiscountAmount = discountValue;
        }
        total = total - reservationDiscountAmount;
      }

      // Start transaction
      const result = await this.db.transaction(async (tx) => {
        // Create the main reservation
        const [reservation] = await tx
          .insert(vehicleReservations)
          .values({
            businessId,
            reservationNumber: createVehicleReservationDto.reservationNumber,
            referenceNumber: createVehicleReservationDto.referenceNumber,
            customerId: createVehicleReservationDto.customerId,
            pickUpDate: new Date(createVehicleReservationDto.pickUpDate),
            returnDate: new Date(createVehicleReservationDto.returnDate),
            status:
              createVehicleReservationDto.status ||
              VehicleReservationStatus.PENDING,
            reservationSource: createVehicleReservationDto.reservationSource,
            dailyRate: createVehicleReservationDto.dailyRate,
            subtotal: subtotal.toFixed(2),
            total: total.toFixed(2),
            discountType: createVehicleReservationDto.discountType,
            discountValue: createVehicleReservationDto.discountValue,
            discountAmount: reservationDiscountAmount.toFixed(2),
            discountReason: createVehicleReservationDto.discountReason,
            paymentStatus: createVehicleReservationDto.paymentStatus,
            depositRequired: createVehicleReservationDto.depositRequired,
            taxType: createVehicleReservationDto.taxType,
            notes: createVehicleReservationDto.notes,
            pickUpLocationType: createVehicleReservationDto.pickUpLocationType,
            returnLocationType: createVehicleReservationDto.returnLocationType,
            pickUpLocation: createVehicleReservationDto.pickUpLocation,
            returnLocation: createVehicleReservationDto.returnLocation,
            pickUpAddress: createVehicleReservationDto.pickUpAddress,
            returnAddress: createVehicleReservationDto.returnAddress,
            pickUpInstructions: createVehicleReservationDto.pickUpInstructions,
            returnInstructions: createVehicleReservationDto.returnInstructions,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: vehicleReservations.id });

        // Create vehicle reservation entries
        for (const vehicleItem of vehicleData) {
          await tx.insert(vehicleReservationVehicles).values({
            businessId,
            reservationId: reservation.id,
            vehicleId: vehicleItem.vehicleId,
            vehicleDailyRate: vehicleItem.vehicleDailyRate,
            subtotal: vehicleItem.subtotal,
            total: vehicleItem.total,
            vehicleDiscountType: vehicleItem.vehicleDiscountType,
            vehicleDiscountValue: vehicleItem.vehicleDiscountValue,
            vehicleDiscountAmount: vehicleItem.vehicleDiscountAmount,
            vehicleDiscountReason: vehicleItem.vehicleDiscountReason,
            vehicleWithDriver: vehicleItem.vehicleWithDriver || false,
            vehicleDriverId: vehicleItem.vehicleDriverId,
            vehicleDriverNotes: vehicleItem.vehicleDriverNotes,
            vehicleExternalDriverName: vehicleItem.vehicleExternalDriverName,
            vehicleExternalDriverLicense:
              vehicleItem.vehicleExternalDriverLicense,
            vehicleExternalDriverContact:
              vehicleItem.vehicleExternalDriverContact,
            vehiclePickUpTime: vehicleItem.vehiclePickUpTime
              ? new Date(vehicleItem.vehiclePickUpTime)
              : null,
            vehicleReturnTime: vehicleItem.vehicleReturnTime
              ? new Date(vehicleItem.vehicleReturnTime)
              : null,
            notes: vehicleItem.notes,
            vehicleOrder: vehicleItem.vehicleOrder,
            vehicleTaxRateId: vehicleItem.vehicleTaxRateId,
            createdBy: userId,
            updatedBy: userId,
          });
        }

        return reservation;
      });

      // Log activity
      await this.activityLogService.logCreate(
        result.id,
        EntityType.RESERVATION,
        userId,
        businessId,
        {
          reason: `Created vehicle reservation: ${createVehicleReservationDto.reservationNumber}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: result.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create vehicle reservation: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createVehicleReservationDto: CreateVehicleReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createVehicleReservationDto,
      metadata,
    );
    return {
      id: result.id,
      message: 'Vehicle reservation created successfully',
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page?: number,
    limit?: number,
    from?: string,
    to?: string,
    reservationNumber?: string,
    referenceNumber?: string,
    status?: string,
    paymentStatus?: string,
    customerId?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: VehicleReservationListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const pageNumber = page || 1;
      const pageSize = limit || 10;
      const offset = (pageNumber - 1) * pageSize;

      // Build base conditions
      const conditions = [
        eq(vehicleReservations.businessId, businessId),
        eq(vehicleReservations.isDeleted, false),
      ];

      // Add date range filters
      if (from) {
        conditions.push(gte(vehicleReservations.pickUpDate, new Date(from)));
      }
      if (to) {
        conditions.push(lte(vehicleReservations.returnDate, new Date(to)));
      }

      // Add simple filters
      if (reservationNumber) {
        conditions.push(
          ilike(
            vehicleReservations.reservationNumber,
            `%${reservationNumber}%`,
          ),
        );
      }
      if (referenceNumber) {
        conditions.push(
          ilike(vehicleReservations.referenceNumber, `%${referenceNumber}%`),
        );
      }
      if (customerId) {
        conditions.push(eq(vehicleReservations.customerId, customerId));
      }

      // Add status filters
      if (status) {
        const statusValues = status.split(',').map((s) => s.trim() as any);
        conditions.push(inArray(vehicleReservations.status, statusValues));
      }
      if (paymentStatus) {
        const paymentStatusValues = paymentStatus
          .split(',')
          .map((s) => s.trim() as any);
        conditions.push(
          inArray(vehicleReservations.paymentStatus, paymentStatusValues),
        );
      }

      // Parse advanced filters
      let advancedConditions = [];
      if (filters) {
        try {
          const parsedFilters = JSON.parse(filters);
          advancedConditions = this.buildAdvancedFilters(parsedFilters);
        } catch {
          throw new BadRequestException('Invalid filters format');
        }
      }

      // Combine conditions
      let whereCondition: SQL<unknown>;
      if (advancedConditions.length > 0) {
        const baseCondition = and(...conditions);
        const advancedCondition =
          joinOperator === 'or'
            ? or(...advancedConditions)
            : and(...advancedConditions);
        whereCondition = and(baseCondition, advancedCondition);
      } else {
        whereCondition = and(...conditions);
      }

      // Parse sort configuration
      let orderBy = [desc(vehicleReservations.createdAt)]; // default sort
      if (sort) {
        try {
          const sortConfig = JSON.parse(sort);
          orderBy = this.buildSortOrder(sortConfig);
        } catch {
          // Keep default sort if parsing fails
        }
      }

      // Get total count
      const totalResult = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(vehicleReservations)
        .where(whereCondition);
      const total = totalResult[0]?.count || 0;

      // Get paginated data with joins
      const reservationsData = await this.db
        .select({
          id: vehicleReservations.id,
          reservationNumber: vehicleReservations.reservationNumber,
          referenceNumber: vehicleReservations.referenceNumber,
          customerId: vehicleReservations.customerId,
          pickUpDate: vehicleReservations.pickUpDate,
          returnDate: vehicleReservations.returnDate,
          status: vehicleReservations.status,
          reservationSource: vehicleReservations.reservationSource,
          paymentStatus: vehicleReservations.paymentStatus,
          dailyRate: vehicleReservations.dailyRate,
          total: vehicleReservations.total,
          balanceDue: vehicleReservations.balanceDue,
          createdAt: vehicleReservations.createdAt,
          updatedAt: vehicleReservations.updatedAt,
          createdBy: vehicleReservations.createdBy,
          updatedBy: vehicleReservations.updatedBy,
          // Customer info
          customerFirstName: customers.firstName,
          customerLastName: customers.lastName,
          customerEmail: customers.email,
          customerPhone: customers.phoneNumber,
          // Creator info
          creatorName: users.name,
          creatorAvatar: users.avatar,
        })
        .from(vehicleReservations)
        .leftJoin(customers, eq(vehicleReservations.customerId, customers.id))
        .leftJoin(users, eq(vehicleReservations.createdBy, users.id))
        .where(whereCondition)
        .orderBy(...orderBy)
        .limit(pageSize)
        .offset(offset);

      // Get vehicle counts and summaries for each reservation
      const reservationIds = reservationsData.map((r) => r.id);
      const vehicleCounts =
        reservationIds.length > 0
          ? await this.db
              .select({
                reservationId: vehicleReservationVehicles.reservationId,
                vehicleCount: sql<number>`count(*)`,
                vehicleSummary: sql<string>`string_agg(${vehicles.make} || ' ' || ${vehicles.model}, ', ')`,
              })
              .from(vehicleReservationVehicles)
              .leftJoin(
                vehicles,
                eq(vehicleReservationVehicles.vehicleId, vehicles.id),
              )
              .where(
                and(
                  inArray(
                    vehicleReservationVehicles.reservationId,
                    reservationIds,
                  ),
                  eq(vehicleReservationVehicles.isDeleted, false),
                ),
              )
              .groupBy(vehicleReservationVehicles.reservationId)
          : [];

      const vehicleCountMap = new Map(
        vehicleCounts.map((vc) => [vc.reservationId, vc]),
      );

      // Transform data
      const data: VehicleReservationListDto[] = reservationsData.map(
        (reservation) => {
          const vehicleInfo = vehicleCountMap.get(reservation.id);

          return {
            id: reservation.id,
            reservationNumber: reservation.reservationNumber,
            referenceNumber: reservation.referenceNumber,
            customerId: reservation.customerId,
            customer: reservation.customerId
              ? {
                  id: reservation.customerId,
                  firstName: reservation.customerFirstName || '',
                  lastName: reservation.customerLastName || '',
                  email: reservation.customerEmail || '',
                  phone: reservation.customerPhone || '',
                }
              : undefined,
            pickUpDate: reservation.pickUpDate.toISOString(),
            returnDate: reservation.returnDate.toISOString(),
            status: reservation.status,
            reservationSource: reservation.reservationSource,
            paymentStatus: reservation.paymentStatus,
            dailyRate: reservation.dailyRate,
            total: reservation.total,
            balanceDue: reservation.balanceDue,
            vehicleCount: vehicleInfo?.vehicleCount || 0,
            vehicleSummary: vehicleInfo?.vehicleSummary || 'No vehicles',
            createdAt: reservation.createdAt.toISOString(),
            updatedAt: reservation.updatedAt.toISOString(),
            createdBy: reservation.createdBy
              ? {
                  id: reservation.createdBy,
                  name: reservation.creatorName || 'Unknown',
                  avatar: reservation.creatorAvatar,
                }
              : undefined,
            updatedBy: reservation.updatedBy
              ? {
                  id: reservation.updatedBy,
                  name: reservation.creatorName || 'Unknown',
                  avatar: reservation.creatorAvatar,
                }
              : undefined,
          };
        },
      );

      const totalPages = Math.ceil(total / pageSize);

      return {
        data,
        total,
        page: pageNumber,
        limit: pageSize,
        totalPages,
        hasNextPage: pageNumber < totalPages,
        hasPrevPage: pageNumber > 1,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch vehicle reservations: ${error.message}`,
      );
    }
  }

  private buildAdvancedFilters(filters: any[]): any[] {
    const conditions = [];

    for (const filter of filters) {
      const { id, value, operator } = filter;

      if (!id || !operator || value === undefined) continue;

      let column: any;
      switch (id) {
        case 'reservationNumber':
          column = vehicleReservations.reservationNumber;
          break;
        case 'referenceNumber':
          column = vehicleReservations.referenceNumber;
          break;
        case 'status':
          column = vehicleReservations.status;
          break;
        case 'paymentStatus':
          column = vehicleReservations.paymentStatus;
          break;
        case 'reservationSource':
          column = vehicleReservations.reservationSource;
          break;
        case 'dailyRate':
          column = vehicleReservations.dailyRate;
          break;
        case 'total':
          column = vehicleReservations.total;
          break;
        default:
          continue;
      }

      switch (operator) {
        case 'iLike':
          conditions.push(ilike(column, `%${value}%`));
          break;
        case 'notILike':
          conditions.push(sql`${column} NOT ILIKE ${`%${value}%`}`);
          break;
        case 'eq':
          conditions.push(eq(column, value));
          break;
        case 'ne':
          conditions.push(sql`${column} != ${value}`);
          break;
        case 'isEmpty':
          conditions.push(isNull(column));
          break;
        case 'isNotEmpty':
          conditions.push(sql`${column} IS NOT NULL`);
          break;
        case 'gte':
          conditions.push(gte(column, value));
          break;
        case 'lte':
          conditions.push(lte(column, value));
          break;
      }
    }

    return conditions;
  }

  private buildSortOrder(sortConfig: any[]): any[] {
    const orderBy = [];

    for (const sort of sortConfig) {
      const { id, desc: isDesc } = sort;

      let column: any;
      switch (id) {
        case 'reservationNumber':
          column = vehicleReservations.reservationNumber;
          break;
        case 'pickUpDate':
          column = vehicleReservations.pickUpDate;
          break;
        case 'returnDate':
          column = vehicleReservations.returnDate;
          break;
        case 'status':
          column = vehicleReservations.status;
          break;
        case 'paymentStatus':
          column = vehicleReservations.paymentStatus;
          break;
        case 'total':
          column = vehicleReservations.total;
          break;
        case 'createdAt':
          column = vehicleReservations.createdAt;
          break;
        case 'updatedAt':
          column = vehicleReservations.updatedAt;
          break;
        default:
          continue;
      }

      orderBy.push(isDesc ? desc(column) : asc(column));
    }

    return orderBy.length > 0 ? orderBy : [desc(vehicleReservations.createdAt)];
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<VehicleReservationSlimDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const reservationsData = await this.db
        .select({
          id: vehicleReservations.id,
          reservationNumber: vehicleReservations.reservationNumber,
          referenceNumber: vehicleReservations.referenceNumber,
          pickUpDate: vehicleReservations.pickUpDate,
          returnDate: vehicleReservations.returnDate,
          status: vehicleReservations.status,
          paymentStatus: vehicleReservations.paymentStatus,
          total: vehicleReservations.total,
          createdAt: vehicleReservations.createdAt,
          customerFirstName: customers.firstName,
          customerLastName: customers.lastName,
        })
        .from(vehicleReservations)
        .leftJoin(customers, eq(vehicleReservations.customerId, customers.id))
        .where(
          and(
            eq(vehicleReservations.businessId, businessId),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .orderBy(desc(vehicleReservations.createdAt));

      // Get vehicle counts for each reservation
      const reservationIds = reservationsData.map((r) => r.id);
      const vehicleCounts =
        reservationIds.length > 0
          ? await this.db
              .select({
                reservationId: vehicleReservationVehicles.reservationId,
                vehicleCount: sql<number>`count(*)`,
              })
              .from(vehicleReservationVehicles)
              .where(
                and(
                  inArray(
                    vehicleReservationVehicles.reservationId,
                    reservationIds,
                  ),
                  eq(vehicleReservationVehicles.isDeleted, false),
                ),
              )
              .groupBy(vehicleReservationVehicles.reservationId)
          : [];

      const vehicleCountMap = new Map(
        vehicleCounts.map((vc) => [vc.reservationId, vc.vehicleCount]),
      );

      return reservationsData.map((reservation) => ({
        id: reservation.id,
        reservationNumber: reservation.reservationNumber,
        referenceNumber: reservation.referenceNumber,
        customerName:
          reservation.customerFirstName && reservation.customerLastName
            ? `${reservation.customerFirstName} ${reservation.customerLastName}`
            : undefined,
        pickUpDate: reservation.pickUpDate.toISOString(),
        returnDate: reservation.returnDate.toISOString(),
        status: reservation.status,
        paymentStatus: reservation.paymentStatus,
        total: reservation.total,
        vehicleCount: vehicleCountMap.get(reservation.id) || 0,
        createdAt: reservation.createdAt.toISOString(),
      }));
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch vehicle reservations: ${error.message}`,
      );
    }
  }

  async findOne(id: string): Promise<VehicleReservationDto> {
    try {
      // Get the reservation with all related data
      const reservationData = await this.db
        .select({
          id: vehicleReservations.id,
          businessId: vehicleReservations.businessId,
          reservationNumber: vehicleReservations.reservationNumber,
          referenceNumber: vehicleReservations.referenceNumber,
          customerId: vehicleReservations.customerId,
          pickUpDate: vehicleReservations.pickUpDate,
          returnDate: vehicleReservations.returnDate,
          actualPickUpTime: vehicleReservations.actualPickUpTime,
          actualReturnTime: vehicleReservations.actualReturnTime,
          status: vehicleReservations.status,
          reservationSource: vehicleReservations.reservationSource,
          dailyRate: vehicleReservations.dailyRate,
          subtotal: vehicleReservations.subtotal,
          total: vehicleReservations.total,
          discountType: vehicleReservations.discountType,
          discountValue: vehicleReservations.discountValue,
          discountAmount: vehicleReservations.discountAmount,
          discountReason: vehicleReservations.discountReason,
          paymentStatus: vehicleReservations.paymentStatus,
          depositRequired: vehicleReservations.depositRequired,
          depositPaid: vehicleReservations.depositPaid,
          balanceDue: vehicleReservations.balanceDue,
          taxType: vehicleReservations.taxType,
          notes: vehicleReservations.notes,
          cancellationReason: vehicleReservations.cancellationReason,
          cancellationDate: vehicleReservations.cancellationDate,
          pickUpLocationType: vehicleReservations.pickUpLocationType,
          returnLocationType: vehicleReservations.returnLocationType,
          pickUpLocation: vehicleReservations.pickUpLocation,
          returnLocation: vehicleReservations.returnLocation,
          pickUpAddress: vehicleReservations.pickUpAddress,
          returnAddress: vehicleReservations.returnAddress,
          pickUpInstructions: vehicleReservations.pickUpInstructions,
          returnInstructions: vehicleReservations.returnInstructions,
          flightArrivalDate: vehicleReservations.flightArrivalDate,
          flightArrivalTime: vehicleReservations.flightArrivalTime,
          flightDepartureDate: vehicleReservations.flightDepartureDate,
          flightDepartureTime: vehicleReservations.flightDepartureTime,
          arrivalFlightNumber: vehicleReservations.arrivalFlightNumber,
          departureFlightNumber: vehicleReservations.departureFlightNumber,
          arrivalAirline: vehicleReservations.arrivalAirline,
          departureAirline: vehicleReservations.departureAirline,
          confirmationSent: vehicleReservations.confirmationSent,
          confirmationSentAt: vehicleReservations.confirmationSentAt,
          reminderSent: vehicleReservations.reminderSent,
          reminderSentAt: vehicleReservations.reminderSentAt,
          createdAt: vehicleReservations.createdAt,
          updatedAt: vehicleReservations.updatedAt,
          createdBy: vehicleReservations.createdBy,
          updatedBy: vehicleReservations.updatedBy,
          // Customer info
          customerFirstName: customers.firstName,
          customerLastName: customers.lastName,
          customerEmail: customers.email,
          customerPhone: customers.phoneNumber,
          // Creator info
          creatorName: users.name,
          creatorAvatar: users.avatar,
        })
        .from(vehicleReservations)
        .leftJoin(customers, eq(vehicleReservations.customerId, customers.id))
        .leftJoin(users, eq(vehicleReservations.createdBy, users.id))
        .where(
          and(
            eq(vehicleReservations.id, id),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!reservationData) {
        throw new NotFoundException('Vehicle reservation not found');
      }

      // Get vehicles for this reservation
      const vehiclesData = await this.db
        .select({
          id: vehicleReservationVehicles.id,
          vehicleId: vehicleReservationVehicles.vehicleId,
          vehicleDailyRate: vehicleReservationVehicles.vehicleDailyRate,
          subtotal: vehicleReservationVehicles.subtotal,
          total: vehicleReservationVehicles.total,
          vehicleDiscountType: vehicleReservationVehicles.vehicleDiscountType,
          vehicleDiscountValue: vehicleReservationVehicles.vehicleDiscountValue,
          vehicleDiscountAmount:
            vehicleReservationVehicles.vehicleDiscountAmount,
          vehicleDiscountReason:
            vehicleReservationVehicles.vehicleDiscountReason,
          vehicleWithDriver: vehicleReservationVehicles.vehicleWithDriver,
          vehicleDriverId: vehicleReservationVehicles.vehicleDriverId,
          vehicleDriverNotes: vehicleReservationVehicles.vehicleDriverNotes,
          vehicleExternalDriverName:
            vehicleReservationVehicles.vehicleExternalDriverName,
          vehicleExternalDriverLicense:
            vehicleReservationVehicles.vehicleExternalDriverLicense,
          vehicleExternalDriverContact:
            vehicleReservationVehicles.vehicleExternalDriverContact,
          vehiclePickUpTime: vehicleReservationVehicles.vehiclePickUpTime,
          vehicleReturnTime: vehicleReservationVehicles.vehicleReturnTime,
          pickUpOdometer: vehicleReservationVehicles.pickUpOdometer,
          returnOdometer: vehicleReservationVehicles.returnOdometer,
          pickUpFuelLevel: vehicleReservationVehicles.pickUpFuelLevel,
          returnFuelLevel: vehicleReservationVehicles.returnFuelLevel,
          pickUpConditionNotes: vehicleReservationVehicles.pickUpConditionNotes,
          returnConditionNotes: vehicleReservationVehicles.returnConditionNotes,
          notes: vehicleReservationVehicles.notes,
          vehicleOrder: vehicleReservationVehicles.vehicleOrder,
          vehicleTaxRateId: vehicleReservationVehicles.vehicleTaxRateId,
          vehicleReservationCreatedAt: vehicleReservationVehicles.createdAt,
          vehicleReservationUpdatedAt: vehicleReservationVehicles.updatedAt,
          vehicleReservationCreatedBy: vehicleReservationVehicles.createdBy,
          vehicleReservationUpdatedBy: vehicleReservationVehicles.updatedBy,
          // Vehicle info
          vehicleNumber: vehicles.licensePlate,
          make: vehicles.make,
          model: vehicles.model,
          year: vehicles.year,
          color: vehicles.color,
          // Driver info
          driverDisplayName: staffMembers.displayName,
          driverProfileImageUrl: sql<string>`NULL`, // TODO: Join with media table for profile image
          // Tax rate info
          taxRateName: taxes.taxName,
          taxRateRate: taxes.salesRate,
        })
        .from(vehicleReservationVehicles)
        .leftJoin(
          vehicles,
          eq(vehicleReservationVehicles.vehicleId, vehicles.id),
        )
        .leftJoin(
          staffMembers,
          eq(vehicleReservationVehicles.vehicleDriverId, staffMembers.id),
        )
        .leftJoin(
          taxes,
          eq(vehicleReservationVehicles.vehicleTaxRateId, taxes.id),
        )
        .where(
          and(
            eq(vehicleReservationVehicles.reservationId, id),
            eq(vehicleReservationVehicles.isDeleted, false),
          ),
        )
        .orderBy(asc(vehicleReservationVehicles.vehicleOrder));

      // Transform the data
      const vehicleReservationDto: VehicleReservationDto = {
        id: reservationData.id,
        businessId: reservationData.businessId,
        reservationNumber: reservationData.reservationNumber,
        referenceNumber: reservationData.referenceNumber,
        customerId: reservationData.customerId,
        customer: reservationData.customerId
          ? {
              id: reservationData.customerId,
              firstName: reservationData.customerFirstName || '',
              lastName: reservationData.customerLastName || '',
              email: reservationData.customerEmail || '',
              phone: reservationData.customerPhone || '',
            }
          : undefined,
        pickUpDate: reservationData.pickUpDate.toISOString(),
        returnDate: reservationData.returnDate.toISOString(),
        actualPickUpTime: reservationData.actualPickUpTime?.toISOString(),
        actualReturnTime: reservationData.actualReturnTime?.toISOString(),
        status: reservationData.status,
        reservationSource: reservationData.reservationSource,
        dailyRate: reservationData.dailyRate,
        subtotal: reservationData.subtotal,
        total: reservationData.total,
        discountType: reservationData.discountType,
        discountValue: reservationData.discountValue,
        discountAmount: reservationData.discountAmount,
        discountReason: reservationData.discountReason,
        paymentStatus: reservationData.paymentStatus,
        depositRequired: reservationData.depositRequired,
        depositPaid: reservationData.depositPaid,
        balanceDue: reservationData.balanceDue,
        taxType: reservationData.taxType,
        notes: reservationData.notes,
        cancellationReason: reservationData.cancellationReason,
        cancellationDate: reservationData.cancellationDate?.toISOString(),
        pickUpLocationType: reservationData.pickUpLocationType,
        returnLocationType: reservationData.returnLocationType,
        pickUpLocation: reservationData.pickUpLocation,
        returnLocation: reservationData.returnLocation,
        pickUpAddress: reservationData.pickUpAddress,
        returnAddress: reservationData.returnAddress,
        pickUpInstructions: reservationData.pickUpInstructions,
        returnInstructions: reservationData.returnInstructions,
        flightArrivalDate: reservationData.flightArrivalDate?.toISOString(),
        flightArrivalTime: reservationData.flightArrivalTime,
        flightDepartureDate: reservationData.flightDepartureDate?.toISOString(),
        flightDepartureTime: reservationData.flightDepartureTime,
        arrivalFlightNumber: reservationData.arrivalFlightNumber,
        departureFlightNumber: reservationData.departureFlightNumber,
        arrivalAirline: reservationData.arrivalAirline,
        departureAirline: reservationData.departureAirline,
        confirmationSent: reservationData.confirmationSent,
        confirmationSentAt: reservationData.confirmationSentAt?.toISOString(),
        reminderSent: reservationData.reminderSent,
        reminderSentAt: reservationData.reminderSentAt?.toISOString(),
        vehicles: vehiclesData.map((vehicle) => ({
          id: vehicle.id,
          vehicleId: vehicle.vehicleId,
          vehicle: {
            id: vehicle.vehicleId,
            vehicleNumber: vehicle.vehicleNumber || '',
            make: vehicle.make || '',
            model: vehicle.model || '',
            year: vehicle.year || 0,
            color: vehicle.color || '',
          },
          vehicleDailyRate: vehicle.vehicleDailyRate,
          subtotal: vehicle.subtotal,
          total: vehicle.total,
          vehicleDiscountType: vehicle.vehicleDiscountType,
          vehicleDiscountValue: vehicle.vehicleDiscountValue,
          vehicleDiscountAmount: vehicle.vehicleDiscountAmount,
          vehicleDiscountReason: vehicle.vehicleDiscountReason,
          vehicleWithDriver: vehicle.vehicleWithDriver,
          vehicleDriverId: vehicle.vehicleDriverId,
          vehicleDriver: vehicle.vehicleDriverId
            ? {
                id: vehicle.vehicleDriverId,
                displayName: vehicle.driverDisplayName || '',
                profileImageUrl: vehicle.driverProfileImageUrl,
              }
            : undefined,
          vehicleDriverNotes: vehicle.vehicleDriverNotes,
          vehicleExternalDriverName: vehicle.vehicleExternalDriverName,
          vehicleExternalDriverLicense: vehicle.vehicleExternalDriverLicense,
          vehicleExternalDriverContact: vehicle.vehicleExternalDriverContact,
          vehiclePickUpTime: vehicle.vehiclePickUpTime?.toISOString(),
          vehicleReturnTime: vehicle.vehicleReturnTime?.toISOString(),
          pickUpOdometer: vehicle.pickUpOdometer,
          returnOdometer: vehicle.returnOdometer,
          pickUpFuelLevel: vehicle.pickUpFuelLevel,
          returnFuelLevel: vehicle.returnFuelLevel,
          pickUpConditionNotes: vehicle.pickUpConditionNotes,
          returnConditionNotes: vehicle.returnConditionNotes,
          notes: vehicle.notes,
          vehicleOrder: vehicle.vehicleOrder,
          vehicleTaxRateId: vehicle.vehicleTaxRateId,
          vehicleTaxRate: vehicle.vehicleTaxRateId
            ? {
                id: vehicle.vehicleTaxRateId,
                name: vehicle.taxRateName || '',
                rate: vehicle.taxRateRate || '',
              }
            : undefined,
          createdAt: vehicle.vehicleReservationCreatedAt.toISOString(),
          updatedAt: vehicle.vehicleReservationUpdatedAt.toISOString(),
          createdBy: vehicle.vehicleReservationCreatedBy
            ? {
                id: vehicle.vehicleReservationCreatedBy,
                name: reservationData.creatorName || 'Unknown',
                avatar: reservationData.creatorAvatar,
              }
            : undefined,
          updatedBy: vehicle.vehicleReservationUpdatedBy
            ? {
                id: vehicle.vehicleReservationUpdatedBy,
                name: reservationData.creatorName || 'Unknown',
                avatar: reservationData.creatorAvatar,
              }
            : undefined,
        })),
        createdAt: reservationData.createdAt.toISOString(),
        updatedAt: reservationData.updatedAt.toISOString(),
        createdBy: reservationData.createdBy
          ? {
              id: reservationData.createdBy,
              name: reservationData.creatorName || 'Unknown',
              avatar: reservationData.creatorAvatar,
            }
          : undefined,
        updatedBy: reservationData.updatedBy
          ? {
              id: reservationData.updatedBy,
              name: reservationData.creatorName || 'Unknown',
              avatar: reservationData.creatorAvatar,
            }
          : undefined,
      };

      return vehicleReservationDto;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch vehicle reservation: ${error.message}`,
      );
    }
  }

  async checkReservationNumberAvailability(
    businessId: string | null,
    reservationNumber: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingReservation = await this.db
        .select()
        .from(vehicleReservations)
        .where(
          and(
            eq(vehicleReservations.businessId, businessId),
            ilike(vehicleReservations.reservationNumber, reservationNumber),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingReservation };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to check reservation number availability: ${error.message}`,
      );
    }
  }

  async checkReferenceNumberAvailability(
    businessId: string | null,
    referenceNumber: string,
  ): Promise<{ available: boolean }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingReservation = await this.db
        .select()
        .from(vehicleReservations)
        .where(
          and(
            eq(vehicleReservations.businessId, businessId),
            ilike(vehicleReservations.referenceNumber, referenceNumber),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      return { available: !existingReservation };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to check reference number availability: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if reservation exists and belongs to the business
      const existingReservation = await this.db
        .select()
        .from(vehicleReservations)
        .where(
          and(
            eq(vehicleReservations.id, id),
            eq(vehicleReservations.businessId, businessId),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingReservation) {
        throw new NotFoundException('Vehicle reservation not found');
      }

      // Soft delete the reservation and its vehicles in a transaction
      await this.db.transaction(async (tx) => {
        // Soft delete the main reservation
        await tx
          .update(vehicleReservations)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(vehicleReservations.id, id));

        // Soft delete all associated vehicles
        await tx
          .update(vehicleReservationVehicles)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(vehicleReservationVehicles.reservationId, id));
      });

      // Log activity
      await this.activityLogService.logDelete(
        id,
        EntityType.RESERVATION,
        userId,
        businessId,
        {
          reason: `Deleted vehicle reservation: ${existingReservation.reservationNumber}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Vehicle reservation deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete vehicle reservation: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleReservationDto: UpdateVehicleReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if reservation exists and belongs to the business
      const existingReservation = await this.db
        .select()
        .from(vehicleReservations)
        .where(
          and(
            eq(vehicleReservations.id, id),
            eq(vehicleReservations.businessId, businessId),
            eq(vehicleReservations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingReservation) {
        throw new NotFoundException('Vehicle reservation not found');
      }

      // Update the main reservation
      const updateData: any = {
        updatedBy: userId,
      };

      // Add fields that can be updated
      if (updateVehicleReservationDto.status !== undefined) {
        updateData.status = updateVehicleReservationDto.status;
      }
      if (updateVehicleReservationDto.paymentStatus !== undefined) {
        updateData.paymentStatus = updateVehicleReservationDto.paymentStatus;
      }
      if (updateVehicleReservationDto.notes !== undefined) {
        updateData.notes = updateVehicleReservationDto.notes;
      }
      if (updateVehicleReservationDto.actualPickUpTime !== undefined) {
        updateData.actualPickUpTime =
          updateVehicleReservationDto.actualPickUpTime
            ? new Date(updateVehicleReservationDto.actualPickUpTime)
            : null;
      }
      if (updateVehicleReservationDto.actualReturnTime !== undefined) {
        updateData.actualReturnTime =
          updateVehicleReservationDto.actualReturnTime
            ? new Date(updateVehicleReservationDto.actualReturnTime)
            : null;
      }

      await this.db
        .update(vehicleReservations)
        .set(updateData)
        .where(eq(vehicleReservations.id, id));

      // Log activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.RESERVATION,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Vehicle reservation updated successfully',
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update vehicle reservation: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    vehicleReservationIds: string[],
    status: VehicleReservationStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    message: string;
    updatedIds: string[];
    failed?: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!vehicleReservationIds || vehicleReservationIds.length === 0) {
        throw new BadRequestException('No vehicle reservation IDs provided');
      }

      const updatedIds: string[] = [];
      const failed: Array<{ id: string; error: string }> = [];

      // Process each reservation
      for (const id of vehicleReservationIds) {
        try {
          // Check if reservation exists and belongs to the business
          const existingReservation = await this.db
            .select()
            .from(vehicleReservations)
            .where(
              and(
                eq(vehicleReservations.id, id),
                eq(vehicleReservations.businessId, businessId),
                eq(vehicleReservations.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (!existingReservation) {
            failed.push({
              id,
              error: 'Vehicle reservation not found',
            });
            continue;
          }

          // Update the status
          await this.db
            .update(vehicleReservations)
            .set({
              status,
              updatedBy: userId,
            })
            .where(eq(vehicleReservations.id, id));

          updatedIds.push(id);

          // Log activity
          await this.activityLogService.logStatusChange(
            id,
            EntityType.RESERVATION,
            userId,
            businessId,
            existingReservation.status,
            status,
            {
              reason: `Bulk status update for reservation: ${existingReservation.reservationNumber}`,
              ipAddress: metadata?.ipAddress,
              userAgent: metadata?.userAgent,
              sessionId: metadata?.sessionId,
            },
          );
        } catch (error) {
          failed.push({
            id,
            error: error.message,
          });
        }
      }

      return {
        updated: updatedIds.length,
        message: `Successfully updated status for ${updatedIds.length} vehicle reservations`,
        updatedIds,
        failed: failed.length > 0 ? failed : undefined,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update vehicle reservation status: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    vehicleReservationIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
    failed?: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!vehicleReservationIds || vehicleReservationIds.length === 0) {
        throw new BadRequestException('No vehicle reservation IDs provided');
      }

      const deletedIds: string[] = [];
      const failed: Array<{ id: string; error: string }> = [];

      // Process each reservation
      for (const id of vehicleReservationIds) {
        try {
          await this.remove(userId, businessId, id, metadata);
          deletedIds.push(id);
        } catch (error) {
          failed.push({
            id,
            error: error.message,
          });
        }
      }

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} vehicle reservations`,
        deletedIds,
        failed: failed.length > 0 ? failed : undefined,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete vehicle reservations: ${error.message}`,
      );
    }
  }
}
