import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateRestaurantTableDto } from './dto/create-restaurant-table.dto';
import { UpdateRestaurantTableDto } from './dto/update-restaurant-table.dto';
import { RestaurantTableDto } from './dto/restaurant-table.dto';
import { RestaurantTableSlimDto } from './dto/restaurant-table-slim.dto';
import { RestaurantTableListDto } from './dto/restaurant-table-list.dto';
import {
  restaurantTables,
  floorPlans,
  tableReservations,
} from '../drizzle/schema/restaurant-tables.schema';
import { customers } from '../drizzle/schema/customers.schema';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  inArray,
  desc,
  asc,
  sql,
  count,
} from 'drizzle-orm';
import { RestaurantTableStatus } from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { BulkCreateRestaurantTableDto } from './dto/bulk-create-restaurant-table.dto';
import { BulkDeleteRestaurantTableDto } from './dto/bulk-delete-restaurant-table.dto';
import { CreateTableReservationDto } from './dto/create-table-reservation.dto';
import { UpdateTableReservationDto } from './dto/update-table-reservation.dto';
import { TableReservationDto } from './dto/table-reservation.dto';
import { TableReservationListDto } from './dto/table-reservation-list.dto';
import { BulkDeleteTableReservationsDto } from './dto/bulk-delete-table-reservations.dto';

@Injectable()
export class RestaurantTablesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createRestaurantTableDto: CreateRestaurantTableDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a restaurant table with the same table number already exists for this floor
      const existingTable = await this.db
        .select()
        .from(restaurantTables)
        .where(
          and(
            eq(restaurantTables.businessId, businessId),
            eq(restaurantTables.floorId, createRestaurantTableDto.floorId),
            ilike(
              restaurantTables.tableNumber,
              createRestaurantTableDto.tableNumber,
            ),
            eq(restaurantTables.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingTable) {
        throw new ConflictException(
          `Restaurant table with number "${createRestaurantTableDto.tableNumber}" already exists on this floor`,
        );
      }

      // Verify that the floor exists and belongs to the business
      const floor = await this.db
        .select()
        .from(floorPlans)
        .where(
          and(
            eq(floorPlans.id, createRestaurantTableDto.floorId),
            eq(floorPlans.businessId, businessId),
            eq(floorPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!floor) {
        throw new NotFoundException(
          `Floor plan with ID ${createRestaurantTableDto.floorId} not found`,
        );
      }

      // Create the restaurant table
      const newTable = await this.db
        .insert(restaurantTables)
        .values({
          businessId,
          floorId: createRestaurantTableDto.floorId,
          tableNumber: createRestaurantTableDto.tableNumber,
          position: createRestaurantTableDto.position,
          shape: createRestaurantTableDto.shape,
          status:
            createRestaurantTableDto.status || RestaurantTableStatus.AVAILABLE,
          orders: createRestaurantTableDto.orders || 0,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: restaurantTables.id })
        .then((results) => results[0]);

      // Log the activity
      await this.activityLogService.logCreate(
        newTable.id,
        EntityType.RESTAURANT_TABLE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newTable.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      console.error('Error creating restaurant table:', error);
      throw new BadRequestException('Failed to create restaurant table');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createRestaurantTableDto: CreateRestaurantTableDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createRestaurantTableDto, metadata);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    bulkCreateDto: BulkCreateRestaurantTableDto,
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const { tables } = bulkCreateDto;

      if (!tables || tables.length === 0) {
        throw new BadRequestException(
          'At least one restaurant table is required',
        );
      }

      // Check for duplicate table numbers within the request
      const tableNumbers = tables.map((table) =>
        table.tableNumber.toLowerCase(),
      );
      const uniqueTableNumbers = new Set(tableNumbers);
      if (tableNumbers.length !== uniqueTableNumbers.size) {
        throw new ConflictException(
          'Duplicate table numbers found in the request',
        );
      }

      // Check for existing table numbers in the database
      const existingTables = await this.db
        .select({
          tableNumber: restaurantTables.tableNumber,
          floorId: restaurantTables.floorId,
        })
        .from(restaurantTables)
        .where(
          and(
            eq(restaurantTables.businessId, businessId),
            eq(restaurantTables.isDeleted, false),
          ),
        );

      const existingTableMap = new Map();
      existingTables.forEach((table) => {
        const key = `${table.floorId}-${table.tableNumber.toLowerCase()}`;
        existingTableMap.set(key, true);
      });

      const conflictingTables = tables.filter((table) => {
        const key = `${table.floorId}-${table.tableNumber.toLowerCase()}`;
        return existingTableMap.has(key);
      });

      if (conflictingTables.length > 0) {
        const conflictingNumbers = conflictingTables
          .map((table) => table.tableNumber)
          .join(', ');
        throw new ConflictException(
          `Restaurant tables with numbers [${conflictingNumbers}] already exist`,
        );
      }

      // Create all restaurant tables
      const newTables = await this.db
        .insert(restaurantTables)
        .values(
          tables.map((table) => ({
            businessId,
            floorId: table.floorId,
            tableNumber: table.tableNumber,
            position: table.position,
            shape: table.shape,
            status: table.status || RestaurantTableStatus.AVAILABLE,
            orders: table.orders || 0,
            createdBy: userId,
            updatedBy: userId,
          })),
        )
        .returning({ id: restaurantTables.id });

      const ids = newTables.map((table) => table.id);

      // Log the activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.RESTAURANT_TABLE,
        ids,
        {
          tableNumbers: tables.map((table) => table.tableNumber),
          status: RestaurantTableStatus.AVAILABLE,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: ids.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { ids };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error bulk creating restaurant tables:', error);
      throw new BadRequestException('Failed to bulk create restaurant tables');
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    bulkCreateDto: BulkCreateRestaurantTableDto,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(userId, businessId, bulkCreateDto);
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: RestaurantTableDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(restaurantTables.isDeleted, false),
      eq(restaurantTables.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(restaurantTables.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(restaurantTables.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(restaurantTables)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const data = await this.db
      .select({
        id: restaurantTables.id,
        businessId: restaurantTables.businessId,
        floorId: restaurantTables.floorId,
        tableNumber: restaurantTables.tableNumber,
        position: restaurantTables.position,
        shape: restaurantTables.shape,
        status: restaurantTables.status,
        orders: restaurantTables.orders,
        createdBy: restaurantTables.createdBy,
        createdAt: restaurantTables.createdAt,
        updatedAt: restaurantTables.updatedAt,
      })
      .from(restaurantTables)
      .where(and(...whereConditions))
      .orderBy(desc(restaurantTables.createdAt))
      .limit(limit)
      .offset(offset);

    return {
      data: data as RestaurantTableDto[],
      meta: { total, page, totalPages },
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    tableNumber?: string,
    status?: string,
    floorId?: string,
    filters?: string,
    sort?: string,
  ): Promise<{
    data: RestaurantTableListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(restaurantTables.isDeleted, false),
      eq(restaurantTables.businessId, businessId),
    ];

    // Add search filters
    if (tableNumber) {
      whereConditions.push(
        ilike(restaurantTables.tableNumber, `%${tableNumber}%`),
      );
    }

    if (status) {
      const statusValues = status.split(',').map((s) => s.trim());
      if (statusValues.length === 1) {
        whereConditions.push(
          eq(restaurantTables.status, statusValues[0] as RestaurantTableStatus),
        );
      } else {
        whereConditions.push(
          inArray(
            restaurantTables.status,
            statusValues as RestaurantTableStatus[],
          ),
        );
      }
    }

    if (floorId) {
      whereConditions.push(eq(restaurantTables.floorId, floorId));
    }

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(restaurantTables.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(restaurantTables.createdAt, toDate));
      }
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        if (Array.isArray(parsedFilters)) {
          for (const filter of parsedFilters) {
            if (filter.id && filter.value !== undefined && filter.operator) {
              this.applyFilter(whereConditions, filter);
            }
          }
        }
      } catch (error) {
        console.warn('Invalid filters JSON:', error);
      }
    }

    // Determine sort order
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'tableNumber':
          orderBy = isDesc
            ? desc(restaurantTables.tableNumber)
            : asc(restaurantTables.tableNumber);
          break;
        case 'status':
          orderBy = isDesc
            ? desc(restaurantTables.status)
            : asc(restaurantTables.status);
          break;
        case 'orders':
          orderBy = isDesc
            ? desc(restaurantTables.orders)
            : asc(restaurantTables.orders);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(restaurantTables.createdAt)
            : asc(restaurantTables.createdAt);
          break;
        case 'updatedAt':
          orderBy = isDesc
            ? desc(restaurantTables.updatedAt)
            : asc(restaurantTables.updatedAt);
          break;
        default:
          orderBy = desc(restaurantTables.createdAt);
      }
    } else {
      orderBy = desc(restaurantTables.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(restaurantTables)
      .leftJoin(floorPlans, eq(restaurantTables.floorId, floorPlans.id))
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with floor plan names
    const data = await this.db
      .select({
        id: restaurantTables.id,
        tableNumber: restaurantTables.tableNumber,
        floorId: restaurantTables.floorId,
        floorName: floorPlans.name,
        position: restaurantTables.position,
        shape: restaurantTables.shape,
        status: restaurantTables.status,
        orders: restaurantTables.orders,
        createdAt: restaurantTables.createdAt,
        updatedAt: restaurantTables.updatedAt,
      })
      .from(restaurantTables)
      .leftJoin(floorPlans, eq(restaurantTables.floorId, floorPlans.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: data as RestaurantTableListDto[],
      meta: { total, page, totalPages },
    };
  }

  private applyFilter(whereConditions: any[], filter: any): void {
    const { id, value, operator } = filter;

    switch (id) {
      case 'tableNumber':
        if (operator === 'like' || operator === 'iLike') {
          whereConditions.push(
            ilike(restaurantTables.tableNumber, `%${value}%`),
          );
        } else if (operator === 'eq') {
          whereConditions.push(eq(restaurantTables.tableNumber, value));
        }
        break;
      case 'status':
        if (operator === 'eq') {
          whereConditions.push(eq(restaurantTables.status, value));
        } else if (operator === 'in') {
          whereConditions.push(inArray(restaurantTables.status, value));
        }
        break;
      case 'orders':
        if (operator === 'eq') {
          whereConditions.push(eq(restaurantTables.orders, value));
        } else if (operator === 'gt') {
          whereConditions.push(gte(restaurantTables.orders, value));
        } else if (operator === 'lt') {
          whereConditions.push(lte(restaurantTables.orders, value));
        }
        break;
      case 'floorId':
        if (operator === 'eq') {
          whereConditions.push(eq(restaurantTables.floorId, value));
        }
        break;
    }
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<RestaurantTableSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const data = await this.db
      .select({
        id: restaurantTables.id,
        floorId: restaurantTables.floorId,
        tableNumber: restaurantTables.tableNumber,
        status: restaurantTables.status,
        seats: sql<number>`(${restaurantTables.shape}->>'seats')::int`.as(
          'seats',
        ),
      })
      .from(restaurantTables)
      .where(
        and(
          eq(restaurantTables.businessId, businessId),
          eq(restaurantTables.isDeleted, false),
        ),
      )
      .orderBy(asc(restaurantTables.tableNumber));

    return data as RestaurantTableSlimDto[];
  }

  async findOne(
    businessId: string | null,
    id: string,
  ): Promise<RestaurantTableDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const restaurantTable = await this.db
      .select({
        id: restaurantTables.id,
        businessId: restaurantTables.businessId,
        floorId: restaurantTables.floorId,
        tableNumber: restaurantTables.tableNumber,
        position: restaurantTables.position,
        shape: restaurantTables.shape,
        status: restaurantTables.status,
        orders: restaurantTables.orders,
        createdBy: restaurantTables.createdBy,
        createdAt: restaurantTables.createdAt,
        updatedAt: restaurantTables.updatedAt,
      })
      .from(restaurantTables)
      .where(
        and(
          eq(restaurantTables.id, id),
          eq(restaurantTables.businessId, businessId),
          eq(restaurantTables.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!restaurantTable) {
      throw new NotFoundException(`Restaurant table with ID ${id} not found`);
    }

    return restaurantTable as RestaurantTableDto;
  }

  async checkTableNumberAvailability(
    businessId: string | null,
    tableNumber: string,
    floorId?: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(restaurantTables.businessId, businessId),
      ilike(restaurantTables.tableNumber, tableNumber),
      eq(restaurantTables.isDeleted, false),
    ];

    if (floorId) {
      whereConditions.push(eq(restaurantTables.floorId, floorId));
    }

    if (excludeId) {
      whereConditions.push(sql`${restaurantTables.id} != ${excludeId}`);
    }

    const existingTable = await this.db
      .select({ id: restaurantTables.id })
      .from(restaurantTables)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingTable };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateRestaurantTableDto: UpdateRestaurantTableDto,
    metadata?: ActivityMetadata,
  ): Promise<RestaurantTableDto> {
    // Get the restaurant table
    const existingTable = await this.db
      .select()
      .from(restaurantTables)
      .where(
        and(eq(restaurantTables.id, id), eq(restaurantTables.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingTable) {
      throw new NotFoundException(`Restaurant table with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingTable.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this restaurant table',
      );
    }

    // Check for table number conflicts if table number is being updated
    if (updateRestaurantTableDto.tableNumber) {
      const floorIdToCheck =
        updateRestaurantTableDto.floorId || existingTable.floorId;
      const availability = await this.checkTableNumberAvailability(
        businessId,
        updateRestaurantTableDto.tableNumber,
        floorIdToCheck,
        id,
      );

      if (!availability.available) {
        throw new ConflictException(
          `Restaurant table with number "${updateRestaurantTableDto.tableNumber}" already exists on this floor`,
        );
      }
    }

    // Verify floor exists if floorId is being updated
    if (
      updateRestaurantTableDto.floorId &&
      updateRestaurantTableDto.floorId !== existingTable.floorId
    ) {
      const floor = await this.db
        .select()
        .from(floorPlans)
        .where(
          and(
            eq(floorPlans.id, updateRestaurantTableDto.floorId),
            eq(floorPlans.businessId, businessId),
            eq(floorPlans.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!floor) {
        throw new NotFoundException(
          `Floor plan with ID ${updateRestaurantTableDto.floorId} not found`,
        );
      }
    }

    // Update the restaurant table
    const updatedTable = await this.db
      .update(restaurantTables)
      .set({
        ...updateRestaurantTableDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(restaurantTables.id, id))
      .returning({
        id: restaurantTables.id,
        businessId: restaurantTables.businessId,
        floorId: restaurantTables.floorId,
        tableNumber: restaurantTables.tableNumber,
        position: restaurantTables.position,
        shape: restaurantTables.shape,
        status: restaurantTables.status,
        orders: restaurantTables.orders,
        createdBy: restaurantTables.createdBy,
        createdAt: restaurantTables.createdAt,
        updatedAt: restaurantTables.updatedAt,
      })
      .then((results) => results[0]);

    // Log the activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.RESTAURANT_TABLE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return updatedTable as RestaurantTableDto;
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateRestaurantTableDto: UpdateRestaurantTableDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateRestaurantTableDto,
      metadata,
    );
    return { id };
  }

  async updateStatus(
    userId: string,
    businessId: string | null,
    id: string,
    status: RestaurantTableStatus,
    orders?: number,
    metadata?: ActivityMetadata,
  ): Promise<RestaurantTableDto> {
    const updateDto: UpdateRestaurantTableDto = { status };
    if (orders !== undefined) {
      updateDto.orders = orders;
    }
    return this.update(userId, businessId, id, updateDto, metadata);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    // Get the restaurant table
    const existingTable = await this.db
      .select()
      .from(restaurantTables)
      .where(
        and(eq(restaurantTables.id, id), eq(restaurantTables.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingTable) {
      throw new NotFoundException(`Restaurant table with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingTable.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this restaurant table',
      );
    }

    // Soft delete the restaurant table
    await this.db
      .update(restaurantTables)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(restaurantTables.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.RESTAURANT_TABLE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    bulkDeleteDto: BulkDeleteRestaurantTableDto,
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string; deletedIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const { tableIds } = bulkDeleteDto;

      if (!tableIds || tableIds.length === 0) {
        throw new BadRequestException(
          'At least one restaurant table ID is required',
        );
      }

      // Verify all restaurant tables exist and belong to the business
      const existingTables = await this.db
        .select({
          id: restaurantTables.id,
          tableNumber: restaurantTables.tableNumber,
        })
        .from(restaurantTables)
        .where(
          and(
            inArray(restaurantTables.id, tableIds),
            eq(restaurantTables.businessId, businessId),
            eq(restaurantTables.isDeleted, false),
          ),
        );

      if (existingTables.length !== tableIds.length) {
        const foundIds = existingTables.map((table) => table.id);
        const missingIds = tableIds.filter((id) => !foundIds.includes(id));
        throw new NotFoundException(
          `Restaurant tables with IDs [${missingIds.join(', ')}] not found`,
        );
      }

      // Soft delete all restaurant tables
      await this.db
        .update(restaurantTables)
        .set({
          updatedBy: userId,
          updatedAt: new Date(),
          isDeleted: true,
        })
        .where(inArray(restaurantTables.id, tableIds));

      // Log the activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.RESTAURANT_TABLE,
        tableIds,
        {
          count: existingTables.length,
          tableNumbers: existingTables.map((table) => table.tableNumber),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: existingTables.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          ...metadata,
        },
      );

      return {
        deleted: existingTables.length,
        message: `Successfully deleted ${existingTables.length} restaurant table(s)`,
        deletedIds: tableIds,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error bulk deleting restaurant tables:', error);
      throw new BadRequestException('Failed to bulk delete restaurant tables');
    }
  }

  // ========================
  // TABLE RESERVATIONS METHODS
  // ========================

  async createReservation(
    userId: string,
    businessId: string | null,
    createDto: CreateTableReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      // Validate table exists and belongs to business
      const table = await this.db
        .select({
          id: restaurantTables.id,
          businessId: restaurantTables.businessId,
        })
        .from(restaurantTables)
        .where(
          and(
            eq(restaurantTables.id, createDto.tableId),
            eq(restaurantTables.businessId, businessId),
            eq(restaurantTables.isDeleted, false),
          ),
        )
        .limit(1);

      if (table.length === 0) {
        throw new NotFoundException('Restaurant table not found');
      }

      // Validate customer exists if provided
      if (createDto.customerId) {
        const customer = await this.db
          .select({ id: customers.id })
          .from(customers)
          .where(
            and(
              eq(customers.id, createDto.customerId),
              eq(customers.businessId, businessId),
              eq(customers.isDeleted, false),
            ),
          )
          .limit(1);

        if (customer.length === 0) {
          throw new NotFoundException('Customer not found');
        }
      }

      // Check for conflicting reservations
      const conflictingReservations = await this.db
        .select({ id: tableReservations.id })
        .from(tableReservations)
        .where(
          and(
            eq(tableReservations.tableId, createDto.tableId),
            eq(tableReservations.status, 'confirmed'),
            sql`${tableReservations.reservationDatetime} = ${createDto.reservationDatetime}`,
            eq(tableReservations.isDeleted, false),
          ),
        );

      if (conflictingReservations.length > 0) {
        throw new ConflictException(
          'Table is already reserved for this time slot',
        );
      }

      const [reservation] = await this.db
        .insert(tableReservations)
        .values({
          businessId,
          customerId: createDto.customerId || null,
          tableId: createDto.tableId,
          reservationDatetime: new Date(createDto.reservationDatetime),
          partySize: createDto.partySize,
          status: createDto.status || 'pending',
          notes: createDto.notes || null,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: tableReservations.id });

      // Log activity
      await this.activityLogService.logCreate(
        reservation.id.toString(),
        EntityType.TABLE_RESERVATION,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: reservation.id };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error creating table reservation:', error);
      throw new BadRequestException('Failed to create table reservation');
    }
  }

  async findAllReservations(
    businessId: string | null,
    page?: number,
    limit?: number,
    from?: string,
    to?: string,
    status?: string,
    tableId?: string,
    customerId?: string,
  ): Promise<{
    data: TableReservationListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      const pageNum = page || 1;
      const pageSize = limit || 10;
      const offset = (pageNum - 1) * pageSize;

      const baseConditions = [
        eq(tableReservations.isDeleted, false),
        eq(restaurantTables.isDeleted, false),
        eq(restaurantTables.businessId, businessId),
      ];

      // Add filters
      if (from) {
        baseConditions.push(
          gte(tableReservations.reservationDatetime, new Date(from)),
        );
      }
      if (to) {
        baseConditions.push(
          lte(tableReservations.reservationDatetime, new Date(to)),
        );
      }
      if (status) {
        baseConditions.push(eq(tableReservations.status, status as any));
      }
      if (tableId) {
        baseConditions.push(eq(tableReservations.tableId, tableId));
      }
      if (customerId) {
        baseConditions.push(eq(tableReservations.customerId, customerId));
      }

      // Get total count
      const [totalResult] = await this.db
        .select({ count: count() })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .innerJoin(floorPlans, eq(restaurantTables.floorId, floorPlans.id))
        .leftJoin(customers, eq(tableReservations.customerId, customers.id))
        .where(and(...baseConditions));

      const total = totalResult.count;
      const totalPages = Math.ceil(total / pageSize);

      // Get paginated data
      const reservations = await this.db
        .select({
          id: tableReservations.id,
          customerDisplayName: customers.customerDisplayName,
          customerContact: sql<string>`COALESCE(${customers.phoneNumber}, ${customers.mobileNumber})`,
          tableNumber: restaurantTables.tableNumber,
          floorPlanName: floorPlans.name,
          reservationDatetime: tableReservations.reservationDatetime,
          partySize: tableReservations.partySize,
          status: tableReservations.status,
          notes: tableReservations.notes,
          createdAt: tableReservations.createdAt,
        })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .innerJoin(floorPlans, eq(restaurantTables.floorId, floorPlans.id))
        .leftJoin(customers, eq(tableReservations.customerId, customers.id))
        .where(and(...baseConditions))
        .orderBy(desc(tableReservations.reservationDatetime))
        .limit(pageSize)
        .offset(offset);

      return {
        data: reservations.map((r) => ({
          id: r.id,
          businessId,
          customerDisplayName: r.customerDisplayName,
          customerContact: r.customerContact,
          tableNumber: r.tableNumber,
          floorPlanName: r.floorPlanName,
          reservationDatetime: r.reservationDatetime.toISOString(),
          partySize: r.partySize,
          status: r.status as any,
          notes: r.notes,
          createdAt: r.createdAt.toISOString(),
        })),
        meta: { total, page: pageNum, totalPages },
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      console.error('Error finding table reservations:', error);
      throw new BadRequestException('Failed to retrieve table reservations');
    }
  }

  async findOneReservation(
    businessId: string | null,
    id: number,
  ): Promise<TableReservationDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      const [reservation] = await this.db
        .select({
          id: tableReservations.id,
          customerId: tableReservations.customerId,
          tableId: tableReservations.tableId,
          reservationDatetime: tableReservations.reservationDatetime,
          partySize: tableReservations.partySize,
          status: tableReservations.status,
          notes: tableReservations.notes,
          createdBy: tableReservations.createdBy,
          updatedBy: tableReservations.updatedBy,
          createdAt: tableReservations.createdAt,
          updatedAt: tableReservations.updatedAt,
          // Customer info
          customerDisplayName: customers.customerDisplayName,
          customerEmail: customers.email,
          customerPhone: customers.phoneNumber,
          customerMobile: customers.mobileNumber,
          // Table info
          tableNumber: restaurantTables.tableNumber,
          tableStatus: restaurantTables.status,
          tableShape: restaurantTables.shape,
          tablePosition: restaurantTables.position,
        })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .leftJoin(customers, eq(tableReservations.customerId, customers.id))
        .where(
          and(
            eq(tableReservations.id, id),
            eq(restaurantTables.businessId, businessId),
            eq(tableReservations.isDeleted, false),
            eq(restaurantTables.isDeleted, false),
          ),
        )
        .limit(1);

      if (!reservation) {
        throw new NotFoundException('Table reservation not found');
      }

      return {
        id: reservation.id,
        customerId: reservation.customerId,
        tableId: reservation.tableId,
        reservationDatetime: reservation.reservationDatetime.toISOString(),
        partySize: reservation.partySize,
        status: reservation.status as any,
        notes: reservation.notes,
        createdBy: reservation.createdBy,
        updatedBy: reservation.updatedBy,
        createdAt: reservation.createdAt.toISOString(),
        updatedAt: reservation.updatedAt.toISOString(),
        customer: reservation.customerId
          ? {
              id: reservation.customerId,
              customerDisplayName: reservation.customerDisplayName,
              email: reservation.customerEmail,
              phoneNumber: reservation.customerPhone,
              mobileNumber: reservation.customerMobile,
            }
          : undefined,
        table: {
          id: reservation.tableId,
          tableNumber: reservation.tableNumber,
          status: reservation.tableStatus,
          shape: reservation.tableShape,
          position: reservation.tablePosition,
        },
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      console.error('Error finding table reservation:', error);
      throw new BadRequestException('Failed to retrieve table reservation');
    }
  }

  async updateReservation(
    userId: string,
    businessId: string | null,
    id: number,
    updateDto: UpdateTableReservationDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      // Check if reservation exists and belongs to business
      const existingReservation = await this.db
        .select({
          id: tableReservations.id,
          tableId: tableReservations.tableId,
        })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .where(
          and(
            eq(tableReservations.id, id),
            eq(restaurantTables.businessId, businessId),
            eq(tableReservations.isDeleted, false),
            eq(restaurantTables.isDeleted, false),
          ),
        )
        .limit(1);

      if (existingReservation.length === 0) {
        throw new NotFoundException('Table reservation not found');
      }

      // Validate new table if provided
      if (updateDto.tableId) {
        const table = await this.db
          .select({ id: restaurantTables.id })
          .from(restaurantTables)
          .where(
            and(
              eq(restaurantTables.id, updateDto.tableId),
              eq(restaurantTables.businessId, businessId),
              eq(restaurantTables.isDeleted, false),
            ),
          )
          .limit(1);

        if (table.length === 0) {
          throw new NotFoundException('Restaurant table not found');
        }
      }

      // Validate new customer if provided
      if (updateDto.customerId) {
        const customer = await this.db
          .select({ id: customers.id })
          .from(customers)
          .where(
            and(
              eq(customers.id, updateDto.customerId),
              eq(customers.businessId, businessId),
              eq(customers.isDeleted, false),
            ),
          )
          .limit(1);

        if (customer.length === 0) {
          throw new NotFoundException('Customer not found');
        }
      }

      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      if (updateDto.customerId !== undefined)
        updateData.customerId = updateDto.customerId;
      if (updateDto.tableId) updateData.tableId = updateDto.tableId;
      if (updateDto.reservationDatetime)
        updateData.reservationDatetime = new Date(
          updateDto.reservationDatetime,
        );
      if (updateDto.partySize) updateData.partySize = updateDto.partySize;
      if (updateDto.status) updateData.status = updateDto.status;
      if (updateDto.notes !== undefined) updateData.notes = updateDto.notes;

      await this.db
        .update(tableReservations)
        .set(updateData)
        .where(eq(tableReservations.id, id));

      // Log activity
      await this.activityLogService.logUpdate(
        id.toString(),
        EntityType.TABLE_RESERVATION,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error updating table reservation:', error);
      throw new BadRequestException('Failed to update table reservation');
    }
  }

  async updateReservationStatus(
    userId: string,
    businessId: string | null,
    id: number,
    status: string,
    metadata?: ActivityMetadata,
  ): Promise<TableReservationDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      const validStatuses = [
        'pending',
        'confirmed',
        'seated',
        'completed',
        'cancelled',
        'no_show',
      ];
      if (!validStatuses.includes(status)) {
        throw new BadRequestException('Invalid reservation status');
      }

      // Check if reservation exists and belongs to business
      const existingReservation = await this.db
        .select({ id: tableReservations.id })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .where(
          and(
            eq(tableReservations.id, id),
            eq(restaurantTables.businessId, businessId),
            eq(tableReservations.isDeleted, false),
            eq(restaurantTables.isDeleted, false),
          ),
        )
        .limit(1);

      if (existingReservation.length === 0) {
        throw new NotFoundException('Table reservation not found');
      }

      await this.db
        .update(tableReservations)
        .set({
          status: status as any,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(tableReservations.id, id));

      // Log activity
      await this.activityLogService.logUpdate(
        id.toString(),
        EntityType.TABLE_RESERVATION,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOneReservation(businessId, id);
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error updating table reservation status:', error);
      throw new BadRequestException(
        'Failed to update table reservation status',
      );
    }
  }

  async bulkDeleteReservations(
    userId: string,
    businessId: string | null,
    bulkDeleteDto: BulkDeleteTableReservationsDto,
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string; deletedIds: number[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      if (bulkDeleteDto.ids.length === 0) {
        throw new BadRequestException('No reservation IDs provided');
      }

      // Find reservations that belong to this business
      const reservationsToDelete = await this.db
        .select({
          id: tableReservations.id,
          tableId: tableReservations.tableId,
        })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .where(
          and(
            inArray(tableReservations.id, bulkDeleteDto.ids),
            eq(restaurantTables.businessId, businessId),
            eq(tableReservations.isDeleted, false),
            eq(restaurantTables.isDeleted, false),
          ),
        );

      if (reservationsToDelete.length === 0) {
        throw new NotFoundException(
          'No table reservations found with the provided IDs',
        );
      }

      const idsToDelete = reservationsToDelete.map((r) => r.id);

      // Soft delete reservations
      await this.db
        .update(tableReservations)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(tableReservations.id, idsToDelete));

      // Log bulk delete activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.TABLE_RESERVATION,
        idsToDelete.map((id) => id.toString()),
        {
          count: idsToDelete.length,
          tableIds: reservationsToDelete.map((r) => r.tableId),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: idsToDelete.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          ...metadata,
        },
      );

      return {
        deleted: idsToDelete.length,
        message: `Successfully deleted ${idsToDelete.length} table reservation(s)`,
        deletedIds: idsToDelete,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error bulk deleting table reservations:', error);
      throw new BadRequestException('Failed to bulk delete table reservations');
    }
  }

  async removeReservation(
    userId: string,
    businessId: string | null,
    id: number,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    try {
      if (!businessId) {
        throw new UnauthorizedException('No active business found');
      }

      // Check if reservation exists and belongs to business
      const existingReservation = await this.db
        .select({ id: tableReservations.id })
        .from(tableReservations)
        .innerJoin(
          restaurantTables,
          eq(tableReservations.tableId, restaurantTables.id),
        )
        .where(
          and(
            eq(tableReservations.id, id),
            eq(restaurantTables.businessId, businessId),
            eq(tableReservations.isDeleted, false),
            eq(restaurantTables.isDeleted, false),
          ),
        )
        .limit(1);

      if (existingReservation.length === 0) {
        throw new NotFoundException('Table reservation not found');
      }

      await this.db
        .update(tableReservations)
        .set({
          updatedBy: userId,
          updatedAt: new Date(),
          isDeleted: true,
        })
        .where(eq(tableReservations.id, id));

      // Log activity
      await this.activityLogService.logDelete(
        id.toString(),
        EntityType.TABLE_RESERVATION,
        userId,
        businessId,
        {
          ...metadata,
        },
      );
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      console.error('Error removing table reservation:', error);
      throw new BadRequestException('Failed to delete table reservation');
    }
  }
}
