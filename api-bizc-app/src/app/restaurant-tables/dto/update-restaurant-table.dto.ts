import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsObject,
  IsEnum,
  IsNumber,
  Min,
} from 'class-validator';
import { RestaurantTableStatus } from '../../shared/types';
import { PositionDto, TableShapeDto } from './restaurant-table.dto';

export class UpdateRestaurantTableDto {
  @ApiProperty({
    example: 'uuid',
    description: 'Floor plan ID',
    required: false,
  })
  @IsOptional()
  @IsString()
  floorId?: string;

  @ApiProperty({
    example: '101',
    description: 'Table number',
    required: false,
  })
  @IsOptional()
  @IsString()
  tableNumber?: string;

  @ApiProperty({
    type: PositionDto,
    description: 'Table position on the floor plan',
    required: false,
  })
  @IsOptional()
  @IsObject()
  position?: PositionDto;

  @ApiProperty({
    type: TableShapeDto,
    description: 'Table shape and dimensions',
    required: false,
  })
  @IsOptional()
  @IsObject()
  shape?: TableShapeDto;

  @ApiProperty({
    example: RestaurantTableStatus.AVAILABLE,
    enum: RestaurantTableStatus,
    description: 'Table status',
    required: false,
  })
  @IsOptional()
  @IsEnum(RestaurantTableStatus)
  status?: RestaurantTableStatus;

  @ApiProperty({
    example: 0,
    description: 'Number of active orders',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  orders?: number;
}
