import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  and,
  eq,
  ilike,
  gte,
  lte,
  desc,
  asc,
  inArray,
  count,
  sql,
} from 'drizzle-orm';

import {
  serviceOrders,
  serviceOrderItems,
  serviceOrderDiagnostics,
  serviceOrderLines,
  serviceOrderLineStaff,
  CustomerApprovalStatus,
  ServiceOrderPaymentStatus,
} from '../drizzle/schema/service-orders.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { serviceOrderStatuses } from '../drizzle/schema/service-order-statuses.schema';
import { serviceOrderPriorities } from '../drizzle/schema/service-order-priorities.schema';
import { serviceOrderTypes } from '../drizzle/schema/service-order-types.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { users } from '../drizzle/schema/users.schema';
import { TasksService } from '../tasks/tasks.service';
import { CreateServiceOrderDto } from './dto/create-service-order.dto';
import { UpdateServiceOrderDto } from './dto/update-service-order.dto';
import { ServiceOrderDto } from './dto/service-order.dto';
import { ServiceOrderListDto } from './dto/service-order-list.dto';
import { ServiceOrderSlimDto } from './dto/service-order-slim.dto';
import { ServiceOrderNumberAvailabilityResponseDto } from './dto/check-service-order-number.dto';
import { BulkDeleteServiceOrderResponseDto } from './dto/bulk-delete-service-order-response.dto';
import {
  CreateServiceOrderLineTaskDto,
  UpdateServiceOrderLineTaskDto,
  ServiceOrderLineTaskDto,
  ServiceOrderLineTaskIdResponseDto,
} from './dto/service-order-line-task.dto';

@Injectable()
export class ServiceOrdersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private tasksService: TasksService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceOrderDto: CreateServiceOrderDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service order with the same order number already exists for this business
      const existingServiceOrder = await this.db
        .select()
        .from(serviceOrders)
        .where(
          and(
            eq(serviceOrders.businessId, businessId),
            ilike(serviceOrders.orderNumber, createServiceOrderDto.orderNumber),
            eq(serviceOrders.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingServiceOrder) {
        throw new ConflictException(
          `Service order with order number "${createServiceOrderDto.orderNumber}" already exists`,
        );
      }

      // Validate foreign key references
      await this.validateForeignKeys(businessId, createServiceOrderDto);

      // Start transaction for creating service order with related data
      const result = await this.db.transaction(async (tx) => {
        // Insert main service order
        const [serviceOrder] = await tx
          .insert(serviceOrders)
          .values({
            businessId,
            orderNumber: createServiceOrderDto.orderNumber,
            customerId: createServiceOrderDto.customerId,
            appointmentId: createServiceOrderDto.appointmentId,
            statusId: createServiceOrderDto.statusId,
            priorityId: createServiceOrderDto.priorityId,
            serviceTypeId: createServiceOrderDto.serviceTypeId,
            locationId: createServiceOrderDto.locationId,
            orderDate: new Date(createServiceOrderDto.orderDate),
            scheduledDate: createServiceOrderDto.scheduledDate
              ? new Date(createServiceOrderDto.scheduledDate)
              : null,
            actualStartDate: createServiceOrderDto.actualStartDate
              ? new Date(createServiceOrderDto.actualStartDate)
              : null,
            actualEndDate: createServiceOrderDto.actualEndDate
              ? new Date(createServiceOrderDto.actualEndDate)
              : null,
            promisedDate: createServiceOrderDto.promisedDate
              ? new Date(createServiceOrderDto.promisedDate)
              : null,
            description: createServiceOrderDto.description,
            estimatedCost: createServiceOrderDto.estimatedCost,
            actualCost: createServiceOrderDto.actualCost,
            totalAmount: createServiceOrderDto.totalAmount,
            taxType: createServiceOrderDto.taxType,
            defaultTaxRateId: createServiceOrderDto.defaultTaxRateId,
            receivedById: createServiceOrderDto.receivedById,
            customerApprovalStatus:
              createServiceOrderDto.customerApprovalStatus ||
              CustomerApprovalStatus.PENDING,
            customerApprovalDate: createServiceOrderDto.customerApprovalDate
              ? new Date(createServiceOrderDto.customerApprovalDate)
              : null,
            cancellationReason: createServiceOrderDto.cancellationReason,
            paymentStatus:
              createServiceOrderDto.paymentStatus ||
              ServiceOrderPaymentStatus.PENDING,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: serviceOrders.id });

        const serviceOrderId = serviceOrder.id;

        // Insert service order items if provided
        if (
          createServiceOrderDto.items &&
          createServiceOrderDto.items.length > 0
        ) {
          await tx.insert(serviceOrderItems).values(
            createServiceOrderDto.items.map((item) => ({
              businessId,
              serviceOrderId,
              itemId: item.itemId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.totalPrice,
              standardUnitOfMeasure: item.standardUnitOfMeasure,
              customUnitId: item.customUnitId,
              description: item.description,
              createdBy: userId,
              updatedBy: userId,
            })),
          );
        }

        // Insert service order diagnostics if provided
        if (
          createServiceOrderDto.diagnostics &&
          createServiceOrderDto.diagnostics.length > 0
        ) {
          await tx.insert(serviceOrderDiagnostics).values(
            createServiceOrderDto.diagnostics.map((diagnostic) => ({
              businessId,
              serviceOrderId,
              testPerformed: diagnostic.testPerformed,
              testResult: diagnostic.testResult,
              issueFound: diagnostic.issueFound,
              recommendedAction: diagnostic.recommendedAction,
              requiresParts: diagnostic.requiresParts || false,
              partsNeeded: diagnostic.partsNeeded,
              estimatedRepairTime: diagnostic.estimatedRepairTime,
              diagnosedById: createServiceOrderDto.receivedById, // Use receivedById as default
              diagnosticDate: new Date(),
              diagnosticNotes: diagnostic.notes,
              createdBy: userId,
              updatedBy: userId,
            })),
          );
        }

        // Insert service order lines if provided
        if (
          createServiceOrderDto.lines &&
          createServiceOrderDto.lines.length > 0
        ) {
          for (const line of createServiceOrderDto.lines) {
            const [serviceOrderLine] = await tx
              .insert(serviceOrderLines)
              .values({
                businessId,
                serviceOrderId,
                serviceId: line.serviceId,
                lineType: line.lineType,
                quantity: line.quantity,
                unitPrice: line.unitPrice,
                discountType: line.discountType,
                discountAmount: line.discountAmount || '0.00',
                lineTotal: line.lineTotal,
                isWarrantyItem: line.isWarrantyItem || false,
                notes: line.notes,
                createdBy: userId,
                updatedBy: userId,
              })
              .returning({ id: serviceOrderLines.id });

            // Insert staff assignments for this line if provided
            if (line.staff && line.staff.length > 0) {
              for (const staff of line.staff) {
                let taskId: string | null = null;

                // Create task if provided
                if (staff.task) {
                  const taskResult =
                    await this.tasksService.createTaskForServiceOrder(
                      userId,
                      businessId,
                      serviceOrder.id,
                      {
                        title: staff.task.title,
                        description: staff.task.description,
                        dueDate: staff.task.dueDate,
                        priority: staff.task.priority,
                        status: staff.task.status,
                        assignedTo: staff.staffId,
                      },
                    );
                  taskId = taskResult.id;
                }

                // Insert staff assignment
                await tx.insert(serviceOrderLineStaff).values({
                  businessId,
                  lineId: serviceOrderLine.id,
                  staffId: staff.staffId,
                  taskId,
                  createdBy: userId,
                  updatedBy: userId,
                });
              }
            }
          }
        }

        return serviceOrder;
      });

      return { id: result.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create service order: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createServiceOrderDto: CreateServiceOrderDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createServiceOrderDto);
  }

  private async validateForeignKeys(
    businessId: string,
    dto: CreateServiceOrderDto | UpdateServiceOrderDto,
  ): Promise<void> {
    // Validate customer exists
    if (dto.customerId) {
      const customer = await this.db
        .select({ id: customers.id })
        .from(customers)
        .where(
          and(
            eq(customers.id, dto.customerId),
            eq(customers.businessId, businessId),
            eq(customers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!customer) {
        throw new BadRequestException('Customer not found');
      }
    }

    // Validate status exists
    if (dto.statusId) {
      const status = await this.db
        .select({ id: serviceOrderStatuses.id })
        .from(serviceOrderStatuses)
        .where(
          and(
            eq(serviceOrderStatuses.id, dto.statusId),
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!status) {
        throw new BadRequestException('Service order status not found');
      }
    }

    // Validate priority exists
    if (dto.priorityId) {
      const priority = await this.db
        .select({ id: serviceOrderPriorities.id })
        .from(serviceOrderPriorities)
        .where(
          and(
            eq(serviceOrderPriorities.id, dto.priorityId),
            eq(serviceOrderPriorities.businessId, businessId),
            eq(serviceOrderPriorities.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!priority) {
        throw new BadRequestException('Service order priority not found');
      }
    }

    // Validate service type exists
    if (dto.serviceTypeId) {
      const serviceType = await this.db
        .select({ id: serviceOrderTypes.id })
        .from(serviceOrderTypes)
        .where(
          and(
            eq(serviceOrderTypes.id, dto.serviceTypeId),
            eq(serviceOrderTypes.businessId, businessId),
            eq(serviceOrderTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!serviceType) {
        throw new BadRequestException('Service order type not found');
      }
    }

    // Validate location exists
    if (dto.locationId) {
      const location = await this.db
        .select({ id: locations.id })
        .from(locations)
        .where(
          and(
            eq(locations.id, dto.locationId),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!location) {
        throw new BadRequestException('Location not found');
      }
    }

    // Validate received by staff member exists
    if (dto.receivedById) {
      const staff = await this.db
        .select({ id: staffMembers.id })
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, dto.receivedById),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!staff) {
        throw new BadRequestException('Staff member not found');
      }
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createServiceOrderDtos: CreateServiceOrderDto[],
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (const dto of createServiceOrderDtos) {
      try {
        const result = await this.create(userId, businessId, dto);
        createdIds.push(result.id);
      } catch (error) {
        errors.push(
          `Failed to create service order ${dto.orderNumber}: ${error.message}`,
        );
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `All service orders failed to create: ${errors.join(', ')}`,
      );
    }

    return { ids: createdIds };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    orderNumber?: string,
    customerId?: string,
    statusId?: string,
    priorityId?: string,
    serviceTypeId?: string,
    locationId?: string,
    customerApprovalStatus?: string,
    paymentStatus?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ServiceOrderListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(serviceOrders.isDeleted, false),
      eq(serviceOrders.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceOrders.orderDate, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(serviceOrders.orderDate, toDate));
      }
    }

    // Add specific field filters
    if (orderNumber) {
      whereConditions.push(
        ilike(serviceOrders.orderNumber, `%${orderNumber}%`),
      );
    }

    if (customerId) {
      whereConditions.push(eq(serviceOrders.customerId, customerId));
    }

    if (statusId) {
      whereConditions.push(eq(serviceOrders.statusId, statusId));
    }

    if (priorityId) {
      whereConditions.push(eq(serviceOrders.priorityId, priorityId));
    }

    if (serviceTypeId) {
      whereConditions.push(eq(serviceOrders.serviceTypeId, serviceTypeId));
    }

    if (locationId) {
      whereConditions.push(eq(serviceOrders.locationId, locationId));
    }

    if (customerApprovalStatus) {
      whereConditions.push(
        eq(
          serviceOrders.customerApprovalStatus,
          customerApprovalStatus as CustomerApprovalStatus,
        ),
      );
    }

    if (paymentStatus) {
      whereConditions.push(
        eq(
          serviceOrders.paymentStatus,
          paymentStatus as ServiceOrderPaymentStatus,
        ),
      );
    }

    // Build order by clause
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'orderNumber':
          orderBy = isDesc
            ? desc(serviceOrders.orderNumber)
            : asc(serviceOrders.orderNumber);
          break;
        case 'orderDate':
          orderBy = isDesc
            ? desc(serviceOrders.orderDate)
            : asc(serviceOrders.orderDate);
          break;
        case 'totalAmount':
          orderBy = isDesc
            ? desc(serviceOrders.totalAmount)
            : asc(serviceOrders.totalAmount);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(serviceOrders.createdAt)
            : asc(serviceOrders.createdAt);
          break;
        default:
          orderBy = desc(serviceOrders.createdAt);
      }
    } else {
      orderBy = desc(serviceOrders.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(serviceOrders)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get service orders with related data
    const serviceOrderResults = await this.db
      .select({
        id: serviceOrders.id,
        orderNumber: serviceOrders.orderNumber,
        orderDate: serviceOrders.orderDate,
        scheduledDate: serviceOrders.scheduledDate,
        promisedDate: serviceOrders.promisedDate,
        totalAmount: serviceOrders.totalAmount,
        customerApprovalStatus: serviceOrders.customerApprovalStatus,
        paymentStatus: serviceOrders.paymentStatus,
        description: serviceOrders.description,
        createdAt: serviceOrders.createdAt,
        updatedAt: serviceOrders.updatedAt,
        // Customer data
        customerId: customers.id,
        customerName: customers.customerDisplayName,
        customerEmail: customers.email,
        customerPhone: customers.phoneNumber,
        // Status data
        statusId: serviceOrderStatuses.id,
        statusName: serviceOrderStatuses.statusName,
        statusCode: serviceOrderStatuses.statusCode,
        statusColorCode: serviceOrderStatuses.colorCode,
        statusType: serviceOrderStatuses.statusType,
        // Priority data
        priorityId: serviceOrderPriorities.id,
        priorityName: serviceOrderPriorities.priorityName,
        priorityCode: serviceOrderPriorities.priorityCode,
        priorityColorCode: serviceOrderPriorities.colorCode,
        prioritySeverityLevel: serviceOrderPriorities.severityLevel,
        // Service type data
        serviceTypeId: serviceOrderTypes.id,
        serviceTypeName: serviceOrderTypes.typeName,
        serviceTypeCode: serviceOrderTypes.typeCode,
        serviceTypeCategory: serviceOrderTypes.category,
        // Location data
        locationId: locations.id,
        locationName: locations.name,
        // Received by staff data
        receivedById: staffMembers.id,
        receivedByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        receivedByAvatar: users.avatar,
        // Created/Updated by data
        createdByName: sql<string>`CONCAT(created_by_user.first_name, ' ', created_by_user.last_name)`,
        updatedByName: sql<string>`CONCAT(updated_by_user.first_name, ' ', updated_by_user.last_name)`,
      })
      .from(serviceOrders)
      .leftJoin(customers, eq(serviceOrders.customerId, customers.id))
      .leftJoin(
        serviceOrderStatuses,
        eq(serviceOrders.statusId, serviceOrderStatuses.id),
      )
      .leftJoin(
        serviceOrderPriorities,
        eq(serviceOrders.priorityId, serviceOrderPriorities.id),
      )
      .leftJoin(
        serviceOrderTypes,
        eq(serviceOrders.serviceTypeId, serviceOrderTypes.id),
      )
      .leftJoin(locations, eq(serviceOrders.locationId, locations.id))
      .leftJoin(staffMembers, eq(serviceOrders.receivedById, staffMembers.id))
      .leftJoin(users, eq(staffMembers.userId, users.id))
      .leftJoin(
        sql`users AS created_by_user`,
        sql`service_orders.created_by = created_by_user.id`,
      )
      .leftJoin(
        sql`users AS updated_by_user`,
        sql`service_orders.updated_by = updated_by_user.id`,
      )
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return this.buildServiceOrderListResponse(
      serviceOrderResults,
      total,
      page,
      totalPages,
    );
  }

  private async buildServiceOrderListResponse(
    serviceOrderResults: any[],
    total: number,
    page: number,
    totalPages: number,
  ): Promise<{
    data: ServiceOrderListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    // Get counts for related data
    const serviceOrderIds = serviceOrderResults.map((so) => so.id);
    let itemsCounts: Record<string, number> = {};
    let diagnosticsCounts: Record<string, number> = {};
    let linesCounts: Record<string, number> = {};

    if (serviceOrderIds.length > 0) {
      // Get items counts
      const itemsCountResults = await this.db
        .select({
          serviceOrderId: serviceOrderItems.serviceOrderId,
          count: count(),
        })
        .from(serviceOrderItems)
        .where(
          and(
            inArray(serviceOrderItems.serviceOrderId, serviceOrderIds),
            eq(serviceOrderItems.isDeleted, false),
          ),
        )
        .groupBy(serviceOrderItems.serviceOrderId);

      itemsCounts = itemsCountResults.reduce(
        (acc, item) => {
          acc[item.serviceOrderId] = item.count;
          return acc;
        },
        {} as Record<string, number>,
      );

      // Get diagnostics counts
      const diagnosticsCountResults = await this.db
        .select({
          serviceOrderId: serviceOrderDiagnostics.serviceOrderId,
          count: count(),
        })
        .from(serviceOrderDiagnostics)
        .where(
          and(
            inArray(serviceOrderDiagnostics.serviceOrderId, serviceOrderIds),
            eq(serviceOrderDiagnostics.isDeleted, false),
          ),
        )
        .groupBy(serviceOrderDiagnostics.serviceOrderId);

      diagnosticsCounts = diagnosticsCountResults.reduce(
        (acc, diagnostic) => {
          acc[diagnostic.serviceOrderId] = diagnostic.count;
          return acc;
        },
        {} as Record<string, number>,
      );

      // Get lines counts
      const linesCountResults = await this.db
        .select({
          serviceOrderId: serviceOrderLines.serviceOrderId,
          count: count(),
        })
        .from(serviceOrderLines)
        .where(
          and(
            inArray(serviceOrderLines.serviceOrderId, serviceOrderIds),
            eq(serviceOrderLines.isDeleted, false),
          ),
        )
        .groupBy(serviceOrderLines.serviceOrderId);

      linesCounts = linesCountResults.reduce(
        (acc, line) => {
          acc[line.serviceOrderId] = line.count;
          return acc;
        },
        {} as Record<string, number>,
      );
    }

    const data: ServiceOrderListDto[] = serviceOrderResults.map((row) => ({
      id: row.id,
      orderNumber: row.orderNumber,
      customer: {
        id: row.customerId,
        name: row.customerName,
        email: row.customerEmail,
        phone: row.customerPhone,
      },
      status: {
        id: row.statusId,
        statusName: row.statusName,
        statusCode: row.statusCode,
        colorCode: row.statusColorCode,
        statusType: row.statusType,
      },
      priority: {
        id: row.priorityId,
        priorityName: row.priorityName,
        priorityCode: row.priorityCode,
        colorCode: row.priorityColorCode,
        severityLevel: row.prioritySeverityLevel,
      },
      serviceType: {
        id: row.serviceTypeId,
        typeName: row.serviceTypeName,
        typeCode: row.serviceTypeCode,
        category: row.serviceTypeCategory,
      },
      location: {
        id: row.locationId,
        name: row.locationName,
      },
      orderDate: row.orderDate,
      scheduledDate: row.scheduledDate,
      promisedDate: row.promisedDate,
      totalAmount: row.totalAmount,
      customerApprovalStatus: row.customerApprovalStatus,
      paymentStatus: row.paymentStatus,
      receivedBy: {
        id: row.receivedById,
        name: row.receivedByName,
        avatar: row.receivedByAvatar,
      },
      description: row.description,
      itemsCount: itemsCounts[row.id] || 0,
      diagnosticsCount: diagnosticsCounts[row.id] || 0,
      linesCount: linesCounts[row.id] || 0,
      createdBy: row.createdByName,
      updatedBy: row.updatedByName,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ServiceOrderSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const serviceOrderResults = await this.db
      .select({
        id: serviceOrders.id,
        orderNumber: serviceOrders.orderNumber,
        orderDate: serviceOrders.orderDate,
        totalAmount: serviceOrders.totalAmount,
        customerApprovalStatus: serviceOrders.customerApprovalStatus,
        paymentStatus: serviceOrders.paymentStatus,
        customerName: customers.customerDisplayName,
        statusName: serviceOrderStatuses.statusName,
        statusColorCode: serviceOrderStatuses.colorCode,
        priorityName: serviceOrderPriorities.priorityName,
        priorityColorCode: serviceOrderPriorities.colorCode,
      })
      .from(serviceOrders)
      .leftJoin(customers, eq(serviceOrders.customerId, customers.id))
      .leftJoin(
        serviceOrderStatuses,
        eq(serviceOrders.statusId, serviceOrderStatuses.id),
      )
      .leftJoin(
        serviceOrderPriorities,
        eq(serviceOrders.priorityId, serviceOrderPriorities.id),
      )
      .where(
        and(
          eq(serviceOrders.isDeleted, false),
          eq(serviceOrders.businessId, businessId),
        ),
      )
      .orderBy(desc(serviceOrders.createdAt));

    return serviceOrderResults.map((row) => ({
      id: row.id,
      orderNumber: row.orderNumber,
      customerName: row.customerName,
      statusName: row.statusName,
      statusColorCode: row.statusColorCode,
      priorityName: row.priorityName,
      priorityColorCode: row.priorityColorCode,
      orderDate: row.orderDate,
      totalAmount: row.totalAmount,
      customerApprovalStatus: row.customerApprovalStatus,
      paymentStatus: row.paymentStatus,
    }));
  }

  async checkOrderNumberAvailability(
    userId: string,
    businessId: string | null,
    orderNumber: string,
  ): Promise<ServiceOrderNumberAvailabilityResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingServiceOrder = await this.db
      .select({ id: serviceOrders.id })
      .from(serviceOrders)
      .where(
        and(
          eq(serviceOrders.businessId, businessId),
          ilike(serviceOrders.orderNumber, orderNumber),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return {
      available: !existingServiceOrder,
      orderNumber,
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ServiceOrderDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const serviceOrder = await this.db
      .select({
        id: serviceOrders.id,
        businessId: serviceOrders.businessId,
        orderNumber: serviceOrders.orderNumber,
        customerId: serviceOrders.customerId,
        appointmentId: serviceOrders.appointmentId,
        statusId: serviceOrders.statusId,
        priorityId: serviceOrders.priorityId,
        serviceTypeId: serviceOrders.serviceTypeId,
        locationId: serviceOrders.locationId,
        orderDate: serviceOrders.orderDate,
        scheduledDate: serviceOrders.scheduledDate,
        actualStartDate: serviceOrders.actualStartDate,
        actualEndDate: serviceOrders.actualEndDate,
        promisedDate: serviceOrders.promisedDate,
        description: serviceOrders.description,
        estimatedCost: serviceOrders.estimatedCost,
        actualCost: serviceOrders.actualCost,
        totalAmount: serviceOrders.totalAmount,
        taxType: serviceOrders.taxType,
        defaultTaxRateId: serviceOrders.defaultTaxRateId,
        receivedById: serviceOrders.receivedById,
        customerApprovalStatus: serviceOrders.customerApprovalStatus,
        customerApprovalDate: serviceOrders.customerApprovalDate,
        cancellationReason: serviceOrders.cancellationReason,
        paymentStatus: serviceOrders.paymentStatus,
        createdAt: serviceOrders.createdAt,
        updatedAt: serviceOrders.updatedAt,
        // Related data
        customerName: customers.customerDisplayName,
        customerEmail: customers.email,
        customerPhone: customers.phoneNumber,
        statusName: serviceOrderStatuses.statusName,
        statusCode: serviceOrderStatuses.statusCode,
        statusColorCode: serviceOrderStatuses.colorCode,
        priorityName: serviceOrderPriorities.priorityName,
        priorityCode: serviceOrderPriorities.priorityCode,
        priorityColorCode: serviceOrderPriorities.colorCode,
        serviceTypeName: serviceOrderTypes.typeName,
        serviceTypeCode: serviceOrderTypes.typeCode,
        locationName: locations.name,
        receivedByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        receivedByAvatar: users.avatar,
        createdByName: sql<string>`CONCAT(created_by_user.first_name, ' ', created_by_user.last_name)`,
        updatedByName: sql<string>`CONCAT(updated_by_user.first_name, ' ', updated_by_user.last_name)`,
      })
      .from(serviceOrders)
      .leftJoin(customers, eq(serviceOrders.customerId, customers.id))
      .leftJoin(
        serviceOrderStatuses,
        eq(serviceOrders.statusId, serviceOrderStatuses.id),
      )
      .leftJoin(
        serviceOrderPriorities,
        eq(serviceOrders.priorityId, serviceOrderPriorities.id),
      )
      .leftJoin(
        serviceOrderTypes,
        eq(serviceOrders.serviceTypeId, serviceOrderTypes.id),
      )
      .leftJoin(locations, eq(serviceOrders.locationId, locations.id))
      .leftJoin(staffMembers, eq(serviceOrders.receivedById, staffMembers.id))
      .leftJoin(users, eq(staffMembers.userId, users.id))
      .leftJoin(
        sql`users AS created_by_user`,
        sql`service_orders.created_by = created_by_user.id`,
      )
      .leftJoin(
        sql`users AS updated_by_user`,
        sql`service_orders.updated_by = updated_by_user.id`,
      )
      .where(
        and(
          eq(serviceOrders.id, id),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!serviceOrder) {
      throw new NotFoundException('Service order not found');
    }

    // Get related items
    const items = await this.db
      .select({
        id: serviceOrderItems.id,
        itemId: serviceOrderItems.itemId,
        quantity: serviceOrderItems.quantity,
        unitPrice: serviceOrderItems.unitPrice,
        totalPrice: serviceOrderItems.totalPrice,
        description: serviceOrderItems.description,
      })
      .from(serviceOrderItems)
      .where(
        and(
          eq(serviceOrderItems.serviceOrderId, id),
          eq(serviceOrderItems.isDeleted, false),
        ),
      );

    // Get related diagnostics
    const diagnostics = await this.db
      .select({
        id: serviceOrderDiagnostics.id,
        testPerformed: serviceOrderDiagnostics.testPerformed,
        testResult: serviceOrderDiagnostics.testResult,
        issueFound: serviceOrderDiagnostics.issueFound,
        recommendedAction: serviceOrderDiagnostics.recommendedAction,
      })
      .from(serviceOrderDiagnostics)
      .where(
        and(
          eq(serviceOrderDiagnostics.serviceOrderId, id),
          eq(serviceOrderDiagnostics.isDeleted, false),
        ),
      );

    // Get related lines
    const lines = await this.db
      .select({
        id: serviceOrderLines.id,
        lineType: serviceOrderLines.lineType,
        quantity: serviceOrderLines.quantity,
        unitPrice: serviceOrderLines.unitPrice,
        lineTotal: serviceOrderLines.lineTotal,
        notes: serviceOrderLines.notes,
      })
      .from(serviceOrderLines)
      .where(
        and(
          eq(serviceOrderLines.serviceOrderId, id),
          eq(serviceOrderLines.isDeleted, false),
        ),
      );

    return {
      id: serviceOrder.id,
      businessId: serviceOrder.businessId,
      orderNumber: serviceOrder.orderNumber,
      customerId: serviceOrder.customerId,
      appointmentId: serviceOrder.appointmentId,
      statusId: serviceOrder.statusId,
      priorityId: serviceOrder.priorityId,
      serviceTypeId: serviceOrder.serviceTypeId,
      locationId: serviceOrder.locationId,
      orderDate: serviceOrder.orderDate,
      scheduledDate: serviceOrder.scheduledDate,
      actualStartDate: serviceOrder.actualStartDate,
      actualEndDate: serviceOrder.actualEndDate,
      promisedDate: serviceOrder.promisedDate,
      description: serviceOrder.description,
      estimatedCost: serviceOrder.estimatedCost,
      actualCost: serviceOrder.actualCost,
      totalAmount: serviceOrder.totalAmount,
      taxType: serviceOrder.taxType,
      defaultTaxRateId: serviceOrder.defaultTaxRateId,
      receivedById: serviceOrder.receivedById,
      customerApprovalStatus: serviceOrder.customerApprovalStatus,
      customerApprovalDate: serviceOrder.customerApprovalDate,
      cancellationReason: serviceOrder.cancellationReason,
      paymentStatus: serviceOrder.paymentStatus,
      customer: {
        id: serviceOrder.customerId,
        name: serviceOrder.customerName,
        email: serviceOrder.customerEmail,
        phone: serviceOrder.customerPhone,
      },
      status: {
        id: serviceOrder.statusId,
        statusName: serviceOrder.statusName,
        statusCode: serviceOrder.statusCode,
        colorCode: serviceOrder.statusColorCode,
      },
      priority: {
        id: serviceOrder.priorityId,
        priorityName: serviceOrder.priorityName,
        priorityCode: serviceOrder.priorityCode,
        colorCode: serviceOrder.priorityColorCode,
      },
      serviceType: {
        id: serviceOrder.serviceTypeId,
        typeName: serviceOrder.serviceTypeName,
        typeCode: serviceOrder.serviceTypeCode,
      },
      location: {
        id: serviceOrder.locationId,
        name: serviceOrder.locationName,
      },
      receivedBy: {
        id: serviceOrder.receivedById,
        name: serviceOrder.receivedByName,
        avatar: serviceOrder.receivedByAvatar,
      },
      items,
      diagnostics,
      lines,
      createdBy: serviceOrder.createdByName,
      updatedBy: serviceOrder.updatedByName,
      createdAt: serviceOrder.createdAt,
      updatedAt: serviceOrder.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceOrderDto: UpdateServiceOrderDto,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if service order exists
    const existingServiceOrder = await this.db
      .select({ id: serviceOrders.id })
      .from(serviceOrders)
      .where(
        and(
          eq(serviceOrders.id, id),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingServiceOrder) {
      throw new NotFoundException('Service order not found');
    }

    // Validate foreign keys if provided
    await this.validateForeignKeys(businessId, updateServiceOrderDto);

    // Start transaction for updating service order with related data
    await this.db.transaction(async (tx) => {
      // Update main service order
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      if (updateServiceOrderDto.orderNumber !== undefined) {
        updateData.orderNumber = updateServiceOrderDto.orderNumber;
      }
      if (updateServiceOrderDto.customerId !== undefined) {
        updateData.customerId = updateServiceOrderDto.customerId;
      }
      if (updateServiceOrderDto.appointmentId !== undefined) {
        updateData.appointmentId = updateServiceOrderDto.appointmentId;
      }
      if (updateServiceOrderDto.statusId !== undefined) {
        updateData.statusId = updateServiceOrderDto.statusId;
      }
      if (updateServiceOrderDto.priorityId !== undefined) {
        updateData.priorityId = updateServiceOrderDto.priorityId;
      }
      if (updateServiceOrderDto.serviceTypeId !== undefined) {
        updateData.serviceTypeId = updateServiceOrderDto.serviceTypeId;
      }
      if (updateServiceOrderDto.locationId !== undefined) {
        updateData.locationId = updateServiceOrderDto.locationId;
      }
      if (updateServiceOrderDto.orderDate !== undefined) {
        updateData.orderDate = new Date(updateServiceOrderDto.orderDate);
      }
      if (updateServiceOrderDto.scheduledDate !== undefined) {
        updateData.scheduledDate = updateServiceOrderDto.scheduledDate
          ? new Date(updateServiceOrderDto.scheduledDate)
          : null;
      }
      if (updateServiceOrderDto.actualStartDate !== undefined) {
        updateData.actualStartDate = updateServiceOrderDto.actualStartDate
          ? new Date(updateServiceOrderDto.actualStartDate)
          : null;
      }
      if (updateServiceOrderDto.actualEndDate !== undefined) {
        updateData.actualEndDate = updateServiceOrderDto.actualEndDate
          ? new Date(updateServiceOrderDto.actualEndDate)
          : null;
      }
      if (updateServiceOrderDto.promisedDate !== undefined) {
        updateData.promisedDate = updateServiceOrderDto.promisedDate
          ? new Date(updateServiceOrderDto.promisedDate)
          : null;
      }
      if (updateServiceOrderDto.description !== undefined) {
        updateData.description = updateServiceOrderDto.description;
      }
      if (updateServiceOrderDto.estimatedCost !== undefined) {
        updateData.estimatedCost = updateServiceOrderDto.estimatedCost;
      }
      if (updateServiceOrderDto.actualCost !== undefined) {
        updateData.actualCost = updateServiceOrderDto.actualCost;
      }
      if (updateServiceOrderDto.totalAmount !== undefined) {
        updateData.totalAmount = updateServiceOrderDto.totalAmount;
      }
      if (updateServiceOrderDto.taxType !== undefined) {
        updateData.taxType = updateServiceOrderDto.taxType;
      }
      if (updateServiceOrderDto.defaultTaxRateId !== undefined) {
        updateData.defaultTaxRateId = updateServiceOrderDto.defaultTaxRateId;
      }
      if (updateServiceOrderDto.receivedById !== undefined) {
        updateData.receivedById = updateServiceOrderDto.receivedById;
      }
      if (updateServiceOrderDto.customerApprovalStatus !== undefined) {
        updateData.customerApprovalStatus =
          updateServiceOrderDto.customerApprovalStatus;
      }
      if (updateServiceOrderDto.customerApprovalDate !== undefined) {
        updateData.customerApprovalDate =
          updateServiceOrderDto.customerApprovalDate
            ? new Date(updateServiceOrderDto.customerApprovalDate)
            : null;
      }
      if (updateServiceOrderDto.cancellationReason !== undefined) {
        updateData.cancellationReason =
          updateServiceOrderDto.cancellationReason;
      }
      if (updateServiceOrderDto.paymentStatus !== undefined) {
        updateData.paymentStatus = updateServiceOrderDto.paymentStatus;
      }

      await tx
        .update(serviceOrders)
        .set(updateData)
        .where(eq(serviceOrders.id, id));

      // Handle related data updates if provided
      // Note: For simplicity, we're not implementing full CRUD for related tables in update
      // In a production system, you might want to handle adding/updating/removing related records
    });

    return { id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceOrderDto: UpdateServiceOrderDto,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateServiceOrderDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingServiceOrder = await this.db
      .select({
        id: serviceOrders.id,
        orderNumber: serviceOrders.orderNumber,
      })
      .from(serviceOrders)
      .where(
        and(
          eq(serviceOrders.id, id),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingServiceOrder) {
      throw new NotFoundException('Service order not found');
    }

    // Soft delete the service order and related data
    await this.db.transaction(async (tx) => {
      const deletedAt = new Date();

      // Soft delete main service order
      await tx
        .update(serviceOrders)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: deletedAt,
        })
        .where(eq(serviceOrders.id, id));

      // Soft delete related items
      await tx
        .update(serviceOrderItems)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: deletedAt,
        })
        .where(eq(serviceOrderItems.serviceOrderId, id));

      // Soft delete related diagnostics
      await tx
        .update(serviceOrderDiagnostics)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(serviceOrderDiagnostics.serviceOrderId, id));

      // Soft delete related lines and their staff assignments
      const linesToDelete = await tx
        .select({ id: serviceOrderLines.id })
        .from(serviceOrderLines)
        .where(eq(serviceOrderLines.serviceOrderId, id));

      if (linesToDelete.length > 0) {
        const lineIds = linesToDelete.map((line) => line.id);

        await tx
          .update(serviceOrderLines)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceOrderLines.serviceOrderId, id));

        await tx
          .update(serviceOrderLineStaff)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(serviceOrderLineStaff.lineId, lineIds));
      }
    });

    return { id };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<BulkDeleteServiceOrderResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        await this.remove(userId, businessId, id);
        deletedIds.push(id);
      } catch {
        failedIds.push(id);
      }
    }

    return {
      deletedCount: deletedIds.length,
      deletedIds,
      failedIds,
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    statusId: string,
  ): Promise<{
    updatedCount: number;
    updatedIds: string[];
    failedIds: string[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Validate status exists
    const status = await this.db
      .select({ id: serviceOrderStatuses.id })
      .from(serviceOrderStatuses)
      .where(
        and(
          eq(serviceOrderStatuses.id, statusId),
          eq(serviceOrderStatuses.businessId, businessId),
          eq(serviceOrderStatuses.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!status) {
      throw new BadRequestException('Service order status not found');
    }

    const updatedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        // Check if service order exists
        const existingServiceOrder = await this.db
          .select({ id: serviceOrders.id })
          .from(serviceOrders)
          .where(
            and(
              eq(serviceOrders.id, id),
              eq(serviceOrders.businessId, businessId),
              eq(serviceOrders.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingServiceOrder) {
          await this.db
            .update(serviceOrders)
            .set({
              statusId,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(serviceOrders.id, id));

          updatedIds.push(id);
        } else {
          failedIds.push(id);
        }
      } catch {
        failedIds.push(id);
      }
    }

    return {
      updatedCount: updatedIds.length,
      updatedIds,
      failedIds,
    };
  }

  async bulkUpdatePaymentStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    paymentStatus: ServiceOrderPaymentStatus,
  ): Promise<{
    updatedCount: number;
    updatedIds: string[];
    failedIds: string[];
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const updatedIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        // Check if service order exists
        const existingServiceOrder = await this.db
          .select({ id: serviceOrders.id })
          .from(serviceOrders)
          .where(
            and(
              eq(serviceOrders.id, id),
              eq(serviceOrders.businessId, businessId),
              eq(serviceOrders.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingServiceOrder) {
          await this.db
            .update(serviceOrders)
            .set({
              paymentStatus,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(serviceOrders.id, id));

          updatedIds.push(id);
        } else {
          failedIds.push(id);
        }
      } catch {
        failedIds.push(id);
      }
    }

    return {
      updatedCount: updatedIds.length,
      updatedIds,
      failedIds,
    };
  }

  // Service Order Line Task Management Methods

  /**
   * Create a task for a service order line staff
   */
  async createServiceOrderLineTask(
    userId: string,
    businessId: string | null,
    serviceOrderId: string,
    lineStaffId: string,
    createTaskDto: CreateServiceOrderLineTaskDto,
  ): Promise<ServiceOrderLineTaskIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Verify service order exists and belongs to business
    const serviceOrder = await this.db
      .select({ id: serviceOrders.id })
      .from(serviceOrders)
      .where(
        and(
          eq(serviceOrders.id, serviceOrderId),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!serviceOrder) {
      throw new NotFoundException('Service order not found');
    }

    // Verify service order line staff exists
    const lineStaff = await this.db
      .select({
        id: serviceOrderLineStaff.id,
        staffId: serviceOrderLineStaff.staffId,
      })
      .from(serviceOrderLineStaff)
      .innerJoin(
        serviceOrderLines,
        eq(serviceOrderLineStaff.lineId, serviceOrderLines.id),
      )
      .where(
        and(
          eq(serviceOrderLineStaff.id, lineStaffId),
          eq(serviceOrderLines.serviceOrderId, serviceOrderId),
          eq(serviceOrderLineStaff.isDeleted, false),
          eq(serviceOrderLines.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!lineStaff) {
      throw new NotFoundException('Service order line staff not found');
    }

    // Check if task already exists for this line staff
    const existingTask = await this.db
      .select({ id: serviceOrderLineStaff.taskId })
      .from(serviceOrderLineStaff)
      .where(
        and(
          eq(serviceOrderLineStaff.id, lineStaffId),
          eq(serviceOrderLineStaff.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (existingTask?.id) {
      throw new ConflictException(
        'Task already exists for this service order line staff',
      );
    }

    // Create the task
    const taskResult = await this.tasksService.createTaskForServiceOrder(
      userId,
      businessId,
      serviceOrderId,
      {
        title: createTaskDto.title,
        description: createTaskDto.description,
        dueDate: createTaskDto.dueDate,
        priority: createTaskDto.priority,
        status: createTaskDto.status,
        assignedTo: createTaskDto.assignedTo,
      },
    );

    // Update the service order line staff with the task ID
    await this.db
      .update(serviceOrderLineStaff)
      .set({
        taskId: taskResult.id,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(serviceOrderLineStaff.id, lineStaffId));

    return { id: taskResult.id };
  }

  /**
   * Get task for a service order line staff
   */
  async getServiceOrderLineTask(
    userId: string,
    businessId: string | null,
    serviceOrderId: string,
    lineStaffId: string,
  ): Promise<ServiceOrderLineTaskDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get task information with joins
    const taskData = await this.db
      .select({
        taskId: serviceOrderLineStaff.taskId,
        serviceOrderId: serviceOrders.id,
        lineStaffId: serviceOrderLineStaff.id,
      })
      .from(serviceOrderLineStaff)
      .innerJoin(
        serviceOrderLines,
        eq(serviceOrderLineStaff.lineId, serviceOrderLines.id),
      )
      .innerJoin(
        serviceOrders,
        eq(serviceOrderLines.serviceOrderId, serviceOrders.id),
      )
      .where(
        and(
          eq(serviceOrderLineStaff.id, lineStaffId),
          eq(serviceOrders.id, serviceOrderId),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrderLineStaff.isDeleted, false),
          eq(serviceOrderLines.isDeleted, false),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!taskData || !taskData.taskId) {
      throw new NotFoundException(
        'Task not found for this service order line staff',
      );
    }

    // Get the actual task details from tasks service
    const task = await this.tasksService.findOne(
      userId,
      businessId,
      taskData.taskId,
    );

    return {
      id: task.id,
      title: task.title,
      description: task.description,
      dueDate: task.dueDate,
      priority: task.priority,
      status: task.status,
      assignedTo: task.assignedStaffMember
        ? {
            id: task.assignedStaffMember.id,
            name: task.assignedStaffMember.displayName,
          }
        : undefined,
      serviceOrderId: taskData.serviceOrderId,
      serviceOrderLineStaffId: taskData.lineStaffId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
    };
  }

  /**
   * Update task for a service order line staff
   */
  async updateServiceOrderLineTask(
    userId: string,
    businessId: string | null,
    serviceOrderId: string,
    lineStaffId: string,
    updateTaskDto: UpdateServiceOrderLineTaskDto,
  ): Promise<ServiceOrderLineTaskIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get task ID from service order line staff
    const taskData = await this.db
      .select({
        taskId: serviceOrderLineStaff.taskId,
      })
      .from(serviceOrderLineStaff)
      .innerJoin(
        serviceOrderLines,
        eq(serviceOrderLineStaff.lineId, serviceOrderLines.id),
      )
      .innerJoin(
        serviceOrders,
        eq(serviceOrderLines.serviceOrderId, serviceOrders.id),
      )
      .where(
        and(
          eq(serviceOrderLineStaff.id, lineStaffId),
          eq(serviceOrders.id, serviceOrderId),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrderLineStaff.isDeleted, false),
          eq(serviceOrderLines.isDeleted, false),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!taskData || !taskData.taskId) {
      throw new NotFoundException(
        'Task not found for this service order line staff',
      );
    }

    // Update the task using tasks service
    await this.tasksService.update(
      userId,
      businessId,
      taskData.taskId,
      updateTaskDto,
    );

    return { id: taskData.taskId };
  }

  /**
   * Delete task for a service order line staff
   */
  async deleteServiceOrderLineTask(
    userId: string,
    businessId: string | null,
    serviceOrderId: string,
    lineStaffId: string,
  ): Promise<ServiceOrderLineTaskIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get task ID from service order line staff
    const taskData = await this.db
      .select({
        taskId: serviceOrderLineStaff.taskId,
      })
      .from(serviceOrderLineStaff)
      .innerJoin(
        serviceOrderLines,
        eq(serviceOrderLineStaff.lineId, serviceOrderLines.id),
      )
      .innerJoin(
        serviceOrders,
        eq(serviceOrderLines.serviceOrderId, serviceOrders.id),
      )
      .where(
        and(
          eq(serviceOrderLineStaff.id, lineStaffId),
          eq(serviceOrders.id, serviceOrderId),
          eq(serviceOrders.businessId, businessId),
          eq(serviceOrderLineStaff.isDeleted, false),
          eq(serviceOrderLines.isDeleted, false),
          eq(serviceOrders.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!taskData || !taskData.taskId) {
      throw new NotFoundException(
        'Task not found for this service order line staff',
      );
    }

    // Delete the task using tasks service
    await this.tasksService.remove(userId, businessId, taskData.taskId);

    // Remove task ID from service order line staff
    await this.db
      .update(serviceOrderLineStaff)
      .set({
        taskId: null,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(serviceOrderLineStaff.id, lineStaffId));

    return { id: taskData.taskId };
  }

  /**
   * Delete task and service order line staff
   */
  async deleteServiceOrderLineTaskWithLine(
    userId: string,
    businessId: string | null,
    serviceOrderId: string,
    lineStaffId: string,
  ): Promise<ServiceOrderLineTaskIdResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    return await this.db.transaction(async (tx) => {
      // Get task ID and verify service order line staff exists
      const taskData = await tx
        .select({
          taskId: serviceOrderLineStaff.taskId,
          lineId: serviceOrderLineStaff.lineId,
        })
        .from(serviceOrderLineStaff)
        .innerJoin(
          serviceOrderLines,
          eq(serviceOrderLineStaff.lineId, serviceOrderLines.id),
        )
        .innerJoin(
          serviceOrders,
          eq(serviceOrderLines.serviceOrderId, serviceOrders.id),
        )
        .where(
          and(
            eq(serviceOrderLineStaff.id, lineStaffId),
            eq(serviceOrders.id, serviceOrderId),
            eq(serviceOrders.businessId, businessId),
            eq(serviceOrderLineStaff.isDeleted, false),
            eq(serviceOrderLines.isDeleted, false),
            eq(serviceOrders.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!taskData) {
        throw new NotFoundException('Service order line staff not found');
      }

      let deletedTaskId: string | null = null;

      // Delete the task if it exists
      if (taskData.taskId) {
        await this.tasksService.remove(userId, businessId, taskData.taskId);
        deletedTaskId = taskData.taskId;
      }

      // Soft delete the service order line staff
      await tx
        .update(serviceOrderLineStaff)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(serviceOrderLineStaff.id, lineStaffId));

      return { id: deletedTaskId || lineStaffId };
    });
  }
}
