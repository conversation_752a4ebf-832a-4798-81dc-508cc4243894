import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsEnum,
  IsOptional,
  IsDateString,
  IsObject,
} from 'class-validator';
import { ActivitySource } from '../../shared/types/activity.enum';
import { TokensDto } from './verify-2fa.dto';
import { UserProfileDto } from '../../users/dto/user-profile.dto';
import { SessionDto } from './session.dto';

/**
 * Combined login and session creation DTO
 */
export class LoginWithSessionDto {
  // Login fields
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email for login',
  })
  @IsString()
  @IsNotEmpty()
  identifier: string;

  @ApiProperty({
    example: '123456',
    description: 'User password',
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  // Session fields
  @ApiProperty({
    description: 'Unique session identifier generated by client',
    example: 'web_1640995200000_abc123',
  })
  @IsString()
  @IsNotEmpty()
  sessionId: string;

  @ApiProperty({
    description: 'Source of the session',
    enum: ActivitySource,
    example: ActivitySource.WEB,
  })
  @IsEnum(ActivitySource)
  source: ActivitySource;

  @ApiPropertyOptional({
    description: 'Device identifier for tracking',
    example: 'device_fingerprint_123',
  })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiPropertyOptional({
    description: 'Additional device information',
    example: { os: 'Windows 10', browser: 'Chrome', version: '96.0' },
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: any;

  @ApiPropertyOptional({
    description: 'User timezone',
    example: 'America/New_York',
  })
  @IsOptional()
  @IsString()
  timezone?: string;

  @ApiPropertyOptional({
    description: 'Session expiry time (ISO string)',
    example: '2024-01-15T10:30:00Z',
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

/**
 * Enhanced login response with session info
 */
export class LoginWithSessionResponseDto {
  @ApiProperty({
    description: 'Authentication tokens',
    type: TokensDto,
  })
  tokens: TokensDto;

  @ApiProperty({
    description: 'User profile information',
    type: UserProfileDto,
  })
  user: UserProfileDto;

  @ApiProperty({
    description: 'Session information',
    type: SessionDto,
  })
  session: SessionDto;

  @ApiPropertyOptional({
    description: 'Whether 2FA is required',
    example: false,
  })
  requires2FA?: boolean;
}