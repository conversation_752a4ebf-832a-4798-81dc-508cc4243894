import { ActivitySource } from '../../shared/types/activity.enum';

/**
 * Session status values
 */
export type SessionStatus = 'ACTIVE' | 'EXPIRED' | 'REVOKED' | 'LOGGED_OUT';

/**
 * Session source type (reusing ActivitySource values)
 */
export type SessionSource =
  | ActivitySource.WEB
  | ActivitySource.MOBILE
  | ActivitySource.API;

/**
 * Device type for better categorization
 */
export type DeviceType =
  | 'DESKTOP'
  | 'MOBILE'
  | 'TABLET'
  | 'API_CLIENT'
  | 'UNKNOWN';

/**
 * User Session interface for Redis storage
 */
export interface UserSession {
  // Primary identification
  id: string;
  sessionId: string; // External session ID
  userId: string;

  // Session metadata
  source: SessionSource;
  deviceType: DeviceType;
  status: SessionStatus;

  // Device/Client information
  userAgent?: string | null;
  ipAddress?: string | null;
  deviceId?: string | null;
  deviceInfo?: string | null; // JSON string with device details

  // Location information (optional)
  country?: string | null;
  city?: string | null;
  timezone?: string | null;

  // Session tracking
  firstSeenAt: Date;
  lastSeenAt: Date;
  expiresAt?: Date | null; // Optional session expiry

  // Activity counters
  requestCount: number;
  lastActivityType?: string | null; // Last action performed

  // Session termination
  endedAt?: Date | null;
  endReason?: string | null; // LOGOUT, TIMEOUT, REVOKED, etc.

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Session with user information
 */
export type SessionWithUser = UserSession & {
  user: {
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
    role: string;
    status: string;
  };
};

/**
 * Session creation data
 */
export interface CreateSessionData {
  sessionId: string;
  userId: string;
  source: ActivitySource;
  userAgent?: string;
  ipAddress?: string;
  deviceId?: string;
  deviceInfo?: any;
  country?: string;
  city?: string;
  timezone?: string;
  expiresAt?: Date;
}

/**
 * Session update data
 */
export interface UpdateSessionData {
  lastSeenAt?: Date;
  requestCount?: number;
  lastActivityType?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
}

/**
 * Session query filters
 */
export interface SessionQueryFilters {
  userId?: string;
  status?: SessionStatus;
  source?: SessionSource;
  deviceType?: DeviceType;
  ipAddress?: string;
  deviceId?: string;
  activeSince?: Date;
  expiredBefore?: Date;
  country?: string;
}

/**
 * Session statistics
 */
export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
  revokedSessions: number;
  bySource: Record<SessionSource, number>;
  byDeviceType: Record<DeviceType, number>;
  uniqueDevices: number;
  uniqueIPs: number;
  averageSessionDuration: number; // in minutes
}

/**
 * Device information structure
 */
export interface DeviceInfo {
  os?: string;
  browser?: string;
  version?: string;
  platform?: string;
  mobile?: boolean;
  screenResolution?: string;
  language?: string;
}
