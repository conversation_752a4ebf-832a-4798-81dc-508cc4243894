import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../redis/redis.service';
import {
  UserSession,
  SessionSource,
  DeviceType,
  CreateSessionData,
  UpdateSessionData,
  SessionStats,
  SessionWithUser,
  DeviceInfo,
} from '../types/session.types';
import { ActivitySource, ActivityType } from '../../shared/types/activity.enum';
import { ActivityLogService } from '../../activity-log/activity-log.service';

/**
 * Redis-based Session Service
 *
 * Provides high-performance session management using Redis as storage backend.
 * Features automatic expiration, better scalability, and reduced database load.
 */
@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  // Redis key patterns
  private readonly SESSION_KEY = 'session:{sessionId}';
  private readonly USER_SESSIONS_KEY = 'user:{userId}:sessions';
  private readonly SESSION_STATS_KEY = 'user:{userId}:session:stats';
  private readonly SESSION_ACTIVITY_KEY = 'session:{sessionId}:activity';

  // Default TTL values (in seconds)
  private readonly DEFAULT_SESSION_TTL = 24 * 60 * 60; // 24 hours
  private readonly STATS_TTL = 7 * 24 * 60 * 60; // 7 days
  private readonly ACTIVITY_TTL = 30 * 24 * 60 * 60; // 30 days

  constructor(
    private readonly redisService: RedisService,
    private readonly activityLogService: ActivityLogService,
  ) {}

  /**
   * Create a new user session in Redis
   */
  async createSession(data: CreateSessionData): Promise<UserSession> {
    try {
      const deviceType = this.detectDeviceType(data.userAgent, data.source);
      const deviceInfo = this.parseDeviceInfo(data.userAgent);

      const now = new Date();
      const expiresAt =
        data.expiresAt ||
        new Date(now.getTime() + this.DEFAULT_SESSION_TTL * 1000);

      const session: UserSession = {
        id: this.generateUUID(),
        sessionId: data.sessionId,
        userId: data.userId,
        source: data.source as SessionSource,
        deviceType,
        status: 'ACTIVE',
        userAgent: data.userAgent,
        ipAddress: data.ipAddress,
        deviceId: data.deviceId,
        deviceInfo: data.deviceInfo ? JSON.stringify(data.deviceInfo) : null,
        country: data.country,
        city: data.city,
        timezone: data.timezone,
        firstSeenAt: now,
        lastSeenAt: now,
        expiresAt,
        requestCount: 0,
        lastActivityType: null,
        endedAt: null,
        endReason: null,
        createdAt: now,
        updatedAt: now,
      };

      // Calculate TTL based on expiry time
      const ttlSeconds = Math.floor(
        (expiresAt.getTime() - now.getTime()) / 1000,
      );

      // Store session data in Redis
      await this.redisService.set(
        this.getSessionKey(data.sessionId),
        session,
        { ttl: Math.max(ttlSeconds, 60) }, // Minimum 1 minute TTL
      );

      // Add session to user's active sessions set
      await this.addToUserSessions(data.userId, data.sessionId, ttlSeconds);

      // Initialize session activity tracking
      await this.initializeSessionActivity(data.sessionId, ttlSeconds);

      // Update user session statistics
      await this.updateSessionStats(
        data.userId,
        'create',
        data.source as SessionSource,
        deviceType,
      );

      // Log session creation
      await this.activityLogService.logAuthEvent(
        ActivityType.LOGIN,
        data.userId,
        {
          ipAddress: data.ipAddress || '',
          userAgent: data.userAgent || '',
          sessionId: data.sessionId,
          deviceInfo: deviceInfo,
        },
      );

      this.logger.log(
        `Redis session created: ${data.sessionId} for user ${data.userId}, TTL: ${ttlSeconds}s`,
      );

      return session;
    } catch (error) {
      this.logger.error('Failed to create Redis session', {
        error: error.message,
        sessionId: data.sessionId,
        userId: data.userId,
      });
      throw error;
    }
  }

  /**
   * Update an existing session in Redis
   */
  async updateSession(
    sessionId: string,
    data: UpdateSessionData,
  ): Promise<UserSession | null> {
    try {
      const sessionKey = this.getSessionKey(sessionId);
      const session = await this.redisService.get<UserSession>(sessionKey);

      if (!session) {
        this.logger.warn(`Session not found for update: ${sessionId}`);
        return null;
      }

      // Update session data
      const updatedSession: UserSession = {
        ...session,
        lastSeenAt: data.lastSeenAt || new Date(),
        requestCount:
          data.requestCount !== undefined
            ? data.requestCount
            : session.requestCount,
        lastActivityType: data.lastActivityType || session.lastActivityType,
        ipAddress: data.ipAddress || session.ipAddress,
        country: data.country || session.country,
        city: data.city || session.city,
        updatedAt: new Date(),
      };

      // Preserve original TTL
      const currentTtl = await this.redisService.ttl(sessionKey);
      const ttl = currentTtl > 0 ? currentTtl : this.DEFAULT_SESSION_TTL;

      await this.redisService.set(sessionKey, updatedSession, { ttl });

      return updatedSession;
    } catch (error) {
      this.logger.error('Failed to update Redis session', {
        error: error.message,
        sessionId,
      });
      throw error;
    }
  }

  /**
   * Get session by session ID from Redis
   */
  async getSession(sessionId: string): Promise<UserSession | null> {
    try {
      const session = await this.redisService.get<UserSession>(
        this.getSessionKey(sessionId),
      );

      if (session && this.isSessionExpired(session)) {
        await this.expireSession(sessionId);
        return null;
      }

      return session;
    } catch (error) {
      this.logger.error('Failed to get Redis session', {
        error: error.message,
        sessionId,
      });
      return null;
    }
  }

  /**
   * Get session with user information
   */
  async getSessionWithUser(sessionId: string): Promise<SessionWithUser | null> {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return null;
      }

      // For Redis implementation, we return the session as-is
      // User information would need to be fetched separately if needed
      return session as SessionWithUser;
    } catch (error) {
      this.logger.error('Failed to get session with user', {
        error: error.message,
        sessionId,
      });
      return null;
    }
  }

  /**
   * Get user's active sessions from Redis
   */
  async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    try {
      const sessionIds = await this.redisService.smembers<string>(
        this.getUserSessionsKey(userId),
      );

      if (sessionIds.length === 0) {
        return [];
      }

      // Get all sessions in parallel
      const sessionKeys = sessionIds.map((id) => this.getSessionKey(id));
      const sessions = await this.redisService.mget<UserSession>(sessionKeys);

      // Filter out null/expired sessions and clean up invalid references
      const activeSessions: UserSession[] = [];
      const expiredSessionIds: string[] = [];

      sessions.forEach((session, index) => {
        if (!session) {
          expiredSessionIds.push(sessionIds[index]);
        } else if (this.isSessionExpired(session)) {
          expiredSessionIds.push(sessionIds[index]);
          this.expireSession(sessionIds[index]).catch((err) =>
            this.logger.error(
              `Failed to expire session ${sessionIds[index]}:`,
              err,
            ),
          );
        } else if (session.status === 'ACTIVE') {
          activeSessions.push(session);
        }
      });

      // Clean up expired session references
      if (expiredSessionIds.length > 0) {
        await this.removeFromUserSessions(userId, expiredSessionIds);
      }

      // Sort by last seen time (most recent first)
      return activeSessions.sort(
        (a, b) => b.lastSeenAt.getTime() - a.lastSeenAt.getTime(),
      );
    } catch (error) {
      this.logger.error('Failed to get user active sessions', {
        error: error.message,
        userId,
      });
      return [];
    }
  }

  /**
   * End a session (logout)
   */
  async endSession(
    sessionId: string,
    reason: string = 'LOGOUT',
    userId?: string,
  ): Promise<UserSession | null> {
    try {
      const sessionKey = this.getSessionKey(sessionId);
      const session = await this.redisService.get<UserSession>(sessionKey);

      if (!session) {
        this.logger.warn(`Session not found for ending: ${sessionId}`);
        return null;
      }

      const endedSession: UserSession = {
        ...session,
        status: 'LOGGED_OUT',
        endedAt: new Date(),
        endReason: reason,
        updatedAt: new Date(),
      };

      // Store ended session with shorter TTL for cleanup
      await this.redisService.set(sessionKey, endedSession, { ttl: 3600 }); // 1 hour

      // Remove from user's active sessions
      await this.removeFromUserSessions(session.userId, [sessionId]);

      // Update session statistics
      await this.updateSessionStats(
        session.userId,
        'end',
        session.source,
        session.deviceType,
      );

      if (userId) {
        // Calculate session duration
        const sessionDuration = endedSession.endedAt
          ? endedSession.endedAt.getTime() - session.firstSeenAt.getTime()
          : 0;

        // Log session end
        await this.activityLogService.logAuthEvent(
          ActivityType.LOGOUT,
          userId,
          {
            ipAddress: session.ipAddress || '',
            userAgent: session.userAgent || '',
            sessionId: sessionId,
            sessionDuration: Math.floor(sessionDuration / 1000), // in seconds
          },
        );
      }

      this.logger.log(`Redis session ended: ${sessionId}, reason: ${reason}`);
      return endedSession;
    } catch (error) {
      this.logger.error('Failed to end Redis session', {
        error: error.message,
        sessionId,
        reason,
      });
      throw error;
    }
  }

  /**
   * Revoke session (admin action or security)
   */
  async revokeSession(
    sessionId: string,
    reason: string = 'REVOKED',
    revokedBy?: string,
  ): Promise<UserSession | null> {
    try {
      const sessionKey = this.getSessionKey(sessionId);
      const session = await this.redisService.get<UserSession>(sessionKey);

      if (!session) {
        this.logger.warn(`Session not found for revocation: ${sessionId}`);
        return null;
      }

      const revokedSession: UserSession = {
        ...session,
        status: 'REVOKED',
        endedAt: new Date(),
        endReason: reason,
        updatedAt: new Date(),
      };

      // Store revoked session with shorter TTL
      await this.redisService.set(sessionKey, revokedSession, { ttl: 3600 }); // 1 hour

      // Remove from user's active sessions
      await this.removeFromUserSessions(session.userId, [sessionId]);

      // Update session statistics
      await this.updateSessionStats(
        session.userId,
        'revoke',
        session.source,
        session.deviceType,
      );

      this.logger.warn(`Redis session revoked: ${sessionId}`, {
        reason,
        revokedBy,
      });

      return revokedSession;
    } catch (error) {
      this.logger.error('Failed to revoke Redis session', {
        error: error.message,
        sessionId,
        reason,
      });
      throw error;
    }
  }

  /**
   * Revoke all user sessions except current
   */
  async revokeAllUserSessions(
    userId: string,
    exceptSessionId?: string,
    reason: string = 'REVOKE_ALL',
  ): Promise<number> {
    try {
      const sessionIds = await this.redisService.smembers<string>(
        this.getUserSessionsKey(userId),
      );

      const sessionsToRevoke = exceptSessionId
        ? sessionIds.filter((id) => id !== exceptSessionId)
        : sessionIds;

      if (sessionsToRevoke.length === 0) {
        return 0;
      }

      // Revoke sessions in parallel
      const revokePromises = sessionsToRevoke.map((sessionId) =>
        this.revokeSession(sessionId, reason),
      );

      const revokedSessions = await Promise.allSettled(revokePromises);
      const successCount = revokedSessions.filter(
        (result) => result.status === 'fulfilled' && result.value !== null,
      ).length;

      this.logger.log(
        `Revoked ${successCount} Redis sessions for user ${userId}`,
        {
          exceptSessionId,
          reason,
          totalAttempted: sessionsToRevoke.length,
        },
      );

      return successCount;
    } catch (error) {
      this.logger.error('Failed to revoke user Redis sessions', {
        error: error.message,
        userId,
        exceptSessionId,
      });
      throw error;
    }
  }

  /**
   * Clean up expired sessions (called by scheduled task)
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      // Redis automatically handles expiration, but we can clean up references
      const userSessionKeys = await this.redisService.keys('user:*:sessions');
      let cleanedCount = 0;

      for (const userKey of userSessionKeys) {
        const sessionIds = await this.redisService.smembers<string>(userKey);
        const expiredIds: string[] = [];

        for (const sessionId of sessionIds) {
          const exists = await this.redisService.exists(
            this.getSessionKey(sessionId),
          );
          if (exists === 0) {
            expiredIds.push(sessionId);
          }
        }

        if (expiredIds.length > 0) {
          const userId = userKey.split(':')[1];
          await this.removeFromUserSessions(userId, expiredIds);
          cleanedCount += expiredIds.length;
        }
      }

      if (cleanedCount > 0) {
        this.logger.log(
          `Cleaned up ${cleanedCount} expired Redis session references`,
        );
      }

      return cleanedCount;
    } catch (error) {
      this.logger.error('Failed to cleanup expired Redis sessions', {
        error: error.message,
      });
      return 0;
    }
  }

  /**
   * Get session statistics for a user
   */
  async getUserSessionStats(userId: string): Promise<SessionStats> {
    try {
      const statsKey = this.getSessionStatsKey(userId);
      let stats = await this.redisService.get<SessionStats>(statsKey);

      if (!stats) {
        // Initialize empty stats
        stats = {
          totalSessions: 0,
          activeSessions: 0,
          expiredSessions: 0,
          revokedSessions: 0,
          bySource: {
            [ActivitySource.WEB]: 0,
            [ActivitySource.MOBILE]: 0,
            [ActivitySource.API]: 0,
          },
          byDeviceType: {
            DESKTOP: 0,
            MOBILE: 0,
            TABLET: 0,
            API_CLIENT: 0,
            UNKNOWN: 0,
          },
          uniqueDevices: 0,
          uniqueIPs: 0,
          averageSessionDuration: 0,
        };
      }

      // Get current active sessions count
      const activeSessions = await this.getUserActiveSessions(userId);
      stats.activeSessions = activeSessions.length;

      return stats;
    } catch (error) {
      this.logger.error('Failed to get user session stats', {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Track session activity (increment request count, update last seen)
   */
  async trackSessionActivity(
    sessionId: string,
    activityType?: string,
    ipAddress?: string,
  ): Promise<void> {
    try {
      const sessionKey = this.getSessionKey(sessionId);
      const session = await this.redisService.get<UserSession>(sessionKey);

      if (!session || session.status !== 'ACTIVE') {
        return;
      }

      // Update session data
      const updatedSession: UserSession = {
        ...session,
        lastSeenAt: new Date(),
        requestCount: session.requestCount + 1,
        lastActivityType: activityType || session.lastActivityType,
        ipAddress: ipAddress || session.ipAddress,
        updatedAt: new Date(),
      };

      // Preserve TTL
      const currentTtl = await this.redisService.ttl(sessionKey);
      const ttl = currentTtl > 0 ? currentTtl : this.DEFAULT_SESSION_TTL;

      await this.redisService.set(sessionKey, updatedSession, { ttl });

      // Track activity pattern (for analytics)
      if (activityType) {
        await this.trackActivityPattern(sessionId, activityType, ttl);
      }
    } catch (error) {
      this.logger.error('Failed to track session activity', {
        error: error.message,
        sessionId,
        activityType,
      });
      // Don't throw error for activity tracking to avoid disrupting main flow
    }
  }

  // Private helper methods

  private getSessionKey(sessionId: string): string {
    return this.SESSION_KEY.replace('{sessionId}', sessionId);
  }

  private getUserSessionsKey(userId: string): string {
    return this.USER_SESSIONS_KEY.replace('{userId}', userId);
  }

  private getSessionStatsKey(userId: string): string {
    return this.SESSION_STATS_KEY.replace('{userId}', userId);
  }

  private getSessionActivityKey(sessionId: string): string {
    return this.SESSION_ACTIVITY_KEY.replace('{sessionId}', sessionId);
  }

  private async addToUserSessions(
    userId: string,
    sessionId: string,
    ttl: number,
  ): Promise<void> {
    const userSessionsKey = this.getUserSessionsKey(userId);
    await this.redisService.sadd(userSessionsKey, sessionId);
    await this.redisService.expire(userSessionsKey, ttl);
  }

  private async removeFromUserSessions(
    userId: string,
    sessionIds: string[],
  ): Promise<void> {
    const userSessionsKey = this.getUserSessionsKey(userId);
    const pipeline = this.redisService.getClient().pipeline();

    sessionIds.forEach((sessionId) => {
      pipeline.srem(userSessionsKey, JSON.stringify(sessionId));
    });

    await pipeline.exec();
  }

  private async initializeSessionActivity(
    sessionId: string,
    ttl: number,
  ): Promise<void> {
    const activityKey = this.getSessionActivityKey(sessionId);
    await this.redisService.set(
      activityKey,
      { requests: 0, activities: [] },
      { ttl },
    );
  }

  private async trackActivityPattern(
    sessionId: string,
    activityType: string,
    ttl: number,
  ): Promise<void> {
    const activityKey = this.getSessionActivityKey(sessionId);
    await this.redisService.lpush(activityKey, {
      type: activityType,
      timestamp: new Date(),
    });
    await this.redisService.expire(
      activityKey,
      Math.min(ttl, this.ACTIVITY_TTL),
    );
  }

  private async updateSessionStats(
    userId: string,
    action: 'create' | 'end' | 'revoke',
    source: SessionSource,
    deviceType: DeviceType,
  ): Promise<void> {
    try {
      const statsKey = this.getSessionStatsKey(userId);
      const stats =
        (await this.redisService.get<SessionStats>(statsKey)) ||
        ({
          totalSessions: 0,
          activeSessions: 0,
          expiredSessions: 0,
          revokedSessions: 0,
          bySource: {
            [ActivitySource.WEB]: 0,
            [ActivitySource.MOBILE]: 0,
            [ActivitySource.API]: 0,
          },
          byDeviceType: {
            DESKTOP: 0,
            MOBILE: 0,
            TABLET: 0,
            API_CLIENT: 0,
            UNKNOWN: 0,
          },
          uniqueDevices: 0,
          uniqueIPs: 0,
          averageSessionDuration: 0,
        } as SessionStats);

      if (action === 'create') {
        stats.totalSessions++;
        stats.bySource[source]++;
        stats.byDeviceType[deviceType]++;
      } else if (action === 'revoke') {
        stats.revokedSessions++;
      }

      await this.redisService.set(statsKey, stats, { ttl: this.STATS_TTL });
    } catch (error) {
      this.logger.error('Failed to update session statistics', {
        error: error.message,
        userId,
      });
    }
  }

  private async expireSession(sessionId: string): Promise<void> {
    try {
      const session = await this.redisService.get<UserSession>(
        this.getSessionKey(sessionId),
      );
      if (session) {
        await this.endSession(sessionId, 'EXPIRED', session.userId);
      }
    } catch (error) {
      this.logger.error(`Failed to expire session ${sessionId}:`, error);
    }
  }

  private isSessionExpired(session: UserSession): boolean {
    if (!session.expiresAt) return false;
    return new Date() > new Date(session.expiresAt);
  }

  private detectDeviceType(
    userAgent?: string,
    source?: ActivitySource,
  ): DeviceType {
    if (source === ActivitySource.API) {
      return 'API_CLIENT';
    }

    if (!userAgent) {
      return 'UNKNOWN';
    }

    const ua = userAgent.toLowerCase();

    if (
      ua.includes('mobile') ||
      ua.includes('android') ||
      ua.includes('iphone')
    ) {
      return 'MOBILE';
    }

    if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'TABLET';
    }

    if (
      ua.includes('mozilla') ||
      ua.includes('chrome') ||
      ua.includes('safari')
    ) {
      return 'DESKTOP';
    }

    return 'UNKNOWN';
  }

  private parseDeviceInfo(userAgent?: string): DeviceInfo | undefined {
    if (!userAgent) return undefined;

    const ua = userAgent.toLowerCase();
    const info: DeviceInfo = {};

    // Detect OS
    if (ua.includes('windows')) info.os = 'Windows';
    else if (ua.includes('mac')) info.os = 'macOS';
    else if (ua.includes('linux')) info.os = 'Linux';
    else if (ua.includes('android')) info.os = 'Android';
    else if (ua.includes('ios')) info.os = 'iOS';

    // Detect browser
    if (ua.includes('chrome')) info.browser = 'Chrome';
    else if (ua.includes('firefox')) info.browser = 'Firefox';
    else if (ua.includes('safari')) info.browser = 'Safari';
    else if (ua.includes('edge')) info.browser = 'Edge';
    else if (ua.includes('opera')) info.browser = 'Opera';

    // Detect mobile
    info.mobile =
      ua.includes('mobile') || ua.includes('android') || ua.includes('iphone');

    return info;
  }

  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0;
        const v = c == 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      },
    );
  }
}
