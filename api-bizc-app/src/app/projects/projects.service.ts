import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ProjectDto } from './dto/project.dto';
import { ProjectSlimDto } from './dto/project-slim.dto';
import { ProjectAutocompleteDto } from './dto/project-autocomplete.dto';
import { ProjectIdResponseDto } from './dto/project-id-response.dto';
import { BulkProjectIdsResponseDto } from './dto/bulk-project-ids-response.dto';
import { BulkDeleteProjectResponseDto } from './dto/bulk-delete-project-response.dto';
import { PaginatedProjectsResponseDto } from './dto/paginated-projects-response.dto';
import { ProjectNameAvailabilityResponseDto } from './dto/check-project-name.dto';
import { projects } from '../drizzle/schema/projects.schema';
import { eq, and, ilike, sql, gte, lte, desc, asc, inArray } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ProjectStatus } from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class ProjectsService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createProjectDto: CreateProjectDto,
    attachments?: Express.Multer.File[],
  ): Promise<ProjectIdResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check for duplicate project name
      if (createProjectDto.name) {
        const existingProject = await this.db
          .select({ id: projects.id })
          .from(projects)
          .where(
            and(
              eq(projects.businessId, businessId),
              ilike(projects.name, createProjectDto.name),
              eq(projects.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingProject) {
          throw new ConflictException(
            `A project with the name '${createProjectDto.name}' already exists`,
          );
        }
      }

      // Validate date range if both start and end dates are provided
      if (createProjectDto.startDate && createProjectDto.endDate) {
        const startDate = new Date(createProjectDto.startDate);
        const endDate = new Date(createProjectDto.endDate);

        if (startDate >= endDate) {
          throw new BadRequestException(
            'Project start date must be before end date',
          );
        }
      }

      // Create the project
      const [newProject] = await this.db
        .insert(projects)
        .values({
          businessId,
          name: createProjectDto.name,
          description: createProjectDto.description,
          startDate: createProjectDto.startDate
            ? createProjectDto.startDate
            : null,
          endDate: createProjectDto.endDate ? createProjectDto.endDate : null,
          status: createProjectDto.status || ProjectStatus.NOT_STARTED,
          customerId: createProjectDto.customerId,
          tags: createProjectDto.tags,
          notes: createProjectDto.notes,
          hasProjectAddress: createProjectDto.hasProjectAddress || false,
          createdBy: userId,
        })
        .returning();

      // Log the project creation activity
      await this.activityLogService.logCreate(
        newProject.id,
        EntityType.PROJECT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id: newProject.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create project: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    customerId?: string,
    status?: string,
    filters?: string,
    // _joinOperator?: 'and' | 'or',
    // _sort?: string,
  ): Promise<PaginatedProjectsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(projects.businessId, businessId),
      eq(projects.isDeleted, false),
    ];

    // Add name search
    if (name) {
      whereConditions.push(ilike(projects.name, `%${name}%`));
    }

    // Add status filter
    if (status) {
      whereConditions.push(eq(projects.status, status as ProjectStatus));
    }

    // Add customer filter
    if (customerId) {
      whereConditions.push(eq(projects.customerId, customerId));
    }

    // Add dynamic filters
    if (filters) {
      try {
        JSON.parse(filters);
        // Add implementation for dynamic filters as needed
      } catch {
        // Ignore invalid JSON filters
      }
    }

    // Add date range filtering
    if (from) {
      const fromDate = from;
      whereConditions.push(gte(projects.startDate, fromDate));
    }

    if (to) {
      const toDate = to;
      whereConditions.push(lte(projects.endDate, toDate));
    }

    // Get projects with user information
    const result = await this.db
      .select({
        id: projects.id,
        businessId: projects.businessId,
        name: projects.name,
        description: projects.description,
        startDate: projects.startDate,
        endDate: projects.endDate,
        status: projects.status,
        customerId: projects.customerId,
        tags: projects.tags,
        hasProjectAddress: projects.hasProjectAddress,
        attachmentsCount: sql<number>`0`,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(projects.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(desc(projects.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(projects)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return {
      data: result,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    businessId: string | null,
    status?: ProjectStatus,
  ): Promise<ProjectSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(projects.businessId, businessId),
      eq(projects.isDeleted, false),
    ];

    if (status) {
      whereConditions.push(eq(projects.status, status));
    }

    const result = await this.db
      .select({
        id: projects.id,
        name: projects.name,
        status: projects.status,
        startDate: projects.startDate,
        endDate: projects.endDate,
      })
      .from(projects)
      .where(and(...whereConditions))
      .orderBy(desc(projects.createdAt));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result;
  }

  async findById(businessId: string | null, id: string): Promise<ProjectDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: projects.id,
        businessId: projects.businessId,
        name: projects.name,
        description: projects.description,
        startDate: projects.startDate,
        endDate: projects.endDate,
        status: projects.status,
        customerId: projects.customerId,
        tags: projects.tags,
        position: projects.position,
        hasProjectAddress: projects.hasProjectAddress,
        attachments: sql<any[]>`'[]'::json`,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(projects.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(projects.id, id),
          eq(projects.businessId, businessId),
          eq(projects.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result;
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateProjectDto: UpdateProjectDto,
    attachments?: Express.Multer.File[],
  ): Promise<ProjectIdResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if project exists and belongs to the business
      const existingProject = await this.db
        .select()
        .from(projects)
        .where(
          and(
            eq(projects.id, id),
            eq(projects.businessId, businessId),
            eq(projects.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProject) {
        throw new NotFoundException(`Project with ID ${id} not found`);
      }

      // Check for name conflicts if name is being updated
      if (
        updateProjectDto.name &&
        updateProjectDto.name !== existingProject.name
      ) {
        const nameConflict = await this.db
          .select({ id: projects.id })
          .from(projects)
          .where(
            and(
              eq(projects.businessId, businessId),
              ilike(projects.name, updateProjectDto.name),
              sql`${projects.id} != ${id}`,
              eq(projects.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (nameConflict) {
          throw new ConflictException(
            `A project with the name '${updateProjectDto.name}' already exists`,
          );
        }
      }

      // Validate date range if dates are being updated
      if (updateProjectDto.startDate || updateProjectDto.endDate) {
        const startDate = updateProjectDto.startDate
          ? new Date(updateProjectDto.startDate)
          : existingProject.startDate
            ? new Date(existingProject.startDate)
            : null;
        const endDate = updateProjectDto.endDate
          ? new Date(updateProjectDto.endDate)
          : existingProject.endDate
            ? new Date(existingProject.endDate)
            : null;

        if (startDate && endDate && startDate >= endDate) {
          throw new BadRequestException(
            'Project start date must be before end date',
          );
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update fields that are provided
      if (updateProjectDto.name !== undefined)
        updateData.name = updateProjectDto.name;
      // if (updateProjectDto.description !== undefined)
      //   updateData.description = updateProjectDto.description;
      if (updateProjectDto.startDate !== undefined)
        updateData.startDate = updateProjectDto.startDate
          ? updateProjectDto.startDate
          : null;
      if (updateProjectDto.endDate !== undefined)
        updateData.endDate = updateProjectDto.endDate
          ? updateProjectDto.endDate
          : null;
      if (updateProjectDto.status !== undefined)
        updateData.status = updateProjectDto.status;
      if (updateProjectDto.customerId !== undefined)
        updateData.customerId = updateProjectDto.customerId;
      if (updateProjectDto.notes !== undefined)
        updateData.notes = updateProjectDto.notes;
      if (updateProjectDto.description !== undefined)
        updateData.description = updateProjectDto.description;
      if (updateProjectDto.tags !== undefined)
        updateData.tags = updateProjectDto.tags;
      if (updateProjectDto.hasProjectAddress !== undefined)
        updateData.hasProjectAddress = updateProjectDto.hasProjectAddress;

      // Update the project
      await this.db.update(projects).set(updateData).where(eq(projects.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PROJECT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update project: ${error.message}`,
      );
    }
  }

  async delete(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if project exists and belongs to the business
      const existingProject = await this.db
        .select({ name: projects.name })
        .from(projects)
        .where(
          and(
            eq(projects.id, id),
            eq(projects.businessId, businessId),
            eq(projects.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProject) {
        throw new NotFoundException(`Project with ID ${id} not found`);
      }

      // Soft delete the project
      await this.db
        .update(projects)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(projects.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        id,
        EntityType.PROJECT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id,
        message: 'Project deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete project: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    projectIds: string[],
  ): Promise<BulkDeleteProjectResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!projectIds || projectIds.length === 0) {
        throw new BadRequestException('No project IDs provided for deletion');
      }

      // Get all projects that exist and belong to the business
      const existingProjects = await this.db
        .select({
          id: projects.id,
          name: projects.name,
          businessId: projects.businessId,
        })
        .from(projects)
        .where(
          and(
            inArray(projects.id, projectIds),
            eq(projects.businessId, businessId),
            eq(projects.isDeleted, false),
          ),
        );

      if (existingProjects.length === 0) {
        throw new NotFoundException('No valid projects found for deletion');
      }

      const deletedIds: string[] = [];
      const now = new Date();

      // Perform soft delete for each project
      for (const project of existingProjects) {
        await this.db
          .update(projects)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: now,
          })
          .where(eq(projects.id, project.id));

        // Log the project deletion activity
        await this.activityLogService.logDelete(
          project.id,
          EntityType.PROJECT,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );

        deletedIds.push(project.id);
      }

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} projects`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete projects: ${error.message}`,
      );
    }
  }

  async updateProjectStatus(
    userId: string,
    businessId: string | null,
    id: string,
    status: ProjectStatus,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if project exists and belongs to the business
      const existingProject = await this.db
        .select({ status: projects.status })
        .from(projects)
        .where(
          and(
            eq(projects.id, id),
            eq(projects.businessId, businessId),
            eq(projects.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProject) {
        throw new NotFoundException(`Project with ID ${id} not found`);
      }

      // Skip if status is already the same
      if (existingProject.status === status) {
        return { id };
      }

      // Update the project status
      await this.db
        .update(projects)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(projects.id, id));

      // Log the status update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PROJECT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update project status: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    projects: CreateProjectDto[],
  ): Promise<BulkProjectIdsResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!projects || projects.length === 0) {
        throw new BadRequestException('No projects provided for creation');
      }

      const createdIds: string[] = [];

      for (const projectDto of projects) {
        const result = await this.createAndReturnId(
          userId,
          businessId,
          projectDto,
        );
        createdIds.push(result.id);
      }

      return {
        ids: createdIds,
        created: createdIds.length,
        message: `Successfully created ${createdIds.length} projects`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create projects: ${error.message}`,
      );
    }
  }

  async findAutocomplete(
    businessId: string | null,
    search?: string,
    limit = 10,
  ): Promise<ProjectAutocompleteDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(projects.businessId, businessId),
      eq(projects.isDeleted, false),
    ];

    if (search) {
      whereConditions.push(ilike(projects.name, `%${search}%`));
    }

    const result = await this.db
      .select({
        id: projects.id,
        name: projects.name,
        status: projects.status,
      })
      .from(projects)
      .where(and(...whereConditions))
      .orderBy(asc(projects.name))
      .limit(Math.min(limit, 50));

    return result;
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
    excludeId?: string,
  ): Promise<ProjectNameAvailabilityResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const conditions = [
      eq(projects.businessId, businessId),
      ilike(projects.name, name),
      eq(projects.isDeleted, false),
    ];

    if (excludeId) {
      conditions.push(sql`${projects.id} != ${excludeId}`);
    }

    const existingProject = await this.db
      .select({ id: projects.id })
      .from(projects)
      .where(and(...conditions))
      .then((results) => results[0]);

    const available = !existingProject;

    return {
      available,
      message: available
        ? 'Project name is available'
        : `Project name '${name}' is already in use`,
    };
  }

  async getProjectsByStatus(
    businessId: string | null,
    status: ProjectStatus,
    limit = 10,
  ): Promise<ProjectDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: projects.id,
        businessId: projects.businessId,
        name: projects.name,
        description: projects.description,
        startDate: projects.startDate,
        endDate: projects.endDate,
        status: projects.status,
        customerId: projects.customerId,
        tags: projects.tags,
        position: projects.position,
        hasProjectAddress: projects.hasProjectAddress,
        attachments: sql<any[]>`'[]'::json`,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: projects.createdAt,
        updatedAt: projects.updatedAt,
      })
      .from(projects)
      .leftJoin(users, eq(projects.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(projects.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(projects.businessId, businessId),
          eq(projects.status, status),
          eq(projects.isDeleted, false),
        ),
      )
      .orderBy(desc(projects.createdAt))
      .limit(limit);

    return result;
  }
}
