import { ApiProperty } from '@nestjs/swagger';

export class BulkDeleteProjectResponseDto {
  @ApiProperty({
    description: 'Array of successfully deleted project IDs',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '550e8400-e29b-41d4-a716-446655440001',
    ],
  })
  deletedIds: string[];

  @ApiProperty({
    description: 'Number of projects deleted',
    example: 2,
  })
  deleted: number;

  @ApiProperty({
    example: 'Successfully deleted 2 projects',
    description: 'Success message',
  })
  message: string;
}
