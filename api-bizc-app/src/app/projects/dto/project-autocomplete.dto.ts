import { ApiProperty } from '@nestjs/swagger';
import { ProjectStatus } from '../../shared/types/project.enum';

export class ProjectAutocompleteDto {
  @ApiProperty({
    description: 'Unique identifier for the project',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Project name',
    example: 'Website Redesign',
  })
  name: string;

  @ApiProperty({
    description: 'Project status',
    enum: ProjectStatus,
    example: ProjectStatus.NOT_STARTED,
  })
  status: ProjectStatus;
}
