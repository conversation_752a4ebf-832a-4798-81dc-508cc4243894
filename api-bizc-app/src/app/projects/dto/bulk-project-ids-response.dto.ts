import { ApiProperty } from '@nestjs/swagger';

export class BulkProjectIdsResponseDto {
  @ApiProperty({
    description: 'Array of created project IDs',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-446655440000',
      '550e8400-e29b-41d4-a716-446655440001',
    ],
  })
  ids: string[];

  @ApiProperty({
    description: 'Number of projects created',
    example: 2,
  })
  created: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully created 2 projects',
  })
  message: string;
}
