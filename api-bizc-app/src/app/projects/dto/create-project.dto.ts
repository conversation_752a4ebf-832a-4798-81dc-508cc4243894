import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsEnum,
  IsDateString,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ProjectStatus } from '../../shared/types/project.enum';
import { AddressType } from '../../shared/types';

export class CreateProjectAddressDto {
  @ApiProperty({ description: 'Street address' })
  @IsNotEmpty()
  @IsString()
  street: string;

  @ApiProperty({ description: 'City' })
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiProperty({ description: 'State/Province' })
  @IsNotEmpty()
  @IsString()
  state: string;

  @ApiProperty({ description: 'ZIP/Postal code' })
  @IsNotEmpty()
  @IsString()
  zipCode: string;

  @ApiProperty({ description: 'Country' })
  @IsNotEmpty()
  @IsString()
  country: string;

  @ApiPropertyOptional({
    enum: AddressType,
    enumName: 'AddressType',
    description: 'Address type',
    default: AddressType.BUSINESS,
  })
  @IsEnum(AddressType)
  @IsOptional()
  addressType?: AddressType;

  @ApiPropertyOptional({
    description: 'Whether this is the default address',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isDefault?: boolean;
}

export class CreateProjectDto {
  @ApiProperty({ description: 'Project name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  name: string;

  @ApiPropertyOptional({
    description: 'Customer ID associated with this project',
  })
  @IsOptional()
  @IsUUID()
  customerId?: string;

  @ApiPropertyOptional({
    description: 'Project start date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Project end date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    enum: ProjectStatus,
    enumName: 'ProjectStatus',
    description: 'Project status',
    default: ProjectStatus.NOT_STARTED,
  })
  @IsEnum(ProjectStatus, {
    message: 'Status must be a valid project status',
  })
  @IsOptional()
  status?: ProjectStatus;

  @ApiPropertyOptional({
    description: 'Project notes',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Project description',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Project tags (comma-separated)',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({
    description: 'Whether this project has specific addresses',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  hasProjectAddress?: boolean;

  @ApiPropertyOptional({
    description: 'Billing address details (only if hasProjectAddress is true)',
    type: CreateProjectAddressDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateProjectAddressDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  billingAddress?: CreateProjectAddressDto;

  @ApiPropertyOptional({
    description: 'Shipping address details (only if hasProjectAddress is true)',
    type: CreateProjectAddressDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateProjectAddressDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  shippingAddress?: CreateProjectAddressDto;
}
