import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsEnum,
  IsDateString,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ProjectStatus } from '../../shared/types/project.enum';
import { AddressType } from '../../shared/types';

export class UpdateProjectAddressDto {
  @ApiPropertyOptional({ description: 'Street address' })
  @IsOptional()
  @IsString()
  street?: string;

  @ApiPropertyOptional({ description: 'City' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({ description: 'State/Province' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({ description: 'ZIP/Postal code' })
  @IsOptional()
  @IsString()
  zipCode?: string;

  @ApiPropertyOptional({ description: 'Country' })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({
    enum: AddressType,
    enumName: 'AddressType',
    description: 'Address type',
  })
  @IsEnum(AddressType)
  @IsOptional()
  addressType?: AddressType;

  @ApiPropertyOptional({
    description: 'Whether this is the default address',
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isDefault?: boolean;
}

export class UpdateProjectDto {
  @ApiPropertyOptional({ description: 'Project name', maxLength: 191 })
  @IsOptional()
  @IsString()
  @MaxLength(191)
  name?: string;

  @ApiPropertyOptional({
    description: 'Customer ID associated with this project',
  })
  @IsOptional()
  @IsUUID()
  customerId?: string;

  @ApiPropertyOptional({
    description: 'Project start date (YYYY-MM-DD)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'Project end date (YYYY-MM-DD)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({
    enum: ProjectStatus,
    enumName: 'ProjectStatus',
    description: 'Project status',
  })
  @IsEnum(ProjectStatus, {
    message: 'Status must be a valid project status',
  })
  @IsOptional()
  status?: ProjectStatus;

  @ApiPropertyOptional({
    description: 'Project notes',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Project description',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Project tags (comma-separated)',
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({
    description: 'Whether this project has specific addresses',
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  hasProjectAddress?: boolean;

  @ApiPropertyOptional({
    description: 'Billing address details (only if hasProjectAddress is true)',
    type: UpdateProjectAddressDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateProjectAddressDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  billingAddress?: UpdateProjectAddressDto;

  @ApiPropertyOptional({
    description: 'Shipping address details (only if hasProjectAddress is true)',
    type: UpdateProjectAddressDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateProjectAddressDto)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  })
  shippingAddress?: UpdateProjectAddressDto;
}
