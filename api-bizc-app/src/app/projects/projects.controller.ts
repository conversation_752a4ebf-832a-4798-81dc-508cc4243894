import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  // UploadedFiles,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { ProjectDto } from './dto/project.dto';
import { ProjectSlimDto } from './dto/project-slim.dto';
import { ProjectAutocompleteDto } from './dto/project-autocomplete.dto';
import { ProjectIdResponseDto } from './dto/project-id-response.dto';
import { BulkProjectIdsResponseDto } from './dto/bulk-project-ids-response.dto';
import { BulkCreateProjectDto } from './dto/bulk-create-project.dto';
import { BulkDeleteProjectDto } from './dto/bulk-delete-project.dto';
import { BulkDeleteProjectResponseDto } from './dto/bulk-delete-project-response.dto';
import { PaginatedProjectsResponseDto } from './dto/paginated-projects-response.dto';
import {
  CheckProjectNameDto,
  ProjectNameAvailabilityResponseDto,
} from './dto/check-project-name.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('projects')
@Controller('projects')
@UseGuards(PermissionsGuard)
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new project with optional attachments',
  })
  @ApiResponse({
    status: 201,
    description: 'Project created successfully',
    type: ProjectIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Project name already exists',
  })
  create(
    @Request() req,
    @Body() createProjectDto: CreateProjectDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
  ): Promise<ProjectIdResponseDto> {
    return this.projectsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createProjectDto,
      attachments,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_CREATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk create projects with optional attachments',
  })
  @ApiResponse({
    status: 201,
    description: 'Projects created successfully',
    type: BulkProjectIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Project names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateProjectDto: BulkCreateProjectDto,
    // @UploadedFiles() _attachments?: Express.Multer.File[],
  ): Promise<BulkProjectIdsResponseDto> {
    return this.projectsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateProjectDto.projects,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_READ)
  @ApiOperation({
    summary: 'Get all projects for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date filter (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by project name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'customerId',
    description: 'Filter by customer ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by project status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'JSON string of dynamic filters',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (field:asc/desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated list of projects',
    type: PaginatedProjectsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('customerId') customerId?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedProjectsResponseDto> {
    return this.projectsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      name,
      customerId,
      status,
      filters,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_READ)
  @ApiOperation({
    summary: 'Get all projects in slim format (for dropdowns/selects)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns list of projects in slim format',
    type: [ProjectSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<ProjectSlimDto[]> {
    return this.projectsService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('autocomplete')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_READ)
  @ApiOperation({
    summary: 'Search projects for autocomplete',
    description:
      'Returns projects matching the search query for autocomplete functionality',
  })
  @ApiQuery({
    name: 'search',
    description: 'Search term to filter projects by name',
    required: false,
    type: String,
    example: 'website',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of results to return (default: 10, max: 50)',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Projects returned successfully',
    type: [ProjectAutocompleteDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAutocomplete(
    @Request() req,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
  ): Promise<ProjectAutocompleteDto[]> {
    return this.projectsService.findAutocomplete(
      req.user.activeBusinessId,
      search,
      limit ? Math.min(parseInt(limit.toString()), 50) : 10,
    );
  }

  @Post('check-name')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_READ)
  @ApiOperation({ summary: 'Check if project name is available' })
  @ApiBody({
    description: 'Project name to check',
    type: CheckProjectNameDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns name availability status',
    type: ProjectNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkNameAvailability(
    @Request() req,
    @Body() checkProjectNameDto: CheckProjectNameDto,
  ): Promise<ProjectNameAvailabilityResponseDto> {
    return this.projectsService.checkNameAvailability(
      req.user.activeBusinessId,
      checkProjectNameDto.name,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_READ)
  @ApiOperation({ summary: 'Get a project by ID' })
  @ApiParam({
    name: 'id',
    description: 'Project ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the project with full details',
    type: ProjectDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this project',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<ProjectDto> {
    return this.projectsService.findById(req.user.activeBusinessId, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_UPDATE)
  @UseInterceptors(FilesInterceptor('attachments'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update a project' })
  @ApiParam({
    name: 'id',
    description: 'Project ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Project updated successfully',
    type: ProjectIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this project',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Project name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @UploadedFiles() attachments?: Express.Multer.File[],
  ): Promise<ProjectIdResponseDto> {
    return this.projectsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateProjectDto,
      attachments,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_DELETE)
  @ApiOperation({ summary: 'Bulk delete projects' })
  @ApiBody({
    description: 'Array of project IDs to delete',
    type: BulkDeleteProjectDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Projects deleted successfully',
    type: BulkDeleteProjectResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteProjectDto: BulkDeleteProjectDto,
  ): Promise<BulkDeleteProjectResponseDto> {
    return this.projectsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteProjectDto.ids,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PROJECT_DELETE)
  @ApiOperation({ summary: 'Delete a project' })
  @ApiParam({
    name: 'id',
    description: 'Project ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Project deleted successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this project',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<{ id: string; message: string }> {
    return this.projectsService.delete(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }
}
