import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateExpenseDto } from './dto/create-expense.dto';
import { UpdateExpenseDto } from './dto/update-expense.dto';
import { ExpenseDto } from './dto/expense.dto';
import { ExpenseSlimDto } from './dto/expense-slim.dto';
import { ExpenseListDto } from './dto/expense-list.dto';
import {
  expenses,
  expenseCategoryLineItems,
  expenseProductServiceLineItems,
  AmountType,
  ExpenseStatus,
  PayeeType,
} from '../drizzle/schema/expenses.schema';
import { media } from '../drizzle/schema/media.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  and,
  eq,
  desc,
  asc,
  count,
  ilike,
  or,
  gte,
  lte,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  ActivitySource,
  ActivityType,
  EntityType,
  ExecutionStrategy,
} from '../shared/types';
import { MediaService } from '../media/media.service';
import { ActivityMetadata } from '@app/shared/types/activity-metadata.type';

@Injectable()
export class ExpensesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createExpenseDto: CreateExpenseDto,
    attachmentFiles?: Express.Multer.File[],
  ): Promise<ExpenseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    try {
      // Check for duplicate reference number if provided
      if (createExpenseDto.referenceNumber) {
        const existingExpense = await this.db
          .select()
          .from(expenses)
          .where(
            and(
              eq(expenses.businessId, businessId),
              eq(expenses.referenceNumber, createExpenseDto.referenceNumber),
              eq(expenses.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingExpense) {
          throw new ConflictException(
            `Expense with reference number "${createExpenseDto.referenceNumber}" already exists`,
          );
        }
      }

      // Create the expense in a transaction
      const newExpense = await this.db.transaction(async (tx) => {
        // Create the main expense record
        const [expense] = await tx
          .insert(expenses)
          .values({
            businessId,
            payeeType: createExpenseDto.payeeType,
            payeeId: createExpenseDto.payeeId,
            paymentAccountId: createExpenseDto.paymentAccountId,
            paymentDate: createExpenseDto.paymentDate,
            paymentMethodId: createExpenseDto.paymentMethodId,
            referenceNumber: createExpenseDto.referenceNumber,
            amountType:
              createExpenseDto.amountType ?? AmountType.EXCLUSIVE_OF_TAX,
            subtotal: createExpenseDto.subtotal.toString(),
            total: createExpenseDto.total.toString(),
            memo: createExpenseDto.memo,
            status: createExpenseDto.status ?? ExpenseStatus.DRAFT,
            createdBy: userId,
          })
          .returning();

        // Handle category line items
        if (createExpenseDto.expenseCategoryLineItems?.length) {
          await tx.insert(expenseCategoryLineItems).values(
            createExpenseDto.expenseCategoryLineItems.map((item, index) => ({
              businessId,
              expenseId: expense.id,
              categoryId: item.categoryId,
              description: item.description,
              amount: item.amount.toString(),
              taxId: item.taxId,
              lineOrder: item.lineOrder ?? index,
              createdBy: userId,
            })),
          );
        }

        // Handle product/service line items
        if (createExpenseDto.expenseProductServiceLineItems?.length) {
          await tx.insert(expenseProductServiceLineItems).values(
            createExpenseDto.expenseProductServiceLineItems.map(
              (item, index) => ({
                businessId,
                expenseId: expense.id,
                itemType: item.itemType,
                itemId: item.itemId,
                description: item.description,
                quantity: item.quantity.toString(),
                rate: item.rate.toString(),
                amount: item.amount.toString(),
                taxId: item.taxId,
                lineOrder: item.lineOrder ?? index,
                createdBy: userId,
              }),
            ),
          );
        }

        return expense;
      });

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        const filesToUpload = createExpenseDto.attachmentIndexes
          ? createExpenseDto.attachmentIndexes
              .map((index) => attachmentFiles[index])
              .filter(Boolean)
          : attachmentFiles;

        if (filesToUpload.length > 0) {
          await this.mediaService.uploadMultipleMediaWithReference(
            filesToUpload,
            'expenses',
            businessId,
            userId,
            newExpense.id,
          );
        }
      }

      // Log the expense creation activity
      await this.activityLogService.logCreate(
        newExpense.id,
        EntityType.EXPENSE,
        userId,
        businessId,
        {
          reason: `Expense "${createExpenseDto.referenceNumber || newExpense.id}" was created`,
        },
      );

      // Return the created expense with full details
      return await this.findOne(userId, businessId, newExpense.id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create expense');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    from?: string,
    to?: string,
    payeeType?: string,
    status?: string,
    referenceNumber?: string,
    filters?: string,
    joinOperator: 'and' | 'or' = 'and',
    sort?: string,
  ): Promise<{
    data: ExpenseListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;
    const conditions = [
      eq(expenses.businessId, businessId),
      eq(expenses.isDeleted, false),
    ];

    // Add date range filters
    if (from) {
      conditions.push(gte(expenses.paymentDate, from));
    }
    if (to) {
      conditions.push(lte(expenses.paymentDate, to));
    }

    // Add payee type filter
    if (
      payeeType &&
      Object.values(PayeeType).includes(payeeType as PayeeType)
    ) {
      conditions.push(eq(expenses.payeeType, payeeType as PayeeType));
    }

    // Add status filter
    if (
      status &&
      Object.values(ExpenseStatus).includes(status as ExpenseStatus)
    ) {
      conditions.push(eq(expenses.status, status as ExpenseStatus));
    }

    // Add reference number filter
    if (referenceNumber) {
      conditions.push(ilike(expenses.referenceNumber, `%${referenceNumber}%`));
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = parsedFilters
          .map((filter: any) => {
            switch (filter.field) {
              case 'payeeType':
                return eq(expenses.payeeType, filter.value);
              case 'status':
                return eq(expenses.status, filter.value);
              case 'referenceNumber':
                return ilike(expenses.referenceNumber, `%${filter.value}%`);
              case 'total':
                switch (filter.operator) {
                  case 'gte':
                    return gte(expenses.total, filter.value);
                  case 'lte':
                    return lte(expenses.total, filter.value);
                  case 'eq':
                    return eq(expenses.total, filter.value);
                  default:
                    return eq(expenses.total, filter.value);
                }
              default:
                return null;
            }
          })
          .filter(Boolean);

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            conditions.push(or(...filterConditions));
          } else {
            conditions.push(...filterConditions);
          }
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Handle sorting
    let orderBy = desc(expenses.createdAt);
    if (sort) {
      const [field, direction] = sort.split(':');
      const isAsc = direction === 'asc';

      switch (field) {
        case 'paymentDate':
          orderBy = isAsc
            ? asc(expenses.paymentDate)
            : desc(expenses.paymentDate);
          break;
        case 'total':
          orderBy = isAsc ? asc(expenses.total) : desc(expenses.total);
          break;
        case 'status':
          orderBy = isAsc ? asc(expenses.status) : desc(expenses.status);
          break;
        case 'referenceNumber':
          orderBy = isAsc
            ? asc(expenses.referenceNumber)
            : desc(expenses.referenceNumber);
          break;
        case 'createdAt':
          orderBy = isAsc ? asc(expenses.createdAt) : desc(expenses.createdAt);
          break;
        default:
          orderBy = desc(expenses.createdAt);
      }
    }

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(expenses)
      .where(and(...conditions));

    // Get expenses with user information
    const expenseResults = await this.db
      .select({
        id: expenses.id,
        payeeType: expenses.payeeType,
        payeeId: expenses.payeeId,
        referenceNumber: expenses.referenceNumber,
        paymentDate: expenses.paymentDate,
        amountType: expenses.amountType,
        total: expenses.total,
        status: expenses.status,
        createdAt: expenses.createdAt,
        updatedAt: expenses.updatedAt,
        createdByName: users.name,
      })
      .from(expenses)
      .leftJoin(users, eq(expenses.createdBy, users.id))
      .where(and(...conditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: expenseResults.map((expense) => ({
        id: expense.id,
        payeeType: expense.payeeType,
        payeeId: expense.payeeId,
        referenceNumber: expense.referenceNumber,
        paymentDate: expense.paymentDate,
        amountType: expense.amountType,
        total: parseFloat(expense.total),
        status: expense.status,
        createdBy: expense.createdByName || 'Unknown',
        createdAt: expense.createdAt,
        updatedAt: expense.updatedAt,
      })),
      meta: {
        total,
        page,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ExpenseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get the expense with user information
    const expenseResult = await this.db
      .select({
        id: expenses.id,
        businessId: expenses.businessId,
        payeeType: expenses.payeeType,
        payeeId: expenses.payeeId,
        paymentAccountId: expenses.paymentAccountId,
        paymentDate: expenses.paymentDate,
        paymentMethodId: expenses.paymentMethodId,
        referenceNumber: expenses.referenceNumber,
        amountType: expenses.amountType,
        subtotal: expenses.subtotal,
        total: expenses.total,
        memo: expenses.memo,
        status: expenses.status,
        createdAt: expenses.createdAt,
        updatedAt: expenses.updatedAt,
        createdByName: users.name,
        updatedByName: users.name,
      })
      .from(expenses)
      .leftJoin(users, eq(expenses.createdBy, users.id))
      .where(
        and(
          eq(expenses.id, id),
          eq(expenses.businessId, businessId),
          eq(expenses.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!expenseResult) {
      throw new NotFoundException('Expense not found');
    }

    // Get category line items
    const categoryLineItems = await this.db
      .select()
      .from(expenseCategoryLineItems)
      .where(
        and(
          eq(expenseCategoryLineItems.expenseId, id),
          eq(expenseCategoryLineItems.isDeleted, false),
        ),
      )
      .orderBy(asc(expenseCategoryLineItems.lineOrder));

    // Get product/service line items
    const productServiceLineItems = await this.db
      .select()
      .from(expenseProductServiceLineItems)
      .where(
        and(
          eq(expenseProductServiceLineItems.expenseId, id),
          eq(expenseProductServiceLineItems.isDeleted, false),
        ),
      )
      .orderBy(asc(expenseProductServiceLineItems.lineOrder));

    // Get attachments
    const attachmentResults = await this.db
      .select()
      .from(media)
      .where(and(eq(media.referenceId, id), eq(media.businessId, businessId)));

    // Generate signed URLs for attachments
    const attachments: string[] = [];
    for (const attachment of attachmentResults) {
      try {
        const signedUrl = await this.mediaService.generateSignedUrlForMedia(
          attachment.id,
          businessId,
          'expenses',
          60, // 60 minutes expiration
        );
        attachments.push(signedUrl);
      } catch (error) {
        console.error('Error generating signed URL for attachment:', error);
      }
    }

    return {
      id: expenseResult.id,
      businessId: expenseResult.businessId,
      payeeType: expenseResult.payeeType,
      payeeId: expenseResult.payeeId,
      paymentAccountId: expenseResult.paymentAccountId,
      paymentDate: expenseResult.paymentDate,
      paymentMethodId: expenseResult.paymentMethodId,
      referenceNumber: expenseResult.referenceNumber,
      amountType: expenseResult.amountType,
      subtotal: parseFloat(expenseResult.subtotal),
      total: parseFloat(expenseResult.total),
      memo: expenseResult.memo,
      status: expenseResult.status,
      attachments,
      expenseCategoryLineItems: categoryLineItems.map((item) => ({
        id: item.id,
        expenseId: item.expenseId,
        categoryId: item.categoryId,
        description: item.description,
        amount: parseFloat(item.amount),
        taxId: item.taxId,
        lineOrder: item.lineOrder,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      })),
      expenseProductServiceLineItems: productServiceLineItems.map((item) => ({
        id: item.id,
        expenseId: item.expenseId,
        itemType: item.itemType,
        itemId: item.itemId,
        description: item.description,
        quantity: parseFloat(item.quantity),
        rate: parseFloat(item.rate),
        amount: parseFloat(item.amount),
        taxId: item.taxId,
        lineOrder: item.lineOrder,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      })),
      createdBy: expenseResult.createdByName || 'Unknown',
      updatedBy: expenseResult.updatedByName || null,
      createdAt: expenseResult.createdAt,
      updatedAt: expenseResult.updatedAt,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ExpenseSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all expenses with only essential fields
    const expenseResults = await this.db
      .select({
        id: expenses.id,
        payeeType: expenses.payeeType,
        payeeId: expenses.payeeId,
        referenceNumber: expenses.referenceNumber,
        total: expenses.total,
        status: expenses.status,
      })
      .from(expenses)
      .where(
        and(
          eq(expenses.isDeleted, false),
          eq(expenses.status, ExpenseStatus.APPROVED),
          eq(expenses.businessId, businessId),
        ),
      )
      .orderBy(desc(expenses.createdAt));

    return expenseResults.map((expense) => ({
      id: expense.id,
      payeeType: expense.payeeType,
      payeeId: expense.payeeId,
      referenceNumber: expense.referenceNumber,
      total: parseFloat(expense.total),
      status: expense.status,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createExpenseDto: CreateExpenseDto,
    attachmentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    const expense = await this.create(
      userId,
      businessId,
      createExpenseDto,
      attachmentFiles,
    );
    return { id: expense.id };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateExpenseDto: UpdateExpenseDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<ExpenseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if expense exists
    const existingExpense = await this.db
      .select()
      .from(expenses)
      .where(
        and(
          eq(expenses.id, id),
          eq(expenses.businessId, businessId),
          eq(expenses.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingExpense) {
      throw new NotFoundException('Expense not found');
    }

    // Check for duplicate reference number if provided and different from current
    if (
      updateExpenseDto.referenceNumber &&
      updateExpenseDto.referenceNumber !== existingExpense.referenceNumber
    ) {
      const duplicateExpense = await this.db
        .select()
        .from(expenses)
        .where(
          and(
            eq(expenses.businessId, businessId),
            eq(expenses.referenceNumber, updateExpenseDto.referenceNumber),
            eq(expenses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (duplicateExpense) {
        throw new ConflictException(
          `Expense with reference number "${updateExpenseDto.referenceNumber}" already exists`,
        );
      }
    }

    try {
      // Update the expense in a transaction
      await this.db.transaction(async (tx) => {
        // Update the main expense record
        const [expense] = await tx
          .update(expenses)
          .set({
            payeeType: updateExpenseDto.payeeType ?? existingExpense.payeeType,
            payeeId: updateExpenseDto.payeeId ?? existingExpense.payeeId,
            paymentAccountId:
              updateExpenseDto.paymentAccountId ??
              existingExpense.paymentAccountId,
            paymentDate:
              updateExpenseDto.paymentDate ?? existingExpense.paymentDate,
            paymentMethodId:
              updateExpenseDto.paymentMethodId ??
              existingExpense.paymentMethodId,
            referenceNumber:
              updateExpenseDto.referenceNumber ??
              existingExpense.referenceNumber,
            amountType:
              updateExpenseDto.amountType ?? existingExpense.amountType,
            subtotal:
              updateExpenseDto.subtotal?.toString() ?? existingExpense.subtotal,
            total: updateExpenseDto.total?.toString() ?? existingExpense.total,
            memo: updateExpenseDto.memo ?? existingExpense.memo,
            status: updateExpenseDto.status ?? existingExpense.status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(expenses.id, id))
          .returning();

        // Handle category line items update
        if (updateExpenseDto.expenseCategoryLineItems !== undefined) {
          // Delete existing category line items
          await tx
            .update(expenseCategoryLineItems)
            .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
            .where(eq(expenseCategoryLineItems.expenseId, id));

          // Insert new category line items
          if (updateExpenseDto.expenseCategoryLineItems.length > 0) {
            await tx.insert(expenseCategoryLineItems).values(
              updateExpenseDto.expenseCategoryLineItems.map((item, index) => ({
                businessId,
                expenseId: id,
                categoryId: item.categoryId,
                description: item.description,
                amount: item.amount.toString(),
                taxId: item.taxId,
                lineOrder: item.lineOrder ?? index,
                createdBy: userId,
              })),
            );
          }
        }

        // Handle product/service line items update
        if (updateExpenseDto.expenseProductServiceLineItems !== undefined) {
          // Delete existing product/service line items
          await tx
            .update(expenseProductServiceLineItems)
            .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
            .where(eq(expenseProductServiceLineItems.expenseId, id));

          // Insert new product/service line items
          if (updateExpenseDto.expenseProductServiceLineItems.length > 0) {
            await tx.insert(expenseProductServiceLineItems).values(
              updateExpenseDto.expenseProductServiceLineItems.map(
                (item, index) => ({
                  businessId,
                  expenseId: id,
                  itemType: item.itemType,
                  itemId: item.itemId,
                  description: item.description,
                  quantity: item.quantity.toString(),
                  rate: item.rate.toString(),
                  amount: item.amount.toString(),
                  taxId: item.taxId,
                  lineOrder: item.lineOrder ?? index,
                  createdBy: userId,
                }),
              ),
            );
          }
        }

        return expense;
      });

      // Handle file attachments if provided
      if (attachmentFiles && attachmentFiles.length > 0) {
        const filesToUpload = updateExpenseDto.attachmentIndexes
          ? updateExpenseDto.attachmentIndexes
              .map((index) => attachmentFiles[index])
              .filter(Boolean)
          : attachmentFiles;

        if (filesToUpload.length > 0) {
          await this.mediaService.updateMediaForReference(
            id,
            filesToUpload,
            'expenses',
            businessId,
            userId,
          );
        }
      }

      // Log the expense update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.EXPENSE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      // Return the updated expense with full details
      return await this.findOne(userId, businessId, id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update expense');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateExpenseDto: UpdateExpenseDto,
    attachmentFiles?: Express.Multer.File[],
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateExpenseDto,
      attachmentFiles,
    );
    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if expense exists
    const existingExpense = await this.db
      .select()
      .from(expenses)
      .where(
        and(
          eq(expenses.id, id),
          eq(expenses.businessId, businessId),
          eq(expenses.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingExpense) {
      throw new NotFoundException('Expense not found');
    }

    try {
      // Soft delete the expense and related records in a transaction
      await this.db.transaction(async (tx) => {
        // Soft delete the main expense
        await tx
          .update(expenses)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(expenses.id, id));

        // Soft delete category line items
        await tx
          .update(expenseCategoryLineItems)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(expenseCategoryLineItems.expenseId, id));

        // Soft delete product/service line items
        await tx
          .update(expenseProductServiceLineItems)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(expenseProductServiceLineItems.expenseId, id));

        // Delete associated media
        await tx
          .delete(media)
          .where(
            and(eq(media.referenceId, id), eq(media.businessId, businessId)),
          );
      });

      // Log the expense deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.EXPENSE,
        userId,
        businessId,
        {
          reason: `Expense "${existingExpense.referenceNumber || id}" was deleted`,
        },
      );

      return {
        success: true,
        message: 'Expense deleted successfully',
      };
    } catch {
      throw new BadRequestException('Failed to delete expense');
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    expenseIds: string[],
  ): Promise<{ success: boolean; message: string; deletedCount: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!expenseIds || expenseIds.length === 0) {
      throw new BadRequestException('No expense IDs provided');
    }

    // Check which expenses exist
    const existingExpenses = await this.db
      .select({ id: expenses.id, referenceNumber: expenses.referenceNumber })
      .from(expenses)
      .where(
        and(
          inArray(expenses.id, expenseIds),
          eq(expenses.businessId, businessId),
          eq(expenses.isDeleted, false),
        ),
      );

    if (existingExpenses.length === 0) {
      throw new NotFoundException('No expenses found to delete');
    }

    const existingExpenseIds = existingExpenses.map((e) => e.id);

    try {
      // Bulk soft delete in a transaction
      await this.db.transaction(async (tx) => {
        // Soft delete the main expenses
        await tx
          .update(expenses)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(expenses.id, existingExpenseIds));

        // Soft delete category line items
        await tx
          .update(expenseCategoryLineItems)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(
            inArray(expenseCategoryLineItems.expenseId, existingExpenseIds),
          );

        // Soft delete product/service line items
        await tx
          .update(expenseProductServiceLineItems)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(
            inArray(
              expenseProductServiceLineItems.expenseId,
              existingExpenseIds,
            ),
          );

        // Delete associated media
        await tx
          .delete(media)
          .where(
            and(
              inArray(media.referenceId, existingExpenseIds),
              eq(media.businessId, businessId),
            ),
          );
      });

      // Log the bulk deletion activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.EXPENSE,
        existingExpenseIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { expenseIds: existingExpenseIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
        },
      );

      return {
        success: true,
        message: `Successfully deleted ${existingExpenses.length} expenses`,
        deletedCount: existingExpenses.length,
      };
    } catch {
      throw new BadRequestException('Failed to bulk delete expenses');
    }
  }
}
