import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateEmployeeSalaryDto } from './dto/create-employee-salary.dto';
import { UpdateEmployeeSalaryDto } from './dto/update-employee-salary.dto';
import { EmployeeSalaryDto } from './dto/employee-salary.dto';
import { EmployeeSalarySlimDto } from './dto/employee-salary-slim.dto';
import { EmployeeSalaryListDto } from './dto/employee-salary-list.dto';
import {
  employeeSalaries,
  EmployeeSalaryStatus,
} from '../drizzle/schema/employee-salary.schema';
import { employeeSalaryAllowances } from '../drizzle/schema/employee-salary-allowances.schema';
import { employeeSalaryDeductions } from '../drizzle/schema/employee-salary-deductions.schema';
import { allowanceTypes } from '../drizzle/schema/allowance-types.schema';
import { deductionTypes } from '../drizzle/schema/deduction-types.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { bankAccounts } from '../drizzle/schema/bank-accounts.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class EmployeeSalariesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createEmployeeSalaryDto: CreateEmployeeSalaryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if employee salary already exists for this employee
      const existingEmployeeSalary = await this.db
        .select()
        .from(employeeSalaries)
        .where(
          and(
            eq(employeeSalaries.businessId, businessId),
            eq(employeeSalaries.employeeId, createEmployeeSalaryDto.employeeId),
            eq(employeeSalaries.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingEmployeeSalary) {
        throw new ConflictException(
          'Employee salary record already exists for this employee',
        );
      }

      // Verify employee exists and belongs to the business
      const employee = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createEmployeeSalaryDto.employeeId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!employee) {
        throw new BadRequestException('Employee not found');
      }

      // Verify bank account exists and belongs to the business
      const bankAccount = await this.db
        .select()
        .from(bankAccounts)
        .where(
          and(
            eq(bankAccounts.id, createEmployeeSalaryDto.bankAccountId),
            eq(bankAccounts.businessId, businessId),
            eq(bankAccounts.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!bankAccount) {
        throw new BadRequestException('Bank account not found');
      }

      // Create employee salary record with allowances and deductions in a transaction
      const newEmployeeSalary = await this.db.transaction(async (tx) => {
        // Create the main salary record
        const [salary] = await tx
          .insert(employeeSalaries)
          .values({
            businessId,
            employeeId: createEmployeeSalaryDto.employeeId,
            basicSalary: createEmployeeSalaryDto.basicSalary.toString(),
            bankAccountId: createEmployeeSalaryDto.bankAccountId,
            status:
              createEmployeeSalaryDto.status ?? EmployeeSalaryStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        // Create allowances if provided
        if (
          createEmployeeSalaryDto.allowances &&
          createEmployeeSalaryDto.allowances.length > 0
        ) {
          for (const allowanceDto of createEmployeeSalaryDto.allowances) {
            // Verify allowance type exists
            const allowanceType = await tx
              .select()
              .from(allowanceTypes)
              .where(
                and(
                  eq(allowanceTypes.id, allowanceDto.allowanceTypeId),
                  eq(allowanceTypes.businessId, businessId),
                  eq(allowanceTypes.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!allowanceType) {
              throw new BadRequestException(
                `Allowance type ${allowanceDto.allowanceTypeId} not found`,
              );
            }

            await tx.insert(employeeSalaryAllowances).values({
              businessId,
              employeeSalaryId: salary.id,
              allowanceTypeId: allowanceDto.allowanceTypeId,
              amount: allowanceDto.amount.toString(),
              notes: allowanceDto.notes,
              createdBy: userId,
            });
          }
        }

        // Create deductions if provided
        if (
          createEmployeeSalaryDto.deductions &&
          createEmployeeSalaryDto.deductions.length > 0
        ) {
          for (const deductionDto of createEmployeeSalaryDto.deductions) {
            // Verify deduction type exists
            const deductionType = await tx
              .select()
              .from(deductionTypes)
              .where(
                and(
                  eq(deductionTypes.id, deductionDto.deductionTypeId),
                  eq(deductionTypes.businessId, businessId),
                  eq(deductionTypes.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!deductionType) {
              throw new BadRequestException(
                `Deduction type ${deductionDto.deductionTypeId} not found`,
              );
            }

            await tx.insert(employeeSalaryDeductions).values({
              businessId,
              employeeSalaryId: salary.id,
              deductionTypeId: deductionDto.deductionTypeId,
              amount: deductionDto.amount.toString(),
              notes: deductionDto.notes,
              createdBy: userId,
            });
          }
        }

        return salary;
      });

      // Log the employee salary creation activity
      await this.activityLogService.logCreate(
        newEmployeeSalary.id,
        EntityType.STAFF,
        userId,
        businessId,
        {
          reason: `Employee salary record was created for ${employee.displayName}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newEmployeeSalary.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to create employee salary: ' + error.message,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createEmployeeSalaryDto: CreateEmployeeSalaryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createEmployeeSalaryDto, metadata);
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    employeeId?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: EmployeeSalaryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(employeeSalaries.isDeleted, false),
      eq(employeeSalaries.businessId, businessId),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(gte(employeeSalaries.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(employeeSalaries.createdAt, new Date(to)));
    }

    // Add employee filter
    if (employeeId) {
      whereConditions.push(eq(employeeSalaries.employeeId, employeeId));
    }

    // Add status filter
    if (status) {
      whereConditions.push(
        eq(employeeSalaries.status, status as EmployeeSalaryStatus),
      );
    }

    // Parse and add advanced filters
    let parsedFilters: any[] = [];
    if (filters) {
      try {
        parsedFilters = JSON.parse(filters);
      } catch {
        throw new BadRequestException('Invalid filters format');
      }
    }

    // Build advanced filter conditions
    const advancedFilterConditions = parsedFilters.map((filter) => {
      const { id, value, operator } = filter;

      switch (id) {
        case 'employeeDisplayName':
          return operator === 'iLike'
            ? ilike(staffMembers.displayName, `%${value}%`)
            : eq(staffMembers.displayName, value);
        case 'basicSalary':
          return operator === 'gte'
            ? gte(employeeSalaries.basicSalary, value)
            : operator === 'lte'
              ? lte(employeeSalaries.basicSalary, value)
              : eq(employeeSalaries.basicSalary, value);
        case 'status':
          return eq(employeeSalaries.status, value);
        default:
          return sql`1=1`; // No-op condition
      }
    });

    // Combine conditions based on join operator
    const finalConditions = [...whereConditions];
    if (advancedFilterConditions.length > 0) {
      const combinedAdvancedConditions =
        joinOperator === 'or'
          ? or(...advancedFilterConditions)
          : and(...advancedFilterConditions);
      finalConditions.push(combinedAdvancedConditions);
    }

    // Parse sort parameter
    let orderBy = desc(employeeSalaries.createdAt); // Default sort
    if (sort) {
      try {
        const sortArray = JSON.parse(sort);
        if (sortArray.length > 0) {
          const { id, desc: isDesc } = sortArray[0];
          const direction = isDesc ? desc : asc;

          switch (id) {
            case 'employeeDisplayName':
              orderBy = direction(staffMembers.displayName);
              break;
            case 'basicSalary':
              orderBy = direction(employeeSalaries.basicSalary);
              break;
            case 'status':
              orderBy = direction(employeeSalaries.status);
              break;
            case 'createdAt':
              orderBy = direction(employeeSalaries.createdAt);
              break;
            default:
              orderBy = desc(employeeSalaries.createdAt);
          }
        }
      } catch {
        // Use default sort if parsing fails
        orderBy = desc(employeeSalaries.createdAt);
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(employeeSalaries)
      .leftJoin(staffMembers, eq(employeeSalaries.employeeId, staffMembers.id))
      .leftJoin(
        bankAccounts,
        eq(employeeSalaries.bankAccountId, bankAccounts.id),
      )
      .where(and(...finalConditions));

    const total = Number(totalResult[0]?.count || 0);
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with optimized query
    const results = await this.db
      .select({
        id: employeeSalaries.id,
        businessId: employeeSalaries.businessId,
        employeeId: employeeSalaries.employeeId,
        employeeDisplayName: staffMembers.displayName,
        employeeEmail: staffMembers.email,
        employeePhone: staffMembers.phone,
        basicSalary: employeeSalaries.basicSalary,
        bankAccountId: employeeSalaries.bankAccountId,
        bankAccountHolderName: bankAccounts.accountHolderName,
        bankName: bankAccounts.bankName,
        bankAccountNumber: bankAccounts.accountNumber,
        status: employeeSalaries.status,
        createdAt: employeeSalaries.createdAt,
        updatedAt: employeeSalaries.updatedAt,
      })
      .from(employeeSalaries)
      .leftJoin(staffMembers, eq(employeeSalaries.employeeId, staffMembers.id))
      .leftJoin(
        bankAccounts,
        eq(employeeSalaries.bankAccountId, bankAccounts.id),
      )
      .where(and(...finalConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get employee salary IDs for batch loading allowances and deductions
    const employeeSalaryIds = results.map((row) => row.id);

    // Batch load allowances totals
    const allowanceTotals =
      employeeSalaryIds.length > 0
        ? await this.db
            .select({
              employeeSalaryId: employeeSalaryAllowances.employeeSalaryId,
              totalAmount:
                sql<string>`COALESCE(SUM(${employeeSalaryAllowances.amount}::numeric), 0)`.as(
                  'totalAmount',
                ),
            })
            .from(employeeSalaryAllowances)
            .where(
              and(
                inArray(
                  employeeSalaryAllowances.employeeSalaryId,
                  employeeSalaryIds,
                ),
                eq(employeeSalaryAllowances.isDeleted, false),
              ),
            )
            .groupBy(employeeSalaryAllowances.employeeSalaryId)
        : [];

    // Batch load deductions totals
    const deductionTotals =
      employeeSalaryIds.length > 0
        ? await this.db
            .select({
              employeeSalaryId: employeeSalaryDeductions.employeeSalaryId,
              totalAmount:
                sql<string>`COALESCE(SUM(${employeeSalaryDeductions.amount}::numeric), 0)`.as(
                  'totalAmount',
                ),
            })
            .from(employeeSalaryDeductions)
            .where(
              and(
                inArray(
                  employeeSalaryDeductions.employeeSalaryId,
                  employeeSalaryIds,
                ),
                eq(employeeSalaryDeductions.isDeleted, false),
              ),
            )
            .groupBy(employeeSalaryDeductions.employeeSalaryId)
        : [];

    // Create lookup maps for efficient data mapping
    const allowanceTotalMap = new Map(
      allowanceTotals.map((item) => [
        item.employeeSalaryId,
        parseFloat(item.totalAmount || '0'),
      ]),
    );
    const deductionTotalMap = new Map(
      deductionTotals.map((item) => [
        item.employeeSalaryId,
        parseFloat(item.totalAmount || '0'),
      ]),
    );

    const data: EmployeeSalaryListDto[] = results.map((row) => ({
      id: row.id,
      businessId: row.businessId,
      employeeId: row.employeeId,
      employeeDisplayName: row.employeeDisplayName || '',
      employeeEmail: row.employeeEmail || undefined,
      employeePhone: row.employeePhone || undefined,
      basicSalary: parseFloat(row.basicSalary || '0'),
      bankAccountId: row.bankAccountId,
      bankAccountHolderName: row.bankAccountHolderName || '',
      bankName: row.bankName || '',
      bankAccountNumber: row.bankAccountNumber || '',
      status: row.status,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      totalAllowanceAmount: allowanceTotalMap.get(row.id) || 0,
      totalDeductionAmount: deductionTotalMap.get(row.id) || 0,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<EmployeeSalaryDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: employeeSalaries.id,
        businessId: employeeSalaries.businessId,
        employeeId: employeeSalaries.employeeId,
        employeeDisplayName: staffMembers.displayName,
        employeeEmail: staffMembers.email,
        basicSalary: employeeSalaries.basicSalary,
        bankAccountId: employeeSalaries.bankAccountId,
        bankAccountHolderName: bankAccounts.accountHolderName,
        bankName: bankAccounts.bankName,
        bankAccountNumber: bankAccounts.accountNumber,
        status: employeeSalaries.status,
        createdBy: employeeSalaries.createdBy,
        updatedBy: employeeSalaries.updatedBy,
        createdAt: employeeSalaries.createdAt,
        updatedAt: employeeSalaries.updatedAt,
      })
      .from(employeeSalaries)
      .leftJoin(staffMembers, eq(employeeSalaries.employeeId, staffMembers.id))
      .leftJoin(
        bankAccounts,
        eq(employeeSalaries.bankAccountId, bankAccounts.id),
      )
      .where(
        and(
          eq(employeeSalaries.id, id),
          eq(employeeSalaries.businessId, businessId),
          eq(employeeSalaries.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Employee salary not found');
    }

    // Get allowances
    const allowances = await this.db
      .select({
        id: employeeSalaryAllowances.id,
        allowanceTypeId: employeeSalaryAllowances.allowanceTypeId,
        allowanceTypeName: allowanceTypes.allowanceName,
        allowanceTypeCode: allowanceTypes.allowanceCode,
        amount: employeeSalaryAllowances.amount,
        notes: employeeSalaryAllowances.notes,
        createdAt: employeeSalaryAllowances.createdAt,
        updatedAt: employeeSalaryAllowances.updatedAt,
      })
      .from(employeeSalaryAllowances)
      .leftJoin(
        allowanceTypes,
        eq(employeeSalaryAllowances.allowanceTypeId, allowanceTypes.id),
      )
      .where(
        and(
          eq(employeeSalaryAllowances.employeeSalaryId, id),
          eq(employeeSalaryAllowances.isDeleted, false),
        ),
      );

    // Get deductions
    const deductions = await this.db
      .select({
        id: employeeSalaryDeductions.id,
        deductionTypeId: employeeSalaryDeductions.deductionTypeId,
        deductionTypeName: deductionTypes.deductionName,
        deductionTypeCode: deductionTypes.deductionCode,
        amount: employeeSalaryDeductions.amount,
        notes: employeeSalaryDeductions.notes,
        createdAt: employeeSalaryDeductions.createdAt,
        updatedAt: employeeSalaryDeductions.updatedAt,
      })
      .from(employeeSalaryDeductions)
      .leftJoin(
        deductionTypes,
        eq(employeeSalaryDeductions.deductionTypeId, deductionTypes.id),
      )
      .where(
        and(
          eq(employeeSalaryDeductions.employeeSalaryId, id),
          eq(employeeSalaryDeductions.isDeleted, false),
        ),
      );

    return {
      id: result.id,
      businessId: result.businessId,
      employeeId: result.employeeId,
      employeeDisplayName: result.employeeDisplayName || '',
      employeeEmail: result.employeeEmail || undefined,
      basicSalary: parseFloat(result.basicSalary || '0'),
      bankAccountId: result.bankAccountId,
      bankAccountHolderName: result.bankAccountHolderName || '',
      bankName: result.bankName || '',
      bankAccountNumber: result.bankAccountNumber || '',
      status: result.status,
      createdBy: result.createdBy,
      updatedBy: result.updatedBy || undefined,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      allowances: allowances.map((allowance) => ({
        id: allowance.id,
        allowanceTypeId: allowance.allowanceTypeId,
        allowanceTypeName: allowance.allowanceTypeName || '',
        allowanceTypeCode: allowance.allowanceTypeCode || '',
        amount: parseFloat(allowance.amount || '0'),
        notes: allowance.notes || undefined,
        createdAt: allowance.createdAt,
        updatedAt: allowance.updatedAt,
      })),
      deductions: deductions.map((deduction) => ({
        id: deduction.id,
        deductionTypeId: deduction.deductionTypeId,
        deductionTypeName: deduction.deductionTypeName || '',
        deductionTypeCode: deduction.deductionTypeCode || '',
        amount: parseFloat(deduction.amount || '0'),
        notes: deduction.notes || undefined,
        createdAt: deduction.createdAt,
        updatedAt: deduction.updatedAt,
      })),
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateEmployeeSalaryDto: UpdateEmployeeSalaryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if employee salary exists
      const existingEmployeeSalary = await this.db
        .select()
        .from(employeeSalaries)
        .where(
          and(
            eq(employeeSalaries.id, id),
            eq(employeeSalaries.businessId, businessId),
            eq(employeeSalaries.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingEmployeeSalary) {
        throw new NotFoundException('Employee salary not found');
      }

      // If updating employee, verify employee exists and belongs to the business
      if (updateEmployeeSalaryDto.employeeId) {
        // Check if another salary record exists for the new employee
        const conflictingEmployeeSalary = await this.db
          .select()
          .from(employeeSalaries)
          .where(
            and(
              eq(
                employeeSalaries.employeeId,
                updateEmployeeSalaryDto.employeeId,
              ),
              eq(employeeSalaries.businessId, businessId),
              sql`${employeeSalaries.id} != ${id}`, // Exclude current record
              eq(employeeSalaries.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingEmployeeSalary) {
          throw new ConflictException(
            'Employee salary record already exists for this employee',
          );
        }

        const employee = await this.db
          .select()
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateEmployeeSalaryDto.employeeId),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!employee) {
          throw new BadRequestException('Employee not found');
        }
      }

      // If updating bank account, verify bank account exists and belongs to the business
      if (updateEmployeeSalaryDto.bankAccountId) {
        const bankAccount = await this.db
          .select()
          .from(bankAccounts)
          .where(
            and(
              eq(bankAccounts.id, updateEmployeeSalaryDto.bankAccountId),
              eq(bankAccounts.businessId, businessId),
              eq(bankAccounts.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!bankAccount) {
          throw new BadRequestException('Bank account not found');
        }
      }

      // Update employee salary record with allowances and deductions in a transaction
      const updatedEmployeeSalary = await this.db.transaction(async (tx) => {
        // Update the main salary record
        const updateData: any = {
          updatedBy: userId,
        };

        if (updateEmployeeSalaryDto.employeeId !== undefined) {
          updateData.employeeId = updateEmployeeSalaryDto.employeeId;
        }
        if (updateEmployeeSalaryDto.basicSalary !== undefined) {
          updateData.basicSalary =
            updateEmployeeSalaryDto.basicSalary.toString();
        }
        if (updateEmployeeSalaryDto.bankAccountId !== undefined) {
          updateData.bankAccountId = updateEmployeeSalaryDto.bankAccountId;
        }
        if (updateEmployeeSalaryDto.status !== undefined) {
          updateData.status = updateEmployeeSalaryDto.status;
        }

        const [salary] = await tx
          .update(employeeSalaries)
          .set(updateData)
          .where(eq(employeeSalaries.id, id))
          .returning();

        // Handle allowances if provided
        if (updateEmployeeSalaryDto.allowances !== undefined) {
          // Get existing allowances
          const existingAllowances = await tx
            .select()
            .from(employeeSalaryAllowances)
            .where(
              and(
                eq(employeeSalaryAllowances.employeeSalaryId, id),
                eq(employeeSalaryAllowances.isDeleted, false),
              ),
            );

          const existingAllowanceIds = existingAllowances.map((a) => a.id);
          const providedAllowanceIds = updateEmployeeSalaryDto.allowances
            .filter((a) => a.id)
            .map((a) => a.id);

          // Delete allowances that are not in the provided list
          const allowancesToDelete = existingAllowanceIds.filter(
            (id) => !providedAllowanceIds.includes(id),
          );
          if (allowancesToDelete.length > 0) {
            await tx
              .update(employeeSalaryAllowances)
              .set({
                isDeleted: true,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(inArray(employeeSalaryAllowances.id, allowancesToDelete));
          }

          // Process each allowance (update existing or create new)
          for (const allowanceDto of updateEmployeeSalaryDto.allowances) {
            // Verify allowance type exists
            const allowanceType = await tx
              .select()
              .from(allowanceTypes)
              .where(
                and(
                  eq(allowanceTypes.id, allowanceDto.allowanceTypeId),
                  eq(allowanceTypes.businessId, businessId),
                  eq(allowanceTypes.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!allowanceType) {
              throw new BadRequestException(
                `Allowance type ${allowanceDto.allowanceTypeId} not found`,
              );
            }

            if (allowanceDto.id) {
              // Update existing allowance
              await tx
                .update(employeeSalaryAllowances)
                .set({
                  allowanceTypeId: allowanceDto.allowanceTypeId,
                  amount: allowanceDto.amount.toString(),
                  notes: allowanceDto.notes,
                  updatedBy: userId,
                })
                .where(eq(employeeSalaryAllowances.id, allowanceDto.id));
            } else {
              // Create new allowance
              await tx.insert(employeeSalaryAllowances).values({
                businessId,
                employeeSalaryId: salary.id,
                allowanceTypeId: allowanceDto.allowanceTypeId,
                amount: allowanceDto.amount.toString(),
                notes: allowanceDto.notes,
                createdBy: userId,
              });
            }
          }
        }

        // Handle deductions if provided
        if (updateEmployeeSalaryDto.deductions !== undefined) {
          // Get existing deductions
          const existingDeductions = await tx
            .select()
            .from(employeeSalaryDeductions)
            .where(
              and(
                eq(employeeSalaryDeductions.employeeSalaryId, id),
                eq(employeeSalaryDeductions.isDeleted, false),
              ),
            );

          const existingDeductionIds = existingDeductions.map((d) => d.id);
          const providedDeductionIds = updateEmployeeSalaryDto.deductions
            .filter((d) => d.id)
            .map((d) => d.id);

          // Delete deductions that are not in the provided list
          const deductionsToDelete = existingDeductionIds.filter(
            (id) => !providedDeductionIds.includes(id),
          );
          if (deductionsToDelete.length > 0) {
            await tx
              .update(employeeSalaryDeductions)
              .set({
                isDeleted: true,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(inArray(employeeSalaryDeductions.id, deductionsToDelete));
          }

          // Process each deduction (update existing or create new)
          for (const deductionDto of updateEmployeeSalaryDto.deductions) {
            // Verify deduction type exists
            const deductionType = await tx
              .select()
              .from(deductionTypes)
              .where(
                and(
                  eq(deductionTypes.id, deductionDto.deductionTypeId),
                  eq(deductionTypes.businessId, businessId),
                  eq(deductionTypes.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!deductionType) {
              throw new BadRequestException(
                `Deduction type ${deductionDto.deductionTypeId} not found`,
              );
            }

            if (deductionDto.id) {
              // Update existing deduction
              await tx
                .update(employeeSalaryDeductions)
                .set({
                  deductionTypeId: deductionDto.deductionTypeId,
                  amount: deductionDto.amount.toString(),
                  notes: deductionDto.notes,
                  updatedBy: userId,
                })
                .where(eq(employeeSalaryDeductions.id, deductionDto.id));
            } else {
              // Create new deduction
              await tx.insert(employeeSalaryDeductions).values({
                businessId,
                employeeSalaryId: salary.id,
                deductionTypeId: deductionDto.deductionTypeId,
                amount: deductionDto.amount.toString(),
                notes: deductionDto.notes,
                createdBy: userId,
              });
            }
          }
        }

        return salary;
      });

      // Log the employee salary update activity
      await this.activityLogService.logUpdate(
        updatedEmployeeSalary.id,
        EntityType.STAFF,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedEmployeeSalary.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to update employee salary: ' + error.message,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateEmployeeSalaryDto: UpdateEmployeeSalaryDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(
      userId,
      businessId,
      id,
      updateEmployeeSalaryDto,
      metadata,
    );
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if employee salary exists
    const existingEmployeeSalary = await this.db
      .select()
      .from(employeeSalaries)
      .where(
        and(
          eq(employeeSalaries.id, id),
          eq(employeeSalaries.businessId, businessId),
          eq(employeeSalaries.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingEmployeeSalary) {
      throw new NotFoundException('Employee salary not found');
    }

    // Soft delete the employee salary record and related allowances/deductions in a transaction
    await this.db.transaction(async (tx) => {
      // Soft delete related allowances
      await tx
        .update(employeeSalaryAllowances)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(employeeSalaryAllowances.employeeSalaryId, id),
            eq(employeeSalaryAllowances.isDeleted, false),
          ),
        );

      // Soft delete related deductions
      await tx
        .update(employeeSalaryDeductions)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(employeeSalaryDeductions.employeeSalaryId, id),
            eq(employeeSalaryDeductions.isDeleted, false),
          ),
        );

      // Soft delete the main employee salary record
      await tx
        .update(employeeSalaries)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(employeeSalaries.id, id));
    });

    // Log the employee salary deletion activity
    await this.activityLogService.logDelete(
      id,
      EntityType.STAFF,
      userId,
      businessId,
      {
        reason: `Employee salary record was deleted`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createEmployeeSalaryDtos: CreateEmployeeSalaryDto[],
  ): Promise<string[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];

    // Process each employee salary creation
    for (const createDto of createEmployeeSalaryDtos) {
      try {
        const result = await this.create(userId, businessId, createDto);
        createdIds.push(result.id);
      } catch (error) {
        // Continue with other creations even if one fails
        console.error(`Failed to create employee salary: ${error.message}`);
      }
    }

    return createdIds;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createEmployeeSalaryDtos: CreateEmployeeSalaryDto[],
  ): Promise<string[]> {
    return this.bulkCreate(userId, businessId, createEmployeeSalaryDtos);
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deletedIds: string[] = [];

    // Process each deletion
    for (const id of ids) {
      try {
        await this.remove(userId, businessId, id, metadata);
        deletedIds.push(id);
      } catch (error) {
        // Continue with other deletions even if one fails
        console.error(
          `Failed to delete employee salary ${id}: ${error.message}`,
        );
      }
    }

    return {
      deletedCount: deletedIds.length,
      deletedIds,
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: EmployeeSalaryStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedCount: number; updatedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const updatedIds: string[] = [];

    // Process each status update
    for (const id of ids) {
      try {
        await this.update(userId, businessId, id, { status }, metadata);
        updatedIds.push(id);
      } catch (error) {
        // Continue with other updates even if one fails
        console.error(
          `Failed to update employee salary status ${id}: ${error.message}`,
        );
      }
    }

    return {
      updatedCount: updatedIds.length,
      updatedIds,
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<EmployeeSalarySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results = await this.db
      .select({
        id: employeeSalaries.id,
        employeeId: employeeSalaries.employeeId,
        employeeDisplayName: staffMembers.displayName,
        basicSalary: employeeSalaries.basicSalary,
        status: employeeSalaries.status,
      })
      .from(employeeSalaries)
      .leftJoin(staffMembers, eq(employeeSalaries.employeeId, staffMembers.id))
      .where(
        and(
          eq(employeeSalaries.businessId, businessId),
          eq(employeeSalaries.status, EmployeeSalaryStatus.ACTIVE),
          eq(employeeSalaries.isDeleted, false),
        ),
      )
      .orderBy(asc(staffMembers.displayName));

    return results.map((row) => ({
      id: row.id,
      employeeId: row.employeeId,
      employeeDisplayName: row.employeeDisplayName || '',
      basicSalary: parseFloat(row.basicSalary || '0'),
      status: row.status,
    }));
  }
}
