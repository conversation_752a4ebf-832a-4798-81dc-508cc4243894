import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateLeadDto } from './dto/create-lead.dto';
import { UpdateLeadDto } from './dto/update-lead.dto';
import { LeadDto } from './dto/lead.dto';
import { LeadSlimDto } from './dto/lead-slim.dto';
import { LeadListDto } from './dto/lead-list.dto';
import { leads, leadLocations } from '../drizzle/schema/leads.schema';
import { addresses } from '../drizzle/schema/address.schema';
import { media } from '../drizzle/schema/media.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { users } from '../drizzle/schema/users.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  count,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { AddressType, EntityType, ActivityType } from '../shared/types';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';

@Injectable()
export class LeadsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createLeadDto: CreateLeadDto,
    profileImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a lead with the same display name already exists for this business
      const existingLead = await this.db
        .select()
        .from(leads)
        .where(
          and(
            eq(leads.businessId, businessId),
            ilike(leads.leadDisplayName, createLeadDto.leadDisplayName),
            eq(leads.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingLead) {
        throw new ConflictException(
          `Lead with display name "${createLeadDto.leadDisplayName}" already exists`,
        );
      }

      // Check if email is provided and already exists
      if (createLeadDto.email) {
        const existingEmailLead = await this.db
          .select()
          .from(leads)
          .where(
            and(
              eq(leads.businessId, businessId),
              ilike(leads.email, createLeadDto.email),
              eq(leads.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingEmailLead) {
          throw new ConflictException(
            `Lead with email "${createLeadDto.email}" already exists`,
          );
        }
      }

      let addressId: string | undefined;
      let profileImageId: string | undefined;

      // Create address if provided
      if (createLeadDto.address) {
        const addressResult = await this.db
          .insert(addresses)
          .values({
            street: createLeadDto.address.street,
            city: createLeadDto.address.city,
            state: createLeadDto.address.state,
            zipCode: createLeadDto.address.zipCode,
            country: createLeadDto.address.country,
            addressType: AddressType.BUSINESS,
            businessId: businessId,
          })
          .returning({ id: addresses.id });

        addressId = addressResult[0].id;
      }

      // Handle profile image upload if provided
      if (profileImageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          profileImageFile,
          'leads',
          businessId,
          userId,
        );
        profileImageId = uploadedMedia.id;
      }

      // Create the lead
      const leadData = {
        businessId,
        title: createLeadDto.title,
        firstName: createLeadDto.firstName,
        middleName: createLeadDto.middleName,
        lastName: createLeadDto.lastName,
        suffix: createLeadDto.suffix,
        leadDisplayName: createLeadDto.leadDisplayName,
        companyName: createLeadDto.companyName,
        leadType: createLeadDto.leadType,
        email: createLeadDto.email,
        phoneNumber: createLeadDto.phoneNumber,
        mobileNumber: createLeadDto.mobileNumber,
        fax: createLeadDto.fax,
        other: createLeadDto.other,
        website: createLeadDto.website,
        leadStatus: createLeadDto.leadStatus,
        leadSource: createLeadDto.leadSource,
        priority: createLeadDto.priority,
        estimatedValue: createLeadDto.estimatedValue,
        estimatedClosingDate: createLeadDto.estimatedClosingDate
          ? new Date(createLeadDto.estimatedClosingDate)
          : undefined,
        isAllocatedToAllLocations:
          createLeadDto.isAllocatedToAllLocations || false,
        leadOwnerId: createLeadDto.leadOwnerId,
        addressId,
        profileImageId,
        notes: createLeadDto.notes,
        lastContactDate: createLeadDto.lastContactDate
          ? new Date(createLeadDto.lastContactDate)
          : undefined,
        createdBy: userId,
        updatedBy: userId,
      };

      const result = await this.db
        .insert(leads)
        .values(leadData)
        .returning({ id: leads.id });

      const leadId = result[0].id;

      // Handle location assignments if not allocated to all locations
      if (
        !createLeadDto.isAllocatedToAllLocations &&
        createLeadDto.locationIds?.length
      ) {
        const locationAssignments = createLeadDto.locationIds.map(
          (locationId) => ({
            leadId,
            locationId,
            businessId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db.insert(leadLocations).values(locationAssignments);
      }

      // Log the activity
      await this.activityLogService.create({
        entityType: EntityType.LEAD,
        entityId: leadId,
        activityType: ActivityType.CREATE,
        userId,
        businessId,
        metadata: {
          leadDisplayName: createLeadDto.leadDisplayName,
          leadType: createLeadDto.leadType,
          leadStatus: createLeadDto.leadStatus,
        },
      });

      return { id: leadId };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create lead');
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createLeadsDto: CreateLeadDto[],
    profileImageFiles?: Express.Multer.File[],
  ): Promise<{ id: string }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const results: { id: string }[] = [];

    // Process each lead individually to handle validation and conflicts
    for (let i = 0; i < createLeadsDto.length; i++) {
      const createLeadDto = createLeadsDto[i];
      const profileImageFile = profileImageFiles?.[i];

      try {
        const result = await this.create(
          userId,
          businessId,
          createLeadDto,
          profileImageFile,
        );
        results.push(result);
      } catch (error) {
        // For bulk operations, we might want to continue with other leads
        // or throw an error. For now, we'll throw to maintain consistency
        throw new BadRequestException(
          `Failed to create lead "${createLeadDto.leadDisplayName}": ${error.message}`,
        );
      }
    }

    return results;
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    leadDisplayName?: string,
    email?: string,
    companyName?: string,
    leadStatus?: string,
    leadSource?: string,
    priority?: string,
    leadOwnerId?: string,
    isConverted?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: LeadListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(leads.isDeleted, false),
      eq(leads.businessId, businessId),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(gte(leads.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(leads.createdAt, new Date(to)));
    }

    // Add search filters
    if (leadDisplayName) {
      whereConditions.push(
        ilike(leads.leadDisplayName, `%${leadDisplayName}%`),
      );
    }
    if (email) {
      whereConditions.push(ilike(leads.email, `%${email}%`));
    }
    if (companyName) {
      whereConditions.push(ilike(leads.companyName, `%${companyName}%`));
    }
    if (leadStatus) {
      whereConditions.push(eq(leads.leadStatus, leadStatus as any));
    }
    if (leadSource) {
      whereConditions.push(eq(leads.leadSource, leadSource as any));
    }
    if (priority) {
      whereConditions.push(eq(leads.priority, priority as any));
    }
    if (leadOwnerId) {
      whereConditions.push(eq(leads.leadOwnerId, leadOwnerId));
    }
    if (isConverted !== undefined) {
      whereConditions.push(eq(leads.isConverted, isConverted === 'true'));
    }

    // Handle additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = parsedFilters
          .map((filter: any) => {
            switch (filter.id) {
              case 'leadDisplayName':
                return ilike(leads.leadDisplayName, `%${filter.value}%`);
              case 'email':
                return ilike(leads.email, `%${filter.value}%`);
              case 'companyName':
                return ilike(leads.companyName, `%${filter.value}%`);
              case 'leadStatus':
                return eq(leads.leadStatus, filter.value);
              case 'leadSource':
                return eq(leads.leadSource, filter.value);
              case 'priority':
                return eq(leads.priority, filter.value);
              default:
                return null;
            }
          })
          .filter(Boolean);

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Build sort conditions
    let orderBy: any[] = [desc(leads.createdAt)]; // Default sort
    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        orderBy = parsedSort.map((sortItem: any) => {
          // Map sort field to actual column
          switch (sortItem.id) {
            case 'leadDisplayName':
              return sortItem.desc
                ? desc(leads.leadDisplayName)
                : asc(leads.leadDisplayName);
            case 'email':
              return sortItem.desc ? desc(leads.email) : asc(leads.email);
            case 'companyName':
              return sortItem.desc
                ? desc(leads.companyName)
                : asc(leads.companyName);
            case 'leadStatus':
              return sortItem.desc
                ? desc(leads.leadStatus)
                : asc(leads.leadStatus);
            case 'leadSource':
              return sortItem.desc
                ? desc(leads.leadSource)
                : asc(leads.leadSource);
            case 'priority':
              return sortItem.desc ? desc(leads.priority) : asc(leads.priority);
            case 'estimatedValue':
              return sortItem.desc
                ? desc(leads.estimatedValue)
                : asc(leads.estimatedValue);
            case 'estimatedClosingDate':
              return sortItem.desc
                ? desc(leads.estimatedClosingDate)
                : asc(leads.estimatedClosingDate);
            case 'lastContactDate':
              return sortItem.desc
                ? desc(leads.lastContactDate)
                : asc(leads.lastContactDate);
            case 'createdAt':
              return sortItem.desc
                ? desc(leads.createdAt)
                : asc(leads.createdAt);
            case 'updatedAt':
              return sortItem.desc
                ? desc(leads.updatedAt)
                : asc(leads.updatedAt);
            default:
              return sortItem.desc
                ? desc(leads.createdAt)
                : asc(leads.createdAt);
          }
        });
      } catch {
        // Invalid JSON sort, use default
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(leads)
      .where(and(...whereConditions));

    const total = totalResult[0].count;
    const totalPages = Math.ceil(total / limit);

    // Get leads with joins
    const leadsData = await this.db
      .select({
        id: leads.id,
        businessId: leads.businessId,
        title: leads.title,
        firstName: leads.firstName,
        middleName: leads.middleName,
        lastName: leads.lastName,
        suffix: leads.suffix,
        leadDisplayName: leads.leadDisplayName,
        companyName: leads.companyName,
        leadType: leads.leadType,
        email: leads.email,
        phoneNumber: leads.phoneNumber,
        mobileNumber: leads.mobileNumber,
        website: leads.website,
        leadStatus: leads.leadStatus,
        leadSource: leads.leadSource,
        priority: leads.priority,
        estimatedValue: leads.estimatedValue,
        estimatedClosingDate: leads.estimatedClosingDate,
        isConverted: leads.isConverted,
        isAllocatedToAllLocations: leads.isAllocatedToAllLocations,
        leadOwnerId: leads.leadOwnerId,
        lastContactDate: leads.lastContactDate,
        profileImageId: leads.profileImageId,
        leadOwnerName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        profileImageUrl: media.publicUrl,
      })
      .from(leads)
      .leftJoin(staffMembers, eq(leads.leadOwnerId, staffMembers.id))
      .leftJoin(media, eq(leads.profileImageId, media.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Generate signed URLs for profile images
    const processedLeadsData = await Promise.all(
      leadsData.map(async (lead) => {
        let profileImageUrl = lead.profileImageUrl;

        // Generate signed URL for profile image if it exists
        if (lead.profileImageId) {
          try {
            const mediaData = await this.mediaService.findById(
              lead.profileImageId,
              lead.businessId,
            );

            // Generate signed URL with 60 minutes expiration for the image
            profileImageUrl = await this.gcsUploadService.generateSignedUrl(
              mediaData.fileName,
              'leads', // folder where lead profile images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            // If media is not found or signed URL generation fails, just continue without it
            console.warn(
              `Failed to generate signed URL for lead ${lead.id} profile image:`,
              error.message,
            );
          }
        }

        return {
          ...lead,
          profileImageUrl,
        };
      }),
    );

    return {
      data: processedLeadsData,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<LeadDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const leadData = await this.db
      .select({
        id: leads.id,
        businessId: leads.businessId,
        title: leads.title,
        firstName: leads.firstName,
        middleName: leads.middleName,
        lastName: leads.lastName,
        suffix: leads.suffix,
        leadDisplayName: leads.leadDisplayName,
        companyName: leads.companyName,
        leadType: leads.leadType,
        email: leads.email,
        phoneNumber: leads.phoneNumber,
        mobileNumber: leads.mobileNumber,
        fax: leads.fax,
        other: leads.other,
        website: leads.website,
        leadStatus: leads.leadStatus,
        leadSource: leads.leadSource,
        priority: leads.priority,
        estimatedValue: leads.estimatedValue,
        estimatedClosingDate: leads.estimatedClosingDate,
        isConverted: leads.isConverted,
        isAllocatedToAllLocations: leads.isAllocatedToAllLocations,
        leadOwnerId: leads.leadOwnerId,
        addressId: leads.addressId,
        profileImageId: leads.profileImageId,
        notes: leads.notes,
        lastContactDate: leads.lastContactDate,
        createdAt: leads.createdAt,
        updatedAt: leads.updatedAt,
        leadOwnerName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        profileImageUrl: media.publicUrl,
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        updatedByName: sql<string>`CONCAT(updated_user.first_name, ' ', updated_user.last_name)`,
        addressStreet: addresses.street,
        addressCity: addresses.city,
        addressState: addresses.state,
        addressZipCode: addresses.zipCode,
        addressCountry: addresses.country,
      })
      .from(leads)
      .leftJoin(staffMembers, eq(leads.leadOwnerId, staffMembers.id))
      .leftJoin(media, eq(leads.profileImageId, media.id))
      .leftJoin(users, eq(leads.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(leads.updatedBy, sql`updated_user.id`),
      )
      .leftJoin(addresses, eq(leads.addressId, addresses.id))
      .where(
        and(
          eq(leads.id, id),
          eq(leads.businessId, businessId),
          eq(leads.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!leadData) {
      throw new NotFoundException('Lead not found');
    }

    // Build address object if address data exists
    const address = leadData.addressStreet
      ? {
          street: leadData.addressStreet,
          city: leadData.addressCity,
          state: leadData.addressState,
          zipCode: leadData.addressZipCode,
          country: leadData.addressCountry,
        }
      : undefined;

    // Generate signed URL for profile image if it exists
    let profileImageUrl = leadData.profileImageUrl;
    if (leadData.profileImageId) {
      try {
        const mediaData = await this.mediaService.findById(
          leadData.profileImageId,
          leadData.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the image
        profileImageUrl = await this.gcsUploadService.generateSignedUrl(
          mediaData.fileName,
          'leads', // folder where lead profile images are stored
          60, // expiration in minutes
        );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for lead ${leadData.id} profile image:`,
          error.message,
        );
      }
    }

    return {
      id: leadData.id,
      businessId: leadData.businessId,
      title: leadData.title,
      firstName: leadData.firstName,
      middleName: leadData.middleName,
      lastName: leadData.lastName,
      suffix: leadData.suffix,
      leadDisplayName: leadData.leadDisplayName,
      companyName: leadData.companyName,
      leadType: leadData.leadType,
      email: leadData.email,
      phoneNumber: leadData.phoneNumber,
      mobileNumber: leadData.mobileNumber,
      fax: leadData.fax,
      other: leadData.other,
      website: leadData.website,
      leadStatus: leadData.leadStatus,
      leadSource: leadData.leadSource,
      priority: leadData.priority,
      estimatedValue: leadData.estimatedValue,
      estimatedClosingDate: leadData.estimatedClosingDate,
      isConverted: leadData.isConverted,
      isAllocatedToAllLocations: leadData.isAllocatedToAllLocations,
      leadOwnerId: leadData.leadOwnerId,
      leadOwnerName: leadData.leadOwnerName,
      addressId: leadData.addressId,
      address,
      profileImageId: leadData.profileImageId,
      profileImageUrl: profileImageUrl,
      notes: leadData.notes,
      lastContactDate: leadData.lastContactDate,
      createdBy: leadData.createdByName,
      updatedBy: leadData.updatedByName,
      createdAt: leadData.createdAt,
      updatedAt: leadData.updatedAt,
    };
  }

  async findAllSlim(businessId: string | null): Promise<LeadSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const leadsData = await this.db
      .select({
        id: leads.id,
        leadDisplayName: leads.leadDisplayName,
        companyName: leads.companyName,
        leadType: leads.leadType,
        email: leads.email,
        phoneNumber: leads.phoneNumber,
        leadStatus: leads.leadStatus,
        leadSource: leads.leadSource,
        priority: leads.priority,
        isConverted: leads.isConverted,
        profileImageId: leads.profileImageId,
        leadOwnerName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        profileImageUrl: media.publicUrl,
      })
      .from(leads)
      .leftJoin(staffMembers, eq(leads.leadOwnerId, staffMembers.id))
      .leftJoin(media, eq(leads.profileImageId, media.id))
      .where(and(eq(leads.businessId, businessId), eq(leads.isDeleted, false)))
      .orderBy(asc(leads.leadDisplayName));

    // Generate signed URLs for profile images
    const processedLeadsData = await Promise.all(
      leadsData.map(async (lead) => {
        let profileImageUrl = lead.profileImageUrl;

        // Generate signed URL for profile image if it exists
        if (lead.profileImageId) {
          try {
            const mediaData = await this.mediaService.findById(
              lead.profileImageId,
              businessId,
            );

            // Generate signed URL with 60 minutes expiration for the image
            profileImageUrl = await this.gcsUploadService.generateSignedUrl(
              mediaData.fileName,
              'leads', // folder where lead profile images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            // If media is not found or signed URL generation fails, just continue without it
            console.warn(
              `Failed to generate signed URL for lead ${lead.id} profile image:`,
              error.message,
            );
          }
        }

        return {
          ...lead,
          profileImageUrl,
        };
      }),
    );

    return processedLeadsData;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeadDto: UpdateLeadDto,
    profileImageFile?: Express.Multer.File,
  ): Promise<LeadDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if lead exists
    const existingLead = await this.db
      .select()
      .from(leads)
      .where(
        and(
          eq(leads.id, id),
          eq(leads.businessId, businessId),
          eq(leads.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingLead) {
      throw new NotFoundException('Lead not found');
    }

    // Check if display name is being updated and already exists
    if (
      updateLeadDto.leadDisplayName &&
      updateLeadDto.leadDisplayName !== existingLead.leadDisplayName
    ) {
      const existingDisplayNameLead = await this.db
        .select()
        .from(leads)
        .where(
          and(
            eq(leads.businessId, businessId),
            ilike(leads.leadDisplayName, updateLeadDto.leadDisplayName),
            eq(leads.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingDisplayNameLead && existingDisplayNameLead.id !== id) {
        throw new ConflictException(
          `Lead with display name "${updateLeadDto.leadDisplayName}" already exists`,
        );
      }
    }

    // Check if email is being updated and already exists
    if (updateLeadDto.email && updateLeadDto.email !== existingLead.email) {
      const existingEmailLead = await this.db
        .select()
        .from(leads)
        .where(
          and(
            eq(leads.businessId, businessId),
            ilike(leads.email, updateLeadDto.email),
            eq(leads.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingEmailLead && existingEmailLead.id !== id) {
        throw new ConflictException(
          `Lead with email "${updateLeadDto.email}" already exists`,
        );
      }
    }

    let addressId = existingLead.addressId;
    let profileImageId = existingLead.profileImageId;

    // Update address if provided
    if (updateLeadDto.address) {
      if (addressId) {
        // Update existing address
        await this.db
          .update(addresses)
          .set({
            street: updateLeadDto.address.street,
            city: updateLeadDto.address.city,
            state: updateLeadDto.address.state,
            zipCode: updateLeadDto.address.zipCode,
            country: updateLeadDto.address.country,
            updatedAt: new Date(),
          })
          .where(eq(addresses.id, addressId));
      } else {
        // Create new address
        const addressResult = await this.db
          .insert(addresses)
          .values({
            street: updateLeadDto.address.street,
            city: updateLeadDto.address.city,
            state: updateLeadDto.address.state,
            zipCode: updateLeadDto.address.zipCode,
            country: updateLeadDto.address.country,
            addressType: AddressType.BUSINESS,
            businessId: businessId,
          })
          .returning({ id: addresses.id });

        addressId = addressResult[0].id;
      }
    }

    // Handle profile image upload if provided
    if (profileImageFile) {
      profileImageId = await this.mediaService.updateMediaReference(
        existingLead.profileImageId,
        profileImageFile,
        'leads',
        businessId,
        userId,
      );
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: userId,
      updatedAt: new Date(),
    };

    // Only update fields that are provided
    if (updateLeadDto.title !== undefined)
      updateData.title = updateLeadDto.title;
    if (updateLeadDto.firstName !== undefined)
      updateData.firstName = updateLeadDto.firstName;
    if (updateLeadDto.middleName !== undefined)
      updateData.middleName = updateLeadDto.middleName;
    if (updateLeadDto.lastName !== undefined)
      updateData.lastName = updateLeadDto.lastName;
    if (updateLeadDto.suffix !== undefined)
      updateData.suffix = updateLeadDto.suffix;
    if (updateLeadDto.leadDisplayName !== undefined)
      updateData.leadDisplayName = updateLeadDto.leadDisplayName;
    if (updateLeadDto.companyName !== undefined)
      updateData.companyName = updateLeadDto.companyName;
    if (updateLeadDto.leadType !== undefined)
      updateData.leadType = updateLeadDto.leadType;
    if (updateLeadDto.email !== undefined)
      updateData.email = updateLeadDto.email;
    if (updateLeadDto.phoneNumber !== undefined)
      updateData.phoneNumber = updateLeadDto.phoneNumber;
    if (updateLeadDto.mobileNumber !== undefined)
      updateData.mobileNumber = updateLeadDto.mobileNumber;
    if (updateLeadDto.fax !== undefined) updateData.fax = updateLeadDto.fax;
    if (updateLeadDto.other !== undefined)
      updateData.other = updateLeadDto.other;
    if (updateLeadDto.website !== undefined)
      updateData.website = updateLeadDto.website;
    if (updateLeadDto.leadStatus !== undefined)
      updateData.leadStatus = updateLeadDto.leadStatus;
    if (updateLeadDto.leadSource !== undefined)
      updateData.leadSource = updateLeadDto.leadSource;
    if (updateLeadDto.priority !== undefined)
      updateData.priority = updateLeadDto.priority;
    if (updateLeadDto.estimatedValue !== undefined)
      updateData.estimatedValue = updateLeadDto.estimatedValue;
    if (updateLeadDto.estimatedClosingDate !== undefined) {
      updateData.estimatedClosingDate = updateLeadDto.estimatedClosingDate
        ? new Date(updateLeadDto.estimatedClosingDate)
        : null;
    }
    if (updateLeadDto.isConverted !== undefined)
      updateData.isConverted = updateLeadDto.isConverted;
    if (updateLeadDto.isAllocatedToAllLocations !== undefined) {
      updateData.isAllocatedToAllLocations =
        updateLeadDto.isAllocatedToAllLocations;
    }
    if (updateLeadDto.leadOwnerId !== undefined)
      updateData.leadOwnerId = updateLeadDto.leadOwnerId;
    if (addressId !== existingLead.addressId) updateData.addressId = addressId;
    if (profileImageId !== existingLead.profileImageId)
      updateData.profileImageId = profileImageId;
    if (updateLeadDto.notes !== undefined)
      updateData.notes = updateLeadDto.notes;
    if (updateLeadDto.lastContactDate !== undefined) {
      updateData.lastContactDate = updateLeadDto.lastContactDate
        ? new Date(updateLeadDto.lastContactDate)
        : null;
    }

    // Update the lead
    await this.db.update(leads).set(updateData).where(eq(leads.id, id));

    // Handle location assignments if location allocation is being updated
    if (
      updateLeadDto.isAllocatedToAllLocations !== undefined ||
      updateLeadDto.locationIds !== undefined
    ) {
      // Remove existing location assignments
      await this.db.delete(leadLocations).where(eq(leadLocations.leadId, id));

      // Add new location assignments if not allocated to all locations
      if (
        !updateLeadDto.isAllocatedToAllLocations &&
        updateLeadDto.locationIds?.length
      ) {
        const locationAssignments = updateLeadDto.locationIds.map(
          (locationId) => ({
            leadId: id,
            locationId,
            businessId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db.insert(leadLocations).values(locationAssignments);
      }
    }

    // Log the activity
    await this.activityLogService.create({
      entityType: EntityType.LEAD,
      entityId: id,
      activityType: ActivityType.UPDATE,
      userId,
      businessId,
      metadata: updateData,
    });

    // Return the updated lead
    return this.findOne(userId, businessId, id);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<void> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingLead = await this.db
      .select()
      .from(leads)
      .where(
        and(
          eq(leads.id, id),
          eq(leads.businessId, businessId),
          eq(leads.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingLead) {
      throw new NotFoundException('Lead not found');
    }

    // Soft delete the lead
    await this.db
      .update(leads)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(leads.id, id));

    // Log the activity
    await this.activityLogService.create({
      entityType: EntityType.LEAD,
      entityId: id,
      activityType: ActivityType.DELETE,
      userId,
      businessId,
      metadata: {
        leadDisplayName: existingLead.leadDisplayName,
        leadType: existingLead.leadType,
        leadStatus: existingLead.leadStatus,
      },
    });
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (ids.length === 0) {
      return { deletedCount: 0, deletedIds: [] };
    }

    // Get existing leads to validate and for logging
    const existingLeads = await this.db
      .select()
      .from(leads)
      .where(
        and(
          inArray(leads.id, ids),
          eq(leads.businessId, businessId),
          eq(leads.isDeleted, false),
        ),
      );

    if (existingLeads.length === 0) {
      throw new NotFoundException('No leads found to delete');
    }

    const validIds = existingLeads.map((lead) => lead.id);

    // Soft delete the leads
    await this.db
      .update(leads)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(leads.id, validIds));

    // Log the activity for each deleted lead
    for (const lead of existingLeads) {
      await this.activityLogService.create({
        entityType: EntityType.LEAD,
        entityId: lead.id,
        activityType: ActivityType.DELETE,
        userId,
        businessId,
        metadata: {
          leadDisplayName: lead.leadDisplayName,
          leadType: lead.leadType,
          leadStatus: lead.leadStatus,
          bulkOperation: true,
        },
      });
    }

    return {
      deletedCount: validIds.length,
      deletedIds: validIds,
    };
  }

  async checkLeadDisplayNameAvailability(
    businessId: string | null,
    leadDisplayName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; leadDisplayName: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(leads.businessId, businessId),
      ilike(leads.leadDisplayName, leadDisplayName),
      eq(leads.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${leads.id} != ${excludeId}`);
    }

    const existingLead = await this.db
      .select()
      .from(leads)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existingLead;

    return {
      available,
      leadDisplayName,
      message: available
        ? 'Lead display name is available'
        : 'Lead display name already exists',
    };
  }

  // Helper methods for returning IDs (following categories pattern)
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createLeadDto: CreateLeadDto,
    profileImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createLeadDto, profileImageFile);
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createLeadsDto: CreateLeadDto[],
    profileImageFiles?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    const leads = await this.bulkCreate(
      userId,
      businessId,
      createLeadsDto,
      profileImageFiles,
    );
    return { ids: leads.map((lead) => lead.id) };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeadDto: UpdateLeadDto,
    profileImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateLeadDto, profileImageFile);
    return { id };
  }
}
