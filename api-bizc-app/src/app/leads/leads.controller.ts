import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
  ApiBody,
  ApiConsumes,
} from '@nestjs/swagger';
import {
  FileInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { LeadsService } from './leads.service';
import { CreateLeadDto } from './dto/create-lead.dto';
import { UpdateLeadDto } from './dto/update-lead.dto';
import { LeadDto } from './dto/lead.dto';
import { LeadSlimDto } from './dto/lead-slim.dto';
import { LeadIdResponseDto } from './dto/lead-id-response.dto';
import { BulkCreateLeadDto } from './dto/bulk-create-lead.dto';
import { BulkLeadIdsResponseDto } from './dto/bulk-lead-ids-response.dto';
import { BulkDeleteLeadDto } from './dto/bulk-delete-lead.dto';
import { BulkDeleteLeadResponseDto } from './dto/bulk-delete-lead-response.dto';
import { PaginatedLeadsResponseDto } from './dto/paginated-leads-response.dto';
import {
  CheckLeadNameDto,
  LeadNameAvailabilityResponseDto,
} from './dto/check-lead-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('leads')
@Controller('leads')
@UseGuards(PermissionsGuard)
export class LeadsController {
  constructor(private readonly leadsService: LeadsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_CREATE)
  @UseInterceptors(FileInterceptor('profileImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Create a new lead' })
  @ApiResponse({
    status: 201,
    description: 'The lead has been successfully created',
    type: LeadIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Lead display name or email already exists',
  })
  create(
    @Request() req,
    @Body() createLeadDto: CreateLeadDto,
    @UploadedFile() profileImage?: Express.Multer.File,
  ): Promise<LeadIdResponseDto> {
    return this.leadsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createLeadDto,
      profileImage,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_CREATE)
  @UseInterceptors(FileFieldsInterceptor([{ name: 'profileImages' }]))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Bulk create leads' })
  @ApiResponse({
    status: 201,
    description: 'The leads have been successfully created',
    type: BulkLeadIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or duplicate names',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Lead names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateLeadDto: BulkCreateLeadDto,
    @UploadedFiles() files?: { profileImages?: Express.Multer.File[] },
  ): Promise<BulkLeadIdsResponseDto> {
    return this.leadsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateLeadDto.leads,
      files?.profileImages,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_READ)
  @ApiOperation({
    summary: 'Get all leads for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'leadDisplayName',
    description: 'Filter by lead display name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'email',
    description: 'Filter by email',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'companyName',
    description: 'Filter by company name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'leadStatus',
    description: 'Filter by lead status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'leadSource',
    description: 'Filter by lead source',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'priority',
    description: 'Filter by priority',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'leadOwnerId',
    description: 'Filter by lead owner ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'isConverted',
    description: 'Filter by conversion status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort configuration as JSON string',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of leads retrieved successfully',
    type: PaginatedLeadsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('leadDisplayName') leadDisplayName?: string,
    @Query('email') email?: string,
    @Query('companyName') companyName?: string,
    @Query('leadStatus') leadStatus?: string,
    @Query('leadSource') leadSource?: string,
    @Query('priority') priority?: string,
    @Query('leadOwnerId') leadOwnerId?: string,
    @Query('isConverted') isConverted?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedLeadsResponseDto> {
    return this.leadsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      leadDisplayName,
      email,
      companyName,
      leadStatus,
      leadSource,
      priority,
      leadOwnerId,
      isConverted,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_READ)
  @ApiOperation({
    summary: 'Get all leads in slim format (for dropdowns)',
  })
  @ApiResponse({
    status: 200,
    description: 'List of leads in slim format retrieved successfully',
    type: [LeadSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<LeadSlimDto[]> {
    return this.leadsService.findAllSlim(req.user.activeBusinessId);
  }

  @Post('check-name')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_READ)
  @ApiOperation({ summary: 'Check if lead display name is available' })
  @ApiResponse({
    status: 200,
    description: 'Lead display name availability checked successfully',
    type: LeadNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkLeadDisplayNameAvailability(
    @Request() req,
    @Body() checkLeadNameDto: CheckLeadNameDto,
  ): Promise<LeadNameAvailabilityResponseDto> {
    return this.leadsService.checkLeadDisplayNameAvailability(
      req.user.activeBusinessId,
      checkLeadNameDto.leadDisplayName,
      checkLeadNameDto.excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_READ)
  @ApiOperation({ summary: 'Get a lead by ID' })
  @ApiParam({
    name: 'id',
    description: 'Lead ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lead retrieved successfully',
    type: LeadDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<LeadDto> {
    return this.leadsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_UPDATE)
  @UseInterceptors(FileInterceptor('profileImage'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update a lead' })
  @ApiParam({
    name: 'id',
    description: 'Lead ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lead updated successfully',
    type: LeadIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Lead display name or email already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateLeadDto: UpdateLeadDto,
    @UploadedFile() profileImage?: Express.Multer.File,
  ): Promise<LeadIdResponseDto> {
    return this.leadsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateLeadDto,
      profileImage,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_DELETE)
  @ApiOperation({ summary: 'Bulk delete leads' })
  @ApiBody({
    description: 'Array of lead IDs to delete',
    type: BulkDeleteLeadDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Leads deleted successfully',
    type: BulkDeleteLeadResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'No leads found to delete',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteLeadDto: BulkDeleteLeadDto,
  ): Promise<BulkDeleteLeadResponseDto> {
    return this.leadsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteLeadDto.ids,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.LEAD_DELETE)
  @ApiOperation({ summary: 'Delete a lead' })
  @ApiParam({
    name: 'id',
    description: 'Lead ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Lead deleted successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing token',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Lead not found',
  })
  remove(@Request() req, @Param('id') id: string): Promise<void> {
    return this.leadsService.remove(req.user.id, req.user.activeBusinessId, id);
  }
}
