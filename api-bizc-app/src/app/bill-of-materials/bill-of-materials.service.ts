import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { billOfMaterials, bomLines } from '../drizzle/schema/bom.schema';
import { users } from '../drizzle/schema/users.schema';
import { products } from '../drizzle/schema/products.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
  count,
} from 'drizzle-orm';
import { BomStatus } from '../shared/types';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { CreateBillOfMaterialsDto } from './dto/create-bill-of-materials.dto';
import { UpdateBillOfMaterialsDto } from './dto/update-bill-of-materials.dto';
import {
  BillOfMaterialsDto,
  BillOfMaterialsListDto,
  BillOfMaterialsSlimDto,
} from './dto/bill-of-materials.dto';
import { CreateBomLineDto } from './dto/bom-line.dto';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class BillOfMaterialsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createBillOfMaterialsDto: CreateBillOfMaterialsDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a BOM with the same code already exists for this business
      const existingBom = await this.db
        .select()
        .from(billOfMaterials)
        .where(
          and(
            eq(billOfMaterials.businessId, businessId),
            ilike(billOfMaterials.bomCode, createBillOfMaterialsDto.bomCode),
            eq(billOfMaterials.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingBom) {
        throw new ConflictException(
          `A bill of materials with the code '${createBillOfMaterialsDto.bomCode}' already exists for this business`,
        );
      }

      // Validate that the product exists
      const product = await this.db
        .select()
        .from(products)
        .where(
          and(
            eq(products.id, createBillOfMaterialsDto.productId),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!product) {
        throw new BadRequestException('Product not found');
      }

      // Use a transaction to ensure BOM and BOM lines are created atomically
      const newBom = await this.db.transaction(async (tx) => {
        // Insert new BOM
        const [bom] = await tx
          .insert(billOfMaterials)
          .values({
            businessId,
            bomCode: createBillOfMaterialsDto.bomCode,
            productId: createBillOfMaterialsDto.productId,
            productVariantId: createBillOfMaterialsDto.productVariantId,
            bomName: createBillOfMaterialsDto.bomName,
            description: createBillOfMaterialsDto.description,
            quantityToProduce:
              createBillOfMaterialsDto.quantityToProduce || '1.000',
            standardUnitOfMeasure:
              createBillOfMaterialsDto.standardUnitOfMeasure,
            unitOfMeasureId: createBillOfMaterialsDto.unitOfMeasureId,
            bomType: createBillOfMaterialsDto.bomType,
            recipeId: createBillOfMaterialsDto.recipeId,
            laborHours: createBillOfMaterialsDto.laborHours || '0.00',
            machineHours: createBillOfMaterialsDto.machineHours || '0.00',
            status: createBillOfMaterialsDto.status || BomStatus.DRAFT,
            versionNumber: createBillOfMaterialsDto.versionNumber || 1,
            createdBy: userId,
          })
          .returning();

        // Insert BOM lines
        if (
          createBillOfMaterialsDto.bomLines &&
          createBillOfMaterialsDto.bomLines.length > 0
        ) {
          const bomLinesData = createBillOfMaterialsDto.bomLines.map(
            (line, index) => ({
              businessId,
              bomId: bom.id,
              componentProductId: line.componentProductId,
              componentVariantId: line.componentVariantId,
              quantityRequired: line.quantityRequired,
              standardUnitOfMeasure: line.standardUnitOfMeasure,
              unitOfMeasureId: line.unitOfMeasureId,
              componentType: line.componentType,
              scrapPercentage: line.scrapPercentage || '0.00',
              sequenceNumber: line.sequenceNumber || index + 1,
              notes: line.notes,
              createdBy: userId,
            }),
          );

          await tx.insert(bomLines).values(bomLinesData);
        }

        return bom;
      });

      // Log the BOM creation activity
      await this.activityLogService.logCreate(
        newBom.id,
        EntityType.BILL_OF_MATERIALS,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newBom.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create bill of materials: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createBillOfMaterialsDto: CreateBillOfMaterialsDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createBillOfMaterialsDto,
      metadata,
    );
    return {
      id: result.id,
      message: 'Bill of materials created successfully',
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    bomCode?: string,
    bomName?: string,
    status?: string,
    bomType?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: BillOfMaterialsListDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(billOfMaterials.isDeleted, false),
      eq(billOfMaterials.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(billOfMaterials.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(billOfMaterials.createdAt, toDate));
      }
    }

    // Add basic filters
    if (bomCode) {
      whereConditions.push(ilike(billOfMaterials.bomCode, `%${bomCode}%`));
    }

    if (bomName) {
      whereConditions.push(ilike(billOfMaterials.bomName, `%${bomName}%`));
    }

    if (status) {
      const statusValues = status
        .split(',')
        .map((s) => s.trim()) as BomStatus[];
      whereConditions.push(inArray(billOfMaterials.status, statusValues));
    }

    if (bomType) {
      const typeValues = bomType.split(',').map((t) => t.trim()) as any[];
      whereConditions.push(inArray(billOfMaterials.bomType, typeValues));
    }

    // Build sort conditions
    let orderBy = [desc(billOfMaterials.createdAt)];
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        orderBy = sortConfig.map((s: any) => {
          const columnName = s.id;
          if (columnName === 'bomCode') {
            return s.desc
              ? desc(billOfMaterials.bomCode)
              : asc(billOfMaterials.bomCode);
          } else if (columnName === 'bomName') {
            return s.desc
              ? desc(billOfMaterials.bomName)
              : asc(billOfMaterials.bomName);
          } else if (columnName === 'createdAt') {
            return s.desc
              ? desc(billOfMaterials.createdAt)
              : asc(billOfMaterials.createdAt);
          } else if (columnName === 'updatedAt') {
            return s.desc
              ? desc(billOfMaterials.updatedAt)
              : asc(billOfMaterials.updatedAt);
          } else {
            return desc(billOfMaterials.createdAt); // fallback
          }
        });
      } catch {
        // Keep default sort if parsing fails
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(billOfMaterials)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;

    // Get paginated data with components count
    const data = await this.db
      .select({
        id: billOfMaterials.id,
        bomCode: billOfMaterials.bomCode,
        bomName: billOfMaterials.bomName,
        productId: billOfMaterials.productId,
        productVariantId: billOfMaterials.productVariantId,
        bomType: billOfMaterials.bomType,
        status: billOfMaterials.status,
        versionNumber: billOfMaterials.versionNumber,
        createdAt: billOfMaterials.createdAt,
        updatedAt: billOfMaterials.updatedAt,
        componentsCount: sql<number>`(
          SELECT COUNT(*)::int
          FROM ${bomLines}
          WHERE ${bomLines.bomId} = ${billOfMaterials.id}
          AND ${bomLines.isDeleted} IS NULL
        )`,
      })
      .from(billOfMaterials)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async findOne(
    userId: string,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<BillOfMaterialsDto> {
    // Get BOM with user information
    const bomResult = await this.db
      .select({
        id: billOfMaterials.id,
        businessId: billOfMaterials.businessId,
        bomCode: billOfMaterials.bomCode,
        productId: billOfMaterials.productId,
        productVariantId: billOfMaterials.productVariantId,
        bomName: billOfMaterials.bomName,
        description: billOfMaterials.description,
        quantityToProduce: billOfMaterials.quantityToProduce,
        standardUnitOfMeasure: billOfMaterials.standardUnitOfMeasure,
        unitOfMeasureId: billOfMaterials.unitOfMeasureId,
        bomType: billOfMaterials.bomType,
        recipeId: billOfMaterials.recipeId,
        laborHours: billOfMaterials.laborHours,
        machineHours: billOfMaterials.machineHours,
        status: billOfMaterials.status,
        versionNumber: billOfMaterials.versionNumber,
        createdAt: billOfMaterials.createdAt,
        updatedAt: billOfMaterials.updatedAt,
        createdBy: billOfMaterials.createdBy,
        updatedBy: billOfMaterials.updatedBy,
        createdByName: sql<string>`${users.firstName} || ' ' || ${users.lastName}`,
        updatedByName: sql<string>`CASE
          WHEN ${billOfMaterials.updatedBy} IS NOT NULL
          THEN ${users.firstName} || ' ' || ${users.lastName}
          ELSE NULL
        END`,
      })
      .from(billOfMaterials)
      .leftJoin(users, eq(users.id, billOfMaterials.createdBy))
      .where(
        and(eq(billOfMaterials.id, id), eq(billOfMaterials.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!bomResult) {
      throw new NotFoundException('Bill of materials not found');
    }

    // Log view activity (optional - can be disabled for performance if needed)
    // Note: View activity logging is commented out for performance reasons
    // Uncomment if detailed view tracking is required
    // await this.activityLogService.logView(
    //   id,
    //   EntityType.BILL_OF_MATERIALS,
    //   userId,
    //   bomResult.businessId,
    //   {
    //     source: metadata?.source || ActivitySource.WEB,
    //     ipAddress: metadata?.ipAddress,
    //     userAgent: metadata?.userAgent,
    //     sessionId: metadata?.sessionId,
    //   },
    // );

    // Get BOM lines
    const bomLinesResult = await this.db
      .select({
        id: bomLines.id,
        businessId: bomLines.businessId,
        bomId: bomLines.bomId,
        componentProductId: bomLines.componentProductId,
        componentVariantId: bomLines.componentVariantId,
        quantityRequired: bomLines.quantityRequired,
        standardUnitOfMeasure: bomLines.standardUnitOfMeasure,
        unitOfMeasureId: bomLines.unitOfMeasureId,
        componentType: bomLines.componentType,
        scrapPercentage: bomLines.scrapPercentage,
        sequenceNumber: bomLines.sequenceNumber,
        notes: bomLines.notes,
        createdAt: bomLines.createdAt,
        updatedAt: bomLines.updatedAt,
      })
      .from(bomLines)
      .where(and(eq(bomLines.bomId, id), eq(bomLines.isDeleted, false)))
      .orderBy(asc(bomLines.sequenceNumber));

    return {
      id: bomResult.id,
      businessId: bomResult.businessId,
      bomCode: bomResult.bomCode,
      productId: bomResult.productId,
      productVariantId: bomResult.productVariantId,
      bomName: bomResult.bomName,
      description: bomResult.description,
      quantityToProduce: bomResult.quantityToProduce,
      standardUnitOfMeasure: bomResult.standardUnitOfMeasure,
      unitOfMeasureId: bomResult.unitOfMeasureId,
      bomType: bomResult.bomType,
      recipeId: bomResult.recipeId,
      laborHours: bomResult.laborHours,
      machineHours: bomResult.machineHours,
      status: bomResult.status,
      versionNumber: bomResult.versionNumber,
      bomLines: bomLinesResult,
      createdBy: bomResult.createdByName,
      updatedBy: bomResult.updatedByName,
      createdAt: bomResult.createdAt,
      updatedAt: bomResult.updatedAt,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<BillOfMaterialsSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    return this.db
      .select({
        id: billOfMaterials.id,
        bomCode: billOfMaterials.bomCode,
        bomName: billOfMaterials.bomName,
        bomType: billOfMaterials.bomType,
        status: billOfMaterials.status,
        versionNumber: billOfMaterials.versionNumber,
      })
      .from(billOfMaterials)
      .where(
        and(
          eq(billOfMaterials.businessId, businessId),
          eq(billOfMaterials.isDeleted, false),
        ),
      )
      .orderBy(asc(billOfMaterials.bomName));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateBillOfMaterialsDto: UpdateBillOfMaterialsDto,
    metadata?: ActivityMetadata,
  ): Promise<BillOfMaterialsDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if BOM exists and belongs to the business
      const existingBom = await this.db
        .select()
        .from(billOfMaterials)
        .where(
          and(
            eq(billOfMaterials.id, id),
            eq(billOfMaterials.businessId, businessId),
            eq(billOfMaterials.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingBom) {
        throw new NotFoundException('Bill of materials not found');
      }

      // Check if bomCode is being changed and if it conflicts
      if (
        updateBillOfMaterialsDto.bomCode &&
        updateBillOfMaterialsDto.bomCode !== existingBom.bomCode
      ) {
        const conflictingBom = await this.db
          .select()
          .from(billOfMaterials)
          .where(
            and(
              eq(billOfMaterials.businessId, businessId),
              ilike(billOfMaterials.bomCode, updateBillOfMaterialsDto.bomCode),
              eq(billOfMaterials.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingBom) {
          throw new ConflictException(
            `A bill of materials with the code '${updateBillOfMaterialsDto.bomCode}' already exists for this business`,
          );
        }
      }

      // Use a transaction to ensure BOM and BOM lines are updated atomically
      const updatedBom = await this.db.transaction(async (tx) => {
        // Update BOM
        const updateData: any = {
          updatedBy: userId,
          updatedAt: new Date(),
        };

        // Only update fields that are provided
        if (updateBillOfMaterialsDto.bomCode !== undefined) {
          updateData.bomCode = updateBillOfMaterialsDto.bomCode;
        }
        if (updateBillOfMaterialsDto.productId !== undefined) {
          updateData.productId = updateBillOfMaterialsDto.productId;
        }
        if (updateBillOfMaterialsDto.productVariantId !== undefined) {
          updateData.productVariantId =
            updateBillOfMaterialsDto.productVariantId;
        }
        if (updateBillOfMaterialsDto.bomName !== undefined) {
          updateData.bomName = updateBillOfMaterialsDto.bomName;
        }
        if (updateBillOfMaterialsDto.description !== undefined) {
          updateData.description = updateBillOfMaterialsDto.description;
        }
        if (updateBillOfMaterialsDto.quantityToProduce !== undefined) {
          updateData.quantityToProduce =
            updateBillOfMaterialsDto.quantityToProduce;
        }
        if (updateBillOfMaterialsDto.standardUnitOfMeasure !== undefined) {
          updateData.standardUnitOfMeasure =
            updateBillOfMaterialsDto.standardUnitOfMeasure;
        }
        if (updateBillOfMaterialsDto.unitOfMeasureId !== undefined) {
          updateData.unitOfMeasureId = updateBillOfMaterialsDto.unitOfMeasureId;
        }
        if (updateBillOfMaterialsDto.bomType !== undefined) {
          updateData.bomType = updateBillOfMaterialsDto.bomType;
        }
        if (updateBillOfMaterialsDto.recipeId !== undefined) {
          updateData.recipeId = updateBillOfMaterialsDto.recipeId;
        }
        if (updateBillOfMaterialsDto.laborHours !== undefined) {
          updateData.laborHours = updateBillOfMaterialsDto.laborHours;
        }
        if (updateBillOfMaterialsDto.machineHours !== undefined) {
          updateData.machineHours = updateBillOfMaterialsDto.machineHours;
        }
        if (updateBillOfMaterialsDto.status !== undefined) {
          updateData.status = updateBillOfMaterialsDto.status;
        }
        if (updateBillOfMaterialsDto.versionNumber !== undefined) {
          updateData.versionNumber = updateBillOfMaterialsDto.versionNumber;
        }

        const [updatedBom] = await tx
          .update(billOfMaterials)
          .set(updateData)
          .where(eq(billOfMaterials.id, id))
          .returning();

        // Handle BOM lines update if provided
        if (updateBillOfMaterialsDto.bomLines) {
          // Delete existing BOM lines (soft delete)
          await tx
            .update(bomLines)
            .set({
              isDeleted: true,
              updatedAt: new Date(),
              updatedBy: userId,
            })
            .where(eq(bomLines.bomId, id));

          // Insert new BOM lines
          if (updateBillOfMaterialsDto.bomLines.length > 0) {
            const bomLinesData = updateBillOfMaterialsDto.bomLines.map(
              (line, index) => ({
                businessId,
                bomId: id,
                componentProductId: line.componentProductId,
                componentVariantId: line.componentVariantId,
                quantityRequired: line.quantityRequired,
                standardUnitOfMeasure: line.standardUnitOfMeasure,
                unitOfMeasureId: line.unitOfMeasureId,
                componentType: line.componentType,
                scrapPercentage: line.scrapPercentage || '0.00',
                sequenceNumber: line.sequenceNumber || index + 1,
                notes: line.notes,
                createdBy: userId,
              }),
            );

            await tx.insert(bomLines).values(bomLinesData);
          }
        }

        return updatedBom;
      });

      // Log the BOM update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.BILL_OF_MATERIALS,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      // Return the updated BOM with lines
      return this.findOne(userId, id);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update bill of materials: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateBillOfMaterialsDto: UpdateBillOfMaterialsDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateBillOfMaterialsDto,
      metadata,
    );
    return {
      id,
      message: 'Bill of materials updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if BOM exists and belongs to the business
      const existingBom = await this.db
        .select()
        .from(billOfMaterials)
        .where(
          and(
            eq(billOfMaterials.id, id),
            eq(billOfMaterials.businessId, businessId),
            eq(billOfMaterials.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingBom) {
        throw new NotFoundException('Bill of materials not found');
      }

      // Use a transaction to ensure BOM and BOM lines are deleted atomically
      await this.db.transaction(async (tx) => {
        // Soft delete BOM lines
        await tx
          .update(bomLines)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(eq(bomLines.bomId, id));

        // Soft delete BOM
        await tx
          .update(billOfMaterials)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(eq(billOfMaterials.id, id));
      });

      // Log the BOM deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.BILL_OF_MATERIALS,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Bill of materials deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete bill of materials: ${error.message}`,
      );
    }
  }

  async checkBomCodeAvailability(
    userId: string,
    businessId: string | null,
    bomCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingBom = await this.db
      .select({ id: billOfMaterials.id })
      .from(billOfMaterials)
      .where(
        and(
          eq(billOfMaterials.businessId, businessId),
          ilike(billOfMaterials.bomCode, bomCode),
          eq(billOfMaterials.isDeleted, false),
        ),
      )
      .limit(1);

    return { available: existingBom.length === 0 };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    billOfMaterialsData: CreateBillOfMaterialsDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; created: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!billOfMaterialsData || billOfMaterialsData.length === 0) {
        throw new BadRequestException('No bill of materials data provided');
      }

      // Check for duplicate BOM codes within the batch
      const bomCodes = billOfMaterialsData.map((bom) =>
        bom.bomCode.toLowerCase(),
      );
      const duplicateCodes = bomCodes.filter(
        (code, index) => bomCodes.indexOf(code) !== index,
      );
      if (duplicateCodes.length > 0) {
        throw new ConflictException(
          `Duplicate BOM codes found in batch: ${duplicateCodes.join(', ')}`,
        );
      }

      // Check for existing BOM codes in database
      const existingBoms = await this.db
        .select({ bomCode: billOfMaterials.bomCode })
        .from(billOfMaterials)
        .where(
          and(
            eq(billOfMaterials.businessId, businessId),
            inArray(billOfMaterials.bomCode, bomCodes),
            eq(billOfMaterials.isDeleted, false),
          ),
        );

      if (existingBoms.length > 0) {
        const existingCodes = existingBoms.map((bom) => bom.bomCode);
        throw new ConflictException(
          `BOM codes already exist: ${existingCodes.join(', ')}`,
        );
      }

      const createdIds: string[] = [];

      // Use a transaction to ensure all BOMs are created atomically
      await this.db.transaction(async (tx) => {
        for (const bomData of billOfMaterialsData) {
          // Insert BOM
          const [newBom] = await tx
            .insert(billOfMaterials)
            .values({
              businessId,
              bomCode: bomData.bomCode,
              productId: bomData.productId,
              productVariantId: bomData.productVariantId,
              bomName: bomData.bomName,
              description: bomData.description,
              quantityToProduce: bomData.quantityToProduce || '1.000',
              standardUnitOfMeasure: bomData.standardUnitOfMeasure,
              unitOfMeasureId: bomData.unitOfMeasureId,
              bomType: bomData.bomType,
              recipeId: bomData.recipeId,
              laborHours: bomData.laborHours || '0.00',
              machineHours: bomData.machineHours || '0.00',
              status: bomData.status || BomStatus.DRAFT,
              versionNumber: bomData.versionNumber || 1,
              createdBy: userId,
            })
            .returning();

          createdIds.push(newBom.id);

          // Insert BOM lines
          if (bomData.bomLines && bomData.bomLines.length > 0) {
            const bomLinesData = bomData.bomLines.map((line, index) => ({
              businessId,
              bomId: newBom.id,
              componentProductId: line.componentProductId,
              componentVariantId: line.componentVariantId,
              quantityRequired: line.quantityRequired,
              standardUnitOfMeasure: line.standardUnitOfMeasure,
              unitOfMeasureId: line.unitOfMeasureId,
              componentType: line.componentType,
              scrapPercentage: line.scrapPercentage || '0.00',
              sequenceNumber: line.sequenceNumber || index + 1,
              notes: line.notes,
              createdBy: userId,
            }));

            await tx.insert(bomLines).values(bomLinesData);
          }
        }
      });

      // Log bulk creation activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.BILL_OF_MATERIALS,
        createdIds,
        {
          bomCodes: billOfMaterialsData.map((dto) => dto.bomCode),
          bomNames: billOfMaterialsData.map((dto) => dto.bomName),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: billOfMaterialsData.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        ids: createdIds,
        created: createdIds.length,
        message: `Successfully created ${createdIds.length} bill of materials`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create bill of materials: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    bomIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedIds: string[];
    deleted: number;
    message: string;
    failed?: Array<{ id: string; reason: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!bomIds || bomIds.length === 0) {
        throw new BadRequestException('No bill of materials IDs provided');
      }

      // Validate that all BOMs exist and belong to the business
      const existingBoms = await this.db
        .select({
          id: billOfMaterials.id,
          bomName: billOfMaterials.bomName,
        })
        .from(billOfMaterials)
        .where(
          and(
            eq(billOfMaterials.businessId, businessId),
            inArray(billOfMaterials.id, bomIds),
            eq(billOfMaterials.isDeleted, false),
          ),
        );

      const existingBomIds = existingBoms.map((bom) => bom.id);
      const failed: Array<{ id: string; reason: string }> = [];

      // Identify missing BOMs
      bomIds.forEach((id) => {
        if (!existingBomIds.includes(id)) {
          failed.push({
            id,
            reason:
              'Bill of materials not found or does not belong to this business',
          });
        }
      });

      const validBomIds = existingBomIds;

      if (validBomIds.length === 0) {
        return {
          deletedIds: [],
          deleted: 0,
          message: 'No valid bill of materials found to delete',
          failed,
        };
      }

      // Use a transaction to ensure all deletions are atomic
      await this.db.transaction(async (tx) => {
        // Soft delete BOM lines
        await tx
          .update(bomLines)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(inArray(bomLines.bomId, validBomIds));

        // Soft delete BOMs
        await tx
          .update(billOfMaterials)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(inArray(billOfMaterials.id, validBomIds));
      });

      // Log bulk deletion activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.BILL_OF_MATERIALS,
        validBomIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { bomIds },
          failures: failed.map((f) => ({ id: f.id, error: f.reason })),
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedIds: validBomIds,
        deleted: validBomIds.length,
        message: `Successfully deleted ${validBomIds.length} bill of materials`,
        failed: failed.length > 0 ? failed : undefined,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete bill of materials: ${error.message}`,
      );
    }
  }

  async bulkUpdateBomStatus(
    userId: string,
    businessId: string | null,
    bomIds: string[],
    status: BomStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ id: string; reason: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!bomIds || bomIds.length === 0) {
        throw new BadRequestException('No bill of materials IDs provided');
      }

      // Validate that all BOMs exist and belong to the business
      const existingBoms = await this.db
        .select({
          id: billOfMaterials.id,
          bomName: billOfMaterials.bomName,
        })
        .from(billOfMaterials)
        .where(
          and(
            eq(billOfMaterials.businessId, businessId),
            inArray(billOfMaterials.id, bomIds),
            eq(billOfMaterials.isDeleted, false),
          ),
        );

      const existingBomIds = existingBoms.map((bom) => bom.id);
      const failed: Array<{ id: string; reason: string }> = [];

      // Identify missing BOMs
      bomIds.forEach((id) => {
        if (!existingBomIds.includes(id)) {
          failed.push({
            id,
            reason:
              'Bill of materials not found or does not belong to this business',
          });
        }
      });

      const validBomIds = existingBomIds;

      if (validBomIds.length === 0) {
        return {
          updated: 0,
          updatedIds: [],
          failed,
        };
      }

      // Update status for valid BOMs
      await this.db
        .update(billOfMaterials)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(billOfMaterials.id, validBomIds));

      // Log bulk status update activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_STATUS_CHANGE,
        EntityType.BILL_OF_MATERIALS,
        validBomIds,
        { status },
        userId,
        businessId,
        {
          filterCriteria: { bomIds, targetStatus: status },
          failures: failed.map((f) => ({ id: f.id, error: f.reason })),
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        updated: validBomIds.length,
        updatedIds: validBomIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update bill of materials status: ${error.message}`,
      );
    }
  }
}
