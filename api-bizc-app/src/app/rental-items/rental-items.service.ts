import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  UnauthorizedException,
  Inject,
} from '@nestjs/common';
import { and, eq, isNull, ilike, sql, asc, desc, inArray } from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
// import {
//   rentalItems,
//   rentalItemCategories,
//   accounts,
// } from '../drizzle/schema/schema';
import { CreateRentalItemDto } from './dto/create-rental-item.dto';
import { UpdateRentalItemDto } from './dto/update-rental-item.dto';
import { RentalItemDto } from './dto/rental-item.dto';
import { RentalItemSlimDto } from './dto/rental-item-slim.dto';
import { PaginatedRentalItemsResponseDto } from './dto/paginated-rental-items-response.dto';
import { BulkUpdateRentalItemsStatusDto } from './dto/bulk-update-rental-items-status.dto';
import { RentalItemStatus } from '../shared/types';
import { MediaService } from '../media/media.service';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { rentalItems } from '@app/drizzle/schema/rental-items.schema';
import { rentalItemCategories } from '@app/drizzle/schema/rental-item-categories.schema';
import { accounts } from '@app/drizzle/schema/accounts.schema';

// Define the missing type
interface RentalItemIdResponseDto {
  id: string;
}

@Injectable()
export class RentalItemsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly mediaService: MediaService,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createRentalItemDto: CreateRentalItemDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a rental item with the same item code already exists for this business
      const existingRentalItem = await this.db
        .select()
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            ilike(rentalItems.itemCode, createRentalItemDto.itemCode),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingRentalItem) {
        throw new ConflictException(
          `Rental item with code "${createRentalItemDto.itemCode}" already exists`,
        );
      }

      // Check if a rental item with the same item name already exists for this business
      const existingRentalItemByName = await this.db
        .select()
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            ilike(rentalItems.itemName, createRentalItemDto.itemName),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingRentalItemByName) {
        throw new ConflictException(
          `Rental item with name "${createRentalItemDto.itemName}" already exists`,
        );
      }

      let ogImageId: string | undefined;

      // Handle OG image upload first if provided
      if (ogImageFile) {
        try {
          const ogImageResult = await this.mediaService.uploadMedia(
            ogImageFile,
            'rental-items/og-images',
            businessId,
            userId,
          );
          ogImageId = ogImageResult.id;
        } catch (error) {
          console.warn('Failed to upload OG image:', error.message);
        }
      }

      // Create the rental item
      const [newRentalItem] = await this.db
        .insert(rentalItems)
        .values({
          businessId,
          itemCode: createRentalItemDto.itemCode,
          itemName: createRentalItemDto.itemName,
          description: createRentalItemDto.description,
          rentalRatePerDay: createRentalItemDto.rentalRatePerDay,
          rentalRatePerWeek: createRentalItemDto.rentalRatePerWeek,
          rentalRatePerMonth: createRentalItemDto.rentalRatePerMonth,
          minimumRentalPeriod: createRentalItemDto.minimumRentalPeriod || 1,
          categoryId: createRentalItemDto.categoryId,
          subCategoryId: createRentalItemDto.subCategoryId,
          assetCategoryId: createRentalItemDto.assetCategoryId,
          assetSubCategoryId: createRentalItemDto.assetSubCategoryId,
          fixedAssetAccountId: createRentalItemDto.fixedAssetAccountId,
          depreciationAccountId: createRentalItemDto.depreciationAccountId,
          expenseAccountId: createRentalItemDto.expenseAccountId,
          seoTitle: createRentalItemDto.seoTitle,
          seoDescription: createRentalItemDto.seoDescription,
          seoKeywords: createRentalItemDto.seoKeywords,
          ogImage: ogImageId,
          status: RentalItemStatus.ACTIVE,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      // Handle multiple image uploads if provided
      if (imageFiles && imageFiles.length > 0) {
        try {
          await this.mediaService.uploadMultipleMediaWithReference(
            imageFiles,
            'rental-items/images',
            businessId,
            userId,
            newRentalItem.id,
          );
        } catch (error) {
          console.warn('Failed to upload rental item images:', error.message);
        }
      }

      // Log activity
      await this.activityLogService.logCreate(
        newRentalItem.id,
        EntityType.RENTAL_ITEM,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newRentalItem.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create rental item: ${error.message}`,
      );
    }
  }

  async findAll(
    businessId: string | null,
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'itemName',
    sortOrder: 'asc' | 'desc' = 'asc',
    status?: RentalItemStatus,
    categoryId?: string,
  ): Promise<PaginatedRentalItemsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(rentalItems.businessId, businessId),
      eq(rentalItems.isDeleted, false),
    ];

    if (search) {
      whereConditions.push(
        sql`(${rentalItems.itemName} ILIKE ${`%${search}%`} OR ${rentalItems.itemCode} ILIKE ${`%${search}%`} OR ${rentalItems.description} ILIKE ${`%${search}%`})`,
      );
    }

    if (status) {
      whereConditions.push(eq(rentalItems.status, status));
    }

    if (categoryId) {
      whereConditions.push(eq(rentalItems.categoryId, categoryId));
    }

    // Build order by clause - fix type issue by being more specific
    let orderByClause;
    switch (sortBy) {
      case 'itemCode':
        orderByClause =
          sortOrder === 'desc'
            ? desc(rentalItems.itemCode)
            : asc(rentalItems.itemCode);
        break;
      case 'itemName':
        orderByClause =
          sortOrder === 'desc'
            ? desc(rentalItems.itemName)
            : asc(rentalItems.itemName);
        break;
      case 'createdAt':
        orderByClause =
          sortOrder === 'desc'
            ? desc(rentalItems.createdAt)
            : asc(rentalItems.createdAt);
        break;
      case 'updatedAt':
        orderByClause =
          sortOrder === 'desc'
            ? desc(rentalItems.updatedAt)
            : asc(rentalItems.updatedAt);
        break;
      default:
        orderByClause =
          sortOrder === 'desc'
            ? desc(rentalItems.itemName)
            : asc(rentalItems.itemName);
        break;
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(rentalItems)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;

    // Get rental items with related data
    const rentalItemsData = await this.db
      .select({
        id: rentalItems.id,
        businessId: rentalItems.businessId,
        itemCode: rentalItems.itemCode,
        itemName: rentalItems.itemName,
        description: rentalItems.description,
        rentalRatePerDay: rentalItems.rentalRatePerDay,
        rentalRatePerWeek: rentalItems.rentalRatePerWeek,
        rentalRatePerMonth: rentalItems.rentalRatePerMonth,
        minimumRentalPeriod: rentalItems.minimumRentalPeriod,
        categoryId: rentalItems.categoryId,
        typeName: rentalItemCategories.name,
        subCategoryId: rentalItems.subCategoryId,
        assetCategoryId: rentalItems.assetCategoryId,
        assetSubCategoryId: rentalItems.assetSubCategoryId,
        fixedAssetAccountId: rentalItems.fixedAssetAccountId,
        depreciationAccountId: rentalItems.depreciationAccountId,
        expenseAccountId: rentalItems.expenseAccountId,
        seoTitle: rentalItems.seoTitle,
        seoDescription: rentalItems.seoDescription,
        seoKeywords: rentalItems.seoKeywords,
        ogImage: rentalItems.ogImage,
        status: rentalItems.status,
        createdBy: rentalItems.createdBy,
        updatedBy: rentalItems.updatedBy,
        createdAt: rentalItems.createdAt,
        updatedAt: rentalItems.updatedAt,
      })
      .from(rentalItems)
      .leftJoin(
        rentalItemCategories,
        eq(rentalItems.categoryId, rentalItemCategories.id),
      )
      .where(and(...whereConditions))
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    // Get images for each rental item and generate signed URLs
    const data = await Promise.all(
      rentalItemsData.map(async (rentalItem) => {
        // Get rental item images
        const rentalItemImages = await this.mediaService.findByReferenceId(
          rentalItem.id,
          businessId,
        );

        // Generate signed URLs for images
        const imagesWithSignedUrls = await Promise.all(
          rentalItemImages.map(async (img) => {
            try {
              const signedUrl =
                await this.mediaService.generateSignedUrlForMedia(
                  img.id,
                  businessId,
                  'rental-items',
                  60, // 60 minutes expiration
                );
              return {
                id: img.id,
                publicUrl: signedUrl,
                originalName: img.originalName,
              };
            } catch (error) {
              console.warn(
                `Failed to generate signed URL for rental item image ${img.id}:`,
                error.message,
              );
              return {
                id: img.id,
                publicUrl: img.publicUrl,
                originalName: img.originalName,
              };
            }
          }),
        );

        // Get OG image signed URL if exists
        let ogImageUrl: string | undefined;
        if (rentalItem.ogImage) {
          try {
            ogImageUrl = await this.mediaService.generateSignedUrlForMedia(
              rentalItem.ogImage,
              businessId,
              'rental-items',
              60,
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for rental item OG image ${rentalItem.ogImage}:`,
              error.message,
            );
          }
        }

        return {
          id: rentalItem.id,
          businessId: rentalItem.businessId,
          itemCode: rentalItem.itemCode,
          itemName: rentalItem.itemName,
          description: rentalItem.description,
          rentalRatePerDay: rentalItem.rentalRatePerDay,
          rentalRatePerWeek: rentalItem.rentalRatePerWeek,
          rentalRatePerMonth: rentalItem.rentalRatePerMonth,
          minimumRentalPeriod: rentalItem.minimumRentalPeriod,
          categoryId: rentalItem.categoryId,
          typeName: rentalItem.typeName || undefined,
          subCategoryId: rentalItem.subCategoryId,
          assetCategoryId: rentalItem.assetCategoryId,
          assetSubCategoryId: rentalItem.assetSubCategoryId,
          fixedAssetAccountId: rentalItem.fixedAssetAccountId,
          depreciationAccountId: rentalItem.depreciationAccountId,
          expenseAccountId: rentalItem.expenseAccountId,
          seoTitle: rentalItem.seoTitle,
          seoDescription: rentalItem.seoDescription,
          seoKeywords: rentalItem.seoKeywords,
          ogImage: ogImageUrl,
          images: imagesWithSignedUrls,
          status: rentalItem.status,
          createdBy: rentalItem.createdBy,
          updatedBy: rentalItem.updatedBy,
          createdAt: rentalItem.createdAt,
          updatedAt: rentalItem.updatedAt,
        };
      }),
    );

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, businessId: string | null): Promise<RentalItemDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const rentalItemData = await this.db
      .select({
        id: rentalItems.id,
        businessId: rentalItems.businessId,
        itemCode: rentalItems.itemCode,
        itemName: rentalItems.itemName,
        description: rentalItems.description,
        rentalRatePerDay: rentalItems.rentalRatePerDay,
        rentalRatePerWeek: rentalItems.rentalRatePerWeek,
        rentalRatePerMonth: rentalItems.rentalRatePerMonth,
        minimumRentalPeriod: rentalItems.minimumRentalPeriod,
        categoryId: rentalItems.categoryId,
        typeName: rentalItemCategories.name,
        subCategoryId: rentalItems.subCategoryId,
        assetCategoryId: rentalItems.assetCategoryId,
        assetSubCategoryId: rentalItems.assetSubCategoryId,
        fixedAssetAccountId: rentalItems.fixedAssetAccountId,
        fixedAssetAccountName: accounts.name,
        depreciationAccountId: rentalItems.depreciationAccountId,
        expenseAccountId: rentalItems.expenseAccountId,
        seoTitle: rentalItems.seoTitle,
        seoDescription: rentalItems.seoDescription,
        seoKeywords: rentalItems.seoKeywords,
        ogImage: rentalItems.ogImage,
        status: rentalItems.status,
        createdBy: rentalItems.createdBy,
        updatedBy: rentalItems.updatedBy,
        createdAt: rentalItems.createdAt,
        updatedAt: rentalItems.updatedAt,
      })
      .from(rentalItems)
      .leftJoin(
        rentalItemCategories,
        eq(rentalItems.categoryId, rentalItemCategories.id),
      )
      .leftJoin(accounts, eq(rentalItems.fixedAssetAccountId, accounts.id))
      .where(
        and(
          eq(rentalItems.id, id),
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!rentalItemData) {
      throw new NotFoundException('Rental item not found');
    }

    // Get rental item images and generate signed URLs for all
    const rentalItemImages = await this.mediaService.findByReferenceId(
      id,
      businessId,
    );

    // Generate signed URLs for all rental item images
    const imagesWithSignedUrls = await Promise.all(
      rentalItemImages.map(async (img) => {
        try {
          const signedUrl = await this.mediaService.generateSignedUrlForMedia(
            img.id,
            businessId,
            'rental-items',
            60, // 60 minutes expiration
          );
          return {
            id: img.id,
            publicUrl: signedUrl,
            originalName: img.originalName,
          };
        } catch (error) {
          console.warn(
            `Failed to generate signed URL for rental item image ${img.id}:`,
            error.message,
          );
          return {
            id: img.id,
            publicUrl: img.publicUrl,
            originalName: img.originalName,
          };
        }
      }),
    );

    // Get OG image signed URL if exists
    let ogImageUrl: string | undefined;
    if (rentalItemData.ogImage) {
      try {
        ogImageUrl = await this.mediaService.generateSignedUrlForMedia(
          rentalItemData.ogImage,
          businessId,
          'rental-items',
          60,
        );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for rental item OG image ${rentalItemData.ogImage}:`,
          error.message,
        );
      }
    }

    return {
      id: rentalItemData.id,
      businessId: rentalItemData.businessId,
      itemCode: rentalItemData.itemCode,
      itemName: rentalItemData.itemName,
      description: rentalItemData.description,
      rentalRatePerDay: rentalItemData.rentalRatePerDay,
      rentalRatePerWeek: rentalItemData.rentalRatePerWeek,
      rentalRatePerMonth: rentalItemData.rentalRatePerMonth,
      minimumRentalPeriod: rentalItemData.minimumRentalPeriod,
      categoryId: rentalItemData.categoryId,
      subCategoryId: rentalItemData.subCategoryId,
      assetCategoryId: rentalItemData.assetCategoryId,
      assetSubCategoryId: rentalItemData.assetSubCategoryId,
      fixedAssetAccountId: rentalItemData.fixedAssetAccountId,
      fixedAssetAccountName: rentalItemData.fixedAssetAccountName || undefined,
      depreciationAccountId: rentalItemData.depreciationAccountId,
      expenseAccountId: rentalItemData.expenseAccountId,
      seoTitle: rentalItemData.seoTitle,
      seoDescription: rentalItemData.seoDescription,
      seoKeywords: rentalItemData.seoKeywords,
      ogImage: ogImageUrl,
      images: imagesWithSignedUrls,
      status: rentalItemData.status,
      createdBy: rentalItemData.createdBy,
      updatedBy: rentalItemData.updatedBy,
      createdAt: rentalItemData.createdAt,
      updatedAt: rentalItemData.updatedAt,
    };
  }

  async update(
    id: string,
    userId: string,
    businessId: string | null,
    updateRentalItemDto: UpdateRentalItemDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if rental item exists
    const existingRentalItem = await this.db
      .select()
      .from(rentalItems)
      .where(
        and(
          eq(rentalItems.id, id),
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingRentalItem) {
      throw new NotFoundException('Rental item not found');
    }

    // Check for duplicate item code if being updated
    if (
      updateRentalItemDto.itemCode &&
      updateRentalItemDto.itemCode !== existingRentalItem.itemCode
    ) {
      const duplicateItemCode = await this.db
        .select()
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            ilike(rentalItems.itemCode, updateRentalItemDto.itemCode),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (duplicateItemCode) {
        throw new ConflictException(
          `Rental item with code "${updateRentalItemDto.itemCode}" already exists`,
        );
      }
    }

    // Check for duplicate item name if being updated
    if (
      updateRentalItemDto.itemName &&
      updateRentalItemDto.itemName !== existingRentalItem.itemName
    ) {
      const duplicateItemName = await this.db
        .select()
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            ilike(rentalItems.itemName, updateRentalItemDto.itemName),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (duplicateItemName) {
        throw new ConflictException(
          `Rental item with name "${updateRentalItemDto.itemName}" already exists`,
        );
      }
    }

    let ogImageId: string | undefined = existingRentalItem.ogImage;

    // Handle OG image upload if provided
    if (ogImageFile) {
      try {
        // Delete existing OG image if it exists
        if (existingRentalItem.ogImage) {
          await this.mediaService.deleteMedia(
            existingRentalItem.ogImage,
            businessId,
          );
        }

        const ogImageResult = await this.mediaService.uploadMedia(
          ogImageFile,
          'rental-items/og-images',
          businessId,
          userId,
        );
        ogImageId = ogImageResult.id;
      } catch (error) {
        console.warn('Failed to upload OG image:', error.message);
      }
    }

    // Update the rental item
    const [updatedRentalItem] = await this.db
      .update(rentalItems)
      .set({
        itemCode: updateRentalItemDto.itemCode,
        itemName: updateRentalItemDto.itemName,
        description: updateRentalItemDto.description,
        rentalRatePerDay: updateRentalItemDto.rentalRatePerDay,
        rentalRatePerWeek: updateRentalItemDto.rentalRatePerWeek,
        rentalRatePerMonth: updateRentalItemDto.rentalRatePerMonth,
        minimumRentalPeriod: updateRentalItemDto.minimumRentalPeriod,
        categoryId: updateRentalItemDto.categoryId,
        subCategoryId: updateRentalItemDto.subCategoryId,
        assetCategoryId: updateRentalItemDto.assetCategoryId,
        assetSubCategoryId: updateRentalItemDto.assetSubCategoryId,
        fixedAssetAccountId: updateRentalItemDto.fixedAssetAccountId,
        depreciationAccountId: updateRentalItemDto.depreciationAccountId,
        expenseAccountId: updateRentalItemDto.expenseAccountId,
        seoTitle: updateRentalItemDto.seoTitle,
        seoDescription: updateRentalItemDto.seoDescription,
        seoKeywords: updateRentalItemDto.seoKeywords,
        ogImage: ogImageId,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(rentalItems.id, id),
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      )
      .returning();

    // Handle multiple image uploads if provided
    if (imageFiles && imageFiles.length > 0) {
      try {
        // Delete existing images
        const existingImages = await this.mediaService.findByReferenceId(
          id,
          businessId,
        );
        for (const img of existingImages) {
          await this.mediaService.deleteMedia(img.id, businessId);
        }

        // Upload new images
        await this.mediaService.uploadMultipleMediaWithReference(
          imageFiles,
          'rental-items/images',
          businessId,
          userId,
          id,
        );
      } catch (error) {
        console.warn('Failed to update rental item images:', error.message);
      }
    }

    // Log activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.RENTAL_ITEM,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id: updatedRentalItem.id };
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createRentalItemDto: CreateRentalItemDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<RentalItemIdResponseDto> {
    const result = await this.create(
      userId,
      businessId,
      createRentalItemDto,
      imageFiles,
      ogImageFile,
    );
    return { id: result.id };
  }

  async updateAndReturnId(
    id: string,
    userId: string,
    businessId: string | null,
    updateRentalItemDto: UpdateRentalItemDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<RentalItemIdResponseDto> {
    const result = await this.update(
      id,
      userId,
      businessId,
      updateRentalItemDto,
      imageFiles,
      ogImageFile,
    );
    return { id: result.id };
  }

  async remove(
    id: string,
    userId: string,
    businessId: string | null,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if rental item exists
    const existingRentalItem = await this.db
      .select()
      .from(rentalItems)
      .where(
        and(
          eq(rentalItems.id, id),
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingRentalItem) {
      throw new NotFoundException('Rental item not found');
    }

    // Soft delete the rental item
    await this.db
      .update(rentalItems)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(rentalItems.id, id),
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      );

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.RENTAL_ITEM,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );
  }

  async findAllSlim(businessId: string | null): Promise<RentalItemSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const rentalItemsData = await this.db
      .select({
        id: rentalItems.id,
        itemCode: rentalItems.itemCode,
        itemName: rentalItems.itemName,
        rentalRatePerDay: rentalItems.rentalRatePerDay,
        rentalRatePerWeek: rentalItems.rentalRatePerWeek,
        rentalRatePerMonth: rentalItems.rentalRatePerMonth,
        minimumRentalPeriod: rentalItems.minimumRentalPeriod,
      })
      .from(rentalItems)
      .where(
        and(
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.status, RentalItemStatus.ACTIVE),
          eq(rentalItems.isDeleted, false),
        ),
      )
      .orderBy(asc(rentalItems.itemName));

    // Get images for each rental item
    const rentalItemIds = rentalItemsData.map((rentalItem) => rentalItem.id);
    const imagesData: any[] = [];

    if (rentalItemIds.length > 0) {
      for (const rentalItemId of rentalItemIds) {
        const rentalItemImages = await this.mediaService.findByReferenceId(
          rentalItemId,
          businessId,
        );
        imagesData.push(
          ...rentalItemImages.map((img) => ({
            ...img,
            referenceId: rentalItemId,
          })),
        );
      }
    }

    return rentalItemsData.map((rentalItem) => {
      const rentalItemImages = imagesData.filter(
        (img) => img.referenceId === rentalItem.id,
      );

      return {
        id: rentalItem.id,
        itemCode: rentalItem.itemCode,
        itemName: rentalItem.itemName,
        rentalRatePerDay: rentalItem.rentalRatePerDay,
        rentalRatePerWeek: rentalItem.rentalRatePerWeek,
        rentalRatePerMonth: rentalItem.rentalRatePerMonth,
        minimumRentalPeriod: rentalItem.minimumRentalPeriod,
        images: rentalItemImages.map((img) => ({
          id: img.id,
          publicUrl: img.publicUrl,
          originalName: img.originalName,
        })),
      };
    });
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    bulkUpdateDto: BulkUpdateRentalItemsStatusDto,
    metadata?: ActivityMetadata,
  ): Promise<void> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const rentalItemIds = bulkUpdateDto.rentalItems.map(
      (item) => item.rentalItemId,
    );

    // Verify all rental items exist and belong to the business
    const existingRentalItems = await this.db
      .select({ id: rentalItems.id, itemName: rentalItems.itemName })
      .from(rentalItems)
      .where(
        and(
          sql`${rentalItems.id} = ANY(${rentalItemIds})`,
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      );

    if (existingRentalItems.length !== rentalItemIds.length) {
      throw new NotFoundException('One or more rental items not found');
    }

    // Update status for all rental items
    await this.db
      .update(rentalItems)
      .set({
        status: bulkUpdateDto.status,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          sql`${rentalItems.id} = ANY(${rentalItemIds})`,
          eq(rentalItems.businessId, businessId),
          eq(rentalItems.isDeleted, false),
        ),
      );

    // Log bulk status update activity
    const updatedIds = existingRentalItems.map((item) => item.id);
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_STATUS_CHANGE,
      EntityType.RENTAL_ITEM,
      updatedIds,
      { status: bulkUpdateDto.status },
      userId,
      businessId,
      {
        filterCriteria: { rentalItemIds, targetStatus: bulkUpdateDto.status },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );
  }

  /**
   * Update global positions for rental items
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateRentalItemGlobalPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate that all rental items exist and belong to the business
      const rentalItemIds = updates.map((update) => update.id);
      const existingRentalItems = await this.db
        .select({ id: rentalItems.id })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            sql`${rentalItems.id} = ANY(${rentalItemIds})`,
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (existingRentalItems.length !== updates.length) {
        throw new BadRequestException(
          'One or more rental items not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateGlobalPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log bulk position update activity
        const updatedIds = updates.map((update) => update.id);
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.RENTAL_ITEM,
          updatedIds,
          { globalPositionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeGlobalPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update rental item global positions: ${error.message}`,
      );
    }
  }

  /**
   * Update category positions for rental items within their specific category
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateRentalItemCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate that all rental items exist and belong to the business
      const rentalItemIds = updates.map((update) => update.id);
      const existingRentalItems = await this.db
        .select({ id: rentalItems.id })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            sql`${rentalItems.id} = ANY(${rentalItemIds})`,
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (existingRentalItems.length !== updates.length) {
        throw new BadRequestException(
          'One or more rental items not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateCategoryPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log bulk position update activity
        const updatedIds = updates.map((update) => update.id);
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.RENTAL_ITEM,
          updatedIds,
          { categoryPositionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeCategoryPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update rental item category positions: ${error.message}`,
      );
    }
  }

  /**
   * Update sub-category positions for rental items within their specific sub-category
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateRentalItemSubCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate that all rental items exist and belong to the business
      const rentalItemIds = updates.map((update) => update.id);
      const existingRentalItems = await this.db
        .select({ id: rentalItems.id })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            sql`${rentalItems.id} = ANY(${rentalItemIds})`,
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (existingRentalItems.length !== updates.length) {
        throw new BadRequestException(
          'One or more rental items not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateSubCategoryPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log bulk position update activity
        const updatedIds = updates.map((update) => update.id);
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.RENTAL_ITEM,
          updatedIds,
          { subCategoryPositionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeSubCategoryPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update rental item sub-category positions: ${error.message}`,
      );
    }
  }

  /**
   * Optimized batch global position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateGlobalPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(rentalItems)
            .set({
              globalPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(rentalItems.id, update.id),
                eq(rentalItems.businessId, businessId),
                eq(rentalItems.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(rentalItems)
          .set({
            globalPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(rentalItems.id, update.id),
              eq(rentalItems.businessId, businessId),
              eq(rentalItems.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update global positions:', error.message);
      throw error;
    }
  }

  /**
   * Optimized batch category position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateCategoryPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(rentalItems)
            .set({
              categoryPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(rentalItems.id, update.id),
                eq(rentalItems.businessId, businessId),
                eq(rentalItems.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(rentalItems)
          .set({
            categoryPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(rentalItems.id, update.id),
              eq(rentalItems.businessId, businessId),
              eq(rentalItems.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update category positions:', error.message);
      throw error;
    }
  }

  /**
   * Optimized batch sub-category position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateSubCategoryPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(rentalItems)
            .set({
              subCategoryPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(rentalItems.id, update.id),
                eq(rentalItems.businessId, businessId),
                eq(rentalItems.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(rentalItems)
          .set({
            subCategoryPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(rentalItems.id, update.id),
              eq(rentalItems.businessId, businessId),
              eq(rentalItems.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn(
        'Failed to batch update sub-category positions:',
        error.message,
      );
      throw error;
    }
  }

  /**
   * Conditionally normalize global positions only when needed
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizeGlobalPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          positions: sql<
            number[]
          >`array_agg(${rentalItems.globalPosition} ORDER BY ${rentalItems.globalPosition})`,
          count: sql<number>`count(*)`,
        })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (positionCheck.length === 0) return;

      const { positions, count: totalCount } = positionCheck[0];

      // Check for gaps or duplicates
      const needsNormalization = this.checkIfNormalizationNeeded(
        positions,
        totalCount,
      );

      if (!needsNormalization) return;

      // Get all rental items ordered by current global position
      const allRentalItems = await tx
        .select({
          id: rentalItems.id,
          globalPosition: rentalItems.globalPosition,
        })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .orderBy(rentalItems.globalPosition, rentalItems.id);

      // Normalize positions
      const updates = allRentalItems
        .map((rentalItem, index) => ({
          id: rentalItem.id,
          position: index + 1,
        }))
        .filter(
          (update, index) =>
            allRentalItems[index].globalPosition !== update.position,
        );

      if (updates.length > 0) {
        await this.batchUpdateGlobalPositions(tx, businessId, updates);
      }
    } catch (error) {
      console.warn('Failed to normalize global positions:', error.message);
      // Don't throw error for normalization failures
    }
  }

  /**
   * Conditionally normalize category positions only when needed
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizeCategoryPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          categoryId: rentalItems.categoryId,
          positions: sql<
            number[]
          >`array_agg(${rentalItems.categoryPosition} ORDER BY ${rentalItems.categoryPosition})`,
          count: sql<number>`count(*)`,
        })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .groupBy(rentalItems.categoryId);

      // Process each category group
      for (const group of positionCheck) {
        const needsNormalization = this.checkIfNormalizationNeeded(
          group.positions,
          group.count,
        );

        if (!needsNormalization) continue;

        // Get all rental items in this category ordered by current position
        const categoryRentalItems = await tx
          .select({
            id: rentalItems.id,
            categoryPosition: rentalItems.categoryPosition,
          })
          .from(rentalItems)
          .where(
            and(
              eq(rentalItems.businessId, businessId),
              group.categoryId
                ? eq(rentalItems.categoryId, group.categoryId)
                : isNull(rentalItems.categoryId),
              eq(rentalItems.isDeleted, false),
            ),
          )
          .orderBy(rentalItems.categoryPosition, rentalItems.id);

        // Normalize positions for this category
        const updates = categoryRentalItems
          .map((rentalItem: any, index: number) => ({
            id: rentalItem.id,
            position: index + 1,
          }))
          .filter(
            (update: any, index: number) =>
              categoryRentalItems[index].categoryPosition !== update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdateCategoryPositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn('Failed to normalize category positions:', error.message);
      // Don't throw error for normalization failures
    }
  }

  /**
   * Conditionally normalize sub-category positions only when needed
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizeSubCategoryPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          subCategoryId: rentalItems.subCategoryId,
          positions: sql<
            number[]
          >`array_agg(${rentalItems.subCategoryPosition} ORDER BY ${rentalItems.subCategoryPosition})`,
          count: sql<number>`count(*)`,
        })
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            eq(rentalItems.isDeleted, false),
          ),
        )
        .groupBy(rentalItems.subCategoryId);

      // Process each sub-category group
      for (const group of positionCheck) {
        const needsNormalization = this.checkIfNormalizationNeeded(
          group.positions,
          group.count,
        );

        if (!needsNormalization) continue;

        // Get all rental items in this sub-category ordered by current position
        const subCategoryRentalItems = await tx
          .select({
            id: rentalItems.id,
            subCategoryPosition: rentalItems.subCategoryPosition,
          })
          .from(rentalItems)
          .where(
            and(
              eq(rentalItems.businessId, businessId),
              group.subCategoryId
                ? eq(rentalItems.subCategoryId, group.subCategoryId)
                : isNull(rentalItems.subCategoryId),
              eq(rentalItems.isDeleted, false),
            ),
          )
          .orderBy(rentalItems.subCategoryPosition, rentalItems.id);

        // Normalize positions for this sub-category
        const updates = subCategoryRentalItems
          .map((rentalItem: any, index: number) => ({
            id: rentalItem.id,
            position: index + 1,
          }))
          .filter(
            (update: any, index: number) =>
              subCategoryRentalItems[index].subCategoryPosition !==
              update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdateSubCategoryPositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn(
        'Failed to normalize sub-category positions:',
        error.message,
      );
      // Don't throw error for normalization failures
    }
  }

  /**
   * Check if position normalization is needed
   * @param positions - Array of current positions
   * @param totalCount - Total count of items
   */
  private checkIfNormalizationNeeded(
    positions: number[],
    totalCount: number,
  ): boolean {
    if (!positions || positions.length === 0) return false;

    // Check for duplicates
    const uniquePositions = new Set(positions);
    if (uniquePositions.size !== positions.length) return true;

    // Check for gaps (positions should be 1, 2, 3, ..., totalCount)
    const sortedPositions = [...positions].sort((a, b) => a - b);
    for (let i = 0; i < sortedPositions.length; i++) {
      if (sortedPositions[i] !== i + 1) return true;
    }

    return false;
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createRentalItemsDto: CreateRentalItemDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<RentalItemDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createRentalItemsDto || createRentalItemsDto.length === 0) {
        throw new BadRequestException('No rental items provided for creation');
      }

      // Validate that if images are provided, they don't exceed the number of rental items
      if (imageFiles && imageFiles.length > createRentalItemsDto.length) {
        throw new BadRequestException(
          'Number of images cannot exceed number of rental items',
        );
      }

      // Validate for duplicate item codes in the request
      const requestItemCodes = createRentalItemsDto
        .map((dto) => dto.itemCode.toLowerCase())
        .filter((code) => code);

      const duplicateItemCodes = requestItemCodes.filter(
        (code, index) => requestItemCodes.indexOf(code) !== index,
      );

      if (duplicateItemCodes.length > 0) {
        throw new BadRequestException(
          `Duplicate rental item codes found in request: ${duplicateItemCodes.join(', ')}`,
        );
      }

      // Validate for duplicate item names in the request
      const requestItemNames = createRentalItemsDto
        .map((dto) => dto.itemName.toLowerCase())
        .filter((name) => name);

      const duplicateItemNames = requestItemNames.filter(
        (name, index) => requestItemNames.indexOf(name) !== index,
      );

      if (duplicateItemNames.length > 0) {
        throw new BadRequestException(
          `Duplicate rental item names found in request: ${duplicateItemNames.join(', ')}`,
        );
      }

      // Check if any rental items with the same item codes already exist for this business
      const existingRentalItemsByCode = await this.db
        .select()
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            sql`LOWER(${rentalItems.itemCode}) IN (${requestItemCodes.map((code) => `'${code}'`).join(',')})`,
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (existingRentalItemsByCode.length > 0) {
        const existingCodes = existingRentalItemsByCode.map(
          (ri) => ri.itemCode,
        );
        throw new ConflictException(
          `Rental items with the following codes already exist: ${existingCodes.join(', ')}`,
        );
      }

      // Check if any rental items with the same item names already exist for this business
      const existingRentalItemsByName = await this.db
        .select()
        .from(rentalItems)
        .where(
          and(
            eq(rentalItems.businessId, businessId),
            sql`LOWER(${rentalItems.itemName}) IN (${requestItemNames.map((name) => `'${name}'`).join(',')})`,
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (existingRentalItemsByName.length > 0) {
        const existingNames = existingRentalItemsByName.map(
          (ri) => ri.itemName,
        );
        throw new ConflictException(
          `Rental items with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      const createdRentalItems: RentalItemDto[] = [];

      // Use a transaction to ensure all rental items are created or none are
      await this.db.transaction(async (tx) => {
        for (let i = 0; i < createRentalItemsDto.length; i++) {
          const createRentalItemDto = createRentalItemsDto[i];

          // Get the next position for this rental item
          const maxPositionResult = await tx
            .select({
              maxPosition: sql<number>`COALESCE(MAX(${rentalItems.globalPosition}), 0)`,
            })
            .from(rentalItems)
            .where(
              and(
                eq(rentalItems.businessId, businessId),
                eq(rentalItems.isDeleted, false),
              ),
            );

          const nextGlobalPosition =
            (maxPositionResult[0]?.maxPosition || 0) + 1;

          // Create the rental item
          const [newRentalItem] = await tx
            .insert(rentalItems)
            .values({
              businessId,
              itemCode: createRentalItemDto.itemCode,
              itemName: createRentalItemDto.itemName,
              description: createRentalItemDto.description,
              categoryId: createRentalItemDto.categoryId,
              subCategoryId: createRentalItemDto.subCategoryId,
              assetCategoryId: createRentalItemDto.assetCategoryId,
              assetSubCategoryId: createRentalItemDto.assetSubCategoryId,
              fixedAssetAccountId: createRentalItemDto.fixedAssetAccountId,
              depreciationAccountId: createRentalItemDto.depreciationAccountId,
              expenseAccountId: createRentalItemDto.expenseAccountId,
              rentalRatePerDay: createRentalItemDto.rentalRatePerDay,
              rentalRatePerWeek: createRentalItemDto.rentalRatePerWeek,
              rentalRatePerMonth: createRentalItemDto.rentalRatePerMonth,
              minimumRentalPeriod: createRentalItemDto.minimumRentalPeriod,
              globalPosition: nextGlobalPosition,
              categoryPosition: 1, // Will be updated if needed
              subCategoryPosition: 1, // Will be updated if needed
              seoTitle: createRentalItemDto.seoTitle,
              seoDescription: createRentalItemDto.seoDescription,
              seoKeywords: createRentalItemDto.seoKeywords,
              createdBy: userId,
              updatedBy: userId,
              status: RentalItemStatus.ACTIVE,
            })
            .returning();

          // Store for bulk logging later

          // Create a basic DTO for bulk response (without image processing)
          createdRentalItems.push({
            id: newRentalItem.id,
            businessId: newRentalItem.businessId,
            itemCode: newRentalItem.itemCode,
            itemName: newRentalItem.itemName,
            description: newRentalItem.description,
            rentalRatePerDay: newRentalItem.rentalRatePerDay,
            rentalRatePerWeek: newRentalItem.rentalRatePerWeek,
            rentalRatePerMonth: newRentalItem.rentalRatePerMonth,
            minimumRentalPeriod: newRentalItem.minimumRentalPeriod,
            categoryId: newRentalItem.categoryId,
            subCategoryId: newRentalItem.subCategoryId,
            assetCategoryId: newRentalItem.assetCategoryId,
            assetSubCategoryId: newRentalItem.assetSubCategoryId,
            fixedAssetAccountId: newRentalItem.fixedAssetAccountId,
            depreciationAccountId: newRentalItem.depreciationAccountId,
            expenseAccountId: newRentalItem.expenseAccountId,
            seoTitle: newRentalItem.seoTitle,
            seoDescription: newRentalItem.seoDescription,
            seoKeywords: newRentalItem.seoKeywords,
            status: newRentalItem.status,
            images: [], // Empty for bulk create
            createdBy: newRentalItem.createdBy,
            updatedBy: newRentalItem.updatedBy,
            createdAt: newRentalItem.createdAt,
            updatedAt: newRentalItem.updatedAt,
          });
        }
      });

      // Log bulk create operation
      const createdIds = createdRentalItems.map((item) => item.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.RENTAL_ITEM,
        createdIds,
        {
          names: createRentalItemsDto.map((dto) => dto.itemName),
          status: RentalItemStatus.ACTIVE,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createRentalItemsDto.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return createdRentalItems;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create rental items: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createRentalItemsDto: CreateRentalItemDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const rentalItems = await this.bulkCreate(
      userId,
      businessId,
      createRentalItemsDto,
      imageFiles,
      metadata,
    );
    return { ids: rentalItems.map((rentalItem) => rentalItem.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    rentalItemIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!rentalItemIds || rentalItemIds.length === 0) {
        throw new BadRequestException(
          'No rental item IDs provided for deletion',
        );
      }

      // Get all rental items that exist and belong to the business
      const existingRentalItems = await this.db
        .select({
          id: rentalItems.id,
          itemName: rentalItems.itemName,
          businessId: rentalItems.businessId,
        })
        .from(rentalItems)
        .where(
          and(
            inArray(rentalItems.id, rentalItemIds),
            eq(rentalItems.businessId, businessId),
            eq(rentalItems.isDeleted, false),
          ),
        );

      if (existingRentalItems.length === 0) {
        throw new NotFoundException('No valid rental items found for deletion');
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const rentalItem of existingRentalItems) {
          // Soft delete the rental item
          await tx
            .update(rentalItems)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(rentalItems.id, rentalItem.id));

          deletedIds.push(rentalItem.id);
        }
      });

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.RENTAL_ITEM,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { rentalItemIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} rental items`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete rental items: ${error.message}`,
      );
    }
  }
}
