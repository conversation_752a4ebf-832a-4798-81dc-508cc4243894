import { Injectable, Inject, UnauthorizedException } from '@nestjs/common';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { and, eq, ilike, isNull } from 'drizzle-orm';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { customers } from '../drizzle/schema/customers.schema';
import { projects } from '../drizzle/schema/projects.schema';
import {
  UnifiedSearchResultDto,
  UnifiedSearchProjectDto,
  UnifiedSearchEntityType,
  UnifiedSearchResponseDto,
} from './dto/unified-search-response.dto';
import { ActivityLogService } from '../activity-log/activity-log.service';

@Injectable()
export class UnifiedSearchService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  /**
   * Search across all business entities with hierarchical grouping for projects under customers
   * Returns unified results for autocomplete functionality
   */
  async searchEntities(
    userId: string,
    businessId: string | null,
    searchTerm?: string,
    limit: number = 20,
  ): Promise<UnifiedSearchResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Build search conditions
    const baseConditions = (table: any) => [
      eq(table.businessId, businessId),
      isNull(table.deletedAt),
    ];

    // Calculate per-entity limits to distribute the total limit
    const perEntityLimit = Math.ceil(limit / 4);

    // Search suppliers
    const supplierConditions = baseConditions(suppliers);
    if (searchTerm) {
      supplierConditions.push(ilike(suppliers.displayName, `%${searchTerm}%`));
    }

    const supplierResults = await this.db
      .select({
        id: suppliers.id,
        name: suppliers.displayName,
      })
      .from(suppliers)
      .where(and(...supplierConditions))
      .limit(perEntityLimit);

    // Search staff members
    const staffConditions = baseConditions(staffMembers);
    if (searchTerm) {
      staffConditions.push(ilike(staffMembers.displayName, `%${searchTerm}%`));
    }

    const staffResults = await this.db
      .select({
        id: staffMembers.id,
        name: staffMembers.displayName,
      })
      .from(staffMembers)
      .where(and(...staffConditions))
      .limit(perEntityLimit);

    // Search projects with customer information for hierarchical grouping
    const projectConditions = baseConditions(projects);
    if (searchTerm) {
      projectConditions.push(ilike(projects.name, `%${searchTerm}%`));
    }

    const projectResults = await this.db
      .select({
        id: projects.id,
        name: projects.name,
        customerId: projects.customerId,
        customerName: customers.customerDisplayName,
      })
      .from(projects)
      .leftJoin(customers, eq(projects.customerId, customers.id))
      .where(and(...projectConditions))
      .limit(perEntityLimit);

    // Search customers and include their projects if any were found
    const customerConditions = baseConditions(customers);
    if (searchTerm) {
      customerConditions.push(
        ilike(customers.customerDisplayName, `%${searchTerm}%`),
      );
    }

    const customerResults = await this.db
      .select({
        id: customers.id,
        name: customers.customerDisplayName,
      })
      .from(customers)
      .where(and(...customerConditions))
      .limit(perEntityLimit);

    // Build hierarchical results
    const results: UnifiedSearchResultDto[] = [];
    const customerProjectMap = new Map<string, UnifiedSearchProjectDto[]>();

    // Group projects by customer
    for (const project of projectResults) {
      if (project.customerId) {
        if (!customerProjectMap.has(project.customerId)) {
          customerProjectMap.set(project.customerId, []);
        }
        const projectList = customerProjectMap.get(project.customerId);
        if (projectList) {
          projectList.push({
            id: project.id,
            name: project.name,
            type: UnifiedSearchEntityType.PROJECTS,
          });
        }
      } else {
        // Projects without customers are added as standalone items
        results.push({
          id: project.id,
          name: project.name,
          type: UnifiedSearchEntityType.PROJECTS,
        });
      }
    }

    // Add suppliers
    results.push(
      ...supplierResults.map((item) => ({
        id: item.id,
        name: item.name,
        type: UnifiedSearchEntityType.SUPPLIERS,
      })),
    );

    // Add staff members
    results.push(
      ...staffResults.map((item) => ({
        id: item.id,
        name: item.name,
        type: UnifiedSearchEntityType.STAFF_MEMBERS,
      })),
    );

    // Add customers with their associated projects
    const customerSet = new Set(customerResults.map((c) => c.id));

    // Add customers from search results
    for (const customer of customerResults) {
      const customerProjects = customerProjectMap.get(customer.id) || [];
      results.push({
        id: customer.id,
        name: customer.name,
        type: UnifiedSearchEntityType.CUSTOMERS,
        projects: customerProjects.length > 0 ? customerProjects : undefined,
      });
    }

    // Add customers that weren't in search results but have matching projects
    for (const [customerId, projects] of customerProjectMap.entries()) {
      if (!customerSet.has(customerId)) {
        // Find the customer name from project results
        const projectWithCustomer = projectResults.find(
          (p) => p.customerId === customerId,
        );
        if (projectWithCustomer && projectWithCustomer.customerName) {
          results.push({
            id: customerId,
            name: projectWithCustomer.customerName,
            type: UnifiedSearchEntityType.CUSTOMERS,
            projects: projects,
          });
        }
      }
    }

    // Apply total limit to final results
    const limitedResults = results.slice(0, limit);

    return {
      data: limitedResults,
      total: limitedResults.length,
    };
  }
}
