import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateReferralDto } from './dto/create-referral.dto';
import { UpdateReferralDto } from './dto/update-referral.dto';
import { ReferralDto } from './dto/referral.dto';
import { ReferralSlimDto } from './dto/referral-slim.dto';
import { ReferralListDto } from './dto/referral-list.dto';
import { PaginatedReferralsResponseDto } from './dto/paginated-referrals-response.dto';
import { referrals } from '../drizzle/schema/referrals.schema';
import { promoCodes } from '../drizzle/schema/promo-codes.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ReferralStatus } from '../shared/types';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { UsersService } from '../users/users.service';

@Injectable()
export class ReferralsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
    private usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createReferralDto: CreateReferralDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a referral with the same title already exists for this business
      // Using ilike for case-insensitive comparison
      const existingReferral = await this.db
        .select()
        .from(referrals)
        .where(
          and(
            eq(referrals.businessId, businessId),
            ilike(referrals.title, createReferralDto.title),
            eq(referrals.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingReferral) {
        throw new ConflictException(
          `A referral with the title "${createReferralDto.title}" already exists`,
        );
      }

      // Verify that the promo code exists and belongs to the business
      const promoCode = await this.db
        .select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.id, createReferralDto.promoCodeId),
            eq(promoCodes.businessId, businessId),
            eq(promoCodes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!promoCode) {
        throw new BadRequestException(
          'Invalid promo code or promo code does not belong to this business',
        );
      }

      // Verify that the staff member exists and belongs to the business
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createReferralDto.staffId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!staffMember) {
        throw new BadRequestException(
          'Invalid staff member or staff member does not belong to this business',
        );
      }

      // Create the referral
      const newReferral = await this.db
        .insert(referrals)
        .values({
          businessId,
          title: createReferralDto.title,
          description: createReferralDto.description,
          link: createReferralDto.link,
          promoCode: createReferralDto.promoCode,
          promoCodeId: createReferralDto.promoCodeId,
          staffId: createReferralDto.staffId,
          status: createReferralDto.status ?? ReferralStatus.ACTIVE,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      const referral = newReferral[0];

      // Log the referral creation activity
      await this.activityLogService.logCreate(
        referral.id,
        EntityType.REFERRAL,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: referral.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create referral');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ReferralDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(referrals.isDeleted, false),
      eq(referrals.status, ReferralStatus.ACTIVE),
      eq(referrals.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(referrals.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(referrals.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(referrals)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get referrals with related data
    const referralResults = await this.db
      .select({
        id: referrals.id,
        businessId: referrals.businessId,
        title: referrals.title,
        description: referrals.description,
        link: referrals.link,
        promoCode: referrals.promoCode,
        promoCodeId: referrals.promoCodeId,
        linkClicks: referrals.linkClicks,
        staffId: referrals.staffId,
        status: referrals.status,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt,
        deletedAt: referrals.isDeleted,
        // Creator info
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        // Promo code details
        promoCodeCode: promoCodes.code,
        promoCodeName: promoCodes.name,
        promoCodeDiscountType: promoCodes.discountType,
        promoCodeDiscountValue: promoCodes.discountValue,
        // Staff details
        staffFirstName: staffMembers.firstName,
        staffLastName: staffMembers.lastName,
        staffDisplayName: staffMembers.displayName,
        staffEmail: staffMembers.email,
      })
      .from(referrals)
      .leftJoin(users, eq(referrals.createdBy, users.id))
      .leftJoin(promoCodes, eq(referrals.promoCodeId, promoCodes.id))
      .leftJoin(staffMembers, eq(referrals.staffId, staffMembers.id))
      .where(and(...whereConditions))
      .orderBy(desc(referrals.createdAt))
      .limit(limit)
      .offset(offset);

    // Transform results to DTOs
    const data: ReferralDto[] = referralResults.map((referral) => ({
      id: referral.id,
      businessId: referral.businessId,
      title: referral.title,
      description: referral.description,
      link: referral.link,
      promoCode: referral.promoCode,
      promoCodeId: referral.promoCodeId,
      linkClicks: referral.linkClicks,
      staffId: referral.staffId,
      status: referral.status,
      createdBy: referral.createdByName || 'Unknown',
      createdAt: referral.createdAt,
      updatedAt: referral.updatedAt,
      promoCodeDetails: referral.promoCodeCode
        ? {
            id: referral.promoCodeId,
            code: referral.promoCodeCode,
            name: referral.promoCodeName || '',
            discountType: referral.promoCodeDiscountType || '',
            discountValue: referral.promoCodeDiscountValue || '0',
          }
        : undefined,
      staffDetails: referral.staffFirstName
        ? {
            id: referral.staffId,
            firstName: referral.staffFirstName,
            lastName: referral.staffLastName || '',
            displayName: referral.staffDisplayName || '',
            email: referral.staffEmail || '',
          }
        : undefined,
    }));

    // Note: Skipping view activity logging for performance reasons

    return {
      data,
      meta: { total, page, totalPages },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    title?: string,
    promoCode?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<PaginatedReferralsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(referrals.isDeleted, false),
      eq(referrals.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(referrals.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(referrals.createdAt, toDate));
      }
    }

    // Add title filtering if provided (searches both title and description)
    if (title) {
      whereConditions.push(
        or(
          ilike(referrals.title, `%${title}%`),
          ilike(referrals.description, `%${title}%`),
        ),
      );
    }

    // Add promoCode filtering if provided
    if (promoCode) {
      whereConditions.push(ilike(referrals.promoCode, `%${promoCode}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as ReferralStatus);
      whereConditions.push(inArray(referrals.status, statusArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'title') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(referrals.title, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(referrals.title, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(referrals.title, value));
                break;
              case 'ne':
                filterConditions.push(sql`${referrals.title} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${referrals.title} IS NULL OR ${referrals.title} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${referrals.title} IS NOT NULL AND ${referrals.title} != ''`,
                );
                break;
            }
          } else if (fieldId === 'description') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(referrals.description, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(referrals.description, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(referrals.description, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${referrals.description} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${referrals.description} IS NULL OR ${referrals.description} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${referrals.description} IS NOT NULL AND ${referrals.description} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(referrals.status, value as ReferralStatus[]),
                  );
                } else {
                  filterConditions.push(eq(referrals.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${referrals.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${referrals.status} != ${value}`);
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as ReferralStatus);
                  filterConditions.push(
                    inArray(referrals.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(referrals.status, value));
                }
                break;
            }
          } else if (fieldId === 'promoCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(referrals.promoCode, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(referrals.promoCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(referrals.promoCode, value));
                break;
              case 'ne':
                filterConditions.push(sql`${referrals.promoCode} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${referrals.promoCode} IS NULL OR ${referrals.promoCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${referrals.promoCode} IS NOT NULL AND ${referrals.promoCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'link') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(referrals.link, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(referrals.link, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(referrals.link, value));
                break;
              case 'ne':
                filterConditions.push(sql`${referrals.link} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${referrals.link} IS NULL OR ${referrals.link} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${referrals.link} IS NOT NULL AND ${referrals.link} != ''`,
                );
                break;
            }
          } else if (fieldId === 'linkClicks') {
            switch (operator) {
              case 'eq':
                filterConditions.push(eq(referrals.linkClicks, Number(value)));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${referrals.linkClicks} != ${Number(value)}`,
                );
                break;
              case 'gt':
                filterConditions.push(
                  sql`${referrals.linkClicks} > ${Number(value)}`,
                );
                break;
              case 'gte':
                filterConditions.push(
                  sql`${referrals.linkClicks} >= ${Number(value)}`,
                );
                break;
              case 'lt':
                filterConditions.push(
                  sql`${referrals.linkClicks} < ${Number(value)}`,
                );
                break;
              case 'lte':
                filterConditions.push(
                  sql`${referrals.linkClicks} <= ${Number(value)}`,
                );
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions with proper indexing strategy
    // Default sort: createdAt descending, then by ID for consistent pagination
    let orderBy = [desc(referrals.createdAt), asc(referrals.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'title':
              orderBy = [
                isDesc ? desc(referrals.title) : asc(referrals.title),
                asc(referrals.id), // Secondary sort for consistency
              ];
              break;
            case 'status':
              orderBy = [
                isDesc ? desc(referrals.status) : asc(referrals.status),
                asc(referrals.id), // Secondary sort for consistency
              ];
              break;
            case 'promoCode':
              orderBy = [
                isDesc ? desc(referrals.promoCode) : asc(referrals.promoCode),
                asc(referrals.id), // Secondary sort for consistency
              ];
              break;
            case 'linkClicks':
              orderBy = [
                isDesc ? desc(referrals.linkClicks) : asc(referrals.linkClicks),
                asc(referrals.id), // Secondary sort for consistency
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(referrals.createdAt) : asc(referrals.createdAt),
                asc(referrals.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(referrals.updatedAt) : asc(referrals.updatedAt),
                asc(referrals.id), // Secondary sort for consistency
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: referrals.id,
        title: referrals.title,
        description: referrals.description,
        link: referrals.link,
        promoCode: referrals.promoCode,
        linkClicks: referrals.linkClicks,
        status: referrals.status,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt,
        // Creator info
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        // Promo code details
        promoCodeId: promoCodes.id,
        promoCodeCode: promoCodes.code,
        promoCodeName: promoCodes.name,
        promoCodeDiscountType: promoCodes.discountType,
        promoCodeDiscountValue: promoCodes.discountValue,
        // Staff details
        staffId: staffMembers.id,
        staffFirstName: staffMembers.firstName,
        staffLastName: staffMembers.lastName,
        staffDisplayName: staffMembers.displayName,
        staffEmail: staffMembers.email,
      })
      .from(referrals)
      .leftJoin(users, eq(referrals.createdBy, users.id))
      .leftJoin(promoCodes, eq(referrals.promoCodeId, promoCodes.id))
      .leftJoin(staffMembers, eq(referrals.staffId, staffMembers.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(referrals)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons

    // Transform results to ReferralListDto
    const data: ReferralListDto[] = result.map((referral) => ({
      id: referral.id,
      title: referral.title,
      description: referral.description,
      link: referral.link,
      promoCode: referral.promoCode,
      linkClicks: referral.linkClicks,
      status: referral.status,
      createdBy: referral.createdByName || 'Unknown',
      createdAt: referral.createdAt,
      updatedAt: referral.updatedAt,
      promoCodeDetails: {
        id: referral.promoCodeId || '',
        code: referral.promoCodeCode || '',
        name: referral.promoCodeName || '',
        discountType: referral.promoCodeDiscountType || '',
        discountValue: referral.promoCodeDiscountValue || '0',
      },
      staffDetails: {
        id: referral.staffId || '',
        firstName: referral.staffFirstName || '',
        lastName: referral.staffLastName || '',
        displayName: referral.staffDisplayName || '',
        email: referral.staffEmail || '',
      },
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ReferralDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const referralResult = await this.db
      .select({
        id: referrals.id,
        businessId: referrals.businessId,
        title: referrals.title,
        description: referrals.description,
        link: referrals.link,
        promoCode: referrals.promoCode,
        promoCodeId: referrals.promoCodeId,
        linkClicks: referrals.linkClicks,
        staffId: referrals.staffId,
        status: referrals.status,
        createdAt: referrals.createdAt,
        updatedAt: referrals.updatedAt,
        deletedAt: referrals.isDeleted,
        // Creator and updater info
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        // Promo code details
        promoCodeCode: promoCodes.code,
        promoCodeName: promoCodes.name,
        promoCodeDiscountType: promoCodes.discountType,
        promoCodeDiscountValue: promoCodes.discountValue,
        // Staff details
        staffFirstName: staffMembers.firstName,
        staffLastName: staffMembers.lastName,
        staffDisplayName: staffMembers.displayName,
        staffEmail: staffMembers.email,
      })
      .from(referrals)
      .leftJoin(users, eq(referrals.createdBy, users.id))
      .leftJoin(promoCodes, eq(referrals.promoCodeId, promoCodes.id))
      .leftJoin(staffMembers, eq(referrals.staffId, staffMembers.id))
      .where(
        and(
          eq(referrals.id, id),
          eq(referrals.businessId, businessId),
          eq(referrals.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!referralResult) {
      throw new NotFoundException(`Referral with ID ${id} not found`);
    }

    // Note: Skipping view activity logging for performance reasons

    return {
      id: referralResult.id,
      businessId: referralResult.businessId,
      title: referralResult.title,
      description: referralResult.description,
      link: referralResult.link,
      promoCode: referralResult.promoCode,
      promoCodeId: referralResult.promoCodeId,
      linkClicks: referralResult.linkClicks,
      staffId: referralResult.staffId,
      status: referralResult.status,
      createdBy: referralResult.createdByName || 'Unknown',
      createdAt: referralResult.createdAt,
      updatedAt: referralResult.updatedAt,
      promoCodeDetails: referralResult.promoCodeCode
        ? {
            id: referralResult.promoCodeId,
            code: referralResult.promoCodeCode,
            name: referralResult.promoCodeName || '',
            discountType: referralResult.promoCodeDiscountType || '',
            discountValue: referralResult.promoCodeDiscountValue || '0',
          }
        : undefined,
      staffDetails: referralResult.staffFirstName
        ? {
            id: referralResult.staffId,
            firstName: referralResult.staffFirstName,
            lastName: referralResult.staffLastName || '',
            displayName: referralResult.staffDisplayName || '',
            email: referralResult.staffEmail || '',
          }
        : undefined,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateReferralDto: UpdateReferralDto,
    metadata?: ActivityMetadata,
  ): Promise<ReferralDto> {
    // Get the referral
    const existingReferral = await this.db
      .select()
      .from(referrals)
      .where(and(eq(referrals.id, id), eq(referrals.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingReferral) {
      throw new NotFoundException(`Referral with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingReferral.businessId) {
      throw new UnauthorizedException('Access denied to update this referral');
    }

    // Check for title conflicts if title is being updated
    if (
      updateReferralDto.title &&
      updateReferralDto.title !== existingReferral.title
    ) {
      const existingWithSameTitle = await this.db
        .select()
        .from(referrals)
        .where(
          and(
            eq(referrals.businessId, businessId),
            ilike(referrals.title, updateReferralDto.title),
            eq(referrals.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingWithSameTitle) {
        throw new ConflictException(
          `A referral with the title "${updateReferralDto.title}" already exists`,
        );
      }
    }

    // Verify promo code if being updated
    if (updateReferralDto.promoCodeId) {
      const promoCode = await this.db
        .select()
        .from(promoCodes)
        .where(
          and(
            eq(promoCodes.id, updateReferralDto.promoCodeId),
            eq(promoCodes.businessId, businessId),
            eq(promoCodes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!promoCode) {
        throw new BadRequestException(
          'Invalid promo code or promo code does not belong to this business',
        );
      }
    }

    // Verify staff member if being updated
    if (updateReferralDto.staffId) {
      const staffMember = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, updateReferralDto.staffId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!staffMember) {
        throw new BadRequestException(
          'Invalid staff member or staff member does not belong to this business',
        );
      }
    }

    // Update the referral
    const updatedReferral = await this.db
      .update(referrals)
      .set({
        ...updateReferralDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(referrals.id, id))
      .returning();

    const referral = updatedReferral[0];

    // Log the activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.REFERRAL,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    // Return the updated referral with full details
    return this.findOne(userId, businessId, id);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get the referral
    const existingReferral = await this.db
      .select()
      .from(referrals)
      .where(
        and(
          eq(referrals.id, id),
          eq(referrals.businessId, businessId),
          eq(referrals.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingReferral) {
      throw new NotFoundException(`Referral with ID ${id} not found`);
    }

    // Soft delete the referral
    await this.db
      .update(referrals)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(referrals.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.REFERRAL,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      message: 'Referral deleted successfully',
      id: existingReferral.id,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('No referral IDs provided');
    }

    // Get existing referrals to verify ownership and get titles for logging
    const existingReferrals = await this.db
      .select()
      .from(referrals)
      .where(
        and(
          inArray(referrals.id, ids),
          eq(referrals.businessId, businessId),
          eq(referrals.isDeleted, false),
        ),
      );

    if (existingReferrals.length === 0) {
      throw new NotFoundException('No referrals found with the provided IDs');
    }

    const validIds = existingReferrals.map((r) => r.id);

    // Bulk soft delete
    await this.db
      .update(referrals)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(referrals.id, validIds));

    // Log the activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.REFERRAL,
      validIds,
      { isDeleted: true, updatedBy: userId },
      userId,
      businessId,
      {
        filterCriteria: { referralIds: ids },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deletedCount: validIds.length,
      deletedIds: validIds,
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: ReferralStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedCount: number; updatedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!ids || ids.length === 0) {
      throw new BadRequestException('No referral IDs provided');
    }

    // Get existing referrals to verify ownership
    const existingReferrals = await this.db
      .select()
      .from(referrals)
      .where(
        and(
          inArray(referrals.id, ids),
          eq(referrals.businessId, businessId),
          eq(referrals.isDeleted, false),
        ),
      );

    if (existingReferrals.length === 0) {
      throw new NotFoundException('No referrals found with the provided IDs');
    }

    const validIds = existingReferrals.map((r) => r.id);

    // Bulk update status
    await this.db
      .update(referrals)
      .set({
        status,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(referrals.id, validIds));

    // Log the activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_STATUS_CHANGE,
      EntityType.REFERRAL,
      validIds,
      { status },
      userId,
      businessId,
      {
        filterCriteria: { referralIds: ids, targetStatus: status },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      updatedCount: validIds.length,
      updatedIds: validIds,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ReferralSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all referrals with only essential fields
    const referralResults = await this.db
      .select({
        id: referrals.id,
        title: referrals.title,
        promoCode: referrals.promoCode,
        status: referrals.status,
      })
      .from(referrals)
      .where(
        and(
          eq(referrals.isDeleted, false),
          eq(referrals.status, ReferralStatus.ACTIVE),
          eq(referrals.businessId, businessId),
        ),
      )
      .orderBy(asc(referrals.title));

    // Note: Skipping view activity logging for performance reasons

    return referralResults.map((referral) => ({
      id: referral.id,
      title: referral.title,
      promoCode: referral.promoCode,
      status: referral.status,
    }));
  }

  async checkTitleAvailability(
    userId: string,
    businessId: string | null,
    title: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a referral with the same title already exists for this business
    // Using ilike for case-insensitive comparison
    const existingReferral = await this.db
      .select()
      .from(referrals)
      .where(
        and(
          eq(referrals.businessId, businessId),
          ilike(referrals.title, title),
          eq(referrals.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReferral };
  }

  // Helper methods for controller
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createReferralDto: CreateReferralDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const referral = await this.create(
      userId,
      businessId,
      createReferralDto,
      metadata,
    );
    return { id: referral.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateReferralDto: UpdateReferralDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateReferralDto, metadata);
    return { id };
  }
}
