import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateVehicleCategoryDto } from './dto/create-vehicle-category.dto';
import { UpdateVehicleCategoryDto } from './dto/update-vehicle-category.dto';
import { VehicleCategoryDto } from './dto/vehicle-category.dto';
import { VehicleCategorySlimDto } from './dto/vehicle-category-slim.dto';
import { VehicleCategoryListDto } from './dto/vehicle-category-list.dto';
import {
  vehicleTypes,
  vehicleTypeLocations,
} from '../drizzle/schema/vehicle-categories.schema';
import { assetCategories } from '../drizzle/schema/asset-categories.schema';

import { vehicles } from '../drizzle/schema/vehicles.schema';
import { locations } from '../drizzle/schema/locations.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { VehicleCategoryStatus, CategoryStatus } from '../shared/types';
import { AssetCategoryReferenceType } from '../shared/types/asset-category-reference.enum';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { LocationsService } from '../locations/locations.service';

@Injectable()
export class VehicleCategoriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createVehicleCategoryDto: CreateVehicleCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a vehicle category with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingVehicleType = await this.db
        .select()
        .from(vehicleTypes)
        .where(
          and(
            eq(vehicleTypes.businessId, businessId),
            ilike(vehicleTypes.name, createVehicleCategoryDto.name),
            isNull(vehicleTypes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingVehicleType) {
        throw new ConflictException(
          `A vehicle category with the name '${createVehicleCategoryDto.name}' already exists for this business`,
        );
      }

      // Create slug from name if not provided
      if (!createVehicleCategoryDto.slug) {
        createVehicleCategoryDto.slug = createVehicleCategoryDto.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');
      }

      // Check if a vehicle category with the same slug already exists for this business
      if (createVehicleCategoryDto.slug) {
        const existingSlugVehicleType = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.businessId, businessId),
              eq(vehicleTypes.slug, createVehicleCategoryDto.slug),
              isNull(vehicleTypes.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingSlugVehicleType) {
          throw new ConflictException(
            `A vehicle category with the slug '${createVehicleCategoryDto.slug}' already exists for this business`,
          );
        }
      }

      // Check if a vehicle category with the same shortCode already exists for this business
      if (createVehicleCategoryDto.shortCode) {
        const existingShortCodeVehicleType = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.businessId, businessId),
              eq(vehicleTypes.shortCode, createVehicleCategoryDto.shortCode),
              isNull(vehicleTypes.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingShortCodeVehicleType) {
          throw new ConflictException(
            `A vehicle category with the short code '${createVehicleCategoryDto.shortCode}' already exists for this business`,
          );
        }
      }

      // Handle parent vehicle category if provided
      if (createVehicleCategoryDto.parentId) {
        const parentVehicleType = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.id, createVehicleCategoryDto.parentId),
              eq(vehicleTypes.businessId, businessId),
              isNull(vehicleTypes.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!parentVehicleType) {
          throw new BadRequestException('Parent vehicle category not found');
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'vehicle-types',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'vehicle-types/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      console.log(
        'createVehicleCategoryDto.locationIds',
        createVehicleCategoryDto.locationIds,
      );

      // Use a transaction to ensure position reordering, vehicle category creation, and asset-category creation are atomic
      const newVehicleType = await this.db.transaction(async (tx) => {
        // Shift all existing vehicle categorys down by 1 position to make room at position 1
        await this.reorderPositions(
          tx,
          businessId,
          createVehicleCategoryDto.parentId,
          1, // Insert at position 1 (first position)
        );

        // Insert new vehicle category at position 1
        const [vehicleType] = await tx
          .insert(vehicleTypes)
          .values({
            businessId,
            name: createVehicleCategoryDto.name,
            shortCode: createVehicleCategoryDto.shortCode,
            parentId: createVehicleCategoryDto.parentId,
            description: createVehicleCategoryDto.description,
            slug: createVehicleCategoryDto.slug,
            availableOnline: createVehicleCategoryDto.availableOnline ?? false,
            position: 1, // Always create new vehicle categorys at position 1 (first)
            color: createVehicleCategoryDto.color,
            image: mediaId,
            isAllocatedToAllLocations:
              createVehicleCategoryDto.isAllocatedToAllLocations ?? false,
            seoTitle: createVehicleCategoryDto.seoTitle,
            seoDescription: createVehicleCategoryDto.seoDescription,
            seoKeywords: createVehicleCategoryDto.seoKeywords,
            ogImage: ogImageId,
            createdBy: userId,
            status:
              createVehicleCategoryDto.status ?? VehicleCategoryStatus.ACTIVE,
          })
          .returning();

        // Automatically create corresponding asset-category
        const [assetCategory] = await tx
          .insert(assetCategories)
          .values({
            businessId,
            name: createVehicleCategoryDto.name,
            description: createVehicleCategoryDto.description,
            referenceId: vehicleType.id,
            referenceType: AssetCategoryReferenceType.VEHICLE_CATEGORY,
            createdBy: userId,
            status: CategoryStatus.ACTIVE,
          })
          .returning();

        // Update the vehicle-category with the asset-category ID
        await tx
          .update(vehicleTypes)
          .set({ assetCategoryId: assetCategory.id })
          .where(eq(vehicleTypes.id, vehicleType.id));

        return { ...vehicleType, assetCategoryId: assetCategory.id };
      });

      // Handle location associations
      await this.manageVehicleTypeLocations(
        newVehicleType.id,
        businessId,
        createVehicleCategoryDto.isAllocatedToAllLocations ?? false,
        createVehicleCategoryDto.locationIds,
        userId,
      );

      // Log the vehicle category creation activity
      await this.activityLogService.logCreate(
        newVehicleType.id,
        EntityType.VEHICLE_CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newVehicleType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create vehicle category: ${error.message}`,
      );
    }
  }

  /**
   * Helper method to reorder positions when inserting at a specific position
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    parentId: string | undefined,
    insertPosition: number,
  ): Promise<void> {
    // Shift all vehicle categorys at or after the insert position down by 1
    const whereConditions = [
      eq(vehicleTypes.businessId, businessId),
      gte(vehicleTypes.position, insertPosition),
      isNull(vehicleTypes.deletedAt),
    ];

    if (parentId) {
      whereConditions.push(eq(vehicleTypes.parentId, parentId));
    } else {
      whereConditions.push(isNull(vehicleTypes.parentId));
    }

    await tx
      .update(vehicleTypes)
      .set({
        position: sql`${vehicleTypes.position} + 1`,
        updatedAt: new Date(),
      })
      .where(and(...whereConditions));
  }

  /**
   * Helper method to manage vehicle category location associations
   */
  private async manageVehicleTypeLocations(
    vehicleTypeId: string,
    businessId: string,
    isAllocatedToAllLocations: boolean,
    locationIds: string[] | undefined,
    userId: string,
  ): Promise<void> {
    // If allocated to all locations, we don't need to create specific associations
    if (isAllocatedToAllLocations) {
      return;
    }

    // If specific location IDs are provided, create associations
    if (locationIds && locationIds.length > 0) {
      // Validate that all location IDs belong to the business
      const validLocations = await this.db
        .select({ id: locations.id })
        .from(locations)
        .where(
          and(
            eq(locations.businessId, businessId),
            inArray(locations.id, locationIds),
            eq(locations.isDeleted, false),
          ),
        );

      if (validLocations.length !== locationIds.length) {
        throw new BadRequestException(
          'One or more location IDs are invalid or do not belong to this business',
        );
      }

      // Create vehicle category location associations
      const vehicleTypeLocationData = locationIds.map((locationId) => ({
        vehicleTypeId,
        locationId,
        createdBy: userId,
      }));

      await this.db
        .insert(vehicleTypeLocations)
        .values(vehicleTypeLocationData);
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: VehicleCategoryDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(vehicleTypes.isDeleted, false),
      eq(vehicleTypes.status, VehicleCategoryStatus.ACTIVE),
      eq(vehicleTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(vehicleTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(vehicleTypes.createdAt, toDate));
      }
    }

    // Find all vehicle categorys for the user's active business with pagination
    const result = await this.db
      .select()
      .from(vehicleTypes)
      .where(and(...whereConditions))
      .orderBy(desc(vehicleTypes.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicleTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Activity logging disabled for performance in findAll method

    return {
      data: await Promise.all(
        result.map((vehicleType) => this.mapToVehicleCategoryDto(vehicleType)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllByBusiness(
    userId: string,
    businessId: string,
  ): Promise<VehicleCategoryDto[]> {
    // This method keeps same functionality but we might consider if this is still needed
    // since we now use activeBusinessId by default
    const result = await this.db
      .select()
      .from(vehicleTypes)
      .where(
        and(
          eq(vehicleTypes.isDeleted, false),
          eq(vehicleTypes.status, VehicleCategoryStatus.ACTIVE),
          eq(vehicleTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(vehicleTypes.position), asc(vehicleTypes.id));

    // Activity logging disabled for performance in findAllByBusiness method

    return await Promise.all(
      result.map((vehicleType) => this.mapToVehicleCategoryDto(vehicleType)),
    );
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<VehicleCategorySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: vehicleTypes.id,
        name: vehicleTypes.name,
        position: vehicleTypes.position,
      })
      .from(vehicleTypes)
      .where(
        and(
          eq(vehicleTypes.isDeleted, false),
          eq(vehicleTypes.status, VehicleCategoryStatus.ACTIVE),
          eq(vehicleTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(vehicleTypes.position), asc(vehicleTypes.id));

    // Activity logging disabled for performance in findAllSlim method

    return result;
  }

  async findAllHierarchy(userId: string, businessId: string | null) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: vehicleTypes.id,
        name: vehicleTypes.name,
        parentId: vehicleTypes.parentId,
        position: vehicleTypes.position,
      })
      .from(vehicleTypes)
      .where(
        and(
          eq(vehicleTypes.isDeleted, false),
          eq(vehicleTypes.status, VehicleCategoryStatus.ACTIVE),
          eq(vehicleTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(vehicleTypes.position), asc(vehicleTypes.id));

    // Activity logging disabled for performance in findAllHierarchy method

    return result;
  }

  async findOne(userId: string, id: string): Promise<VehicleCategoryDto> {
    const vehicleType = await this.db
      .select()
      .from(vehicleTypes)
      .where(and(eq(vehicleTypes.id, id), eq(vehicleTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!vehicleType) {
      throw new NotFoundException('Vehicle category not found');
    }

    // Check if user has access to this vehicle category's business
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });
    if (!user || vehicleType.businessId !== user.activeBusinessId) {
      throw new UnauthorizedException('Access denied to this vehicle category');
    }

    // Activity logging disabled for performance in findOne method

    return this.mapToVehicleCategoryDto(vehicleType);
  }

  /**
   * Helper method to map database result to VehicleCategoryDto
   */
  private async mapToVehicleCategoryDto(
    vehicleType: any,
  ): Promise<VehicleCategoryDto> {
    // Get creator name
    const creator = await this.db.query.users.findFirst({
      where: eq(users.id, vehicleType.createdBy),
    });
    let updater = null;
    if (vehicleType.updatedBy) {
      updater = await this.db.query.users.findFirst({
        where: eq(users.id, vehicleType.updatedBy),
      });
    }

    // Get vehicle count for this vehicle category
    const vehicleCountResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicles)
      .where(
        and(
          or(
            eq(vehicles.vehicleTypeId, vehicleType.id),
            eq(vehicles.subTypeId, vehicleType.id),
          ),
          eq(vehicles.isDeleted, false),
        ),
      );

    const vehiclesCount = Number(vehicleCountResult[0].count);

    // Get locations for this vehicle category
    let vehicleTypeLocationsList: { id: string; name: string }[] = [];
    if (!vehicleType.isAllocatedToAllLocations) {
      const vehicleTypeLocationResults = await this.db
        .select({
          id: locations.id,
          name: locations.name,
        })
        .from(vehicleTypeLocations)
        .innerJoin(locations, eq(vehicleTypeLocations.locationId, locations.id))
        .where(eq(vehicleTypeLocations.vehicleTypeId, vehicleType.id));

      vehicleTypeLocationsList = vehicleTypeLocationResults;
    }

    return {
      id: vehicleType.id,
      businessId: vehicleType.businessId,
      name: vehicleType.name,
      shortCode: vehicleType.shortCode,
      parentId: vehicleType.parentId,
      description: vehicleType.description,
      slug: vehicleType.slug,
      availableOnline: vehicleType.availableOnline,
      position: vehicleType.position,
      color: vehicleType.color,
      image: vehicleType.image,
      seoTitle: vehicleType.seoTitle,
      seoDescription: vehicleType.seoDescription,
      seoKeywords: vehicleType.seoKeywords,
      ogImage: vehicleType.ogImage,
      isAllocatedToAllLocations: vehicleType.isAllocatedToAllLocations,
      locations: vehicleTypeLocationsList,
      createdBy: creator?.firstName + ' ' + creator?.lastName || 'Unknown',
      updatedBy: updater?.firstName + ' ' + updater?.lastName || undefined,
      status: vehicleType.status,
      vehiclesCount,
      createdAt: vehicleType.createdAt,
      updatedAt: vehicleType.updatedAt,
    };
  }

  /**
   * Helper method to map database result to VehicleCategoryListDto
   */
  private async mapToVehicleCategoryListDto(
    vehicleType: any,
  ): Promise<VehicleCategoryListDto> {
    // Get vehicle count for this vehicle category
    const vehicleCountResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicles)
      .where(
        and(
          or(
            eq(vehicles.vehicleTypeId, vehicleType.id),
            eq(vehicles.subTypeId, vehicleType.id),
          ),
          eq(vehicles.isDeleted, false),
        ),
      );

    const vehiclesCount = Number(vehicleCountResult[0].count);

    // Get parent name if exists
    let parentName: string | undefined;
    if (vehicleType.parentId) {
      const parent = await this.db
        .select({ name: vehicleTypes.name })
        .from(vehicleTypes)
        .where(eq(vehicleTypes.id, vehicleType.parentId))
        .then((results) => results[0]);
      parentName = parent?.name;
    }

    // Get sub vehicle categorys count
    const subVehicleCategorysCountResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicleTypes)
      .where(
        and(
          eq(vehicleTypes.parentId, vehicleType.id),
          eq(vehicleTypes.isDeleted, false),
        ),
      );

    const subVehicleCategorysCount = Number(
      subVehicleCategorysCountResult[0].count,
    );

    return {
      id: vehicleType.id,
      name: vehicleType.name,
      shortCode: vehicleType.shortCode,
      slug: vehicleType.slug,
      status: vehicleType.status,
      availableOnline: vehicleType.availableOnline,
      color: vehicleType.color,
      parentId: vehicleType.parentId,
      parentName,
      subVehicleCategorysCount,
      vehiclesCount,
      image: vehicleType.image,
      isAllocatedToAllLocations: vehicleType.isAllocatedToAllLocations,
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    shortCode?: string,
    status?: string,
    availableOnline?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: VehicleCategoryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(vehicleTypes.isDeleted, false),
      eq(vehicleTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(vehicleTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(vehicleTypes.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches both name and shortCode)
    if (name) {
      whereConditions.push(
        or(
          ilike(vehicleTypes.name, `%${name}%`),
          ilike(vehicleTypes.shortCode, `%${name}%`),
        ),
      );
    }

    // Add slug filtering if provided
    if (slug) {
      whereConditions.push(ilike(vehicleTypes.slug, `%${slug}%`));
    }

    // Add shortCode filtering if provided
    if (shortCode) {
      whereConditions.push(ilike(vehicleTypes.shortCode, `%${shortCode}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as VehicleCategoryStatus);
      whereConditions.push(inArray(vehicleTypes.status, statusArray));
    }

    // Add availableOnline filtering if provided
    if (availableOnline) {
      // Decode URL-encoded commas and split by comma
      const decodedAvailableOnline = decodeURIComponent(availableOnline);
      const availableOnlineValues = decodedAvailableOnline
        .split(',')
        .map((s) => s.trim());
      if (availableOnlineValues.length === 1) {
        const boolValue = availableOnlineValues[0].toLowerCase() === 'true';
        whereConditions.push(eq(vehicleTypes.availableOnline, boolValue));
      } else {
        // Handle multiple boolean values (true, false)
        const boolValues = availableOnlineValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(vehicleTypes.availableOnline, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(vehicleTypes.availableOnline, false));
        }
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(vehicleTypes.name, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(vehicleTypes.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(vehicleTypes.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${vehicleTypes.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${vehicleTypes.name} IS NULL OR ${vehicleTypes.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${vehicleTypes.name} IS NOT NULL AND ${vehicleTypes.name} != ''`,
                );
                break;
            }
          }
          // Add more field filters as needed
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Determine sort order
    let orderBy = desc(vehicleTypes.createdAt); // default
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'name':
          orderBy = isDesc ? desc(vehicleTypes.name) : asc(vehicleTypes.name);
          break;
        case 'position':
          orderBy = isDesc
            ? desc(vehicleTypes.position)
            : asc(vehicleTypes.position);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(vehicleTypes.createdAt)
            : asc(vehicleTypes.createdAt);
          break;
        case 'updatedAt':
          orderBy = isDesc
            ? desc(vehicleTypes.updatedAt)
            : asc(vehicleTypes.updatedAt);
          break;
        default:
          orderBy = desc(vehicleTypes.createdAt);
      }
    }

    // Find vehicle categorys with pagination
    const result = await this.db
      .select()
      .from(vehicleTypes)
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicleTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Activity logging disabled for performance in findAllOptimized method

    return {
      data: await Promise.all(
        result.map((vehicleType) =>
          this.mapToVehicleCategoryListDto(vehicleType),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleCategoryDto: UpdateVehicleCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if vehicle category exists and user has access
      const existingVehicleType = await this.db
        .select()
        .from(vehicleTypes)
        .where(
          and(
            eq(vehicleTypes.id, id),
            eq(vehicleTypes.businessId, businessId),
            eq(vehicleTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingVehicleType) {
        throw new NotFoundException('Vehicle category not found');
      }

      // Check for name conflicts (if name is being updated)
      if (
        updateVehicleCategoryDto.name &&
        updateVehicleCategoryDto.name !== existingVehicleType.name
      ) {
        const nameConflict = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.businessId, businessId),
              ilike(vehicleTypes.name, updateVehicleCategoryDto.name),
              eq(vehicleTypes.isDeleted, false),
              sql`${vehicleTypes.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (nameConflict) {
          throw new ConflictException(
            `A vehicle category with the name '${updateVehicleCategoryDto.name}' already exists for this business`,
          );
        }
      }

      // Check for slug conflicts (if slug is being updated)
      if (
        updateVehicleCategoryDto.slug &&
        updateVehicleCategoryDto.slug !== existingVehicleType.slug
      ) {
        const slugConflict = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.businessId, businessId),
              eq(vehicleTypes.slug, updateVehicleCategoryDto.slug),
              eq(vehicleTypes.isDeleted, false),
              sql`${vehicleTypes.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (slugConflict) {
          throw new ConflictException(
            `A vehicle category with the slug '${updateVehicleCategoryDto.slug}' already exists for this business`,
          );
        }
      }

      // Check for shortCode conflicts (if shortCode is being updated)
      if (
        updateVehicleCategoryDto.shortCode &&
        updateVehicleCategoryDto.shortCode !== existingVehicleType.shortCode
      ) {
        const shortCodeConflict = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.businessId, businessId),
              eq(vehicleTypes.shortCode, updateVehicleCategoryDto.shortCode),
              eq(vehicleTypes.isDeleted, false),
              sql`${vehicleTypes.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (shortCodeConflict) {
          throw new ConflictException(
            `A vehicle category with the short code '${updateVehicleCategoryDto.shortCode}' already exists for this business`,
          );
        }
      }

      // Handle parent vehicle category validation
      if (
        updateVehicleCategoryDto.parentId &&
        updateVehicleCategoryDto.parentId !== existingVehicleType.parentId
      ) {
        const parentVehicleType = await this.db
          .select()
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.id, updateVehicleCategoryDto.parentId),
              eq(vehicleTypes.businessId, businessId),
              eq(vehicleTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentVehicleType) {
          throw new BadRequestException('Parent vehicle category not found');
        }
      }

      let mediaId: string | undefined = existingVehicleType.image;
      let ogImageId: string | undefined = existingVehicleType.ogImage;

      // Upload new image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'vehicle-types',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload new OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'vehicle-types/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Update vehicle category and corresponding asset-category in a transaction
      const updatedVehicleType = await this.db.transaction(async (tx) => {
        // Update the vehicle-category
        const [updated] = await tx
          .update(vehicleTypes)
          .set({
            name: updateVehicleCategoryDto.name ?? existingVehicleType.name,
            shortCode:
              updateVehicleCategoryDto.shortCode ??
              existingVehicleType.shortCode,
            parentId:
              updateVehicleCategoryDto.parentId ?? existingVehicleType.parentId,
            description:
              updateVehicleCategoryDto.description ??
              existingVehicleType.description,
            slug: updateVehicleCategoryDto.slug ?? existingVehicleType.slug,
            availableOnline:
              updateVehicleCategoryDto.availableOnline ??
              existingVehicleType.availableOnline,
            color: updateVehicleCategoryDto.color ?? existingVehicleType.color,
            image: mediaId,
            seoTitle:
              updateVehicleCategoryDto.seoTitle ?? existingVehicleType.seoTitle,
            seoDescription:
              updateVehicleCategoryDto.seoDescription ??
              existingVehicleType.seoDescription,
            seoKeywords:
              updateVehicleCategoryDto.seoKeywords ??
              existingVehicleType.seoKeywords,
            ogImage: ogImageId,
            status:
              updateVehicleCategoryDto.status ?? existingVehicleType.status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(vehicleTypes.id, id))
          .returning();

        // Update the corresponding asset-category if it exists
        if (updated.assetCategoryId) {
          await tx
            .update(assetCategories)
            .set({
              name: updateVehicleCategoryDto.name ?? updated.name,
              description:
                updateVehicleCategoryDto.description !== undefined
                  ? updateVehicleCategoryDto.description
                  : updated.description,
              updatedAt: new Date(),
            })
            .where(eq(assetCategories.id, updated.assetCategoryId));
        }

        return updated;
      });

      // Handle location associations if provided
      if (
        updateVehicleCategoryDto.isAllocatedToAllLocations !== undefined ||
        updateVehicleCategoryDto.locationIds !== undefined
      ) {
        // Remove existing location associations
        await this.db
          .delete(vehicleTypeLocations)
          .where(eq(vehicleTypeLocations.vehicleTypeId, id));

        // Add new location associations
        await this.manageVehicleTypeLocations(
          id,
          businessId,
          updateVehicleCategoryDto.isAllocatedToAllLocations ??
            existingVehicleType.isAllocatedToAllLocations,
          updateVehicleCategoryDto.locationIds,
          userId,
        );
      }

      // Log the vehicle category update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.VEHICLE_CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedVehicleType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update vehicle category: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    vehicleTypeIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!vehicleTypeIds || vehicleTypeIds.length === 0) {
      throw new BadRequestException('No vehicle category IDs provided');
    }

    // Verify all vehicle categorys exist and belong to the business
    const existingVehicleTypes = await this.db
      .select({
        id: vehicleTypes.id,
        name: vehicleTypes.name,
        assetCategoryId: vehicleTypes.assetCategoryId,
      })
      .from(vehicleTypes)
      .where(
        and(
          inArray(vehicleTypes.id, vehicleTypeIds),
          eq(vehicleTypes.businessId, businessId),
          eq(vehicleTypes.isDeleted, false),
        ),
      );

    if (existingVehicleTypes.length !== vehicleTypeIds.length) {
      throw new NotFoundException(
        'One or more vehicle categorys not found or do not belong to this business',
      );
    }

    // Soft delete the vehicle categorys and corresponding asset-categories in a transaction
    const deletedVehicleTypes = await this.db.transaction(async (tx) => {
      // Soft delete the vehicle-categories
      const deleted = await tx
        .update(vehicleTypes)
        .set({
          isDeleted: true,
          updatedAt: new Date(),
          updatedBy: userId,
        })
        .where(
          and(
            inArray(vehicleTypes.id, vehicleTypeIds),
            eq(vehicleTypes.businessId, businessId),
            eq(vehicleTypes.isDeleted, false),
          ),
        )
        .returning({ id: vehicleTypes.id, name: vehicleTypes.name });

      // Soft delete corresponding asset-categories
      const assetCategoryIds = existingVehicleTypes
        .filter((vt) => vt.assetCategoryId)
        .map((vt) => vt.assetCategoryId);

      if (assetCategoryIds.length > 0) {
        await tx
          .update(assetCategories)
          .set({
            deletedBy: userId,
            deletedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(inArray(assetCategories.id, assetCategoryIds));
      }

      return deleted;
    });

    // Log the bulk delete activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.VEHICLE_CATEGORY,
      deletedVehicleTypes.map((vt) => vt.id),
      { isDeleted: true, updatedBy: userId },
      userId,
      businessId,
      {
        filterCriteria: { vehicleTypeIds },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      deleted: deletedVehicleTypes.length,
      message: `Successfully deleted ${deletedVehicleTypes.length} vehicle categorys`,
      deletedIds: deletedVehicleTypes.map((vt) => vt.id),
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if vehicle category exists and belongs to the business
    const existingVehicleType = await this.db
      .select({
        id: vehicleTypes.id,
        name: vehicleTypes.name,
        assetCategoryId: vehicleTypes.assetCategoryId,
      })
      .from(vehicleTypes)
      .where(
        and(
          eq(vehicleTypes.id, id),
          eq(vehicleTypes.businessId, businessId),
          eq(vehicleTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingVehicleType) {
      throw new NotFoundException('Vehicle category not found');
    }

    // Soft delete the vehicle category and corresponding asset-category in a transaction
    await this.db.transaction(async (tx) => {
      // Soft delete the vehicle-category
      await tx
        .update(vehicleTypes)
        .set({
          isDeleted: true,
          updatedAt: new Date(),
          updatedBy: userId,
        })
        .where(eq(vehicleTypes.id, id));

      // Soft delete the corresponding asset-category if it exists
      if (existingVehicleType.assetCategoryId) {
        await tx
          .update(assetCategories)
          .set({
            isDeleted: true,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(eq(assetCategories.id, existingVehicleType.assetCategoryId));
      }
    });

    // Log the delete activity
    await this.activityLogService.logDelete(
      id,
      EntityType.VEHICLE_CATEGORY,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: 'Vehicle category deleted successfully',
    };
  }

  async updatePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!updates || updates.length === 0) {
      throw new BadRequestException('No position updates provided');
    }

    // Verify all vehicle categorys exist and belong to the business
    const vehicleTypeIds = updates.map((update) => update.id);
    const existingVehicleTypes = await this.db
      .select({ id: vehicleTypes.id })
      .from(vehicleTypes)
      .where(
        and(
          inArray(vehicleTypes.id, vehicleTypeIds),
          eq(vehicleTypes.businessId, businessId),
          eq(vehicleTypes.isDeleted, false),
        ),
      );

    if (existingVehicleTypes.length !== vehicleTypeIds.length) {
      throw new NotFoundException(
        'One or more vehicle categorys not found or do not belong to this business',
      );
    }

    // Update positions in a transaction
    await this.db.transaction(async (tx) => {
      for (const update of updates) {
        await tx
          .update(vehicleTypes)
          .set({
            position: update.position,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(vehicleTypes.id, update.id));
      }
    });

    // Log the position update activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_UPDATE,
      EntityType.VEHICLE_CATEGORY,
      vehicleTypeIds,
      { positionsUpdated: true },
      userId,
      businessId,
      {
        filterCriteria: { positionUpdates: updates.length },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      updated: updates.length,
      message: `Successfully updated ${updates.length} vehicle category positions`,
    };
  }

  async updateHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number; failed: { id: string; error: string }[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!updates || updates.length === 0) {
      throw new BadRequestException('No hierarchy updates provided');
    }

    const failed: { id: string; error: string }[] = [];
    let updated = 0;

    // Process each update individually to handle failures gracefully
    for (const update of updates) {
      try {
        // Verify vehicle category exists and belongs to the business
        const existingVehicleType = await this.db
          .select({ id: vehicleTypes.id })
          .from(vehicleTypes)
          .where(
            and(
              eq(vehicleTypes.id, update.id),
              eq(vehicleTypes.businessId, businessId),
              eq(vehicleTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!existingVehicleType) {
          failed.push({
            id: update.id,
            error:
              'Vehicle category not found or does not belong to this business',
          });
          continue;
        }

        // Verify parent vehicle category exists if provided
        if (update.parentId) {
          const parentVehicleType = await this.db
            .select({ id: vehicleTypes.id })
            .from(vehicleTypes)
            .where(
              and(
                eq(vehicleTypes.id, update.parentId),
                eq(vehicleTypes.businessId, businessId),
                eq(vehicleTypes.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (!parentVehicleType) {
            failed.push({
              id: update.id,
              error: 'Parent vehicle category not found',
            });
            continue;
          }
        }

        // Update the hierarchy
        await this.db
          .update(vehicleTypes)
          .set({
            parentId: update.parentId,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(vehicleTypes.id, update.id));

        updated++;
      } catch (error) {
        failed.push({
          id: update.id,
          error: error.message || 'Unknown error occurred',
        });
      }
    }

    // Log the hierarchy update activity
    if (updated > 0) {
      const updatedIds = updates
        .filter((_, index) => index < updated)
        .map((u) => u.id);

      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.VEHICLE_CATEGORY,
        updatedIds,
        { hierarchyUpdated: true },
        userId,
        businessId,
        {
          filterCriteria: { hierarchyUpdates: updates.length },
          failures: failed.map((f) => ({ id: f.id, error: f.error })),
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      updated,
      failed,
    };
  }
}
