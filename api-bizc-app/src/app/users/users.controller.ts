import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Request,
  Query,
  Param,
  Delete,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { UsersService } from './users.service';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { SkipAuthentication } from '../auth/decorators/skip-authentication.decorator';
import { UserRole } from './types/users.type';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UpdateUserDto, UpdateUserResponseDto } from './dto/update-user.dto';
import {
  AdminUpdateUserDto,
  AdminUpdateUserResponseDto,
} from './dto/admin-update-user.dto';
import { UserFilterDto } from './dto/user-filter.dto';
import { DeleteUsersDto } from './dto/delete-users.dto';
import {
  DeleteUserResponseDto,
  DeleteUsersResponseDto,
  GetAllUsersResponseDto,
  StatusCountDto,
  RoleCountDto,
  PublicUserDto,
  UserProfileDataDto,
  TwoFactorStatusDto,
} from './dto/service-responses.dto';
import { AuthProfileResponseDto } from '../shared/dto/auth-profile-response.dto';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'Returns user profile',
    type: AuthProfileResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Get('profile')
  getProfile(@Request() req): Promise<AuthProfileResponseDto> {
    return this.usersService.getProfile(req.user.id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user profile data for profile settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns user profile data',
    type: UserProfileDataDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Get('profile-data')
  getProfileData(@Request() req): Promise<UserProfileDataDto> {
    return this.usersService.getProfileData(req.user.id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user 2FA status for two-factor settings' })
  @ApiResponse({
    status: 200,
    description: 'Returns user 2FA status',
    type: TwoFactorStatusDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Get('2fa-status')
  getTwoFactorStatus(@Request() req): Promise<TwoFactorStatusDto> {
    return this.usersService.getTwoFactorStatus(req.user.id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user details' })
  @ApiResponse({
    status: 200,
    description: 'User details updated successfully',
    type: UpdateUserResponseDto,
  })
  @UseInterceptors(FileInterceptor('avatar'))
  @Post('update-profile')
  updateProfile(
    @Request() req,
    @Body() updateUserDto: UpdateUserDto,
    @UploadedFile() avatar?: Express.Multer.File,
  ): Promise<UpdateUserResponseDto> {
    return this.usersService.updateProfile(req.user.id, updateUserDto, avatar);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update user details (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: AdminUpdateUserResponseDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post('admin/update-user')
  adminUpdateUser(
    @Body() updateUserDto: AdminUpdateUserDto,
  ): Promise<AdminUpdateUserResponseDto> {
    return this.usersService.adminUpdateUser(updateUserDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete user (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'User archived successfully',
    type: DeleteUserResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Delete(':id')
  deleteUser(@Param('id') id: string) {
    return this.usersService.deleteUser(id);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete multiple users (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Users archived successfully',
    type: DeleteUsersResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Post('delete-many')
  deleteUsers(
    @Body() deleteUsersDto: DeleteUsersDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ) {
    return this.usersService.deleteUsers(deleteUsersDto.userIds);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all users (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns filtered users with pagination',
    type: [GetAllUsersResponseDto],
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get()
  getAllUsers(@Query() filterDto: UserFilterDto) {
    return this.usersService.getAllUsers(filterDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user status counts (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns count of users by status',
    type: StatusCountDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get('status-counts')
  getStatusCounts(@Query() filterDto: UserFilterDto): Promise<StatusCountDto> {
    return this.usersService.getStatusCounts(filterDto);
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get user role counts (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Returns count of users by role',
    type: RoleCountDto,
  })
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @Get('role-counts')
  getRoleCounts(@Query() filterDto: UserFilterDto): Promise<RoleCountDto> {
    return this.usersService.getRoleCounts(filterDto);
  }

  @ApiOperation({ summary: 'Get public user information' })
  @ApiResponse({
    status: 200,
    description: 'Returns public user information',
    type: PublicUserDto,
  })
  @Get('public/:id')
  @SkipAuthentication()
  getPublicUserInfo(@Param('id') id: string) {
    return this.usersService.getPublicUserInfo(id);
  }
}
