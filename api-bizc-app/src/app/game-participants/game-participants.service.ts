import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  NotFoundException,
  Inject,
} from '@nestjs/common';
import { and, eq, ilike, desc, asc, inArray } from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { gameParticipants, games, users } from '../drizzle/schema/schema';
import { CreateGameParticipantDto } from './dto/create-game-participant.dto';
import { UpdateGameParticipantDto } from './dto/update-game-participant.dto';
import { GameParticipantDto } from './dto/game-participant.dto';
import { GameParticipantListDto } from './dto/game-participant-list.dto';

@Injectable()
export class GameParticipantsService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async create(
    userId: string,
    businessId: string | null,
    createGameParticipantDto: CreateGameParticipantDto,
  ): Promise<{ id: string }> {
    try {
      // For public endpoints, derive businessId from gameId
      let actualBusinessId = businessId;

      // Verify the game exists and get businessId if not provided
      const game = await this.db
        .select()
        .from(games)
        .where(
          and(
            eq(games.id, createGameParticipantDto.gameId),
            eq(games.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!game) {
        throw new NotFoundException('Game not found');
      }

      // Use game's businessId if not provided (for public endpoints)
      if (!actualBusinessId) {
        actualBusinessId = game.businessId;
      }

      // Verify businessId matches if both are provided
      if (businessId && game.businessId !== businessId) {
        throw new UnauthorizedException(
          'Game does not belong to this business',
        );
      }

      const result = await this.db
        .insert(gameParticipants)
        .values({
          businessId: actualBusinessId,
          gameId: createGameParticipantDto.gameId,
          firstName: createGameParticipantDto.firstName,
          lastName: createGameParticipantDto.lastName,
          phone: createGameParticipantDto.phone,
          promocode: createGameParticipantDto.promocode,
          isWinner: createGameParticipantDto.isWinner || false,
          winnerAt: createGameParticipantDto.isWinner ? new Date() : null,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: gameParticipants.id });

      return { id: result[0].id };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new ConflictException('Failed to create game participant');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createGameParticipantDto: CreateGameParticipantDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createGameParticipantDto);
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createGameParticipantDtos: CreateGameParticipantDto[],
  ): Promise<{ ids: string[] }> {
    const results = await Promise.all(
      createGameParticipantDtos.map((dto) =>
        this.create(userId, businessId, dto),
      ),
    );

    return { ids: results.map((result) => result.id) };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    firstName?: string,
    lastName?: string,
    phone?: string,
    gameId?: string,
    isWinner?: string,
    sort?: string,
  ): Promise<{
    data: GameParticipantListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [eq(gameParticipants.isDeleted, false)];

    // Add businessId filter if provided (for authenticated endpoints)
    if (businessId) {
      whereConditions.push(eq(gameParticipants.businessId, businessId));
    }

    // Add date range filters
    if (from) {
      whereConditions.push(eq(gameParticipants.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(eq(gameParticipants.createdAt, new Date(to)));
    }

    // Add search filters
    if (firstName) {
      whereConditions.push(ilike(gameParticipants.firstName, `%${firstName}%`));
    }
    if (lastName) {
      whereConditions.push(ilike(gameParticipants.lastName, `%${lastName}%`));
    }
    if (phone) {
      whereConditions.push(ilike(gameParticipants.phone, `%${phone}%`));
    }
    if (gameId) {
      whereConditions.push(eq(gameParticipants.gameId, gameId));
    }
    if (isWinner !== undefined) {
      whereConditions.push(eq(gameParticipants.isWinner, isWinner === 'true'));
    }

    // Build sort order
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isAsc = direction === 'asc';

      switch (field) {
        case 'firstName':
          orderBy = isAsc
            ? asc(gameParticipants.firstName)
            : desc(gameParticipants.firstName);
          break;
        case 'lastName':
          orderBy = isAsc
            ? asc(gameParticipants.lastName)
            : desc(gameParticipants.lastName);
          break;
        case 'phone':
          orderBy = isAsc
            ? asc(gameParticipants.phone)
            : desc(gameParticipants.phone);
          break;
        case 'createdAt':
          orderBy = isAsc
            ? asc(gameParticipants.createdAt)
            : desc(gameParticipants.createdAt);
          break;
        default:
          orderBy = desc(gameParticipants.createdAt);
      }
    } else {
      orderBy = desc(gameParticipants.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: gameParticipants.id })
      .from(gameParticipants)
      .leftJoin(games, eq(gameParticipants.gameId, games.id))
      .where(and(...whereConditions));

    const total = totalResult.length;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select({
        id: gameParticipants.id,
        firstName: gameParticipants.firstName,
        lastName: gameParticipants.lastName,
        phone: gameParticipants.phone,
        promocode: gameParticipants.promocode,
        isWinner: gameParticipants.isWinner,
        winnerAt: gameParticipants.winnerAt,
        gameId: gameParticipants.gameId,
        gameName: games.name,
        createdAt: gameParticipants.createdAt,
      })
      .from(gameParticipants)
      .leftJoin(games, eq(gameParticipants.gameId, games.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    return {
      data: results,
      meta: { total, page, totalPages },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<GameParticipantDto> {
    const whereConditions = [
      eq(gameParticipants.id, id),
      eq(gameParticipants.isDeleted, false),
    ];

    // Add businessId filter if provided (for authenticated endpoints)
    if (businessId) {
      whereConditions.push(eq(gameParticipants.businessId, businessId));
    }

    const result = await this.db
      .select({
        id: gameParticipants.id,
        businessId: gameParticipants.businessId,
        gameId: gameParticipants.gameId,
        gameName: games.name,
        firstName: gameParticipants.firstName,
        lastName: gameParticipants.lastName,
        phone: gameParticipants.phone,
        promocode: gameParticipants.promocode,
        isWinner: gameParticipants.isWinner,
        winnerAt: gameParticipants.winnerAt,
        createdBy: users.firstName,
        updatedBy: users.firstName,
        createdAt: gameParticipants.createdAt,
        updatedAt: gameParticipants.updatedAt,
      })
      .from(gameParticipants)
      .leftJoin(games, eq(gameParticipants.gameId, games.id))
      .leftJoin(users, eq(gameParticipants.createdBy, users.id))
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Game participant not found');
    }

    return result;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateGameParticipantDto: UpdateGameParticipantDto,
  ): Promise<{ id: string }> {
    const whereConditions = [
      eq(gameParticipants.id, id),
      eq(gameParticipants.isDeleted, false),
    ];

    // Add businessId filter if provided (for authenticated endpoints)
    if (businessId) {
      whereConditions.push(eq(gameParticipants.businessId, businessId));
    }

    // Check if game participant exists
    const existingParticipant = await this.db
      .select()
      .from(gameParticipants)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!existingParticipant) {
      throw new NotFoundException('Game participant not found');
    }

    // If gameId is being updated, verify the new game exists
    if (updateGameParticipantDto.gameId) {
      const gameWhereConditions = [
        eq(games.id, updateGameParticipantDto.gameId),
        eq(games.isDeleted, false),
      ];

      // Add businessId filter if provided
      if (businessId) {
        gameWhereConditions.push(eq(games.businessId, businessId));
      }

      const game = await this.db
        .select()
        .from(games)
        .where(and(...gameWhereConditions))
        .then((results) => results[0]);

      if (!game) {
        throw new NotFoundException('Game not found');
      }
    }

    const updateData: any = {
      updatedBy: userId,
      updatedAt: new Date(),
    };

    // Only update provided fields
    if (updateGameParticipantDto.gameId !== undefined) {
      updateData.gameId = updateGameParticipantDto.gameId;
    }
    if (updateGameParticipantDto.firstName !== undefined) {
      updateData.firstName = updateGameParticipantDto.firstName;
    }
    if (updateGameParticipantDto.lastName !== undefined) {
      updateData.lastName = updateGameParticipantDto.lastName;
    }
    if (updateGameParticipantDto.phone !== undefined) {
      updateData.phone = updateGameParticipantDto.phone;
    }
    if (updateGameParticipantDto.promocode !== undefined) {
      updateData.promocode = updateGameParticipantDto.promocode;
    }
    if (updateGameParticipantDto.isWinner !== undefined) {
      updateData.isWinner = updateGameParticipantDto.isWinner;
      // Set winnerAt timestamp if marking as winner
      if (updateGameParticipantDto.isWinner && !existingParticipant.winnerAt) {
        updateData.winnerAt = new Date();
      } else if (!updateGameParticipantDto.isWinner) {
        updateData.winnerAt = null;
      }
    }

    await this.db
      .update(gameParticipants)
      .set(updateData)
      .where(eq(gameParticipants.id, id));

    return { id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateGameParticipantDto: UpdateGameParticipantDto,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateGameParticipantDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string }> {
    const whereConditions = [
      eq(gameParticipants.id, id),
      eq(gameParticipants.isDeleted, false),
    ];

    // Add businessId filter if provided (for authenticated endpoints)
    if (businessId) {
      whereConditions.push(eq(gameParticipants.businessId, businessId));
    }

    const existingParticipant = await this.db
      .select()
      .from(gameParticipants)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    if (!existingParticipant) {
      throw new NotFoundException('Game participant not found');
    }

    await this.db
      .update(gameParticipants)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(gameParticipants.id, id));

    return { id };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (ids.length === 0) {
      return { deletedCount: 0, deletedIds: [] };
    }

    const whereConditions = [
      inArray(gameParticipants.id, ids),
      eq(gameParticipants.isDeleted, false),
    ];

    // Add businessId filter if provided (for authenticated endpoints)
    if (businessId) {
      whereConditions.push(eq(gameParticipants.businessId, businessId));
    }

    // Check which participants exist and belong to the business
    const existingParticipants = await this.db
      .select({ id: gameParticipants.id })
      .from(gameParticipants)
      .where(and(...whereConditions));

    const existingIds = existingParticipants.map((p) => p.id);

    if (existingIds.length === 0) {
      return { deletedCount: 0, deletedIds: [] };
    }

    await this.db
      .update(gameParticipants)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(gameParticipants.id, existingIds));

    return { deletedCount: existingIds.length, deletedIds: existingIds };
  }
}
