import {
  Injectable,
  ConflictException,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { eq, and, ilike, gte, lte, desc, asc, or, sql } from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreatePaymentMethodDto } from './dto/create-payment-method.dto';
import { UpdatePaymentMethodDto } from './dto/update-payment-method.dto';
import { PaymentMethodDto } from './dto/payment-method.dto';
import { PaymentMethodListDto } from './dto/payment-method-list.dto';
import { PaymentMethodSlimDto } from './dto/payment-method-slim.dto';
import { paymentMethods } from '../drizzle/schema/payment-methods.schema';
import { users } from '../drizzle/schema/users.schema';
import { PaymentMethodStatus } from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class PaymentMethodsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPaymentMethodDto: CreatePaymentMethodDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a payment method with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingPaymentMethod = await this.db
        .select()
        .from(paymentMethods)
        .where(
          and(
            eq(paymentMethods.businessId, businessId),
            ilike(paymentMethods.name, createPaymentMethodDto.name),
            eq(paymentMethods.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingPaymentMethod) {
        throw new ConflictException(
          `Payment method with name "${createPaymentMethodDto.name}" already exists`,
        );
      }

      // Insert new payment method
      const [paymentMethod] = await this.db
        .insert(paymentMethods)
        .values({
          businessId,
          name: createPaymentMethodDto.name,
          isCreditOrDebitCard:
            createPaymentMethodDto.isCreditOrDebitCard ?? false,
          status: createPaymentMethodDto.status ?? PaymentMethodStatus.ACTIVE,
          createdBy: userId,
        })
        .returning({ id: paymentMethods.id });

      // Log the activity
      await this.activityLogService.logCreate(
        paymentMethod.id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Created payment method: ${createPaymentMethodDto.name}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: paymentMethod.id };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create payment method');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPaymentMethodDto: CreatePaymentMethodDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createPaymentMethodDto, metadata);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createPaymentMethodDtos: CreatePaymentMethodDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!createPaymentMethodDtos || createPaymentMethodDtos.length === 0) {
      throw new BadRequestException('At least one payment method is required');
    }

    // Check for duplicate names within the request
    const names = createPaymentMethodDtos.map((dto) => dto.name.toLowerCase());
    const duplicateNames = names.filter(
      (name, index) => names.indexOf(name) !== index,
    );

    if (duplicateNames.length > 0) {
      throw new ConflictException(
        `Duplicate payment method names found: ${duplicateNames.join(', ')}`,
      );
    }

    // Check if any payment methods with the same names already exist
    const existingPaymentMethods = await this.db
      .select({ name: paymentMethods.name })
      .from(paymentMethods)
      .where(
        and(
          eq(paymentMethods.businessId, businessId),
          or(
            ...createPaymentMethodDtos.map((dto) =>
              ilike(paymentMethods.name, dto.name),
            ),
          ),
          eq(paymentMethods.isDeleted, false),
        ),
      );

    if (existingPaymentMethods.length > 0) {
      const existingNames = existingPaymentMethods.map((pm) => pm.name);
      throw new ConflictException(
        `Payment methods with these names already exist: ${existingNames.join(', ')}`,
      );
    }

    // Insert all payment methods
    const insertedPaymentMethods = await this.db
      .insert(paymentMethods)
      .values(
        createPaymentMethodDtos.map((dto) => ({
          businessId,
          name: dto.name,
          isCreditOrDebitCard: dto.isCreditOrDebitCard ?? false,
          status: dto.status ?? PaymentMethodStatus.ACTIVE,
          createdBy: userId,
        })),
      )
      .returning({ id: paymentMethods.id });

    // Log bulk create activity
    for (const paymentMethod of insertedPaymentMethods) {
      await this.activityLogService.logCreate(
        paymentMethod.id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Bulk created payment method`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return { ids: insertedPaymentMethods.map((pm) => pm.id) };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createPaymentMethodDtos: CreatePaymentMethodDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(
      userId,
      businessId,
      createPaymentMethodDtos,
      metadata,
    );
  }

  async findAll(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: PaymentMethodDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(paymentMethods.isDeleted, false),
      eq(paymentMethods.status, PaymentMethodStatus.ACTIVE),
      eq(paymentMethods.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(paymentMethods.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(paymentMethods.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(paymentMethods)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Get payment methods with user information
    const paymentMethodResults = await this.db
      .select({
        id: paymentMethods.id,
        businessId: paymentMethods.businessId,
        name: paymentMethods.name,
        isCreditOrDebitCard: paymentMethods.isCreditOrDebitCard,
        status: paymentMethods.status,
        createdAt: paymentMethods.createdAt,
        updatedAt: paymentMethods.updatedAt,
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        updatedByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
      })
      .from(paymentMethods)
      .leftJoin(users, eq(paymentMethods.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(paymentMethods.createdAt))
      .limit(limit)
      .offset(offset);

    const data: PaymentMethodDto[] = paymentMethodResults.map((result) => ({
      id: result.id,
      businessId: result.businessId,
      name: result.name,
      isCreditOrDebitCard: result.isCreditOrDebitCard,
      status: result.status,
      createdBy: result.createdByName || 'Unknown',
      updatedBy: result.updatedByName || undefined,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: PaymentMethodListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(paymentMethods.isDeleted, false),
      eq(paymentMethods.businessId, businessId),
    ];

    // Add individual filter conditions
    if (name) {
      whereConditions.push(ilike(paymentMethods.name, `%${name}%`));
    }

    if (
      status &&
      Object.values(PaymentMethodStatus).includes(status as PaymentMethodStatus)
    ) {
      whereConditions.push(
        eq(paymentMethods.status, status as PaymentMethodStatus),
      );
    }

    // Add date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(paymentMethods.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(paymentMethods.createdAt, toDate));
      }
    }

    // Parse and add advanced filters
    let advancedFilters: any[] = [];
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        if (Array.isArray(parsedFilters)) {
          advancedFilters = parsedFilters
            .map((filter) => {
              const { field, operator, value } = filter;
              switch (field) {
                case 'name':
                  return operator === 'contains'
                    ? ilike(paymentMethods.name, `%${value}%`)
                    : eq(paymentMethods.name, value);
                case 'status':
                  return eq(paymentMethods.status, value);
                case 'isCreditOrDebitCard':
                  return eq(
                    paymentMethods.isCreditOrDebitCard,
                    value === 'true',
                  );
                default:
                  return null;
              }
            })
            .filter(Boolean);
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Combine conditions based on join operator
    let finalConditions: any;
    if (advancedFilters.length > 0) {
      const baseCondition = and(...whereConditions);
      const advancedCondition =
        joinOperator === 'or'
          ? or(...advancedFilters)
          : and(...advancedFilters);
      finalConditions = and(baseCondition, advancedCondition);
    } else {
      finalConditions = and(...whereConditions);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(paymentMethods)
      .where(finalConditions);

    const total = Number(totalResult[0]?.count) || 0;
    const totalPages = Math.ceil(total / limit);

    // Determine sort order
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';
      switch (field) {
        case 'name':
          orderBy = isDesc
            ? desc(paymentMethods.name)
            : asc(paymentMethods.name);
          break;
        case 'status':
          orderBy = isDesc
            ? desc(paymentMethods.status)
            : asc(paymentMethods.status);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(paymentMethods.createdAt)
            : asc(paymentMethods.createdAt);
          break;
        case 'updatedAt':
          orderBy = isDesc
            ? desc(paymentMethods.updatedAt)
            : asc(paymentMethods.updatedAt);
          break;
        default:
          orderBy = desc(paymentMethods.createdAt);
      }
    } else {
      orderBy = desc(paymentMethods.createdAt);
    }

    // Get payment methods with optimized query (only essential fields)
    const paymentMethodResults = await this.db
      .select({
        id: paymentMethods.id,
        name: paymentMethods.name,
        isCreditOrDebitCard: paymentMethods.isCreditOrDebitCard,
        status: paymentMethods.status,
        createdAt: paymentMethods.createdAt,
        updatedAt: paymentMethods.updatedAt,
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
      })
      .from(paymentMethods)
      .leftJoin(users, eq(paymentMethods.createdBy, users.id))
      .where(finalConditions)
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    const data: PaymentMethodListDto[] = paymentMethodResults.map((result) => ({
      id: result.id,
      name: result.name,
      isCreditOrDebitCard: result.isCreditOrDebitCard,
      status: result.status,
      createdBy: result.createdByName || 'Unknown',
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<PaymentMethodDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const paymentMethodResult = await this.db
      .select({
        id: paymentMethods.id,
        businessId: paymentMethods.businessId,
        name: paymentMethods.name,
        isCreditOrDebitCard: paymentMethods.isCreditOrDebitCard,
        status: paymentMethods.status,
        createdAt: paymentMethods.createdAt,
        updatedAt: paymentMethods.updatedAt,
        createdByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        updatedByName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
      })
      .from(paymentMethods)
      .leftJoin(users, eq(paymentMethods.createdBy, users.id))
      .where(
        and(
          eq(paymentMethods.id, id),
          eq(paymentMethods.businessId, businessId),
          eq(paymentMethods.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!paymentMethodResult) {
      throw new NotFoundException('Payment method not found');
    }

    return {
      id: paymentMethodResult.id,
      businessId: paymentMethodResult.businessId,
      name: paymentMethodResult.name,
      isCreditOrDebitCard: paymentMethodResult.isCreditOrDebitCard,
      status: paymentMethodResult.status,
      createdBy: paymentMethodResult.createdByName || 'Unknown',
      updatedBy: paymentMethodResult.updatedByName || undefined,
      createdAt: paymentMethodResult.createdAt,
      updatedAt: paymentMethodResult.updatedAt,
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<PaymentMethodSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const paymentMethodResults = await this.db
      .select({
        id: paymentMethods.id,
        name: paymentMethods.name,
        isCreditOrDebitCard: paymentMethods.isCreditOrDebitCard,
      })
      .from(paymentMethods)
      .where(
        and(
          eq(paymentMethods.businessId, businessId),
          eq(paymentMethods.status, PaymentMethodStatus.ACTIVE),
          eq(paymentMethods.isDeleted, false),
        ),
      )
      .orderBy(asc(paymentMethods.name));

    return paymentMethodResults.map((result) => ({
      id: result.id,
      name: result.name,
      isCreditOrDebitCard: result.isCreditOrDebitCard,
    }));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePaymentMethodDto: UpdatePaymentMethodDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if payment method exists
    const existingPaymentMethod = await this.db
      .select()
      .from(paymentMethods)
      .where(
        and(
          eq(paymentMethods.id, id),
          eq(paymentMethods.businessId, businessId),
          eq(paymentMethods.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingPaymentMethod) {
      throw new NotFoundException('Payment method not found');
    }

    // Check for name conflicts if name is being updated
    if (
      updatePaymentMethodDto.name &&
      updatePaymentMethodDto.name !== existingPaymentMethod.name
    ) {
      const conflictingPaymentMethod = await this.db
        .select()
        .from(paymentMethods)
        .where(
          and(
            eq(paymentMethods.businessId, businessId),
            ilike(paymentMethods.name, updatePaymentMethodDto.name),
            eq(paymentMethods.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (conflictingPaymentMethod) {
        throw new ConflictException(
          `Payment method with name "${updatePaymentMethodDto.name}" already exists`,
        );
      }
    }

    // Update payment method
    await this.db
      .update(paymentMethods)
      .set({
        ...updatePaymentMethodDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(paymentMethods.id, id));

    // Log the activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.ACCOUNT,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePaymentMethodDto: UpdatePaymentMethodDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(
      userId,
      businessId,
      id,
      updatePaymentMethodDto,
      metadata,
    );
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if payment method exists
    const existingPaymentMethod = await this.db
      .select()
      .from(paymentMethods)
      .where(
        and(
          eq(paymentMethods.id, id),
          eq(paymentMethods.businessId, businessId),
          eq(paymentMethods.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingPaymentMethod) {
      throw new NotFoundException('Payment method not found');
    }

    // Soft delete the payment method
    await this.db
      .update(paymentMethods)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(paymentMethods.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ACCOUNT,
      userId,
      businessId,
      {
        reason: `Deleted payment method: ${existingPaymentMethod.name}`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    paymentMethodIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!paymentMethodIds || paymentMethodIds.length === 0) {
      throw new BadRequestException(
        'At least one payment method ID is required',
      );
    }

    // Check which payment methods exist and belong to the business
    const existingPaymentMethods = await this.db
      .select({ id: paymentMethods.id, name: paymentMethods.name })
      .from(paymentMethods)
      .where(
        and(
          eq(paymentMethods.businessId, businessId),
          or(...paymentMethodIds.map((id) => eq(paymentMethods.id, id))),
          eq(paymentMethods.isDeleted, false),
        ),
      );

    if (existingPaymentMethods.length === 0) {
      throw new NotFoundException('No payment methods found to delete');
    }

    const existingIds = existingPaymentMethods.map((pm) => pm.id);

    // Soft delete the payment methods
    await this.db
      .update(paymentMethods)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(paymentMethods.businessId, businessId),
          or(...existingIds.map((id) => eq(paymentMethods.id, id))),
        ),
      );

    // Log bulk delete activity
    for (const id of existingIds) {
      await this.activityLogService.logDelete(
        id,
        EntityType.ACCOUNT,
        userId,
        businessId,
        {
          reason: `Bulk deleted payment method`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      deletedCount: existingIds.length,
      deletedIds: existingIds,
    };
  }

  async checkNameAvailability(
    _userId: string,
    businessId: string | null,
    name: string,
    excludeId?: string,
  ): Promise<{ available: boolean; name: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(paymentMethods.businessId, businessId),
      ilike(paymentMethods.name, name),
      eq(paymentMethods.isDeleted, false),
    ];

    // Exclude current payment method if updating
    if (excludeId) {
      whereConditions.push(sql`${paymentMethods.id} != ${excludeId}`);
    }

    const existingPaymentMethod = await this.db
      .select()
      .from(paymentMethods)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return {
      available: !existingPaymentMethod,
      name,
    };
  }
}
