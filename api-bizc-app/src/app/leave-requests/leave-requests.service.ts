import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateLeaveRequestDto } from './dto/create-leave-request.dto';
import { UpdateLeaveRequestDto } from './dto/update-leave-request.dto';
import { LeaveRequestDto } from './dto/leave-request.dto';
import { LeaveRequestSlimDto } from './dto/leave-request-slim.dto';
import { ApproveLeaveRequestDto } from './dto/approve-leave-request.dto';
import { RejectLeaveRequestDto } from './dto/reject-leave-request.dto';
import { leaveRequests } from '../drizzle/schema/leave-requests.schema';
import { leaveTypes } from '../drizzle/schema/leave-types.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { eq, and, isNull, sql, gte, lte, desc, inArray } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { LeaveRequestStatus } from '../shared/types';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { UsersService } from '../users/users.service';

@Injectable()
export class LeaveRequestsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createLeaveRequestDto: CreateLeaveRequestDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate employee exists
      const employee = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createLeaveRequestDto.employeeId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!employee) {
        throw new BadRequestException('Employee not found');
      }

      // Validate leave type exists
      const leaveType = await this.db
        .select()
        .from(leaveTypes)
        .where(
          and(
            eq(leaveTypes.id, createLeaveRequestDto.leaveTypeId),
            eq(leaveTypes.businessId, businessId),
            eq(leaveTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!leaveType) {
        throw new BadRequestException('Leave type not found');
      }

      // Validate dates
      const startDate = new Date(createLeaveRequestDto.startDate);
      const endDate = new Date(createLeaveRequestDto.endDate);

      if (startDate > endDate) {
        throw new BadRequestException('Start date cannot be after end date');
      }

      // Create the leave request
      const [newLeaveRequest] = await this.db
        .insert(leaveRequests)
        .values({
          businessId,
          employeeId: createLeaveRequestDto.employeeId,
          leaveTypeId: createLeaveRequestDto.leaveTypeId,
          startDate: createLeaveRequestDto.startDate,
          endDate: createLeaveRequestDto.endDate,
          daysRequested: createLeaveRequestDto.daysRequested,
          reason: createLeaveRequestDto.reason,
          status: createLeaveRequestDto.status ?? LeaveRequestStatus.DRAFT,
          comments: createLeaveRequestDto.comments,
          createdBy: userId,
        })
        .returning();

      // Log the leave request creation activity
      await this.activityLogService.logCreate(
        newLeaveRequest.id,
        EntityType.LEAVE_REQUEST,
        userId,
        businessId,
        {
          reason: `Leave request for ${employee.firstName} ${employee.lastName} was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newLeaveRequest.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create leave request: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    status?: string,
    employeeId?: string,
  ): Promise<{
    data: LeaveRequestDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(leaveRequests.isDeleted, false),
      eq(leaveRequests.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(leaveRequests.startDate, from));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(leaveRequests.endDate, to));
      }
    }

    // Add status filtering if provided
    if (status) {
      const statusArray = status
        .split(',')
        .map((s) => s.trim() as LeaveRequestStatus);
      whereConditions.push(inArray(leaveRequests.status, statusArray));
    }

    // Add employee filtering if provided
    if (employeeId) {
      whereConditions.push(eq(leaveRequests.employeeId, employeeId));
    }

    // Find all leave requests with related data
    const result = await this.db
      .select({
        leaveRequest: leaveRequests,
        employee: {
          id: staffMembers.id,
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
        },
        approver: {
          id: sql<string | null>`approver.id`,
          firstName: sql<string | null>`approver.first_name`,
          lastName: sql<string | null>`approver.last_name`,
        },
      })
      .from(leaveRequests)
      .innerJoin(staffMembers, eq(leaveRequests.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveRequests.leaveTypeId, leaveTypes.id))
      .leftJoin(
        sql`${staffMembers} approver`,
        sql`${leaveRequests.approvedBy} = approver.id`,
      )
      .where(and(...whereConditions))
      .orderBy(desc(leaveRequests.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(leaveRequests)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: await Promise.all(
        result.map((item) => this.mapToLeaveRequestDto(item)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<LeaveRequestDto> {
    // Get the leave request with related data
    const result = await this.db
      .select({
        leaveRequest: leaveRequests,
        employee: {
          id: staffMembers.id,
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
        },
        approver: {
          id: sql<string | null>`approver.id`,
          firstName: sql<string | null>`approver.first_name`,
          lastName: sql<string | null>`approver.last_name`,
        },
      })
      .from(leaveRequests)
      .innerJoin(staffMembers, eq(leaveRequests.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveRequests.leaveTypeId, leaveTypes.id))
      .leftJoin(
        sql`${staffMembers} approver`,
        sql`${leaveRequests.approvedBy} = approver.id`,
      )
      .where(and(eq(leaveRequests.id, id), eq(leaveRequests.isDeleted, false)))
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== result.leaveRequest.businessId
    ) {
      throw new UnauthorizedException('Access denied to this leave request');
    }

    // Disable view logging for performance

    return await this.mapToLeaveRequestDto(result);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
    metadata?: ActivityMetadata,
  ): Promise<
    { id: string } | { error: string; message: string; statusCode: number }
  > {
    // Get the leave request
    const existingLeaveRequest = await this.db
      .select()
      .from(leaveRequests)
      .where(and(eq(leaveRequests.id, id), eq(leaveRequests.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingLeaveRequest.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this leave request',
      );
    }

    // Validate dates if being updated
    if (updateLeaveRequestDto.startDate && updateLeaveRequestDto.endDate) {
      const startDate = new Date(updateLeaveRequestDto.startDate);
      const endDate = new Date(updateLeaveRequestDto.endDate);

      if (startDate > endDate) {
        throw new BadRequestException('Start date cannot be after end date');
      }
    }

    try {
      // Update the leave request
      const [updatedLeaveRequest] = await this.db
        .update(leaveRequests)
        .set({
          ...updateLeaveRequestDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(leaveRequests.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.LEAVE_REQUEST,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      // Get the updated data with related information
      return {
        id: updatedLeaveRequest.id,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update leave request: ${error.message}`,
      );
    }
  }

  async approve(
    userId: string,
    businessId: string | null,
    id: string,
    approveLeaveRequestDto: ApproveLeaveRequestDto,
    metadata?: ActivityMetadata,
  ): Promise<LeaveRequestDto> {
    const existingLeaveRequest = await this.db
      .select()
      .from(leaveRequests)
      .where(and(eq(leaveRequests.id, id), eq(leaveRequests.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    if (businessId !== existingLeaveRequest.businessId) {
      throw new UnauthorizedException(
        'Access denied to approve this leave request',
      );
    }

    if (existingLeaveRequest.status === LeaveRequestStatus.APPROVED) {
      throw new ConflictException('Leave request is already approved');
    }

    // Update the leave request to approved status
    await this.db
      .update(leaveRequests)
      .set({
        status: LeaveRequestStatus.APPROVED,
        approvedBy: approveLeaveRequestDto.approvedBy,
        approvalDate: new Date(),
        comments: approveLeaveRequestDto.comments,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(leaveRequests.id, id));

    // Log the activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.LEAVE_REQUEST,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return await this.findOne(userId, id);
  }

  async reject(
    userId: string,
    businessId: string | null,
    id: string,
    rejectLeaveRequestDto: RejectLeaveRequestDto,
    metadata?: ActivityMetadata,
  ): Promise<LeaveRequestDto> {
    const existingLeaveRequest = await this.db
      .select()
      .from(leaveRequests)
      .where(and(eq(leaveRequests.id, id), eq(leaveRequests.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    if (businessId !== existingLeaveRequest.businessId) {
      throw new UnauthorizedException(
        'Access denied to reject this leave request',
      );
    }

    if (existingLeaveRequest.status === LeaveRequestStatus.REJECTED) {
      throw new ConflictException('Leave request is already rejected');
    }

    // Update the leave request to rejected status
    await this.db
      .update(leaveRequests)
      .set({
        status: LeaveRequestStatus.REJECTED,
        comments: rejectLeaveRequestDto.reason,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(leaveRequests.id, id));

    // Log the activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.LEAVE_REQUEST,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return await this.findOne(userId, id);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the leave request
    const existingLeaveRequest = await this.db
      .select()
      .from(leaveRequests)
      .where(and(eq(leaveRequests.id, id), eq(leaveRequests.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveRequest) {
      throw new NotFoundException(`Leave request with ID ${id} not found`);
    }

    if (businessId !== existingLeaveRequest.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this leave request',
      );
    }

    // Soft delete the leave request
    await this.db
      .update(leaveRequests)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(leaveRequests.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.LEAVE_REQUEST,
      userId,
      businessId,
      {
        reason: `Leave request was deleted`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Leave request with ID ${id} has been deleted`,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<LeaveRequestSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all leave requests with only essential fields
    const result = await this.db
      .select({
        id: leaveRequests.id,
        startDate: leaveRequests.startDate,
        endDate: leaveRequests.endDate,
        daysRequested: leaveRequests.daysRequested,
        status: leaveRequests.status,
        employeeName: sql<string>`${staffMembers.firstName} || ' ' || ${staffMembers.lastName}`,
        leaveTypeName: leaveTypes.leaveName,
      })
      .from(leaveRequests)
      .innerJoin(staffMembers, eq(leaveRequests.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveRequests.leaveTypeId, leaveTypes.id))
      .where(
        and(
          eq(leaveRequests.isDeleted, false),
          eq(leaveRequests.businessId, businessId),
        ),
      )
      .orderBy(desc(leaveRequests.createdAt));

    // Disable view logging for performance

    return result.map((item) => ({
      id: item.id.toString(),
      employeeName: item.employeeName,
      leaveTypeName: item.leaveTypeName,
      startDate: item.startDate,
      endDate: item.endDate,
      daysRequested: item.daysRequested,
      status: item.status,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createLeaveRequestDto: CreateLeaveRequestDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const leaveRequest = await this.create(
      userId,
      businessId,
      createLeaveRequestDto,
      metadata,
    );
    return { id: leaveRequest.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeaveRequestDto: UpdateLeaveRequestDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateLeaveRequestDto, metadata);
    return { id };
  }

  private async mapToLeaveRequestDto(data: any): Promise<LeaveRequestDto> {
    const { leaveRequest, employee, leaveType, approver } = data;

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      leaveRequest.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (leaveRequest.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        leaveRequest.updatedBy.toString(),
      );
    }

    const leaveRequestDto: LeaveRequestDto = {
      id: leaveRequest.id.toString(),
      businessId: leaveRequest.businessId.toString(),
      employeeId: leaveRequest.employeeId.toString(),
      employeeName: `${employee.firstName} ${employee.lastName}`,
      leaveTypeId: leaveRequest.leaveTypeId.toString(),
      leaveTypeName: leaveType.leaveName,
      startDate: leaveRequest.startDate,
      endDate: leaveRequest.endDate,
      daysRequested: leaveRequest.daysRequested,
      reason: leaveRequest.reason,
      status: leaveRequest.status,
      approvedBy: leaveRequest.approvedBy?.toString(),
      approvedByName:
        approver?.firstName && approver?.lastName
          ? `${approver.firstName} ${approver.lastName}`
          : undefined,
      approvalDate: leaveRequest.approvalDate,
      comments: leaveRequest.comments,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: leaveRequest.createdAt,
      updatedAt: leaveRequest.updatedAt,
    };

    return leaveRequestDto;
  }
}
