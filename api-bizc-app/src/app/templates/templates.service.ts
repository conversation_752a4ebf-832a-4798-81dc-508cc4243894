import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { and, eq, isNull, ilike, gte, lte, desc, count } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MediaService } from '../media/media.service';

import { StatusType } from '../shared/types/common.enum';

// Import schemas
import {
  smsTemplates,
  whatsappTemplates,
  emailTemplates,
} from '../drizzle/schema/templates.schema';

// Import DTOs
import { CreateSmsTemplateDto } from './dto/create-sms-template.dto';
import { CreateWhatsappTemplateDto } from './dto/create-whatsapp-template.dto';
import { CreateEmailTemplateDto } from './dto/create-email-template.dto';
import { UpdateSmsTemplateDto } from './dto/update-sms-template.dto';
import { UpdateWhatsappTemplateDto } from './dto/update-whatsapp-template.dto';
import { UpdateEmailTemplateDto } from './dto/update-email-template.dto';
import { SmsTemplateResponseDto } from './dto/sms-template-response.dto';
import { WhatsappTemplateResponseDto } from './dto/whatsapp-template-response.dto';
import { EmailTemplateResponseDto } from './dto/email-template-response.dto';
import { SmsTemplateSlimDto } from './dto/sms-template-slim.dto';
import { WhatsappTemplateSlimDto } from './dto/whatsapp-template-slim.dto';
import { EmailTemplateSlimDto } from './dto/email-template-slim.dto';
import { PaginatedSmsTemplatesResponseDto } from './dto/paginated-sms-templates-response.dto';
import { PaginatedWhatsappTemplatesResponseDto } from './dto/paginated-whatsapp-templates-response.dto';
import { PaginatedEmailTemplatesResponseDto } from './dto/paginated-email-templates-response.dto';
import {
  CheckTemplateNameAvailabilityDto,
  TemplateNameAvailabilityResponseDto,
  TemplateTypeEnum,
} from './dto/check-template-name-availability.dto';

@Injectable()
export class TemplatesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  // SMS Templates Methods
  async createSmsTemplate(
    userId: string,
    businessId: string | null,
    createSmsTemplateDto: CreateSmsTemplateDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if SMS template with same name exists
      const existingTemplate = await this.db
        .select()
        .from(smsTemplates)
        .where(
          and(
            eq(smsTemplates.businessId, businessId),
            ilike(smsTemplates.name, createSmsTemplateDto.name),
            eq(smsTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingTemplate) {
        throw new ConflictException(
          `SMS template with name "${createSmsTemplateDto.name}" already exists`,
        );
      }

      // Create SMS template
      const [newTemplate] = await this.db
        .insert(smsTemplates)
        .values({
          businessId,
          name: createSmsTemplateDto.name,
          content: createSmsTemplateDto.content,
          category: createSmsTemplateDto.category,
          type: createSmsTemplateDto.type,
          description: createSmsTemplateDto.description,
          status: createSmsTemplateDto.status ?? StatusType.ACTIVE,
          language: createSmsTemplateDto.language,
          createdBy: userId,
        })
        .returning();

      // Log activity
      await this.activityLogService.logCreate(
        newTemplate.id,
        EntityType.SMS_TEMPLATE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newTemplate.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create SMS template: ${error.message}`,
      );
    }
  }

  async findAllSmsTemplates(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    category?: string,
    status?: string,
  ): Promise<PaginatedSmsTemplatesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(smsTemplates.isDeleted, false),
      eq(smsTemplates.businessId, businessId),
    ];

    // Add filters
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(smsTemplates.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(smsTemplates.createdAt, toDate));
      }
    }

    if (name) {
      whereConditions.push(ilike(smsTemplates.name, `%${name}%`));
    }

    if (category) {
      whereConditions.push(eq(smsTemplates.category, category as any));
    }

    if (status) {
      whereConditions.push(eq(smsTemplates.status, status as StatusType));
    }

    // Get total count
    const [totalResult] = await this.db
      .select({ count: count() })
      .from(smsTemplates)
      .where(and(...whereConditions));

    const total = totalResult.count;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select()
      .from(smsTemplates)
      .where(and(...whereConditions))
      .orderBy(desc(smsTemplates.createdAt))
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      results.map((template) => this.mapToSmsTemplateResponseDto(template)),
    );

    // Note: Removed logging for list views as they generate too much activity log data
    // Consider only logging specific template views instead

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async findSmsTemplateById(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<SmsTemplateResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const template = await this.db
      .select()
      .from(smsTemplates)
      .where(
        and(
          eq(smsTemplates.id, id),
          eq(smsTemplates.businessId, businessId),
          eq(smsTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!template) {
      throw new NotFoundException(`SMS template with ID ${id} not found`);
    }

    // Note: Removed view logging to reduce activity log volume
    // Consider implementing only for critical template access

    return this.mapToSmsTemplateResponseDto(template);
  }

  async updateSmsTemplate(
    userId: string,
    businessId: string | null,
    id: string,
    updateSmsTemplateDto: UpdateSmsTemplateDto,
    metadata?: ActivityMetadata,
  ): Promise<SmsTemplateResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get existing template
    const existingTemplate = await this.db
      .select()
      .from(smsTemplates)
      .where(
        and(
          eq(smsTemplates.id, id),
          eq(smsTemplates.businessId, businessId),
          eq(smsTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingTemplate) {
      throw new NotFoundException(`SMS template with ID ${id} not found`);
    }

    // Check name uniqueness if name is being updated
    if (
      updateSmsTemplateDto.name &&
      updateSmsTemplateDto.name !== existingTemplate.name
    ) {
      const duplicateTemplate = await this.db
        .select()
        .from(smsTemplates)
        .where(
          and(
            eq(smsTemplates.businessId, businessId),
            ilike(smsTemplates.name, updateSmsTemplateDto.name),
            eq(smsTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (duplicateTemplate) {
        throw new ConflictException(
          `SMS template with name "${updateSmsTemplateDto.name}" already exists`,
        );
      }
    }

    // Update template
    const [updatedTemplate] = await this.db
      .update(smsTemplates)
      .set({
        ...updateSmsTemplateDto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(smsTemplates.id, id))
      .returning();

    // Log activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.SMS_TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.mapToSmsTemplateResponseDto(updatedTemplate);
  }

  async deleteSmsTemplate(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingTemplate = await this.db
      .select()
      .from(smsTemplates)
      .where(
        and(
          eq(smsTemplates.id, id),
          eq(smsTemplates.businessId, businessId),
          eq(smsTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingTemplate) {
      throw new NotFoundException(`SMS template with ID ${id} not found`);
    }

    // Soft delete
    await this.db
      .update(smsTemplates)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(smsTemplates.id, id));

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.SMS_TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id,
      message: 'SMS template deleted successfully',
    };
  }

  // WhatsApp Templates Methods
  async createWhatsappTemplate(
    userId: string,
    businessId: string | null,
    createWhatsappTemplateDto: CreateWhatsappTemplateDto,
    mediaFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if WhatsApp template with same name exists
      const existingTemplate = await this.db
        .select()
        .from(whatsappTemplates)
        .where(
          and(
            eq(whatsappTemplates.businessId, businessId),
            ilike(whatsappTemplates.name, createWhatsappTemplateDto.name),
            eq(whatsappTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingTemplate) {
        throw new ConflictException(
          `WhatsApp template with name "${createWhatsappTemplateDto.name}" already exists`,
        );
      }

      let mediaId: string | undefined;

      // Upload media if provided
      if (mediaFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          mediaFile,
          'templates/whatsapp',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Create WhatsApp template
      const [newTemplate] = await this.db
        .insert(whatsappTemplates)
        .values({
          businessId,
          name: createWhatsappTemplateDto.name,
          content: createWhatsappTemplateDto.content,
          category: createWhatsappTemplateDto.category,
          type: createWhatsappTemplateDto.type,
          description: createWhatsappTemplateDto.description,
          mediaId,
          captionEnabled: createWhatsappTemplateDto.captionEnabled ?? false,
          status: createWhatsappTemplateDto.status ?? StatusType.ACTIVE,
          language: createWhatsappTemplateDto.language,
          createdBy: userId,
        })
        .returning();

      // Log activity
      await this.activityLogService.logCreate(
        newTemplate.id,
        EntityType.WHATSAPP_TEMPLATE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newTemplate.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create WhatsApp template: ${error.message}`,
      );
    }
  }

  async findAllWhatsappTemplates(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    category?: string,
    status?: string,
  ): Promise<PaginatedWhatsappTemplatesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(whatsappTemplates.isDeleted, false),
      eq(whatsappTemplates.businessId, businessId),
    ];

    // Add filters
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(whatsappTemplates.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(whatsappTemplates.createdAt, toDate));
      }
    }

    if (name) {
      whereConditions.push(ilike(whatsappTemplates.name, `%${name}%`));
    }

    if (category) {
      whereConditions.push(eq(whatsappTemplates.category, category as any));
    }

    if (status) {
      whereConditions.push(eq(whatsappTemplates.status, status as StatusType));
    }

    // Get total count
    const [totalResult] = await this.db
      .select({ count: count() })
      .from(whatsappTemplates)
      .where(and(...whereConditions));

    const total = totalResult.count;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select()
      .from(whatsappTemplates)
      .where(and(...whereConditions))
      .orderBy(desc(whatsappTemplates.createdAt))
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      results.map((template) =>
        this.mapToWhatsappTemplateResponseDto(template),
      ),
    );

    // Note: Removed logging for list views as they generate too much activity log data
    // Consider only logging specific template views instead

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  private async mapToSmsTemplateResponseDto(
    template: typeof smsTemplates.$inferSelect,
  ): Promise<SmsTemplateResponseDto> {
    return {
      id: template.id,
      businessId: template.businessId,
      name: template.name,
      content: template.content,
      category: template.category,
      type: template.type,
      description: template.description,
      status: template.status,
      language: template.language,
      createdBy: template.createdBy,
      updatedBy: template.updatedBy,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  }

  private async mapToWhatsappTemplateResponseDto(
    template: typeof whatsappTemplates.$inferSelect,
  ): Promise<WhatsappTemplateResponseDto> {
    let mediaUrl: string | undefined;

    // Get signed URL for media if exists
    if (template.mediaId) {
      try {
        mediaUrl = await this.mediaService.generateSignedUrlForMedia(
          template.mediaId,
          template.businessId,
          'templates/whatsapp',
          60, // 60 minutes expiration
        );
      } catch (error) {
        // Log error but don't fail the request
        console.warn(
          `Failed to get signed URL for media ${template.mediaId}:`,
          error,
        );
      }
    }

    return {
      id: template.id,
      businessId: template.businessId,
      name: template.name,
      content: template.content,
      category: template.category,
      type: template.type,
      description: template.description,
      mediaId: template.mediaId,
      mediaUrl,
      captionEnabled: template.captionEnabled,
      status: template.status,
      language: template.language,
      createdBy: template.createdBy,
      updatedBy: template.updatedBy,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  }

  async findWhatsappTemplateById(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<WhatsappTemplateResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const template = await this.db
      .select()
      .from(whatsappTemplates)
      .where(
        and(
          eq(whatsappTemplates.id, id),
          eq(whatsappTemplates.businessId, businessId),
          eq(whatsappTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!template) {
      throw new NotFoundException(`WhatsApp template with ID ${id} not found`);
    }

    // Note: Removed view logging to reduce activity log volume
    // Consider implementing only for critical template access

    return this.mapToWhatsappTemplateResponseDto(template);
  }

  async updateWhatsappTemplate(
    userId: string,
    businessId: string | null,
    id: string,
    updateWhatsappTemplateDto: UpdateWhatsappTemplateDto,
    mediaFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<WhatsappTemplateResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get existing template
    const existingTemplate = await this.db
      .select()
      .from(whatsappTemplates)
      .where(
        and(
          eq(whatsappTemplates.id, id),
          eq(whatsappTemplates.businessId, businessId),
          eq(whatsappTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingTemplate) {
      throw new NotFoundException(`WhatsApp template with ID ${id} not found`);
    }

    // Check name uniqueness if name is being updated
    if (
      updateWhatsappTemplateDto.name &&
      updateWhatsappTemplateDto.name !== existingTemplate.name
    ) {
      const duplicateTemplate = await this.db
        .select()
        .from(whatsappTemplates)
        .where(
          and(
            eq(whatsappTemplates.businessId, businessId),
            ilike(whatsappTemplates.name, updateWhatsappTemplateDto.name),
            eq(whatsappTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (duplicateTemplate) {
        throw new ConflictException(
          `WhatsApp template with name "${updateWhatsappTemplateDto.name}" already exists`,
        );
      }
    }

    let mediaId = existingTemplate.mediaId;

    // Upload new media if provided
    if (mediaFile) {
      const uploadedMedia = await this.mediaService.uploadMedia(
        mediaFile,
        'templates/whatsapp',
        businessId,
        userId,
      );
      mediaId = uploadedMedia.id;
    }

    // Update template
    const [updatedTemplate] = await this.db
      .update(whatsappTemplates)
      .set({
        ...updateWhatsappTemplateDto,
        mediaId,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(whatsappTemplates.id, id))
      .returning();

    // Log activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.WHATSAPP_TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.mapToWhatsappTemplateResponseDto(updatedTemplate);
  }

  async deleteWhatsappTemplate(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingTemplate = await this.db
      .select()
      .from(whatsappTemplates)
      .where(
        and(
          eq(whatsappTemplates.id, id),
          eq(whatsappTemplates.businessId, businessId),
          eq(whatsappTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingTemplate) {
      throw new NotFoundException(`WhatsApp template with ID ${id} not found`);
    }

    // Soft delete
    await this.db
      .update(whatsappTemplates)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(whatsappTemplates.id, id));

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.WHATSAPP_TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id,
      message: 'WhatsApp template deleted successfully',
    };
  }

  // Email Templates Methods
  async createEmailTemplate(
    userId: string,
    businessId: string | null,
    createEmailTemplateDto: CreateEmailTemplateDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if email template with same name exists
      const existingTemplate = await this.db
        .select()
        .from(emailTemplates)
        .where(
          and(
            eq(emailTemplates.businessId, businessId),
            ilike(emailTemplates.name, createEmailTemplateDto.name),
            eq(emailTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingTemplate) {
        throw new ConflictException(
          `Email template with name "${createEmailTemplateDto.name}" already exists`,
        );
      }

      // Create email template first to get the ID for reference
      const [newTemplate] = await this.db
        .insert(emailTemplates)
        .values({
          businessId,
          name: createEmailTemplateDto.name,
          subject: createEmailTemplateDto.subject,
          category: createEmailTemplateDto.category,
          type: createEmailTemplateDto.type,
          description: createEmailTemplateDto.description,
          textContent: createEmailTemplateDto.textContent,
          htmlContent: createEmailTemplateDto.htmlContent,
          ampContent: createEmailTemplateDto.ampContent,
          fromEmail: createEmailTemplateDto.fromEmail || '<EMAIL>',
          fromName: createEmailTemplateDto.fromName,
          replyToEmail: createEmailTemplateDto.replyToEmail,
          defaultCcEmails: createEmailTemplateDto.defaultCc,
          defaultBccEmails: createEmailTemplateDto.defaultBcc,
          status: createEmailTemplateDto.status ?? StatusType.ACTIVE,
          language: createEmailTemplateDto.language,
          createdBy: userId,
        })
        .returning();

      // Upload attachments if provided (using the template ID as reference)
      if (attachmentFiles && attachmentFiles.length > 0) {
        await this.mediaService.uploadMultipleMediaWithReference(
          attachmentFiles,
          'templates/email/attachments',
          businessId,
          userId,
          newTemplate.id,
        );
      }

      // Log activity
      await this.activityLogService.logCreate(
        newTemplate.id,
        EntityType.EMAIL_TEMPLATE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: newTemplate.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create email template: ${error.message}`,
      );
    }
  }

  async findAllEmailTemplates(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    category?: string,
    status?: string,
  ): Promise<PaginatedEmailTemplatesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(emailTemplates.isDeleted, false),
      eq(emailTemplates.businessId, businessId),
    ];

    // Add filters
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(emailTemplates.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(emailTemplates.createdAt, toDate));
      }
    }

    if (name) {
      whereConditions.push(ilike(emailTemplates.name, `%${name}%`));
    }

    if (category) {
      whereConditions.push(eq(emailTemplates.category, category as any));
    }

    if (status) {
      whereConditions.push(eq(emailTemplates.status, status as StatusType));
    }

    // Get total count
    const [totalResult] = await this.db
      .select({ count: count() })
      .from(emailTemplates)
      .where(and(...whereConditions));

    const total = totalResult.count;
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const results = await this.db
      .select()
      .from(emailTemplates)
      .where(and(...whereConditions))
      .orderBy(desc(emailTemplates.createdAt))
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      results.map((template) => this.mapToEmailTemplateResponseDto(template)),
    );

    // Note: Removed logging for list views as they generate too much activity log data
    // Consider only logging specific template views instead

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async findEmailTemplateById(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<EmailTemplateResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const template = await this.db
      .select()
      .from(emailTemplates)
      .where(
        and(
          eq(emailTemplates.id, id),
          eq(emailTemplates.businessId, businessId),
          eq(emailTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!template) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }

    // Note: Removed view logging to reduce activity log volume
    // Consider implementing only for critical template access

    return this.mapToEmailTemplateResponseDto(template);
  }

  async updateEmailTemplate(
    userId: string,
    businessId: string | null,
    id: string,
    updateEmailTemplateDto: UpdateEmailTemplateDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<EmailTemplateResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get existing template
    const existingTemplate = await this.db
      .select()
      .from(emailTemplates)
      .where(
        and(
          eq(emailTemplates.id, id),
          eq(emailTemplates.businessId, businessId),
          eq(emailTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingTemplate) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }

    // Check name uniqueness if name is being updated
    if (
      updateEmailTemplateDto.name &&
      updateEmailTemplateDto.name !== existingTemplate.name
    ) {
      const duplicateTemplate = await this.db
        .select()
        .from(emailTemplates)
        .where(
          and(
            eq(emailTemplates.businessId, businessId),
            ilike(emailTemplates.name, updateEmailTemplateDto.name),
            eq(emailTemplates.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (duplicateTemplate) {
        throw new ConflictException(
          `Email template with name "${updateEmailTemplateDto.name}" already exists`,
        );
      }
    }

    // Update template
    const [updatedTemplate] = await this.db
      .update(emailTemplates)
      .set({
        ...updateEmailTemplateDto,
        defaultCcEmails: updateEmailTemplateDto.defaultCc,
        defaultBccEmails: updateEmailTemplateDto.defaultBcc,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(emailTemplates.id, id))
      .returning();

    // Handle attachment updates if provided
    if (attachmentFiles && attachmentFiles.length > 0) {
      await this.mediaService.updateMediaForReference(
        id,
        attachmentFiles,
        'templates/email/attachments',
        businessId,
        userId,
      );
    }

    // Log activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.EMAIL_TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.mapToEmailTemplateResponseDto(updatedTemplate);
  }

  async deleteEmailTemplate(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingTemplate = await this.db
      .select()
      .from(emailTemplates)
      .where(
        and(
          eq(emailTemplates.id, id),
          eq(emailTemplates.businessId, businessId),
          eq(emailTemplates.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingTemplate) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }

    // Soft delete
    await this.db
      .update(emailTemplates)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(emailTemplates.id, id));

    // Delete associated attachments
    await this.mediaService.deleteMediaByReferenceId(
      id,
      businessId,
      'templates/email/attachments',
    );

    // Log activity
    await this.activityLogService.logDelete(
      id,
      EntityType.EMAIL_TEMPLATE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id,
      message: 'Email template deleted successfully',
    };
  }

  // Utility Methods
  async findAllSmsTemplatesSlim(
    businessId: string | null,
  ): Promise<SmsTemplateSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const templates = await this.db
      .select({
        id: smsTemplates.id,
        name: smsTemplates.name,
        category: smsTemplates.category,
        type: smsTemplates.type,
        status: smsTemplates.status,
      })
      .from(smsTemplates)
      .where(
        and(
          eq(smsTemplates.businessId, businessId),
          eq(smsTemplates.status, StatusType.ACTIVE),
          eq(smsTemplates.isDeleted, false),
        ),
      )
      .orderBy(smsTemplates.name);

    return templates.map((template) => ({
      id: template.id,
      name: template.name,
      category: template.category,
      type: template.type,
      status: template.status,
    }));
  }

  async findAllWhatsappTemplatesSlim(
    businessId: string | null,
  ): Promise<WhatsappTemplateSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const templates = await this.db
      .select({
        id: whatsappTemplates.id,
        name: whatsappTemplates.name,
        category: whatsappTemplates.category,
        type: whatsappTemplates.type,
        status: whatsappTemplates.status,
      })
      .from(whatsappTemplates)
      .where(
        and(
          eq(whatsappTemplates.businessId, businessId),
          eq(whatsappTemplates.status, StatusType.ACTIVE),
          eq(whatsappTemplates.isDeleted, false),
        ),
      )
      .orderBy(whatsappTemplates.name);

    return templates.map((template) => ({
      id: template.id,
      name: template.name,
      category: template.category,
      type: template.type,
      status: template.status,
    }));
  }

  async findAllEmailTemplatesSlim(
    businessId: string | null,
  ): Promise<EmailTemplateSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const templates = await this.db
      .select({
        id: emailTemplates.id,
        name: emailTemplates.name,
        subject: emailTemplates.subject,
        category: emailTemplates.category,
        type: emailTemplates.type,
        status: emailTemplates.status,
      })
      .from(emailTemplates)
      .where(
        and(
          eq(emailTemplates.businessId, businessId),
          eq(emailTemplates.status, StatusType.ACTIVE),
          eq(emailTemplates.isDeleted, false),
        ),
      )
      .orderBy(emailTemplates.name);

    return templates.map((template) => ({
      id: template.id,
      name: template.name,
      subject: template.subject,
      category: template.category,
      type: template.type,
      status: template.status,
    }));
  }

  async checkNameAvailability(
    businessId: string | null,
    checkNameDto: CheckTemplateNameAvailabilityDto,
  ): Promise<TemplateNameAvailabilityResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const { name, templateType } = checkNameDto;
    let isAvailable = true;

    // Check based on template type
    switch (templateType) {
      case TemplateTypeEnum.SMS: {
        const smsTemplate = await this.db
          .select()
          .from(smsTemplates)
          .where(
            and(
              eq(smsTemplates.businessId, businessId),
              ilike(smsTemplates.name, name),
              eq(smsTemplates.isDeleted, false),
            ),
          )
          .then((results) => results[0]);
        isAvailable = !smsTemplate;
        break;
      }

      case TemplateTypeEnum.WHATSAPP: {
        const whatsappTemplate = await this.db
          .select()
          .from(whatsappTemplates)
          .where(
            and(
              eq(whatsappTemplates.businessId, businessId),
              ilike(whatsappTemplates.name, name),
              eq(whatsappTemplates.isDeleted, false),
            ),
          )
          .then((results) => results[0]);
        isAvailable = !whatsappTemplate;
        break;
      }

      case TemplateTypeEnum.EMAIL: {
        const emailTemplate = await this.db
          .select()
          .from(emailTemplates)
          .where(
            and(
              eq(emailTemplates.businessId, businessId),
              ilike(emailTemplates.name, name),
              eq(emailTemplates.isDeleted, false),
            ),
          )
          .then((results) => results[0]);
        isAvailable = !emailTemplate;
        break;
      }

      default:
        throw new BadRequestException('Invalid template type');
    }

    return {
      name,
      templateType,
      available: isAvailable,
      message: isAvailable
        ? 'Template name is available'
        : 'Template name is already taken',
    };
  }

  // Private mapping methods
  private mapToEmailTemplateResponseDto(
    template: typeof emailTemplates.$inferSelect,
  ): EmailTemplateResponseDto {
    return {
      id: template.id,
      businessId: template.businessId,
      name: template.name,
      subject: template.subject,
      textContent: template.textContent,
      htmlContent: template.htmlContent,
      ampContent: template.ampContent,
      fromEmail: template.fromEmail,
      fromName: template.fromName,
      replyToEmail: template.replyToEmail,
      defaultCc: template.defaultCcEmails,
      defaultBcc: template.defaultBccEmails,
      category: template.category,
      type: template.type,
      description: template.description,
      status: template.status,
      language: template.language,
      createdBy: template.createdBy,
      updatedBy: template.updatedBy,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  }
}
