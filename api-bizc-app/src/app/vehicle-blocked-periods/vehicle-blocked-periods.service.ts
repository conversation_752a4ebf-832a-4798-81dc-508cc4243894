import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateVehicleBlockedPeriodDto } from './dto/create-vehicle-blocked-period.dto';
import { UpdateVehicleBlockedPeriodDto } from './dto/update-vehicle-blocked-period.dto';
import { VehicleBlockedPeriodDto } from './dto/vehicle-blocked-period.dto';
import { vehicleBlockedPeriods } from '../drizzle/schema/vehicle-blocked-periods.schema';
import { vehicles } from '../drizzle/schema/vehicles.schema';
import { eq, and, isNull, gte, lte, desc, ne, sql, inArray } from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class VehicleBlockedPeriodsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createVehicleBlockedPeriodDto: CreateVehicleBlockedPeriodDto,
    metadata?: ActivityMetadata,
  ): Promise<VehicleBlockedPeriodDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify vehicle belongs to the business
      const vehicle = await this.db
        .select()
        .from(vehicles)
        .where(
          and(
            eq(vehicles.id, createVehicleBlockedPeriodDto.vehicleId),
            eq(vehicles.businessId, businessId),
            isNull(vehicles.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!vehicle) {
        throw new BadRequestException(
          'Vehicle not found or does not belong to this business',
        );
      }

      // Check for overlapping blocked periods
      const overlappingPeriods = await this.checkForOverlappingPeriods(
        businessId,
        createVehicleBlockedPeriodDto.vehicleId,
        createVehicleBlockedPeriodDto.startDate,
        createVehicleBlockedPeriodDto.startTime,
        createVehicleBlockedPeriodDto.endDate,
        createVehicleBlockedPeriodDto.endTime,
      );

      if (overlappingPeriods.length > 0) {
        throw new ConflictException(
          'The specified time period overlaps with an existing blocked period',
        );
      }

      // Validate that start date/time is before end date/time
      const startDateTime = new Date(
        `${createVehicleBlockedPeriodDto.startDate}T${createVehicleBlockedPeriodDto.startTime}`,
      );
      const endDateTime = new Date(
        `${createVehicleBlockedPeriodDto.endDate}T${createVehicleBlockedPeriodDto.endTime}`,
      );

      if (startDateTime >= endDateTime) {
        throw new BadRequestException(
          'Start date/time must be before end date/time',
        );
      }

      // Insert new vehicle blocked period
      const [newPeriod] = await this.db
        .insert(vehicleBlockedPeriods)
        .values({
          businessId,
          vehicleId: createVehicleBlockedPeriodDto.vehicleId,
          startDate: createVehicleBlockedPeriodDto.startDate,
          startTime: createVehicleBlockedPeriodDto.startTime,
          endDate: createVehicleBlockedPeriodDto.endDate,
          endTime: createVehicleBlockedPeriodDto.endTime,
          description: createVehicleBlockedPeriodDto.description,
          color: createVehicleBlockedPeriodDto.color || '#FF0000',
          createdBy: userId,
        })
        .returning();

      // Log the vehicle blocked period creation activity
      await this.activityLogService.logCreate(
        newPeriod.id,
        EntityType.VEHICLE_BLOCKED_PERIOD,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToVehicleBlockedPeriodDto(newPeriod);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create vehicle blocked period: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    vehicleId?: string,
    from?: string,
    to?: string,
  ): Promise<{
    data: VehicleBlockedPeriodDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(vehicleBlockedPeriods.deletedAt),
      eq(vehicleBlockedPeriods.businessId, businessId),
    ];

    // Add vehicle filter if provided
    if (vehicleId) {
      whereConditions.push(eq(vehicleBlockedPeriods.vehicleId, vehicleId));
    }

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(vehicleBlockedPeriods.startDate, from));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(vehicleBlockedPeriods.endDate, to));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(vehicleBlockedPeriods)
      .where(and(...whereConditions));

    const total = Number(totalResult[0]?.count || 0);
    const totalPages = Math.ceil(total / limit);

    // Get paginated results
    const periods = await this.db
      .select()
      .from(vehicleBlockedPeriods)
      .where(and(...whereConditions))
      .orderBy(
        desc(vehicleBlockedPeriods.startDate),
        desc(vehicleBlockedPeriods.startTime),
      )
      .limit(limit)
      .offset(offset);

    const data = await Promise.all(
      periods.map((period) => this.mapToVehicleBlockedPeriodDto(period)),
    );

    return {
      data,
      meta: { total, page, totalPages },
    };
  }

  async findOne(userId: string, id: string): Promise<VehicleBlockedPeriodDto> {
    try {
      const period = await this.db
        .select()
        .from(vehicleBlockedPeriods)
        .where(
          and(
            eq(vehicleBlockedPeriods.id, id),
            isNull(vehicleBlockedPeriods.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!period) {
        throw new NotFoundException('Vehicle blocked period not found');
      }

      return await this.mapToVehicleBlockedPeriodDto(period);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to fetch vehicle blocked period: ${error.message}`,
      );
    }
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleBlockedPeriodDto: UpdateVehicleBlockedPeriodDto,
    metadata?: ActivityMetadata,
  ): Promise<VehicleBlockedPeriodDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Find existing period
      const existingPeriod = await this.db
        .select()
        .from(vehicleBlockedPeriods)
        .where(
          and(
            eq(vehicleBlockedPeriods.id, id),
            eq(vehicleBlockedPeriods.businessId, businessId),
            isNull(vehicleBlockedPeriods.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingPeriod) {
        throw new NotFoundException('Vehicle blocked period not found');
      }

      // If updating dates/times, validate and check for overlaps
      if (
        updateVehicleBlockedPeriodDto.startDate ||
        updateVehicleBlockedPeriodDto.startTime ||
        updateVehicleBlockedPeriodDto.endDate ||
        updateVehicleBlockedPeriodDto.endTime
      ) {
        const newStartDate =
          updateVehicleBlockedPeriodDto.startDate || existingPeriod.startDate;
        const newStartTime =
          updateVehicleBlockedPeriodDto.startTime || existingPeriod.startTime;
        const newEndDate =
          updateVehicleBlockedPeriodDto.endDate || existingPeriod.endDate;
        const newEndTime =
          updateVehicleBlockedPeriodDto.endTime || existingPeriod.endTime;

        // Validate that start date/time is before end date/time
        const startDateTime = new Date(`${newStartDate}T${newStartTime}`);
        const endDateTime = new Date(`${newEndDate}T${newEndTime}`);

        if (startDateTime >= endDateTime) {
          throw new BadRequestException(
            'Start date/time must be before end date/time',
          );
        }

        // Check for overlapping periods (excluding the current period being updated)
        const overlappingPeriods = await this.checkForOverlappingPeriods(
          businessId,
          existingPeriod.vehicleId,
          newStartDate,
          newStartTime,
          newEndDate,
          newEndTime,
          id, // Exclude current period from overlap check
        );

        if (overlappingPeriods.length > 0) {
          throw new ConflictException(
            'The specified time period overlaps with an existing blocked period',
          );
        }
      }

      // Update the period
      const [updatedPeriod] = await this.db
        .update(vehicleBlockedPeriods)
        .set({
          ...updateVehicleBlockedPeriodDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(vehicleBlockedPeriods.id, id))
        .returning();

      // Log the vehicle blocked period update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.VEHICLE_BLOCKED_PERIOD,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToVehicleBlockedPeriodDto(updatedPeriod);
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update vehicle blocked period: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Find existing period
      const existingPeriod = await this.db
        .select()
        .from(vehicleBlockedPeriods)
        .where(
          and(
            eq(vehicleBlockedPeriods.id, id),
            eq(vehicleBlockedPeriods.businessId, businessId),
            isNull(vehicleBlockedPeriods.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingPeriod) {
        throw new NotFoundException('Vehicle blocked period not found');
      }

      // Soft delete the period
      await this.db
        .update(vehicleBlockedPeriods)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
        })
        .where(eq(vehicleBlockedPeriods.id, id));

      // Log the vehicle blocked period deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.VEHICLE_BLOCKED_PERIOD,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        success: true,
        message: 'Vehicle blocked period deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete vehicle blocked period: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createVehicleBlockedPeriodDto: CreateVehicleBlockedPeriodDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const period = await this.create(
      userId,
      businessId,
      createVehicleBlockedPeriodDto,
      metadata,
    );
    return { id: period.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateVehicleBlockedPeriodDto: UpdateVehicleBlockedPeriodDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const period = await this.update(
      userId,
      businessId,
      id,
      updateVehicleBlockedPeriodDto,
      metadata,
    );
    return { id: period.id };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deletedIds: string[];
    notFoundIds: string[];
    deleted: number;
    message: string;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No IDs provided for deletion');
      }

      // Check which periods exist
      const existingPeriods = await this.db
        .select({ id: vehicleBlockedPeriods.id })
        .from(vehicleBlockedPeriods)
        .where(
          and(
            eq(vehicleBlockedPeriods.businessId, businessId),
            inArray(vehicleBlockedPeriods.id, ids),
            isNull(vehicleBlockedPeriods.deletedAt),
          ),
        );

      const existingIds = existingPeriods.map((period) => period.id);
      const notFoundIds = ids.filter((id) => !existingIds.includes(id));

      if (existingIds.length === 0) {
        throw new NotFoundException(
          'No vehicle blocked periods found to delete',
        );
      }

      // Soft delete existing periods
      await this.db
        .update(vehicleBlockedPeriods)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
        })
        .where(inArray(vehicleBlockedPeriods.id, existingIds));

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.VEHICLE_BLOCKED_PERIOD,
        existingIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { vehicleBlockedPeriodIds: ids },
          failures: notFoundIds.map((id) => ({
            id,
            error: 'Vehicle blocked period not found',
          })),
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedIds: existingIds,
        notFoundIds,
        deleted: existingIds.length,
        message: `Successfully deleted ${existingIds.length} vehicle blocked period(s)`,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk delete vehicle blocked periods',
      );
    }
  }

  private async checkForOverlappingPeriods(
    businessId: string,
    vehicleId: string,
    startDate: string,
    startTime: string,
    endDate: string,
    endTime: string,
    excludeId?: string,
  ): Promise<(typeof vehicleBlockedPeriods.$inferSelect)[]> {
    const whereConditions = [
      eq(vehicleBlockedPeriods.businessId, businessId),
      eq(vehicleBlockedPeriods.vehicleId, vehicleId),
      isNull(vehicleBlockedPeriods.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(ne(vehicleBlockedPeriods.id, excludeId));
    }

    // Simple but effective overlap check using combined date-time strings
    // This avoids complex date/time comparison logic by converting to comparable strings
    const newStartDateTime = `${startDate} ${startTime}`;
    const newEndDateTime = `${endDate} ${endTime}`;

    // Check for overlapping periods
    // Two periods overlap if: start1 < end2 AND start2 < end1
    // We use string comparison which works for ISO format dates and times
    const overlappingPeriods = await this.db
      .select()
      .from(vehicleBlockedPeriods)
      .where(
        and(
          ...whereConditions,
          // Period overlaps if:
          // - New period starts before existing period ends AND
          // - Existing period starts before new period ends
          // Using string concatenation for date-time comparison
          sql`CONCAT(${vehicleBlockedPeriods.startDate}, ' ', ${vehicleBlockedPeriods.startTime}) < ${newEndDateTime}`,
          sql`CONCAT(${vehicleBlockedPeriods.endDate}, ' ', ${vehicleBlockedPeriods.endTime}) > ${newStartDateTime}`,
        ),
      );

    return overlappingPeriods;
  }

  private async mapToVehicleBlockedPeriodDto(
    period: typeof vehicleBlockedPeriods.$inferSelect,
  ): Promise<VehicleBlockedPeriodDto> {
    return {
      id: period.id,
      businessId: period.businessId,
      vehicleId: period.vehicleId,
      startDate: period.startDate,
      startTime: period.startTime,
      endDate: period.endDate,
      endTime: period.endTime,
      description: period.description,
      color: period.color,
      createdBy: period.createdBy,
      updatedBy: period.updatedBy,
      deletedBy: period.deletedBy,
      deletedAt: period.deletedAt,
      createdAt: period.createdAt,
      updatedAt: period.updatedAt,
    };
  }
}
