import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FileFieldsInterceptor,
  FilesInterceptor,
} from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { EventSpacesService } from './event-spaces.service';
import { CreateEventSpaceDto } from './dto/create-event-space.dto';
import { UpdateEventSpaceDto } from './dto/update-event-space.dto';
import { EventSpaceDto } from './dto/event-space.dto';

import { EventSpaceSlimDto } from './dto/event-space-slim.dto';
import { EventSpaceIdResponseDto } from './dto/event-space-id-response.dto';
import { PaginatedEventSpacesResponseDto } from './dto/paginated-event-spaces-response.dto';
import { BulkCreateEventSpaceDto } from './dto/bulk-create-event-space.dto';
import { BulkEventSpaceIdsResponseDto } from './dto/bulk-event-space-ids-response.dto';
import { BulkDeleteEventSpaceDto } from './dto/bulk-delete-event-space.dto';
import { BulkDeleteEventSpaceResponseDto } from './dto/bulk-delete-event-space-response.dto';
import {
  BulkUpdateEventSpaceStatusDto,
  BulkUpdateEventSpaceStatusResponseDto,
} from './dto/bulk-update-event-space-status.dto';
import {
  UpdateEventSpacePositionsDto,
  UpdateEventSpacePositionsResponseDto,
} from './dto/update-event-space-positions.dto';
import { DeleteEventSpaceResponseDto } from './dto/delete-event-space-response.dto';
import { EventSpaceNameAvailabilityResponseDto } from './dto/check-event-space-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('event-spaces')
@Controller('event-spaces')
@UseGuards(PermissionsGuard)
export class EventSpacesController {
  constructor(private readonly eventSpacesService: EventSpacesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new event space',
    description:
      'Creates a new event space with optional image and OG image uploads',
  })
  @ApiBody({
    description: 'Event space creation data with optional image files',
    type: CreateEventSpaceDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Event space created successfully',
    type: EventSpaceIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Event space name or code already exists',
  })
  create(
    @Request() req,
    @Body() createEventSpaceDto: CreateEventSpaceDto,
    @UploadedFiles()
    files?: { image?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
  ): Promise<EventSpaceIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.eventSpacesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createEventSpaceDto,
      image,
      ogImage,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create multiple event spaces in bulk',
    description: 'Creates multiple event spaces at once with optional images',
  })
  @ApiBody({
    description: 'Bulk event space creation data',
    type: BulkCreateEventSpaceDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Event spaces created successfully',
    type: BulkEventSpaceIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Event space names or codes already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateEventSpaceDto: BulkCreateEventSpaceDto,
    @UploadedFiles() images?: Express.Multer.File[],
  ): Promise<BulkEventSpaceIdsResponseDto> {
    return this.eventSpacesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateEventSpaceDto.eventSpaces,
      images,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_READ)
  @ApiOperation({
    summary: 'Get all event spaces for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by event space name (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'spaceCode',
    description: 'Filter by event space code (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by event space status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'floor',
    description: 'Filter by floor number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'building',
    description: 'Filter by building name (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for multiple filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (e.g., name:asc, createdAt:desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Event spaces retrieved successfully',
    type: PaginatedEventSpacesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('spaceCode') spaceCode?: string,
    @Query('status') status?: string,
    @Query('floor') floor?: string,
    @Query('building') building?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedEventSpacesResponseDto> {
    return this.eventSpacesService.findAllOptimized(
      req.user.activeBusinessId,
      page ? parseInt(page.toString()) : undefined,
      limit ? parseInt(limit.toString()) : undefined,
      from,
      to,
      name,
      spaceCode,
      status,
      floor,
      building,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_READ)
  @ApiOperation({
    summary: 'Get all event spaces in slim format',
    description:
      'Returns a lightweight list of event spaces for dropdowns and selections',
  })
  @ApiResponse({
    status: 200,
    description: 'Event spaces retrieved successfully',
    type: [EventSpaceSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<EventSpaceSlimDto[]> {
    return this.eventSpacesService.findAllSlim(req.user.activeBusinessId);
  }

  @Get('check-name/:name')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_READ)
  @ApiOperation({
    summary: 'Check if event space name is available',
    description: 'Checks if the given event space name is available for use',
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: EventSpaceNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkNameAvailability(
    @Request() req,
    @Param('name') name: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<EventSpaceNameAvailabilityResponseDto> {
    return this.eventSpacesService.checkNameAvailability(
      req.user.activeBusinessId,
      name,
      excludeId,
    );
  }

  @Get('check-space-code/:spaceCode')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_READ)
  @ApiOperation({
    summary: 'Check if event space code is available',
    description: 'Checks if the given event space code is available for use',
  })
  @ApiResponse({
    status: 200,
    description: 'Space code availability checked successfully',
    type: EventSpaceNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkSpaceCodeAvailability(
    @Request() req,
    @Param('spaceCode') spaceCode: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<EventSpaceNameAvailabilityResponseDto> {
    return this.eventSpacesService.checkSpaceCodeAvailability(
      req.user.activeBusinessId,
      spaceCode,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_READ)
  @ApiOperation({
    summary: 'Get event space by ID',
    description: 'Retrieves a specific event space by its ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Event space retrieved successfully',
    type: EventSpaceDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Event space not found',
  })
  findOne(@Request() req, @Param('id') id: string): Promise<EventSpaceDto> {
    return this.eventSpacesService.findOne(req.user.id, id);
  }

  @Patch('positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_UPDATE)
  @ApiOperation({
    summary: 'Update event space positions',
    description: 'Updates the positions of multiple event spaces for ordering',
  })
  @ApiBody({
    description: 'Position updates for event spaces',
    type: UpdateEventSpacePositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Positions updated successfully',
    type: UpdateEventSpacePositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid position data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updatePositions(
    @Request() req,
    @Body() updateEventSpacePositionsDto: UpdateEventSpacePositionsDto,
  ): Promise<UpdateEventSpacePositionsResponseDto> {
    const result = await this.eventSpacesService.updateEventSpacePositions(
      req.user.id,
      req.user.activeBusinessId,
      updateEventSpacePositionsDto.updates,
    );
    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} event space positions`,
    };
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'image', maxCount: 1 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update an event space',
    description: 'Updates an existing event space with optional image uploads',
  })
  @ApiBody({
    description: 'Event space update data with optional image files',
    type: UpdateEventSpaceDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Event space updated successfully',
    type: EventSpaceIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Event space not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Event space name or code already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateEventSpaceDto: UpdateEventSpaceDto,
    @UploadedFiles()
    files?: { image?: Express.Multer.File[]; ogImage?: Express.Multer.File[] },
  ): Promise<EventSpaceIdResponseDto> {
    // Extract image and ogImage from the files object
    const image = files?.image?.[0];
    const ogImage = files?.ogImage?.[0];

    return this.eventSpacesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateEventSpaceDto,
      image,
      ogImage,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_DELETE)
  @ApiOperation({ summary: 'Bulk delete event spaces' })
  @ApiBody({
    description: 'Array of event space IDs to delete',
    type: BulkDeleteEventSpaceDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Event spaces deleted successfully',
    type: BulkDeleteEventSpaceResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkDelete(
    @Request() req,
    @Body() bulkDeleteEventSpaceDto: BulkDeleteEventSpaceDto,
  ): Promise<BulkDeleteEventSpaceResponseDto> {
    return this.eventSpacesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteEventSpaceDto.eventSpaceIds,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_UPDATE)
  @ApiOperation({
    summary: 'Bulk update event space status',
    description: 'Updates the status of multiple event spaces at once',
  })
  @ApiBody({
    description: 'Event space IDs and new status',
    type: BulkUpdateEventSpaceStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Event space statuses updated successfully',
    type: BulkUpdateEventSpaceStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req,
    @Body() bulkUpdateEventSpaceStatusDto: BulkUpdateEventSpaceStatusDto,
  ): Promise<BulkUpdateEventSpaceStatusResponseDto> {
    const result = await this.eventSpacesService.bulkUpdateEventSpaceStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateEventSpaceStatusDto.eventSpaceIds,
      bulkUpdateEventSpaceStatusDto.status,
    );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} event spaces`,
      updatedIds: result.updatedIds,
      failed: result.failed,
    };
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.EVENT_SPACE_DELETE)
  @ApiOperation({
    summary: 'Delete an event space',
    description: 'Soft deletes an event space by ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Event space deleted successfully',
    type: DeleteEventSpaceResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Event space not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteEventSpaceResponseDto> {
    return this.eventSpacesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }
}
