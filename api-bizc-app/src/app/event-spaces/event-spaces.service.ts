import {
  Injectable,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  and,
  eq,
  isNull,
  ilike,
  gte,
  lte,
  inArray,
  asc,
  desc,
  or,
  sql,
} from 'drizzle-orm';
import { eventSpaces } from '../drizzle/schema/event-spaces.schema';
import { users } from '../drizzle/schema/users.schema';
import { media } from '../drizzle/schema/media.schema';
import { reservationTypes } from '../drizzle/schema/reservation-types.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ActivitySource,
  DeleteType,
} from '../shared/types/activity.enum';
import { MediaService } from '../media/media.service';
import { CreateEventSpaceDto } from './dto/create-event-space.dto';
import { UpdateEventSpaceDto } from './dto/update-event-space.dto';
import { EventSpaceDto } from './dto/event-space.dto';
import { EventSpaceListDto } from './dto/event-space-list.dto';
import { EventSpaceSlimDto } from './dto/event-space-slim.dto';
import { EventSpaceStatus } from '../shared/types';

@Injectable()
export class EventSpacesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createEventSpaceDto: CreateEventSpaceDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an event space with the same name already exists for this business
      const existingByName = await this.db
        .select()
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            ilike(eventSpaces.name, createEventSpaceDto.name),
            eq(eventSpaces.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByName) {
        throw new ConflictException(
          `Event space with name "${createEventSpaceDto.name}" already exists`,
        );
      }

      // Check if an event space with the same space code already exists for this business
      const existingByCode = await this.db
        .select()
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            ilike(eventSpaces.spaceCode, createEventSpaceDto.spaceCode),
            eq(eventSpaces.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingByCode) {
        throw new ConflictException(
          `Event space with code "${createEventSpaceDto.spaceCode}" already exists`,
        );
      }

      // Validate type reference
      if (createEventSpaceDto.type) {
        const typeExists = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.id, createEventSpaceDto.type),
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!typeExists) {
          throw new BadRequestException('Type reference not found');
        }
      }

      // Validate sub-type reference if provided
      if (createEventSpaceDto.subType) {
        const subTypeExists = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.id, createEventSpaceDto.subType),
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!subTypeExists) {
          throw new BadRequestException('Sub-type reference not found');
        }
      }

      // Validate account references
      const accountIds = [
        createEventSpaceDto.incomeAccountId,
        createEventSpaceDto.expenseAccountId,
        createEventSpaceDto.assetAccountId,
      ].filter(Boolean);

      if (accountIds.length > 0) {
        const existingAccounts = await this.db
          .select({ id: accounts.id })
          .from(accounts)
          .where(
            and(
              inArray(accounts.id, accountIds),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          );

        const existingAccountIds = existingAccounts.map((acc) => acc.id);
        const missingAccounts = accountIds.filter(
          (id) => !existingAccountIds.includes(id),
        );

        if (missingAccounts.length > 0) {
          throw new BadRequestException(
            `Account references not found: ${missingAccounts.join(', ')}`,
          );
        }
      }

      // Validate tax rate reference if provided
      if (createEventSpaceDto.defaultTaxRateId) {
        const taxRateExists = await this.db
          .select()
          .from(taxes)
          .where(
            and(
              eq(taxes.id, createEventSpaceDto.defaultTaxRateId),
              eq(taxes.businessId, businessId),
              eq(taxes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!taxRateExists) {
          throw new BadRequestException('Tax rate reference not found');
        }
      }

      // Validate asset reference if provided
      if (createEventSpaceDto.assetId) {
        const assetExists = await this.db
          .select()
          .from(assets)
          .where(
            and(
              eq(assets.id, createEventSpaceDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!assetExists) {
          throw new BadRequestException('Asset reference not found');
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'event-spaces',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'event-spaces/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Use a transaction to ensure position reordering and event space creation are atomic
      const newEventSpace = await this.db.transaction(async (tx) => {
        // Shift all existing event spaces down by 1 position to make room at position 1
        await this.reorderPositions(
          tx,
          businessId,
          createEventSpaceDto.type,
          createEventSpaceDto.subType,
          1, // Insert at position 1 (first position)
        );

        // Insert new event space at position 1
        const [eventSpace] = await tx
          .insert(eventSpaces)
          .values({
            businessId,
            spaceCode: createEventSpaceDto.spaceCode,
            name: createEventSpaceDto.name,
            type: createEventSpaceDto.type,
            subType: createEventSpaceDto.subType,
            status: createEventSpaceDto.status ?? EventSpaceStatus.AVAILABLE,
            floor: createEventSpaceDto.floor,
            building: createEventSpaceDto.building,
            description: createEventSpaceDto.description,
            features: createEventSpaceDto.features ?? {},
            maxOccupancy: createEventSpaceDto.maxOccupancy,
            baseHourlyRate: createEventSpaceDto.baseHourlyRate ?? '0.00',
            baseHalfDayRate: createEventSpaceDto.baseHalfDayRate ?? '0.00',
            baseFullDayRate: createEventSpaceDto.baseFullDayRate ?? '0.00',
            standardCost: createEventSpaceDto.standardCost ?? '0.00',
            minBookingHours: createEventSpaceDto.minBookingHours ?? '1.00',
            incomeAccountId: createEventSpaceDto.incomeAccountId,
            expenseAccountId: createEventSpaceDto.expenseAccountId,
            assetAccountId: createEventSpaceDto.assetAccountId,
            assetId: createEventSpaceDto.assetId,
            seoTitle: createEventSpaceDto.seoTitle,
            seoDescription: createEventSpaceDto.seoDescription,
            seoKeywords: createEventSpaceDto.seoKeywords,
            ogImage: ogImageId,
            typePosition: createEventSpaceDto.typePosition ?? 1,
            subTypePosition: createEventSpaceDto.subTypePosition ?? 1,
            globalPosition: createEventSpaceDto.globalPosition ?? 1,
            taxType: createEventSpaceDto.taxType,
            defaultTaxRateId: createEventSpaceDto.defaultTaxRateId,
            image: mediaId,
            createdBy: userId,
          })
          .returning();

        return eventSpace;
      });

      // Log the activity
      await this.activityLogService.logCreate(
        newEventSpace.id,
        EntityType.EVENT_SPACE,
        userId,
        businessId,
        {
          reason: `Event space "${createEventSpaceDto.name}" was created`,
        },
      );

      return { id: newEventSpace.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create event space: ${error.message}`,
      );
    }
  }

  // Helper method to reorder positions
  private async reorderPositions(
    tx: any,
    businessId: string,
    type?: string,
    subType?: string,
    insertPosition?: number,
  ): Promise<void> {
    if (insertPosition) {
      // Shift positions down to make room for new item
      await tx
        .update(eventSpaces)
        .set({
          typePosition: sql`${eventSpaces.typePosition} + 1`,
          subTypePosition: sql`${eventSpaces.subTypePosition} + 1`,
          globalPosition: sql`${eventSpaces.globalPosition} + 1`,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
            type ? eq(eventSpaces.type, type) : undefined,
            subType ? eq(eventSpaces.subType, subType) : undefined,
          ),
        );
    }
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: EventSpaceDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(eventSpaces.isDeleted, false),
      eq(eventSpaces.status, EventSpaceStatus.AVAILABLE),
      eq(eventSpaces.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(eventSpaces.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(eventSpaces.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(eventSpaces)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get event spaces with joins
    const eventSpaceResults = await this.db
      .select({
        id: eventSpaces.id,
        businessId: eventSpaces.businessId,
        spaceCode: eventSpaces.spaceCode,
        name: eventSpaces.name,
        type: eventSpaces.type,
        subType: eventSpaces.subType,
        status: eventSpaces.status,
        floor: eventSpaces.floor,
        building: eventSpaces.building,
        description: eventSpaces.description,
        features: eventSpaces.features,
        maxOccupancy: eventSpaces.maxOccupancy,
        baseHourlyRate: eventSpaces.baseHourlyRate,
        baseHalfDayRate: eventSpaces.baseHalfDayRate,
        baseFullDayRate: eventSpaces.baseFullDayRate,
        standardCost: eventSpaces.standardCost,
        minBookingHours: eventSpaces.minBookingHours,
        incomeAccountId: eventSpaces.incomeAccountId,
        expenseAccountId: eventSpaces.expenseAccountId,
        assetAccountId: eventSpaces.assetAccountId,
        assetId: eventSpaces.assetId,
        seoTitle: eventSpaces.seoTitle,
        seoDescription: eventSpaces.seoDescription,
        seoKeywords: eventSpaces.seoKeywords,
        ogImage: eventSpaces.ogImage,
        typePosition: eventSpaces.typePosition,
        subTypePosition: eventSpaces.subTypePosition,
        globalPosition: eventSpaces.globalPosition,
        taxType: eventSpaces.taxType,
        defaultTaxRateId: eventSpaces.defaultTaxRateId,
        image: eventSpaces.image,
        createdBy: eventSpaces.createdBy,
        updatedBy: eventSpaces.updatedBy,
        createdAt: eventSpaces.createdAt,
        updatedAt: eventSpaces.updatedAt,
        // Joined fields
        imageUrl: media.signedUrl,
        ogImageUrl: sql<string>`og_media.signed_url`,
        createdByName: sql<string>`creator.display_name`,
        updatedByName: sql<string>`updater.display_name`,
      })
      .from(eventSpaces)
      .leftJoin(media, eq(eventSpaces.image, media.id))
      .leftJoin(
        sql`${media} as og_media`,
        eq(eventSpaces.ogImage, sql`og_media.id`),
      )
      .leftJoin(
        sql`${users} as creator`,
        eq(eventSpaces.createdBy, sql`creator.id`),
      )
      .leftJoin(
        sql`${users} as updater`,
        eq(eventSpaces.updatedBy, sql`updater.id`),
      )
      .where(and(...whereConditions))
      .orderBy(asc(eventSpaces.globalPosition), asc(eventSpaces.id))
      .limit(limit)
      .offset(offset);

    // Transform to DTOs
    const data: EventSpaceDto[] = eventSpaceResults.map((eventSpace) => ({
      id: eventSpace.id,
      businessId: eventSpace.businessId,
      spaceCode: eventSpace.spaceCode,
      name: eventSpace.name,
      type: eventSpace.type,
      subType: eventSpace.subType,
      status: eventSpace.status,
      floor: eventSpace.floor,
      building: eventSpace.building,
      description: eventSpace.description,
      features: eventSpace.features,
      maxOccupancy: eventSpace.maxOccupancy,
      baseHourlyRate: eventSpace.baseHourlyRate,
      baseHalfDayRate: eventSpace.baseHalfDayRate,
      baseFullDayRate: eventSpace.baseFullDayRate,
      standardCost: eventSpace.standardCost,
      minBookingHours: eventSpace.minBookingHours,
      incomeAccountId: eventSpace.incomeAccountId,
      expenseAccountId: eventSpace.expenseAccountId,
      assetAccountId: eventSpace.assetAccountId,
      assetId: eventSpace.assetId,
      seoTitle: eventSpace.seoTitle,
      seoDescription: eventSpace.seoDescription,
      seoKeywords: eventSpace.seoKeywords,
      ogImage: eventSpace.ogImageUrl,
      typePosition: eventSpace.typePosition,
      subTypePosition: eventSpace.subTypePosition,
      globalPosition: eventSpace.globalPosition,
      taxType: eventSpace.taxType,
      defaultTaxRateId: eventSpace.defaultTaxRateId,
      image: eventSpace.imageUrl,
      createdBy: eventSpace.createdByName || 'Unknown',
      updatedBy: eventSpace.updatedByName,
      createdAt: eventSpace.createdAt,
      updatedAt: eventSpace.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    spaceCode?: string,
    status?: string,
    floor?: string,
    building?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: EventSpaceListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(eventSpaces.isDeleted, false),
      eq(eventSpaces.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(eventSpaces.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(eventSpaces.createdAt, toDate));
      }
    }

    // Build search conditions
    const searchConditions = [];

    if (name) {
      searchConditions.push(ilike(eventSpaces.name, `%${name}%`));
    }

    if (spaceCode) {
      searchConditions.push(ilike(eventSpaces.spaceCode, `%${spaceCode}%`));
    }

    if (
      status &&
      Object.values(EventSpaceStatus).includes(status as EventSpaceStatus)
    ) {
      searchConditions.push(eq(eventSpaces.status, status as EventSpaceStatus));
    }

    if (floor) {
      const floorNum = parseInt(floor);
      if (!isNaN(floorNum)) {
        searchConditions.push(eq(eventSpaces.floor, floorNum));
      }
    }

    if (building) {
      searchConditions.push(ilike(eventSpaces.building, `%${building}%`));
    }

    // Handle additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const [key, value] of Object.entries(parsedFilters)) {
          if (value && typeof value === 'string') {
            switch (key) {
              case 'maxOccupancy': {
                const occupancy = parseInt(value);
                if (!isNaN(occupancy)) {
                  searchConditions.push(
                    gte(eventSpaces.maxOccupancy, occupancy),
                  );
                }
                break;
              }
              case 'type': {
                searchConditions.push(eq(eventSpaces.type, value));
                break;
              }
              case 'subType': {
                searchConditions.push(eq(eventSpaces.subType, value));
                break;
              }
            }
          }
        }
      } catch {
        // Ignore invalid JSON filters
      }
    }

    // Combine search conditions with join operator
    let finalWhereCondition: any;
    if (searchConditions.length > 0) {
      const searchCondition =
        joinOperator === 'or'
          ? or(...searchConditions)
          : and(...searchConditions);
      finalWhereCondition = and(...whereConditions, searchCondition);
    } else {
      finalWhereCondition = and(...whereConditions);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(eventSpaces)
      .where(finalWhereCondition);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Build order by clause
    let orderByClause: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'name':
          orderByClause = isDesc
            ? desc(eventSpaces.name)
            : asc(eventSpaces.name);
          break;
        case 'spaceCode':
          orderByClause = isDesc
            ? desc(eventSpaces.spaceCode)
            : asc(eventSpaces.spaceCode);
          break;
        case 'status':
          orderByClause = isDesc
            ? desc(eventSpaces.status)
            : asc(eventSpaces.status);
          break;
        case 'floor':
          orderByClause = isDesc
            ? desc(eventSpaces.floor)
            : asc(eventSpaces.floor);
          break;
        case 'maxOccupancy':
          orderByClause = isDesc
            ? desc(eventSpaces.maxOccupancy)
            : asc(eventSpaces.maxOccupancy);
          break;
        case 'createdAt':
          orderByClause = isDesc
            ? desc(eventSpaces.createdAt)
            : asc(eventSpaces.createdAt);
          break;
        default:
          orderByClause = asc(eventSpaces.globalPosition);
      }
    } else {
      orderByClause = asc(eventSpaces.globalPosition);
    }

    // Get event spaces with joins
    const eventSpaceResults = await this.db
      .select({
        id: eventSpaces.id,
        spaceCode: eventSpaces.spaceCode,
        name: eventSpaces.name,
        status: eventSpaces.status,
        floor: eventSpaces.floor,
        building: eventSpaces.building,
        maxOccupancy: eventSpaces.maxOccupancy,
        baseHourlyRate: eventSpaces.baseHourlyRate,
        baseHalfDayRate: eventSpaces.baseHalfDayRate,
        baseFullDayRate: eventSpaces.baseFullDayRate,
        typePosition: eventSpaces.typePosition,
        subTypePosition: eventSpaces.subTypePosition,
        globalPosition: eventSpaces.globalPosition,
        imageUrl: media.signedUrl,
        typeName: sql<string>`type_rt.name`,
        subTypeName: sql<string>`subtype_rt.name`,
      })
      .from(eventSpaces)
      .leftJoin(media, eq(eventSpaces.image, media.id))
      .leftJoin(
        sql`${reservationTypes} as type_rt`,
        eq(eventSpaces.type, sql`type_rt.id`),
      )
      .leftJoin(
        sql`${reservationTypes} as subtype_rt`,
        eq(eventSpaces.subType, sql`subtype_rt.id`),
      )
      .where(finalWhereCondition)
      .orderBy(orderByClause, asc(eventSpaces.id))
      .limit(limit)
      .offset(offset);

    // Transform to DTOs
    const data: EventSpaceListDto[] = eventSpaceResults.map((eventSpace) => ({
      id: eventSpace.id,
      spaceCode: eventSpace.spaceCode,
      name: eventSpace.name,
      status: eventSpace.status,
      floor: eventSpace.floor,
      building: eventSpace.building,
      maxOccupancy: eventSpace.maxOccupancy,
      baseHourlyRate: eventSpace.baseHourlyRate,
      baseHalfDayRate: eventSpace.baseHalfDayRate,
      baseFullDayRate: eventSpace.baseFullDayRate,
      typePosition: eventSpace.typePosition,
      subTypePosition: eventSpace.subTypePosition,
      globalPosition: eventSpace.globalPosition,
      image: eventSpace.imageUrl,
      typeName: eventSpace.typeName,
      subTypeName: eventSpace.subTypeName,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<EventSpaceDto> {
    // Get the event space
    const eventSpace = await this.db
      .select()
      .from(eventSpaces)
      .where(and(eq(eventSpaces.id, id), eq(eventSpaces.isDeleted, false)))
      .then((results) => results[0]);

    if (!eventSpace) {
      throw new NotFoundException(`Event space with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db
      .select({ activeBusinessId: users.activeBusinessId })
      .from(users)
      .where(eq(users.id, userId))
      .then((results) => results[0]);

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== eventSpace.businessId
    ) {
      throw new UnauthorizedException('Access denied to this event space');
    }

    // Get detailed event space with joins
    const detailedEventSpace = await this.db
      .select({
        id: eventSpaces.id,
        businessId: eventSpaces.businessId,
        spaceCode: eventSpaces.spaceCode,
        name: eventSpaces.name,
        type: eventSpaces.type,
        subType: eventSpaces.subType,
        status: eventSpaces.status,
        floor: eventSpaces.floor,
        building: eventSpaces.building,
        description: eventSpaces.description,
        features: eventSpaces.features,
        maxOccupancy: eventSpaces.maxOccupancy,
        baseHourlyRate: eventSpaces.baseHourlyRate,
        baseHalfDayRate: eventSpaces.baseHalfDayRate,
        baseFullDayRate: eventSpaces.baseFullDayRate,
        standardCost: eventSpaces.standardCost,
        minBookingHours: eventSpaces.minBookingHours,
        incomeAccountId: eventSpaces.incomeAccountId,
        expenseAccountId: eventSpaces.expenseAccountId,
        assetAccountId: eventSpaces.assetAccountId,
        assetId: eventSpaces.assetId,
        seoTitle: eventSpaces.seoTitle,
        seoDescription: eventSpaces.seoDescription,
        seoKeywords: eventSpaces.seoKeywords,
        ogImage: eventSpaces.ogImage,
        typePosition: eventSpaces.typePosition,
        subTypePosition: eventSpaces.subTypePosition,
        globalPosition: eventSpaces.globalPosition,
        taxType: eventSpaces.taxType,
        defaultTaxRateId: eventSpaces.defaultTaxRateId,
        image: eventSpaces.image,
        createdBy: eventSpaces.createdBy,
        updatedBy: eventSpaces.updatedBy,
        createdAt: eventSpaces.createdAt,
        updatedAt: eventSpaces.updatedAt,
        // Joined fields
        imageUrl: media.signedUrl,
        ogImageUrl: sql<string>`og_media.signed_url`,
        createdByName: sql<string>`creator.display_name`,
        updatedByName: sql<string>`updater.display_name`,
      })
      .from(eventSpaces)
      .leftJoin(media, eq(eventSpaces.image, media.id))
      .leftJoin(
        sql`${media} as og_media`,
        eq(eventSpaces.ogImage, sql`og_media.id`),
      )
      .leftJoin(
        sql`${users} as creator`,
        eq(eventSpaces.createdBy, sql`creator.id`),
      )
      .leftJoin(
        sql`${users} as updater`,
        eq(eventSpaces.updatedBy, sql`updater.id`),
      )
      .where(eq(eventSpaces.id, id))
      .then((results) => results[0]);

    if (!detailedEventSpace) {
      throw new NotFoundException(`Event space with ID ${id} not found`);
    }

    // Transform to DTO
    return {
      id: detailedEventSpace.id,
      businessId: detailedEventSpace.businessId,
      spaceCode: detailedEventSpace.spaceCode,
      name: detailedEventSpace.name,
      type: detailedEventSpace.type,
      subType: detailedEventSpace.subType,
      status: detailedEventSpace.status,
      floor: detailedEventSpace.floor,
      building: detailedEventSpace.building,
      description: detailedEventSpace.description,
      features: detailedEventSpace.features,
      maxOccupancy: detailedEventSpace.maxOccupancy,
      baseHourlyRate: detailedEventSpace.baseHourlyRate,
      baseHalfDayRate: detailedEventSpace.baseHalfDayRate,
      baseFullDayRate: detailedEventSpace.baseFullDayRate,
      standardCost: detailedEventSpace.standardCost,
      minBookingHours: detailedEventSpace.minBookingHours,
      incomeAccountId: detailedEventSpace.incomeAccountId,
      expenseAccountId: detailedEventSpace.expenseAccountId,
      assetAccountId: detailedEventSpace.assetAccountId,
      assetId: detailedEventSpace.assetId,
      seoTitle: detailedEventSpace.seoTitle,
      seoDescription: detailedEventSpace.seoDescription,
      seoKeywords: detailedEventSpace.seoKeywords,
      ogImage: detailedEventSpace.ogImageUrl,
      typePosition: detailedEventSpace.typePosition,
      subTypePosition: detailedEventSpace.subTypePosition,
      globalPosition: detailedEventSpace.globalPosition,
      taxType: detailedEventSpace.taxType,
      defaultTaxRateId: detailedEventSpace.defaultTaxRateId,
      image: detailedEventSpace.imageUrl,
      createdBy: detailedEventSpace.createdByName || 'Unknown',
      updatedBy: detailedEventSpace.updatedByName,
      createdAt: detailedEventSpace.createdAt,
      updatedAt: detailedEventSpace.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateEventSpaceDto: UpdateEventSpaceDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<EventSpaceDto> {
    // Get the event space
    const existingEventSpace = await this.db
      .select()
      .from(eventSpaces)
      .where(and(eq(eventSpaces.id, id), eq(eventSpaces.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingEventSpace) {
      throw new NotFoundException(`Event space with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingEventSpace.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this event space',
      );
    }

    // Check for name conflicts if name is being updated
    if (
      updateEventSpaceDto.name &&
      updateEventSpaceDto.name !== existingEventSpace.name
    ) {
      const existingByName = await this.db
        .select()
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            ilike(eventSpaces.name, updateEventSpaceDto.name),
            eq(eventSpaces.isDeleted, false),
            sql`${eventSpaces.id} != ${id}`,
          ),
        )
        .then((results) => results[0]);

      if (existingByName) {
        throw new ConflictException(
          `Event space with name "${updateEventSpaceDto.name}" already exists`,
        );
      }
    }

    // Check for space code conflicts if space code is being updated
    if (
      updateEventSpaceDto.spaceCode &&
      updateEventSpaceDto.spaceCode !== existingEventSpace.spaceCode
    ) {
      const existingByCode = await this.db
        .select()
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            ilike(eventSpaces.spaceCode, updateEventSpaceDto.spaceCode),
            eq(eventSpaces.isDeleted, false),
            sql`${eventSpaces.id} != ${id}`,
          ),
        )
        .then((results) => results[0]);

      if (existingByCode) {
        throw new ConflictException(
          `Event space with code "${updateEventSpaceDto.spaceCode}" already exists`,
        );
      }
    }

    // Validate type reference if being updated
    if (updateEventSpaceDto.type) {
      const typeExists = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.id, updateEventSpaceDto.type),
            eq(reservationTypes.businessId, businessId),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!typeExists) {
        throw new BadRequestException('Type reference not found');
      }
    }

    // Validate sub-type reference if being updated
    if (updateEventSpaceDto.subType) {
      const subTypeExists = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.id, updateEventSpaceDto.subType),
            eq(reservationTypes.businessId, businessId),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!subTypeExists) {
        throw new BadRequestException('Sub-type reference not found');
      }
    }

    // Validate account references if being updated
    const accountIds = [
      updateEventSpaceDto.incomeAccountId,
      updateEventSpaceDto.expenseAccountId,
      updateEventSpaceDto.assetAccountId,
    ].filter(Boolean);

    if (accountIds.length > 0) {
      const existingAccounts = await this.db
        .select({ id: accounts.id })
        .from(accounts)
        .where(
          and(
            inArray(accounts.id, accountIds),
            eq(accounts.businessId, businessId),
            eq(accounts.isDeleted, false),
          ),
        );

      const existingAccountIds = existingAccounts.map((acc) => acc.id);
      const missingAccounts = accountIds.filter(
        (id) => !existingAccountIds.includes(id),
      );

      if (missingAccounts.length > 0) {
        throw new BadRequestException(
          `Account references not found: ${missingAccounts.join(', ')}`,
        );
      }
    }

    // Validate tax rate reference if being updated
    if (updateEventSpaceDto.defaultTaxRateId) {
      const taxRateExists = await this.db
        .select()
        .from(taxes)
        .where(
          and(
            eq(taxes.id, updateEventSpaceDto.defaultTaxRateId),
            eq(taxes.businessId, businessId),
            eq(taxes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!taxRateExists) {
        throw new BadRequestException('Tax rate reference not found');
      }
    }

    // Validate asset reference if being updated
    if (updateEventSpaceDto.assetId) {
      const assetExists = await this.db
        .select()
        .from(assets)
        .where(
          and(
            eq(assets.id, updateEventSpaceDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!assetExists) {
        throw new BadRequestException('Asset reference not found');
      }
    }

    let mediaId: string | undefined = existingEventSpace.image;
    let ogImageId: string | undefined = existingEventSpace.ogImage;

    // Upload new image if provided
    if (imageFile) {
      const uploadedMedia = await this.mediaService.uploadMedia(
        imageFile,
        'event-spaces',
        businessId,
        userId,
      );
      mediaId = uploadedMedia.id;
    }

    // Upload new OG image if provided
    if (ogImageFile) {
      const uploadedOgMedia = await this.mediaService.uploadMedia(
        ogImageFile,
        'event-spaces/og-images',
        businessId,
        userId,
      );
      ogImageId = uploadedOgMedia.id;
    }

    try {
      // Update the event space
      const [updatedEventSpace] = await this.db
        .update(eventSpaces)
        .set({
          ...updateEventSpaceDto,
          image: mediaId,
          ogImage: ogImageId,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(eventSpaces.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        updatedEventSpace.id,
        EntityType.EVENT_SPACE,
        userId,
        businessId,
        {},
      );

      // Return the updated event space
      return await this.findOne(userId, id);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update event space: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    // Get the event space
    const existingEventSpace = await this.db
      .select()
      .from(eventSpaces)
      .where(and(eq(eventSpaces.id, id), eq(eventSpaces.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingEventSpace) {
      throw new NotFoundException(`Event space with ID ${id} not found`);
    }

    if (businessId !== existingEventSpace.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this event space',
      );
    }

    // Soft delete the event space
    await this.db
      .update(eventSpaces)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(eventSpaces.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      existingEventSpace.id,
      EntityType.EVENT_SPACE,
      userId,
      businessId,
      {
        reason: `Event space "${existingEventSpace.name}" was deleted`,
        source: ActivitySource.SYSTEM,
        deleteType: DeleteType.SOFT,
        relatedEntities: [
          {
            type: EntityType.EVENT_SPACE,
            id: existingEventSpace.id,
            action: 'delete',
          },
        ],
      },
    );

    return {
      success: true,
      message: `Event space with ID ${id} has been deleted`,
    };
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createEventSpaceDto: CreateEventSpaceDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    const eventSpace = await this.create(
      userId,
      businessId,
      createEventSpaceDto,
      imageFile,
      ogImageFile,
    );
    return { id: eventSpace.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateEventSpaceDto: UpdateEventSpaceDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateEventSpaceDto,
      imageFile,
      ogImageFile,
    );
    return { id };
  }

  async checkNameAvailability(
    businessId: string,
    name: string,
    excludeId?: string,
  ): Promise<{ available: boolean; message: string }> {
    const whereConditions = [
      eq(eventSpaces.businessId, businessId),
      ilike(eventSpaces.name, name),
      eq(eventSpaces.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${eventSpaces.id} != ${excludeId}`);
    }

    const existing = await this.db
      .select()
      .from(eventSpaces)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existing;
    return {
      available,
      message: available
        ? 'Event space name is available'
        : 'Event space name is already taken',
    };
  }

  async checkSpaceCodeAvailability(
    businessId: string,
    spaceCode: string,
    excludeId?: string,
  ): Promise<{ available: boolean; message: string }> {
    const whereConditions = [
      eq(eventSpaces.businessId, businessId),
      ilike(eventSpaces.spaceCode, spaceCode),
      eq(eventSpaces.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${eventSpaces.id} != ${excludeId}`);
    }

    const existing = await this.db
      .select()
      .from(eventSpaces)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existing;
    return {
      available,
      message: available
        ? 'Event space code is available'
        : 'Event space code is already taken',
    };
  }

  async findAllSlim(businessId: string | null): Promise<EventSpaceSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const eventSpaceResults = await this.db
      .select({
        id: eventSpaces.id,
        spaceCode: eventSpaces.spaceCode,
        name: eventSpaces.name,
        status: eventSpaces.status,
        floor: eventSpaces.floor,
        building: eventSpaces.building,
        maxOccupancy: eventSpaces.maxOccupancy,
        imageUrl: media.signedUrl,
      })
      .from(eventSpaces)
      .leftJoin(media, eq(eventSpaces.image, media.id))
      .where(
        and(
          eq(eventSpaces.businessId, businessId),
          eq(eventSpaces.isDeleted, false),
          eq(eventSpaces.status, EventSpaceStatus.AVAILABLE),
        ),
      )
      .orderBy(asc(eventSpaces.globalPosition), asc(eventSpaces.id));

    return eventSpaceResults.map((eventSpace) => ({
      id: eventSpace.id,
      spaceCode: eventSpace.spaceCode,
      name: eventSpace.name,
      status: eventSpace.status,
      floor: eventSpace.floor,
      building: eventSpace.building,
      maxOccupancy: eventSpace.maxOccupancy,
      image: eventSpace.imageUrl,
    }));
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createEventSpacesDto: CreateEventSpaceDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<EventSpaceDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createEventSpacesDto || createEventSpacesDto.length === 0) {
        throw new BadRequestException('No event spaces provided for creation');
      }

      // Validate that if images are provided, they don't exceed the number of event spaces
      if (imageFiles && imageFiles.length > createEventSpacesDto.length) {
        throw new BadRequestException(
          'Number of images cannot exceed number of event spaces',
        );
      }

      // Check for duplicate names and space codes within the batch
      const names = createEventSpacesDto.map((dto) => dto.name.toLowerCase());
      const spaceCodes = createEventSpacesDto.map((dto) =>
        dto.spaceCode.toLowerCase(),
      );

      const duplicateNames = names.filter(
        (name, index) => names.indexOf(name) !== index,
      );
      const duplicateSpaceCodes = spaceCodes.filter(
        (code, index) => spaceCodes.indexOf(code) !== index,
      );

      if (duplicateNames.length > 0) {
        throw new ConflictException(
          `Duplicate event space names in batch: ${duplicateNames.join(', ')}`,
        );
      }

      if (duplicateSpaceCodes.length > 0) {
        throw new ConflictException(
          `Duplicate event space codes in batch: ${duplicateSpaceCodes.join(', ')}`,
        );
      }

      // Check for existing names and space codes in database
      const existingByNames = await this.db
        .select({ name: eventSpaces.name })
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            inArray(eventSpaces.name, names),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      const existingByCodes = await this.db
        .select({ spaceCode: eventSpaces.spaceCode })
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            inArray(eventSpaces.spaceCode, spaceCodes),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      if (existingByNames.length > 0) {
        const conflictingNames = existingByNames.map((item) => item.name);
        throw new ConflictException(
          `Event spaces with these names already exist: ${conflictingNames.join(', ')}`,
        );
      }

      if (existingByCodes.length > 0) {
        const conflictingCodes = existingByCodes.map((item) => item.spaceCode);
        throw new ConflictException(
          `Event spaces with these codes already exist: ${conflictingCodes.join(', ')}`,
        );
      }

      // Validate all type and sub-type references
      const typeIds = [...new Set(createEventSpacesDto.map((dto) => dto.type))];
      const subTypeIds = [
        ...new Set(
          createEventSpacesDto.map((dto) => dto.subType).filter(Boolean),
        ),
      ];

      if (typeIds.length > 0) {
        const existingTypes = await this.db
          .select({ id: reservationTypes.id })
          .from(reservationTypes)
          .where(
            and(
              inArray(reservationTypes.id, typeIds),
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.isDeleted, false),
            ),
          );

        const existingTypeIds = existingTypes.map((type) => type.id);
        const missingTypes = typeIds.filter(
          (id) => !existingTypeIds.includes(id),
        );

        if (missingTypes.length > 0) {
          throw new BadRequestException(
            `Type references not found: ${missingTypes.join(', ')}`,
          );
        }
      }

      if (subTypeIds.length > 0) {
        const existingSubTypes = await this.db
          .select({ id: reservationTypes.id })
          .from(reservationTypes)
          .where(
            and(
              inArray(reservationTypes.id, subTypeIds),
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.isDeleted, false),
            ),
          );

        const existingSubTypeIds = existingSubTypes.map(
          (subType) => subType.id,
        );
        const missingSubTypes = subTypeIds.filter(
          (id) => !existingSubTypeIds.includes(id),
        );

        if (missingSubTypes.length > 0) {
          throw new BadRequestException(
            `Sub-type references not found: ${missingSubTypes.join(', ')}`,
          );
        }
      }

      // Validate all account references
      const allAccountIds = [
        ...new Set([
          ...createEventSpacesDto.map((dto) => dto.incomeAccountId),
          ...createEventSpacesDto.map((dto) => dto.expenseAccountId),
          ...createEventSpacesDto
            .map((dto) => dto.assetAccountId)
            .filter(Boolean),
        ]),
      ];

      if (allAccountIds.length > 0) {
        const existingAccounts = await this.db
          .select({ id: accounts.id })
          .from(accounts)
          .where(
            and(
              inArray(accounts.id, allAccountIds),
              eq(accounts.businessId, businessId),
              eq(accounts.isDeleted, false),
            ),
          );

        const existingAccountIds = existingAccounts.map((acc) => acc.id);
        const missingAccounts = allAccountIds.filter(
          (id) => !existingAccountIds.includes(id),
        );

        if (missingAccounts.length > 0) {
          throw new BadRequestException(
            `Account references not found: ${missingAccounts.join(', ')}`,
          );
        }
      }

      const createdEventSpaces: EventSpaceDto[] = [];

      // Process each event space in a transaction
      await this.db.transaction(async (tx) => {
        for (let i = 0; i < createEventSpacesDto.length; i++) {
          const createEventSpaceDto = createEventSpacesDto[i];

          // Handle image upload if provided
          let mediaId: string | undefined;
          const imageFile = imageFiles?.[createEventSpaceDto.imageIndex ?? i];

          if (imageFile) {
            const uploadedMedia = await this.mediaService.uploadMedia(
              imageFile,
              'event-spaces',
              businessId,
              userId,
            );
            mediaId = uploadedMedia.id;
          }

          // Shift positions for new event space
          await this.reorderPositions(
            tx,
            businessId,
            createEventSpaceDto.type,
            createEventSpaceDto.subType,
            1,
          );

          // Insert the event space
          const [eventSpace] = await tx
            .insert(eventSpaces)
            .values({
              businessId,
              spaceCode: createEventSpaceDto.spaceCode,
              name: createEventSpaceDto.name,
              type: createEventSpaceDto.type,
              subType: createEventSpaceDto.subType,
              status: createEventSpaceDto.status ?? EventSpaceStatus.AVAILABLE,
              floor: createEventSpaceDto.floor,
              building: createEventSpaceDto.building,
              description: createEventSpaceDto.description,
              features: createEventSpaceDto.features ?? {},
              maxOccupancy: createEventSpaceDto.maxOccupancy,
              baseHourlyRate: createEventSpaceDto.baseHourlyRate ?? '0.00',
              baseHalfDayRate: createEventSpaceDto.baseHalfDayRate ?? '0.00',
              baseFullDayRate: createEventSpaceDto.baseFullDayRate ?? '0.00',
              standardCost: createEventSpaceDto.standardCost ?? '0.00',
              minBookingHours: createEventSpaceDto.minBookingHours ?? '1.00',
              incomeAccountId: createEventSpaceDto.incomeAccountId,
              expenseAccountId: createEventSpaceDto.expenseAccountId,
              assetAccountId: createEventSpaceDto.assetAccountId,
              assetId: createEventSpaceDto.assetId,
              seoTitle: createEventSpaceDto.seoTitle,
              seoDescription: createEventSpaceDto.seoDescription,
              seoKeywords: createEventSpaceDto.seoKeywords,
              typePosition: createEventSpaceDto.typePosition ?? 1,
              subTypePosition: createEventSpaceDto.subTypePosition ?? 1,
              globalPosition: createEventSpaceDto.globalPosition ?? 1,
              taxType: createEventSpaceDto.taxType,
              defaultTaxRateId: createEventSpaceDto.defaultTaxRateId,
              image: mediaId,
              createdBy: userId,
            })
            .returning();

          // Get the full event space data for response
          const fullEventSpace = await this.findOne(userId, eventSpace.id);
          createdEventSpaces.push(fullEventSpace);

          // Log the activity
          await this.activityLogService.logCreate(
            eventSpace.id,
            EntityType.EVENT_SPACE,
            userId,
            businessId,
            {
              source: ActivitySource.SYSTEM,
            },
          );
        }
      });

      return createdEventSpaces;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create event spaces: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createEventSpacesDto: CreateEventSpaceDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    const eventSpaces = await this.bulkCreate(
      userId,
      businessId,
      createEventSpacesDto,
      imageFiles,
    );
    return { ids: eventSpaces.map((eventSpace) => eventSpace.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    eventSpaceIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!eventSpaceIds || eventSpaceIds.length === 0) {
        throw new BadRequestException(
          'No event space IDs provided for deletion',
        );
      }

      // Get all event spaces that exist and belong to the business
      const existingEventSpaces = await this.db
        .select({
          id: eventSpaces.id,
          name: eventSpaces.name,
          businessId: eventSpaces.businessId,
        })
        .from(eventSpaces)
        .where(
          and(
            inArray(eventSpaces.id, eventSpaceIds),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      if (existingEventSpaces.length === 0) {
        throw new BadRequestException(
          'No valid event spaces found for deletion',
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const eventSpace of existingEventSpaces) {
          // Soft delete the event space
          await tx
            .update(eventSpaces)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(eventSpaces.id, eventSpace.id));

          deletedIds.push(eventSpace.id);

          // Log the activity for each deleted event space
          await this.activityLogService.logDelete(
            eventSpace.id,
            EntityType.EVENT_SPACE,
            userId,
            businessId,
            {
              reason: `Event space "${eventSpace.name}" was deleted (bulk)`,
            },
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} event spaces`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete event spaces: ${error.message}`,
      );
    }
  }

  async bulkUpdateEventSpaceStatus(
    userId: string,
    businessId: string | null,
    eventSpaceIds: string[],
    status: EventSpaceStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ eventSpaceId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!eventSpaceIds || eventSpaceIds.length === 0) {
        throw new BadRequestException(
          'No event space IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ eventSpaceId: string; error: string }> = [];

      // Process each event space ID
      await this.db.transaction(async (tx) => {
        for (const eventSpaceId of eventSpaceIds) {
          try {
            // Check if event space exists and belongs to the business
            const existingEventSpace = await tx
              .select()
              .from(eventSpaces)
              .where(
                and(
                  eq(eventSpaces.id, eventSpaceId),
                  eq(eventSpaces.businessId, businessId),
                  eq(eventSpaces.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingEventSpace) {
              failed.push({
                eventSpaceId,
                error: 'Event space not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingEventSpace.status === status) {
              failed.push({
                eventSpaceId,
                error: `Event space already has status: ${status}`,
              });
              continue;
            }

            // Update the event space status
            await tx
              .update(eventSpaces)
              .set({
                status,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(eventSpaces.id, eventSpaceId),
                  eq(eventSpaces.businessId, businessId),
                  eq(eventSpaces.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(eventSpaceId);

            // Log the status update activity
            await this.activityLogService.logUpdate(
              eventSpaceId,
              EntityType.EVENT_SPACE,
              userId,
              businessId,
              {
                source: ActivitySource.SYSTEM,
              },
            );
          } catch (error) {
            failed.push({
              eventSpaceId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update event space status: ${error.message}`,
      );
    }
  }

  async updateEventSpacePositions(
    userId: string,
    businessId: string | null,
    updates: {
      id: string;
      typePosition: number;
      subTypePosition: number;
      globalPosition: number;
    }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be non-negative integers)
      for (const update of updates) {
        if (!Number.isInteger(update.typePosition) || update.typePosition < 0) {
          throw new BadRequestException(
            `Invalid type position ${update.typePosition} for event space ${update.id}. Position must be a non-negative integer.`,
          );
        }
        if (
          !Number.isInteger(update.subTypePosition) ||
          update.subTypePosition < 0
        ) {
          throw new BadRequestException(
            `Invalid sub-type position ${update.subTypePosition} for event space ${update.id}. Position must be a non-negative integer.`,
          );
        }
        if (
          !Number.isInteger(update.globalPosition) ||
          update.globalPosition < 0
        ) {
          throw new BadRequestException(
            `Invalid global position ${update.globalPosition} for event space ${update.id}. Position must be a non-negative integer.`,
          );
        }
      }

      // Verify all event spaces exist and belong to the business
      const eventSpaceIds = updates.map((update) => update.id);
      const existingEventSpaces = await this.db
        .select({
          id: eventSpaces.id,
          name: eventSpaces.name,
        })
        .from(eventSpaces)
        .where(
          and(
            inArray(eventSpaces.id, eventSpaceIds),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      const existingIds = existingEventSpaces.map((es) => es.id);
      const missingIds = eventSpaceIds.filter(
        (id) => !existingIds.includes(id),
      );

      if (missingIds.length > 0) {
        throw new BadRequestException(
          `Event spaces not found or access denied: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdatePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.logUpdate(
            update.id,
            EntityType.EVENT_SPACE,
            userId,
            businessId,
            {
              source: ActivitySource.SYSTEM,
            },
          ),
        );

        await Promise.all(activityPromises);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update event space positions: ${error.message}`,
      );
    }
  }

  // Helper method for batch position updates
  private async batchUpdatePositions(
    tx: any,
    businessId: string,
    updates: {
      id: string;
      typePosition: number;
      subTypePosition: number;
      globalPosition: number;
    }[],
  ): Promise<void> {
    // Sort updates by ID for consistent ordering
    const sortedUpdates = updates.sort((a, b) => a.id.localeCompare(b.id));

    // For small batches, use individual updates which are more reliable
    if (sortedUpdates.length <= 5) {
      for (const update of sortedUpdates) {
        await tx
          .update(eventSpaces)
          .set({
            typePosition: update.typePosition,
            subTypePosition: update.subTypePosition,
            globalPosition: update.globalPosition,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(eventSpaces.id, update.id),
              eq(eventSpaces.businessId, businessId),
              eq(eventSpaces.isDeleted, false),
            ),
          );
      }
      return;
    }

    // For larger batches, use parallel individual updates
    // This approach is more reliable than raw SQL with arrays
    const updatePromises = sortedUpdates.map((update) =>
      tx
        .update(eventSpaces)
        .set({
          typePosition: update.typePosition,
          subTypePosition: update.subTypePosition,
          globalPosition: update.globalPosition,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(eventSpaces.id, update.id),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        ),
    );

    await Promise.all(updatePromises);
  }

  /**
   * Update global positions for event spaces
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateEventSpaceGlobalPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for event space ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all event spaces exist and belong to the business
      const eventSpaceIds = updates.map((update) => update.id);
      const existingEventSpaces = await this.db
        .select({ id: eventSpaces.id, name: eventSpaces.name })
        .from(eventSpaces)
        .where(
          and(
            inArray(eventSpaces.id, eventSpaceIds),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      if (existingEventSpaces.length !== eventSpaceIds.length) {
        throw new BadRequestException(
          'One or more event spaces not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateGlobalPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.logUpdate(
            update.id,
            EntityType.EVENT_SPACE,
            userId,
            businessId,
            {
              source: ActivitySource.SYSTEM,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeGlobalPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update event space global positions: ${error.message}`,
      );
    }
  }

  /**
   * Update type positions for event spaces within their specific type
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateEventSpaceTypePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for event space ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all event spaces exist and belong to the business
      const eventSpaceIds = updates.map((update) => update.id);
      const existingEventSpaces = await this.db
        .select({
          id: eventSpaces.id,
          name: eventSpaces.name,
          type: eventSpaces.type,
        })
        .from(eventSpaces)
        .where(
          and(
            inArray(eventSpaces.id, eventSpaceIds),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      if (existingEventSpaces.length !== eventSpaceIds.length) {
        throw new BadRequestException(
          'One or more event spaces not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateTypePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.logUpdate(
            update.id,
            EntityType.EVENT_SPACE,
            userId,
            businessId,
            {
              source: ActivitySource.SYSTEM,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeTypePositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update event space type positions: ${error.message}`,
      );
    }
  }

  /**
   * Update sub-type positions for event spaces within their specific sub-type
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateEventSpaceSubTypePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for event space ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all event spaces exist and belong to the business
      const eventSpaceIds = updates.map((update) => update.id);
      const existingEventSpaces = await this.db
        .select({
          id: eventSpaces.id,
          name: eventSpaces.name,
          subType: eventSpaces.subType,
        })
        .from(eventSpaces)
        .where(
          and(
            inArray(eventSpaces.id, eventSpaceIds),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        );

      if (existingEventSpaces.length !== eventSpaceIds.length) {
        throw new BadRequestException(
          'One or more event spaces not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateSubTypePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.logUpdate(
            update.id,
            EntityType.EVENT_SPACE,
            userId,
            businessId,
            {
              source: ActivitySource.SYSTEM,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeSubTypePositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update event space sub-type positions: ${error.message}`,
      );
    }
  }

  /**
   * Validate position updates for duplicate IDs and positions
   * @param updates - Array of position updates
   */
  private validatePositionUpdates(
    updates: { id: string; position: number }[],
  ): void {
    // Check for duplicate IDs
    const ids = updates.map((update) => update.id);
    const uniqueIds = new Set(ids);
    if (uniqueIds.size !== ids.length) {
      throw new BadRequestException(
        'Duplicate event space IDs found in updates',
      );
    }

    // Check for duplicate positions
    const positions = updates.map((update) => update.position);
    const uniquePositions = new Set(positions);
    if (uniquePositions.size !== positions.length) {
      throw new BadRequestException('Duplicate positions found in updates');
    }
  }

  /**
   * Optimized batch global position update method
   * @param tx - Database transaction
   * @param businessId - Business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateGlobalPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    // Sort updates by ID for consistent ordering
    const sortedUpdates = updates.sort((a, b) => a.id.localeCompare(b.id));

    // For small batches, use individual updates which are more reliable
    if (sortedUpdates.length <= 5) {
      for (const update of sortedUpdates) {
        await tx
          .update(eventSpaces)
          .set({
            globalPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(eventSpaces.id, update.id),
              eq(eventSpaces.businessId, businessId),
              eq(eventSpaces.isDeleted, false),
            ),
          );
      }
      return;
    }

    // For larger batches, use parallel individual updates
    const updatePromises = sortedUpdates.map((update) =>
      tx
        .update(eventSpaces)
        .set({
          globalPosition: update.position,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(eventSpaces.id, update.id),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        ),
    );

    await Promise.all(updatePromises);
  }

  /**
   * Optimized batch type position update method
   * @param tx - Database transaction
   * @param businessId - Business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateTypePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    // Sort updates by ID for consistent ordering
    const sortedUpdates = updates.sort((a, b) => a.id.localeCompare(b.id));

    // For small batches, use individual updates which are more reliable
    if (sortedUpdates.length <= 5) {
      for (const update of sortedUpdates) {
        await tx
          .update(eventSpaces)
          .set({
            typePosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(eventSpaces.id, update.id),
              eq(eventSpaces.businessId, businessId),
              eq(eventSpaces.isDeleted, false),
            ),
          );
      }
      return;
    }

    // For larger batches, use parallel individual updates
    const updatePromises = sortedUpdates.map((update) =>
      tx
        .update(eventSpaces)
        .set({
          typePosition: update.position,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(eventSpaces.id, update.id),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        ),
    );

    await Promise.all(updatePromises);
  }

  /**
   * Optimized batch sub-type position update method
   * @param tx - Database transaction
   * @param businessId - Business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateSubTypePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    // Sort updates by ID for consistent ordering
    const sortedUpdates = updates.sort((a, b) => a.id.localeCompare(b.id));

    // For small batches, use individual updates which are more reliable
    if (sortedUpdates.length <= 5) {
      for (const update of sortedUpdates) {
        await tx
          .update(eventSpaces)
          .set({
            subTypePosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(eventSpaces.id, update.id),
              eq(eventSpaces.businessId, businessId),
              eq(eventSpaces.isDeleted, false),
            ),
          );
      }
      return;
    }

    // For larger batches, use parallel individual updates
    const updatePromises = sortedUpdates.map((update) =>
      tx
        .update(eventSpaces)
        .set({
          subTypePosition: update.position,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(eventSpaces.id, update.id),
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.isDeleted, false),
          ),
        ),
    );

    await Promise.all(updatePromises);
  }

  /**
   * Conditionally normalize global positions if there are gaps or conflicts
   * @param tx - Database transaction
   * @param businessId - Business ID
   */
  private async conditionalNormalizeGlobalPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    // Check if normalization is needed
    const positionCheck = await tx
      .select({
        positions: sql<
          number[]
        >`array_agg(${eventSpaces.globalPosition} ORDER BY ${eventSpaces.globalPosition})`,
        count: sql<number>`count(*)::int`,
      })
      .from(eventSpaces)
      .where(
        and(
          eq(eventSpaces.businessId, businessId),
          eq(eventSpaces.isDeleted, false),
        ),
      );

    if (positionCheck.length === 0) return;

    const { positions, count: totalCount } = positionCheck[0];

    // Check for gaps or duplicates
    const needsNormalization = this.checkIfNormalizationNeeded(
      positions,
      totalCount,
    );

    if (!needsNormalization) return;

    // Get all event spaces ordered by current global position
    const allEventSpaces = await tx
      .select({
        id: eventSpaces.id,
        globalPosition: eventSpaces.globalPosition,
      })
      .from(eventSpaces)
      .where(
        and(
          eq(eventSpaces.businessId, businessId),
          eq(eventSpaces.isDeleted, false),
        ),
      )
      .orderBy(eventSpaces.globalPosition, eventSpaces.id);

    // Normalize positions
    const updates = allEventSpaces
      .map((eventSpace: any, index: number) => ({
        id: eventSpace.id,
        position: index + 1,
      }))
      .filter(
        (update: any, index: number) =>
          allEventSpaces[index].globalPosition !== update.position,
      );

    if (updates.length > 0) {
      await this.batchUpdateGlobalPositions(tx, businessId, updates);
    }
  }

  /**
   * Conditionally normalize type positions if there are gaps or conflicts
   * @param tx - Database transaction
   * @param businessId - Business ID
   */
  private async conditionalNormalizeTypePositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    // Check if normalization is needed for each type group
    const positionCheck = await tx
      .select({
        type: eventSpaces.type,
        positions: sql<
          number[]
        >`array_agg(${eventSpaces.typePosition} ORDER BY ${eventSpaces.typePosition})`,
        count: sql<number>`count(*)::int`,
      })
      .from(eventSpaces)
      .where(
        and(
          eq(eventSpaces.businessId, businessId),
          eq(eventSpaces.isDeleted, false),
        ),
      )
      .groupBy(eventSpaces.type);

    // Process each type group
    for (const group of positionCheck) {
      const needsNormalization = this.checkIfNormalizationNeeded(
        group.positions,
        group.count,
      );

      if (!needsNormalization) continue;

      // Get all event spaces in this type ordered by current position
      const typeEventSpaces = await tx
        .select({
          id: eventSpaces.id,
          typePosition: eventSpaces.typePosition,
        })
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            eq(eventSpaces.type, group.type),
            eq(eventSpaces.isDeleted, false),
          ),
        )
        .orderBy(eventSpaces.typePosition, eventSpaces.id);

      // Normalize positions
      const updates = typeEventSpaces
        .map((eventSpace: any, index: number) => ({
          id: eventSpace.id,
          position: index + 1,
        }))
        .filter(
          (update: any, index: number) =>
            typeEventSpaces[index].typePosition !== update.position,
        );

      if (updates.length > 0) {
        await this.batchUpdateTypePositions(tx, businessId, updates);
      }
    }
  }

  /**
   * Conditionally normalize sub-type positions if there are gaps or conflicts
   * @param tx - Database transaction
   * @param businessId - Business ID
   */
  private async conditionalNormalizeSubTypePositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    // Check if normalization is needed for each sub-type group
    const positionCheck = await tx
      .select({
        subType: eventSpaces.subType,
        positions: sql<
          number[]
        >`array_agg(${eventSpaces.subTypePosition} ORDER BY ${eventSpaces.subTypePosition})`,
        count: sql<number>`count(*)::int`,
      })
      .from(eventSpaces)
      .where(
        and(
          eq(eventSpaces.businessId, businessId),
          eq(eventSpaces.isDeleted, false),
        ),
      )
      .groupBy(eventSpaces.subType);

    // Process each sub-type group
    for (const group of positionCheck) {
      const needsNormalization = this.checkIfNormalizationNeeded(
        group.positions,
        group.count,
      );

      if (!needsNormalization) continue;

      // Get all event spaces in this sub-type ordered by current position
      const subTypeEventSpaces = await tx
        .select({
          id: eventSpaces.id,
          subTypePosition: eventSpaces.subTypePosition,
        })
        .from(eventSpaces)
        .where(
          and(
            eq(eventSpaces.businessId, businessId),
            group.subType
              ? eq(eventSpaces.subType, group.subType)
              : isNull(eventSpaces.subType),
            eq(eventSpaces.isDeleted, false),
          ),
        )
        .orderBy(eventSpaces.subTypePosition, eventSpaces.id);

      // Normalize positions
      const updates = subTypeEventSpaces
        .map((eventSpace: any, index: number) => ({
          id: eventSpace.id,
          position: index + 1,
        }))
        .filter(
          (update: any, index: number) =>
            subTypeEventSpaces[index].subTypePosition !== update.position,
        );

      if (updates.length > 0) {
        await this.batchUpdateSubTypePositions(tx, businessId, updates);
      }
    }
  }

  /**
   * Check if position normalization is needed
   * @param positions - Array of current positions
   * @param totalCount - Total count of items
   * @returns true if normalization is needed
   */
  private checkIfNormalizationNeeded(
    positions: number[],
    totalCount: number,
  ): boolean {
    if (positions.length !== totalCount) return true;

    // Check for gaps or duplicates
    const uniquePositions = new Set(positions);
    if (uniquePositions.size !== totalCount) return true;

    // Check if positions start from 1 and are consecutive
    const sortedPositions = [...positions].sort((a, b) => a - b);
    for (let i = 0; i < sortedPositions.length; i++) {
      if (sortedPositions[i] !== i + 1) return true;
    }

    return false;
  }
}
