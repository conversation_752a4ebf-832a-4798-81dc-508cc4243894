import {
  Injectable,
  UnauthorizedException,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { and, eq, desc, gte, lte, between } from 'drizzle-orm';
import { meetings } from '../drizzle/schema/meetings.schema';
import { users } from '../drizzle/schema/users.schema';
import { CreateMeetingDto } from './dto/create-meeting.dto';
import { UpdateMeetingDto } from './dto/update-meeting.dto';
import { MeetingDto } from './dto/meeting.dto';
import { MeetingListDto } from './dto/meeting-list.dto';
import {
  CheckAvailabilityDto,
  AvailabilityResponseDto,
  TimeSlotDto,
} from './dto/meeting-availability.dto';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MeetingStatus, MeetingType } from '../shared/types';

// Configuration constant for maximum meetings per time slot
const MAX_MEETINGS_PER_SLOT = 1;

@Injectable()
export class MeetingsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createMeetingDto: CreateMeetingDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Business-specific meeting slot management:
      // Override/replace previous meeting slot for this business (one active meeting slot per business)
      const scheduledDateTime = new Date(createMeetingDto.scheduledDateTime);

      // Find any existing scheduled meetings for this business
      const existingMeetings = await this.db
        .select()
        .from(meetings)
        .where(
          and(
            eq(meetings.businessId, businessId),
            eq(meetings.status, MeetingStatus.SCHEDULED),
            eq(meetings.isDeleted, false),
          ),
        );

      // If there are existing meetings, cancel them (soft delete) to replace with new slot
      if (existingMeetings.length > 0) {
        await this.db
          .update(meetings)
          .set({
            status: MeetingStatus.CANCELLED,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(meetings.businessId, businessId),
              eq(meetings.status, MeetingStatus.SCHEDULED),
              eq(meetings.isDeleted, false),
            ),
          );
      }

      console.log('createMeetingDto', createMeetingDto);

      const [meeting] = await this.db
        .insert(meetings)
        .values({
          businessId,
          title: createMeetingDto.title,
          description: createMeetingDto.description,
          scheduledDateTime,
          duration: createMeetingDto.duration,
          meetingType: createMeetingDto.meetingType || MeetingType.ZOOM_MEET,
          status: MeetingStatus.SCHEDULED,
          assignedAdminId: null,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: meetings.id });

      console.log('meeting', meeting);

      // Log the activity
      await this.activityLogService.logCreate(
        meeting.id,
        EntityType.PROJECT,
        userId,
        businessId,
        {
          reason: `Created meeting: ${createMeetingDto.title}`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: meeting.id };
    } catch (error) {
      console.error('Error creating meeting:', error);
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create meeting');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    status?: string,
    assignedAdminId?: string,
    from?: string,
    to?: string,
  ): Promise<{
    data: MeetingListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(meetings.isDeleted, false),
      eq(meetings.businessId, businessId),
    ];

    if (status) {
      whereConditions.push(eq(meetings.status, status as MeetingStatus));
    }

    if (assignedAdminId) {
      whereConditions.push(eq(meetings.assignedAdminId, assignedAdminId));
    }

    if (from && to) {
      whereConditions.push(
        between(meetings.scheduledDateTime, new Date(from), new Date(to)),
      );
    } else if (from) {
      whereConditions.push(gte(meetings.scheduledDateTime, new Date(from)));
    } else if (to) {
      whereConditions.push(lte(meetings.scheduledDateTime, new Date(to)));
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: meetings.id })
      .from(meetings)
      .where(and(...whereConditions));

    const total = totalResult.length;

    // Get meetings with admin details
    const meetingResults = await this.db
      .select({
        id: meetings.id,
        title: meetings.title,
        scheduledDateTime: meetings.scheduledDateTime,
        duration: meetings.duration,
        meetingType: meetings.meetingType,
        status: meetings.status,
        assignedAdminName: users.firstName,
        createdBy: meetings.createdBy,
        createdAt: meetings.createdAt,
      })
      .from(meetings)
      .leftJoin(users, eq(meetings.assignedAdminId, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(meetings.scheduledDateTime))
      .limit(limit)
      .offset(offset);

    // Disable view logging for performance in optimized queries

    return {
      data: meetingResults.map((meeting) => ({
        id: meeting.id,
        title: meeting.title,
        scheduledDateTime: meeting.scheduledDateTime,
        duration: meeting.duration,
        meetingType: meeting.meetingType,
        status: meeting.status,
        assignedAdminName: meeting.assignedAdminName || 'Unknown',
        createdBy: meeting.createdBy,
        createdAt: meeting.createdAt,
      })),
      meta: {
        total,
        page,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<MeetingDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const meetingResult = await this.db
      .select({
        id: meetings.id,
        businessId: meetings.businessId,
        title: meetings.title,
        description: meetings.description,
        scheduledDateTime: meetings.scheduledDateTime,
        duration: meetings.duration,
        meetingType: meetings.meetingType,
        status: meetings.status,
        assignedAdminId: meetings.assignedAdminId,
        assignedAdminName: users.firstName,
        createdBy: meetings.createdBy,
        updatedBy: meetings.updatedBy,
        createdAt: meetings.createdAt,
        updatedAt: meetings.updatedAt,
      })
      .from(meetings)
      .leftJoin(users, eq(meetings.assignedAdminId, users.id))
      .where(
        and(
          eq(meetings.id, id),
          eq(meetings.businessId, businessId),
          eq(meetings.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!meetingResult) {
      throw new NotFoundException('Meeting not found');
    }

    // Disable view logging for performance

    return {
      id: meetingResult.id,
      businessId: meetingResult.businessId,
      title: meetingResult.title,
      description: meetingResult.description,
      scheduledDateTime: meetingResult.scheduledDateTime,
      duration: meetingResult.duration,
      meetingType: meetingResult.meetingType,
      status: meetingResult.status,
      assignedAdminId: meetingResult.assignedAdminId,
      assignedAdminName: meetingResult.assignedAdminName || 'Unknown',
      createdBy: meetingResult.createdBy,
      updatedBy: meetingResult.updatedBy,
      createdAt: meetingResult.createdAt,
      updatedAt: meetingResult.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateMeetingDto: UpdateMeetingDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if meeting exists
    const existingMeeting = await this.db
      .select()
      .from(meetings)
      .where(
        and(
          eq(meetings.id, id),
          eq(meetings.businessId, businessId),
          eq(meetings.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingMeeting) {
      throw new NotFoundException('Meeting not found');
    }

    // If updating assigned admin, validate they exist
    if (updateMeetingDto.assignedAdminId) {
      const assignedAdmin = await this.db
        .select()
        .from(users)
        .where(eq(users.id, updateMeetingDto.assignedAdminId))
        .then((results) => results[0]);

      if (!assignedAdmin) {
        throw new NotFoundException('Assigned admin not found');
      }
    }

    // Check for scheduling conflicts if updating time or admin
    if (
      updateMeetingDto.scheduledDateTime ||
      updateMeetingDto.duration ||
      updateMeetingDto.assignedAdminId
    ) {
      const scheduledDateTime = updateMeetingDto.scheduledDateTime
        ? new Date(updateMeetingDto.scheduledDateTime)
        : existingMeeting.scheduledDateTime;
      const duration = updateMeetingDto.duration || existingMeeting.duration;
      const assignedAdminId =
        updateMeetingDto.assignedAdminId || existingMeeting.assignedAdminId;
      const endDateTime = new Date(
        scheduledDateTime.getTime() + duration * 60000,
      );

      const conflictingMeetings = await this.db
        .select()
        .from(meetings)
        .where(
          and(
            eq(meetings.businessId, businessId),
            eq(meetings.assignedAdminId, assignedAdminId),
            eq(meetings.status, MeetingStatus.SCHEDULED),
            eq(meetings.isDeleted, false),
            // Exclude current meeting
            // Check for time overlap
            gte(meetings.scheduledDateTime, scheduledDateTime),
            lte(meetings.scheduledDateTime, endDateTime),
          ),
        );

      const filteredConflicts = conflictingMeetings.filter((m) => m.id !== id);
      if (filteredConflicts.length > 0) {
        throw new ConflictException(
          'The assigned admin has a conflicting meeting at this time',
        );
      }
    }

    const updateData: any = {
      updatedBy: userId,
      updatedAt: new Date(),
    };

    if (updateMeetingDto.title !== undefined)
      updateData.title = updateMeetingDto.title;
    if (updateMeetingDto.description !== undefined)
      updateData.description = updateMeetingDto.description;
    if (updateMeetingDto.scheduledDateTime !== undefined)
      updateData.scheduledDateTime = new Date(
        updateMeetingDto.scheduledDateTime,
      );
    if (updateMeetingDto.duration !== undefined)
      updateData.duration = updateMeetingDto.duration;
    if (updateMeetingDto.meetingType !== undefined)
      updateData.meetingType = updateMeetingDto.meetingType;
    if (updateMeetingDto.status !== undefined)
      updateData.status = updateMeetingDto.status;
    if (updateMeetingDto.assignedAdminId !== undefined)
      updateData.assignedAdminId = updateMeetingDto.assignedAdminId;

    await this.db.update(meetings).set(updateData).where(eq(meetings.id, id));

    // Log the activity
    await this.activityLogService.logUpdate(
      id,
      EntityType.PROJECT,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingMeeting = await this.db
      .select()
      .from(meetings)
      .where(
        and(
          eq(meetings.id, id),
          eq(meetings.businessId, businessId),
          eq(meetings.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!existingMeeting) {
      throw new NotFoundException('Meeting not found');
    }

    await this.db
      .update(meetings)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(meetings.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.PROJECT,
      userId,
      businessId,
      {
        reason: `Deleted meeting: ${existingMeeting.title}`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return { id };
  }

  async checkAvailability(
    userId: string,
    businessId: string | null,
    checkAvailabilityDto: CheckAvailabilityDto,
  ): Promise<AvailabilityResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const date = new Date(checkAvailabilityDto.date);
    const startOfDay = new Date(date.setHours(0, 0, 0, 0));
    const endOfDay = new Date(date.setHours(23, 59, 59, 999));

    // Generate time slots (9 AM to 5 PM, 1-hour slots)
    const timeSlots: TimeSlotDto[] = [];
    for (let hour = 9; hour < 17; hour++) {
      const startTime = `${hour.toString().padStart(2, '0')}:00`;
      const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;

      // Check how many meetings are scheduled for this time slot across all businesses
      const slotStart = new Date(date.setHours(hour, 0, 0, 0));
      const slotEnd = new Date(date.setHours(hour + 1, 0, 0, 0));

      // Get all meetings from all businesses for this time slot
      const whereConditions = [
        eq(meetings.status, MeetingStatus.SCHEDULED),
        eq(meetings.isDeleted, false),
        between(meetings.scheduledDateTime, startOfDay, endOfDay),
      ];

      if (checkAvailabilityDto.adminId) {
        whereConditions.push(
          eq(meetings.assignedAdminId, checkAvailabilityDto.adminId),
        );
      }

      const allMeetingsInSlot = await this.db
        .select({
          scheduledDateTime: meetings.scheduledDateTime,
          duration: meetings.duration,
        })
        .from(meetings)
        .where(and(...whereConditions));

      // Count meetings that overlap with this specific time slot
      const overlappingMeetings = allMeetingsInSlot.filter((meeting) => {
        const meetingStart = new Date(meeting.scheduledDateTime);
        const meetingEnd = new Date(
          meetingStart.getTime() + meeting.duration * 60000,
        );

        return slotStart < meetingEnd && slotEnd > meetingStart;
      });

      // Slot is available if the number of overlapping meetings is less than the maximum allowed
      const available = overlappingMeetings.length < MAX_MEETINGS_PER_SLOT;

      timeSlots.push({
        startTime,
        endTime,
        available,
      });
    }

    return {
      date: checkAvailabilityDto.date,
      timeSlots,
    };
  }

  async getCurrentBusinessMeeting(
    userId: string,
    businessId: string | null,
  ): Promise<MeetingDto | null> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const meetingResult = await this.db
      .select({
        id: meetings.id,
        businessId: meetings.businessId,
        title: meetings.title,
        description: meetings.description,
        scheduledDateTime: meetings.scheduledDateTime,
        duration: meetings.duration,
        meetingType: meetings.meetingType,
        status: meetings.status,
        assignedAdminId: meetings.assignedAdminId,
        assignedAdminName: users.firstName,
        createdBy: meetings.createdBy,
        updatedBy: meetings.updatedBy,
        createdAt: meetings.createdAt,
        updatedAt: meetings.updatedAt,
      })
      .from(meetings)
      .leftJoin(users, eq(meetings.assignedAdminId, users.id))
      .where(
        and(
          eq(meetings.businessId, businessId),
          eq(meetings.status, MeetingStatus.SCHEDULED),
          eq(meetings.isDeleted, false),
        ),
      )
      .orderBy(desc(meetings.scheduledDateTime))
      .limit(1)
      .then((results) => results[0]);

    if (!meetingResult) {
      return null;
    }

    // Disable view logging for performance

    return {
      id: meetingResult.id,
      businessId: meetingResult.businessId,
      title: meetingResult.title,
      description: meetingResult.description,
      scheduledDateTime: meetingResult.scheduledDateTime,
      duration: meetingResult.duration,
      meetingType: meetingResult.meetingType,
      status: meetingResult.status,
      assignedAdminId: meetingResult.assignedAdminId,
      assignedAdminName: meetingResult.assignedAdminName || 'Unknown',
      createdBy: meetingResult.createdBy,
      updatedBy: meetingResult.updatedBy,
      createdAt: meetingResult.createdAt,
      updatedAt: meetingResult.updatedAt,
    };
  }
}
