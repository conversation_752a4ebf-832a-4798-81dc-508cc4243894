import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateServiceOrderStatusDto } from './dto/create-service-order-status.dto';
import { UpdateServiceOrderStatusDto } from './dto/update-service-order-status.dto';
import { ServiceOrderStatusDto } from './dto/service-order-status.dto';
import { ServiceOrderStatusSlimDto } from './dto/service-order-status-slim.dto';
import { ServiceOrderStatusListDto } from './dto/service-order-status-list.dto';
import { serviceOrderStatuses } from '../drizzle/schema/service-order-statuses.schema';
import { eq, and, ilike, sql, gte, lte, asc, inArray } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { ServiceOrderStatusPositionDto } from './dto/update-service-order-status-positions.dto';

@Injectable()
export class ServiceOrderStatusesService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceOrderStatusDto: CreateServiceOrderStatusDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service order status with the same status code already exists for this business
      const existingStatusCode = await this.db
        .select()
        .from(serviceOrderStatuses)
        .where(
          and(
            eq(serviceOrderStatuses.businessId, businessId),
            ilike(
              serviceOrderStatuses.statusCode,
              createServiceOrderStatusDto.statusCode,
            ),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingStatusCode) {
        throw new ConflictException(
          `A service order status with the status code '${createServiceOrderStatusDto.statusCode}' already exists for this business`,
        );
      }

      // Check if a service order status with the same status name already exists for this business
      const existingStatusName = await this.db
        .select()
        .from(serviceOrderStatuses)
        .where(
          and(
            eq(serviceOrderStatuses.businessId, businessId),
            ilike(
              serviceOrderStatuses.statusName,
              createServiceOrderStatusDto.statusName,
            ),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingStatusName) {
        throw new ConflictException(
          `A service order status with the status name '${createServiceOrderStatusDto.statusName}' already exists for this business`,
        );
      }

      // Use a transaction to ensure position reordering and status creation are atomic
      const newServiceOrderStatus = await this.db.transaction(async (tx) => {
        // Shift all existing statuses down by 1 position to make room at position 1
        await this.reorderPositions(tx, businessId, 1);

        // Insert new service order status at position 1
        const [status] = await tx
          .insert(serviceOrderStatuses)
          .values({
            businessId,
            statusCode: createServiceOrderStatusDto.statusCode,
            statusName: createServiceOrderStatusDto.statusName,
            description: createServiceOrderStatusDto.description,
            colorCode: createServiceOrderStatusDto.colorCode,
            statusType: createServiceOrderStatusDto.statusType,
            isActive: createServiceOrderStatusDto.isActive ?? true,
            isDefault: createServiceOrderStatusDto.isDefault ?? false,
            position: 1, // Always create new statuses at position 1 (first)
            createdBy: userId,
          })
          .returning();

        return status;
      });

      // Log the service order status creation activity
      await this.activityLogService.logCreate(
        newServiceOrderStatus.id,
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id: newServiceOrderStatus.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create service order status: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ServiceOrderStatusDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(serviceOrderStatuses.isDeleted, false),
      eq(serviceOrderStatuses.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceOrderStatuses.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(serviceOrderStatuses.createdAt, toDate));
      }
    }

    // Find all service order statuses for the user's active business with pagination
    const [statusesData, totalCount] = await Promise.all([
      this.db
        .select({
          id: serviceOrderStatuses.id,
          businessId: serviceOrderStatuses.businessId,
          statusCode: serviceOrderStatuses.statusCode,
          statusName: serviceOrderStatuses.statusName,
          description: serviceOrderStatuses.description,
          colorCode: serviceOrderStatuses.colorCode,
          statusType: serviceOrderStatuses.statusType,
          isActive: serviceOrderStatuses.isActive,
          isDefault: serviceOrderStatuses.isDefault,
          position: serviceOrderStatuses.position,
          createdAt: serviceOrderStatuses.createdAt,
          updatedAt: serviceOrderStatuses.updatedAt,
          createdBy: users.name,
          updatedBy: sql<string>`updated_user.name`,
        })
        .from(serviceOrderStatuses)
        .leftJoin(users, eq(serviceOrderStatuses.createdBy, users.id))
        .leftJoin(
          sql`${users} AS updated_user`,
          eq(serviceOrderStatuses.updatedBy, sql`updated_user.id`),
        )
        .where(and(...whereConditions))
        .orderBy(
          asc(serviceOrderStatuses.position),
          asc(serviceOrderStatuses.createdAt),
        )
        .limit(limit)
        .offset(offset),
      this.db
        .select({ count: sql<number>`count(*)` })
        .from(serviceOrderStatuses)
        .where(and(...whereConditions))
        .then((result) => result[0]?.count || 0),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return {
      data: statusesData.map((status) => ({
        id: status.id,
        businessId: status.businessId,
        statusCode: status.statusCode,
        statusName: status.statusName,
        description: status.description,
        colorCode: status.colorCode,
        statusType: status.statusType,
        isActive: status.isActive,
        isDefault: status.isDefault,
        position: status.position,
        createdBy: status.createdBy || 'Unknown',
        updatedBy: status.updatedBy,
        createdAt: status.createdAt,
        updatedAt: status.updatedAt,
      })),
      meta: {
        total: totalCount,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<ServiceOrderStatusDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const statusData = await this.db
      .select({
        id: serviceOrderStatuses.id,
        businessId: serviceOrderStatuses.businessId,
        statusCode: serviceOrderStatuses.statusCode,
        statusName: serviceOrderStatuses.statusName,
        description: serviceOrderStatuses.description,
        colorCode: serviceOrderStatuses.colorCode,
        statusType: serviceOrderStatuses.statusType,
        isActive: serviceOrderStatuses.isActive,
        isDefault: serviceOrderStatuses.isDefault,
        position: serviceOrderStatuses.position,
        createdAt: serviceOrderStatuses.createdAt,
        updatedAt: serviceOrderStatuses.updatedAt,
        createdBy: users.name,
        updatedBy: sql<string>`updated_user.name`,
      })
      .from(serviceOrderStatuses)
      .leftJoin(users, eq(serviceOrderStatuses.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(serviceOrderStatuses.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(serviceOrderStatuses.id, id),
          eq(serviceOrderStatuses.businessId, businessId),
          eq(serviceOrderStatuses.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!statusData) {
      throw new NotFoundException('Service order status not found');
    }

    return {
      id: statusData.id,
      businessId: statusData.businessId,
      statusCode: statusData.statusCode,
      statusName: statusData.statusName,
      description: statusData.description,
      colorCode: statusData.colorCode,
      statusType: statusData.statusType,
      isActive: statusData.isActive,
      isDefault: statusData.isDefault,
      position: statusData.position,
      createdBy: statusData.createdBy || 'Unknown',
      updatedBy: statusData.updatedBy,
      createdAt: statusData.createdAt,
      updatedAt: statusData.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceOrderStatusDto: UpdateServiceOrderStatusDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the service order status exists
      const existingStatus = await this.db
        .select()
        .from(serviceOrderStatuses)
        .where(
          and(
            eq(serviceOrderStatuses.id, id),
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingStatus) {
        throw new NotFoundException('Service order status not found');
      }

      // Check for conflicts if status code is being updated
      if (
        updateServiceOrderStatusDto.statusCode &&
        updateServiceOrderStatusDto.statusCode !== existingStatus.statusCode
      ) {
        const existingStatusCode = await this.db
          .select()
          .from(serviceOrderStatuses)
          .where(
            and(
              eq(serviceOrderStatuses.businessId, businessId),
              ilike(
                serviceOrderStatuses.statusCode,
                updateServiceOrderStatusDto.statusCode,
              ),
              eq(serviceOrderStatuses.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingStatusCode) {
          throw new ConflictException(
            `A service order status with the status code '${updateServiceOrderStatusDto.statusCode}' already exists for this business`,
          );
        }
      }

      // Check for conflicts if status name is being updated
      if (
        updateServiceOrderStatusDto.statusName &&
        updateServiceOrderStatusDto.statusName !== existingStatus.statusName
      ) {
        const existingStatusName = await this.db
          .select()
          .from(serviceOrderStatuses)
          .where(
            and(
              eq(serviceOrderStatuses.businessId, businessId),
              ilike(
                serviceOrderStatuses.statusName,
                updateServiceOrderStatusDto.statusName,
              ),
              eq(serviceOrderStatuses.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingStatusName) {
          throw new ConflictException(
            `A service order status with the status name '${updateServiceOrderStatusDto.statusName}' already exists for this business`,
          );
        }
      }

      // Update the service order status
      const [updatedStatus] = await this.db
        .update(serviceOrderStatuses)
        .set({
          ...updateServiceOrderStatusDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(serviceOrderStatuses.id, id))
        .returning();

      // Log the service order status update activity
      await this.activityLogService.logUpdate(
        updatedStatus.id,
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id: updatedStatus.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update service order status: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the service order status exists
      const existingStatus = await this.db
        .select()
        .from(serviceOrderStatuses)
        .where(
          and(
            eq(serviceOrderStatuses.id, id),
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingStatus) {
        throw new NotFoundException('Service order status not found');
      }

      // Soft delete the service order status
      await this.db
        .update(serviceOrderStatuses)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(serviceOrderStatuses.id, id));

      // Log the service order status deletion activity
      await this.activityLogService.logDelete(
        existingStatus.id,
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Service order status deleted successfully',
        id: existingStatus.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete service order status: ${error.message}`,
      );
    }
  }

  // Utility method to reorder positions when inserting at a specific position
  private async reorderPositions(
    tx: any,
    businessId: string,
    insertPosition: number,
  ): Promise<void> {
    // Shift all statuses at or after the insert position down by 1
    await tx
      .update(serviceOrderStatuses)
      .set({
        position: sql`${serviceOrderStatuses.position} + 1`,
      })
      .where(
        and(
          eq(serviceOrderStatuses.businessId, businessId),
          gte(serviceOrderStatuses.position, insertPosition),
          eq(serviceOrderStatuses.isDeleted, false),
        ),
      );
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createServiceOrderStatusDtos: CreateServiceOrderStatusDto[],
  ): Promise<{ ids: string[]; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createServiceOrderStatusDtos ||
        createServiceOrderStatusDtos.length === 0
      ) {
        throw new BadRequestException('No service order statuses provided');
      }

      // Check for duplicate status codes and names within the batch
      const statusCodes = createServiceOrderStatusDtos.map((dto) =>
        dto.statusCode.toLowerCase(),
      );
      const statusNames = createServiceOrderStatusDtos.map((dto) =>
        dto.statusName.toLowerCase(),
      );

      const duplicateStatusCodes = statusCodes.filter(
        (code, index) => statusCodes.indexOf(code) !== index,
      );
      const duplicateStatusNames = statusNames.filter(
        (name, index) => statusNames.indexOf(name) !== index,
      );

      if (duplicateStatusCodes.length > 0) {
        throw new ConflictException(
          `Duplicate status codes found in batch: ${duplicateStatusCodes.join(', ')}`,
        );
      }

      if (duplicateStatusNames.length > 0) {
        throw new ConflictException(
          `Duplicate status names found in batch: ${duplicateStatusNames.join(', ')}`,
        );
      }

      // Check for existing status codes and names in the database
      const existingStatuses = await this.db
        .select({
          statusCode: serviceOrderStatuses.statusCode,
          statusName: serviceOrderStatuses.statusName,
        })
        .from(serviceOrderStatuses)
        .where(
          and(
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        );

      const existingStatusCodes = existingStatuses.map((s) =>
        s.statusCode.toLowerCase(),
      );
      const existingStatusNames = existingStatuses.map((s) =>
        s.statusName.toLowerCase(),
      );

      const conflictingStatusCodes = statusCodes.filter((code) =>
        existingStatusCodes.includes(code),
      );
      const conflictingStatusNames = statusNames.filter((name) =>
        existingStatusNames.includes(name),
      );

      if (conflictingStatusCodes.length > 0) {
        throw new ConflictException(
          `Status codes already exist: ${conflictingStatusCodes.join(', ')}`,
        );
      }

      if (conflictingStatusNames.length > 0) {
        throw new ConflictException(
          `Status names already exist: ${conflictingStatusNames.join(', ')}`,
        );
      }

      // Create all service order statuses in a transaction
      const createdStatuses = await this.db.transaction(async (tx) => {
        const results: { id: string }[] = [];

        for (let i = 0; i < createServiceOrderStatusDtos.length; i++) {
          const dto = createServiceOrderStatusDtos[i];

          // Shift existing statuses down to make room at position (i + 1)
          await this.reorderPositions(tx, businessId, i + 1);

          const [status] = await tx
            .insert(serviceOrderStatuses)
            .values({
              businessId,
              statusCode: dto.statusCode,
              statusName: dto.statusName,
              description: dto.description,
              colorCode: dto.colorCode,
              statusType: dto.statusType,
              isActive: dto.isActive ?? true,
              isDefault: dto.isDefault ?? false,
              position: i + 1, // Sequential positions starting from 1
              createdBy: userId,
            })
            .returning({ id: serviceOrderStatuses.id });

          results.push(status);
        }

        return results;
      });

      // Log bulk creation activity
      await this.activityLogService.logCreate(
        createdStatuses.map((s) => s.id).join(','),
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        ids: createdStatuses.map((status) => status.id),
        message: 'Service order statuses created successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create service order statuses: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    serviceOrderStatusIds: string[],
  ): Promise<{ message: string; deletedIds: string[]; deletedCount: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!serviceOrderStatusIds || serviceOrderStatusIds.length === 0) {
        throw new BadRequestException('No service order status IDs provided');
      }

      // Check which statuses exist and belong to the business
      const existingStatuses = await this.db
        .select({
          id: serviceOrderStatuses.id,
          statusName: serviceOrderStatuses.statusName,
        })
        .from(serviceOrderStatuses)
        .where(
          and(
            inArray(serviceOrderStatuses.id, serviceOrderStatusIds),
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        );

      if (existingStatuses.length === 0) {
        throw new NotFoundException(
          'No service order statuses found to delete',
        );
      }

      const existingIds = existingStatuses.map((status) => status.id);

      // Soft delete the service order statuses
      await this.db
        .update(serviceOrderStatuses)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(serviceOrderStatuses.id, existingIds));

      // Log bulk deletion activity
      await this.activityLogService.logDelete(
        existingIds.join(','),
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Service order statuses deleted successfully',
        deletedIds: existingIds,
        deletedCount: existingStatuses.length,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete service order statuses: ${error.message}`,
      );
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    serviceOrderStatusIds: string[],
    isActive: boolean,
  ): Promise<{ message: string; updatedIds: string[]; updatedCount: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!serviceOrderStatusIds || serviceOrderStatusIds.length === 0) {
        throw new BadRequestException('No service order status IDs provided');
      }

      // Check which statuses exist and belong to the business
      const existingStatuses = await this.db
        .select({
          id: serviceOrderStatuses.id,
          statusName: serviceOrderStatuses.statusName,
        })
        .from(serviceOrderStatuses)
        .where(
          and(
            inArray(serviceOrderStatuses.id, serviceOrderStatusIds),
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        );

      if (existingStatuses.length === 0) {
        throw new NotFoundException(
          'No service order statuses found to update',
        );
      }

      const existingIds = existingStatuses.map((status) => status.id);

      // Update the status of service order statuses
      await this.db
        .update(serviceOrderStatuses)
        .set({
          isActive,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(serviceOrderStatuses.id, existingIds));

      // Log bulk status update activity
      await this.activityLogService.logUpdate(
        existingIds.join(','),
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Service order statuses updated successfully',
        updatedIds: existingIds,
        updatedCount: existingStatuses.length,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update service order statuses: ${error.message}`,
      );
    }
  }

  async updatePositions(
    userId: string,
    businessId: string | null,
    positionUpdates: ServiceOrderStatusPositionDto[],
  ): Promise<{ message: string; updatedIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!positionUpdates || positionUpdates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate that all positions are unique and positive
      const positions = positionUpdates.map((update) => update.position);
      const uniquePositions = [...new Set(positions)];

      if (positions.length !== uniquePositions.length) {
        throw new BadRequestException('Duplicate positions are not allowed');
      }

      if (positions.some((pos) => pos < 1)) {
        throw new BadRequestException('Positions must be positive integers');
      }

      // Check that all service order statuses exist and belong to the business
      const statusIds = positionUpdates.map((update) => update.id);
      const existingStatuses = await this.db
        .select({
          id: serviceOrderStatuses.id,
          statusName: serviceOrderStatuses.statusName,
        })
        .from(serviceOrderStatuses)
        .where(
          and(
            inArray(serviceOrderStatuses.id, statusIds),
            eq(serviceOrderStatuses.businessId, businessId),
            eq(serviceOrderStatuses.isDeleted, false),
          ),
        );

      if (existingStatuses.length !== statusIds.length) {
        throw new NotFoundException('Some service order statuses not found');
      }

      // Update positions in a transaction
      await this.db.transaction(async (tx) => {
        for (const update of positionUpdates) {
          await tx
            .update(serviceOrderStatuses)
            .set({
              position: update.position,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(serviceOrderStatuses.id, update.id));
        }
      });

      // Log position update activity
      await this.activityLogService.logUpdate(
        statusIds.join(','),
        EntityType.SERVICE_ORDER_STATUS,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Service order status positions updated successfully',
        updatedIds: statusIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update service order status positions: ${error.message}`,
      );
    }
  }

  async checkStatusNameAvailability(
    businessId: string | null,
    statusName: string,
    excludeId?: string,
  ): Promise<{ available: boolean; statusName: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(serviceOrderStatuses.businessId, businessId),
      ilike(serviceOrderStatuses.statusName, statusName),
      eq(serviceOrderStatuses.isDeleted, false),
    ];

    if (excludeId) {
      whereConditions.push(sql`${serviceOrderStatuses.id} != ${excludeId}`);
    }

    const existingStatus = await this.db
      .select()
      .from(serviceOrderStatuses)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const available = !existingStatus;

    return {
      available,
      statusName,
      message: available
        ? 'Service order status name is available'
        : 'Service order status name is already taken',
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ServiceOrderStatusSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const statuses = await this.db
      .select({
        id: serviceOrderStatuses.id,
        statusCode: serviceOrderStatuses.statusCode,
        statusName: serviceOrderStatuses.statusName,
        colorCode: serviceOrderStatuses.colorCode,
        statusType: serviceOrderStatuses.statusType,
        isActive: serviceOrderStatuses.isActive,
        isDefault: serviceOrderStatuses.isDefault,
        position: serviceOrderStatuses.position,
      })
      .from(serviceOrderStatuses)
      .where(
        and(
          eq(serviceOrderStatuses.businessId, businessId),
          eq(serviceOrderStatuses.isActive, true),
          eq(serviceOrderStatuses.isDeleted, false),
        ),
      )
      .orderBy(
        asc(serviceOrderStatuses.position),
        asc(serviceOrderStatuses.createdAt),
      );

    return statuses;
  }

  async findAllList(
    userId: string,
    businessId: string | null,
  ): Promise<ServiceOrderStatusListDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const statuses = await this.db
      .select({
        id: serviceOrderStatuses.id,
        statusCode: serviceOrderStatuses.statusCode,
        statusName: serviceOrderStatuses.statusName,
        description: serviceOrderStatuses.description,
        colorCode: serviceOrderStatuses.colorCode,
        statusType: serviceOrderStatuses.statusType,
        isActive: serviceOrderStatuses.isActive,
        isDefault: serviceOrderStatuses.isDefault,
        position: serviceOrderStatuses.position,
        createdAt: serviceOrderStatuses.createdAt,
        updatedAt: serviceOrderStatuses.updatedAt,
      })
      .from(serviceOrderStatuses)
      .where(
        and(
          eq(serviceOrderStatuses.businessId, businessId),
          eq(serviceOrderStatuses.isDeleted, false),
        ),
      )
      .orderBy(
        asc(serviceOrderStatuses.position),
        asc(serviceOrderStatuses.createdAt),
      );

    return statuses;
  }
}
