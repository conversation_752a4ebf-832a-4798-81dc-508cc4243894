import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  serviceTimeSlots,
  serviceTimeSlotServices,
  serviceTimeSlotStaff,
} from '../drizzle/schema/service-time-slots.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { services } from '../drizzle/schema/services.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { eq, and, ilike, sql, gte, lte, desc, asc, inArray } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import { UsersService } from '../users/users.service';
import { CreateServiceTimeSlotDto } from './dto/create-service-time-slot.dto';
import { UpdateServiceTimeSlotDto } from './dto/update-service-time-slot.dto';
import { ServiceTimeSlotDto } from './dto/service-time-slot.dto';
import { ServiceTimeSlotSlimDto } from './dto/service-time-slot-slim.dto';

@Injectable()
export class ServiceTimeSlotsService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceTimeSlotDto: CreateServiceTimeSlotDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service time slot with the same name already exists for this location
      const existingSlot = await this.db
        .select()
        .from(serviceTimeSlots)
        .where(
          and(
            eq(
              serviceTimeSlots.locationId,
              createServiceTimeSlotDto.locationId,
            ),
            ilike(serviceTimeSlots.slotName, createServiceTimeSlotDto.slotName),
            eq(serviceTimeSlots.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingSlot) {
        throw new ConflictException(
          `A service time slot with the name '${createServiceTimeSlotDto.slotName}' already exists for this location`,
        );
      }

      // Validate location exists and belongs to business
      const location = await this.db
        .select()
        .from(locations)
        .where(
          and(
            eq(locations.id, createServiceTimeSlotDto.locationId),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!location) {
        throw new BadRequestException(
          'Location not found or does not belong to this business',
        );
      }

      // Validate services if provided
      if (
        createServiceTimeSlotDto.serviceIds &&
        createServiceTimeSlotDto.serviceIds.length > 0
      ) {
        const validServices = await this.db
          .select({ id: services.id })
          .from(services)
          .where(
            and(
              inArray(services.id, createServiceTimeSlotDto.serviceIds),
              eq(services.businessId, businessId),
              eq(services.isDeleted, false),
            ),
          );

        if (
          validServices.length !== createServiceTimeSlotDto.serviceIds.length
        ) {
          throw new BadRequestException(
            'One or more services not found or do not belong to this business',
          );
        }
      }

      // Validate staff if provided
      if (
        createServiceTimeSlotDto.staffIds &&
        createServiceTimeSlotDto.staffIds.length > 0
      ) {
        const validStaff = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              inArray(staffMembers.id, createServiceTimeSlotDto.staffIds),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          );

        if (validStaff.length !== createServiceTimeSlotDto.staffIds.length) {
          throw new BadRequestException(
            'One or more staff members not found or do not belong to this business',
          );
        }
      }

      // Use a transaction to ensure all operations are atomic
      const newServiceTimeSlot = await this.db.transaction(async (tx) => {
        // Insert new service time slot
        const [slot] = await tx
          .insert(serviceTimeSlots)
          .values({
            businessId,
            locationId: createServiceTimeSlotDto.locationId,
            slotName: createServiceTimeSlotDto.slotName,
            startTime: createServiceTimeSlotDto.startTime,
            endTime: createServiceTimeSlotDto.endTime,
            maxAppointments: createServiceTimeSlotDto.maxAppointments ?? 1,
            reservedCount: createServiceTimeSlotDto.reservedCount ?? 0,
            scheduleType: createServiceTimeSlotDto.scheduleType,
            dayOfWeek: createServiceTimeSlotDto.dayOfWeek,
            dayOfMonth: createServiceTimeSlotDto.dayOfMonth,
            weekOfMonth: createServiceTimeSlotDto.weekOfMonth,
            monthOfYear: createServiceTimeSlotDto.monthOfYear,
            createdBy: userId,
          })
          .returning();

        // Handle service associations
        if (
          createServiceTimeSlotDto.serviceIds &&
          createServiceTimeSlotDto.serviceIds.length > 0
        ) {
          const serviceAssociations = createServiceTimeSlotDto.serviceIds.map(
            (serviceId) => ({
              timeSlotId: slot.id,
              serviceId,
              businessId,
              createdBy: userId,
            }),
          );

          await tx.insert(serviceTimeSlotServices).values(serviceAssociations);
        }

        // Handle staff associations
        if (
          createServiceTimeSlotDto.staffIds &&
          createServiceTimeSlotDto.staffIds.length > 0
        ) {
          const staffAssociations = createServiceTimeSlotDto.staffIds.map(
            (staffId) => ({
              timeSlotId: slot.id,
              staffId,
              businessId,
              createdBy: userId,
            }),
          );

          await tx.insert(serviceTimeSlotStaff).values(staffAssociations);
        }

        return slot;
      });

      // Log the service time slot creation activity
      await this.activityLogService.logCreate(
        newServiceTimeSlot.id,
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id: newServiceTimeSlot.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create service time slot: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createServiceTimeSlotDto: CreateServiceTimeSlotDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.create(
      userId,
      businessId,
      createServiceTimeSlotDto,
    );
    return {
      id: result.id,
      message: 'Service time slot created successfully',
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    slotName?: string,
    locationId?: string,
    scheduleType?: string,
    dayOfWeek?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ServiceTimeSlotDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [eq(serviceTimeSlots.isDeleted, false)];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(serviceTimeSlots.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(serviceTimeSlots.createdAt, toDate));
      }
    }

    // Add simple filters
    if (slotName) {
      whereConditions.push(ilike(serviceTimeSlots.slotName, `%${slotName}%`));
    }

    if (locationId) {
      whereConditions.push(eq(serviceTimeSlots.locationId, locationId));
    }

    if (scheduleType) {
      whereConditions.push(
        eq(serviceTimeSlots.scheduleType, scheduleType as any),
      );
    }

    if (dayOfWeek) {
      whereConditions.push(eq(serviceTimeSlots.dayOfWeek, dayOfWeek as any));
    }

    // Build sort conditions
    let orderBy = [desc(serviceTimeSlots.createdAt)];
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        if (Array.isArray(sortConfig) && sortConfig.length > 0) {
          orderBy = sortConfig.map((s) => {
            // Define allowed sortable columns
            const allowedColumns = {
              slotName: serviceTimeSlots.slotName,
              startTime: serviceTimeSlots.startTime,
              endTime: serviceTimeSlots.endTime,
              scheduleType: serviceTimeSlots.scheduleType,
              dayOfWeek: serviceTimeSlots.dayOfWeek,
              maxAppointments: serviceTimeSlots.maxAppointments,
              reservedCount: serviceTimeSlots.reservedCount,
              createdAt: serviceTimeSlots.createdAt,
              updatedAt: serviceTimeSlots.updatedAt,
            };

            const column = allowedColumns[s.id as keyof typeof allowedColumns];
            if (column) {
              return s.desc ? desc(column) : asc(column);
            }
            // Fallback to default sort
            return asc(serviceTimeSlots.slotName);
          });
        }
      } catch {
        // Keep default sort if parsing fails
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(serviceTimeSlots)
      .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
      .where(
        and(
          ...whereConditions,
          eq(locations.businessId, businessId),
          eq(locations.isDeleted, false),
        ),
      );

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data with relationships
    const serviceTimeSlotsData = await this.db
      .select({
        id: serviceTimeSlots.id,
        locationId: serviceTimeSlots.locationId,
        slotName: serviceTimeSlots.slotName,
        startTime: serviceTimeSlots.startTime,
        endTime: serviceTimeSlots.endTime,
        maxAppointments: serviceTimeSlots.maxAppointments,
        reservedCount: serviceTimeSlots.reservedCount,
        scheduleType: serviceTimeSlots.scheduleType,
        dayOfWeek: serviceTimeSlots.dayOfWeek,
        dayOfMonth: serviceTimeSlots.dayOfMonth,
        weekOfMonth: serviceTimeSlots.weekOfMonth,
        monthOfYear: serviceTimeSlots.monthOfYear,
        createdAt: serviceTimeSlots.createdAt,
        updatedAt: serviceTimeSlots.updatedAt,
        createdBy: serviceTimeSlots.createdBy,
        updatedBy: serviceTimeSlots.updatedBy,
        locationName: locations.name,
        createdByUser: users.name,
        updatedByUser: sql<string>`updated_user.name`,
      })
      .from(serviceTimeSlots)
      .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
      .leftJoin(users, eq(serviceTimeSlots.createdBy, users.id))
      .leftJoin(
        sql`${users} as updated_user`,
        eq(serviceTimeSlots.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          ...whereConditions,
          eq(locations.businessId, businessId),
          eq(locations.isDeleted, false),
        ),
      )
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get services and staff for each time slot
    const timeSlotIds = serviceTimeSlotsData.map((slot) => slot.id);

    let servicesData: any[] = [];
    let staffData: any[] = [];

    if (timeSlotIds.length > 0) {
      // Get associated services
      servicesData = await this.db
        .select({
          timeSlotId: serviceTimeSlotServices.timeSlotId,
          serviceId: services.id,
          serviceName: services.name,
        })
        .from(serviceTimeSlotServices)
        .innerJoin(services, eq(serviceTimeSlotServices.serviceId, services.id))
        .where(
          and(
            inArray(serviceTimeSlotServices.timeSlotId, timeSlotIds),
            eq(serviceTimeSlotServices.isDeleted, false),
            eq(services.isDeleted, false),
          ),
        );

      // Get associated staff
      staffData = await this.db
        .select({
          timeSlotId: serviceTimeSlotStaff.timeSlotId,
          staffId: staffMembers.id,
          staffDisplayName: staffMembers.displayName,
        })
        .from(serviceTimeSlotStaff)
        .innerJoin(
          staffMembers,
          eq(serviceTimeSlotStaff.staffId, staffMembers.id),
        )
        .where(
          and(
            inArray(serviceTimeSlotStaff.timeSlotId, timeSlotIds),
            eq(serviceTimeSlotStaff.isDeleted, false),
            eq(staffMembers.isDeleted, false),
          ),
        );
    }

    // Transform data to DTOs
    const data: ServiceTimeSlotDto[] = serviceTimeSlotsData.map((slot) => ({
      id: slot.id,
      locationId: slot.locationId,
      location: {
        id: slot.locationId,
        name: slot.locationName,
      },
      slotName: slot.slotName,
      startTime: slot.startTime,
      endTime: slot.endTime,
      maxAppointments: slot.maxAppointments,
      reservedCount: slot.reservedCount,
      scheduleType: slot.scheduleType,
      dayOfWeek: slot.dayOfWeek,
      dayOfMonth: slot.dayOfMonth,
      weekOfMonth: slot.weekOfMonth,
      monthOfYear: slot.monthOfYear,
      services: servicesData
        .filter((s) => s.timeSlotId === slot.id)
        .map((s) => ({ id: s.serviceId, name: s.serviceName })),
      staff: staffData
        .filter((s) => s.timeSlotId === slot.id)
        .map((s) => ({ id: s.staffId, displayName: s.staffDisplayName })),
      createdBy: slot.createdByUser || 'Unknown',
      updatedBy: slot.updatedByUser || undefined,
      createdAt: slot.createdAt,
      updatedAt: slot.updatedAt,
    }));

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<ServiceTimeSlotSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const serviceTimeSlotsData = await this.db
      .select({
        id: serviceTimeSlots.id,
        locationId: serviceTimeSlots.locationId,
        slotName: serviceTimeSlots.slotName,
        startTime: serviceTimeSlots.startTime,
        endTime: serviceTimeSlots.endTime,
        maxAppointments: serviceTimeSlots.maxAppointments,
        reservedCount: serviceTimeSlots.reservedCount,
        scheduleType: serviceTimeSlots.scheduleType,
        dayOfWeek: serviceTimeSlots.dayOfWeek,
      })
      .from(serviceTimeSlots)
      .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
      .where(
        and(
          eq(serviceTimeSlots.isDeleted, false),
          eq(locations.businessId, businessId),
          eq(locations.isDeleted, false),
        ),
      )
      .orderBy(asc(serviceTimeSlots.slotName));

    return serviceTimeSlotsData.map((slot) => ({
      id: slot.id,
      locationId: slot.locationId,
      slotName: slot.slotName,
      startTime: slot.startTime,
      endTime: slot.endTime,
      maxAppointments: slot.maxAppointments,
      reservedCount: slot.reservedCount,
      scheduleType: slot.scheduleType,
      dayOfWeek: slot.dayOfWeek,
    }));
  }

  async findOne(userId: string, id: string): Promise<ServiceTimeSlotDto> {
    const serviceTimeSlotData = await this.db
      .select({
        id: serviceTimeSlots.id,
        locationId: serviceTimeSlots.locationId,
        slotName: serviceTimeSlots.slotName,
        startTime: serviceTimeSlots.startTime,
        endTime: serviceTimeSlots.endTime,
        maxAppointments: serviceTimeSlots.maxAppointments,
        reservedCount: serviceTimeSlots.reservedCount,
        scheduleType: serviceTimeSlots.scheduleType,
        dayOfWeek: serviceTimeSlots.dayOfWeek,
        dayOfMonth: serviceTimeSlots.dayOfMonth,
        weekOfMonth: serviceTimeSlots.weekOfMonth,
        monthOfYear: serviceTimeSlots.monthOfYear,
        createdAt: serviceTimeSlots.createdAt,
        updatedAt: serviceTimeSlots.updatedAt,
        createdBy: serviceTimeSlots.createdBy,
        updatedBy: serviceTimeSlots.updatedBy,
        locationName: locations.name,
        businessId: locations.businessId,
        createdByUser: users.name,
        updatedByUser: sql<string>`updated_user.name`,
      })
      .from(serviceTimeSlots)
      .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
      .leftJoin(users, eq(serviceTimeSlots.createdBy, users.id))
      .leftJoin(
        sql`${users} as updated_user`,
        eq(serviceTimeSlots.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(serviceTimeSlots.id, id),
          eq(serviceTimeSlots.isDeleted, false),
          eq(locations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!serviceTimeSlotData) {
      throw new NotFoundException('Service time slot not found');
    }

    // Access control is handled by the business ID check in the query

    // Get associated services
    const servicesData = await this.db
      .select({
        serviceId: services.id,
        serviceName: services.name,
      })
      .from(serviceTimeSlotServices)
      .innerJoin(services, eq(serviceTimeSlotServices.serviceId, services.id))
      .where(
        and(
          eq(serviceTimeSlotServices.timeSlotId, id),
          eq(serviceTimeSlotServices.isDeleted, false),
          eq(services.isDeleted, false),
        ),
      );

    // Get associated staff
    const staffData = await this.db
      .select({
        staffId: staffMembers.id,
        staffDisplayName: staffMembers.displayName,
      })
      .from(serviceTimeSlotStaff)
      .innerJoin(
        staffMembers,
        eq(serviceTimeSlotStaff.staffId, staffMembers.id),
      )
      .where(
        and(
          eq(serviceTimeSlotStaff.timeSlotId, id),
          eq(serviceTimeSlotStaff.isDeleted, false),
          eq(staffMembers.isDeleted, false),
        ),
      );

    return {
      id: serviceTimeSlotData.id,
      locationId: serviceTimeSlotData.locationId,
      location: {
        id: serviceTimeSlotData.locationId,
        name: serviceTimeSlotData.locationName,
      },
      slotName: serviceTimeSlotData.slotName,
      startTime: serviceTimeSlotData.startTime,
      endTime: serviceTimeSlotData.endTime,
      maxAppointments: serviceTimeSlotData.maxAppointments,
      reservedCount: serviceTimeSlotData.reservedCount,
      scheduleType: serviceTimeSlotData.scheduleType,
      dayOfWeek: serviceTimeSlotData.dayOfWeek,
      dayOfMonth: serviceTimeSlotData.dayOfMonth,
      weekOfMonth: serviceTimeSlotData.weekOfMonth,
      monthOfYear: serviceTimeSlotData.monthOfYear,
      services: servicesData.map((s) => ({
        id: s.serviceId,
        name: s.serviceName,
      })),
      staff: staffData.map((s) => ({
        id: s.staffId,
        displayName: s.staffDisplayName,
      })),
      createdBy: serviceTimeSlotData.createdByUser || 'Unknown',
      updatedBy: serviceTimeSlotData.updatedByUser || undefined,
      createdAt: serviceTimeSlotData.createdAt,
      updatedAt: serviceTimeSlotData.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceTimeSlotDto: UpdateServiceTimeSlotDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if service time slot exists and belongs to user's business
      const existingSlot = await this.db
        .select({
          id: serviceTimeSlots.id,
          locationId: serviceTimeSlots.locationId,
          slotName: serviceTimeSlots.slotName,
          businessId: locations.businessId,
        })
        .from(serviceTimeSlots)
        .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
        .where(
          and(
            eq(serviceTimeSlots.id, id),
            eq(serviceTimeSlots.isDeleted, false),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingSlot) {
        throw new NotFoundException('Service time slot not found');
      }

      if (existingSlot.businessId !== businessId) {
        throw new UnauthorizedException(
          'Access denied to this service time slot',
        );
      }

      // Check for name conflicts if name is being updated
      if (
        updateServiceTimeSlotDto.slotName &&
        updateServiceTimeSlotDto.slotName !== existingSlot.slotName
      ) {
        const locationIdToCheck =
          updateServiceTimeSlotDto.locationId || existingSlot.locationId;

        const conflictingSlot = await this.db
          .select()
          .from(serviceTimeSlots)
          .where(
            and(
              eq(serviceTimeSlots.locationId, locationIdToCheck),
              ilike(
                serviceTimeSlots.slotName,
                updateServiceTimeSlotDto.slotName,
              ),
              eq(serviceTimeSlots.isDeleted, false),
              sql`${serviceTimeSlots.id} != ${id}`,
            ),
          )
          .then((results) => results[0]);

        if (conflictingSlot) {
          throw new ConflictException(
            `A service time slot with the name '${updateServiceTimeSlotDto.slotName}' already exists for this location`,
          );
        }
      }

      // Validate location if being updated
      if (
        updateServiceTimeSlotDto.locationId &&
        updateServiceTimeSlotDto.locationId !== existingSlot.locationId
      ) {
        const location = await this.db
          .select()
          .from(locations)
          .where(
            and(
              eq(locations.id, updateServiceTimeSlotDto.locationId),
              eq(locations.businessId, businessId),
              eq(locations.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!location) {
          throw new BadRequestException(
            'Location not found or does not belong to this business',
          );
        }
      }

      // Validate services if provided
      if (
        updateServiceTimeSlotDto.serviceIds &&
        updateServiceTimeSlotDto.serviceIds.length > 0
      ) {
        const validServices = await this.db
          .select({ id: services.id })
          .from(services)
          .where(
            and(
              inArray(services.id, updateServiceTimeSlotDto.serviceIds),
              eq(services.businessId, businessId),
              eq(services.isDeleted, false),
            ),
          );

        if (
          validServices.length !== updateServiceTimeSlotDto.serviceIds.length
        ) {
          throw new BadRequestException(
            'One or more services not found or do not belong to this business',
          );
        }
      }

      // Validate staff if provided
      if (
        updateServiceTimeSlotDto.staffIds &&
        updateServiceTimeSlotDto.staffIds.length > 0
      ) {
        const validStaff = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              inArray(staffMembers.id, updateServiceTimeSlotDto.staffIds),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          );

        if (validStaff.length !== updateServiceTimeSlotDto.staffIds.length) {
          throw new BadRequestException(
            'One or more staff members not found or do not belong to this business',
          );
        }
      }

      // Use a transaction to ensure all operations are atomic
      const updatedServiceTimeSlot = await this.db.transaction(async (tx) => {
        // Update service time slot
        const [slot] = await tx
          .update(serviceTimeSlots)
          .set({
            locationId: updateServiceTimeSlotDto.locationId,
            slotName: updateServiceTimeSlotDto.slotName,
            startTime: updateServiceTimeSlotDto.startTime,
            endTime: updateServiceTimeSlotDto.endTime,
            maxAppointments: updateServiceTimeSlotDto.maxAppointments,
            reservedCount: updateServiceTimeSlotDto.reservedCount,
            scheduleType: updateServiceTimeSlotDto.scheduleType,
            dayOfWeek: updateServiceTimeSlotDto.dayOfWeek,
            dayOfMonth: updateServiceTimeSlotDto.dayOfMonth,
            weekOfMonth: updateServiceTimeSlotDto.weekOfMonth,
            monthOfYear: updateServiceTimeSlotDto.monthOfYear,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceTimeSlots.id, id))
          .returning();

        // Handle service associations if provided
        if (updateServiceTimeSlotDto.serviceIds !== undefined) {
          // Remove existing service associations
          await tx
            .update(serviceTimeSlotServices)
            .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
            .where(eq(serviceTimeSlotServices.timeSlotId, id));

          // Add new service associations
          if (updateServiceTimeSlotDto.serviceIds.length > 0) {
            const serviceAssociations = updateServiceTimeSlotDto.serviceIds.map(
              (serviceId) => ({
                timeSlotId: id,
                serviceId,
                businessId,
                createdBy: userId,
              }),
            );

            await tx
              .insert(serviceTimeSlotServices)
              .values(serviceAssociations);
          }
        }

        // Handle staff associations if provided
        if (updateServiceTimeSlotDto.staffIds !== undefined) {
          // Remove existing staff associations
          await tx
            .update(serviceTimeSlotStaff)
            .set({ isDeleted: true, updatedBy: userId, updatedAt: new Date() })
            .where(eq(serviceTimeSlotStaff.timeSlotId, id));

          // Add new staff associations
          if (updateServiceTimeSlotDto.staffIds.length > 0) {
            const staffAssociations = updateServiceTimeSlotDto.staffIds.map(
              (staffId) => ({
                timeSlotId: id,
                staffId,
                businessId,
                createdBy: userId,
              }),
            );

            await tx.insert(serviceTimeSlotStaff).values(staffAssociations);
          }
        }

        return slot;
      });

      // Log the service time slot update activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id: updatedServiceTimeSlot.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update service time slot: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateServiceTimeSlotDto: UpdateServiceTimeSlotDto,
  ): Promise<{ id: string; message: string }> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateServiceTimeSlotDto,
    );
    return {
      id: result.id,
      message: 'Service time slot updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if service time slot exists and belongs to user's business
      const existingSlot = await this.db
        .select({
          id: serviceTimeSlots.id,
          slotName: serviceTimeSlots.slotName,
          businessId: locations.businessId,
        })
        .from(serviceTimeSlots)
        .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
        .where(
          and(
            eq(serviceTimeSlots.id, id),
            eq(serviceTimeSlots.isDeleted, false),
            eq(locations.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingSlot) {
        throw new NotFoundException('Service time slot not found');
      }

      if (existingSlot.businessId !== businessId) {
        throw new UnauthorizedException(
          'Access denied to this service time slot',
        );
      }

      // Use a transaction to ensure all operations are atomic
      await this.db.transaction(async (tx) => {
        // Soft delete the service time slot
        await tx
          .update(serviceTimeSlots)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceTimeSlots.id, id));

        // Soft delete associated service relationships
        await tx
          .update(serviceTimeSlotServices)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceTimeSlotServices.timeSlotId, id));

        // Soft delete associated staff relationships
        await tx
          .update(serviceTimeSlotStaff)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceTimeSlotStaff.timeSlotId, id));
      });

      // Log the service time slot deletion activity
      await this.activityLogService.logDelete(
        id,
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        id,
        message: 'Service time slot deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete service time slot: ${error.message}`,
      );
    }
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    locationId: string,
    name: string,
  ): Promise<{ available: boolean; name: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Validate location exists and belongs to business
    const location = await this.db
      .select()
      .from(locations)
      .where(
        and(
          eq(locations.id, locationId),
          eq(locations.businessId, businessId),
          eq(locations.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!location) {
      throw new BadRequestException(
        'Location not found or does not belong to this business',
      );
    }

    const existingSlot = await this.db
      .select()
      .from(serviceTimeSlots)
      .where(
        and(
          eq(serviceTimeSlots.locationId, locationId),
          ilike(serviceTimeSlots.slotName, name),
          eq(serviceTimeSlots.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    const available = !existingSlot;

    return {
      available,
      name,
      message: available
        ? 'Service time slot name is available'
        : 'Service time slot name is already taken for this location',
    };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    serviceTimeSlotsData: CreateServiceTimeSlotDto[],
  ): Promise<{ ids: string[]; created: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!serviceTimeSlotsData || serviceTimeSlotsData.length === 0) {
        throw new BadRequestException('No service time slots provided');
      }

      // Check for duplicate names within the batch
      const nameLocationPairs = serviceTimeSlotsData.map(
        (slot) => `${slot.slotName.toLowerCase()}-${slot.locationId}`,
      );
      const uniqueNameLocationPairs = new Set(nameLocationPairs);
      if (nameLocationPairs.length !== uniqueNameLocationPairs.size) {
        throw new ConflictException(
          'Duplicate service time slot names found in the batch for the same location',
        );
      }

      // Validate all locations exist and belong to business
      const locationIds = [
        ...new Set(serviceTimeSlotsData.map((slot) => slot.locationId)),
      ];
      const validLocations = await this.db
        .select({ id: locations.id })
        .from(locations)
        .where(
          and(
            inArray(locations.id, locationIds),
            eq(locations.businessId, businessId),
            eq(locations.isDeleted, false),
          ),
        );

      if (validLocations.length !== locationIds.length) {
        throw new BadRequestException(
          'One or more locations not found or do not belong to this business',
        );
      }

      // Check for existing names in database
      const existingSlots = await this.db
        .select({
          slotName: serviceTimeSlots.slotName,
          locationId: serviceTimeSlots.locationId,
        })
        .from(serviceTimeSlots)
        .where(
          and(
            inArray(serviceTimeSlots.locationId, locationIds),
            eq(serviceTimeSlots.isDeleted, false),
          ),
        );

      const existingNameLocationPairs = new Set(
        existingSlots.map(
          (slot) => `${slot.slotName.toLowerCase()}-${slot.locationId}`,
        ),
      );

      const conflicts = serviceTimeSlotsData.filter((slot) =>
        existingNameLocationPairs.has(
          `${slot.slotName.toLowerCase()}-${slot.locationId}`,
        ),
      );

      if (conflicts.length > 0) {
        const conflictNames = conflicts
          .map((slot) => `"${slot.slotName}" for location ${slot.locationId}`)
          .join(', ');
        throw new ConflictException(
          `Service time slot names already exist: ${conflictNames}`,
        );
      }

      // Validate all services if provided
      const allServiceIds = serviceTimeSlotsData
        .flatMap((slot) => slot.serviceIds || [])
        .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates

      if (allServiceIds.length > 0) {
        const validServices = await this.db
          .select({ id: services.id })
          .from(services)
          .where(
            and(
              inArray(services.id, allServiceIds),
              eq(services.businessId, businessId),
              eq(services.isDeleted, false),
            ),
          );

        if (validServices.length !== allServiceIds.length) {
          throw new BadRequestException(
            'One or more services not found or do not belong to this business',
          );
        }
      }

      // Validate all staff if provided
      const allStaffIds = serviceTimeSlotsData
        .flatMap((slot) => slot.staffIds || [])
        .filter((id, index, arr) => arr.indexOf(id) === index); // Remove duplicates

      if (allStaffIds.length > 0) {
        const validStaff = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              inArray(staffMembers.id, allStaffIds),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          );

        if (validStaff.length !== allStaffIds.length) {
          throw new BadRequestException(
            'One or more staff members not found or do not belong to this business',
          );
        }
      }

      // Use a transaction to ensure all operations are atomic
      const createdSlots = await this.db.transaction(async (tx) => {
        const slots = [];

        for (const slotDto of serviceTimeSlotsData) {
          // Insert service time slot
          const [slot] = await tx
            .insert(serviceTimeSlots)
            .values({
              businessId,
              locationId: slotDto.locationId,
              slotName: slotDto.slotName,
              startTime: slotDto.startTime,
              endTime: slotDto.endTime,
              maxAppointments: slotDto.maxAppointments ?? 1,
              reservedCount: slotDto.reservedCount ?? 0,
              scheduleType: slotDto.scheduleType,
              dayOfWeek: slotDto.dayOfWeek,
              dayOfMonth: slotDto.dayOfMonth,
              weekOfMonth: slotDto.weekOfMonth,
              monthOfYear: slotDto.monthOfYear,
              createdBy: userId,
            })
            .returning();

          // Handle service associations
          if (slotDto.serviceIds && slotDto.serviceIds.length > 0) {
            const serviceAssociations = slotDto.serviceIds.map((serviceId) => ({
              timeSlotId: slot.id,
              serviceId,
              businessId,
              createdBy: userId,
            }));

            await tx
              .insert(serviceTimeSlotServices)
              .values(serviceAssociations);
          }

          // Handle staff associations
          if (slotDto.staffIds && slotDto.staffIds.length > 0) {
            const staffAssociations = slotDto.staffIds.map((staffId) => ({
              timeSlotId: slot.id,
              staffId,
              businessId,
              createdBy: userId,
            }));

            await tx.insert(serviceTimeSlotStaff).values(staffAssociations);
          }

          slots.push(slot);
        }

        return slots;
      });

      // Log bulk creation activity
      await this.activityLogService.logCreate(
        createdSlots.map((slot) => slot.id).join(','),
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        ids: createdSlots.map((slot) => slot.id),
        created: createdSlots.length,
        message: `Successfully created ${createdSlots.length} service time slots`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create service time slots: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    serviceTimeSlotIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
    failed?: Array<{ id: string; reason: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!serviceTimeSlotIds || serviceTimeSlotIds.length === 0) {
        throw new BadRequestException('No service time slot IDs provided');
      }

      // Find existing service time slots that belong to user's business
      const existingSlots = await this.db
        .select({
          id: serviceTimeSlots.id,
          slotName: serviceTimeSlots.slotName,
          businessId: locations.businessId,
        })
        .from(serviceTimeSlots)
        .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
        .where(
          and(
            inArray(serviceTimeSlots.id, serviceTimeSlotIds),
            eq(serviceTimeSlots.isDeleted, false),
            eq(locations.isDeleted, false),
          ),
        );

      const validSlots = existingSlots.filter(
        (slot) => slot.businessId === businessId,
      );
      const validIds = validSlots.map((slot) => slot.id);

      const failed: Array<{ id: string; reason: string }> = [];

      // Track failed deletions
      serviceTimeSlotIds.forEach((id) => {
        if (!validIds.includes(id)) {
          const existingSlot = existingSlots.find((slot) => slot.id === id);
          if (!existingSlot) {
            failed.push({ id, reason: 'Service time slot not found' });
          } else if (existingSlot.businessId !== businessId) {
            failed.push({
              id,
              reason: 'Access denied to this service time slot',
            });
          }
        }
      });

      if (validIds.length === 0) {
        return {
          deleted: 0,
          message: 'No valid service time slots found to delete',
          deletedIds: [],
          failed: failed.length > 0 ? failed : undefined,
        };
      }

      // Use a transaction to ensure all operations are atomic
      await this.db.transaction(async (tx) => {
        // Soft delete the service time slots
        await tx
          .update(serviceTimeSlots)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(serviceTimeSlots.id, validIds));

        // Soft delete associated service relationships
        await tx
          .update(serviceTimeSlotServices)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(serviceTimeSlotServices.timeSlotId, validIds));

        // Soft delete associated staff relationships
        await tx
          .update(serviceTimeSlotStaff)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(serviceTimeSlotStaff.timeSlotId, validIds));
      });

      // Log bulk deletion activity
      await this.activityLogService.logDelete(
        validIds.join(','),
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        deleted: validIds.length,
        message: `Successfully deleted ${validIds.length} service time slots`,
        deletedIds: validIds,
        failed: failed.length > 0 ? failed : undefined,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete service time slots: ${error.message}`,
      );
    }
  }

  async bulkUpdate(
    userId: string,
    businessId: string | null,
    updates: Array<{ id: string; data: UpdateServiceTimeSlotDto }>,
  ): Promise<{
    updated: number;
    message: string;
    updatedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No updates provided');
      }

      const updateIds = updates.map((update) => update.id);

      // Find existing service time slots that belong to user's business
      const existingSlots = await this.db
        .select({
          id: serviceTimeSlots.id,
          slotName: serviceTimeSlots.slotName,
          locationId: serviceTimeSlots.locationId,
          businessId: locations.businessId,
        })
        .from(serviceTimeSlots)
        .innerJoin(locations, eq(serviceTimeSlots.locationId, locations.id))
        .where(
          and(
            inArray(serviceTimeSlots.id, updateIds),
            eq(serviceTimeSlots.isDeleted, false),
            eq(locations.isDeleted, false),
          ),
        );

      const validSlots = existingSlots.filter(
        (slot) => slot.businessId === businessId,
      );
      const validUpdates = updates.filter((update) =>
        validSlots.some((slot) => slot.id === update.id),
      );

      if (validUpdates.length === 0) {
        throw new BadRequestException(
          'No valid service time slots found to update',
        );
      }

      // Use a transaction to ensure all operations are atomic
      const updatedIds: string[] = [];

      await this.db.transaction(async (tx) => {
        for (const update of validUpdates) {
          const existingSlot = validSlots.find((slot) => slot.id === update.id);
          if (!existingSlot) continue;

          // Update service time slot
          await tx
            .update(serviceTimeSlots)
            .set({
              locationId: update.data.locationId,
              slotName: update.data.slotName,
              startTime: update.data.startTime,
              endTime: update.data.endTime,
              maxAppointments: update.data.maxAppointments,
              reservedCount: update.data.reservedCount,
              scheduleType: update.data.scheduleType,
              dayOfWeek: update.data.dayOfWeek,
              dayOfMonth: update.data.dayOfMonth,
              weekOfMonth: update.data.weekOfMonth,
              monthOfYear: update.data.monthOfYear,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(serviceTimeSlots.id, update.id));

          // Handle service associations if provided
          if (update.data.serviceIds !== undefined) {
            // Remove existing service associations
            await tx
              .update(serviceTimeSlotServices)
              .set({
                isDeleted: true,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(eq(serviceTimeSlotServices.timeSlotId, update.id));

            // Add new service associations
            if (update.data.serviceIds.length > 0) {
              const serviceAssociations = update.data.serviceIds.map(
                (serviceId) => ({
                  timeSlotId: update.id,
                  serviceId,
                  businessId,
                  createdBy: userId,
                }),
              );

              await tx
                .insert(serviceTimeSlotServices)
                .values(serviceAssociations);
            }
          }

          // Handle staff associations if provided
          if (update.data.staffIds !== undefined) {
            // Remove existing staff associations
            await tx
              .update(serviceTimeSlotStaff)
              .set({
                isDeleted: true,
                updatedBy: userId,
                updatedAt: new Date(),
              })
              .where(eq(serviceTimeSlotStaff.timeSlotId, update.id));

            // Add new staff associations
            if (update.data.staffIds.length > 0) {
              const staffAssociations = update.data.staffIds.map((staffId) => ({
                timeSlotId: update.id,
                staffId,
                businessId,
                createdBy: userId,
              }));

              await tx.insert(serviceTimeSlotStaff).values(staffAssociations);
            }
          }

          updatedIds.push(update.id);
        }
      });

      // Log bulk update activity
      await this.activityLogService.logUpdate(
        updatedIds.join(','),
        EntityType.SERVICE_TIME_SLOT,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        updated: updatedIds.length,
        message: `Successfully updated ${updatedIds.length} service time slots`,
        updatedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update service time slots: ${error.message}`,
      );
    }
  }
}
