// dto/create-inventory.dto.ts
import { IsNumber, IsOptional, Min, IsUUID } from 'class-validator';

export class CreateInventoryDto {
  @IsUUID()
  productId: string;

  @IsOptional()
  @IsUUID()
  variantId?: string;

  @IsUUID()
  locationId: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  initialQuantity?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  minStockLevel?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  maxStockLevel?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  reorderPoint?: number;
}
