// dto/inventory-transaction.dto.ts
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsDate,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum TransactionType {
  RECEIPT = 'receipt',
  ISSUE = 'issue',
  TRANSFER_IN = 'transfer_in',
  TRANSFER_OUT = 'transfer_out',
  ADJUSTMENT = 'adjustment',
  COUNT = 'count',
}

export class CreateInventoryTransactionDto {
  @IsString()
  inventoryId: string;

  @IsOptional()
  @IsString()
  serialId?: string;

  @IsOptional()
  @IsString()
  batchId?: string;

  @IsEnum(TransactionType)
  transactionType: string;

  @IsNumber()
  quantity: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  unitCost?: number;

  @IsString()
  referenceType: string;

  @IsString()
  referenceId: string;

  @IsOptional()
  @IsString()
  reasonCode?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class InventoryTransactionDto {
  transactionId: string;
  inventoryId: string;
  productId?: string;
  productName?: string;
  productCode?: string;
  variantId?: string;
  variantName?: string;
  locationId?: string;
  locationName?: string;
  serialId?: string;
  serialNumber?: string;
  batchId?: string;
  batchNumber?: string;
  transactionType: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  referenceType: string;
  referenceId: string;
  reasonCode?: string;
  notes?: string;
  createdBy: string;
  createdByName?: string;
  transactionDate: Date;
  createdAt: Date;
}

export class InventoryTransactionQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @IsOptional()
  @IsString()
  locationId?: string;

  @IsOptional()
  @IsString()
  productId?: string;

  @IsOptional()
  @IsString()
  variantId?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  @IsOptional()
  @IsEnum(TransactionType)
  transactionType?: string;

  @IsOptional()
  @IsString()
  referenceType?: string;
}
