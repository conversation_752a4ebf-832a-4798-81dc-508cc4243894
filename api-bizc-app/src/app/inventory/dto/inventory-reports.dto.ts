// dto/inventory-reports.dto.ts
import {
  IsOptional,
  IsString,
  IsDateString,
  IsNumber,
  IsArray,
  ValidateNested,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class InventoryMovementReportDto {
  @IsDateString()
  startDate: string;

  @IsDateString()
  endDate: string;

  @IsOptional()
  @IsString()
  productId?: string;

  @IsOptional()
  @IsString()
  locationId?: string;
}

export class InventoryValuationReportDto {
  @IsOptional()
  @IsString()
  locationId?: string;

  @IsDateString()
  asOfDate: string;
}

export class BatchInventoryUpdateDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchUpdateItemDto)
  updates: BatchUpdateItemDto[];
}

export class BatchUpdateItemDto {
  @IsString()
  inventoryId: string;

  @IsOptional()
  @IsNumber()
  @Min(0)
  minStockLevel?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  maxStockLevel?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  reorderPoint?: number;
}
