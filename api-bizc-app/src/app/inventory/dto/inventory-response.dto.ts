export class InventoryResponseDto {
  inventoryId: string;
  productId: string;
  productName: string;
  productCode: string;
  variantId: string | null;
  variantName: string | null;
  locationId: string;
  locationName: string;
  quantityOnHand: number;
  quantityReserved: number;
  quantityAvailable: number;
  minStockLevel: number | null;
  maxStockLevel: number | null;
  reorderPoint: number | null;
  lastCountDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export class InventoryListResponseDto {
  data: InventoryResponseDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class InventoryLevelResponseDto {
  inventoryId: string;
  locationId: string;
  locationName: string;
  quantityOnHand: number;
  quantityReserved: number;
  quantityAvailable: number;
  lastCountDate: Date | null;
}

export class DeleteInventoryResponseDto {
  success: boolean;
  message: string;
}
