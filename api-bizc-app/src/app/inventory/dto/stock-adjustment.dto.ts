// dto/stock-adjustment.dto.ts
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsDate,
  IsArray,
  ValidateNested,
  Min,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum AdjustmentType {
  PHYSICAL_COUNT = 'physical_count',
  DAMAGE = 'damage',
  EXPIRY = 'expiry',
  CORRECTION = 'correction',
  OTHER = 'other',
}

export enum AdjustmentStatus {
  DRAFT = 'draft',
  APPROVED = 'approved',
  POSTED = 'posted',
  CANCELLED = 'cancelled',
}

export class StockAdjustmentLineDto {
  @IsUUID()
  productId: string;

  @IsOptional()
  @IsUUID()
  variantId?: string;

  @IsNumber()
  @Min(0)
  newQuantity: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  unitCost?: number;

  @IsOptional()
  @IsUUID()
  serialId?: string;

  @IsOptional()
  @IsUUID()
  batchId?: string;

  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CreateStockAdjustmentDto {
  @IsUUID()
  locationId: string;

  @IsEnum(AdjustmentType)
  adjustmentType: string;

  @IsString()
  reason: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StockAdjustmentLineDto)
  lines: StockAdjustmentLineDto[];
}

export class UpdateStockAdjustmentDto {
  @IsOptional()
  @IsString()
  reason?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StockAdjustmentLineDto)
  lines?: StockAdjustmentLineDto[];
}

export class StockAdjustmentResponseDto {
  adjustmentId: string;
  adjustmentNumber: string;
  locationId: string;
  locationName: string;
  adjustmentDate: Date;
  adjustmentType: string;
  status: string;
  reason: string;
  notes?: string;
  createdBy: string;
  createdByName: string;
  approvedBy?: string;
  approvedByName?: string;
  approvedDate?: Date;
  postedDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  lines: StockAdjustmentLineResponseDto[];
  totalValue: number;
  totalItems: number;
}

export class StockAdjustmentLineResponseDto {
  adjustmentLineId: string;
  productId: string;
  productCode: string;
  productName: string;
  variantId?: string;
  variantName?: string;
  currentQuantity: number;
  countedQuantity: number;
  quantityDifference: number;
  unitCost: number;
  totalValue: number;
  serialId?: string;
  serialNumber?: string;
  batchId?: string;
  batchNumber?: string;
  reason: string;
  notes?: string;
}

export class StockAdjustmentListResponseDto {
  data: StockAdjustmentSummaryDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class StockAdjustmentSummaryDto {
  adjustmentId: string;
  adjustmentNumber: string;
  locationId: string;
  locationName: string;
  adjustmentDate: Date;
  adjustmentType: string;
  status: string;
  reason: string;
  lineCount: number;
  totalValue: number;
  createdBy: string;
  createdByName: string;
  createdAt: Date;
}

export class StockAdjustmentQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @IsOptional()
  @IsString()
  locationId?: string;

  @IsOptional()
  @IsEnum(AdjustmentStatus)
  status?: string;

  @IsOptional()
  @IsEnum(AdjustmentType)
  adjustmentType?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;
}
