// dto/stock-transfer.dto.ts
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsDate,
  IsArray,
  ValidateNested,
  Min,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum TransferStatus {
  DRAFT = 'draft',
  APPROVED = 'approved',
  IN_TRANSIT = 'in_transit',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export class StockTransferLineDto {
  @IsString()
  productId: string;

  @IsOptional()
  @IsString()
  variantId?: string;

  @IsNumber()
  @Min(0.01)
  quantity: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  unitCost?: number;

  @IsOptional()
  @IsString()
  batchId?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  serialIds?: string[];

  @IsOptional()
  @IsString()
  notes?: string;
}

export class CreateStockTransferDto {
  @IsString()
  fromLocationId: string;

  @IsString()
  toLocationId: string;

  @IsString()
  transferReason: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StockTransferLineDto)
  @ArrayMinSize(1)
  lines: StockTransferLineDto[];
}

export class UpdateStockTransferDto {
  @IsOptional()
  @IsString()
  transferReason?: string;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StockTransferLineDto)
  lines?: StockTransferLineDto[];
}

export class UpdateStockTransferStatusDto {
  @IsEnum(TransferStatus)
  status: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class StockTransferResponseDto {
  transferId: string;
  transferNumber: string;
  fromLocationId: string;
  fromLocationName: string;
  toLocationId: string;
  toLocationName: string;
  transferDate: Date;
  status: string;
  transferReason: string;
  notes?: string;
  requestedBy: string;
  requestedByName: string;
  approvedBy?: string;
  approvedByName?: string;
  shippedBy?: string;
  shippedByName?: string;
  shippedDate?: Date;
  receivedBy?: string;
  receivedByName?: string;
  receivedDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  lines: StockTransferLineResponseDto[];
  totalItems: number;
  totalQuantity: number;
  totalValue: number;
}

export class StockTransferLineResponseDto {
  transferLineId: string;
  productId: string;
  productCode: string;
  productName: string;
  variantId?: string;
  variantName?: string;
  quantity: number;
  fromInventoryId: string;
  toInventoryId?: string;
  batchId?: string;
  batchNumber?: string;
  serialIds: string[];
  serialNumbers: string[];
  unitCost: number;
  totalValue: number;
  notes?: string;
}

export class StockTransferListResponseDto {
  data: StockTransferSummaryDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class StockTransferSummaryDto {
  transferId: string;
  transferNumber: string;
  fromLocationId: string;
  fromLocationName: string;
  toLocationId: string;
  toLocationName: string;
  transferDate: Date;
  status: string;
  transferReason: string;
  lineCount: number;
  totalQuantity: number;
  requestedBy: string;
  requestedByName: string;
  createdAt: Date;
}

export class StockTransferQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @IsOptional()
  @IsString()
  fromLocationId?: string;

  @IsOptional()
  @IsString()
  toLocationId?: string;

  @IsOptional()
  @IsEnum(TransferStatus)
  status?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;
}

export class CancelStockTransferDto {
  @IsString()
  reason: string;
}

export class ShipStockTransferDto {
  @IsOptional()
  @IsString()
  shippingNotes?: string;

  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @IsOptional()
  @IsString()
  carrier?: string;
}

export class ReceiveStockTransferDto {
  @IsOptional()
  @IsString()
  receivingNotes?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StockTransferReceivingLineDto)
  receivedLines?: StockTransferReceivingLineDto[];
}

export class StockTransferReceivingLineDto {
  @IsString()
  transferLineId: string;

  @IsNumber()
  @Min(0)
  receivedQuantity: number;

  @IsOptional()
  @IsString()
  condition?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class StockTransferLineSerialDto {
  @IsString()
  transferLineId: string;

  @IsString()
  serialId: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  createdAt?: Date;
}
