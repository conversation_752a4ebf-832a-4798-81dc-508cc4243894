// dto/inventory-reservation.dto.ts
import {
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsDate,
  IsBoolean,
  Min,
  IsDateString,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum ReservationType {
  QUOTATION = 'quotation',
  SALES_ORDER = 'sales_order',
  TRANSFER = 'transfer',
  HOLD = 'hold',
}

export enum ReservationStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled',
  FULFILLED = 'fulfilled',
}

export class CreateInventoryReservationDto {
  @IsNumber()
  productId: number;

  @IsOptional()
  @IsNumber()
  variantId?: number;

  @IsNumber()
  locationId: number;

  @IsEnum(ReservationType)
  reservationType: string;

  @IsNumber()
  referenceId: number;

  @IsNumber()
  @Min(0.01)
  quantityReserved: number;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateInventoryReservationDto {
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  quantityReserved?: number;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class InventoryReservationResponseDto {
  reservationId: number;
  productId: number;
  productCode: string;
  productName: string;
  variantId?: number;
  variantName?: string;
  locationId: number;
  locationName: string;
  reservationType: string;
  referenceId: number;
  referenceNumber: string;
  referenceName: string;
  quantityReserved: number;
  expiryDate?: Date;
  status: string;
  notes?: string;
  createdBy: number;
  createdByName: string;
  createdAt: Date;
  updatedAt: Date;
}

export class InventoryReservationSummaryDto {
  reservationId: number;
  productCode: string;
  productName: string;
  variantName?: string;
  locationName: string;
  reservationType: string;
  referenceNumber: string;
  quantityReserved: number;
  expiryDate?: Date;
  status: string;
  createdAt: Date;
}

export class InventoryReservationListResponseDto {
  data: InventoryReservationSummaryDto[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class InventoryReservationQueryDto {
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 20;

  @IsOptional()
  @IsString()
  productId?: string;

  @IsOptional()
  @IsString()
  locationId?: string;

  @IsOptional()
  @IsEnum(ReservationStatus)
  status?: string;

  @IsOptional()
  @IsEnum(ReservationType)
  reservationType?: string;

  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  includeExpired?: boolean = false;
}

export class ExtendReservationDto {
  @IsDateString()
  newExpiryDate: string;
}

export class BatchReservationDto {
  @IsNumber()
  referenceId: number;

  @IsEnum(ReservationType)
  reservationType: string;

  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @IsOptional()
  @IsString()
  notes?: string;
}

export class ReservationLineDto {
  @IsNumber()
  productId: number;

  @IsOptional()
  @IsNumber()
  variantId?: number;

  @IsNumber()
  locationId: number;

  @IsNumber()
  @Min(0.01)
  quantity: number;
}

export class CreateBatchReservationDto extends BatchReservationDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReservationLineDto)
  lines: ReservationLineDto[];
}
