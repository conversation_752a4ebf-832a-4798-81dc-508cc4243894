// inventory.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { and, eq, gte, lte, sql, desc } from 'drizzle-orm';
import {
  inventory,
  inventoryTransactions,
} from '../drizzle/schema/inventory.schema';
import { products, productVariants } from '../drizzle/schema/products.schema';
import { productLocations } from '../drizzle/schema/inventory.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { InventoryTransactionsService } from './services/inventory-transactions.service';
import { StockAdjustmentsService } from './services/stock-adjustments.service';
import { StockTransfersService } from './services/stock-transfers.service';
import { InventoryReservationsService } from './services/inventory-reservations.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import {
  CreateInventoryReservationDto,
  InventoryReservationResponseDto,
} from './dto/inventory-reservation.dto';
import { InventoryTransactionDto } from './dto/inventory-transaction.dto';
import { InventoryQueryDto } from './dto/inventory-query.dto';
import {
  BatchInventoryUpdateDto,
  InventoryMovementReportDto,
} from './dto/inventory-reports.dto';
import { InventoryResponseDto } from './dto/inventory-response.dto';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import {
  CreateStockTransferDto,
  StockTransferResponseDto,
  UpdateStockTransferStatusDto,
} from './dto/stock-transfer.dto';
import {
  CreateStockAdjustmentDto,
  StockAdjustmentResponseDto,
} from './dto/stock-adjustment.dto';
import { DRIZZLE } from '@app/drizzle/drizzle.module';
import { DrizzleDB } from '@app/drizzle/types/drizzle';

@Injectable()
export class InventoryService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly transactionsService: InventoryTransactionsService,
    private readonly adjustmentsService: StockAdjustmentsService,
    private readonly transfersService: StockTransfersService,
    private readonly reservationsService: InventoryReservationsService,
  ) {}

  async findAll(
    _userId: string,
    _businessId: string,
    query: InventoryQueryDto,
  ): Promise<{
    data: InventoryResponseDto[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    const {
      page = 1,
      limit = 20,
      locationId,
      productId,
      variantId,
      lowStock,
    } = query;
    const offset = (page - 1) * limit;

    const conditions = [];

    if (locationId) {
      conditions.push(eq(inventory.locationId, locationId));
    }

    if (productId) {
      conditions.push(eq(inventory.productId, productId));
    }

    if (variantId) {
      conditions.push(eq(inventory.variantId, variantId));
    }

    // Get inventory with product and location details
    const inventoryItems = await this.db
      .select({
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .where(and(...conditions))
      .limit(limit)
      .offset(offset);

    // Apply low stock filter if needed
    const filteredItems = lowStock
      ? inventoryItems.filter((item) => {
          const reorderPoint = item.inventory.reorderPoint || 0;
          return item.inventory.quantityAvailable <= reorderPoint;
        })
      : inventoryItems;

    // Get total count
    const [{ count }] = await this.db
      .select({ count: sql`count(*)` })
      .from(inventory)
      .where(and(...conditions));

    return {
      data: filteredItems.map((item) => this.mapToInventoryResponse(item)),
      pagination: {
        page,
        limit,
        total: Number(count),
        totalPages: Math.ceil(Number(count) / limit),
      },
    };
  }

  async findOne(
    _userId: string,
    _businessId: string,
    id: string,
  ): Promise<InventoryResponseDto> {
    const [inventoryItem] = await this.db
      .select({
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .where(eq(inventory.id, id));

    if (!inventoryItem) {
      throw new NotFoundException('Inventory record not found');
    }

    return this.mapToInventoryResponse(inventoryItem);
  }

  async findByProduct(
    _userId: string,
    _businessId: string,
    productId: string,
    variantId: string | undefined,
  ): Promise<
    {
      inventoryId: string;
      locationId: string;
      locationName: string;
      quantityOnHand: number;
      quantityReserved: number;
      quantityAvailable: number;
      lastCountDate: Date | null;
    }[]
  > {
    const conditions = [eq(inventory.productId, productId)];

    if (variantId) {
      conditions.push(eq(inventory.variantId, variantId));
    }

    const inventoryLevels = await this.db
      .select({
        inventory: inventory,
        location: locations,
      })
      .from(inventory)
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .where(and(...conditions));

    return inventoryLevels.map((level) => ({
      inventoryId: level.inventory.id,
      locationId: level.inventory.locationId,
      locationName: level.location?.name || '',
      quantityOnHand: Number(level.inventory.quantityOnHand),
      quantityReserved: Number(level.inventory.quantityReserved),
      quantityAvailable: Number(level.inventory.quantityAvailable),
      lastCountDate: level.inventory.lastCountDate,
    }));
  }

  async findByLocation(
    userId: string,
    businessId: string,
    locationId: string,
    query: InventoryQueryDto,
  ): Promise<{
    data: InventoryResponseDto[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    return this.findAll(userId, businessId, { ...query, locationId });
  }

  async create(
    userId: string,
    businessId: string,
    createInventoryDto: CreateInventoryDto,
    _metadata?: ActivityMetadata,
  ): Promise<InventoryResponseDto> {
    const { productId, variantId, locationId, initialQuantity } =
      createInventoryDto;

    // Check if inventory record already exists
    const [existing] = await this.db
      .select()
      .from(inventory)
      .where(
        and(
          eq(inventory.productId, productId),
          eq(inventory.locationId, locationId),
          variantId
            ? eq(inventory.variantId, variantId)
            : sql`${inventory.variantId} IS NULL`,
        ),
      );

    if (existing) {
      throw new BadRequestException(
        'Inventory record already exists for this product/location combination',
      );
    }

    // Create inventory record
    const [newInventory] = await this.db
      .insert(inventory)
      .values({
        businessId,
        productId,
        variantId: variantId || null,
        locationId,
        quantityOnHand: String(initialQuantity || 0),
        quantityReserved: '0',
        quantityAvailable: String(initialQuantity || 0),
        lastCountDate: new Date(),
        createdBy: userId,
      })
      .returning();

    // Create initial transaction if quantity provided
    if (initialQuantity && initialQuantity > 0) {
      await this.transactionsService.create(
        userId,
        businessId,
        {
          inventoryId: newInventory.id,
          transactionType: 'adjustment',
          quantity: initialQuantity,
          unitCost: 0,
          referenceType: 'initial_setup',
          referenceId: newInventory.id,
          reasonCode: 'INITIAL_STOCK',
          notes: 'Initial inventory setup',
        },
        _metadata,
      );
    }

    return this.findOne(userId, businessId, newInventory.id);
  }

  async update(
    userId: string,
    businessId: string,
    id: string,
    updateInventoryDto: UpdateInventoryDto,
  ): Promise<InventoryResponseDto> {
    const { minStockLevel, maxStockLevel, reorderPoint } = updateInventoryDto;

    const [updated] = await this.db
      .update(inventory)
      .set({
        minStockLevel: minStockLevel ? String(minStockLevel) : undefined,
        maxStockLevel: maxStockLevel ? String(maxStockLevel) : undefined,
        reorderPoint: reorderPoint ? String(reorderPoint) : undefined,
        updatedAt: new Date(),
      })
      .where(eq(inventory.id, id))
      .returning();

    if (!updated) {
      throw new NotFoundException('Inventory record not found');
    }

    return this.findOne(userId, businessId, id);
  }

  async batchUpdate(
    userId: string,
    businessId: string,
    batchUpdateDto: BatchInventoryUpdateDto,
  ): Promise<InventoryResponseDto[]> {
    const results = [];

    for (const update of batchUpdateDto.updates) {
      const result = await this.update(
        userId,
        businessId,
        update.inventoryId,
        update,
      );
      results.push(result);
    }

    return results;
  }

  async remove(
    _userId: string,
    _businessId: string,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    // Check if inventory has any quantity
    const [inventoryRecord] = await this.db
      .select()
      .from(inventory)
      .where(eq(inventory.id, id));

    if (!inventoryRecord) {
      throw new NotFoundException('Inventory record not found');
    }

    if (Number(inventoryRecord.quantityOnHand) > 0) {
      throw new BadRequestException(
        'Cannot delete inventory with quantity on hand',
      );
    }

    // Delete inventory record
    await this.db.delete(inventory).where(eq(inventory.id, id));

    return {
      success: true,
      message: 'Inventory record deleted successfully',
    };
  }

  // Stock Adjustments
  async createStockAdjustment(
    userId: string,
    businessId: string,
    createDto: CreateStockAdjustmentDto,
    metadata: ActivityMetadata,
  ): Promise<StockAdjustmentResponseDto> {
    return this.adjustmentsService.create(
      userId,
      businessId,
      createDto,
      metadata,
    );
  }

  async getStockAdjustment(
    userId: string,
    businessId: string,
    id: string,
  ): Promise<StockAdjustmentResponseDto> {
    return this.adjustmentsService.findOne(userId, businessId, id);
  }

  async approveStockAdjustment(
    userId: string,
    businessId: string,
    id: string,
    metadata: ActivityMetadata,
  ): Promise<StockAdjustmentResponseDto> {
    return this.adjustmentsService.approve(userId, businessId, id, metadata);
  }

  async postStockAdjustment(
    userId: string,
    businessId: string,
    id: string,
    metadata: ActivityMetadata,
  ): Promise<StockAdjustmentResponseDto> {
    return this.adjustmentsService.post(userId, businessId, id, metadata);
  }

  // Stock Transfers
  async createStockTransfer(
    userId: string,
    businessId: string,
    createDto: CreateStockTransferDto,
    metadata: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    return this.transfersService.create(
      userId,
      businessId,
      createDto,
      metadata,
    );
  }

  async getStockTransfer(
    userId: string,
    businessId: string,
    id: string,
  ): Promise<StockTransferResponseDto> {
    return this.transfersService.findOne(userId, businessId, id);
  }

  async updateStockTransferStatus(
    userId: string,
    businessId: string,
    id: string,
    updateStatusDto: UpdateStockTransferStatusDto,
    metadata: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    return this.transfersService.updateStatus(
      userId,
      businessId,
      id,
      updateStatusDto,
      metadata,
    );
  }

  async shipStockTransfer(
    userId: string,
    businessId: string,
    id: string,
    metadata: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    return this.transfersService.ship(userId, businessId, id, metadata);
  }

  async receiveStockTransfer(
    userId: string,
    businessId: string,
    id: string,
    metadata: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    return this.transfersService.receive(userId, businessId, id, metadata);
  }

  // Inventory Reservations
  async createReservation(
    userId: string,
    businessId: string,
    createDto: CreateInventoryReservationDto,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    return this.reservationsService.create(
      userId,
      businessId,
      createDto,
      metadata,
    );
  }

  async getReservation(
    userId: string,
    businessId: string,
    id: string,
  ): Promise<InventoryReservationResponseDto> {
    return this.reservationsService.findOne(userId, businessId, id);
  }

  async cancelReservation(
    userId: string,
    businessId: string,
    id: string,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    return this.reservationsService.cancel(userId, businessId, id, metadata);
  }

  async fulfillReservation(
    userId: string,
    businessId: string,
    id: string,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    return this.reservationsService.fulfill(userId, businessId, id, metadata);
  }

  // Transactions
  async getTransactions(
    userId: string,
    businessId: string,
    query: InventoryQueryDto,
  ): Promise<InventoryTransactionDto[]> {
    return this.transactionsService.findAll(userId, businessId, query);
  }

  async createTransaction(
    userId: string,
    businessId: string,
    createDto: InventoryTransactionDto,
    metadata?: ActivityMetadata,
  ): Promise<InventoryTransactionDto> {
    return this.transactionsService.create(
      userId,
      businessId,
      createDto,
      metadata,
    );
  }

  // Reports
  async getMovementReport(
    _userId: string,
    _businessId: string,
    query: InventoryMovementReportDto,
  ): Promise<any> {
    const { startDate, endDate, productId, locationId } = query;

    const conditions = [
      gte(inventoryTransactions.transactionDate, new Date(startDate)),
      lte(inventoryTransactions.transactionDate, new Date(endDate)),
    ];

    if (productId) {
      conditions.push(eq(inventory.productId, productId));
    }

    if (locationId) {
      conditions.push(eq(inventory.locationId, locationId));
    }

    const movements = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        location: locations,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .where(and(...conditions))
      .orderBy(desc(inventoryTransactions.transactionDate));

    return {
      startDate,
      endDate,
      movements: movements.map((m) => ({
        transactionId: m.transaction.id,
        transactionDate: m.transaction.transactionDate,
        transactionType: m.transaction.transactionType,
        productName: m.product?.name,
        locationName: m.location?.name,
        quantity: m.transaction.quantity,
        unitCost: m.transaction.unitCost,
        totalValue:
          Number(m.transaction.quantity) * Number(m.transaction.unitCost || 0),
        referenceType: m.transaction.referenceType,
        referenceId: m.transaction.referenceId,
        notes: m.transaction.notes,
      })),
    };
  }

  async getValuationReport(
    _userId: string,
    _businessId: string,
    locationId: string,
    asOfDate: string,
  ): Promise<any> {
    const conditions = [];

    if (locationId) {
      conditions.push(eq(inventory.locationId, locationId));
    }

    const inventoryItems = await this.db
      .select({
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .where(and(...conditions));

    const valuation = inventoryItems.map((item) => {
      const cost = Number(item.product?.standardCost || 0);
      const value = Number(item.inventory.quantityOnHand) * cost;

      return {
        productId: item.product?.id,
        productCode: item.product?.shortCode,
        productName: item.product?.name,
        variantName: item.variant?.name,
        locationName: item.location?.name,
        quantityOnHand: item.inventory.quantityOnHand,
        unitCost: cost,
        totalValue: value,
      };
    });

    const totalValue = valuation.reduce(
      (sum, item) => sum + item.totalValue,
      0,
    );

    return {
      asOfDate,
      locationId,
      items: valuation,
      summary: {
        totalItems: valuation.length,
        totalValue,
      },
    };
  }

  async getReorderReport(
    _userId: string,
    _businessId: string,
    locationId: string,
  ): Promise<any> {
    const conditions = [
      sql`${inventory.quantityAvailable} <= ${inventory.reorderPoint}`,
    ];

    if (locationId) {
      conditions.push(eq(inventory.locationId, locationId));
    }

    const reorderItems = await this.db
      .select({
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        productLocation: productLocations,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        productLocations,
        and(
          eq(productLocations.productId, inventory.productId),
          eq(productLocations.locationId, inventory.locationId),
        ),
      )
      .where(and(...conditions));

    return {
      locationId,
      items: reorderItems.map((item) => ({
        productId: item.product?.id,
        productCode: item.product?.shortCode,
        productName: item.product?.name,
        variantName: item.variant?.name,
        locationName: item.location?.name,
        quantityOnHand: item.inventory.quantityOnHand,
        quantityAvailable: item.inventory.quantityAvailable,
        reorderPoint: Number(item.inventory.reorderPoint || 0),
        suggestedOrderQuantity: Math.max(
          0,
          Number(item.inventory.maxStockLevel || 0) -
            Number(item.inventory.quantityAvailable),
        ),
        leadTimeDays: item.product?.leadTimeDays || 0,
      })),
    };
  }

  // Physical Count
  async startPhysicalCount(
    userId: string,
    _businessId: string,
    startCountDto: { locationId: string; productIds?: string[] },
  ): Promise<any> {
    // Implementation for starting physical count
    // This would create a new count session and return count sheets
    return {
      countId: 'generated-count-id',
      locationId: startCountDto.locationId,
      startedBy: userId,
      startedAt: new Date(),
      status: 'in_progress',
    };
  }

  async recordCountItem(
    userId: string,
    _businessId: string,
    countId: string,
    countItemDto: { productId: string; variantId?: string; quantity: number },
  ): Promise<any> {
    // Implementation for recording count items
    return {
      countId,
      ...countItemDto,
      recordedBy: userId,
      recordedAt: new Date(),
    };
  }

  async completePhysicalCount(
    userId: string,
    _businessId: string,
    countId: string,
  ): Promise<any> {
    // Implementation for completing physical count
    // This would create adjustment transactions for differences
    return {
      countId,
      completedBy: userId,
      completedAt: new Date(),
      status: 'completed',
      adjustmentsCreated: true,
    };
  }

  // Helper methods
  private mapToInventoryResponse(data: any): InventoryResponseDto {
    return {
      inventoryId: data.inventory.id,
      productId: data.inventory.productId,
      productName: data.product?.name || '',
      productCode: data.product?.shortCode || '',
      variantId: data.inventory.variantId || null,
      variantName: data.variant?.name || null,
      locationId: data.inventory.locationId,
      locationName: data.location?.name || '',
      quantityOnHand: data.inventory.quantityOnHand,
      quantityReserved: data.inventory.quantityReserved,
      quantityAvailable: data.inventory.quantityAvailable,
      minStockLevel: data.inventory.minStockLevel || null,
      maxStockLevel: data.inventory.maxStockLevel || null,
      reorderPoint: data.inventory.reorderPoint || null,
      lastCountDate: data.inventory.lastCountDate,
      createdAt: data.inventory.createdAt,
      updatedAt: data.inventory.updatedAt,
    };
  }
}
