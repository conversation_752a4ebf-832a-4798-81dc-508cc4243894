// inventory.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Patch,
  Body,
  Param,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { InventoryService } from './inventory.service';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateInventoryDto } from './dto/create-inventory.dto';
import { UpdateInventoryDto } from './dto/update-inventory.dto';
import {
  InventoryResponseDto,
  InventoryListResponseDto,
  InventoryLevelResponseDto,
  DeleteInventoryResponseDto,
} from './dto/inventory-response.dto';
import { InventoryTransactionDto } from './dto/inventory-transaction.dto';
import {
  CreateStockAdjustmentDto,
  StockAdjustmentResponseDto,
} from './dto/stock-adjustment.dto';
import {
  CreateStockTransferDto,
  StockTransferResponseDto,
  UpdateStockTransferStatusDto,
} from './dto/stock-transfer.dto';
import {
  CreateInventoryReservationDto,
  InventoryReservationResponseDto,
} from './dto/inventory-reservation.dto';
import { InventoryQueryDto } from './dto/inventory-query.dto';
import {
  BatchInventoryUpdateDto,
  InventoryMovementReportDto,
} from './dto/inventory-reports.dto';

@Controller('inventory')
@UseGuards(JwtAuthGuard)
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  // Get inventory levels
  @Get()
  findAll(
    @Request() req,
    @Query() query: InventoryQueryDto,
  ): Promise<InventoryListResponseDto> {
    return this.inventoryService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      query,
    );
  }

  // Get inventory by ID
  @Get(':id')
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<InventoryResponseDto> {
    return this.inventoryService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  // Get inventory levels by product
  @Get('product/:productId')
  findByProduct(
    @Request() req,
    @Param('productId') productId: string,
    @Query('variantId') variantId?: string,
  ): Promise<InventoryLevelResponseDto[]> {
    return this.inventoryService.findByProduct(
      req.user.id,
      req.user.activeBusinessId,
      productId,
      variantId,
    );
  }

  // Get inventory levels by location
  @Get('location/:locationId')
  findByLocation(
    @Request() req,
    @Param('locationId') locationId: string,
    @Query() query: InventoryQueryDto,
  ): Promise<InventoryListResponseDto> {
    return this.inventoryService.findByLocation(
      req.user.id,
      req.user.activeBusinessId,
      locationId,
      query,
    );
  }

  // Create inventory record
  @Post()
  create(
    @Request() req,
    @Body() createInventoryDto: CreateInventoryDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<InventoryResponseDto> {
    return this.inventoryService.create(
      req.user.id,
      req.user.activeBusinessId,
      createInventoryDto,
      metadata,
    );
  }

  // Update inventory levels
  @Put(':id')
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateInventoryDto: UpdateInventoryDto,
  ): Promise<InventoryResponseDto> {
    return this.inventoryService.update(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateInventoryDto,
    );
  }

  // Batch update inventory levels
  @Post('batch-update')
  batchUpdate(
    @Request() req,
    @Body() batchUpdateDto: BatchInventoryUpdateDto,
  ): Promise<InventoryResponseDto[]> {
    return this.inventoryService.batchUpdate(
      req.user.id,
      req.user.activeBusinessId,
      batchUpdateDto,
    );
  }

  // Delete inventory record
  @Delete(':id')
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteInventoryResponseDto> {
    return this.inventoryService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  // Stock Adjustments
  @Post('adjustments')
  createAdjustment(
    @Request() req,
    @Body() createStockAdjustmentDto: CreateStockAdjustmentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    return this.inventoryService.createStockAdjustment(
      req.user.id,
      req.user.activeBusinessId,
      createStockAdjustmentDto,
      metadata,
    );
  }

  @Get('adjustments/:id')
  getAdjustment(
    @Request() req,
    @Param('id') id: string,
  ): Promise<StockAdjustmentResponseDto> {
    return this.inventoryService.getStockAdjustment(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('adjustments/:id/approve')
  approveAdjustment(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    return this.inventoryService.approveStockAdjustment(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('adjustments/:id/post')
  postAdjustment(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    return this.inventoryService.postStockAdjustment(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  // Stock Transfers
  @Post('transfers')
  createTransfer(
    @Request() req,
    @Body() createStockTransferDto: CreateStockTransferDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockTransferResponseDto> {
    return this.inventoryService.createStockTransfer(
      req.user.id,
      req.user.activeBusinessId,
      createStockTransferDto,
      metadata,
    );
  }

  @Get('transfers/:id')
  getTransfer(
    @Request() req,
    @Param('id') id: string,
  ): Promise<StockTransferResponseDto> {
    return this.inventoryService.getStockTransfer(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('transfers/:id/status')
  updateTransferStatus(
    @Request() req,
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateStockTransferStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockTransferResponseDto> {
    return this.inventoryService.updateStockTransferStatus(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateStatusDto,
      metadata,
    );
  }

  @Patch('transfers/:id/ship')
  shipTransfer(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockTransferResponseDto> {
    return this.inventoryService.shipStockTransfer(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('transfers/:id/receive')
  receiveTransfer(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<StockTransferResponseDto> {
    return this.inventoryService.receiveStockTransfer(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  // Inventory Reservations
  @Post('reservations')
  createReservation(
    @Request() req,
    @Body() createReservationDto: CreateInventoryReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<InventoryReservationResponseDto> {
    return this.inventoryService.createReservation(
      req.user.id,
      req.user.activeBusinessId,
      createReservationDto,
      metadata,
    );
  }

  @Get('reservations/:id')
  getReservation(
    @Request() req,
    @Param('id') id: string,
  ): Promise<InventoryReservationResponseDto> {
    return this.inventoryService.getReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch('reservations/:id/cancel')
  cancelReservation(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<InventoryReservationResponseDto> {
    return this.inventoryService.cancelReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('reservations/:id/fulfill')
  fulfillReservation(
    @Request() req,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<InventoryReservationResponseDto> {
    return this.inventoryService.fulfillReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  // Inventory Transactions
  @Get('transactions')
  getTransactions(
    @Request() req,
    @Query() query: InventoryQueryDto,
  ): Promise<InventoryTransactionDto[]> {
    return this.inventoryService.getTransactions(
      req.user.id,
      req.user.activeBusinessId,
      query,
    );
  }

  @Post('transactions')
  createTransaction(
    @Request() req,
    @Body() createTransactionDto: InventoryTransactionDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<InventoryTransactionDto> {
    return this.inventoryService.createTransaction(
      req.user.id,
      req.user.activeBusinessId,
      createTransactionDto,
      metadata,
    );
  }

  // Reports
  @Get('reports/movement')
  getMovementReport(
    @Request() req,
    @Query() query: InventoryMovementReportDto,
  ): Promise<any> {
    return this.inventoryService.getMovementReport(
      req.user.id,
      req.user.activeBusinessId,
      query,
    );
  }

  @Get('reports/valuation')
  getValuationReport(
    @Request() req,
    @Query('locationId') locationId: string,
    @Query('asOfDate') asOfDate: string,
  ): Promise<any> {
    return this.inventoryService.getValuationReport(
      req.user.id,
      req.user.activeBusinessId,
      locationId,
      asOfDate,
    );
  }

  @Get('reports/reorder')
  getReorderReport(
    @Request() req,
    @Query('locationId') locationId: string,
  ): Promise<any> {
    return this.inventoryService.getReorderReport(
      req.user.id,
      req.user.activeBusinessId,
      locationId,
    );
  }

  // Physical Count
  @Post('physical-count/start')
  startPhysicalCount(
    @Request() req,
    @Body() startCountDto: { locationId: string; productIds?: string[] },
  ): Promise<any> {
    return this.inventoryService.startPhysicalCount(
      req.user.id,
      req.user.activeBusinessId,
      startCountDto,
    );
  }

  @Post('physical-count/:countId/items')
  recordCountItem(
    @Request() req,
    @Param('countId') countId: string,
    @Body()
    countItemDto: { productId: string; variantId?: string; quantity: number },
  ): Promise<any> {
    return this.inventoryService.recordCountItem(
      req.user.id,
      req.user.activeBusinessId,
      countId,
      countItemDto,
    );
  }

  @Patch('physical-count/:countId/complete')
  completePhysicalCount(
    @Request() req,
    @Param('countId') countId: string,
  ): Promise<any> {
    return this.inventoryService.completePhysicalCount(
      req.user.id,
      req.user.activeBusinessId,
      countId,
    );
  }
}
