import {
  Injectable,
  Inject,
  BadRequestException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { and, eq, sql, desc, inArray } from 'drizzle-orm';
import { DRIZZLE } from '../../drizzle/drizzle.module';
import { DrizzleDB } from '../../drizzle/types/drizzle';
import {
  stockTransfers,
  stockTransferLines,
  inventory,
  serialNumbers,
  batchNumbers,
} from '../../drizzle/schema/inventory.schema';
import {
  products,
  productVariants,
} from '../../drizzle/schema/products.schema';
import { staffMembers } from '../../drizzle/schema/staff.schema';
import { InventoryTransactionsService } from './inventory-transactions.service';
import { ActivityLogService } from '../../activity-log/activity-log.service';
import { ActivityMetadata } from '../../shared/types/activity-metadata.type';
import { EntityType, ActivitySource } from '../../shared/types/activity.enum';
import {
  CreateStockTransferDto,
  StockTransferListResponseDto,
  StockTransferQueryDto,
  StockTransferResponseDto,
  UpdateStockTransferDto,
  UpdateStockTransferStatusDto,
} from '../dto/stock-transfer.dto';
import { locations } from '../../drizzle/schema/locations.schema';

@Injectable()
export class StockTransfersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly transactionsService: InventoryTransactionsService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createDto: CreateStockTransferDto,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const { fromLocationId, toLocationId, transferReason, notes, lines } =
      createDto;

    // Validate locations
    if (fromLocationId === toLocationId) {
      throw new BadRequestException('Cannot transfer to the same location');
    }

    const [fromLocation, toLocation] = await Promise.all([
      this.db.select().from(locations).where(eq(locations.id, fromLocationId)),
      this.db.select().from(locations).where(eq(locations.id, toLocationId)),
    ]);

    if (!fromLocation[0]) {
      throw new NotFoundException('Source location not found');
    }

    if (!toLocation[0]) {
      throw new NotFoundException('Destination location not found');
    }

    return await this.db.transaction(async (tx) => {
      // Generate transfer number
      const transferNumber = await this.generateTransferNumber(tx);

      // Create transfer header
      const [transfer] = await tx
        .insert(stockTransfers)
        .values({
          transferNumber,
          fromLocationId,
          toLocationId,
          transferDate: new Date(),
          status: 'draft',
          transferReason,
          notes,
          requestedBy: userId,
          createdBy: userId,
          businessId,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      // Create transfer lines
      if (lines && lines.length > 0) {
        await Promise.all(
          lines.map(async (line, index) => {
            // Validate source inventory
            const [sourceInventory] = await tx
              .select()
              .from(inventory)
              .where(
                and(
                  eq(inventory.productId, line.productId),
                  eq(inventory.locationId, fromLocationId),
                  line.variantId
                    ? eq(inventory.variantId, line.variantId)
                    : sql`${inventory.variantId} IS NULL`,
                ),
              );

            if (!sourceInventory) {
              throw new NotFoundException(
                `Product ${line.productId} not found in source location`,
              );
            }

            if (Number(sourceInventory.quantityAvailable) < line.quantity) {
              throw new BadRequestException(
                `Insufficient quantity available for product ${line.productId}. Available: ${sourceInventory.quantityAvailable}, Requested: ${line.quantity}`,
              );
            }

            // Validate serial numbers if provided
            if (line.serialIds && line.serialIds.length > 0) {
              const serials = await tx
                .select()
                .from(serialNumbers)
                .where(inArray(serialNumbers.id, line.serialIds));

              if (serials.length !== line.serialIds.length) {
                throw new NotFoundException(
                  'One or more serial numbers not found',
                );
              }

              // Validate serials are at source location
              const invalidSerials = serials.filter(
                (s) =>
                  s.currentLocationId !== fromLocationId ||
                  s.status !== 'available',
              );

              if (invalidSerials.length > 0) {
                throw new BadRequestException(
                  'One or more serial numbers are not available at source location',
                );
              }
            }

            // Validate batch if provided
            if (line.batchId) {
              const [batch] = await tx
                .select()
                .from(batchNumbers)
                .where(eq(batchNumbers.id, line.batchId));

              if (!batch) {
                throw new NotFoundException('Batch number not found');
              }

              if (batch.locationId !== fromLocationId) {
                throw new BadRequestException(
                  'Batch is not at source location',
                );
              }

              if (Number(batch.availableQuantity) < line.quantity) {
                throw new BadRequestException(
                  `Insufficient batch quantity. Available: ${batch.availableQuantity}, Requested: ${line.quantity}`,
                );
              }
            }

            // Create transfer line
            const [transferLine] = await tx
              .insert(stockTransferLines)
              .values({
                transferId: transfer.id,
                productId: line.productId,
                variantId: line.variantId || null,
                quantity: line.quantity.toString(),
                fromInventoryId: sourceInventory.id,
                batchId: line.batchId || null,
                unitCost: (
                  line.unitCost ||
                  Number(sourceInventory.unitCost) ||
                  0
                ).toString(),
                sortOrder: index + 1,
                notes: line.notes || null,
                createdBy: userId,
                businessId,
                createdAt: new Date(),
              })
              .returning();

            // Update transfer line with serial IDs if provided
            if (line.serialIds && line.serialIds.length > 0) {
              await tx
                .update(stockTransferLines)
                .set({
                  serialIds: line.serialIds,
                })
                .where(eq(stockTransferLines.id, transferLine.id));
            }
          }),
        );
      }

      // Log the stock transfer creation activity
      await this.activityLogService.logCreate(
        transfer.id,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOne(userId, businessId, transfer.id, metadata);
    });
  }

  async findOne(
    userId: string,
    businessId: string | null,
    transferId: string,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Use metadata for activity logging if needed
    console.debug('Finding stock transfer', { transferId, userId, metadata });

    const [transfer] = await this.db
      .select({
        transfer: stockTransfers,
        fromLocation: locations,
        toLocation: locations,
        requestedBy: staffMembers,
        approvedBy: staffMembers,
        shippedBy: staffMembers,
        receivedBy: staffMembers,
      })
      .from(stockTransfers)
      .leftJoin(locations, eq(stockTransfers.fromLocationId, locations.id))
      .leftJoin(
        locations,
        and(
          eq(stockTransfers.toLocationId, locations.id),
          sql`${stockTransfers.toLocationId} != ${stockTransfers.fromLocationId}`,
        ),
      )
      .leftJoin(staffMembers, eq(stockTransfers.requestedBy, staffMembers.id))
      .leftJoin(
        staffMembers,
        and(
          eq(stockTransfers.approvedBy, staffMembers.id),
          sql`${stockTransfers.approvedBy} IS NOT NULL`,
        ),
      )
      .leftJoin(
        staffMembers,
        and(
          eq(stockTransfers.shippedBy, staffMembers.id),
          sql`${stockTransfers.shippedBy} IS NOT NULL`,
        ),
      )
      .leftJoin(
        staffMembers,
        and(
          eq(stockTransfers.receivedBy, staffMembers.id),
          sql`${stockTransfers.receivedBy} IS NOT NULL`,
        ),
      )
      .where(eq(stockTransfers.id, transferId));

    if (!transfer) {
      throw new NotFoundException('Stock transfer not found');
    }

    // Get transfer lines
    const lines = await this.getTransferLines(transferId);

    return this.mapToTransferResponse(transfer, lines);
  }

  async findAll(
    userId: string,
    businessId: string | null,
    query: StockTransferQueryDto,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferListResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Use metadata for activity logging if needed
    console.debug('Finding stock transfers', { query, userId, metadata });

    const {
      page = 1,
      limit = 20,
      fromLocationId,
      toLocationId,
      status,
      startDate,
      endDate,
    } = query;
    const offset = (page - 1) * limit;

    const conditions = [];

    if (fromLocationId) {
      conditions.push(eq(stockTransfers.fromLocationId, fromLocationId));
    }

    if (toLocationId) {
      conditions.push(eq(stockTransfers.toLocationId, toLocationId));
    }

    if (status) {
      conditions.push(eq(stockTransfers.status, status as any));
    }

    if (startDate) {
      conditions.push(
        sql`${stockTransfers.transferDate} >= ${new Date(startDate)}`,
      );
    }

    if (endDate) {
      conditions.push(
        sql`${stockTransfers.transferDate} <= ${new Date(endDate)}`,
      );
    }

    const transfers = await this.db
      .select({
        transfer: stockTransfers,
        fromLocation: locations,
        toLocation: locations,
        requestedBy: staffMembers,
        lineCount: sql<number>`(
          SELECT COUNT(*) 
          FROM ${stockTransferLines} 
          WHERE ${stockTransferLines.transferId} = ${stockTransfers.id}
        )`,
        totalQuantity: sql<number>`(
          SELECT COALESCE(SUM(${stockTransferLines.quantity}::decimal), 0) 
          FROM ${stockTransferLines} 
          WHERE ${stockTransferLines.transferId} = ${stockTransfers.id}
        )`,
      })
      .from(stockTransfers)
      .leftJoin(locations, eq(stockTransfers.fromLocationId, locations.id))
      .leftJoin(
        locations,
        and(
          eq(stockTransfers.toLocationId, locations.id),
          sql`${stockTransfers.toLocationId} != ${stockTransfers.fromLocationId}`,
        ),
      )
      .leftJoin(staffMembers, eq(stockTransfers.requestedBy, staffMembers.id))
      .where(and(...conditions))
      .orderBy(desc(stockTransfers.transferDate))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ count }] = await this.db
      .select({ count: sql`count(*)` })
      .from(stockTransfers)
      .where(and(...conditions));

    return {
      data: transfers.map((t) => ({
        transferId: t.transfer.id,
        transferNumber: t.transfer.transferNumber,
        fromLocationId: t.transfer.fromLocationId,
        fromLocationName: t.fromLocation?.name || '',
        toLocationId: t.transfer.toLocationId,
        toLocationName: t.toLocation?.name || '',
        transferDate: t.transfer.transferDate,
        status: t.transfer.status,
        transferReason: t.transfer.transferReason,
        lineCount: Number(t.lineCount),
        totalQuantity: Number(t.totalQuantity),
        requestedBy: t.transfer.requestedBy,
        requestedByName: t.requestedBy
          ? `${t.requestedBy.firstName} ${t.requestedBy.lastName}`
          : '',
        createdAt: t.transfer.createdAt,
      })),
      pagination: {
        page,
        limit,
        total: Number(count),
        totalPages: Math.ceil(Number(count) / limit),
      },
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    transferId: string,
    updateDto: UpdateStockTransferDto,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const { transferReason, notes, lines } = updateDto;

    const [existing] = await this.db
      .select()
      .from(stockTransfers)
      .where(eq(stockTransfers.id, transferId));

    if (!existing) {
      throw new NotFoundException('Stock transfer not found');
    }

    if (existing.status !== 'draft') {
      throw new BadRequestException('Only draft transfers can be updated');
    }

    return await this.db.transaction(async (tx) => {
      // Update header if needed
      if (transferReason || notes !== undefined) {
        await tx
          .update(stockTransfers)
          .set({
            transferReason: transferReason || existing.transferReason,
            notes: notes !== undefined ? notes : existing.notes,
            updatedAt: new Date(),
          })
          .where(eq(stockTransfers.id, transferId));
      }

      // Update lines if provided
      if (lines && lines.length > 0) {
        // Delete existing lines
        await tx
          .delete(stockTransferLines)
          .where(eq(stockTransferLines.transferId, transferId));

        // Create new lines
        await Promise.all(
          lines.map(async (line, index) => {
            const [sourceInventory] = await tx
              .select()
              .from(inventory)
              .where(
                and(
                  eq(inventory.productId, line.productId),
                  eq(inventory.locationId, existing.fromLocationId),
                  line.variantId
                    ? eq(inventory.variantId, line.variantId)
                    : sql`${inventory.variantId} IS NULL`,
                ),
              );

            if (!sourceInventory) {
              throw new NotFoundException(
                `Product ${line.productId} not found in source location`,
              );
            }

            if (Number(sourceInventory.quantityAvailable) < line.quantity) {
              throw new BadRequestException(
                `Insufficient quantity available for product ${line.productId}`,
              );
            }

            const [transferLine] = await tx
              .insert(stockTransferLines)
              .values({
                transferId: transferId,
                productId: line.productId,
                variantId: line.variantId || null,
                quantity: line.quantity.toString(),
                fromInventoryId: sourceInventory.id,
                batchId: line.batchId || null,
                unitCost: (
                  line.unitCost ||
                  Number(sourceInventory.unitCost) ||
                  0
                ).toString(),
                sortOrder: index + 1,
                notes: line.notes || null,
                createdBy: userId,
                businessId,
                createdAt: new Date(),
              })
              .returning();

            if (line.serialIds && line.serialIds.length > 0) {
              await tx
                .update(stockTransferLines)
                .set({
                  serialIds: line.serialIds,
                })
                .where(eq(stockTransferLines.id, transferLine.id));
            }
          }),
        );
      }

      // Log the stock transfer update activity
      await this.activityLogService.logUpdate(
        transferId,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOne(userId, businessId, transferId, metadata);
    });
  }

  async updateStatus(
    userId: string,
    businessId: string | null,
    transferId: string,
    updateStatusDto: UpdateStockTransferStatusDto,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const { status, notes } = updateStatusDto;

    const [transfer] = await this.db
      .select()
      .from(stockTransfers)
      .where(eq(stockTransfers.id, transferId));

    if (!transfer) {
      throw new NotFoundException('Stock transfer not found');
    }

    // Validate status transition
    this.validateStatusTransition(transfer.status, status);

    const updates: any = {
      status,
      updatedAt: new Date(),
    };

    if (notes) {
      updates.notes = transfer.notes ? `${transfer.notes}\n${notes}` : notes;
    }

    await this.db
      .update(stockTransfers)
      .set(updates)
      .where(eq(stockTransfers.id, transferId));

    // Log the stock transfer status update activity
    await this.activityLogService.logUpdate(
      transferId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, transferId, metadata);
  }

  async approve(
    userId: string,
    businessId: string | null,
    transferId: string,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const [transfer] = await this.db
      .select()
      .from(stockTransfers)
      .where(eq(stockTransfers.id, transferId));

    if (!transfer) {
      throw new NotFoundException('Stock transfer not found');
    }

    if (transfer.status !== 'draft') {
      throw new BadRequestException('Only draft transfers can be approved');
    }

    await this.db
      .update(stockTransfers)
      .set({
        status: 'approved',
        approvedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(stockTransfers.id, transferId));

    // Log the stock transfer approval activity
    await this.activityLogService.logUpdate(
      transferId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, transferId, metadata);
  }

  async ship(
    userId: string,
    businessId: string | null,
    transferId: string,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const transfer = await this.findOne(
      userId,
      businessId,
      transferId,
      metadata,
    );

    if (transfer.status !== 'approved' && transfer.status !== 'draft') {
      throw new BadRequestException(
        'Transfer must be approved before shipping',
      );
    }

    return await this.db.transaction(async (tx) => {
      // Create inventory transactions for outbound
      for (const line of transfer.lines) {
        // Create transfer out transaction
        await this.transactionsService.create(
          userId,
          businessId,
          {
            inventoryId: line.fromInventoryId,
            serialId: line.serialIds?.[0] || undefined, // Handle multiple serials separately
            batchId: line.batchId,
            transactionType: 'transfer_out',
            quantity: line.quantity,
            unitCost: line.unitCost,
            referenceType: 'stock_transfer',
            referenceId: transferId,
            reasonCode: 'TRANSFER_OUT',
            notes: `Transfer to ${transfer.toLocationName}`,
          },
          metadata,
        );

        // Update serial numbers location if any
        if (line.serialIds && line.serialIds.length > 0) {
          await tx
            .update(serialNumbers)
            .set({
              currentLocationId: transfer.toLocationId,
              updatedAt: new Date(),
            })
            .where(inArray(serialNumbers.id, line.serialIds));
        }

        // Create inventory reservations for destination
        await this.createDestinationInventory(tx, line, transfer.toLocationId);
      }

      // Update transfer status
      await tx
        .update(stockTransfers)
        .set({
          status: 'in_transit',
          shippedBy: userId,
          shippedDate: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(stockTransfers.id, transferId));

      // Log the stock transfer shipping activity
      await this.activityLogService.logUpdate(
        transferId,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOne(userId, businessId, transferId, metadata);
    });
  }

  async receive(
    userId: string,
    businessId: string | null,
    transferId: string,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const transfer = await this.findOne(
      userId,
      businessId,
      transferId,
      metadata,
    );

    if (transfer.status !== 'in_transit') {
      throw new BadRequestException(
        'Only in-transit transfers can be received',
      );
    }

    return await this.db.transaction(async (tx) => {
      // Create inventory transactions for inbound
      for (const line of transfer.lines) {
        // Get or create destination inventory
        let [destInventory] = await tx
          .select()
          .from(inventory)
          .where(
            and(
              eq(inventory.productId, line.productId),
              eq(inventory.locationId, transfer.toLocationId),
              line.variantId
                ? eq(inventory.variantId, line.variantId)
                : sql`${inventory.variantId} IS NULL`,
            ),
          );

        if (!destInventory) {
          [destInventory] = await tx
            .insert(inventory)
            .values({
              productId: line.productId,
              variantId: line.variantId || null,
              locationId: transfer.toLocationId,
              quantityOnHand: '0',
              quantityReserved: '0',
              quantityAvailable: '0',
              createdBy: userId,
              businessId,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();
        }

        // Create transfer in transaction
        await this.transactionsService.create(
          userId,
          businessId,
          {
            inventoryId: destInventory.id,
            serialId: line.serialIds?.[0] || undefined,
            batchId: line.batchId,
            transactionType: 'transfer_in',
            quantity: line.quantity,
            unitCost: line.unitCost,
            referenceType: 'stock_transfer',
            referenceId: transferId,
            reasonCode: 'TRANSFER_IN',
            notes: `Transfer from ${transfer.fromLocationName}`,
          },
          metadata,
        );

        // Update batch location if applicable
        if (line.batchId) {
          await tx
            .update(batchNumbers)
            .set({
              locationId: transfer.toLocationId,
              updatedAt: new Date(),
            })
            .where(eq(batchNumbers.id, line.batchId));
        }
      }

      // Update transfer status
      await tx
        .update(stockTransfers)
        .set({
          status: 'completed',
          receivedBy: userId,
          receivedDate: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(stockTransfers.id, transferId));

      // Log the stock transfer receiving activity
      await this.activityLogService.logUpdate(
        transferId,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOne(userId, businessId, transferId, metadata);
    });
  }

  async cancel(
    userId: string,
    businessId: string | null,
    transferId: string,
    reason: string,
    metadata?: ActivityMetadata,
  ): Promise<StockTransferResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const [transfer] = await this.db
      .select()
      .from(stockTransfers)
      .where(eq(stockTransfers.id, transferId));

    if (!transfer) {
      throw new NotFoundException('Stock transfer not found');
    }

    if (transfer.status === 'completed') {
      throw new BadRequestException('Completed transfers cannot be cancelled');
    }

    if (transfer.status === 'cancelled') {
      throw new BadRequestException('Transfer is already cancelled');
    }

    // If in transit, we need to reverse the outbound transactions
    if (transfer.status === 'in_transit') {
      const transactions =
        await this.transactionsService.getTransactionsByReference(
          'stock_transfer',
          transferId as any,
        );

      for (const transaction of transactions) {
        if (transaction.transactionType === 'transfer_out') {
          await this.transactionsService.reverseTransaction(
            userId,
            businessId,
            transaction.transactionId,
            `Transfer cancelled: ${reason}`,
            metadata,
          );
        }
      }
    }

    await this.db
      .update(stockTransfers)
      .set({
        status: 'cancelled',
        notes: transfer.notes
          ? `${transfer.notes}\nCancelled: ${reason}`
          : `Cancelled: ${reason}`,
        updatedAt: new Date(),
      })
      .where(eq(stockTransfers.id, transferId));

    // Log the stock transfer cancellation activity
    await this.activityLogService.logUpdate(
      transferId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, transferId, metadata);
  }

  // Helper methods
  private async generateTransferNumber(tx: any): Promise<string> {
    const [lastTransfer] = await tx
      .select({ transferNumber: stockTransfers.transferNumber })
      .from(stockTransfers)
      .orderBy(desc(stockTransfers.id))
      .limit(1);

    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');

    if (
      lastTransfer &&
      lastTransfer.transferNumber.startsWith(
        `TRF-${currentYear}${currentMonth}`,
      )
    ) {
      const lastNumber = parseInt(
        lastTransfer.transferNumber.split('-').pop() || '0',
      );
      return `TRF-${currentYear}${currentMonth}-${String(lastNumber + 1).padStart(4, '0')}`;
    }

    return `TRF-${currentYear}${currentMonth}-0001`;
  }

  private async getTransferLines(transferId: string): Promise<any[]> {
    const lines = await this.db
      .select({
        line: stockTransferLines,
        product: products,
        variant: productVariants,
        batch: batchNumbers,
      })
      .from(stockTransferLines)
      .leftJoin(products, eq(stockTransferLines.productId, products.id))
      .leftJoin(
        productVariants,
        eq(stockTransferLines.variantId, productVariants.id),
      )
      .leftJoin(batchNumbers, eq(stockTransferLines.batchId, batchNumbers.id))
      .where(eq(stockTransferLines.transferId, transferId))
      .orderBy(stockTransferLines.sortOrder);

    // Get serial numbers for each line
    const linesWithSerials = await Promise.all(
      lines.map(async (line) => {
        let serialNumbersList = [];
        if (line.line.serialIds && line.line.serialIds.length > 0) {
          const serials = await this.db
            .select({
              serialId: serialNumbers.id,
              serialNumber: serialNumbers.serialNumber,
            })
            .from(serialNumbers)
            .where(inArray(serialNumbers.id, line.line.serialIds));

          serialNumbersList = serials.map((s) => s.serialNumber);
        }

        return {
          ...line,
          serialIds: line.line.serialIds || [],
          serialNumbers: serialNumbersList,
        };
      }),
    );

    return linesWithSerials;
  }

  private async createDestinationInventory(
    tx: any,
    line: any,
    toLocationId: string,
  ): Promise<void> {
    // Check if inventory exists at destination
    const [destInventory] = await tx
      .select()
      .from(inventory)
      .where(
        and(
          eq(inventory.productId, line.productId),
          eq(inventory.locationId, toLocationId),
          line.variantId
            ? eq(inventory.variantId, line.variantId)
            : sql`${inventory.variantId} IS NULL`,
        ),
      );

    if (!destInventory) {
      // Create inventory record at destination
      await tx.insert(inventory).values({
        productId: line.productId,
        variantId: line.variantId || null,
        locationId: toLocationId,
        quantityOnHand: '0',
        quantityReserved: '0',
        quantityAvailable: '0',
        createdBy: 'system',
        businessId: 'default',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }
  }

  private validateStatusTransition(
    currentStatus: string,
    newStatus: string,
  ): void {
    const validTransitions = {
      draft: ['approved', 'cancelled'],
      approved: ['in_transit', 'cancelled'],
      in_transit: ['completed', 'cancelled'],
      completed: [],
      cancelled: [],
    };

    const allowed = validTransitions[currentStatus] || [];

    if (!allowed.includes(newStatus)) {
      throw new BadRequestException(
        `Invalid status transition from ${currentStatus} to ${newStatus}`,
      );
    }
  }

  private mapToTransferResponse(
    data: any,
    lines: any[],
  ): StockTransferResponseDto {
    return {
      transferId: data.transfer.id,
      transferNumber: data.transfer.transferNumber,
      fromLocationId: data.transfer.fromLocationId,
      fromLocationName: data.fromLocation?.name || '',
      toLocationId: data.transfer.toLocationId,
      toLocationName: data.toLocation?.name || '',
      transferDate: data.transfer.transferDate,
      status: data.transfer.status,
      transferReason: data.transfer.transferReason,
      notes: data.transfer.notes,
      requestedBy: data.transfer.requestedBy,
      requestedByName: data.requestedBy
        ? `${data.requestedBy.firstName} ${data.requestedBy.lastName}`
        : '',
      approvedBy: data.transfer.approvedBy,
      approvedByName: data.approvedBy
        ? `${data.approvedBy.firstName} ${data.approvedBy.lastName}`
        : null,
      shippedBy: data.transfer.shippedBy,
      shippedByName: data.shippedBy
        ? `${data.shippedBy.firstName} ${data.shippedBy.lastName}`
        : null,
      shippedDate: data.transfer.shippedDate,
      receivedBy: data.transfer.receivedBy,
      receivedByName: data.receivedBy
        ? `${data.receivedBy.firstName} ${data.receivedBy.lastName}`
        : null,
      receivedDate: data.transfer.receivedDate,
      createdAt: data.transfer.createdAt,
      updatedAt: data.transfer.updatedAt,
      lines: lines.map((l) => ({
        transferLineId: l.line.id,
        productId: l.line.productId,
        productCode: l.product?.shortCode || '',
        productName: l.product?.name || '',
        variantId: l.line.variantId,
        variantName: l.variant?.name || null,
        quantity: Number(l.line.quantity),
        fromInventoryId: l.line.fromInventoryId,
        toInventoryId: l.line.toInventoryId,
        batchId: l.line.batchId,
        batchNumber: l.batch?.batchNumber || null,
        serialIds: l.serialIds || [],
        serialNumbers: l.serialNumbers || [],
        unitCost: Number(l.line.unitCost),
        totalValue: Number(l.line.quantity) * Number(l.line.unitCost),
        notes: l.line.notes,
      })),
      totalItems: lines.length,
      totalQuantity: lines.reduce((sum, l) => sum + Number(l.line.quantity), 0),
      totalValue: lines.reduce(
        (sum, l) => sum + Number(l.line.quantity) * Number(l.line.unitCost),
        0,
      ),
    };
  }
}
