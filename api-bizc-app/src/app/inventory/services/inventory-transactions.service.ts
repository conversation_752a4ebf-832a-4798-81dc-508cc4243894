import {
  Injectable,
  Inject,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { and, eq, gte, lte, desc } from 'drizzle-orm';
import { DRIZZLE } from '../../drizzle/drizzle.module';
import { DrizzleDB } from '../../drizzle/types/drizzle';
import {
  inventory,
  inventoryTransactions,
  serialNumbers,
  batchNumbers,
} from '../../drizzle/schema/inventory.schema';
import {
  products,
  productVariants,
} from '../../drizzle/schema/products.schema';
import { locations } from '../../drizzle/schema/locations.schema';
import { staffMembers } from '../../drizzle/schema/staff.schema';
import {
  CreateInventoryTransactionDto,
  InventoryTransactionDto,
} from '../dto/inventory-transaction.dto';
import { InventoryQueryDto } from '../dto/inventory-query.dto';
import { ActivityMetadata } from '../../shared/types/activity-metadata.type';

@Injectable()
export class InventoryTransactionsService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async findAll(
    _userId: string,
    _businessId: string,
    query: InventoryQueryDto,
  ): Promise<InventoryTransactionDto[]> {
    const {
      page = 1,
      limit = 20,
      locationId,
      productId,
      variantId,
      startDate,
      endDate,
      transactionType,
      referenceType,
    } = query;
    const offset = (page - 1) * limit;

    const conditions = [];

    // Add date range filter
    if (startDate) {
      conditions.push(
        gte(inventoryTransactions.transactionDate, new Date(startDate)),
      );
    }
    if (endDate) {
      conditions.push(
        lte(inventoryTransactions.transactionDate, new Date(endDate)),
      );
    }

    // Add transaction type filter
    if (transactionType) {
      conditions.push(
        eq(inventoryTransactions.transactionType, transactionType as any),
      );
    }

    // Add reference type filter
    if (referenceType) {
      conditions.push(eq(inventoryTransactions.referenceType, referenceType));
    }

    // Join with inventory to filter by location/product
    const transactions = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        serial: serialNumbers,
        batch: batchNumbers,
        createdByStaff: staffMembers,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        serialNumbers,
        eq(inventoryTransactions.serialId, serialNumbers.id),
      )
      .leftJoin(
        batchNumbers,
        eq(inventoryTransactions.batchId, batchNumbers.id),
      )
      .leftJoin(
        staffMembers,
        eq(inventoryTransactions.performedBy, staffMembers.id),
      )
      .where(
        and(
          ...conditions,
          locationId ? eq(inventory.locationId, locationId) : undefined,
          productId ? eq(inventory.productId, productId) : undefined,
          variantId ? eq(inventory.variantId, variantId) : undefined,
        ),
      )
      .orderBy(desc(inventoryTransactions.transactionDate))
      .limit(limit)
      .offset(offset);

    return transactions.map((t) => this.mapToTransactionDto(t));
  }

  async findOne(
    _userId: string,
    _businessId: string,
    transactionId: string,
  ): Promise<InventoryTransactionDto> {
    const [transaction] = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        serial: serialNumbers,
        batch: batchNumbers,
        createdByStaff: staffMembers,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        serialNumbers,
        eq(inventoryTransactions.serialId, serialNumbers.id),
      )
      .leftJoin(
        batchNumbers,
        eq(inventoryTransactions.batchId, batchNumbers.id),
      )
      .leftJoin(
        staffMembers,
        eq(inventoryTransactions.performedBy, staffMembers.id),
      )
      .where(eq(inventoryTransactions.id, transactionId));

    if (!transaction) {
      throw new NotFoundException('Transaction not found');
    }

    return this.mapToTransactionDto(transaction);
  }

  async create(
    userId: string,
    businessId: string,
    createDto: CreateInventoryTransactionDto,
    _metadata: ActivityMetadata,
  ): Promise<InventoryTransactionDto> {
    const {
      inventoryId,
      serialId,
      batchId,
      transactionType,
      quantity,
      unitCost,
      referenceType,
      referenceId,
      reasonCode,
      notes,
    } = createDto;

    // Validate inventory exists
    const [inventoryRecord] = await this.db
      .select()
      .from(inventory)
      .where(eq(inventory.id, inventoryId));

    if (!inventoryRecord) {
      throw new NotFoundException('Inventory record not found');
    }

    // Validate serial number if provided
    if (serialId) {
      const [serial] = await this.db
        .select()
        .from(serialNumbers)
        .where(eq(serialNumbers.id, serialId));

      if (!serial) {
        throw new NotFoundException('Serial number not found');
      }

      // Validate serial number belongs to the product
      if (
        serial.productId !== inventoryRecord.productId ||
        (serial.variantId && serial.variantId !== inventoryRecord.variantId)
      ) {
        throw new BadRequestException(
          'Serial number does not match the inventory product/variant',
        );
      }
    }

    // Validate batch number if provided
    if (batchId) {
      const [batch] = await this.db
        .select()
        .from(batchNumbers)
        .where(eq(batchNumbers.id, batchId));

      if (!batch) {
        throw new NotFoundException('Batch number not found');
      }

      // Validate batch belongs to the product
      if (
        batch.productId !== inventoryRecord.productId ||
        (batch.variantId && batch.variantId !== inventoryRecord.variantId)
      ) {
        throw new BadRequestException(
          'Batch number does not match the inventory product/variant',
        );
      }

      // Check batch availability for issues
      if (
        transactionType === 'issue' &&
        Number(batch.availableQuantity) < quantity
      ) {
        throw new BadRequestException('Insufficient batch quantity available');
      }
    }

    // Begin transaction
    return await this.db.transaction(async (tx) => {
      // Validate and update inventory levels based on transaction type
      await this.updateInventoryLevels(
        tx,
        inventoryRecord,
        transactionType,
        quantity,
      );

      // Create the transaction
      const [newTransaction] = await tx
        .insert(inventoryTransactions)
        .values({
          businessId,
          inventoryId,
          serialId: serialId || null,
          batchId: batchId || null,
          transactionType: transactionType as any,
          quantity: quantity.toString(),
          unitCost: unitCost ? unitCost.toString() : '0',
          referenceType,
          referenceId,
          reasonCode: reasonCode || null,
          notes: notes || null,
          performedBy: userId,
          transactionDate: new Date(),
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      // Update serial number status if applicable
      if (serialId) {
        await this.updateSerialStatus(
          tx,
          serialId,
          transactionType,
          referenceType,
          referenceId,
        );
      }

      // Update batch quantities if applicable
      if (batchId) {
        await this.updateBatchQuantity(tx, batchId, transactionType, quantity);
      }

      // Return the created transaction
      return this.findOne(userId, businessId, newTransaction.id);
    });
  }

  async createBulk(
    userId: string,
    businessId: string,
    transactions: CreateInventoryTransactionDto[],
    metadata: ActivityMetadata,
  ): Promise<InventoryTransactionDto[]> {
    const results = [];

    // Process in a single database transaction
    await this.db.transaction(async () => {
      for (const transaction of transactions) {
        const result = await this.create(
          userId,
          businessId,
          transaction,
          metadata,
        );
        results.push(result);
      }
    });

    return results;
  }

  async reverseTransaction(
    userId: string,
    businessId: string,
    transactionId: string,
    reason: string,
    metadata: ActivityMetadata,
  ): Promise<InventoryTransactionDto> {
    // Get the original transaction
    const originalTransaction = await this.findOne(
      userId,
      businessId,
      transactionId,
    );

    if (!originalTransaction) {
      throw new NotFoundException('Transaction not found');
    }

    // Determine the reverse transaction type
    const reverseType = this.getReverseTransactionType(
      originalTransaction.transactionType,
    );

    // Create the reverse transaction
    return this.create(
      userId,
      businessId,
      {
        inventoryId: originalTransaction.inventoryId,
        serialId: originalTransaction.serialId,
        batchId: originalTransaction.batchId,
        transactionType: reverseType,
        quantity: originalTransaction.quantity,
        unitCost: originalTransaction.unitCost,
        referenceType: 'reverse_transaction',
        referenceId: originalTransaction.transactionId,
        reasonCode: 'REVERSAL',
        notes: `Reversal of transaction ${transactionId}: ${reason}`,
      },
      metadata,
    );
  }

  // Helper methods
  private async updateInventoryLevels(
    tx: any,
    inventoryRecord: any,
    transactionType: string,
    quantity: number,
  ): Promise<any> {
    let newQuantityOnHand = Number(inventoryRecord.quantityOnHand);
    let newQuantityAvailable = Number(inventoryRecord.quantityAvailable);

    switch (transactionType) {
      case 'receipt':
      case 'transfer_in':
      case 'adjustment':
        if (transactionType === 'adjustment' && quantity < 0) {
          // Negative adjustment
          newQuantityOnHand += quantity;
          newQuantityAvailable += quantity;
        } else {
          // Positive adjustment or receipt
          newQuantityOnHand += quantity;
          newQuantityAvailable += quantity;
        }
        break;

      case 'issue':
      case 'transfer_out':
        if (newQuantityAvailable < quantity) {
          throw new BadRequestException('Insufficient inventory available');
        }
        newQuantityOnHand -= quantity;
        newQuantityAvailable -= quantity;
        break;

      case 'count': {
        // Physical count - set absolute quantity
        newQuantityOnHand = quantity;
        newQuantityAvailable =
          quantity - Number(inventoryRecord.quantityReserved);
        break;
      }

      default:
        throw new BadRequestException(
          `Invalid transaction type: ${transactionType}`,
        );
    }

    // Validate that quantities don't go negative
    if (newQuantityOnHand < 0 || newQuantityAvailable < 0) {
      throw new BadRequestException(
        'Transaction would result in negative inventory',
      );
    }

    // Update inventory record
    const [updated] = await tx
      .update(inventory)
      .set({
        quantityOnHand: newQuantityOnHand,
        quantityAvailable: newQuantityAvailable,
        lastCountDate:
          transactionType === 'count'
            ? new Date()
            : inventoryRecord.lastCountDate,
        updatedAt: new Date(),
      })
      .where(eq(inventory.id, inventoryRecord.id))
      .returning();

    return updated;
  }

  private async updateSerialStatus(
    tx: any,
    serialId: string,
    transactionType: string,
    referenceType: string,
    referenceId: string,
  ): Promise<void> {
    let newStatus: string;
    const updates: any = {
      updatedAt: new Date(),
    };

    switch (transactionType) {
      case 'issue':
        if (referenceType === 'sales_order') {
          newStatus = 'sold';
          updates.salesOrderId = referenceId;
          updates.salesDate = new Date();
        } else {
          newStatus = 'reserved';
        }
        break;

      case 'receipt':
        newStatus = 'available';
        break;

      case 'transfer_out':
      case 'transfer_in':
        // Status doesn't change on transfers, just location
        return;

      default:
        return;
    }

    updates.status = newStatus;

    await tx
      .update(serialNumbers)
      .set(updates)
      .where(eq(serialNumbers.id, serialId));
  }

  private async updateBatchQuantity(
    tx: any,
    batchId: string,
    transactionType: string,
    quantity: number,
  ): Promise<void> {
    const [batch] = await tx
      .select()
      .from(batchNumbers)
      .where(eq(batchNumbers.id, batchId));

    if (!batch) {
      throw new NotFoundException('Batch not found');
    }

    let newAvailableQuantity = Number(batch.availableQuantity);

    switch (transactionType) {
      case 'receipt':
      case 'transfer_in':
        newAvailableQuantity += quantity;
        break;

      case 'issue':
      case 'transfer_out':
        newAvailableQuantity -= quantity;
        break;

      case 'adjustment':
        newAvailableQuantity += quantity; // Can be positive or negative
        break;
    }

    if (newAvailableQuantity < 0) {
      throw new BadRequestException(
        'Transaction would result in negative batch quantity',
      );
    }

    await tx
      .update(batchNumbers)
      .set({
        availableQuantity: newAvailableQuantity,
        updatedAt: new Date(),
      })
      .where(eq(batchNumbers.id, batchId));
  }

  private getReverseTransactionType(originalType: string): string {
    const reverseMap = {
      receipt: 'issue',
      issue: 'receipt',
      transfer_in: 'transfer_out',
      transfer_out: 'transfer_in',
      adjustment: 'adjustment',
      count: 'adjustment',
    };

    return reverseMap[originalType] || 'adjustment';
  }

  private mapToTransactionDto(data: any): InventoryTransactionDto {
    return {
      transactionId: data.transaction.id,
      inventoryId: data.transaction.inventoryId,
      productId: data.inventory?.productId,
      productName: data.product?.productName,
      productCode: data.product?.productCode,
      variantId: data.inventory?.variantId,
      variantName: data.variant?.variantName,
      locationId: data.inventory?.locationId,
      locationName: data.location?.locationName,
      serialId: data.transaction.serialId,
      serialNumber: data.serial?.serialNumber,
      batchId: data.transaction.batchId,
      batchNumber: data.batch?.batchNumber,
      transactionType: data.transaction.transactionType,
      quantity: Number(data.transaction.quantity),
      unitCost: Number(data.transaction.unitCost || 0),
      totalCost:
        Number(data.transaction.quantity) *
        Number(data.transaction.unitCost || 0),
      referenceType: data.transaction.referenceType,
      referenceId: data.transaction.referenceId,
      reasonCode: data.transaction.reasonCode,
      notes: data.transaction.notes,
      createdBy: data.transaction.createdBy,
      createdByName: data.createdByStaff
        ? `${data.createdByStaff.firstName} ${data.createdByStaff.lastName}`
        : null,
      transactionDate: data.transaction.transactionDate,
      createdAt: data.transaction.createdAt,
    };
  }

  // Additional utility methods

  async getTransactionsByReference(
    referenceType: string,
    referenceId: string,
  ): Promise<InventoryTransactionDto[]> {
    const transactions = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        serial: serialNumbers,
        batch: batchNumbers,
        createdByStaff: staffMembers,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        serialNumbers,
        eq(inventoryTransactions.serialId, serialNumbers.id),
      )
      .leftJoin(
        batchNumbers,
        eq(inventoryTransactions.batchId, batchNumbers.id),
      )
      .leftJoin(
        staffMembers,
        eq(inventoryTransactions.performedBy, staffMembers.id),
      )
      .where(
        and(
          eq(inventoryTransactions.referenceType, referenceType),
          eq(inventoryTransactions.referenceId, referenceId),
        ),
      )
      .orderBy(desc(inventoryTransactions.transactionDate));

    return transactions.map((t) => this.mapToTransactionDto(t));
  }

  async getTransactionHistory(
    inventoryId: string,
    limit: number = 50,
  ): Promise<InventoryTransactionDto[]> {
    const transactions = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        serial: serialNumbers,
        batch: batchNumbers,
        createdByStaff: staffMembers,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        serialNumbers,
        eq(inventoryTransactions.serialId, serialNumbers.id),
      )
      .leftJoin(
        batchNumbers,
        eq(inventoryTransactions.batchId, batchNumbers.id),
      )
      .leftJoin(
        staffMembers,
        eq(inventoryTransactions.performedBy, staffMembers.id),
      )
      .where(eq(inventoryTransactions.inventoryId, inventoryId))
      .orderBy(desc(inventoryTransactions.transactionDate))
      .limit(limit);

    return transactions.map((t) => this.mapToTransactionDto(t));
  }

  async getSerialNumberHistory(
    serialNumber: string,
  ): Promise<InventoryTransactionDto[]> {
    // First find the serial number
    const [serial] = await this.db
      .select()
      .from(serialNumbers)
      .where(eq(serialNumbers.serialNumber, serialNumber));

    if (!serial) {
      throw new NotFoundException('Serial number not found');
    }

    // Get all transactions for this serial
    const transactions = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        serial: serialNumbers,
        batch: batchNumbers,
        createdByStaff: staffMembers,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        serialNumbers,
        eq(inventoryTransactions.serialId, serialNumbers.id),
      )
      .leftJoin(
        batchNumbers,
        eq(inventoryTransactions.batchId, batchNumbers.id),
      )
      .leftJoin(
        staffMembers,
        eq(inventoryTransactions.performedBy, staffMembers.id),
      )
      .where(eq(inventoryTransactions.serialId, serial.id))
      .orderBy(desc(inventoryTransactions.transactionDate));

    return transactions.map((t) => this.mapToTransactionDto(t));
  }

  async getBatchHistory(
    batchNumber: string,
  ): Promise<InventoryTransactionDto[]> {
    // First find the batch number
    const [batch] = await this.db
      .select()
      .from(batchNumbers)
      .where(eq(batchNumbers.batchNumber, batchNumber));

    if (!batch) {
      throw new NotFoundException('Batch number not found');
    }

    // Get all transactions for this batch
    const transactions = await this.db
      .select({
        transaction: inventoryTransactions,
        inventory: inventory,
        product: products,
        variant: productVariants,
        location: locations,
        serial: serialNumbers,
        batch: batchNumbers,
        createdByStaff: staffMembers,
      })
      .from(inventoryTransactions)
      .leftJoin(inventory, eq(inventoryTransactions.inventoryId, inventory.id))
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .leftJoin(locations, eq(inventory.locationId, locations.id))
      .leftJoin(
        serialNumbers,
        eq(inventoryTransactions.serialId, serialNumbers.id),
      )
      .leftJoin(
        batchNumbers,
        eq(inventoryTransactions.batchId, batchNumbers.id),
      )
      .leftJoin(
        staffMembers,
        eq(inventoryTransactions.performedBy, staffMembers.id),
      )
      .where(eq(inventoryTransactions.batchId, batch.id))
      .orderBy(desc(inventoryTransactions.transactionDate));

    return transactions.map((t) => this.mapToTransactionDto(t));
  }
}
