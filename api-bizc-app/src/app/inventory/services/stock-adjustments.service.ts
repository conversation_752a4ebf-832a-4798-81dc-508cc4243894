import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Inject,
} from '@nestjs/common';
import { and, eq, sql, desc, asc, inArray } from 'drizzle-orm';
import { DRIZZLE } from '../../drizzle/drizzle.module';
import { DrizzleDB } from '../../drizzle/types/drizzle';
import {
  stockAdjustments,
  stockAdjustmentLines,
  inventory,
  serialNumbers,
  batchNumbers,
} from '../../drizzle/schema/inventory.schema';
import {
  products,
  productVariants,
} from '../../drizzle/schema/products.schema';
import { locations } from '../../drizzle/schema/locations.schema';
import { staffMembers } from '../../drizzle/schema/staff.schema';
import { InventoryTransactionsService } from './inventory-transactions.service';
import { ActivityLogService } from '../../activity-log/activity-log.service';
import type { ActivityMetadata as ActivityMetadataType } from '../../shared/types/activity-metadata.type';
import { EntityType, ActivitySource } from '../../shared/types/activity.enum';
import {
  CreateStockAdjustmentDto,
  UpdateStockAdjustmentDto,
  StockAdjustmentResponseDto,
  StockAdjustmentQueryDto,
  StockAdjustmentListResponseDto,
  StockAdjustmentLineDto,
} from '../dto/stock-adjustment.dto';

@Injectable()
export class StockAdjustmentsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly transactionsService: InventoryTransactionsService,
  ) {}

  async create(
    userId: string,
    businessId: string,
    createDto: CreateStockAdjustmentDto,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const { locationId, adjustmentType, reason, notes, lines } = createDto;

    // Validate location exists
    const [location] = await this.db
      .select()
      .from(locations)
      .where(eq(locations.id, locationId));

    if (!location) {
      throw new NotFoundException('Location not found');
    }

    // Begin transaction
    return await this.db.transaction(async (tx) => {
      // Generate adjustment number
      const adjustmentNumber = await this.generateAdjustmentNumber(tx);

      // Create adjustment header
      const [adjustment] = await tx
        .insert(stockAdjustments)
        .values({
          adjustmentNumber,
          locationId,
          adjustmentDate: new Date(),
          adjustmentType: adjustmentType as any,
          status: 'draft',
          reason,
          notes,
          requestedBy: userId,
          createdBy: userId,
          businessId,
        })
        .returning();

      // Create adjustment lines
      if (lines && lines.length > 0) {
        await Promise.all(
          lines.map(async (line, index) => {
            // Validate inventory exists
            const [inventoryRecord] = await tx
              .select()
              .from(inventory)
              .where(
                and(
                  eq(inventory.productId, line.productId),
                  eq(inventory.locationId, locationId),
                  line.variantId
                    ? eq(inventory.variantId, line.variantId)
                    : sql`${inventory.variantId} IS NULL`,
                ),
              );

            if (!inventoryRecord) {
              throw new NotFoundException(
                `Inventory not found for product ${line.productId} at location ${locationId}`,
              );
            }

            // Calculate quantity difference
            const quantityDifference =
              line.newQuantity - Number(inventoryRecord.quantityOnHand);

            // Create adjustment line
            await tx.insert(stockAdjustmentLines).values({
              adjustmentId: adjustment.id,
              productId: line.productId,
              variantId: line.variantId || null,
              inventoryId: inventoryRecord.id,
              currentQuantity: Number(inventoryRecord.quantityOnHand),
              countedQuantity: line.newQuantity,
              quantityDifference,
              unitCost: line.unitCost || inventoryRecord.unitCost || 0,
              totalValue:
                Math.abs(quantityDifference) *
                (line.unitCost || Number(inventoryRecord.unitCost) || 0),
              serialId: line.serialId || null,
              batchId: line.batchId || null,
              reason: line.reason || adjustmentType,
              notes: line.notes || null,
              sortOrder: index + 1,
              createdBy: userId,
              businessId,
              createdAt: new Date(),
            } as any);
          }),
        );
      }

      // Log the stock adjustment creation activity
      await this.activityLogService.logCreate(
        adjustment.id,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      // Return the created adjustment
      return this.findOne(userId, businessId, adjustment.id);
    });
  }

  async findOne(
    userId: string,
    businessId: string,
    adjustmentId: string,
  ): Promise<StockAdjustmentResponseDto> {
    const [adjustment] = await this.db
      .select({
        adjustment: stockAdjustments,
        location: locations,
        createdByStaff: staffMembers,
        approvedByStaff: staffMembers,
      })
      .from(stockAdjustments)
      .leftJoin(locations, eq(stockAdjustments.locationId, locations.id))
      .leftJoin(staffMembers, eq(stockAdjustments.requestedBy, staffMembers.id))
      .leftJoin(
        staffMembers,
        and(
          eq(stockAdjustments.approvedBy, staffMembers.id),
          sql`${stockAdjustments.approvedBy} IS NOT NULL`,
        ),
      )
      .where(eq(stockAdjustments.id, adjustmentId));

    if (!adjustment) {
      throw new NotFoundException('Stock adjustment not found');
    }

    // Get adjustment lines
    const lines = await this.getAdjustmentLines(adjustmentId);

    return this.mapToAdjustmentResponse(adjustment, lines);
  }

  async findAll(
    userId: string,
    businessId: string,
    query: StockAdjustmentQueryDto,
  ): Promise<StockAdjustmentListResponseDto> {
    const {
      page = 1,
      limit = 20,
      locationId,
      status,
      adjustmentType,
      startDate,
      endDate,
    } = query;
    const offset = (page - 1) * limit;

    const conditions = [];

    if (locationId) {
      conditions.push(eq(stockAdjustments.locationId, locationId));
    }

    if (status) {
      conditions.push(eq(stockAdjustments.status, status as any));
    }

    if (adjustmentType) {
      conditions.push(
        eq(stockAdjustments.adjustmentType, adjustmentType as any),
      );
    }

    if (startDate) {
      conditions.push(
        sql`${stockAdjustments.adjustmentDate} >= ${new Date(startDate)}`,
      );
    }

    if (endDate) {
      conditions.push(
        sql`${stockAdjustments.adjustmentDate} <= ${new Date(endDate)}`,
      );
    }

    const adjustments = await this.db
      .select({
        adjustment: stockAdjustments,
        location: locations,
        createdByStaff: staffMembers,
        lineCount: sql<number>`(
          SELECT COUNT(*) 
          FROM ${stockAdjustmentLines} 
          WHERE ${stockAdjustmentLines.adjustmentId} = ${stockAdjustments.id}
        )`,
        totalValue: sql<number>`(
          SELECT COALESCE(SUM(${stockAdjustmentLines.totalValue}), 0) 
          FROM ${stockAdjustmentLines} 
          WHERE ${stockAdjustmentLines.adjustmentId} = ${stockAdjustments.id}
        )`,
      })
      .from(stockAdjustments)
      .leftJoin(locations, eq(stockAdjustments.locationId, locations.id))
      .leftJoin(staffMembers, eq(stockAdjustments.requestedBy, staffMembers.id))
      .where(and(...conditions))
      .orderBy(desc(stockAdjustments.adjustmentDate))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ count }] = await this.db
      .select({ count: sql`count(*)` })
      .from(stockAdjustments)
      .where(and(...conditions));

    return {
      data: adjustments.map((adj) => ({
        adjustmentId: adj.adjustment.id,
        adjustmentNumber: adj.adjustment.adjustmentNumber,
        locationId: adj.adjustment.locationId,
        locationName: adj.location?.name || '',
        adjustmentDate: adj.adjustment.adjustmentDate,
        adjustmentType: adj.adjustment.adjustmentType,
        status: adj.adjustment.status,
        reason: adj.adjustment.reason,
        lineCount: Number(adj.lineCount),
        totalValue: Number(adj.totalValue),
        createdBy: adj.adjustment.createdBy,
        createdByName: adj.createdByStaff
          ? `${adj.createdByStaff.firstName} ${adj.createdByStaff.lastName}`
          : '',
        createdAt: adj.adjustment.createdAt,
      })),
      pagination: {
        page,
        limit,
        total: Number(count),
        totalPages: Math.ceil(Number(count) / limit),
      },
    };
  }

  async update(
    userId: string,
    businessId: string,
    adjustmentId: string,
    updateDto: UpdateStockAdjustmentDto,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const { reason, notes, lines } = updateDto;

    // Get existing adjustment
    const [existing] = await this.db
      .select()
      .from(stockAdjustments)
      .where(eq(stockAdjustments.id, adjustmentId));

    if (!existing) {
      throw new NotFoundException('Stock adjustment not found');
    }

    if (existing.status !== 'draft') {
      throw new BadRequestException('Only draft adjustments can be updated');
    }

    return await this.db.transaction(async (tx) => {
      // Update header if needed
      if (reason || notes !== undefined) {
        await tx
          .update(stockAdjustments)
          .set({
            reason: reason || existing.reason,
            notes: notes !== undefined ? notes : existing.notes,
            updatedAt: new Date(),
          })
          .where(eq(stockAdjustments.id, adjustmentId));
      }

      // Update lines if provided
      if (lines && lines.length > 0) {
        // Delete existing lines
        await tx
          .delete(stockAdjustmentLines)
          .where(eq(stockAdjustmentLines.adjustmentId, adjustmentId));

        // Create new lines
        await Promise.all(
          lines.map(async (line, index) => {
            const [inventoryRecord] = await tx
              .select()
              .from(inventory)
              .where(
                and(
                  eq(inventory.productId, line.productId),
                  eq(inventory.locationId, existing.locationId),
                  line.variantId
                    ? eq(inventory.variantId, line.variantId)
                    : sql`${inventory.variantId} IS NULL`,
                ),
              );

            if (!inventoryRecord) {
              throw new NotFoundException(
                `Inventory not found for product ${line.productId}`,
              );
            }

            const quantityDifference =
              line.newQuantity - Number(inventoryRecord.quantityOnHand);

            await tx.insert(stockAdjustmentLines).values({
              adjustmentId: adjustmentId,
              productId: line.productId,
              variantId: line.variantId || null,
              inventoryId: inventoryRecord.id,
              currentQuantity: Number(inventoryRecord.quantityOnHand),
              countedQuantity: line.newQuantity,
              quantityDifference,
              unitCost: line.unitCost || inventoryRecord.unitCost || 0,
              totalValue:
                Math.abs(quantityDifference) *
                (line.unitCost || Number(inventoryRecord.unitCost) || 0),
              serialId: line.serialId || null,
              batchId: line.batchId || null,
              reason: line.reason || existing.adjustmentType,
              notes: line.notes || null,
              sortOrder: index + 1,
              createdBy: userId,
              businessId,
              createdAt: new Date(),
            } as any);
          }),
        );
      }

      // Log the stock adjustment update activity
      await this.activityLogService.logUpdate(
        adjustmentId,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOne(userId, businessId, adjustmentId);
    });
  }

  async approve(
    userId: string,
    businessId: string,
    adjustmentId: string,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const [adjustment] = await this.db
      .select()
      .from(stockAdjustments)
      .where(eq(stockAdjustments.id, adjustmentId));

    if (!adjustment) {
      throw new NotFoundException('Stock adjustment not found');
    }

    if (adjustment.status !== 'draft') {
      throw new BadRequestException('Only draft adjustments can be approved');
    }

    // Check if user has approval permissions (implement your business logic here)
    // For now, we'll allow any logged-in user to approve

    await this.db
      .update(stockAdjustments)
      .set({
        status: 'approved',
        approvedBy: userId,
        approvedDate: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(stockAdjustments.id, adjustmentId));

    // Log the approval activity
    await this.activityLogService.logUpdate(
      adjustmentId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, adjustmentId);
  }

  async post(
    userId: string,
    businessId: string,
    adjustmentId: string,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const adjustment = await this.findOne(userId, businessId, adjustmentId);

    if (adjustment.status !== 'approved') {
      throw new BadRequestException('Only approved adjustments can be posted');
    }

    return await this.db.transaction(async (tx) => {
      // Get adjustment lines
      const lines = await tx
        .select({
          line: stockAdjustmentLines,
          product: products,
          variant: productVariants,
        })
        .from(stockAdjustmentLines)
        .leftJoin(products, eq(stockAdjustmentLines.productId, products.id))
        .leftJoin(
          productVariants,
          eq(stockAdjustmentLines.variantId, productVariants.id),
        )
        .where(eq(stockAdjustmentLines.adjustmentId, adjustmentId));

      // Create inventory transactions for each line
      for (const { line } of lines) {
        if (Number(line.quantityDifference) !== 0) {
          await this.transactionsService.create(
            userId,
            businessId,
            {
              inventoryId: line.inventoryId,
              serialId: line.serialId,
              batchId: line.batchId,
              transactionType: 'adjustment',
              quantity: Number(line.quantityDifference),
              unitCost: Number(line.unitCost),
              referenceType: 'stock_adjustment',
              referenceId: adjustmentId,
              reasonCode: line.reason,
              notes: line.notes,
            },
            metadata,
          );
        }
      }

      // Update adjustment status
      await tx
        .update(stockAdjustments)
        .set({
          status: 'posted',
          postedDate: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(stockAdjustments.id, adjustmentId));

      // Log the posting activity
      await this.activityLogService.logUpdate(
        adjustmentId,
        EntityType.STOCK_TRANSFER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return this.findOne(userId, businessId, adjustmentId);
    });
  }

  async cancel(
    userId: string,
    businessId: string,
    adjustmentId: string,
    reason: string,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const [adjustment] = await this.db
      .select()
      .from(stockAdjustments)
      .where(eq(stockAdjustments.id, adjustmentId));

    if (!adjustment) {
      throw new NotFoundException('Stock adjustment not found');
    }

    if (adjustment.status === 'posted') {
      throw new BadRequestException('Posted adjustments cannot be cancelled');
    }

    if (adjustment.status === 'cancelled') {
      throw new BadRequestException('Adjustment is already cancelled');
    }

    await this.db
      .update(stockAdjustments)
      .set({
        status: 'cancelled',
        notes: adjustment.notes
          ? `${adjustment.notes}\nCancelled: ${reason}`
          : `Cancelled: ${reason}`,
        updatedAt: new Date(),
      })
      .where(eq(stockAdjustments.id, adjustmentId));

    // Log the cancellation activity
    await this.activityLogService.logUpdate(
      adjustmentId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, adjustmentId);
  }

  async addLine(
    userId: string,
    businessId: string,
    adjustmentId: string,
    lineDto: StockAdjustmentLineDto,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const [adjustment] = await this.db
      .select()
      .from(stockAdjustments)
      .where(eq(stockAdjustments.id, adjustmentId));

    if (!adjustment) {
      throw new NotFoundException('Stock adjustment not found');
    }

    if (adjustment.status !== 'draft') {
      throw new BadRequestException(
        'Lines can only be added to draft adjustments',
      );
    }

    // Get the highest sort order
    const [maxOrder] = await this.db
      .select({ maxOrder: sql`MAX(${stockAdjustmentLines.sortOrder})` })
      .from(stockAdjustmentLines)
      .where(eq(stockAdjustmentLines.adjustmentId, adjustmentId));

    const nextOrder = (Number(maxOrder?.maxOrder) || 0) + 1;

    // Find inventory record
    const [inventoryRecord] = await this.db
      .select()
      .from(inventory)
      .where(
        and(
          eq(inventory.productId, lineDto.productId),
          eq(inventory.locationId, adjustment.locationId),
          lineDto.variantId
            ? eq(inventory.variantId, lineDto.variantId)
            : sql`${inventory.variantId} IS NULL`,
        ),
      );

    if (!inventoryRecord) {
      throw new NotFoundException('Inventory not found for product');
    }

    const quantityDifference =
      lineDto.newQuantity - Number(inventoryRecord.quantityOnHand);

    await this.db.insert(stockAdjustmentLines).values({
      adjustmentId,
      productId: lineDto.productId,
      variantId: lineDto.variantId || null,
      inventoryId: inventoryRecord.id,
      currentQuantity: Number(inventoryRecord.quantityOnHand),
      countedQuantity: lineDto.newQuantity,
      quantityDifference,
      unitCost: lineDto.unitCost || inventoryRecord.unitCost || 0,
      totalValue:
        Math.abs(quantityDifference) *
        (lineDto.unitCost || Number(inventoryRecord.unitCost) || 0),
      serialId: lineDto.serialId || null,
      batchId: lineDto.batchId || null,
      reason: lineDto.reason || adjustment.adjustmentType,
      notes: lineDto.notes || null,
      sortOrder: nextOrder,
      createdBy: userId,
      businessId,
      createdAt: new Date(),
    } as any);

    // Log the line addition activity
    await this.activityLogService.logUpdate(
      adjustmentId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, adjustmentId);
  }

  async removeLine(
    userId: string,
    businessId: string,
    adjustmentId: string,
    lineId: string,
    metadata: ActivityMetadataType,
  ): Promise<StockAdjustmentResponseDto> {
    const [adjustment] = await this.db
      .select()
      .from(stockAdjustments)
      .where(eq(stockAdjustments.id, adjustmentId));

    if (!adjustment) {
      throw new NotFoundException('Stock adjustment not found');
    }

    if (adjustment.status !== 'draft') {
      throw new BadRequestException(
        'Lines can only be removed from draft adjustments',
      );
    }

    await this.db
      .delete(stockAdjustmentLines)
      .where(
        and(
          eq(stockAdjustmentLines.id, lineId),
          eq(stockAdjustmentLines.adjustmentId, adjustmentId),
        ),
      );

    // Log the line removal activity
    await this.activityLogService.logUpdate(
      adjustmentId,
      EntityType.STOCK_TRANSFER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return this.findOne(userId, businessId, adjustmentId);
  }

  // Helper methods
  private async generateAdjustmentNumber(tx: any): Promise<string> {
    const [lastAdjustment] = await tx
      .select({ adjustmentNumber: stockAdjustments.adjustmentNumber })
      .from(stockAdjustments)
      .orderBy(desc(stockAdjustments.id))
      .limit(1);

    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');

    if (
      lastAdjustment &&
      lastAdjustment.adjustmentNumber.startsWith(
        `ADJ-${currentYear}${currentMonth}`,
      )
    ) {
      const lastNumber = parseInt(
        lastAdjustment.adjustmentNumber.split('-').pop() || '0',
      );
      return `ADJ-${currentYear}${currentMonth}-${String(lastNumber + 1).padStart(4, '0')}`;
    }

    return `ADJ-${currentYear}${currentMonth}-0001`;
  }

  private async getAdjustmentLines(adjustmentId: string): Promise<any[]> {
    return await this.db
      .select({
        line: stockAdjustmentLines,
        product: products,
        variant: productVariants,
        serial: serialNumbers,
        batch: batchNumbers,
      })
      .from(stockAdjustmentLines)
      .leftJoin(products, eq(stockAdjustmentLines.productId, products.id))
      .leftJoin(
        productVariants,
        eq(stockAdjustmentLines.variantId, productVariants.id),
      )
      .leftJoin(
        serialNumbers,
        eq(stockAdjustmentLines.serialId, serialNumbers.id),
      )
      .leftJoin(batchNumbers, eq(stockAdjustmentLines.batchId, batchNumbers.id))
      .where(eq(stockAdjustmentLines.adjustmentId, adjustmentId))
      .orderBy(asc(stockAdjustmentLines.sortOrder));
  }

  private mapToAdjustmentResponse(
    data: any,
    lines: any[],
  ): StockAdjustmentResponseDto {
    return {
      adjustmentId: data.adjustment.id,
      adjustmentNumber: data.adjustment.adjustmentNumber,
      locationId: data.adjustment.locationId,
      locationName: data.location?.name || '',
      adjustmentDate: data.adjustment.adjustmentDate,
      adjustmentType: data.adjustment.adjustmentType,
      status: data.adjustment.status,
      reason: data.adjustment.reason,
      notes: data.adjustment.notes,
      createdBy: data.adjustment.createdBy,
      createdByName: data.createdByStaff
        ? `${data.createdByStaff.firstName} ${data.createdByStaff.lastName}`
        : '',
      approvedBy: data.adjustment.approvedBy,
      approvedByName: data.approvedByStaff
        ? `${data.approvedByStaff.firstName} ${data.approvedByStaff.lastName}`
        : null,
      approvedDate: data.adjustment.approvedDate,
      postedDate: data.adjustment.postedDate,
      createdAt: data.adjustment.createdAt,
      updatedAt: data.adjustment.updatedAt,
      lines: lines.map((l) => ({
        adjustmentLineId: l.line.id,
        productId: l.line.productId,
        productCode: l.product?.productCode || '',
        productName: l.product?.productName || '',
        variantId: l.line.variantId,
        variantName: l.variant?.variantName || null,
        currentQuantity: Number(l.line.currentQuantity),
        countedQuantity: Number(l.line.countedQuantity),
        quantityDifference: Number(l.line.quantityDifference),
        unitCost: Number(l.line.unitCost),
        totalValue: Number(l.line.totalValue),
        serialId: l.line.serialId,
        serialNumber: l.serial?.serialNumber || null,
        batchId: l.line.batchId,
        batchNumber: l.batch?.batchNumber || null,
        reason: l.line.reason,
        notes: l.line.notes,
      })),
      totalValue: lines.reduce((sum, l) => sum + Number(l.line.totalValue), 0),
      totalItems: lines.length,
    };
  }

  // Additional methods for physical count support
  async createPhysicalCount(
    userId: string,
    businessId: string,
    locationId: string,
    productIds?: string[],
  ): Promise<StockAdjustmentResponseDto> {
    // Get all inventory items for the location
    const conditions = [eq(inventory.locationId, locationId)];

    if (productIds && productIds.length > 0) {
      conditions.push(inArray(inventory.productId, productIds));
    }

    const inventoryItems = await this.db
      .select({
        inventory: inventory,
        product: products,
        variant: productVariants,
      })
      .from(inventory)
      .leftJoin(products, eq(inventory.productId, products.id))
      .leftJoin(productVariants, eq(inventory.variantId, productVariants.id))
      .where(and(...conditions));

    // Create adjustment with all items set to current quantity
    const lines = inventoryItems.map((item) => ({
      productId: item.inventory.productId,
      variantId: item.inventory.variantId,
      newQuantity: Number(item.inventory.quantityOnHand),
      unitCost: Number(item.product?.standardCost) || 0,
      reason: 'Physical count',
    }));

    return this.create(
      userId,
      businessId,
      {
        locationId,
        adjustmentType: 'physical_count',
        reason: 'Scheduled physical count',
        notes: `Physical count initiated by user ${userId}`,
        lines,
      },
      { source: ActivitySource.WEB },
    );
  }

  async getCountSheet(
    userId: string,
    businessId: string,
    adjustmentId: string,
  ): Promise<any> {
    const adjustment = await this.findOne(userId, businessId, adjustmentId);

    if (adjustment.adjustmentType !== 'physical_count') {
      throw new BadRequestException('Not a physical count adjustment');
    }

    // Format for printing/display
    return {
      adjustmentNumber: adjustment.adjustmentNumber,
      location: adjustment.locationName,
      countDate: adjustment.adjustmentDate,
      items: adjustment.lines.map((line) => ({
        productCode: line.productCode,
        productName: line.productName,
        variantName: line.variantName,
        currentQuantity: line.currentQuantity,
        countedQuantity: '________', // Blank for manual entry
        serialNumber: line.serialNumber,
        batchNumber: line.batchNumber,
      })),
    };
  }
}
