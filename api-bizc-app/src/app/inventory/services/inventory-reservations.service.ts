// services/inventory-reservations.service.ts
import {
  Injectable,
  Inject,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { and, eq, sql, desc, lte, gte, or, isNull } from 'drizzle-orm';
import { DRIZZLE } from '../../drizzle/drizzle.module';
import { DrizzleDB } from '../../drizzle/types/drizzle';
import {
  inventoryReservations,
  inventory,
  stockTransfers,
} from '../../drizzle/schema/inventory.schema';
import {
  products,
  productVariants,
} from '../../drizzle/schema/products.schema';
import { locations } from '../../drizzle/schema/locations.schema';
import { staffMembers } from '../../drizzle/schema/staff.schema';
import { salesOrders } from '../../drizzle/schema/sales-orders.schema';
import { quotations } from '../../drizzle/schema/quotations.schema';
import { customers } from '../../drizzle/schema/customers.schema';
import { ActivityMetadata } from '../../shared/types/activity-metadata.type';
import {
  CreateInventoryReservationDto,
  InventoryReservationListResponseDto,
  InventoryReservationQueryDto,
  InventoryReservationResponseDto,
  InventoryReservationSummaryDto,
  UpdateInventoryReservationDto,
} from '../dto/inventory-reservation.dto';

@Injectable()
export class InventoryReservationsService {
  constructor(@Inject(DRIZZLE) private db: DrizzleDB) {}

  async create(
    userId: string,
    businessId: string,
    createDto: CreateInventoryReservationDto,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    const {
      productId,
      variantId,
      locationId,
      reservationType,
      referenceId,
      quantityReserved,
      expiryDate,
      notes,
    } = createDto;

    // Validate inventory exists and has sufficient quantity
    const [inventoryRecord] = await this.db
      .select()
      .from(inventory)
      .where(
        and(
          eq(inventory.productId, productId.toString()),
          eq(inventory.locationId, locationId.toString()),
          variantId
            ? eq(inventory.variantId, variantId.toString())
            : sql`${inventory.variantId} IS NULL`,
        ),
      );

    if (!inventoryRecord) {
      throw new NotFoundException(
        `Inventory not found for product ${productId} at location ${locationId}`,
      );
    }

    if (Number(inventoryRecord.quantityAvailable) < quantityReserved) {
      throw new BadRequestException(
        `Insufficient inventory available. Available: ${inventoryRecord.quantityAvailable}, Requested: ${quantityReserved}`,
      );
    }

    // Validate reference exists based on type
    await this.validateReference(reservationType, referenceId);

    return await this.db.transaction(async (tx) => {
      // Create reservation
      const [reservation] = await tx
        .insert(inventoryReservations)
        .values({
          businessId,
          productId: productId.toString(),
          variantId: variantId ? variantId.toString() : null,
          locationId: locationId.toString(),
          reservationType: reservationType as any,
          referenceId: referenceId.toString(),
          quantityReserved: quantityReserved.toString(),
          expiryDate: expiryDate
            ? new Date(expiryDate)
            : this.getDefaultExpiryDate(reservationType),
          status: 'active',
          notes,
          reservedBy: userId,
          createdBy: userId,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      // Update inventory quantities
      await tx
        .update(inventory)
        .set({
          quantityReserved: sql`${inventory.quantityReserved} + ${quantityReserved}`,
          quantityAvailable: sql`${inventory.quantityAvailable} - ${quantityReserved}`,
          updatedAt: new Date(),
        })
        .where(eq(inventory.id, inventoryRecord.id));

      return this.findOne(userId, businessId, reservation.id);
    });
  }

  async findOne(
    userId: string,
    businessId: string,
    reservationId: string,
  ): Promise<InventoryReservationResponseDto> {
    const [reservation] = await this.db
      .select({
        reservation: inventoryReservations,
        product: products,
        variant: productVariants,
        location: locations,
        createdByStaff: staffMembers,
      })
      .from(inventoryReservations)
      .leftJoin(products, eq(inventoryReservations.productId, products.id))
      .leftJoin(
        productVariants,
        eq(inventoryReservations.variantId, productVariants.id),
      )
      .leftJoin(locations, eq(inventoryReservations.locationId, locations.id))
      .leftJoin(
        staffMembers,
        eq(inventoryReservations.reservedBy, staffMembers.id),
      )
      .where(eq(inventoryReservations.id, reservationId));

    if (!reservation) {
      throw new NotFoundException('Reservation not found');
    }

    // Get reference details
    const referenceDetails = await this.getReferenceDetails(
      reservation.reservation.reservationType,
      reservation.reservation.referenceId,
    );

    return this.mapToReservationResponse(reservation, referenceDetails);
  }

  async findAll(
    userId: string,
    businessId: string,
    query: InventoryReservationQueryDto,
  ): Promise<InventoryReservationListResponseDto> {
    const {
      page = 1,
      limit = 20,
      productId,
      locationId,
      status,
      reservationType,
      includeExpired,
    } = query;
    const offset = (page - 1) * limit;

    const conditions = [];

    if (productId) {
      conditions.push(
        eq(inventoryReservations.productId, productId.toString()),
      );
    }

    if (locationId) {
      conditions.push(
        eq(inventoryReservations.locationId, locationId.toString()),
      );
    }

    if (status) {
      conditions.push(eq(inventoryReservations.status, status as any));
    }

    if (reservationType) {
      conditions.push(
        eq(inventoryReservations.reservationType, reservationType as any),
      );
    }

    if (!includeExpired) {
      conditions.push(
        or(
          gte(inventoryReservations.expiryDate, new Date()),
          isNull(inventoryReservations.expiryDate),
          eq(inventoryReservations.status, 'fulfilled'),
        ),
      );
    }

    const reservations = await this.db
      .select({
        reservation: inventoryReservations,
        product: products,
        variant: productVariants,
        location: locations,
        createdByStaff: staffMembers,
      })
      .from(inventoryReservations)
      .leftJoin(products, eq(inventoryReservations.productId, products.id))
      .leftJoin(
        productVariants,
        eq(inventoryReservations.variantId, productVariants.id),
      )
      .leftJoin(locations, eq(inventoryReservations.locationId, locations.id))
      .leftJoin(
        staffMembers,
        eq(inventoryReservations.reservedBy, staffMembers.id),
      )
      .where(and(...conditions))
      .orderBy(desc(inventoryReservations.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ count }] = await this.db
      .select({ count: sql`count(*)` })
      .from(inventoryReservations)
      .where(and(...conditions));

    const data = await Promise.all(
      reservations.map(async (res) => {
        const referenceDetails = await this.getReferenceDetails(
          res.reservation.reservationType,
          res.reservation.referenceId,
        );
        return this.mapToReservationSummary(res, referenceDetails);
      }),
    );

    return {
      data,
      pagination: {
        page,
        limit,
        total: Number(count),
        totalPages: Math.ceil(Number(count) / limit),
      },
    };
  }

  async update(
    userId: string,
    businessId: string,
    reservationId: string,
    updateDto: UpdateInventoryReservationDto,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    const { quantityReserved, expiryDate, notes } = updateDto;

    const [existing] = await this.db
      .select({
        reservation: inventoryReservations,
        inventory: inventory,
      })
      .from(inventoryReservations)
      .leftJoin(
        inventory,
        and(
          eq(inventory.productId, inventoryReservations.productId),
          eq(inventory.locationId, inventoryReservations.locationId),
          or(
            and(
              isNull(inventory.variantId),
              isNull(inventoryReservations.variantId),
            ),
            eq(inventory.variantId, inventoryReservations.variantId),
          ),
        ),
      )
      .where(eq(inventoryReservations.id, reservationId));

    if (!existing) {
      throw new NotFoundException('Reservation not found');
    }

    if (existing.reservation.status !== 'active') {
      throw new BadRequestException('Only active reservations can be updated');
    }

    return await this.db.transaction(async (tx) => {
      // If quantity is being updated
      if (
        quantityReserved !== undefined &&
        Number(existing.reservation.quantityReserved) !== quantityReserved
      ) {
        const quantityDiff =
          quantityReserved - Number(existing.reservation.quantityReserved);

        // Check if increasing reservation
        if (quantityDiff > 0 && existing.inventory) {
          if (Number(existing.inventory.quantityAvailable) < quantityDiff) {
            throw new BadRequestException(
              `Insufficient inventory available. Available: ${existing.inventory.quantityAvailable}, Additional requested: ${quantityDiff}`,
            );
          }
        }

        // Update inventory quantities
        if (existing.inventory) {
          await tx
            .update(inventory)
            .set({
              quantityReserved: sql`${inventory.quantityReserved} + ${quantityDiff}`,
              quantityAvailable: sql`${inventory.quantityAvailable} - ${quantityDiff}`,
              updatedAt: new Date(),
            })
            .where(eq(inventory.id, existing.inventory.id));
        }
      }

      // Update reservation
      const updates: any = {
        updatedAt: new Date(),
      };

      if (quantityReserved !== undefined) {
        updates.quantityReserved = quantityReserved.toString();
      }

      if (expiryDate !== undefined) {
        updates.expiryDate = expiryDate ? new Date(expiryDate) : null;
      }

      if (notes !== undefined) {
        updates.notes = notes;
      }

      await tx
        .update(inventoryReservations)
        .set(updates)
        .where(eq(inventoryReservations.id, reservationId));

      return this.findOne(userId, businessId, reservationId);
    });
  }

  async cancel(
    userId: string,
    businessId: string,
    reservationId: string,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    const [existing] = await this.db
      .select({
        reservation: inventoryReservations,
        inventory: inventory,
      })
      .from(inventoryReservations)
      .leftJoin(
        inventory,
        and(
          eq(inventory.productId, inventoryReservations.productId),
          eq(inventory.locationId, inventoryReservations.locationId),
          or(
            and(
              isNull(inventory.variantId),
              isNull(inventoryReservations.variantId),
            ),
            eq(inventory.variantId, inventoryReservations.variantId),
          ),
        ),
      )
      .where(eq(inventoryReservations.id, reservationId));

    if (!existing) {
      throw new NotFoundException('Reservation not found');
    }

    if (existing.reservation.status !== 'active') {
      throw new BadRequestException(
        'Only active reservations can be cancelled',
      );
    }

    return await this.db.transaction(async (tx) => {
      // Update reservation status
      await tx
        .update(inventoryReservations)
        .set({
          status: 'cancelled',
          updatedAt: new Date(),
        })
        .where(eq(inventoryReservations.id, reservationId));

      // Release reserved inventory
      if (existing.inventory) {
        await tx
          .update(inventory)
          .set({
            quantityReserved: sql`${inventory.quantityReserved} - ${existing.reservation.quantityReserved}`,
            quantityAvailable: sql`${inventory.quantityAvailable} + ${existing.reservation.quantityReserved}`,
            updatedAt: new Date(),
          })
          .where(eq(inventory.id, existing.inventory.id));
      }

      return this.findOne(userId, businessId, reservationId);
    });
  }

  async fulfill(
    userId: string,
    businessId: string,
    reservationId: string,
    metadata: ActivityMetadata,
  ): Promise<InventoryReservationResponseDto> {
    const [existing] = await this.db
      .select({
        reservation: inventoryReservations,
        inventory: inventory,
      })
      .from(inventoryReservations)
      .leftJoin(
        inventory,
        and(
          eq(inventory.productId, inventoryReservations.productId),
          eq(inventory.locationId, inventoryReservations.locationId),
          or(
            and(
              isNull(inventory.variantId),
              isNull(inventoryReservations.variantId),
            ),
            eq(inventory.variantId, inventoryReservations.variantId),
          ),
        ),
      )
      .where(eq(inventoryReservations.id, reservationId));

    if (!existing) {
      throw new NotFoundException('Reservation not found');
    }

    if (existing.reservation.status !== 'active') {
      throw new BadRequestException(
        'Only active reservations can be fulfilled',
      );
    }

    return await this.db.transaction(async (tx) => {
      // Update reservation status
      await tx
        .update(inventoryReservations)
        .set({
          status: 'fulfilled',
          updatedAt: new Date(),
        })
        .where(eq(inventoryReservations.id, reservationId));

      // Update inventory - reduce reserved quantity only (on hand will be reduced by transaction)
      if (existing.inventory) {
        await tx
          .update(inventory)
          .set({
            quantityReserved: sql`${inventory.quantityReserved} - ${existing.reservation.quantityReserved}`,
            updatedAt: new Date(),
          })
          .where(eq(inventory.id, existing.inventory.id));
      }

      return this.findOne(userId, businessId, reservationId);
    });
  }

  async extend(
    userId: string,
    businessId: string,
    reservationId: string,
    newExpiryDate: Date,
  ): Promise<InventoryReservationResponseDto> {
    const [existing] = await this.db
      .select()
      .from(inventoryReservations)
      .where(eq(inventoryReservations.id, reservationId));

    if (!existing) {
      throw new NotFoundException('Reservation not found');
    }

    if (existing.status !== 'active') {
      throw new BadRequestException('Only active reservations can be extended');
    }

    if (new Date(newExpiryDate) <= new Date()) {
      throw new BadRequestException('New expiry date must be in the future');
    }

    await this.db
      .update(inventoryReservations)
      .set({
        expiryDate: new Date(newExpiryDate),
        updatedAt: new Date(),
      })
      .where(eq(inventoryReservations.id, reservationId));

    return this.findOne(userId, businessId, reservationId);
  }

  async processExpiredReservations(
    userId: string,
    businessId: string,
    metadata: ActivityMetadata,
  ): Promise<{ processedCount: number; releasedReservations: number[] }> {
    // Find all expired active reservations
    const expiredReservations = await this.db
      .select({
        reservation: inventoryReservations,
        inventory: inventory,
      })
      .from(inventoryReservations)
      .leftJoin(
        inventory,
        and(
          eq(inventory.productId, inventoryReservations.productId),
          eq(inventory.locationId, inventoryReservations.locationId),
          or(
            and(
              isNull(inventory.variantId),
              isNull(inventoryReservations.variantId),
            ),
            eq(inventory.variantId, inventoryReservations.variantId),
          ),
        ),
      )
      .where(
        and(
          eq(inventoryReservations.status, 'active'),
          lte(inventoryReservations.expiryDate, new Date()),
        ),
      );

    const releasedReservations: number[] = [];

    await this.db.transaction(async (tx) => {
      for (const expired of expiredReservations) {
        // Update reservation status
        await tx
          .update(inventoryReservations)
          .set({
            status: 'expired',
            updatedAt: new Date(),
          })
          .where(eq(inventoryReservations.id, expired.reservation.id));

        // Release reserved inventory
        if (expired.inventory) {
          await tx
            .update(inventory)
            .set({
              quantityReserved: sql`${inventory.quantityReserved} - ${expired.reservation.quantityReserved}`,
              quantityAvailable: sql`${inventory.quantityAvailable} + ${expired.reservation.quantityReserved}`,
              updatedAt: new Date(),
            })
            .where(eq(inventory.id, expired.inventory.id));
        }

        releasedReservations.push(Number(expired.reservation.id));
      }
    });

    return {
      processedCount: expiredReservations.length,
      releasedReservations,
    };
  }

  async getReservationsByReference(
    referenceType: string,
    referenceId: string,
  ): Promise<InventoryReservationResponseDto[]> {
    const reservations = await this.db
      .select({
        reservation: inventoryReservations,
        product: products,
        variant: productVariants,
        location: locations,
        createdByStaff: staffMembers,
      })
      .from(inventoryReservations)
      .leftJoin(products, eq(inventoryReservations.productId, products.id))
      .leftJoin(
        productVariants,
        eq(inventoryReservations.variantId, productVariants.id),
      )
      .leftJoin(locations, eq(inventoryReservations.locationId, locations.id))
      .leftJoin(
        staffMembers,
        eq(inventoryReservations.reservedBy, staffMembers.id),
      )
      .where(
        and(
          eq(inventoryReservations.reservationType, referenceType as any),
          eq(inventoryReservations.referenceId, referenceId.toString()),
        ),
      );

    return Promise.all(
      reservations.map(async (res) => {
        const referenceDetails = await this.getReferenceDetails(
          res.reservation.reservationType,
          res.reservation.referenceId,
        );
        return this.mapToReservationResponse(res, referenceDetails);
      }),
    );
  }

  async getReservationSummary(
    userId: string,
    businessId: string,
    locationId?: number,
  ): Promise<any> {
    const conditions = [eq(inventoryReservations.status, 'active')];

    if (locationId) {
      conditions.push(
        eq(inventoryReservations.locationId, locationId.toString()),
      );
    }

    const summary = await this.db
      .select({
        productId: inventoryReservations.productId,
        productName: products.name,
        productCode: products.sku,
        variantId: inventoryReservations.variantId,
        variantName: productVariants.name,
        locationId: inventoryReservations.locationId,
        locationName: locations.name,
        totalReserved: sql<number>`SUM(${inventoryReservations.quantityReserved})`,
        reservationCount: sql<number>`COUNT(*)`,
      })
      .from(inventoryReservations)
      .leftJoin(products, eq(inventoryReservations.productId, products.id))
      .leftJoin(
        productVariants,
        eq(inventoryReservations.variantId, productVariants.id),
      )
      .leftJoin(locations, eq(inventoryReservations.locationId, locations.id))
      .where(and(...conditions))
      .groupBy(
        inventoryReservations.productId,
        products.name,
        products.sku,
        inventoryReservations.variantId,
        productVariants.name,
        inventoryReservations.locationId,
        locations.name,
      );

    return summary.map((item) => ({
      productId: item.productId,
      productName: item.productName,
      productCode: item.productCode,
      variantId: item.variantId,
      variantName: item.variantName,
      locationId: item.locationId,
      locationName: item.locationName,
      totalReserved: Number(item.totalReserved),
      reservationCount: Number(item.reservationCount),
    }));
  }

  // Helper methods
  private async validateReference(
    type: string,
    referenceId: number | string,
  ): Promise<void> {
    let exists = false;

    switch (type) {
      case 'quotation': {
        const [quotation] = await this.db
          .select()
          .from(quotations)
          .where(eq(quotations.id, referenceId.toString()));
        exists = !!quotation;
        break;
      }

      case 'sales_order': {
        const [salesOrder] = await this.db
          .select()
          .from(salesOrders)
          .where(eq(salesOrders.id, referenceId.toString()));
        exists = !!salesOrder;
        break;
      }

      case 'transfer': {
        const [transfer] = await this.db
          .select()
          .from(stockTransfers)
          .where(eq(stockTransfers.id, referenceId.toString()));
        exists = !!transfer;
        break;
      }

      case 'hold':
        // Generic hold doesn't require validation
        exists = true;
        break;

      default:
        throw new BadRequestException(`Invalid reservation type: ${type}`);
    }

    if (!exists) {
      throw new NotFoundException(
        `Reference ${type} with ID ${referenceId} not found`,
      );
    }
  }

  private async getReferenceDetails(
    type: string,
    referenceId: string,
  ): Promise<any> {
    let details: any = {};

    switch (type) {
      case 'quotation': {
        const [quotation] = await this.db
          .select({
            quotationNumber: quotations.quotationNumber,
            customerName: customers.customerDisplayName,
          })
          .from(quotations)
          .leftJoin(customers, eq(quotations.customerId, customers.id))
          .where(eq(quotations.id, referenceId.toString()));

        if (quotation) {
          details = {
            referenceNumber: quotation.quotationNumber,
            referenceName: `Quote for ${quotation.customerName}`,
          };
        }
        break;
      }

      case 'sales_order': {
        const [salesOrder] = await this.db
          .select({
            orderNumber: salesOrders.orderNumber,
            customerName: customers.customerDisplayName,
          })
          .from(salesOrders)
          .leftJoin(customers, eq(salesOrders.customerId, customers.id))
          .where(eq(salesOrders.id, referenceId.toString()));

        if (salesOrder) {
          details = {
            referenceNumber: salesOrder.orderNumber,
            referenceName: `Order for ${salesOrder.customerName}`,
          };
        }
        break;
      }

      case 'transfer': {
        const [transfer] = await this.db
          .select({
            transferNumber: stockTransfers.transferNumber,
          })
          .from(stockTransfers)
          .where(eq(stockTransfers.id, referenceId.toString()));

        if (transfer) {
          details = {
            referenceNumber: transfer.transferNumber,
            referenceName: `Stock Transfer ${transfer.transferNumber}`,
          };
        }
        break;
      }

      case 'hold':
        details = {
          referenceNumber: `HOLD-${referenceId}`,
          referenceName: 'Manual Hold',
        };
        break;
    }

    return details;
  }

  private getDefaultExpiryDate(reservationType: string): Date {
    const expiryDate = new Date();

    switch (reservationType) {
      case 'quotation':
        expiryDate.setDate(expiryDate.getDate() + 30); // 30 days for quotes
        break;
      case 'sales_order':
        expiryDate.setDate(expiryDate.getDate() + 7); // 7 days for orders
        break;
      case 'transfer':
        expiryDate.setDate(expiryDate.getDate() + 3); // 3 days for transfers
        break;
      case 'hold':
        expiryDate.setDate(expiryDate.getDate() + 14); // 14 days for holds
        break;
      default:
        expiryDate.setDate(expiryDate.getDate() + 7); // Default 7 days
    }

    return expiryDate;
  }

  private mapToReservationResponse(
    data: any,
    referenceDetails: any,
  ): InventoryReservationResponseDto {
    return {
      reservationId: data.reservation.id,
      productId: data.reservation.productId,
      productCode: data.product?.sku || '',
      productName: data.product?.name || '',
      variantId: data.reservation.variantId,
      variantName: data.variant?.name || null,
      locationId: data.reservation.locationId,
      locationName: data.location?.name || '',
      reservationType: data.reservation.reservationType,
      referenceId: Number(data.reservation.referenceId),
      referenceNumber: referenceDetails.referenceNumber || '',
      referenceName: referenceDetails.referenceName || '',
      quantityReserved: Number(data.reservation.quantityReserved),
      expiryDate: data.reservation.expiryDate,
      status: data.reservation.status,
      notes: data.reservation.notes,
      createdBy: data.reservation.reservedBy,
      createdByName: data.createdByStaff
        ? `${data.createdByStaff.firstName} ${data.createdByStaff.lastName}`
        : '',
      createdAt: data.reservation.createdAt,
      updatedAt: data.reservation.updatedAt,
    };
  }

  private mapToReservationSummary(
    data: any,
    referenceDetails: any,
  ): InventoryReservationSummaryDto {
    return {
      reservationId: data.reservation.id,
      productCode: data.product?.sku || '',
      productName: data.product?.name || '',
      variantName: data.variant?.name || null,
      locationName: data.location?.name || '',
      reservationType: data.reservation.reservationType,
      referenceNumber: referenceDetails.referenceNumber || '',
      quantityReserved: Number(data.reservation.quantityReserved),
      expiryDate: data.reservation.expiryDate,
      status: data.reservation.status,
      createdAt: data.reservation.createdAt,
    };
  }
}
