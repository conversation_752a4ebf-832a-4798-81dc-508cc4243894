// inventory.module.ts
import { Module } from '@nestjs/common';
import { InventoryController } from './inventory.controller';
import { InventoryService } from './inventory.service';
import { InventoryTransactionsService } from './services/inventory-transactions.service';
import { InventoryReservationsService } from './services/inventory-reservations.service';
import { StockAdjustmentsService } from './services/stock-adjustments.service';
import { StockTransfersService } from './services/stock-transfers.service';
import { DrizzleModule } from '../drizzle/drizzle.module';

@Module({
  imports: [DrizzleModule],
  controllers: [InventoryController],
  providers: [
    InventoryService,
    InventoryTransactionsService,
    InventoryReservationsService,
    StockAdjustmentsService,
    StockTransfersService,
  ],
  exports: [InventoryService],
})
export class InventoryModule {}
