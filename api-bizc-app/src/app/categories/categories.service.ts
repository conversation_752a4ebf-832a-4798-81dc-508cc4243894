import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { CategoryDto } from './dto/category.dto';
import { CategorySlimDto } from './dto/category-slim.dto';
import { CategoryListDto } from './dto/category-list.dto';
import {
  categories,
  categoryLocations,
} from '../drizzle/schema/categories.schema';
import { media } from '../drizzle/schema/media.schema';
import { products } from '../drizzle/schema/products.schema';
import { locations } from '../drizzle/schema/locations.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { CategoryStatus } from '../shared/types';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class CategoriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createCategoryDto: CreateCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a category with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingCategory = await this.db
        .select()
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            ilike(categories.name, createCategoryDto.name),
            eq(categories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingCategory) {
        throw new ConflictException(
          `A category with the name '${createCategoryDto.name}' already exists for this business`,
        );
      }

      // Create slug from name if not provided
      if (!createCategoryDto.slug) {
        createCategoryDto.slug = createCategoryDto.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');
      }

      // Check if a category with the same slug already exists for this business
      if (createCategoryDto.slug) {
        const existingSlugCategory = await this.db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.businessId, businessId),
              eq(categories.slug, createCategoryDto.slug),
              eq(categories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingSlugCategory) {
          throw new ConflictException(
            `A category with the slug '${createCategoryDto.slug}' already exists for this business`,
          );
        }
      }

      // Check if a category with the same shortCode already exists for this business
      if (createCategoryDto.shortCode) {
        const existingShortCodeCategory = await this.db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.businessId, businessId),
              eq(categories.shortCode, createCategoryDto.shortCode),
              eq(categories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingShortCodeCategory) {
          throw new ConflictException(
            `A category with the short code '${createCategoryDto.shortCode}' already exists for this business`,
          );
        }
      }

      // Handle parent category if provided
      if (createCategoryDto.parentId) {
        const parentCategory = await this.db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.id, createCategoryDto.parentId),
              eq(categories.businessId, businessId),
              eq(categories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentCategory) {
          throw new BadRequestException('Parent category not found');
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'categories',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'categories/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      // Use a transaction to ensure position reordering and category creation are atomic
      const newCategory = await this.db.transaction(async (tx) => {
        // Shift all existing categories down by 1 position to make room at position 1
        await this.reorderPositions(
          tx,
          businessId,
          createCategoryDto.parentId,
          1, // Insert at position 1 (first position)
        );

        // Insert new category at position 1
        const [category] = await tx
          .insert(categories)
          .values({
            businessId,
            name: createCategoryDto.name,
            shortCode: createCategoryDto.shortCode,
            parentId: createCategoryDto.parentId,
            description: createCategoryDto.description,
            slug: createCategoryDto.slug,
            availableOnline: createCategoryDto.availableOnline ?? false,
            position: 1, // Always create new categories at position 1 (first)
            color: createCategoryDto.color,
            image: mediaId,
            isAllocatedToAllLocations:
              createCategoryDto.isAllocatedToAllLocations ?? false,
            seoTitle: createCategoryDto.seoTitle,
            seoDescription: createCategoryDto.seoDescription,
            seoKeywords: createCategoryDto.seoKeywords,
            ogImage: ogImageId,
            createdBy: userId,
            status: createCategoryDto.status ?? CategoryStatus.ACTIVE,
          })
          .returning();

        return category;
      });

      // Handle location associations
      await this.manageCategoryLocations(
        newCategory.id,
        businessId,
        createCategoryDto.isAllocatedToAllLocations ?? false,
        createCategoryDto.locationIds,
        userId,
      );

      // Log the category creation activity
      await this.activityLogService.logCreate(
        newCategory.id,
        EntityType.CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newCategory.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create category: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: CategoryDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(categories.isDeleted, false),
      eq(categories.status, CategoryStatus.ACTIVE),
      eq(categories.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(categories.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(categories.createdAt, toDate));
      }
    }

    // Find all categories for the user's active business with pagination
    const result = await this.db
      .select()
      .from(categories)
      .where(and(...whereConditions))
      .orderBy(desc(categories.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(categories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Log the activity
    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logCreate(...);

    return {
      data: await Promise.all(
        result.map((category) => this.mapToCategoryDto(category)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllByBusiness(
    userId: string,
    businessId: string,
  ): Promise<CategoryDto[]> {
    // This method keeps same functionality but we might consider if this is still needed
    // since we now use activeBusinessId by default
    const result = await this.db
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.isDeleted, false),
          eq(categories.status, CategoryStatus.ACTIVE),
          eq(categories.businessId, businessId),
        ),
      )
      .orderBy(asc(categories.position), asc(categories.id));

    return await Promise.all(
      result.map((category) => this.mapToCategoryDto(category)),
    );
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    shortCode?: string,
    status?: string,
    availableOnline?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: CategoryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(categories.isDeleted, false),
      eq(categories.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(categories.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(categories.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches both name and shortCode)
    if (name) {
      whereConditions.push(
        or(
          ilike(categories.name, `%${name}%`),
          ilike(categories.shortCode, `%${name}%`),
        ),
      );
    }

    // Add slug filtering if provided
    if (slug) {
      whereConditions.push(ilike(categories.slug, `%${slug}%`));
    }

    // Add shortCode filtering if provided
    if (shortCode) {
      whereConditions.push(ilike(categories.shortCode, `%${shortCode}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as CategoryStatus);
      whereConditions.push(inArray(categories.status, statusArray));
    }

    // Add availableOnline filtering if provided
    if (availableOnline) {
      // Decode URL-encoded commas and split by comma
      const decodedAvailableOnline = decodeURIComponent(availableOnline);
      const availableOnlineValues = decodedAvailableOnline
        .split(',')
        .map((s) => s.trim());
      if (availableOnlineValues.length === 1) {
        const boolValue = availableOnlineValues[0].toLowerCase() === 'true';
        whereConditions.push(eq(categories.availableOnline, boolValue));
      } else {
        // Handle multiple boolean values (true, false)
        const boolValues = availableOnlineValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(categories.availableOnline, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(categories.availableOnline, false));
        }
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(categories.name, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(categories.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(categories.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${categories.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${categories.name} IS NULL OR ${categories.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${categories.name} IS NOT NULL AND ${categories.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(categories.status, value as CategoryStatus[]),
                  );
                } else {
                  filterConditions.push(eq(categories.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${categories.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${categories.status} != ${value}`);
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as CategoryStatus);
                  filterConditions.push(
                    inArray(categories.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(categories.status, value));
                }
                break;
            }
          } else if (fieldId === 'slug') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(categories.slug, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(categories.slug, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(categories.slug, value));
                break;
              case 'ne':
                filterConditions.push(sql`${categories.slug} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${categories.slug} IS NULL OR ${categories.slug} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${categories.slug} IS NOT NULL AND ${categories.slug} != ''`,
                );
                break;
            }
          } else if (fieldId === 'shortCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(categories.shortCode, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(categories.shortCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(categories.shortCode, value));
                break;
              case 'ne':
                filterConditions.push(sql`${categories.shortCode} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${categories.shortCode} IS NULL OR ${categories.shortCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${categories.shortCode} IS NOT NULL AND ${categories.shortCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'availableOnline') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(categories.availableOnline, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(categories.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(categories.availableOnline, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(
                  eq(categories.availableOnline, !boolValue),
                );
                break;
              }
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const availableOnlineValues = decodedValue
                    .split(',')
                    .map((s) => s.trim());
                  const boolValues = availableOnlineValues.map(
                    (s) => s.toLowerCase() === 'true',
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(categories.availableOnline, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(categories.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(categories.availableOnline, boolValue),
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions with proper indexing strategy
    // Default sort: position ascending, then by ID for consistent pagination when positions are equal
    // This leverages the composite index for optimal performance
    let orderBy = [asc(categories.position), asc(categories.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              // Use name index for sorting
              orderBy = [
                isDesc ? desc(categories.name) : asc(categories.name),
                asc(categories.id), // Secondary sort for consistency
              ];
              break;
            case 'position':
              // Optimized position sorting with secondary sort for pagination consistency
              orderBy = [
                isDesc ? desc(categories.position) : asc(categories.position),
                asc(categories.id), // Use ID for consistent pagination when positions are equal
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(categories.createdAt) : asc(categories.createdAt),
                asc(categories.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(categories.updatedAt) : asc(categories.updatedAt),
                asc(categories.id), // Secondary sort for consistency
              ];
              break;
            case 'productsCount':
            case 'subcategoriesCount':
              // These will be handled with post-query sorting since they are calculated fields
              // Use position as default to maintain performance
              orderBy = [asc(categories.position), asc(categories.id)];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Check if we need to sort by calculated fields
    const needsPostQuerySort =
      sort &&
      (() => {
        try {
          const parsedSort = JSON.parse(sort);
          return (
            parsedSort.length > 0 &&
            (parsedSort[0].id === 'productsCount' ||
              parsedSort[0].id === 'subcategoriesCount')
          );
        } catch {
          return false;
        }
      })();

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: categories.id,
        name: categories.name,
        shortCode: categories.shortCode,
        slug: categories.slug,
        status: categories.status,
        availableOnline: categories.availableOnline,
        position: categories.position,
        parentId: categories.parentId,
        color: categories.color,
        isAllocatedToAllLocations: categories.isAllocatedToAllLocations,
        imageId: categories.image,
        imagePublicUrl: media.publicUrl,
        imageFileName: media.fileName,
        createdAt: categories.createdAt,
        updatedAt: categories.updatedAt,
      })
      .from(categories)
      .leftJoin(media, eq(categories.image, media.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(needsPostQuerySort ? undefined : limit)
      .offset(needsPostQuerySort ? undefined : offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(categories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get parent names and subcategories count
    const categoryIds = result.map((cat) => cat.id);

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: categories.id,
        parentId: categories.parentId,
        parentName: sql<string>`parent_cat.name`.as('parentName'),
      })
      .from(categories)
      .leftJoin(
        sql`${categories} parent_cat`,
        sql`${categories.parentId} = parent_cat.id`,
      )
      .where(
        and(
          inArray(categories.id, categoryIds),
          eq(categories.isDeleted, false),
        ),
      );

    // Get subcategories count for each category
    const subcategoriesCountQuery = await this.db
      .select({
        parentId: categories.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(categories)
      .where(
        and(
          inArray(categories.parentId, categoryIds),
          eq(categories.isDeleted, false),
        ),
      )
      .groupBy(categories.parentId);

    // Get products count for each category
    const productsCountMap = await this.getProductsCountForCategories(
      categoryIds,
      businessId,
    );

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subcategoriesCountMap = new Map(
      subcategoriesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // Generate signed URLs for images and build final data
    let data = await Promise.all(
      result.map(async (category) => {
        let imageUrl: string | undefined;

        if (category.imageFileName) {
          try {
            // Generate signed URL with 60 minutes expiration for the image
            imageUrl = await this.gcsUploadService.generateSignedUrl(
              category.imageFileName,
              'categories', // folder where category images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for category ${category.id} image:`,
              error.message,
            );
            // Fallback to public URL if signed URL generation fails
            imageUrl = category.imagePublicUrl || undefined;
          }
        }

        return {
          id: category.id.toString(),
          name: category.name,
          shortCode: category.shortCode,
          slug: category.slug,
          status: category.status,
          availableOnline: category.availableOnline,
          parentId: category.parentId?.toString(),
          parentName: parentInfoMap.get(category.id) || undefined,
          color: category.color,
          isAllocatedToAllLocations: category.isAllocatedToAllLocations,
          subcategoriesCount: subcategoriesCountMap.get(category.id) || 0,
          productsCount: productsCountMap.get(category.id) || 0,
          image: imageUrl,
        };
      }),
    );

    // Handle post-query sorting for calculated fields
    if (needsPostQuerySort && sort) {
      try {
        const parsedSort = JSON.parse(sort);
        const sortField = parsedSort[0];
        const isDesc = sortField.desc === true;

        if (sortField.id === 'productsCount') {
          data.sort((a, b) => {
            const diff = a.productsCount - b.productsCount;
            return isDesc ? -diff : diff;
          });
        } else if (sortField.id === 'subcategoriesCount') {
          data.sort((a, b) => {
            const diff = a.subcategoriesCount - b.subcategoriesCount;
            return isDesc ? -diff : diff;
          });
        }

        // Apply pagination after sorting
        data = data.slice(offset, offset + limit);
      } catch {
        // Invalid JSON, use unsorted data with pagination
        data = data.slice(offset, offset + limit);
      }
    }

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a category with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingCategory = await this.db
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.businessId, businessId),
          ilike(categories.name, name),
          eq(categories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCategory };
  }

  async checkSlugAvailability(
    userId: string,
    businessId: string | null,
    slug: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a category with the same slug already exists for this business
    const existingCategory = await this.db
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.businessId, businessId),
          eq(categories.slug, slug),
          eq(categories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCategory };
  }

  async checkShortCodeAvailability(
    userId: string,
    businessId: string | null,
    shortCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a category with the same shortCode already exists for this business
    const existingCategory = await this.db
      .select()
      .from(categories)
      .where(
        and(
          eq(categories.businessId, businessId),
          eq(categories.shortCode, shortCode),
          eq(categories.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingCategory };
  }

  async findOne(userId: string, id: string): Promise<CategoryDto> {
    // Get the category
    const category = await this.db
      .select()
      .from(categories)
      .where(and(eq(categories.id, id), eq(categories.isDeleted, false)))
      .then((results) => results[0]);

    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== category.businessId
    ) {
      throw new UnauthorizedException('Access denied to this category');
    }

    return await this.mapToCategoryDto(category);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateCategoryDto: UpdateCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<CategoryDto> {
    // Get the category
    const existingCategory = await this.db
      .select()
      .from(categories)
      .where(and(eq(categories.id, id), eq(categories.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingCategory) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingCategory.businessId) {
      throw new UnauthorizedException('Access denied to update this category');
    }

    // Check for name conflict if name is being updated
    if (
      updateCategoryDto.name &&
      updateCategoryDto.name !== existingCategory.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            ilike(categories.name, updateCategoryDto.name),
            eq(categories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A category with the name '${updateCategoryDto.name}' already exists for this business`,
        );
      }
    }

    // Handle parent category validation if being updated
    if (updateCategoryDto.parentId) {
      // Prevent category from being its own parent
      if (updateCategoryDto.parentId === id) {
        throw new BadRequestException('Category cannot be its own parent');
      }

      const parentCategory = await this.db
        .select()
        .from(categories)
        .where(
          and(
            eq(categories.id, updateCategoryDto.parentId),
            eq(categories.businessId, existingCategory.businessId),
            eq(categories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!parentCategory) {
        throw new BadRequestException('Parent category not found');
      }
    }

    let mediaId = existingCategory.image;
    let ogImageId = existingCategory.ogImage;

    // Handle image update if provided
    if (imageFile) {
      mediaId = await this.mediaService.updateMediaReference(
        existingCategory.image,
        imageFile,
        'categories',
        businessId,
        userId,
      );
    }

    // Handle OG image update if provided
    if (ogImageFile) {
      ogImageId = await this.mediaService.updateMediaReference(
        existingCategory.ogImage,
        ogImageFile,
        'categories/og-images',
        businessId,
        userId,
      );
    }

    // Update slug if name is changing and slug not explicitly provided
    if (
      updateCategoryDto.name &&
      !updateCategoryDto.slug &&
      updateCategoryDto.name !== existingCategory.name
    ) {
      updateCategoryDto.slug = updateCategoryDto.name
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '');
    }

    // Check for slug conflict if slug is being updated
    if (
      updateCategoryDto.slug &&
      updateCategoryDto.slug !== existingCategory.slug
    ) {
      const slugConflict = await this.db
        .select()
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            eq(categories.slug, updateCategoryDto.slug),
            eq(categories.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (slugConflict) {
        throw new ConflictException(
          `A category with the slug '${updateCategoryDto.slug}' already exists for this business`,
        );
      }
    }

    // Check for shortCode conflict if shortCode is being updated
    if (
      updateCategoryDto.shortCode !== undefined &&
      updateCategoryDto.shortCode !== existingCategory.shortCode
    ) {
      if (updateCategoryDto.shortCode) {
        const shortCodeConflict = await this.db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.businessId, businessId),
              eq(categories.shortCode, updateCategoryDto.shortCode),
              eq(categories.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (shortCodeConflict) {
          throw new ConflictException(
            `A category with the short code '${updateCategoryDto.shortCode}' already exists for this business`,
          );
        }
      }
    }

    try {
      // Update the category
      const [updatedCategory] = await this.db
        .update(categories)
        .set({
          ...updateCategoryDto,
          image: mediaId,
          ogImage: ogImageId,
          updatedAt: new Date(),
        })
        .where(eq(categories.id, id))
        .returning();

      // Handle location associations if provided
      if (
        updateCategoryDto.isAllocatedToAllLocations !== undefined ||
        updateCategoryDto.locationIds !== undefined
      ) {
        await this.manageCategoryLocations(
          updatedCategory.id,
          businessId,
          updateCategoryDto.isAllocatedToAllLocations ??
            existingCategory.isAllocatedToAllLocations,
          updateCategoryDto.locationIds,
          userId,
        );
      }

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.CATEGORY,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToCategoryDto(updatedCategory);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update category: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the category
    const existingCategory = await this.db
      .select()
      .from(categories)
      .where(and(eq(categories.id, id), eq(categories.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingCategory) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    if (businessId !== existingCategory.businessId) {
      throw new UnauthorizedException('Access denied to delete this category');
    }

    // Check for child categories
    const childCategories = await this.db
      .select()
      .from(categories)
      .where(and(eq(categories.parentId, id), eq(categories.isDeleted, false)));

    if (childCategories.length > 0) {
      throw new BadRequestException(
        'Cannot delete category with child categories. Please remove or reassign child categories first.',
      );
    }

    // Soft delete the category
    await this.db
      .update(categories)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(categories.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.CATEGORY,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Category with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    categoryIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!categoryIds || categoryIds.length === 0) {
        throw new BadRequestException('No category IDs provided for deletion');
      }

      // Get all categories that exist and belong to the business
      const existingCategories = await this.db
        .select({
          id: categories.id,
          name: categories.name,
          businessId: categories.businessId,
        })
        .from(categories)
        .where(
          and(
            inArray(categories.id, categoryIds),
            eq(categories.businessId, businessId),
            eq(categories.isDeleted, false),
          ),
        );

      if (existingCategories.length === 0) {
        throw new NotFoundException('No valid categories found for deletion');
      }

      // Check if any of the found categories have child categories
      const categoriesWithChildren = await this.db
        .select({
          parentId: categories.parentId,
          childName: categories.name,
        })
        .from(categories)
        .where(
          and(
            inArray(
              categories.parentId,
              existingCategories.map((c) => c.id),
            ),
            eq(categories.isDeleted, false),
          ),
        );

      if (categoriesWithChildren.length > 0) {
        const parentIds = [
          ...new Set(categoriesWithChildren.map((c) => c.parentId)),
        ];
        const parentNames = existingCategories
          .filter((c) => parentIds.includes(c.id))
          .map((c) => c.name);

        throw new BadRequestException(
          `Cannot delete categories with child categories: ${parentNames.join(', ')}. Please remove or reassign child categories first.`,
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const category of existingCategories) {
          // Soft delete the category
          await tx
            .update(categories)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(categories.id, category.id));

          deletedIds.push(category.id);
        }
      });

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.CATEGORY,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { categoryIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} categories`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete categories: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<CategorySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all categories with only essential fields
    const categoryResults = await this.db
      .select({
        id: categories.id,
        name: categories.name,
        position: categories.position,
      })
      .from(categories)
      .where(
        and(
          eq(categories.isDeleted, false),
          eq(categories.status, CategoryStatus.ACTIVE),
          eq(categories.businessId, businessId),
        ),
      )
      .orderBy(asc(categories.position), asc(categories.id));

    return categoryResults.map((category) => ({
      id: category.id.toString(),
      name: category.name,
      position: category.position,
    }));
  }

  async findAllHierarchy(
    userId: string,
    businessId: string | null,
  ): Promise<
    { id: string; name: string; parentId: string | null; position: number }[]
  > {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all categories with only the essential hierarchy fields
    const categoryResults = await this.db
      .select({
        id: categories.id,
        name: categories.name,
        parentId: categories.parentId,
        position: categories.position,
      })
      .from(categories)
      .where(
        and(
          eq(categories.isDeleted, false),
          eq(categories.status, CategoryStatus.ACTIVE),
          eq(categories.businessId, businessId),
        ),
      )
      .orderBy(asc(categories.position), asc(categories.id));

    return categoryResults.map((category) => ({
      id: category.id.toString(),
      name: category.name,
      parentId: category.parentId?.toString() || null,
      position: category.position,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createCategoryDto: CreateCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const category = await this.create(
      userId,
      businessId,
      createCategoryDto,
      imageFile,
      ogImageFile,
      metadata,
    );
    return { id: category.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createCategoriesDto: CreateCategoryDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<CategoryDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createCategoriesDto || createCategoriesDto.length === 0) {
        throw new BadRequestException('No categories provided for creation');
      }

      // Validate that if images are provided, they don't exceed the number of categories
      if (imageFiles && imageFiles.length > createCategoriesDto.length) {
        throw new BadRequestException(
          'Number of images cannot exceed number of categories',
        );
      }

      // Validate imageIndex values if provided
      if (imageFiles) {
        for (let i = 0; i < createCategoriesDto.length; i++) {
          const category = createCategoriesDto[i];
          if (category.imageIndex !== undefined) {
            if (category.imageIndex < 0) {
              throw new BadRequestException(
                `Category "${category.name}" has invalid imageIndex: ${category.imageIndex}. Must be 0 or greater.`,
              );
            }
            if (category.imageIndex >= imageFiles.length) {
              throw new BadRequestException(
                `Category "${category.name}" has imageIndex ${category.imageIndex} but only ${imageFiles.length} images provided.`,
              );
            }
          }
        }
      }

      // Generate slugs for categories that don't have them
      createCategoriesDto.forEach((dto) => {
        if (!dto.slug) {
          dto.slug = dto.name
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '');
        }
      });

      // Check for duplicate names within the request
      const requestNames = createCategoriesDto.map((dto) =>
        dto.name.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate category names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for duplicate slugs within the request
      const requestSlugs = createCategoriesDto
        .map((dto) => dto.slug)
        .filter((slug): slug is string => Boolean(slug));
      const duplicateSlugs = requestSlugs.filter(
        (slug, index) => requestSlugs.indexOf(slug) !== index,
      );
      if (duplicateSlugs.length > 0) {
        throw new BadRequestException(
          `Duplicate category slugs found in request: ${duplicateSlugs.join(', ')}`,
        );
      }

      // Check for duplicate shortCodes within the request
      const requestShortCodes = createCategoriesDto
        .map((dto) => dto.shortCode)
        .filter((shortCode): shortCode is string => Boolean(shortCode));
      const duplicateShortCodes = requestShortCodes.filter(
        (shortCode, index) => requestShortCodes.indexOf(shortCode) !== index,
      );
      if (duplicateShortCodes.length > 0) {
        throw new BadRequestException(
          `Duplicate category short codes found in request: ${duplicateShortCodes.join(', ')}`,
        );
      }

      // Check if any categories with the same names already exist for this business
      const existingCategories = await this.db
        .select()
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            sql`LOWER(${categories.name}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(categories.isDeleted, false),
          ),
        );

      if (existingCategories.length > 0) {
        const existingNames = existingCategories.map((c) => c.name);
        throw new ConflictException(
          `Categories with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      // Check if any categories with the same slugs already exist for this business
      if (requestSlugs.length > 0) {
        const existingSlugCategories = await this.db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.businessId, businessId),
              inArray(categories.slug, requestSlugs),
              eq(categories.isDeleted, false),
            ),
          );

        if (existingSlugCategories.length > 0) {
          const existingSlugs = existingSlugCategories.map((c) => c.slug);
          throw new ConflictException(
            `Categories with the following slugs already exist: ${existingSlugs.join(', ')}`,
          );
        }
      }

      // Check if any categories with the same shortCodes already exist for this business
      if (requestShortCodes.length > 0) {
        const existingShortCodeCategories = await this.db
          .select()
          .from(categories)
          .where(
            and(
              eq(categories.businessId, businessId),
              inArray(categories.shortCode, requestShortCodes),
              eq(categories.isDeleted, false),
            ),
          );

        if (existingShortCodeCategories.length > 0) {
          const existingShortCodes = existingShortCodeCategories.map(
            (c) => c.shortCode,
          );
          throw new ConflictException(
            `Categories with the following short codes already exist: ${existingShortCodes.join(', ')}`,
          );
        }
      }

      const createdCategories: CategoryDto[] = [];

      // Use a transaction to ensure all categories are created or none are
      await this.db.transaction(async (tx) => {
        // Group categories by parent to assign proper sequential positions
        const categoryGroups = new Map<string, CreateCategoryDto[]>();

        for (const dto of createCategoriesDto) {
          const parentKey = dto.parentId || 'root';
          if (!categoryGroups.has(parentKey)) {
            categoryGroups.set(parentKey, []);
          }
          categoryGroups.get(parentKey).push(dto);
        }

        // For each parent group, shift existing categories to make room for new ones at the beginning
        for (const [parentKey, groupCategories] of categoryGroups) {
          const parentId = parentKey === 'root' ? null : parentKey;

          // Shift existing categories down by the number of new categories being added
          await tx
            .update(categories)
            .set({
              position: sql`${categories.position} + ${groupCategories.length}`,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(categories.businessId, businessId),
                parentId
                  ? eq(categories.parentId, parentId)
                  : isNull(categories.parentId),
                eq(categories.isDeleted, false),
              ),
            );
        }

        for (let i = 0; i < createCategoriesDto.length; i++) {
          const createCategoryDto = createCategoriesDto[i];
          const parentKey = createCategoryDto.parentId || 'root';

          // Calculate position within the parent group (starting from position 1)
          const groupCategories = categoryGroups.get(parentKey);
          const indexInGroup = groupCategories.indexOf(createCategoryDto);
          const categoryPosition = indexInGroup + 1; // Start from position 1

          // Get image file based on imageIndex if specified, otherwise use array index
          let imageFile: Express.Multer.File | undefined;
          if (createCategoryDto.imageIndex !== undefined) {
            // Use specific image index if provided
            imageFile = imageFiles?.[createCategoryDto.imageIndex];
          } else {
            // Fallback to array index mapping for backward compatibility
            imageFile = imageFiles?.[i];
          }

          let mediaId: string | undefined;

          // Upload image if provided for this category
          if (imageFile) {
            const uploadedMedia = await this.mediaService.uploadMedia(
              imageFile,
              'categories',
              businessId,
              userId,
            );
            mediaId = uploadedMedia.id;
          }

          // Create the category with proper position (starting from position 1)
          const [newCategory] = await tx
            .insert(categories)
            .values({
              businessId,
              name: createCategoryDto.name,
              shortCode: createCategoryDto.shortCode,
              parentId: createCategoryDto.parentId,
              description: createCategoryDto.description,
              slug: createCategoryDto.slug,
              availableOnline: createCategoryDto.availableOnline ?? false,
              position: categoryPosition, // Position starts from 1 for new categories
              color: createCategoryDto.color,
              image: mediaId,
              isAllocatedToAllLocations:
                createCategoryDto.isAllocatedToAllLocations ?? false,
              seoTitle: createCategoryDto.seoTitle,
              seoDescription: createCategoryDto.seoDescription,
              seoKeywords: createCategoryDto.seoKeywords,
              createdBy: userId,
              status: createCategoryDto.status ?? CategoryStatus.ACTIVE,
            })
            .returning();

          // Handle location associations
          await this.manageCategoryLocations(
            newCategory.id,
            businessId,
            createCategoryDto.isAllocatedToAllLocations ?? false,
            createCategoryDto.locationIds,
            userId,
          );

          createdCategories.push(await this.mapToCategoryDto(newCategory));
        }
      });

      // Log bulk create operation
      const createdIds = createdCategories.map((cat) => cat.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.CATEGORY,
        createdIds,
        {
          names: createCategoriesDto.map((dto) => dto.name),
          status: CategoryStatus.ACTIVE,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createCategoriesDto.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return createdCategories;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create categories: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createCategoriesDto: CreateCategoryDto[],
    imageFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const categories = await this.bulkCreate(
      userId,
      businessId,
      createCategoriesDto,
      imageFiles,
      metadata,
    );
    return { ids: categories.map((category) => category.id) };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateCategoryDto: UpdateCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateCategoryDto,
      imageFile,
      ogImageFile,
      metadata,
    );
    return { id };
  }

  async updateCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for category ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all categories belong to the business
      const categoryIds = updates.map((update) => update.id);
      const existingCategories = await this.db
        .select({
          id: categories.id,
          parentId: categories.parentId,
          currentPosition: categories.position,
        })
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            inArray(categories.id, categoryIds),
            eq(categories.isDeleted, false),
          ),
        );

      if (existingCategories.length !== categoryIds.length) {
        const foundIds = existingCategories.map((cat) => cat.id);
        const missingIds = categoryIds.filter((id) => !foundIds.includes(id));
        throw new BadRequestException(
          `Categories not found or don't belong to this business: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdatePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Only normalize positions if there are gaps or conflicts
        // This is much more efficient than always normalizing
        await this.conditionalNormalizePositions(tx, businessId);
      });

      // Log bulk position update operation
      const updatedIds = updates.map((update) => update.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.CATEGORY,
        updatedIds,
        { positionsUpdated: true },
        userId,
        businessId,
        {
          filterCriteria: { positionUpdates: updates.length },
          executionStrategy: ExecutionStrategy.PARALLEL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update category positions: ${error.message}`,
      );
    }
  }

  async bulkUpdateCategoryHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      const failed: Array<{ id: string; error: string }> = [];
      let updatedCount = 0;

      // Validate that all categories belong to the business
      const categoryIds = updates.map((update) => update.id);
      const existingCategories = await this.db
        .select({ id: categories.id, parentId: categories.parentId })
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            inArray(categories.id, categoryIds),
            eq(categories.isDeleted, false),
          ),
        );

      const existingCategoryMap = new Map(
        existingCategories.map((cat) => [cat.id, cat]),
      );

      // Use a transaction to ensure data consistency
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          try {
            // Check if category exists
            if (!existingCategoryMap.has(update.id)) {
              failed.push({
                id: update.id,
                error: 'Category not found or does not belong to this business',
              });
              continue;
            }

            // Check if parent exists (if parentId is not null)
            if (update.parentId !== null) {
              const parentExists = await tx
                .select({ id: categories.id })
                .from(categories)
                .where(
                  and(
                    eq(categories.id, update.parentId),
                    eq(categories.businessId, businessId),
                    eq(categories.isDeleted, false),
                  ),
                )
                .limit(1);

              if (parentExists.length === 0) {
                failed.push({
                  id: update.id,
                  error: 'Parent category not found',
                });
                continue;
              }

              // Check for circular reference (category cannot be its own ancestor)
              if (update.parentId === update.id) {
                failed.push({
                  id: update.id,
                  error: 'Category cannot be its own parent',
                });
                continue;
              }

              // Check if this would create a circular reference
              const wouldCreateCircle = async (
                parentId: string,
                childId: string,
              ): Promise<boolean> => {
                const parent = await tx
                  .select({ parentId: categories.parentId })
                  .from(categories)
                  .where(
                    and(
                      eq(categories.id, parentId),
                      eq(categories.businessId, businessId),
                      eq(categories.isDeleted, false),
                    ),
                  )
                  .limit(1);

                if (parent.length === 0) return false;
                if (parent[0].parentId === childId) return true;
                if (parent[0].parentId)
                  return wouldCreateCircle(parent[0].parentId, childId);
                return false;
              };

              if (await wouldCreateCircle(update.parentId, update.id)) {
                failed.push({
                  id: update.id,
                  error: 'Would create circular reference',
                });
                continue;
              }
            }

            // Update the category
            await tx
              .update(categories)
              .set({
                parentId: update.parentId,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(categories.id, update.id),
                  eq(categories.businessId, businessId),
                  eq(categories.isDeleted, false),
                ),
              );

            updatedCount++;

            // Log the hierarchy update activity
            await this.activityLogService.logUpdate(
              update.id,
              EntityType.CATEGORY,
              userId,
              businessId,
              {
                ipAddress: metadata?.ipAddress,
                userAgent: metadata?.userAgent,
                sessionId: metadata?.sessionId,
              },
            );
          } catch (error) {
            failed.push({
              id: update.id,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return { updated: updatedCount, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update category hierarchy: ${error.message}`,
      );
    }
  }

  async bulkUpdateCategoryStatus(
    userId: string,
    businessId: string | null,
    categoryIds: string[],
    status: CategoryStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ categoryId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!categoryIds || categoryIds.length === 0) {
        throw new BadRequestException(
          'No category IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ categoryId: string; error: string }> = [];

      // Process each category ID
      await this.db.transaction(async (tx) => {
        for (const categoryId of categoryIds) {
          try {
            // Check if category exists and belongs to the business
            const existingCategory = await tx
              .select()
              .from(categories)
              .where(
                and(
                  eq(categories.id, categoryId),
                  eq(categories.businessId, businessId),
                  eq(categories.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingCategory) {
              failed.push({
                categoryId,
                error: 'Category not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingCategory.status === status) {
              failed.push({
                categoryId,
                error: `Category already has status: ${status}`,
              });
              continue;
            }

            // Update the category status
            await tx
              .update(categories)
              .set({
                status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(categories.id, categoryId),
                  eq(categories.businessId, businessId),
                  eq(categories.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(categoryId);
          } catch (error) {
            failed.push({
              categoryId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      // Log bulk status change operation if any categories were updated
      if (updatedIds.length > 0) {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_STATUS_CHANGE,
          EntityType.CATEGORY,
          updatedIds,
          { status },
          userId,
          businessId,
          {
            filterCriteria: { categoryIds, targetStatus: status },
            failures: failed.map((f) => ({ id: f.categoryId, error: f.error })),
            executionStrategy: ExecutionStrategy.SEQUENTIAL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update category status: ${error.message}`,
      );
    }
  }

  private async getProductsCountForCategory(
    categoryId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            or(
              eq(products.categoryId, categoryId),
              eq(products.subCategoryId, categoryId),
            ),
            eq(products.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get products count for category ${categoryId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getProductsCountForCategories(
    categoryIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (categoryIds.length === 0) {
        return new Map();
      }

      // Count products where categoryId matches
      const categoryResults = await this.db
        .select({
          categoryId: products.categoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            inArray(products.categoryId, categoryIds),
            eq(products.isDeleted, false),
          ),
        )
        .groupBy(products.categoryId);

      // Count products where subCategoryId matches
      const subCategoryResults = await this.db
        .select({
          subCategoryId: products.subCategoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            inArray(products.subCategoryId, categoryIds),
            eq(products.isDeleted, false),
          ),
        )
        .groupBy(products.subCategoryId);

      // Combine the counts
      const countsMap = new Map<string, number>();

      // Initialize all category IDs with 0
      categoryIds.forEach((id) => countsMap.set(id, 0));

      // Add category counts
      categoryResults.forEach((result) => {
        if (result.categoryId) {
          countsMap.set(
            result.categoryId,
            (countsMap.get(result.categoryId) || 0) + result.count,
          );
        }
      });

      // Add subcategory counts
      subCategoryResults.forEach((result) => {
        if (result.subCategoryId) {
          countsMap.set(
            result.subCategoryId,
            (countsMap.get(result.subCategoryId) || 0) + result.count,
          );
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get products count for categories:',
        error.message,
      );
      return new Map();
    }
  }

  private async mapToCategoryDto(
    category: typeof categories.$inferSelect,
  ): Promise<CategoryDto> {
    // Get products count for this category
    const productsCount = await this.getProductsCountForCategory(
      category.id,
      category.businessId,
    );

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      category.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (category.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        category.updatedBy.toString(),
      );
    }

    // Get category locations if not allocated to all locations
    let locations: { id: string; name: string }[] = [];
    if (!category.isAllocatedToAllLocations) {
      locations = await this.getCategoryLocations(category.id);
    }

    const categoryDto: CategoryDto = {
      id: category.id.toString(),
      businessId: category.businessId.toString(),
      name: category.name,
      shortCode: category.shortCode,
      parentId: category.parentId?.toString(),
      description: category.description,
      slug: category.slug,
      availableOnline: category.availableOnline,
      position: category.position,
      color: category.color,
      isAllocatedToAllLocations: category.isAllocatedToAllLocations,
      locations,
      seoTitle: category.seoTitle,
      seoDescription: category.seoDescription,
      seoKeywords: category.seoKeywords,
      createdBy: createdByName,
      updatedBy: updatedByName,
      status: category.status,
      productsCount,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };

    // Fetch media information and generate signed URL if image exists
    if (category.image) {
      try {
        const mediaData = await this.mediaService.findById(
          category.image,
          category.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the image
        categoryDto.image = await this.gcsUploadService.generateSignedUrl(
          mediaData.fileName,
          'categories', // folder where category images are stored
          60, // expiration in minutes
        );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for category ${category.id} image:`,
          error.message,
        );
      }
    }

    // Fetch OG image information and generate signed URL if ogImage exists
    if (category.ogImage) {
      try {
        const ogMediaData = await this.mediaService.findById(
          category.ogImage,
          category.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the OG image
        categoryDto.ogImage = await this.gcsUploadService.generateSignedUrl(
          ogMediaData.fileName,
          'categories/og-images', // folder where OG images are stored
          60, // expiration in minutes
        );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for category ${category.id} OG image:`,
          error.message,
        );
      }
    }

    return categoryDto;
  }

  /**
   * Conditionally normalize positions only when needed
   * This is much more efficient than always normalizing
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizePositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          parentId: categories.parentId,
          positions: sql<
            number[]
          >`array_agg(${categories.position} ORDER BY ${categories.position})`,
          count: sql<number>`count(*)`,
        })
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            eq(categories.isDeleted, false),
          ),
        )
        .groupBy(categories.parentId);

      const needsNormalization = positionCheck.some((group) => {
        const positions = group.positions;
        const expectedPositions = Array.from(
          { length: group.count },
          (_, i) => i + 1,
        );
        return JSON.stringify(positions) !== JSON.stringify(expectedPositions);
      });

      if (needsNormalization) {
        await this.normalizePositions(tx, businessId);
      }
    } catch (error) {
      console.warn('Failed to check position normalization:', error.message);
      // Fallback to full normalization if check fails
      await this.normalizePositions(tx, businessId);
    }
  }

  /**
   * Normalize positions to ensure sequential ordering without gaps
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async normalizePositions(tx: any, businessId: string): Promise<void> {
    try {
      // Get all categories ordered by current position and createdAt
      const allCategories = await tx
        .select({
          id: categories.id,
          parentId: categories.parentId,
          position: categories.position,
        })
        .from(categories)
        .where(
          and(
            eq(categories.businessId, businessId),
            eq(categories.isDeleted, false),
          ),
        )
        .orderBy(asc(categories.position), asc(categories.createdAt));

      // Group categories by parent level
      const categoryGroups = new Map<string, typeof allCategories>();

      for (const category of allCategories) {
        const parentKey = category.parentId || 'root';
        if (!categoryGroups.has(parentKey)) {
          categoryGroups.set(parentKey, []);
        }
        categoryGroups.get(parentKey)!.push(category);
      }

      // Batch normalize positions for each group using optimized queries
      for (const [, groupCategories] of categoryGroups) {
        const updates = groupCategories
          .map((category, index) => ({
            id: category.id,
            position: index + 1,
          }))
          .filter(
            (update, index) =>
              groupCategories[index].position !== update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdatePositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn('Failed to normalize positions:', error.message);
      // Don't throw error as this is a cleanup operation
    }
  }

  /**
   * Optimized reorder positions when inserting a category at a specific position
   * Uses batch updates and optimized queries for better performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param parentId - The parent category ID
   * @param insertPosition - The position where the new category will be inserted
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    parentId: string | null,
    insertPosition: number,
  ): Promise<void> {
    try {
      // Use a single optimized query with proper indexing
      // This query will be much faster with the composite index on (businessId, parentId, position, deletedAt)
      await tx
        .update(categories)
        .set({
          position: sql`${categories.position} + 1`,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(categories.businessId, businessId),
            parentId
              ? eq(categories.parentId, parentId)
              : isNull(categories.parentId),
            gte(categories.position, insertPosition),
            eq(categories.isDeleted, false),
          ),
        );
    } catch (error) {
      console.warn('Failed to reorder positions:', error.message);
      throw error; // This is critical for data consistency
    }
  }

  /**
   * Optimized batch position update method
   * Reduces the number of database operations and improves performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdatePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(categories)
            .set({
              position: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(categories.id, update.id),
                eq(categories.businessId, businessId),
                eq(categories.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      // This approach is more reliable than raw SQL with arrays
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(categories)
          .set({
            position: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(categories.id, update.id),
              eq(categories.businessId, businessId),
              eq(categories.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update positions:', error.message);
      throw error;
    }
  }

  /**
   * Validate location IDs belong to the business
   */
  private async validateLocationIds(
    businessId: string,
    locationIds: string[],
  ): Promise<void> {
    if (!locationIds || locationIds.length === 0) {
      return;
    }

    const validLocations = await this.db
      .select({ id: locations.id })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          inArray(locations.id, locationIds),
          eq(locations.isDeleted, false),
        ),
      );

    if (validLocations.length !== locationIds.length) {
      throw new BadRequestException(
        'Some location IDs do not exist or belong to this business',
      );
    }
  }

  /**
   * Manage category location associations
   */
  private async manageCategoryLocations(
    categoryId: string,
    businessId: string,
    isAllocatedToAllLocations: boolean,
    locationIds: string[] | undefined,
    userId: string,
  ): Promise<void> {
    // Delete existing location associations
    await this.db
      .delete(categoryLocations)
      .where(eq(categoryLocations.categoryId, categoryId));

    // If allocated to all locations, no need to create specific associations
    if (isAllocatedToAllLocations) {
      return;
    }

    // If specific locations are provided, create associations
    if (locationIds && locationIds.length > 0) {
      await this.validateLocationIds(businessId, locationIds);

      const locationAssociations = locationIds.map((locationId) => ({
        businessId,
        categoryId,
        locationId,
        createdBy: userId,
      }));

      await this.db.insert(categoryLocations).values(locationAssociations);
    }
  }

  /**
   * Get category location associations
   */
  private async getCategoryLocations(categoryId: string): Promise<
    {
      id: string;
      name: string;
    }[]
  > {
    const categoryLocationData = await this.db
      .select({
        id: locations.id,
        name: locations.name,
      })
      .from(categoryLocations)
      .innerJoin(locations, eq(categoryLocations.locationId, locations.id))
      .where(
        and(
          eq(categoryLocations.categoryId, categoryId),
          eq(locations.isDeleted, false),
        ),
      )
      .orderBy(locations.name);

    return categoryLocationData;
  }
}
