import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import {
  and,
  eq,
  isNull,
  ilike,
  gte,
  lte,
  asc,
  desc,
  inArray,
  sql,
} from 'drizzle-orm';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivitySource,
  ActivityType,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import {
  subscriptionPlans,
  subscriptionPlanProducts,
  subscriptionPlanServices,
  SubscriptionPlanStatus,
} from '../drizzle/schema/subscription-plans.schema';
import { users } from '../drizzle/schema/users.schema';
import { products, productVariants } from '../drizzle/schema/products.schema';
import { services } from '../drizzle/schema/services.schema';
import { units } from '../drizzle/schema/units.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import { CreateSubscriptionPlanDto } from './dto/create-subscription-plan.dto';
import { UpdateSubscriptionPlanDto } from './dto/update-subscription-plan.dto';
import { SubscriptionPlanDto } from './dto/subscription-plan.dto';
import { SubscriptionPlanListDto } from './dto/subscription-plan-list.dto';
import { SubscriptionPlanSlimDto } from './dto/subscription-plan-slim.dto';

@Injectable()
export class SubscriptionPlansService {
  constructor(
    @Inject(DRIZZLE) private readonly db: NodePgDatabase<any>,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createSubscriptionPlanDto: CreateSubscriptionPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a subscription plan with the same name already exists for this business
      const existingSubscriptionPlan = await this.db
        .select()
        .from(subscriptionPlans)
        .where(
          and(
            eq(subscriptionPlans.businessId, businessId),
            ilike(subscriptionPlans.name, createSubscriptionPlanDto.name),
            isNull(subscriptionPlans.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingSubscriptionPlan) {
        throw new ConflictException(
          `Subscription plan with name "${createSubscriptionPlanDto.name}" already exists`,
        );
      }

      // Validate referenced entities exist
      await this.validateReferencedEntities(
        businessId,
        createSubscriptionPlanDto.taxId,
        createSubscriptionPlanDto.incomeAccountId,
        createSubscriptionPlanDto.expenseAccountId,
        createSubscriptionPlanDto.products,
        createSubscriptionPlanDto.services,
      );

      // Start transaction
      const result = await this.db.transaction(async (tx) => {
        // Create the subscription plan
        const [newSubscriptionPlan] = await tx
          .insert(subscriptionPlans)
          .values({
            businessId,
            name: createSubscriptionPlanDto.name,
            description: createSubscriptionPlanDto.description,
            status:
              createSubscriptionPlanDto.status || SubscriptionPlanStatus.ACTIVE,
            basePrice: createSubscriptionPlanDto.basePrice.toString(),
            taxType: createSubscriptionPlanDto.taxType,
            taxId: createSubscriptionPlanDto.taxId,
            standardCost: createSubscriptionPlanDto.standardCost.toString(),
            incomeAccountId: createSubscriptionPlanDto.incomeAccountId,
            expenseAccountId: createSubscriptionPlanDto.expenseAccountId,
            netDays: createSubscriptionPlanDto.netDays || 30,
            recurrencePattern: createSubscriptionPlanDto.recurrencePattern,
            recurrenceInterval:
              createSubscriptionPlanDto.recurrenceInterval || 1,
            recurrenceEndType: createSubscriptionPlanDto.recurrenceEndType,
            recurrenceEndDate: createSubscriptionPlanDto.recurrenceEndDate,
            recurrenceEndAfterOccurrences:
              createSubscriptionPlanDto.recurrenceEndAfterOccurrences,
            recurrenceDaysOfWeek:
              createSubscriptionPlanDto.recurrenceDaysOfWeek,
            recurrenceDayOfMonth:
              createSubscriptionPlanDto.recurrenceDayOfMonth,
            recurrenceMonthOfYear:
              createSubscriptionPlanDto.recurrenceMonthOfYear,
            nextOccurrenceDate: createSubscriptionPlanDto.nextOccurrenceDate,
            occurrenceCount: 0,
            createdBy: userId,
          })
          .returning({ id: subscriptionPlans.id });

        // Create product associations
        if (
          createSubscriptionPlanDto.products &&
          createSubscriptionPlanDto.products.length > 0
        ) {
          await tx.insert(subscriptionPlanProducts).values(
            createSubscriptionPlanDto.products.map((product) => ({
              subscriptionPlanId: newSubscriptionPlan.id,
              productId: product.productId,
              variantId: product.variantId,
              quantity: product.quantity.toString(),
              standardUnitOfMeasure: product.standardUnitOfMeasure,
              customUnitId: product.customUnitId,
              createdBy: userId,
            })),
          );
        }

        // Create service associations
        if (
          createSubscriptionPlanDto.services &&
          createSubscriptionPlanDto.services.length > 0
        ) {
          await tx.insert(subscriptionPlanServices).values(
            createSubscriptionPlanDto.services.map((service) => ({
              subscriptionPlanId: newSubscriptionPlan.id,
              serviceId: service.serviceId,
              createdBy: userId,
            })),
          );
        }

        return newSubscriptionPlan;
      });

      // Log the activity
      await this.activityLogService.logCreate(
        result.id,
        EntityType.SUBSCRIPTION_PLAN,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id: result.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create subscription plan: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createSubscriptionPlanDto: CreateSubscriptionPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createSubscriptionPlanDto, metadata);
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createSubscriptionPlanDtos: CreateSubscriptionPlanDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (
      !createSubscriptionPlanDtos ||
      createSubscriptionPlanDtos.length === 0
    ) {
      throw new BadRequestException(
        'No subscription plans provided for bulk creation',
      );
    }

    // Check for duplicate names within the request
    const names = createSubscriptionPlanDtos.map((dto) =>
      dto.name.toLowerCase(),
    );
    const duplicateNames = names.filter(
      (name, index) => names.indexOf(name) !== index,
    );
    if (duplicateNames.length > 0) {
      throw new ConflictException(
        `Duplicate subscription plan names in request: ${duplicateNames.join(', ')}`,
      );
    }

    // Check for existing names in database
    const existingSubscriptionPlans = await this.db
      .select({ name: subscriptionPlans.name })
      .from(subscriptionPlans)
      .where(
        and(
          eq(subscriptionPlans.businessId, businessId),
          inArray(sql`LOWER(${subscriptionPlans.name})`, names),
          isNull(subscriptionPlans.deletedAt),
        ),
      );

    if (existingSubscriptionPlans.length > 0) {
      const existingNames = existingSubscriptionPlans.map((sp) => sp.name);
      throw new ConflictException(
        `Subscription plans with these names already exist: ${existingNames.join(', ')}`,
      );
    }

    const createdIds: string[] = [];

    // Process each subscription plan
    for (const createDto of createSubscriptionPlanDtos) {
      const result = await this.create(userId, businessId, createDto, metadata);
      createdIds.push(result.id);
    }

    return { ids: createdIds };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createSubscriptionPlanDtos: CreateSubscriptionPlanDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(
      userId,
      businessId,
      createSubscriptionPlanDtos,
      metadata,
    );
  }

  private async validateReferencedEntities(
    businessId: string,
    taxId: string,
    incomeAccountId: string,
    expenseAccountId?: string,
    productDtos?: any[],
    serviceDtos?: any[],
  ): Promise<void> {
    // Validate tax exists
    const tax = await this.db
      .select()
      .from(taxes)
      .where(and(eq(taxes.id, taxId), eq(taxes.businessId, businessId)))
      .then((results) => results[0]);

    if (!tax) {
      throw new BadRequestException(`Tax with ID ${taxId} not found`);
    }

    // Validate income account exists
    const incomeAccount = await this.db
      .select()
      .from(accounts)
      .where(
        and(
          eq(accounts.id, incomeAccountId),
          eq(accounts.businessId, businessId),
        ),
      )
      .then((results) => results[0]);

    if (!incomeAccount) {
      throw new BadRequestException(
        `Income account with ID ${incomeAccountId} not found`,
      );
    }

    // Validate expense account if provided
    if (expenseAccountId) {
      const expenseAccount = await this.db
        .select()
        .from(accounts)
        .where(
          and(
            eq(accounts.id, expenseAccountId),
            eq(accounts.businessId, businessId),
          ),
        )
        .then((results) => results[0]);

      if (!expenseAccount) {
        throw new BadRequestException(
          `Expense account with ID ${expenseAccountId} not found`,
        );
      }
    }

    // Validate products if provided
    if (productDtos && productDtos.length > 0) {
      const productIds = productDtos.map((p) => p.productId);
      const existingProducts = await this.db
        .select({ id: products.id })
        .from(products)
        .where(
          and(
            inArray(products.id, productIds),
            eq(products.businessId, businessId),
          ),
        );

      if (existingProducts.length !== productIds.length) {
        throw new BadRequestException('One or more products not found');
      }

      // Validate variants if provided
      const variantIds = productDtos
        .filter((p) => p.variantId)
        .map((p) => p.variantId);
      if (variantIds.length > 0) {
        const existingVariants = await this.db
          .select({ id: productVariants.id })
          .from(productVariants)
          .where(inArray(productVariants.id, variantIds));

        if (existingVariants.length !== variantIds.length) {
          throw new BadRequestException(
            'One or more product variants not found',
          );
        }
      }

      // Validate custom units if provided
      const customUnitIds = productDtos
        .filter((p) => p.customUnitId)
        .map((p) => p.customUnitId);
      if (customUnitIds.length > 0) {
        const existingUnits = await this.db
          .select({ id: units.id })
          .from(units)
          .where(
            and(
              inArray(units.id, customUnitIds),
              eq(units.businessId, businessId),
            ),
          );

        if (existingUnits.length !== customUnitIds.length) {
          throw new BadRequestException('One or more custom units not found');
        }
      }
    }

    // Validate services if provided
    if (serviceDtos && serviceDtos.length > 0) {
      const serviceIds = serviceDtos.map((s) => s.serviceId);
      const existingServices = await this.db
        .select({ id: services.id })
        .from(services)
        .where(
          and(
            inArray(services.id, serviceIds),
            eq(services.businessId, businessId),
          ),
        );

      if (existingServices.length !== serviceIds.length) {
        throw new BadRequestException('One or more services not found');
      }
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: SubscriptionPlanListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(subscriptionPlans.deletedAt),
      eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
      eq(subscriptionPlans.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(subscriptionPlans.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(subscriptionPlans.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(subscriptionPlans)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get subscription plans with creator info
    const subscriptionPlanResults = await this.db
      .select({
        id: subscriptionPlans.id,
        name: subscriptionPlans.name,
        description: subscriptionPlans.description,
        status: subscriptionPlans.status,
        basePrice: subscriptionPlans.basePrice,
        recurrencePattern: subscriptionPlans.recurrencePattern,
        recurrenceInterval: subscriptionPlans.recurrenceInterval,
        occurrenceCount: subscriptionPlans.occurrenceCount,
        nextOccurrenceDate: subscriptionPlans.nextOccurrenceDate,
        createdBy: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
        createdAt: subscriptionPlans.createdAt,
        updatedAt: subscriptionPlans.updatedAt,
      })
      .from(subscriptionPlans)
      .leftJoin(users, eq(subscriptionPlans.createdBy, users.id))
      .where(and(...whereConditions))
      .orderBy(desc(subscriptionPlans.createdAt))
      .limit(limit)
      .offset(offset);

    // Transform results
    const data: SubscriptionPlanListDto[] = subscriptionPlanResults.map(
      (sp) => ({
        id: sp.id,
        name: sp.name,
        description: sp.description,
        status: sp.status,
        basePrice: parseFloat(sp.basePrice),
        recurrencePattern: sp.recurrencePattern,
        recurrenceInterval: sp.recurrenceInterval,
        occurrenceCount: sp.occurrenceCount,
        nextOccurrenceDate: sp.nextOccurrenceDate,
        createdBy: sp.createdBy || 'Unknown',
        createdAt: sp.createdAt,
        updatedAt: sp.updatedAt,
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<SubscriptionPlanDto> {
    // Get the subscription plan with all related data
    const subscriptionPlan = await this.db
      .select({
        id: subscriptionPlans.id,
        businessId: subscriptionPlans.businessId,
        name: subscriptionPlans.name,
        description: subscriptionPlans.description,
        status: subscriptionPlans.status,
        basePrice: subscriptionPlans.basePrice,
        taxType: subscriptionPlans.taxType,
        taxId: subscriptionPlans.taxId,
        standardCost: subscriptionPlans.standardCost,
        incomeAccountId: subscriptionPlans.incomeAccountId,
        expenseAccountId: subscriptionPlans.expenseAccountId,
        netDays: subscriptionPlans.netDays,
        recurrencePattern: subscriptionPlans.recurrencePattern,
        recurrenceInterval: subscriptionPlans.recurrenceInterval,
        recurrenceEndType: subscriptionPlans.recurrenceEndType,
        recurrenceEndDate: subscriptionPlans.recurrenceEndDate,
        recurrenceEndAfterOccurrences:
          subscriptionPlans.recurrenceEndAfterOccurrences,
        recurrenceDaysOfWeek: subscriptionPlans.recurrenceDaysOfWeek,
        recurrenceDayOfMonth: subscriptionPlans.recurrenceDayOfMonth,
        recurrenceMonthOfYear: subscriptionPlans.recurrenceMonthOfYear,
        nextOccurrenceDate: subscriptionPlans.nextOccurrenceDate,
        lastOccurrenceDate: subscriptionPlans.lastOccurrenceDate,
        occurrenceCount: subscriptionPlans.occurrenceCount,
        createdBy: sql<string>`CONCAT(creator.first_name, ' ', creator.last_name)`,
        updatedBy: sql<string>`CONCAT(updater.first_name, ' ', updater.last_name)`,
        createdAt: subscriptionPlans.createdAt,
        updatedAt: subscriptionPlans.updatedAt,
        deletedAt: subscriptionPlans.deletedAt,
      })
      .from(subscriptionPlans)
      .leftJoin(
        users.as('creator'),
        eq(subscriptionPlans.createdBy, users.as('creator').id),
      )
      .leftJoin(
        users.as('updater'),
        eq(subscriptionPlans.updatedBy, users.as('updater').id),
      )
      .where(
        and(eq(subscriptionPlans.id, id), isNull(subscriptionPlans.deletedAt)),
      )
      .then((results) => results[0]);

    if (!subscriptionPlan) {
      throw new NotFoundException(`Subscription plan with ID ${id} not found`);
    }

    // Get associated products
    const associatedProducts = await this.db
      .select({
        id: subscriptionPlanProducts.id,
        productId: subscriptionPlanProducts.productId,
        variantId: subscriptionPlanProducts.variantId,
        quantity: subscriptionPlanProducts.quantity,
        standardUnitOfMeasure: subscriptionPlanProducts.standardUnitOfMeasure,
        customUnitId: subscriptionPlanProducts.customUnitId,
        productName: products.name,
        variantName: productVariants.name,
        customUnitName: units.name,
      })
      .from(subscriptionPlanProducts)
      .leftJoin(products, eq(subscriptionPlanProducts.productId, products.id))
      .leftJoin(
        productVariants,
        eq(subscriptionPlanProducts.variantId, productVariants.id),
      )
      .leftJoin(units, eq(subscriptionPlanProducts.customUnitId, units.id))
      .where(eq(subscriptionPlanProducts.subscriptionPlanId, id));

    // Get associated services
    const associatedServices = await this.db
      .select({
        id: subscriptionPlanServices.id,
        serviceId: subscriptionPlanServices.serviceId,
        serviceName: services.name,
      })
      .from(subscriptionPlanServices)
      .leftJoin(services, eq(subscriptionPlanServices.serviceId, services.id))
      .where(eq(subscriptionPlanServices.subscriptionPlanId, id));

    // Transform the result
    const result: SubscriptionPlanDto = {
      id: subscriptionPlan.id,
      businessId: subscriptionPlan.businessId,
      name: subscriptionPlan.name,
      description: subscriptionPlan.description,
      status: subscriptionPlan.status,
      basePrice: parseFloat(subscriptionPlan.basePrice),
      taxType: subscriptionPlan.taxType,
      taxId: subscriptionPlan.taxId,
      standardCost: parseFloat(subscriptionPlan.standardCost),
      incomeAccountId: subscriptionPlan.incomeAccountId,
      expenseAccountId: subscriptionPlan.expenseAccountId,
      netDays: subscriptionPlan.netDays,
      recurrencePattern: subscriptionPlan.recurrencePattern,
      recurrenceInterval: subscriptionPlan.recurrenceInterval,
      recurrenceEndType: subscriptionPlan.recurrenceEndType,
      recurrenceEndDate: subscriptionPlan.recurrenceEndDate,
      recurrenceEndAfterOccurrences:
        subscriptionPlan.recurrenceEndAfterOccurrences,
      recurrenceDaysOfWeek: subscriptionPlan.recurrenceDaysOfWeek,
      recurrenceDayOfMonth: subscriptionPlan.recurrenceDayOfMonth,
      recurrenceMonthOfYear: subscriptionPlan.recurrenceMonthOfYear,
      nextOccurrenceDate: subscriptionPlan.nextOccurrenceDate,
      lastOccurrenceDate: subscriptionPlan.lastOccurrenceDate,
      occurrenceCount: subscriptionPlan.occurrenceCount,
      products: associatedProducts.map((p) => ({
        id: p.id,
        productId: p.productId,
        variantId: p.variantId,
        quantity: parseFloat(p.quantity),
        standardUnitOfMeasure: p.standardUnitOfMeasure,
        customUnitId: p.customUnitId,
        product: {
          id: p.productId,
          name: p.productName || 'Unknown Product',
        },
        variant: p.variantId
          ? {
              id: p.variantId,
              name: p.variantName || 'Unknown Variant',
            }
          : undefined,
        customUnit: p.customUnitId
          ? {
              id: p.customUnitId,
              name: p.customUnitName || 'Unknown Unit',
            }
          : undefined,
      })),
      services: associatedServices.map((s) => ({
        id: s.id,
        serviceId: s.serviceId,
        service: {
          id: s.serviceId,
          name: s.serviceName || 'Unknown Service',
        },
      })),
      createdBy: subscriptionPlan.createdBy || 'Unknown',
      updatedBy: subscriptionPlan.updatedBy,
      createdAt: subscriptionPlan.createdAt,
      updatedAt: subscriptionPlan.updatedAt,
      deletedAt: subscriptionPlan.deletedAt,
    };

    return result;
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<SubscriptionPlanSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all subscription plans with only essential fields
    const subscriptionPlanResults = await this.db
      .select({
        id: subscriptionPlans.id,
        name: subscriptionPlans.name,
        status: subscriptionPlans.status,
        basePrice: subscriptionPlans.basePrice,
      })
      .from(subscriptionPlans)
      .where(
        and(
          isNull(subscriptionPlans.deletedAt),
          eq(subscriptionPlans.status, SubscriptionPlanStatus.ACTIVE),
          eq(subscriptionPlans.businessId, businessId),
        ),
      )
      .orderBy(asc(subscriptionPlans.name));

    // Transform results
    const data: SubscriptionPlanSlimDto[] = subscriptionPlanResults.map(
      (sp) => ({
        id: sp.id,
        name: sp.name,
        status: sp.status,
        basePrice: parseFloat(sp.basePrice),
      }),
    );

    return data;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<SubscriptionPlanDto> {
    // Get the subscription plan
    const existingSubscriptionPlan = await this.db
      .select()
      .from(subscriptionPlans)
      .where(
        and(eq(subscriptionPlans.id, id), isNull(subscriptionPlans.deletedAt)),
      )
      .then((results) => results[0]);

    if (!existingSubscriptionPlan) {
      throw new NotFoundException(`Subscription plan with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingSubscriptionPlan.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this subscription plan',
      );
    }

    // Check for name conflicts if name is being updated
    if (
      updateSubscriptionPlanDto.name &&
      updateSubscriptionPlanDto.name !== existingSubscriptionPlan.name
    ) {
      const existingWithSameName = await this.db
        .select()
        .from(subscriptionPlans)
        .where(
          and(
            eq(subscriptionPlans.businessId, businessId),
            ilike(subscriptionPlans.name, updateSubscriptionPlanDto.name),
            isNull(subscriptionPlans.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingWithSameName) {
        throw new ConflictException(
          `Subscription plan with name "${updateSubscriptionPlanDto.name}" already exists`,
        );
      }
    }

    // Validate referenced entities if they are being updated
    if (
      updateSubscriptionPlanDto.taxId ||
      updateSubscriptionPlanDto.incomeAccountId ||
      updateSubscriptionPlanDto.expenseAccountId ||
      updateSubscriptionPlanDto.products ||
      updateSubscriptionPlanDto.services
    ) {
      await this.validateReferencedEntities(
        businessId,
        updateSubscriptionPlanDto.taxId || existingSubscriptionPlan.taxId,
        updateSubscriptionPlanDto.incomeAccountId ||
          existingSubscriptionPlan.incomeAccountId,
        updateSubscriptionPlanDto.expenseAccountId ||
          existingSubscriptionPlan.expenseAccountId,
        updateSubscriptionPlanDto.products,
        updateSubscriptionPlanDto.services,
      );
    }

    // Start transaction
    const result = await this.db.transaction(async (tx) => {
      // Update the subscription plan
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update fields that are provided
      if (updateSubscriptionPlanDto.name !== undefined) {
        updateData.name = updateSubscriptionPlanDto.name;
      }
      if (updateSubscriptionPlanDto.description !== undefined) {
        updateData.description = updateSubscriptionPlanDto.description;
      }
      if (updateSubscriptionPlanDto.status !== undefined) {
        updateData.status = updateSubscriptionPlanDto.status;
      }
      if (updateSubscriptionPlanDto.basePrice !== undefined) {
        updateData.basePrice = updateSubscriptionPlanDto.basePrice.toString();
      }
      if (updateSubscriptionPlanDto.taxType !== undefined) {
        updateData.taxType = updateSubscriptionPlanDto.taxType;
      }
      if (updateSubscriptionPlanDto.taxId !== undefined) {
        updateData.taxId = updateSubscriptionPlanDto.taxId;
      }
      if (updateSubscriptionPlanDto.standardCost !== undefined) {
        updateData.standardCost =
          updateSubscriptionPlanDto.standardCost.toString();
      }
      if (updateSubscriptionPlanDto.incomeAccountId !== undefined) {
        updateData.incomeAccountId = updateSubscriptionPlanDto.incomeAccountId;
      }
      if (updateSubscriptionPlanDto.expenseAccountId !== undefined) {
        updateData.expenseAccountId =
          updateSubscriptionPlanDto.expenseAccountId;
      }
      if (updateSubscriptionPlanDto.netDays !== undefined) {
        updateData.netDays = updateSubscriptionPlanDto.netDays;
      }
      if (updateSubscriptionPlanDto.recurrencePattern !== undefined) {
        updateData.recurrencePattern =
          updateSubscriptionPlanDto.recurrencePattern;
      }
      if (updateSubscriptionPlanDto.recurrenceInterval !== undefined) {
        updateData.recurrenceInterval =
          updateSubscriptionPlanDto.recurrenceInterval;
      }
      if (updateSubscriptionPlanDto.recurrenceEndType !== undefined) {
        updateData.recurrenceEndType =
          updateSubscriptionPlanDto.recurrenceEndType;
      }
      if (updateSubscriptionPlanDto.recurrenceEndDate !== undefined) {
        updateData.recurrenceEndDate =
          updateSubscriptionPlanDto.recurrenceEndDate;
      }
      if (
        updateSubscriptionPlanDto.recurrenceEndAfterOccurrences !== undefined
      ) {
        updateData.recurrenceEndAfterOccurrences =
          updateSubscriptionPlanDto.recurrenceEndAfterOccurrences;
      }
      if (updateSubscriptionPlanDto.recurrenceDaysOfWeek !== undefined) {
        updateData.recurrenceDaysOfWeek =
          updateSubscriptionPlanDto.recurrenceDaysOfWeek;
      }
      if (updateSubscriptionPlanDto.recurrenceDayOfMonth !== undefined) {
        updateData.recurrenceDayOfMonth =
          updateSubscriptionPlanDto.recurrenceDayOfMonth;
      }
      if (updateSubscriptionPlanDto.recurrenceMonthOfYear !== undefined) {
        updateData.recurrenceMonthOfYear =
          updateSubscriptionPlanDto.recurrenceMonthOfYear;
      }
      if (updateSubscriptionPlanDto.nextOccurrenceDate !== undefined) {
        updateData.nextOccurrenceDate =
          updateSubscriptionPlanDto.nextOccurrenceDate;
      }

      await tx
        .update(subscriptionPlans)
        .set(updateData)
        .where(eq(subscriptionPlans.id, id));

      // Update product associations if provided
      if (updateSubscriptionPlanDto.products !== undefined) {
        // Delete existing product associations
        await tx
          .delete(subscriptionPlanProducts)
          .where(eq(subscriptionPlanProducts.subscriptionPlanId, id));

        // Create new product associations
        if (updateSubscriptionPlanDto.products.length > 0) {
          await tx.insert(subscriptionPlanProducts).values(
            updateSubscriptionPlanDto.products.map((product) => ({
              subscriptionPlanId: id,
              productId: product.productId,
              variantId: product.variantId,
              quantity: product.quantity.toString(),
              standardUnitOfMeasure: product.standardUnitOfMeasure,
              customUnitId: product.customUnitId,
              createdBy: userId,
            })),
          );
        }
      }

      // Update service associations if provided
      if (updateSubscriptionPlanDto.services !== undefined) {
        // Delete existing service associations
        await tx
          .delete(subscriptionPlanServices)
          .where(eq(subscriptionPlanServices.subscriptionPlanId, id));

        // Create new service associations
        if (updateSubscriptionPlanDto.services.length > 0) {
          await tx.insert(subscriptionPlanServices).values(
            updateSubscriptionPlanDto.services.map((service) => ({
              subscriptionPlanId: id,
              serviceId: service.serviceId,
              createdBy: userId,
            })),
          );
        }
      }

      return { id };
    });

    // Log the activity
    await this.activityLogService.logUpdate(
      result.id,
      EntityType.SUBSCRIPTION_PLAN,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    // Return the updated subscription plan
    return this.findOne(userId, id);
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateSubscriptionPlanDto,
      metadata,
    );
    return { id };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the subscription plan
    const existingSubscriptionPlan = await this.db
      .select()
      .from(subscriptionPlans)
      .where(
        and(eq(subscriptionPlans.id, id), isNull(subscriptionPlans.deletedAt)),
      )
      .then((results) => results[0]);

    if (!existingSubscriptionPlan) {
      throw new NotFoundException(`Subscription plan with ID ${id} not found`);
    }

    // Soft delete the subscription plan
    await this.db
      .update(subscriptionPlans)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(subscriptionPlans.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      existingSubscriptionPlan.id,
      EntityType.SUBSCRIPTION_PLAN,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: 'Subscription plan deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    subscriptionPlanIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!subscriptionPlanIds || subscriptionPlanIds.length === 0) {
      throw new BadRequestException(
        'No subscription plan IDs provided for deletion',
      );
    }

    // Get existing subscription plans
    const existingSubscriptionPlans = await this.db
      .select({
        id: subscriptionPlans.id,
        name: subscriptionPlans.name,
        businessId: subscriptionPlans.businessId,
      })
      .from(subscriptionPlans)
      .where(
        and(
          inArray(subscriptionPlans.id, subscriptionPlanIds),
          isNull(subscriptionPlans.deletedAt),
        ),
      );

    // Verify business ownership
    const unauthorizedPlans = existingSubscriptionPlans.filter(
      (sp) => sp.businessId !== businessId,
    );
    if (unauthorizedPlans.length > 0) {
      throw new UnauthorizedException(
        'Access denied to delete some subscription plans',
      );
    }

    if (existingSubscriptionPlans.length === 0) {
      throw new NotFoundException('No subscription plans found to delete');
    }

    const deletedIds: string[] = [];

    // Soft delete each subscription plan
    for (const subscriptionPlan of existingSubscriptionPlans) {
      await this.db
        .update(subscriptionPlans)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(subscriptionPlans.id, subscriptionPlan.id));

      deletedIds.push(subscriptionPlan.id);
    }

    // Log bulk delete operation
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.SUBSCRIPTION_PLAN,
      deletedIds,
      {},
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
        filterCriteria: {
          subscriptionPlanIds: subscriptionPlanIds,
          businessId: businessId,
        },
      },
    );

    return {
      deleted: deletedIds.length,
      message: `Successfully deleted ${deletedIds.length} subscription plans`,
      deletedIds,
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    subscriptionPlanIds: string[],
    status: SubscriptionPlanStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!subscriptionPlanIds || subscriptionPlanIds.length === 0) {
      throw new BadRequestException(
        'No subscription plan IDs provided for status update',
      );
    }

    // Get existing subscription plans
    const existingSubscriptionPlans = await this.db
      .select({
        id: subscriptionPlans.id,
        name: subscriptionPlans.name,
        businessId: subscriptionPlans.businessId,
      })
      .from(subscriptionPlans)
      .where(
        and(
          inArray(subscriptionPlans.id, subscriptionPlanIds),
          isNull(subscriptionPlans.deletedAt),
        ),
      );

    // Verify business ownership
    const unauthorizedPlans = existingSubscriptionPlans.filter(
      (sp) => sp.businessId !== businessId,
    );
    if (unauthorizedPlans.length > 0) {
      throw new UnauthorizedException(
        'Access denied to update some subscription plans',
      );
    }

    if (existingSubscriptionPlans.length === 0) {
      throw new NotFoundException('No subscription plans found to update');
    }

    // Update status for all subscription plans
    await this.db
      .update(subscriptionPlans)
      .set({
        status,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          inArray(subscriptionPlans.id, subscriptionPlanIds),
          isNull(subscriptionPlans.deletedAt),
        ),
      );

    // Log bulk status update operation
    const updatedIds = existingSubscriptionPlans.map((sp) => sp.id);
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_UPDATE,
      EntityType.SUBSCRIPTION_PLAN,
      updatedIds,
      {},
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
        filterCriteria: {
          subscriptionPlanIds: subscriptionPlanIds,
          businessId: businessId,
          newStatus: status,
        },
      },
    );

    return {
      updated: existingSubscriptionPlans.length,
      message: `Successfully updated status for ${existingSubscriptionPlans.length} subscription plans`,
    };
  }

  async checkNameAvailability(
    userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingSubscriptionPlan = await this.db
      .select()
      .from(subscriptionPlans)
      .where(
        and(
          eq(subscriptionPlans.businessId, businessId),
          ilike(subscriptionPlans.name, name),
          isNull(subscriptionPlans.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingSubscriptionPlan };
  }
}
