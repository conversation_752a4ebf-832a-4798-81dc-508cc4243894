import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { SubscriptionPlansService } from './subscription-plans.service';
import { CreateSubscriptionPlanDto } from './dto/create-subscription-plan.dto';
import { UpdateSubscriptionPlanDto } from './dto/update-subscription-plan.dto';
import { SubscriptionPlanDto } from './dto/subscription-plan.dto';
import { SubscriptionPlanSlimDto } from './dto/subscription-plan-slim.dto';
import { SubscriptionPlanIdResponseDto } from './dto/subscription-plan-id-response.dto';
import { BulkSubscriptionPlanIdsResponseDto } from './dto/bulk-subscription-plan-ids-response.dto';
import { BulkCreateSubscriptionPlanDto } from './dto/bulk-create-subscription-plan.dto';
import { BulkDeleteSubscriptionPlanDto } from './dto/bulk-delete-subscription-plan.dto';
import { BulkDeleteSubscriptionPlanResponseDto } from './dto/bulk-delete-subscription-plan-response.dto';
import { BulkUpdateSubscriptionPlanStatusDto } from './dto/bulk-update-subscription-plan-status.dto';
import { DeleteSubscriptionPlanResponseDto } from './dto/delete-subscription-plan-response.dto';
import { PaginatedSubscriptionPlansResponseDto } from './dto/paginated-subscription-plans-response.dto';
import { SubscriptionPlanNameAvailabilityResponseDto } from './dto/check-subscription-plan-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('subscription-plans')
@Controller('subscription-plans')
@UseGuards(PermissionsGuard)
export class SubscriptionPlansController {
  constructor(
    private readonly subscriptionPlansService: SubscriptionPlansService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_CREATE)
  @ApiOperation({ summary: 'Create a new subscription plan' })
  @ApiBody({
    description: 'Subscription plan creation data',
    type: CreateSubscriptionPlanDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Subscription plan has been successfully created',
    type: SubscriptionPlanIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Subscription plan name already exists',
  })
  create(
    @Request() req,
    @Body() createSubscriptionPlanDto: CreateSubscriptionPlanDto,
  ): Promise<SubscriptionPlanIdResponseDto> {
    return this.subscriptionPlansService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createSubscriptionPlanDto,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_CREATE)
  @ApiOperation({ summary: 'Bulk create subscription plans' })
  @ApiBody({
    description: 'Bulk subscription plan creation data',
    type: BulkCreateSubscriptionPlanDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Subscription plans have been successfully created',
    type: BulkSubscriptionPlanIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Subscription plan names already exist',
  })
  bulkCreate(
    @Request() req,
    @Body() bulkCreateSubscriptionPlanDto: BulkCreateSubscriptionPlanDto,
  ): Promise<BulkSubscriptionPlanIdsResponseDto> {
    return this.subscriptionPlansService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateSubscriptionPlanDto.subscriptionPlans,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_READ)
  @ApiOperation({
    summary: 'Get all subscription plans for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description:
      'Filter subscription plans created from this date (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description:
      'Filter subscription plans created until this date (ISO string)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription plans returned successfully',
    type: PaginatedSubscriptionPlansResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ): Promise<PaginatedSubscriptionPlansResponseDto> {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;

    return this.subscriptionPlansService.findAll(
      req.user.id,
      req.user.activeBusinessId,
      pageNum,
      limitNum,
      from,
      to,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_READ)
  @ApiOperation({ summary: 'Check if subscription plan name is available' })
  @ApiQuery({
    name: 'name',
    description: 'Subscription plan name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: SubscriptionPlanNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.subscriptionPlansService.checkNameAvailability(
      req.user.id,
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_READ)
  @ApiOperation({ summary: 'Get all subscription plans in slim format' })
  @ApiResponse({
    status: 200,
    description: 'All subscription plans returned successfully',
    type: [SubscriptionPlanSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req): Promise<SubscriptionPlanSlimDto[]> {
    return this.subscriptionPlansService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_READ)
  @ApiOperation({ summary: 'Get a subscription plan by ID' })
  @ApiParam({
    name: 'id',
    description: 'Subscription plan ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan returned successfully',
    type: SubscriptionPlanDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Subscription plan not found',
  })
  findOne(
    @Request() req,
    @Param('id') id: string,
  ): Promise<SubscriptionPlanDto> {
    return this.subscriptionPlansService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_UPDATE)
  @ApiOperation({ summary: 'Update a subscription plan' })
  @ApiParam({
    name: 'id',
    description: 'Subscription plan ID',
  })
  @ApiBody({
    description: 'Subscription plan update data',
    type: UpdateSubscriptionPlanDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan has been successfully updated',
    type: SubscriptionPlanIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Subscription plan not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Subscription plan name already exists',
  })
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
  ): Promise<SubscriptionPlanIdResponseDto> {
    return this.subscriptionPlansService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateSubscriptionPlanDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_DELETE)
  @ApiOperation({ summary: 'Bulk delete subscription plans' })
  @ApiBody({
    description: 'Array of subscription plan IDs to delete',
    type: BulkDeleteSubscriptionPlanDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription plans have been successfully deleted',
    type: BulkDeleteSubscriptionPlanResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Some subscription plans not found',
  })
  bulkDelete(
    @Request() req,
    @Body() bulkDeleteSubscriptionPlanDto: BulkDeleteSubscriptionPlanDto,
  ): Promise<BulkDeleteSubscriptionPlanResponseDto> {
    return this.subscriptionPlansService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteSubscriptionPlanDto.subscriptionPlanIds,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_UPDATE)
  @ApiOperation({ summary: 'Bulk update subscription plan status' })
  @ApiBody({
    description: 'Subscription plan IDs and new status',
    type: BulkUpdateSubscriptionPlanStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan statuses have been successfully updated',
    schema: {
      type: 'object',
      properties: {
        updated: { type: 'number', example: 2 },
        message: {
          type: 'string',
          example: 'Successfully updated status for 2 subscription plans',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Some subscription plans not found',
  })
  bulkUpdateStatus(
    @Request() req,
    @Body()
    bulkUpdateSubscriptionPlanStatusDto: BulkUpdateSubscriptionPlanStatusDto,
  ): Promise<{ updated: number; message: string }> {
    return this.subscriptionPlansService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateSubscriptionPlanStatusDto.subscriptionPlanIds,
      bulkUpdateSubscriptionPlanStatusDto.status,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.SUBSCRIPTION_PLAN_DELETE)
  @ApiOperation({ summary: 'Delete a subscription plan' })
  @ApiParam({
    name: 'id',
    description: 'Subscription plan ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan has been successfully deleted',
    type: DeleteSubscriptionPlanResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Subscription plan not found',
  })
  remove(
    @Request() req,
    @Param('id') id: string,
  ): Promise<DeleteSubscriptionPlanResponseDto> {
    return this.subscriptionPlansService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }
}
