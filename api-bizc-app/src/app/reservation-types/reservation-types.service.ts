import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateReservationTypeDto } from './dto/create-reservation-type.dto';
import { UpdateReservationTypeDto } from './dto/update-reservation-type.dto';
import { ReservationTypeDto } from './dto/reservation-type.dto';
import { ReservationTypeSlimDto } from './dto/reservation-type-slim.dto';
import { ReservationTypeListDto } from './dto/reservation-type-list.dto';
import {
  reservationTypes,
  reservationTypeLocations,
} from '../drizzle/schema/reservation-types.schema';
import { media } from '../drizzle/schema/media.schema';
import { locations } from '../drizzle/schema/locations.schema';
import {
  eq,
  and,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { ReservationTypeStatus } from '../shared/types';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';

@Injectable()
export class ReservationTypesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createReservationTypeDto: CreateReservationTypeDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a reservation type with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingReservationType = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            ilike(reservationTypes.name, createReservationTypeDto.name),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingReservationType) {
        throw new ConflictException(
          `A reservation type with the name '${createReservationTypeDto.name}' already exists for this business`,
        );
      }

      // Create slug from name if not provided
      if (!createReservationTypeDto.slug) {
        createReservationTypeDto.slug = createReservationTypeDto.name
          .toLowerCase()
          .replace(/\s+/g, '-')
          .replace(/[^a-z0-9-]/g, '');
      }

      // Check if a reservation type with the same slug already exists for this business
      if (createReservationTypeDto.slug) {
        const existingSlugReservationType = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.slug, createReservationTypeDto.slug),
              eq(reservationTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingSlugReservationType) {
          throw new ConflictException(
            `A reservation type with the slug '${createReservationTypeDto.slug}' already exists for this business`,
          );
        }
      }

      // Check if a reservation type with the same shortCode already exists for this business
      if (createReservationTypeDto.shortCode) {
        const existingShortCodeReservationType = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.businessId, businessId),
              eq(
                reservationTypes.shortCode,
                createReservationTypeDto.shortCode,
              ),
              eq(reservationTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingShortCodeReservationType) {
          throw new ConflictException(
            `A reservation type with the short code '${createReservationTypeDto.shortCode}' already exists for this business`,
          );
        }
      }

      // Handle parent reservation type if provided
      if (createReservationTypeDto.parentId) {
        const parentReservationType = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.id, createReservationTypeDto.parentId),
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentReservationType) {
          throw new BadRequestException('Parent reservation type not found');
        }
      }

      let mediaId: string | undefined;
      let ogImageId: string | undefined;

      // Upload image if provided
      if (imageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          imageFile,
          'reservation-types',
          businessId,
          userId,
        );
        mediaId = uploadedMedia.id;
      }

      // Upload OG image if provided
      if (ogImageFile) {
        const uploadedOgMedia = await this.mediaService.uploadMedia(
          ogImageFile,
          'reservation-types/og-images',
          businessId,
          userId,
        );
        ogImageId = uploadedOgMedia.id;
      }

      console.log(
        'createReservationTypeDto.locationIds',
        createReservationTypeDto.locationIds,
      );

      // Use a transaction to ensure position reordering and reservation type creation are atomic
      const newReservationType = await this.db.transaction(async (tx) => {
        // Shift all existing reservation types down by 1 position to make room at position 1
        await this.reorderPositions(
          tx,
          businessId,
          createReservationTypeDto.parentId,
          1, // Insert at position 1 (first position)
        );

        // Insert new reservation type at position 1
        const [reservationType] = await tx
          .insert(reservationTypes)
          .values({
            businessId,
            name: createReservationTypeDto.name,
            shortCode: createReservationTypeDto.shortCode,
            parentId: createReservationTypeDto.parentId,
            category: createReservationTypeDto.category,
            description: createReservationTypeDto.description,
            slug: createReservationTypeDto.slug,
            availableOnline: createReservationTypeDto.availableOnline ?? false,
            position: 1, // Always create new reservation types at position 1 (first)
            image: mediaId,
            isAllocatedToAllLocations:
              createReservationTypeDto.isAllocatedToAllLocations ?? false,
            seoTitle: createReservationTypeDto.seoTitle,
            seoDescription: createReservationTypeDto.seoDescription,
            seoKeywords: createReservationTypeDto.seoKeywords,
            ogImage: ogImageId,
            createdBy: userId,
            status:
              createReservationTypeDto.status ?? ReservationTypeStatus.ACTIVE,
          })
          .returning();

        return reservationType;
      });

      // Handle location associations
      await this.manageReservationTypeLocations(
        newReservationType.id,
        businessId,
        createReservationTypeDto.isAllocatedToAllLocations ?? false,
        createReservationTypeDto.locationIds,
        userId,
      );

      // Log the reservation type creation activity
      await this.activityLogService.logCreate(
        newReservationType.id,
        EntityType.RESERVATION_TYPE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newReservationType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create reservation type: ${error.message}`,
      );
    }
  }

  async findAll(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: ReservationTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(reservationTypes.isDeleted, false),
      eq(reservationTypes.status, ReservationTypeStatus.ACTIVE),
      eq(reservationTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(reservationTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(reservationTypes.createdAt, toDate));
      }
    }

    // Find all reservation types for the user's active business with pagination
    const result = await this.db
      .select()
      .from(reservationTypes)
      .where(and(...whereConditions))
      .orderBy(desc(reservationTypes.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(reservationTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // View logging removed to reduce activity log volume

    return {
      data: await Promise.all(
        result.map((reservationType) =>
          this.mapToReservationTypeDto(reservationType),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllByBusiness(
    _userId: string,
    businessId: string,
  ): Promise<ReservationTypeDto[]> {
    // This method keeps same functionality but we might consider if this is still needed
    // since we now use activeBusinessId by default
    const result = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.isDeleted, false),
          eq(reservationTypes.status, ReservationTypeStatus.ACTIVE),
          eq(reservationTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(reservationTypes.position), asc(reservationTypes.id));

    // View logging removed to reduce activity log volume

    return await Promise.all(
      result.map((reservationType) =>
        this.mapToReservationTypeDto(reservationType),
      ),
    );
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    shortCode?: string,
    status?: string,
    availableOnline?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ReservationTypeListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(reservationTypes.isDeleted, false),
      eq(reservationTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(reservationTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(reservationTypes.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches both name and shortCode)
    if (name) {
      whereConditions.push(
        or(
          ilike(reservationTypes.name, `%${name}%`),
          ilike(reservationTypes.shortCode, `%${name}%`),
        ),
      );
    }

    // Add slug filtering if provided
    if (slug) {
      whereConditions.push(ilike(reservationTypes.slug, `%${slug}%`));
    }

    // Add shortCode filtering if provided
    if (shortCode) {
      whereConditions.push(ilike(reservationTypes.shortCode, `%${shortCode}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as ReservationTypeStatus);
      whereConditions.push(inArray(reservationTypes.status, statusArray));
    }

    // Add availableOnline filtering if provided
    if (availableOnline) {
      // Decode URL-encoded commas and split by comma
      const decodedAvailableOnline = decodeURIComponent(availableOnline);
      const availableOnlineValues = decodedAvailableOnline
        .split(',')
        .map((s) => s.trim());
      if (availableOnlineValues.length === 1) {
        const boolValue = availableOnlineValues[0].toLowerCase() === 'true';
        whereConditions.push(eq(reservationTypes.availableOnline, boolValue));
      } else {
        // Handle multiple boolean values (true, false)
        const boolValues = availableOnlineValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(reservationTypes.availableOnline, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(reservationTypes.availableOnline, false));
        }
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(reservationTypes.name, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(reservationTypes.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(reservationTypes.name, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${reservationTypes.name} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${reservationTypes.name} IS NULL OR ${reservationTypes.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${reservationTypes.name} IS NOT NULL AND ${reservationTypes.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(
                      reservationTypes.status,
                      value as ReservationTypeStatus[],
                    ),
                  );
                } else {
                  filterConditions.push(eq(reservationTypes.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${reservationTypes.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${reservationTypes.status} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as ReservationTypeStatus);
                  filterConditions.push(
                    inArray(reservationTypes.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(reservationTypes.status, value));
                }
                break;
            }
          } else if (fieldId === 'slug') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(reservationTypes.slug, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(reservationTypes.slug, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(reservationTypes.slug, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${reservationTypes.slug} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${reservationTypes.slug} IS NULL OR ${reservationTypes.slug} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${reservationTypes.slug} IS NOT NULL AND ${reservationTypes.slug} != ''`,
                );
                break;
            }
          } else if (fieldId === 'shortCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(reservationTypes.shortCode, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(reservationTypes.shortCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(reservationTypes.shortCode, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${reservationTypes.shortCode} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${reservationTypes.shortCode} IS NULL OR ${reservationTypes.shortCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${reservationTypes.shortCode} IS NOT NULL AND ${reservationTypes.shortCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'availableOnline') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(
                      eq(reservationTypes.availableOnline, true),
                    );
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(reservationTypes.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(reservationTypes.availableOnline, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(
                  eq(reservationTypes.availableOnline, !boolValue),
                );
                break;
              }
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const availableOnlineValues = decodedValue
                    .split(',')
                    .map((s) => s.trim());
                  const boolValues = availableOnlineValues.map(
                    (s) => s.toLowerCase() === 'true',
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(
                      eq(reservationTypes.availableOnline, true),
                    );
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(reservationTypes.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(reservationTypes.availableOnline, boolValue),
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions with proper indexing strategy
    // Default sort: position ascending, then by ID for consistent pagination when positions are equal
    // This leverages the composite index for optimal performance
    let orderBy = [asc(reservationTypes.position), asc(reservationTypes.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              // Use name index for sorting
              orderBy = [
                isDesc
                  ? desc(reservationTypes.name)
                  : asc(reservationTypes.name),
                asc(reservationTypes.id), // Secondary sort for consistency
              ];
              break;
            case 'position':
              // Optimized position sorting with secondary sort for pagination consistency
              orderBy = [
                isDesc
                  ? desc(reservationTypes.position)
                  : asc(reservationTypes.position),
                asc(reservationTypes.id), // Use ID for consistent pagination when positions are equal
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(reservationTypes.createdAt)
                  : asc(reservationTypes.createdAt),
                asc(reservationTypes.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc
                  ? desc(reservationTypes.updatedAt)
                  : asc(reservationTypes.updatedAt),
                asc(reservationTypes.id), // Secondary sort for consistency
              ];
              break;
            case 'productsCount':
            case 'subReservationTypesCount':
              // These will be handled with post-query sorting since they are calculated fields
              // Use position as default to maintain performance
              orderBy = [
                asc(reservationTypes.position),
                asc(reservationTypes.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Check if we need to sort by calculated fields
    const needsPostQuerySort =
      sort &&
      (() => {
        try {
          const parsedSort = JSON.parse(sort);
          return (
            parsedSort.length > 0 &&
            (parsedSort[0].id === 'productsCount' ||
              parsedSort[0].id === 'subReservationTypesCount')
          );
        } catch {
          return false;
        }
      })();

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: reservationTypes.id,
        name: reservationTypes.name,
        shortCode: reservationTypes.shortCode,
        slug: reservationTypes.slug,
        status: reservationTypes.status,
        availableOnline: reservationTypes.availableOnline,
        position: reservationTypes.position,
        parentId: reservationTypes.parentId,
        category: reservationTypes.category,
        isAllocatedToAllLocations: reservationTypes.isAllocatedToAllLocations,
        imageId: reservationTypes.image,
        imagePublicUrl: media.publicUrl,
        imageFileName: media.fileName,
        createdAt: reservationTypes.createdAt,
        updatedAt: reservationTypes.updatedAt,
      })
      .from(reservationTypes)
      .leftJoin(media, eq(reservationTypes.image, media.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(needsPostQuerySort ? undefined : limit)
      .offset(needsPostQuerySort ? undefined : offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(reservationTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get parent names and sub-reservation types count
    const reservationTypeIds = result.map((cat) => cat.id);

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: reservationTypes.id,
        parentId: reservationTypes.parentId,
        parentName: sql<string>`parent_cat.name`.as('parentName'),
      })
      .from(reservationTypes)
      .leftJoin(
        sql`${reservationTypes} parent_cat`,
        sql`${reservationTypes.parentId} = parent_cat.id`,
      )
      .where(
        and(
          inArray(reservationTypes.id, reservationTypeIds),
          eq(reservationTypes.isDeleted, false),
        ),
      );

    // Get sub-reservation types count for each reservation type
    const subReservationTypesCountQuery = await this.db
      .select({
        parentId: reservationTypes.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(reservationTypes)
      .where(
        and(
          inArray(reservationTypes.parentId, reservationTypeIds),
          eq(reservationTypes.isDeleted, false),
        ),
      )
      .groupBy(reservationTypes.parentId);

    // Get products count for each reservation type
    const productsCountMap =
      await this.getProductsCountForReservationTypes(reservationTypeIds);

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subReservationTypesCountMap = new Map(
      subReservationTypesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // View logging removed to reduce activity log volume

    // Generate signed URLs for images and build final data
    let data = await Promise.all(
      result.map(async (reservationType) => {
        let imageUrl: string | undefined;

        if (reservationType.imageFileName) {
          try {
            // Generate signed URL with 60 minutes expiration for the image
            imageUrl = await this.gcsUploadService.generateSignedUrl(
              reservationType.imageFileName,
              'reservation types', // folder where reservation type images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for reservation type ${reservationType.id} image:`,
              error.message,
            );
            // Fallback to public URL if signed URL generation fails
            imageUrl = reservationType.imagePublicUrl || undefined;
          }
        }

        return {
          id: reservationType.id.toString(),
          name: reservationType.name,
          shortCode: reservationType.shortCode,
          slug: reservationType.slug,
          status: reservationType.status,
          availableOnline: reservationType.availableOnline,
          parentId: reservationType.parentId?.toString(),
          parentName: parentInfoMap.get(reservationType.id) || undefined,
          category: reservationType.category,
          isAllocatedToAllLocations: reservationType.isAllocatedToAllLocations,
          subReservationTypesCount:
            subReservationTypesCountMap.get(reservationType.id) || 0,
          productsCount: productsCountMap.get(reservationType.id) || 0,
          image: imageUrl,
        };
      }),
    );

    // Handle post-query sorting for calculated fields
    if (needsPostQuerySort && sort) {
      try {
        const parsedSort = JSON.parse(sort);
        const sortField = parsedSort[0];
        const isDesc = sortField.desc === true;

        if (sortField.id === 'productsCount') {
          data.sort((a, b) => {
            const diff = a.productsCount - b.productsCount;
            return isDesc ? -diff : diff;
          });
        } else if (sortField.id === 'subReservationTypesCount') {
          data.sort((a, b) => {
            const diff =
              a.subReservationTypesCount - b.subReservationTypesCount;
            return isDesc ? -diff : diff;
          });
        }

        // Apply pagination after sorting
        data = data.slice(offset, offset + limit);
      } catch {
        // Invalid JSON, use unsorted data with pagination
        data = data.slice(offset, offset + limit);
      }
    }

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    _userId: string,
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a reservation type with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingReservationType = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.businessId, businessId),
          ilike(reservationTypes.name, name),
          eq(reservationTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReservationType };
  }

  async checkSlugAvailability(
    _userId: string,
    businessId: string | null,
    slug: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a reservation type with the same slug already exists for this business
    const existingReservationType = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.businessId, businessId),
          eq(reservationTypes.slug, slug),
          eq(reservationTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReservationType };
  }

  async checkShortCodeAvailability(
    _userId: string,
    businessId: string | null,
    shortCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a reservation type with the same shortCode already exists for this business
    const existingReservationType = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.businessId, businessId),
          eq(reservationTypes.shortCode, shortCode),
          eq(reservationTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingReservationType };
  }

  async findOne(userId: string, id: string): Promise<ReservationTypeDto> {
    // Get the reservation type
    const reservationType = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(eq(reservationTypes.id, id), eq(reservationTypes.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!reservationType) {
      throw new NotFoundException(`Reservation type with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== reservationType.businessId
    ) {
      throw new UnauthorizedException('Access denied to this reservation type');
    }

    // View logging removed to reduce activity log volume

    return await this.mapToReservationTypeDto(reservationType);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateReservationTypeDto: UpdateReservationTypeDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<ReservationTypeDto> {
    // Get the reservation type
    const existingReservationType = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(eq(reservationTypes.id, id), eq(reservationTypes.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingReservationType) {
      throw new NotFoundException(`Reservation type with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingReservationType.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this reservation type',
      );
    }

    // Check for name conflict if name is being updated
    if (
      updateReservationTypeDto.name &&
      updateReservationTypeDto.name !== existingReservationType.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            ilike(reservationTypes.name, updateReservationTypeDto.name),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `A reservation type with the name '${updateReservationTypeDto.name}' already exists for this business`,
        );
      }
    }

    // Handle parent reservation type validation if being updated
    if (updateReservationTypeDto.parentId) {
      // Prevent reservation type from being its own parent
      if (updateReservationTypeDto.parentId === id) {
        throw new BadRequestException(
          'Reservation type cannot be its own parent',
        );
      }

      const parentReservationType = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.id, updateReservationTypeDto.parentId),
            eq(reservationTypes.businessId, existingReservationType.businessId),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!parentReservationType) {
        throw new BadRequestException('Parent reservation type not found');
      }
    }

    let mediaId = existingReservationType.image;
    let ogImageId = existingReservationType.ogImage;

    // Handle image update if provided
    if (imageFile) {
      mediaId = await this.mediaService.updateMediaReference(
        existingReservationType.image,
        imageFile,
        'reservation types',
        businessId,
        userId,
      );
    }

    // Handle OG image update if provided
    if (ogImageFile) {
      ogImageId = await this.mediaService.updateMediaReference(
        existingReservationType.ogImage,
        ogImageFile,
        'reservation types/og-images',
        businessId,
        userId,
      );
    }

    // Update slug if name is changing and slug not explicitly provided
    if (
      updateReservationTypeDto.name &&
      !updateReservationTypeDto.slug &&
      updateReservationTypeDto.name !== existingReservationType.name
    ) {
      updateReservationTypeDto.slug = updateReservationTypeDto.name
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '');
    }

    // Check for slug conflict if slug is being updated
    if (
      updateReservationTypeDto.slug &&
      updateReservationTypeDto.slug !== existingReservationType.slug
    ) {
      const slugConflict = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            eq(reservationTypes.slug, updateReservationTypeDto.slug),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (slugConflict) {
        throw new ConflictException(
          `A reservation type with the slug '${updateReservationTypeDto.slug}' already exists for this business`,
        );
      }
    }

    // Check for shortCode conflict if shortCode is being updated
    if (
      updateReservationTypeDto.shortCode !== undefined &&
      updateReservationTypeDto.shortCode !== existingReservationType.shortCode
    ) {
      if (updateReservationTypeDto.shortCode) {
        const shortCodeConflict = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.businessId, businessId),
              eq(
                reservationTypes.shortCode,
                updateReservationTypeDto.shortCode,
              ),
              eq(reservationTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (shortCodeConflict) {
          throw new ConflictException(
            `A reservation type with the short code '${updateReservationTypeDto.shortCode}' already exists for this business`,
          );
        }
      }
    }

    try {
      // Update the reservation type
      const [updatedReservationType] = await this.db
        .update(reservationTypes)
        .set({
          ...updateReservationTypeDto,
          image: mediaId,
          ogImage: ogImageId,
          updatedAt: new Date(),
        })
        .where(eq(reservationTypes.id, id))
        .returning();

      // Handle location associations if provided
      if (
        updateReservationTypeDto.isAllocatedToAllLocations !== undefined ||
        updateReservationTypeDto.locationIds !== undefined
      ) {
        await this.manageReservationTypeLocations(
          updatedReservationType.id,
          businessId,
          updateReservationTypeDto.isAllocatedToAllLocations ??
            existingReservationType.isAllocatedToAllLocations,
          updateReservationTypeDto.locationIds,
          userId,
        );
      }

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.RESERVATION_TYPE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToReservationTypeDto(updatedReservationType);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update reservation type: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the reservation type
    const existingReservationType = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(eq(reservationTypes.id, id), eq(reservationTypes.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!existingReservationType) {
      throw new NotFoundException(`Reservation type with ID ${id} not found`);
    }

    if (businessId !== existingReservationType.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this reservation type',
      );
    }

    // Check for child reservation types
    const childReservationTypes = await this.db
      .select()
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.parentId, id),
          eq(reservationTypes.isDeleted, false),
        ),
      );

    if (childReservationTypes.length > 0) {
      throw new BadRequestException(
        'Cannot delete reservation type with child reservation types. Please remove or reassign child reservation types first.',
      );
    }

    // Soft delete the reservation type
    await this.db
      .update(reservationTypes)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(reservationTypes.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.RESERVATION_TYPE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Reservation type with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    reservationTypeIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!reservationTypeIds || reservationTypeIds.length === 0) {
        throw new BadRequestException(
          'No reservation type IDs provided for deletion',
        );
      }

      // Get all reservation types that exist and belong to the business
      const existingReservationTypes = await this.db
        .select({
          id: reservationTypes.id,
          name: reservationTypes.name,
          businessId: reservationTypes.businessId,
        })
        .from(reservationTypes)
        .where(
          and(
            inArray(reservationTypes.id, reservationTypeIds),
            eq(reservationTypes.businessId, businessId),
            eq(reservationTypes.isDeleted, false),
          ),
        );

      if (existingReservationTypes.length === 0) {
        throw new NotFoundException(
          'No valid reservation types found for deletion',
        );
      }

      // Check if any of the found reservation types have child reservation types
      const reservationTypesWithChildren = await this.db
        .select({
          parentId: reservationTypes.parentId,
          childName: reservationTypes.name,
        })
        .from(reservationTypes)
        .where(
          and(
            inArray(
              reservationTypes.parentId,
              existingReservationTypes.map((c) => c.id),
            ),
            eq(reservationTypes.isDeleted, false),
          ),
        );

      if (reservationTypesWithChildren.length > 0) {
        const parentIds = [
          ...new Set(reservationTypesWithChildren.map((c) => c.parentId)),
        ];
        const parentNames = existingReservationTypes
          .filter((c) => parentIds.includes(c.id))
          .map((c) => c.name);

        throw new BadRequestException(
          `Cannot delete reservation types with child reservation types: ${parentNames.join(', ')}. Please remove or reassign child reservation types first.`,
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const reservationType of existingReservationTypes) {
          // Soft delete the reservation type
          await tx
            .update(reservationTypes)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(reservationTypes.id, reservationType.id));

          deletedIds.push(reservationType.id);

          // Log the activity for each deleted reservation type
          await this.activityLogService.logDelete(
            reservationType.id,
            EntityType.RESERVATION_TYPE,
            userId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} reservation types`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete reservation types: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<ReservationTypeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all reservation types with only essential fields
    const reservationTypeResults = await this.db
      .select({
        id: reservationTypes.id,
        name: reservationTypes.name,
        position: reservationTypes.position,
      })
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.isDeleted, false),
          eq(reservationTypes.status, ReservationTypeStatus.ACTIVE),
          eq(reservationTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(reservationTypes.position), asc(reservationTypes.id));

    // View logging removed to reduce activity log volume

    return reservationTypeResults.map((reservationType) => ({
      id: reservationType.id.toString(),
      name: reservationType.name,
      position: reservationType.position,
    }));
  }

  async findAllHierarchy(
    _userId: string,
    businessId: string | null,
  ): Promise<
    { id: string; name: string; parentId: string | null; position: number }[]
  > {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all reservation types with only the essential hierarchy fields
    const reservationTypeResults = await this.db
      .select({
        id: reservationTypes.id,
        name: reservationTypes.name,
        parentId: reservationTypes.parentId,
        position: reservationTypes.position,
      })
      .from(reservationTypes)
      .where(
        and(
          eq(reservationTypes.isDeleted, false),
          eq(reservationTypes.status, ReservationTypeStatus.ACTIVE),
          eq(reservationTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(reservationTypes.position), asc(reservationTypes.id));

    // View logging removed to reduce activity log volume

    return reservationTypeResults.map((reservationType) => ({
      id: reservationType.id.toString(),
      name: reservationType.name,
      parentId: reservationType.parentId?.toString() || null,
      position: reservationType.position,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createReservationTypeDto: CreateReservationTypeDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    const reservationType = await this.create(
      userId,
      businessId,
      createReservationTypeDto,
      imageFile,
      ogImageFile,
    );
    return { id: reservationType.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createReservationTypesDto: CreateReservationTypeDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<ReservationTypeDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (
        !createReservationTypesDto ||
        createReservationTypesDto.length === 0
      ) {
        throw new BadRequestException(
          'No reservation types provided for creation',
        );
      }

      // Validate that if images are provided, they don't exceed the number of reservation types
      if (imageFiles && imageFiles.length > createReservationTypesDto.length) {
        throw new BadRequestException(
          'Number of images cannot exceed number of reservation types',
        );
      }

      // Validate imageIndex values if provided
      if (imageFiles) {
        for (let i = 0; i < createReservationTypesDto.length; i++) {
          const reservationType = createReservationTypesDto[i];
          if (reservationType.imageIndex !== undefined) {
            if (reservationType.imageIndex < 0) {
              throw new BadRequestException(
                `Reservation type "${reservationType.name}" has invalid imageIndex: ${reservationType.imageIndex}. Must be 0 or greater.`,
              );
            }
            if (reservationType.imageIndex >= imageFiles.length) {
              throw new BadRequestException(
                `Reservation type "${reservationType.name}" has imageIndex ${reservationType.imageIndex} but only ${imageFiles.length} images provided.`,
              );
            }
          }
        }
      }

      // Generate slugs for reservation types that don't have them
      createReservationTypesDto.forEach((dto) => {
        if (!dto.slug) {
          dto.slug = dto.name
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '');
        }
      });

      // Check for duplicate names within the request
      const requestNames = createReservationTypesDto.map((dto) =>
        dto.name.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate reservation type names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check for duplicate slugs within the request
      const requestSlugs = createReservationTypesDto
        .map((dto) => dto.slug)
        .filter((slug): slug is string => Boolean(slug));
      const duplicateSlugs = requestSlugs.filter(
        (slug, index) => requestSlugs.indexOf(slug) !== index,
      );
      if (duplicateSlugs.length > 0) {
        throw new BadRequestException(
          `Duplicate reservation type slugs found in request: ${duplicateSlugs.join(', ')}`,
        );
      }

      // Check for duplicate shortCodes within the request
      const requestShortCodes = createReservationTypesDto
        .map((dto) => dto.shortCode)
        .filter((shortCode): shortCode is string => Boolean(shortCode));
      const duplicateShortCodes = requestShortCodes.filter(
        (shortCode, index) => requestShortCodes.indexOf(shortCode) !== index,
      );
      if (duplicateShortCodes.length > 0) {
        throw new BadRequestException(
          `Duplicate reservation type short codes found in request: ${duplicateShortCodes.join(', ')}`,
        );
      }

      // Check if any reservation types with the same names already exist for this business
      const existingReservationTypes = await this.db
        .select()
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            sql`LOWER(${reservationTypes.name}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(reservationTypes.isDeleted, false),
          ),
        );

      if (existingReservationTypes.length > 0) {
        const existingNames = existingReservationTypes.map((c) => c.name);
        throw new ConflictException(
          `Reservation types with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      // Check if any reservation types with the same slugs already exist for this business
      if (requestSlugs.length > 0) {
        const existingSlugReservationTypes = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.businessId, businessId),
              inArray(reservationTypes.slug, requestSlugs),
              eq(reservationTypes.isDeleted, false),
            ),
          );

        if (existingSlugReservationTypes.length > 0) {
          const existingSlugs = existingSlugReservationTypes.map((c) => c.slug);
          throw new ConflictException(
            `Reservation types with the following slugs already exist: ${existingSlugs.join(', ')}`,
          );
        }
      }

      // Check if any reservation types with the same shortCodes already exist for this business
      if (requestShortCodes.length > 0) {
        const existingShortCodeReservationTypes = await this.db
          .select()
          .from(reservationTypes)
          .where(
            and(
              eq(reservationTypes.businessId, businessId),
              inArray(reservationTypes.shortCode, requestShortCodes),
              eq(reservationTypes.isDeleted, false),
            ),
          );

        if (existingShortCodeReservationTypes.length > 0) {
          const existingShortCodes = existingShortCodeReservationTypes.map(
            (c) => c.shortCode,
          );
          throw new ConflictException(
            `Reservation types with the following short codes already exist: ${existingShortCodes.join(', ')}`,
          );
        }
      }

      const createdReservationTypes: ReservationTypeDto[] = [];

      // Use a transaction to ensure all reservation types are created or none are
      await this.db.transaction(async (tx) => {
        // Group reservation types by parent to assign proper sequential positions
        const reservationTypeGroups = new Map<
          string,
          CreateReservationTypeDto[]
        >();

        for (const dto of createReservationTypesDto) {
          const parentKey = dto.parentId || 'root';
          if (!reservationTypeGroups.has(parentKey)) {
            reservationTypeGroups.set(parentKey, []);
          }
          reservationTypeGroups.get(parentKey).push(dto);
        }

        // For each parent group, shift existing reservation types to make room for new ones at the beginning
        for (const [
          parentKey,
          groupReservationTypes,
        ] of reservationTypeGroups) {
          const parentId = parentKey === 'root' ? null : parentKey;

          // Shift existing reservation types down by the number of new reservation types being added
          await tx
            .update(reservationTypes)
            .set({
              position: sql`${reservationTypes.position} + ${groupReservationTypes.length}`,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(reservationTypes.businessId, businessId),
                parentId
                  ? eq(reservationTypes.parentId, parentId)
                  : isNull(reservationTypes.parentId),
                eq(reservationTypes.isDeleted, false),
              ),
            );
        }

        for (let i = 0; i < createReservationTypesDto.length; i++) {
          const createReservationTypeDto = createReservationTypesDto[i];
          const parentKey = createReservationTypeDto.parentId || 'root';

          // Calculate position within the parent group (starting from position 1)
          const groupReservationTypes = reservationTypeGroups.get(parentKey);
          const indexInGroup = groupReservationTypes.indexOf(
            createReservationTypeDto,
          );
          const reservationTypePosition = indexInGroup + 1; // Start from position 1

          // Get image file based on imageIndex if specified, otherwise use array index
          let imageFile: Express.Multer.File | undefined;
          if (createReservationTypeDto.imageIndex !== undefined) {
            // Use specific image index if provided
            imageFile = imageFiles?.[createReservationTypeDto.imageIndex];
          } else {
            // Fallback to array index mapping for backward compatibility
            imageFile = imageFiles?.[i];
          }

          let mediaId: string | undefined;

          // Upload image if provided for this reservation type
          if (imageFile) {
            const uploadedMedia = await this.mediaService.uploadMedia(
              imageFile,
              'reservation types',
              businessId,
              userId,
            );
            mediaId = uploadedMedia.id;
          }

          // Create the reservation type with proper position (starting from position 1)
          const [newReservationType] = await tx
            .insert(reservationTypes)
            .values({
              businessId,
              name: createReservationTypeDto.name,
              shortCode: createReservationTypeDto.shortCode,
              parentId: createReservationTypeDto.parentId,
              category: createReservationTypeDto.category,
              description: createReservationTypeDto.description,
              slug: createReservationTypeDto.slug,
              availableOnline:
                createReservationTypeDto.availableOnline ?? false,
              position: reservationTypePosition, // Position starts from 1 for new reservation types
              image: mediaId,
              isAllocatedToAllLocations:
                createReservationTypeDto.isAllocatedToAllLocations ?? false,
              seoTitle: createReservationTypeDto.seoTitle,
              seoDescription: createReservationTypeDto.seoDescription,
              seoKeywords: createReservationTypeDto.seoKeywords,
              createdBy: userId,
              status:
                createReservationTypeDto.status ?? ReservationTypeStatus.ACTIVE,
            })
            .returning();

          // Handle location associations
          await this.manageReservationTypeLocations(
            newReservationType.id,
            businessId,
            createReservationTypeDto.isAllocatedToAllLocations ?? false,
            createReservationTypeDto.locationIds,
            userId,
          );

          // Log the reservation type creation activity
          await this.activityLogService.logCreate(
            newReservationType.id,
            EntityType.RESERVATION_TYPE,
            userId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );

          createdReservationTypes.push(
            await this.mapToReservationTypeDto(newReservationType),
          );
        }
      });

      return createdReservationTypes;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create reservation types: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createReservationTypesDto: CreateReservationTypeDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    const reservationTypes = await this.bulkCreate(
      userId,
      businessId,
      createReservationTypesDto,
      imageFiles,
    );
    return {
      ids: reservationTypes.map((reservationType) => reservationType.id),
    };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateReservationTypeDto: UpdateReservationTypeDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    await this.update(
      userId,
      businessId,
      id,
      updateReservationTypeDto,
      imageFile,
      ogImageFile,
    );
    return { id };
  }

  async updateReservationTypePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for reservation type ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all reservation types belong to the business
      const reservationTypeIds = updates.map((update) => update.id);
      const existingReservationTypes = await this.db
        .select({
          id: reservationTypes.id,
          parentId: reservationTypes.parentId,
          currentPosition: reservationTypes.position,
        })
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            inArray(reservationTypes.id, reservationTypeIds),
            eq(reservationTypes.isDeleted, false),
          ),
        );

      if (existingReservationTypes.length !== reservationTypeIds.length) {
        const foundIds = existingReservationTypes.map((cat) => cat.id);
        const missingIds = reservationTypeIds.filter(
          (id) => !foundIds.includes(id),
        );
        throw new BadRequestException(
          `Reservation types not found or don't belong to this business: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdatePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.logUpdate(
            update.id,
            EntityType.RESERVATION_TYPE,
            userId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        // This is much more efficient than always normalizing
        await this.conditionalNormalizePositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update reservation type positions: ${error.message}`,
      );
    }
  }

  async bulkUpdateReservationTypeHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      const failed: Array<{ id: string; error: string }> = [];
      let updatedCount = 0;

      // Validate that all reservation types belong to the business
      const reservationTypeIds = updates.map((update) => update.id);
      const existingReservationTypes = await this.db
        .select({
          id: reservationTypes.id,
          parentId: reservationTypes.parentId,
        })
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            inArray(reservationTypes.id, reservationTypeIds),
            eq(reservationTypes.isDeleted, false),
          ),
        );

      const existingReservationTypeMap = new Map(
        existingReservationTypes.map((cat) => [cat.id, cat]),
      );

      // Use a transaction to ensure data consistency
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          try {
            // Check if reservation type exists
            if (!existingReservationTypeMap.has(update.id)) {
              failed.push({
                id: update.id,
                error:
                  'Reservation type not found or does not belong to this business',
              });
              continue;
            }

            // Check if parent exists (if parentId is not null)
            if (update.parentId !== null) {
              const parentExists = await tx
                .select({ id: reservationTypes.id })
                .from(reservationTypes)
                .where(
                  and(
                    eq(reservationTypes.id, update.parentId),
                    eq(reservationTypes.businessId, businessId),
                    eq(reservationTypes.isDeleted, false),
                  ),
                )
                .limit(1);

              if (parentExists.length === 0) {
                failed.push({
                  id: update.id,
                  error: 'Parent reservation type not found',
                });
                continue;
              }

              // Check for circular reference (reservation type cannot be its own ancestor)
              if (update.parentId === update.id) {
                failed.push({
                  id: update.id,
                  error: 'Reservation type cannot be its own parent',
                });
                continue;
              }

              // Check if this would create a circular reference
              const wouldCreateCircle = async (
                parentId: string,
                childId: string,
              ): Promise<boolean> => {
                const parent = await tx
                  .select({ parentId: reservationTypes.parentId })
                  .from(reservationTypes)
                  .where(
                    and(
                      eq(reservationTypes.id, parentId),
                      eq(reservationTypes.businessId, businessId),
                      eq(reservationTypes.isDeleted, false),
                    ),
                  )
                  .limit(1);

                if (parent.length === 0) return false;
                if (parent[0].parentId === childId) return true;
                if (parent[0].parentId)
                  return wouldCreateCircle(parent[0].parentId, childId);
                return false;
              };

              if (await wouldCreateCircle(update.parentId, update.id)) {
                failed.push({
                  id: update.id,
                  error: 'Would create circular reference',
                });
                continue;
              }
            }

            // Update the reservation type
            await tx
              .update(reservationTypes)
              .set({
                parentId: update.parentId,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(reservationTypes.id, update.id),
                  eq(reservationTypes.businessId, businessId),
                  eq(reservationTypes.isDeleted, false),
                ),
              );

            updatedCount++;

            // Log the hierarchy update activity
            await this.activityLogService.logUpdate(
              update.id,
              EntityType.RESERVATION_TYPE,
              userId,
              businessId,
              {
                source: ActivitySource.WEB,
              },
            );
          } catch (error) {
            failed.push({
              id: update.id,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return { updated: updatedCount, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update reservation type hierarchy: ${error.message}`,
      );
    }
  }

  private async getProductsCountForReservationType(
    reservationTypeId: string,
  ): Promise<number> {
    try {
      // Reservation types don't have direct product relationships like categories do
      // This method returns 0 as reservation types are separate entities from products
      // If reservation type-product relationships are needed in the future,
      // the products schema would need to be updated with reservationTypeId fields
      return 0;
    } catch (error) {
      console.warn(
        `Failed to get products count for reservation type ${reservationTypeId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getProductsCountForReservationTypes(
    reservationTypeIds: string[],
  ): Promise<Map<string, number>> {
    try {
      // Reservation types don't have direct product relationships like categories do
      // This method returns 0 for all reservation types as they are separate entities from products
      // If reservation type-product relationships are needed in the future,
      // the products schema would need to be updated with reservationTypeId fields

      const countsMap = new Map<string, number>();
      // Initialize all reservation type IDs with 0
      reservationTypeIds.forEach((id) => countsMap.set(id, 0));
      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get products count for reservation types:',
        error.message,
      );
      return new Map();
    }
  }

  private async mapToReservationTypeDto(
    reservationType: typeof reservationTypes.$inferSelect,
  ): Promise<ReservationTypeDto> {
    // Get products count for this reservation type
    const productsCount = await this.getProductsCountForReservationType(
      reservationType.id,
    );

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      reservationType.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (reservationType.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        reservationType.updatedBy.toString(),
      );
    }

    // Get reservation type locations if not allocated to all locations
    let locations: { id: string; name: string }[] = [];
    if (!reservationType.isAllocatedToAllLocations) {
      locations = await this.getReservationTypeLocations(reservationType.id);
    }

    const reservationTypeDto: ReservationTypeDto = {
      id: reservationType.id.toString(),
      businessId: reservationType.businessId.toString(),
      name: reservationType.name,
      shortCode: reservationType.shortCode,
      parentId: reservationType.parentId?.toString(),
      description: reservationType.description,
      slug: reservationType.slug,
      availableOnline: reservationType.availableOnline,
      position: reservationType.position,
      category: reservationType.category,
      isAllocatedToAllLocations: reservationType.isAllocatedToAllLocations,
      locations,
      seoTitle: reservationType.seoTitle,
      seoDescription: reservationType.seoDescription,
      seoKeywords: reservationType.seoKeywords,
      createdBy: createdByName,
      updatedBy: updatedByName,
      status: reservationType.status,
      productsCount,
      createdAt: reservationType.createdAt,
      updatedAt: reservationType.updatedAt,
    };

    // Fetch media information and generate signed URL if image exists
    if (reservationType.image) {
      try {
        const mediaData = await this.mediaService.findById(
          reservationType.image,
          reservationType.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the image
        reservationTypeDto.image =
          await this.gcsUploadService.generateSignedUrl(
            mediaData.fileName,
            'reservation types', // folder where reservation type images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for reservation type ${reservationType.id} image:`,
          error.message,
        );
      }
    }

    // Fetch OG image information and generate signed URL if ogImage exists
    if (reservationType.ogImage) {
      try {
        const ogMediaData = await this.mediaService.findById(
          reservationType.ogImage,
          reservationType.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the OG image
        reservationTypeDto.ogImage =
          await this.gcsUploadService.generateSignedUrl(
            ogMediaData.fileName,
            'reservation types/og-images', // folder where OG images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for reservation type ${reservationType.id} OG image:`,
          error.message,
        );
      }
    }

    return reservationTypeDto;
  }

  /**
   * Conditionally normalize positions only when needed
   * This is much more efficient than always normalizing
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizePositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          parentId: reservationTypes.parentId,
          positions: sql<
            number[]
          >`array_agg(${reservationTypes.position} ORDER BY ${reservationTypes.position})`,
          count: sql<number>`count(*)`,
        })
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .groupBy(reservationTypes.parentId);

      const needsNormalization = positionCheck.some((group: any) => {
        const positions = group.positions;
        const expectedPositions = Array.from(
          { length: group.count },
          (_, i) => i + 1,
        );
        return JSON.stringify(positions) !== JSON.stringify(expectedPositions);
      });

      if (needsNormalization) {
        await this.normalizePositions(tx, businessId);
      }
    } catch (error) {
      console.warn('Failed to check position normalization:', error.message);
      // Fallback to full normalization if check fails
      await this.normalizePositions(tx, businessId);
    }
  }

  /**
   * Normalize positions to ensure sequential ordering without gaps
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async normalizePositions(tx: any, businessId: string): Promise<void> {
    try {
      // Get all reservation types ordered by current position and createdAt
      const allReservationTypes = await tx
        .select({
          id: reservationTypes.id,
          parentId: reservationTypes.parentId,
          position: reservationTypes.position,
        })
        .from(reservationTypes)
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            eq(reservationTypes.isDeleted, false),
          ),
        )
        .orderBy(
          asc(reservationTypes.position),
          asc(reservationTypes.createdAt),
        );

      // Group reservation types by parent level
      const reservationTypeGroups = new Map<
        string,
        typeof allReservationTypes
      >();

      for (const reservationType of allReservationTypes) {
        const parentKey = reservationType.parentId || 'root';
        if (!reservationTypeGroups.has(parentKey)) {
          reservationTypeGroups.set(parentKey, []);
        }
        reservationTypeGroups.get(parentKey)!.push(reservationType);
      }

      // Batch normalize positions for each group using optimized queries
      for (const [, groupReservationTypes] of reservationTypeGroups) {
        const updates = groupReservationTypes
          .map(
            (
              reservationType: { id: string; position: number },
              index: number,
            ) => ({
              id: reservationType.id,
              position: index + 1,
            }),
          )
          .filter(
            (update: { id: string; position: number }, index: number) =>
              groupReservationTypes[index].position !== update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdatePositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn('Failed to normalize positions:', error.message);
      // Don't throw error as this is a cleanup operation
    }
  }

  /**
   * Optimized reorder positions when inserting a room type at a specific position
   * Uses batch updates and optimized queries for better performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param parentId - The parent reservation type ID
   * @param insertPosition - The position where the new reservation type will be inserted
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    parentId: string | null,
    insertPosition: number,
  ): Promise<void> {
    try {
      // Use a single optimized query with proper indexing
      // This query will be much faster with the composite index on (businessId, parentId, position, deletedAt)
      await tx
        .update(reservationTypes)
        .set({
          position: sql`${reservationTypes.position} + 1`,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(reservationTypes.businessId, businessId),
            parentId
              ? eq(reservationTypes.parentId, parentId)
              : isNull(reservationTypes.parentId),
            gte(reservationTypes.position, insertPosition),
            eq(reservationTypes.isDeleted, false),
          ),
        );
    } catch (error) {
      console.warn('Failed to reorder positions:', error.message);
      throw error; // This is critical for data consistency
    }
  }

  /**
   * Optimized batch position update method
   * Reduces the number of database operations and improves performance
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdatePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(reservationTypes)
            .set({
              position: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(reservationTypes.id, update.id),
                eq(reservationTypes.businessId, businessId),
                eq(reservationTypes.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      // This approach is more reliable than raw SQL with arrays
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(reservationTypes)
          .set({
            position: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(reservationTypes.id, update.id),
              eq(reservationTypes.businessId, businessId),
              eq(reservationTypes.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update positions:', error.message);
      throw error;
    }
  }

  /**
   * Validate location IDs belong to the business
   */
  private async validateLocationIds(
    businessId: string,
    locationIds: string[],
  ): Promise<void> {
    if (!locationIds || locationIds.length === 0) {
      return;
    }

    const validLocations = await this.db
      .select({ id: locations.id })
      .from(locations)
      .where(
        and(
          eq(locations.businessId, businessId),
          inArray(locations.id, locationIds),
          eq(locations.isDeleted, false),
        ),
      );

    if (validLocations.length !== locationIds.length) {
      throw new BadRequestException(
        'Some location IDs do not exist or belong to this business',
      );
    }
  }

  /**
   * Manage reservation type location associations
   */
  private async manageReservationTypeLocations(
    reservationTypeId: string,
    businessId: string,
    isAllocatedToAllLocations: boolean,
    locationIds: string[] | undefined,
    userId: string,
  ): Promise<void> {
    // Delete existing location associations
    await this.db
      .delete(reservationTypeLocations)
      .where(eq(reservationTypeLocations.reservationTypeId, reservationTypeId));

    // If allocated to all locations, no need to create specific associations
    if (isAllocatedToAllLocations) {
      return;
    }

    // If specific locations are provided, create associations
    if (locationIds && locationIds.length > 0) {
      await this.validateLocationIds(businessId, locationIds);

      const locationAssociations = locationIds.map((locationId) => ({
        reservationTypeId,
        locationId,
        businessId,
        createdBy: userId,
      }));

      await this.db
        .insert(reservationTypeLocations)
        .values(locationAssociations);
    }
  }

  /**
   * Get reservation type location associations
   */
  private async getReservationTypeLocations(reservationTypeId: string): Promise<
    {
      id: string;
      name: string;
    }[]
  > {
    const reservationTypeLocationData = await this.db
      .select({
        id: locations.id,
        name: locations.name,
      })
      .from(reservationTypeLocations)
      .innerJoin(
        locations,
        eq(reservationTypeLocations.locationId, locations.id),
      )
      .where(
        and(
          eq(reservationTypeLocations.reservationTypeId, reservationTypeId),
          eq(locations.isDeleted, false),
        ),
      )
      .orderBy(locations.name);

    return reservationTypeLocationData;
  }
}
