import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { BusinessRolesService } from './business-roles.service';
import { CreateBusinessRoleDto } from './dto/create-business-role.dto';
import { UpdateBusinessRoleDto } from './dto/update-business-role.dto';
import { BusinessRoleDto } from './dto/business-role.dto';
import { BusinessRoleSlimDto } from './dto/business-role-slim.dto';
import { BusinessRoleIdResponseDto } from './dto/business-role-id-response.dto';
import { BulkDeleteBusinessRoleResponseDto } from './dto/bulk-delete-business-role-response.dto';
import { BulkDeleteBusinessRoleDto } from './dto/bulk-delete-business-role.dto';
import { BulkUpdateBusinessRoleStatusDto } from './dto/bulk-update-business-role-status.dto';
import { BulkUpdateBusinessRoleStatusResponseDto } from './dto/bulk-update-business-role-status-response.dto';
import { PaginatedBusinessRolesResponseDto } from './dto/paginated-business-roles-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { BusinessRoleNameAvailabilityResponseDto } from './dto/check-business-role-name.dto';
import { DeleteBusinessRoleResponseDto } from './dto/delete-business-role-response.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('business-roles')
@Controller('business-roles')
@UseGuards(PermissionsGuard)
export class BusinessRolesController {
  constructor(private readonly businessRolesService: BusinessRolesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_CREATE)
  @ApiOperation({
    summary: 'Create a new business role',
    description: 'Creates a new business role with the provided details',
  })
  @ApiResponse({
    status: 201,
    description: 'Business role created successfully',
    type: BusinessRoleIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Business role name already exists',
  })
  create(
    @Request() req: any,
    @Body() createBusinessRoleDto: CreateBusinessRoleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessRoleIdResponseDto> {
    return this.businessRolesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createBusinessRoleDto,
      metadata,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_READ)
  @ApiOperation({
    summary: 'Get all business roles for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter business roles created from this date (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter business roles created until this date (ISO string)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by business role name (partial match)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by business role status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'filters',
    description: 'Additional filters as JSON string',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for filters (and/or)',
    required: false,
    enum: ['and', 'or'],
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort field and direction (e.g., name:asc, createdAt:desc)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Business roles retrieved successfully',
    type: PaginatedBusinessRolesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedBusinessRolesResponseDto> {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;

    return this.businessRolesService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      pageNum,
      limitNum,
      from,
      to,
      name,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_READ)
  @ApiOperation({
    summary: 'Get all business roles in slim format',
    description:
      'Returns business roles with only essential fields for dropdowns and lists',
  })
  @ApiResponse({
    status: 200,
    description: 'Business roles retrieved successfully',
    type: [BusinessRoleSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: any): Promise<BusinessRoleSlimDto[]> {
    return this.businessRolesService.findAllSlim(
      req.user.id,
      req.user.activeBusinessId,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_READ)
  @ApiOperation({
    summary: 'Check if business role name is available',
    description: 'Checks if a business role name is available for use',
  })
  @ApiQuery({
    name: 'name',
    description: 'Business role name to check',
    required: true,
    type: String,
  })
  @ApiQuery({
    name: 'excludeId',
    description: 'Business role ID to exclude from the check (for updates)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Name availability checked successfully',
    type: BusinessRoleNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Name parameter is required',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req: any,
    @Query('name') name: string,
    @Query('excludeId') excludeId?: string,
  ): Promise<BusinessRoleNameAvailabilityResponseDto> {
    return this.businessRolesService.checkNameAvailability(
      req.user.activeBusinessId,
      name,
      excludeId,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_READ)
  @ApiOperation({
    summary: 'Get a business role by ID',
    description: 'Retrieves a specific business role by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Business role ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Business role retrieved successfully',
    type: BusinessRoleDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Business role not found',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<BusinessRoleDto> {
    return this.businessRolesService.findOne(id, req.user.activeBusinessId);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_UPDATE)
  @ApiOperation({
    summary: 'Update a business role',
    description: 'Updates an existing business role with the provided details',
  })
  @ApiParam({
    name: 'id',
    description: 'Business role ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Business role updated successfully',
    type: BusinessRoleIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Business role not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Business role name already exists',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateBusinessRoleDto: UpdateBusinessRoleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BusinessRoleIdResponseDto> {
    return this.businessRolesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateBusinessRoleDto,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_DELETE)
  @ApiOperation({ summary: 'Bulk delete business roles' })
  @ApiBody({
    description: 'Array of business role IDs to delete',
    type: BulkDeleteBusinessRoleDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Business roles deleted successfully',
    type: BulkDeleteBusinessRoleResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - No business roles found to delete',
  })
  bulkDelete(
    @Request() req: any,
    @Body() bulkDeleteBusinessRoleDto: BulkDeleteBusinessRoleDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteBusinessRoleResponseDto> {
    return this.businessRolesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteBusinessRoleDto.businessRoleIds,
      metadata,
    );
  }

  @Patch('bulk/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_UPDATE)
  @ApiOperation({ summary: 'Bulk update business role status' })
  @ApiBody({
    description: 'Business role IDs and new status',
    type: BulkUpdateBusinessRoleStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Business role status updated successfully',
    type: BulkUpdateBusinessRoleStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - No business roles found to update',
  })
  bulkUpdateStatus(
    @Request() req: any,
    @Body() bulkUpdateBusinessRoleStatusDto: BulkUpdateBusinessRoleStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateBusinessRoleStatusResponseDto> {
    return this.businessRolesService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateBusinessRoleStatusDto.businessRoleIds,
      bulkUpdateBusinessRoleStatusDto.status,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.BUSINESS_ROLE_DELETE)
  @ApiOperation({
    summary: 'Delete a business role',
    description: 'Soft deletes a business role by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Business role ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Business role deleted successfully',
    type: DeleteBusinessRoleResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Business role not found',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteBusinessRoleResponseDto> {
    return this.businessRolesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
