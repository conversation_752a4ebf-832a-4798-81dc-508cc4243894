import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateBusinessRoleDto } from './dto/create-business-role.dto';
import { UpdateBusinessRoleDto } from './dto/update-business-role.dto';
import { BusinessRoleDto } from './dto/business-role.dto';
import { BusinessRoleSlimDto } from './dto/business-role-slim.dto';
import { BusinessRoleListDto } from './dto/business-role-list.dto';
import { businessRoles } from '../drizzle/schema/business-roles.schema';
import {
  and,
  eq,
  ilike,
  isNull,
  gte,
  lte,
  or,
  asc,
  desc,
  sql,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { BusinessRoleStatus } from '../shared/types/business.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class BusinessRolesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createBusinessRoleDto: CreateBusinessRoleDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a business role with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingBusinessRole = await this.db
        .select()
        .from(businessRoles)
        .where(
          and(
            eq(businessRoles.businessId, businessId),
            ilike(businessRoles.name, createBusinessRoleDto.name),
            isNull(businessRoles.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingBusinessRole) {
        throw new ConflictException(
          `Business role with name "${createBusinessRoleDto.name}" already exists`,
        );
      }

      // Create the business role
      const [newBusinessRole] = await this.db
        .insert(businessRoles)
        .values({
          businessId,
          name: createBusinessRoleDto.name,
          description: createBusinessRoleDto.description,
          permissions: createBusinessRoleDto.permissions,
          createdBy: userId,
          status: createBusinessRoleDto.status ?? BusinessRoleStatus.ACTIVE,
        })
        .returning();

      // Log the business role creation activity
      await this.activityLogService.logCreate(
        newBusinessRole.id,
        EntityType.BUSINESS,
        userId,
        businessId,
        {
          reason: `Business role "${createBusinessRoleDto.name}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newBusinessRole.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create business role');
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: BusinessRoleDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(businessRoles.deletedAt),
      eq(businessRoles.status, BusinessRoleStatus.ACTIVE),
      eq(businessRoles.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(businessRoles.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(businessRoles.createdAt, toDate));
      }
    }

    // Get business roles with pagination
    const businessRoleResults = await this.db
      .select()
      .from(businessRoles)
      .where(and(...whereConditions))
      .limit(limit)
      .offset(offset)
      .orderBy(asc(businessRoles.createdAt));

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(businessRoles)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: businessRoleResults.map((businessRole) =>
        this.mapToBusinessRoleDto(businessRole),
      ),
      meta: { total, page, totalPages },
    };
  }

  async findAllByBusiness(
    userId: string,
    businessId: string,
  ): Promise<BusinessRoleDto[]> {
    const result = await this.db
      .select()
      .from(businessRoles)
      .where(
        and(
          isNull(businessRoles.deletedAt),
          eq(businessRoles.status, BusinessRoleStatus.ACTIVE),
          eq(businessRoles.businessId, businessId),
        ),
      )
      .orderBy(asc(businessRoles.createdAt));

    // Disable view logging for performance

    return result.map((businessRole) =>
      this.mapToBusinessRoleDto(businessRole),
    );
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    businessRoles: BusinessRoleSlimDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(businessRoles.deletedAt),
      eq(businessRoles.businessId, businessId),
    ];

    // Add date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(businessRoles.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(businessRoles.createdAt, toDate));
      }
    }

    // Build filter conditions
    const filterConditions = [];

    if (name) {
      filterConditions.push(ilike(businessRoles.name, `%${name}%`));
    }

    if (
      status &&
      Object.values(BusinessRoleStatus).includes(status as BusinessRoleStatus)
    ) {
      filterConditions.push(
        eq(businessRoles.status, status as BusinessRoleStatus),
      );
    }

    // Parse additional filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        for (const [key, value] of Object.entries(parsedFilters)) {
          if (key === 'description' && typeof value === 'string') {
            filterConditions.push(
              ilike(businessRoles.description, `%${value}%`),
            );
          }
        }
      } catch (error) {
        // Ignore invalid JSON filters
      }
    }

    // Combine filter conditions based on join operator
    if (filterConditions.length > 0) {
      if (joinOperator === 'or') {
        whereConditions.push(or(...filterConditions));
      } else {
        whereConditions.push(and(...filterConditions));
      }
    }

    // Build order by clause
    let orderBy = asc(businessRoles.createdAt);
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'name':
          orderBy = isDesc ? desc(businessRoles.name) : asc(businessRoles.name);
          break;
        case 'status':
          orderBy = isDesc
            ? desc(businessRoles.status)
            : asc(businessRoles.status);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(businessRoles.createdAt)
            : asc(businessRoles.createdAt);
          break;
        case 'updatedAt':
          orderBy = isDesc
            ? desc(businessRoles.updatedAt)
            : asc(businessRoles.updatedAt);
          break;
        default:
          orderBy = asc(businessRoles.createdAt);
      }
    }

    // Get business roles with pagination
    const businessRoleResults = await this.db
      .select()
      .from(businessRoles)
      .where(and(...whereConditions))
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    // Get total count
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(businessRoles)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      businessRoles: businessRoleResults.map((businessRole) =>
        this.mapToBusinessRoleSlimDto(businessRole),
      ),
      total,
      page,
      limit,
      totalPages,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<BusinessRoleSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all business roles with only essential fields
    const businessRoleResults = await this.db
      .select({
        id: businessRoles.id,
        name: businessRoles.name,
        description: businessRoles.description,
        permissions: businessRoles.permissions,
        status: businessRoles.status,
        createdAt: businessRoles.createdAt,
        updatedAt: businessRoles.updatedAt,
      })
      .from(businessRoles)
      .where(
        and(
          isNull(businessRoles.deletedAt),
          eq(businessRoles.status, BusinessRoleStatus.ACTIVE),
          eq(businessRoles.businessId, businessId),
        ),
      )
      .orderBy(asc(businessRoles.createdAt));

    // Disable view logging for performance in slim queries

    return businessRoleResults.map((businessRole) => ({
      id: businessRole.id.toString(),
      name: businessRole.name,
      description: businessRole.description,
      permissions: businessRole.permissions as any,
      status: businessRole.status,
      createdAt: businessRole.createdAt,
      updatedAt: businessRole.updatedAt,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createBusinessRoleDto: CreateBusinessRoleDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const businessRole = await this.create(
      userId,
      businessId,
      createBusinessRoleDto,
      metadata,
    );
    return { id: businessRole.id };
  }

  async findOne(
    id: string,
    businessId: string | null,
  ): Promise<BusinessRoleDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const businessRole = await this.db
      .select()
      .from(businessRoles)
      .where(
        and(
          eq(businessRoles.id, id),
          eq(businessRoles.businessId, businessId),
          isNull(businessRoles.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!businessRole) {
      throw new NotFoundException('Business role not found');
    }

    return this.mapToBusinessRoleDto(businessRole);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateBusinessRoleDto: UpdateBusinessRoleDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if business role exists
      const existingBusinessRole = await this.db
        .select()
        .from(businessRoles)
        .where(
          and(
            eq(businessRoles.id, id),
            eq(businessRoles.businessId, businessId),
            isNull(businessRoles.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingBusinessRole) {
        throw new NotFoundException('Business role not found');
      }

      // Check if name is being updated and if it conflicts with existing names
      if (
        updateBusinessRoleDto.name &&
        updateBusinessRoleDto.name !== existingBusinessRole.name
      ) {
        const conflictingBusinessRole = await this.db
          .select()
          .from(businessRoles)
          .where(
            and(
              eq(businessRoles.businessId, businessId),
              ilike(businessRoles.name, updateBusinessRoleDto.name),
              isNull(businessRoles.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (conflictingBusinessRole) {
          throw new ConflictException(
            `Business role with name "${updateBusinessRoleDto.name}" already exists`,
          );
        }
      }

      // Update the business role
      const [updatedBusinessRole] = await this.db
        .update(businessRoles)
        .set({
          name: updateBusinessRoleDto.name,
          description: updateBusinessRoleDto.description,
          permissions: updateBusinessRoleDto.permissions,
          status: updateBusinessRoleDto.status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(businessRoles.id, id))
        .returning();

      // Log the business role update activity
      await this.activityLogService.logUpdate(
        updatedBusinessRole.id,
        EntityType.BUSINESS,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedBusinessRole.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update business role');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateBusinessRoleDto: UpdateBusinessRoleDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updateBusinessRoleDto, metadata);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ message: string; id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if business role exists
      const existingBusinessRole = await this.db
        .select()
        .from(businessRoles)
        .where(
          and(
            eq(businessRoles.id, id),
            eq(businessRoles.businessId, businessId),
            isNull(businessRoles.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingBusinessRole) {
        throw new NotFoundException('Business role not found');
      }

      // Soft delete the business role
      await this.db
        .update(businessRoles)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(businessRoles.id, id));

      // Log the business role deletion activity
      await this.activityLogService.logDelete(
        existingBusinessRole.id,
        EntityType.BUSINESS,
        userId,
        businessId,
        {
          reason: `Business role "${existingBusinessRole.name}" was deleted`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        message: 'Business role deleted successfully',
        id: existingBusinessRole.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete business role');
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    businessRoleIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!businessRoleIds || businessRoleIds.length === 0) {
        throw new BadRequestException('No business role IDs provided');
      }

      // Check which business roles exist and belong to the business
      const existingBusinessRoles = await this.db
        .select()
        .from(businessRoles)
        .where(
          and(
            inArray(businessRoles.id, businessRoleIds),
            eq(businessRoles.businessId, businessId),
            isNull(businessRoles.deletedAt),
          ),
        );

      if (existingBusinessRoles.length === 0) {
        throw new NotFoundException('No business roles found to delete');
      }

      const existingIds = existingBusinessRoles.map((role) => role.id);

      // Soft delete the business roles
      await this.db
        .update(businessRoles)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(inArray(businessRoles.id, existingIds));

      // Log bulk deletion operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.BUSINESS,
        existingIds,
        {
          names: existingBusinessRoles.map((role) => role.name),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: existingBusinessRoles.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deletedCount: existingBusinessRoles.length,
        deletedIds: existingIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to bulk delete business roles');
    }
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    businessRoleIds: string[],
    status: BusinessRoleStatus,
    metadata?: ActivityMetadata,
  ): Promise<{ updatedCount: number; updatedIds: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!businessRoleIds || businessRoleIds.length === 0) {
        throw new BadRequestException('No business role IDs provided');
      }

      // Check which business roles exist and belong to the business
      const existingBusinessRoles = await this.db
        .select()
        .from(businessRoles)
        .where(
          and(
            inArray(businessRoles.id, businessRoleIds),
            eq(businessRoles.businessId, businessId),
            isNull(businessRoles.deletedAt),
          ),
        );

      if (existingBusinessRoles.length === 0) {
        throw new NotFoundException('No business roles found to update');
      }

      const existingIds = existingBusinessRoles.map((role) => role.id);

      // Update the status of business roles
      await this.db
        .update(businessRoles)
        .set({
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(businessRoles.id, existingIds));

      // Log bulk status update operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_STATUS_CHANGE,
        EntityType.BUSINESS,
        existingIds,
        {
          status,
          names: existingBusinessRoles.map((role) => role.name),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: existingBusinessRoles.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        updatedCount: existingBusinessRoles.length,
        updatedIds: existingIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk update business role status',
      );
    }
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
    excludeId?: string,
  ): Promise<{ available: boolean; name: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(businessRoles.businessId, businessId),
      ilike(businessRoles.name, name),
      isNull(businessRoles.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${businessRoles.id} != ${excludeId}`);
    }

    const existingBusinessRole = await this.db
      .select()
      .from(businessRoles)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return {
      available: !existingBusinessRole,
      name,
    };
  }

  // Helper methods for mapping
  private mapToBusinessRoleDto(businessRole: any): BusinessRoleDto {
    return {
      id: businessRole.id.toString(),
      businessId: businessRole.businessId,
      name: businessRole.name,
      description: businessRole.description,
      permissions: businessRole.permissions,
      status: businessRole.status,
      createdBy: businessRole.createdBy,
      updatedBy: businessRole.updatedBy,
      deletedBy: businessRole.deletedBy,
      deletedAt: businessRole.deletedAt,
      createdAt: businessRole.createdAt,
      updatedAt: businessRole.updatedAt,
    };
  }

  private mapToBusinessRoleSlimDto(businessRole: any): BusinessRoleSlimDto {
    return {
      id: businessRole.id.toString(),
      name: businessRole.name,
      description: businessRole.description,
      permissions: businessRole.permissions,
      status: businessRole.status,
      createdAt: businessRole.createdAt,
      updatedAt: businessRole.updatedAt,
    };
  }
}
