import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { eq, and, desc, count, isNull, gte, lte } from 'drizzle-orm';
import { subscriptionPayments } from '../drizzle/schema/subscription-payments.schema';
import { PaymentStatus } from '@app/shared/types/common.enum';
import { businessSubscriptions } from '../drizzle/schema/business-subscriptions.schema';
import { businessUsers } from '../drizzle/schema/business-users.schema';
import { BusinessUserStatus } from '../shared/types';
import {
  CreatePaymentDto,
  UpdatePaymentDto,
  ProcessPaymentDto,
  RefundPaymentDto,
  PaymentResponseDto,
  PaymentListResponseDto,
  PaymentStatusUpdateDto,
  PaymentSummaryDto,
} from './dto/payment.dto';

@Injectable()
export class PaymentService {
  constructor(@Inject(DRIZZLE) private readonly db: DrizzleDB) {}

  /**
   * Create a new payment record
   */
  async createPayment(
    businessId: string,
    userId: string,
    createPaymentDto: CreatePaymentDto,
  ): Promise<PaymentResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    // Verify subscription belongs to the business
    const subscription = await this.db
      .select({ id: businessSubscriptions.id })
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, createPaymentDto.subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    // Check for duplicate invoice number
    const existingPayment = await this.db
      .select({ id: subscriptionPayments.id })
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.businessId, businessId),
          eq(
            subscriptionPayments.invoiceNumber,
            createPaymentDto.invoiceNumber,
          ),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (existingPayment) {
      throw new BadRequestException('Invoice number already exists');
    }

    // Create the payment record
    const [newPayment] = await this.db
      .insert(subscriptionPayments)
      .values({
        businessId,
        subscriptionId: createPaymentDto.subscriptionId,
        invoiceNumber: createPaymentDto.invoiceNumber,
        invoiceType: createPaymentDto.invoiceType,
        amount: createPaymentDto.amount.toString(),
        currency: createPaymentDto.currency || 'LKR',
        paymentMethod: createPaymentDto.paymentMethod,
        transactionId: createPaymentDto.transactionId,
        referenceNumber: createPaymentDto.referenceNumber,
        invoiceDate: createPaymentDto.invoiceDate,
        dueDate: createPaymentDto.dueDate,
        billingPeriodStart: createPaymentDto.billingPeriodStart,
        billingPeriodEnd: createPaymentDto.billingPeriodEnd,
        gatewayProvider: createPaymentDto.gatewayProvider,
        gatewayTransactionId: createPaymentDto.gatewayTransactionId,
        taxAmount: createPaymentDto.taxAmount?.toString() || '0.00',
        processingFee: createPaymentDto.processingFee?.toString() || '0.00',
        discountAmount: createPaymentDto.discountAmount?.toString() || '0.00',
        notes: createPaymentDto.notes,
        paymentInstructions: createPaymentDto.paymentInstructions,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning();

    return this.mapToResponseDto(newPayment);
  }

  /**
   * Get payments for a business subscription
   */
  async getSubscriptionPayments(
    businessId: string,
    subscriptionId: string,
    userId: string,
    page = 1,
    limit = 10,
  ): Promise<PaymentListResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    // Verify subscription belongs to the business
    await this.verifySubscriptionAccess(subscriptionId, businessId);

    const offset = (page - 1) * limit;

    // Get payments with pagination
    const payments = await this.db
      .select()
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.subscriptionId, subscriptionId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .orderBy(desc(subscriptionPayments.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.subscriptionId, subscriptionId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      );

    return {
      payments: payments.map(this.mapToResponseDto),
      total,
      page,
      limit,
    };
  }

  /**
   * Get all payments for a business
   */
  async getBusinessPayments(
    businessId: string,
    userId: string,
    page = 1,
    limit = 10,
    status?: PaymentStatus,
    dateFrom?: string,
    dateTo?: string,
  ): Promise<PaymentListResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const offset = (page - 1) * limit;
    const conditions = [
      eq(subscriptionPayments.businessId, businessId),
      eq(subscriptionPayments.isDeleted, false),
    ];

    if (status) {
      conditions.push(eq(subscriptionPayments.status, status));
    }

    if (dateFrom) {
      conditions.push(gte(subscriptionPayments.invoiceDate, dateFrom));
    }

    if (dateTo) {
      conditions.push(lte(subscriptionPayments.invoiceDate, dateTo));
    }

    // Get payments with pagination and filters
    const payments = await this.db
      .select()
      .from(subscriptionPayments)
      .where(and(...conditions))
      .orderBy(desc(subscriptionPayments.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(subscriptionPayments)
      .where(and(...conditions));

    return {
      payments: payments.map(this.mapToResponseDto),
      total,
      page,
      limit,
    };
  }

  /**
   * Get payment by ID
   */
  async getPaymentById(
    paymentId: string,
    businessId: string,
    userId: string,
  ): Promise<PaymentResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const payment = await this.db
      .select()
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.id, paymentId),
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!payment) {
      throw new NotFoundException('Payment not found');
    }

    return this.mapToResponseDto(payment);
  }

  /**
   * Update payment details
   */
  async updatePayment(
    paymentId: string,
    businessId: string,
    userId: string,
    updatePaymentDto: UpdatePaymentDto,
  ): Promise<PaymentResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingPayment = await this.db
      .select()
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.id, paymentId),
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingPayment) {
      throw new NotFoundException('Payment not found');
    }

    const updateData: any = {
      ...updatePaymentDto,
      updatedBy: userId,
    };

    // Convert number fields to strings for decimal storage
    if (updatePaymentDto.taxAmount !== undefined) {
      updateData.taxAmount = updatePaymentDto.taxAmount.toString();
    }
    if (updatePaymentDto.processingFee !== undefined) {
      updateData.processingFee = updatePaymentDto.processingFee.toString();
    }
    if (updatePaymentDto.discountAmount !== undefined) {
      updateData.discountAmount = updatePaymentDto.discountAmount.toString();
    }

    const [updatedPayment] = await this.db
      .update(subscriptionPayments)
      .set(updateData)
      .where(eq(subscriptionPayments.id, paymentId))
      .returning();

    return this.mapToResponseDto(updatedPayment);
  }

  /**
   * Process a payment (mark as paid)
   */
  async processPayment(
    businessId: string,
    userId: string,
    processPaymentDto: ProcessPaymentDto,
  ): Promise<PaymentResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingPayment = await this.db
      .select()
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.id, processPaymentDto.paymentId),
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingPayment) {
      throw new NotFoundException('Payment not found');
    }

    if (existingPayment.status === PaymentStatus.PAID) {
      throw new BadRequestException('Payment is already processed');
    }

    const [updatedPayment] = await this.db
      .update(subscriptionPayments)
      .set({
        status: PaymentStatus.PAID,
        paymentMethod: processPaymentDto.paymentMethod,
        transactionId: processPaymentDto.transactionId,
        referenceNumber: processPaymentDto.referenceNumber,
        gatewayProvider: processPaymentDto.gatewayProvider,
        gatewayTransactionId: processPaymentDto.gatewayTransactionId,
        gatewayResponse: processPaymentDto.gatewayResponse,
        paidDate: new Date().toISOString().split('T')[0],
        notes: processPaymentDto.notes
          ? `${existingPayment.notes || ''}\nPayment processed: ${processPaymentDto.notes}`
          : existingPayment.notes,
        updatedBy: userId,
      })
      .where(eq(subscriptionPayments.id, processPaymentDto.paymentId))
      .returning();

    return this.mapToResponseDto(updatedPayment);
  }

  /**
   * Process a refund
   */
  async processRefund(
    businessId: string,
    userId: string,
    refundPaymentDto: RefundPaymentDto,
  ): Promise<PaymentResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingPayment = await this.db
      .select()
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.id, refundPaymentDto.paymentId),
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingPayment) {
      throw new NotFoundException('Payment not found');
    }

    if (existingPayment.status !== PaymentStatus.PAID) {
      throw new BadRequestException('Can only refund paid payments');
    }

    const currentRefundAmount = parseFloat(existingPayment.refundAmount || '0');
    const paymentAmount = parseFloat(existingPayment.amount);
    const newRefundAmount = currentRefundAmount + refundPaymentDto.refundAmount;

    if (newRefundAmount > paymentAmount) {
      throw new BadRequestException('Refund amount exceeds payment amount');
    }

    const newStatus =
      newRefundAmount === paymentAmount
        ? PaymentStatus.REFUNDED
        : PaymentStatus.PARTIALLY_REFUNDED;

    const [updatedPayment] = await this.db
      .update(subscriptionPayments)
      .set({
        status: newStatus,
        refundAmount: newRefundAmount.toString(),
        refundDate: new Date().toISOString().split('T')[0],
        refundReason: refundPaymentDto.refundReason,
        notes: refundPaymentDto.notes
          ? `${existingPayment.notes || ''}\nRefund processed: ${refundPaymentDto.notes}`
          : existingPayment.notes,
        updatedBy: userId,
      })
      .where(eq(subscriptionPayments.id, refundPaymentDto.paymentId))
      .returning();

    return this.mapToResponseDto(updatedPayment);
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(
    paymentId: string,
    businessId: string,
    userId: string,
    statusUpdate: PaymentStatusUpdateDto,
  ): Promise<PaymentResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingPayment = await this.db
      .select()
      .from(subscriptionPayments)
      .where(
        and(
          eq(subscriptionPayments.id, paymentId),
          eq(subscriptionPayments.businessId, businessId),
          eq(subscriptionPayments.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingPayment) {
      throw new NotFoundException('Payment not found');
    }

    const updateData: any = {
      status: statusUpdate.status,
      updatedBy: userId,
    };

    if (statusUpdate.paidDate) {
      updateData.paidDate = statusUpdate.paidDate;
    }

    if (statusUpdate.reason) {
      updateData.notes = statusUpdate.reason
        ? `${existingPayment.notes || ''}\nStatus change: ${statusUpdate.reason}`
        : existingPayment.notes;
    }

    const [updatedPayment] = await this.db
      .update(subscriptionPayments)
      .set(updateData)
      .where(eq(subscriptionPayments.id, paymentId))
      .returning();

    return this.mapToResponseDto(updatedPayment);
  }

  /**
   * Get payment summary for a business
   */
  async getPaymentSummary(
    businessId: string,
    userId: string,
    subscriptionId?: string,
  ): Promise<PaymentSummaryDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const conditions = [
      eq(subscriptionPayments.businessId, businessId),
      eq(subscriptionPayments.isDeleted, false),
    ];

    if (subscriptionId) {
      conditions.push(eq(subscriptionPayments.subscriptionId, subscriptionId));
    }

    const payments = await this.db
      .select({
        amount: subscriptionPayments.amount,
        status: subscriptionPayments.status,
        refundAmount: subscriptionPayments.refundAmount,
      })
      .from(subscriptionPayments)
      .where(and(...conditions));

    let totalAmount = 0;
    let paidAmount = 0;
    let pendingAmount = 0;
    let overdueAmount = 0;
    let refundedAmount = 0;
    let totalPayments = 0;
    let paidPayments = 0;
    let pendingPayments = 0;
    let overduePayments = 0;

    payments.forEach((payment) => {
      const amount = parseFloat(payment.amount);
      const refund = parseFloat(payment.refundAmount || '0');

      totalAmount += amount;
      totalPayments++;

      switch (payment.status) {
        case PaymentStatus.PAID:
          paidAmount += amount - refund;
          paidPayments++;
          refundedAmount += refund;
          break;
        case PaymentStatus.PENDING:
          pendingAmount += amount;
          pendingPayments++;
          break;
        case PaymentStatus.OVERDUE:
          overdueAmount += amount;
          overduePayments++;
          break;
        case PaymentStatus.REFUNDED:
        case PaymentStatus.PARTIALLY_REFUNDED:
          paidAmount += amount - refund;
          paidPayments++;
          refundedAmount += refund;
          break;
      }
    });

    return {
      totalAmount: totalAmount.toFixed(2),
      paidAmount: paidAmount.toFixed(2),
      pendingAmount: pendingAmount.toFixed(2),
      overdueAmount: overdueAmount.toFixed(2),
      refundedAmount: refundedAmount.toFixed(2),
      totalPayments,
      paidPayments,
      pendingPayments,
      overduePayments,
    };
  }

  /**
   * Verify user has access to the business
   */
  private async verifyBusinessAccess(
    businessId: string,
    userId: string,
  ): Promise<void> {
    const businessUser = await this.db
      .select({
        status: businessUsers.status,
      })
      .from(businessUsers)
      .where(
        and(
          eq(businessUsers.userId, userId),
          eq(businessUsers.businessId, businessId),
        ),
      )
      .then((result) => result[0]);

    if (!businessUser) {
      throw new ForbiddenException(
        'Access denied: User not associated with business',
      );
    }

    if (businessUser.status !== BusinessUserStatus.ACTIVE) {
      throw new ForbiddenException('Access denied: User account not active');
    }
  }

  /**
   * Verify subscription belongs to business
   */
  private async verifySubscriptionAccess(
    subscriptionId: string,
    businessId: string,
  ): Promise<void> {
    const subscription = await this.db
      .select({ id: businessSubscriptions.id })
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
  }

  /**
   * Map database entity to response DTO
   */
  private mapToResponseDto(payment: any): PaymentResponseDto {
    return {
      id: payment.id,
      businessId: payment.businessId,
      subscriptionId: payment.subscriptionId,
      invoiceNumber: payment.invoiceNumber,
      invoiceType: payment.invoiceType,
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      paymentMethod: payment.paymentMethod,
      transactionId: payment.transactionId,
      referenceNumber: payment.referenceNumber,
      invoiceDate: payment.invoiceDate,
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      billingPeriodStart: payment.billingPeriodStart,
      billingPeriodEnd: payment.billingPeriodEnd,
      gatewayProvider: payment.gatewayProvider,
      gatewayTransactionId: payment.gatewayTransactionId,
      taxAmount: payment.taxAmount,
      processingFee: payment.processingFee,
      discountAmount: payment.discountAmount,
      refundAmount: payment.refundAmount,
      refundDate: payment.refundDate,
      refundReason: payment.refundReason,
      isOverdue: payment.isOverdue,
      overdueDate: payment.overdueDate,
      lateFeeAmount: payment.lateFeeAmount,
      remindersSent: payment.remindersSent,
      lastReminderDate: payment.lastReminderDate,
      notes: payment.notes,
      internalNotes: payment.internalNotes,
      paymentInstructions: payment.paymentInstructions,
      retryAttempts: payment.retryAttempts,
      nextRetryDate: payment.nextRetryDate,
      createdAt: payment.createdAt.toISOString(),
      updatedAt: payment.updatedAt.toISOString(),
    };
  }
}
