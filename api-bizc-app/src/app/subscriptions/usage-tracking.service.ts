import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { eq, and, desc, count, isNull, gte, lte } from 'drizzle-orm';
import {
  subscriptionUsageTracking,
  subscriptionUsageHistory,
  UsageMetricType,
} from '../drizzle/schema/subscription-usage-tracking.schema';
import { businessSubscriptions } from '../drizzle/schema/business-subscriptions.schema';
import { businessUsers } from '../drizzle/schema/business-users.schema';
import { BusinessUserStatus } from '../shared/types';
import {
  CreateUsageTrackingDto,
  UpdateUsageTrackingDto,
  IncrementUsageDto,
  ResetUsageDto,
  UsageTrackingResponseDto,
  UsageTrackingListResponseDto,
  UsageHistoryResponseDto,
  UsageHistoryListResponseDto,
  UsageSummaryDto,
} from './dto/usage-tracking.dto';

@Injectable()
export class UsageTrackingService {
  constructor(@Inject(DRIZZLE) private readonly db: DrizzleDB) {}

  /**
   * Create a new usage tracking metric
   */
  async createUsageTracking(
    businessId: string,
    userId: string,
    createUsageTrackingDto: CreateUsageTrackingDto,
  ): Promise<UsageTrackingResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    // Verify subscription belongs to the business
    const subscription = await this.db
      .select({ id: businessSubscriptions.id })
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, createUsageTrackingDto.subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    // Check for duplicate metric for the same subscription
    const existingMetric = await this.db
      .select({ id: subscriptionUsageTracking.id })
      .from(subscriptionUsageTracking)
      .where(
        and(
          eq(
            subscriptionUsageTracking.subscriptionId,
            createUsageTrackingDto.subscriptionId,
          ),
          eq(
            subscriptionUsageTracking.metricType,
            createUsageTrackingDto.metricType,
          ),
          eq(
            subscriptionUsageTracking.metricName,
            createUsageTrackingDto.metricName,
          ),
          eq(subscriptionUsageTracking.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (existingMetric) {
      throw new BadRequestException(
        'Usage metric already exists for this subscription',
      );
    }

    // Create the usage tracking record
    const [newUsageTracking] = await this.db
      .insert(subscriptionUsageTracking)
      .values({
        businessId,
        subscriptionId: createUsageTrackingDto.subscriptionId,
        metricType: createUsageTrackingDto.metricType,
        metricName: createUsageTrackingDto.metricName,
        metricDescription: createUsageTrackingDto.metricDescription,
        usageLimit: createUsageTrackingDto.usageLimit,
        currentUsage: createUsageTrackingDto.currentUsage || 0,
        usageUnit: createUsageTrackingDto.usageUnit,
        periodType: createUsageTrackingDto.periodType,
        periodStart: createUsageTrackingDto.periodStart,
        periodEnd: createUsageTrackingDto.periodEnd,
        overageChargePerUnit:
          createUsageTrackingDto.overageChargePerUnit?.toString(),
        alertThresholds: createUsageTrackingDto.alertThresholds || [],
        nextResetDate: createUsageTrackingDto.nextResetDate,
        autoReset: createUsageTrackingDto.autoReset ?? true,
        measurementFrequency:
          createUsageTrackingDto.measurementFrequency || 'hourly',
        metadata: createUsageTrackingDto.metadata,
        tags: createUsageTrackingDto.tags || [],
        isActive: createUsageTrackingDto.isActive ?? true,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning();

    // Calculate usage percentage
    await this.updateUsagePercentage(newUsageTracking.id);

    // Create initial history record
    await this.createUsageHistoryRecord(
      newUsageTracking.id,
      businessId,
      userId,
      newUsageTracking.currentUsage,
      newUsageTracking.usageLimit,
      null,
      newUsageTracking.currentUsage,
      'initial_creation',
      'Initial usage tracking setup',
    );

    return this.getUsageTrackingById(newUsageTracking.id, businessId, userId);
  }

  /**
   * Get usage tracking metrics for a subscription
   */
  async getSubscriptionUsageTracking(
    businessId: string,
    subscriptionId: string,
    userId: string,
    page = 1,
    limit = 10,
    metricType?: UsageMetricType,
    isActive?: boolean,
  ): Promise<UsageTrackingListResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    // Verify subscription belongs to the business
    await this.verifySubscriptionAccess(subscriptionId, businessId);

    const offset = (page - 1) * limit;
    const conditions = [
      eq(subscriptionUsageTracking.businessId, businessId),
      eq(subscriptionUsageTracking.subscriptionId, subscriptionId),
      eq(subscriptionUsageTracking.isDeleted, false),
    ];

    if (metricType) {
      conditions.push(eq(subscriptionUsageTracking.metricType, metricType));
    }

    if (isActive !== undefined) {
      conditions.push(eq(subscriptionUsageTracking.isActive, isActive));
    }

    // Get usage metrics with pagination
    const usageMetrics = await this.db
      .select()
      .from(subscriptionUsageTracking)
      .where(and(...conditions))
      .orderBy(desc(subscriptionUsageTracking.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(subscriptionUsageTracking)
      .where(and(...conditions));

    return {
      usageMetrics: usageMetrics.map(this.mapToResponseDto),
      total,
      page,
      limit,
    };
  }

  /**
   * Get usage tracking by ID
   */
  async getUsageTrackingById(
    usageTrackingId: string,
    businessId: string,
    userId: string,
  ): Promise<UsageTrackingResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const usageTracking = await this.db
      .select()
      .from(subscriptionUsageTracking)
      .where(
        and(
          eq(subscriptionUsageTracking.id, usageTrackingId),
          eq(subscriptionUsageTracking.businessId, businessId),
          eq(subscriptionUsageTracking.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!usageTracking) {
      throw new NotFoundException('Usage tracking not found');
    }

    return this.mapToResponseDto(usageTracking);
  }

  /**
   * Update usage tracking
   */
  async updateUsageTracking(
    usageTrackingId: string,
    businessId: string,
    userId: string,
    updateUsageTrackingDto: UpdateUsageTrackingDto,
  ): Promise<UsageTrackingResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingUsageTracking = await this.db
      .select()
      .from(subscriptionUsageTracking)
      .where(
        and(
          eq(subscriptionUsageTracking.id, usageTrackingId),
          eq(subscriptionUsageTracking.businessId, businessId),
          eq(subscriptionUsageTracking.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingUsageTracking) {
      throw new NotFoundException('Usage tracking not found');
    }

    const updateData: any = {
      ...updateUsageTrackingDto,
      updatedBy: userId,
    };

    // Convert number fields to strings for decimal storage
    if (updateUsageTrackingDto.overageChargePerUnit !== undefined) {
      updateData.overageChargePerUnit =
        updateUsageTrackingDto.overageChargePerUnit.toString();
    }

    const [updatedUsageTracking] = await this.db
      .update(subscriptionUsageTracking)
      .set(updateData)
      .where(eq(subscriptionUsageTracking.id, usageTrackingId))
      .returning();

    // Recalculate usage percentage if usage or limit changed
    if (
      updateUsageTrackingDto.currentUsage !== undefined ||
      updateUsageTrackingDto.usageLimit !== undefined
    ) {
      await this.updateUsagePercentage(usageTrackingId);
    }

    return this.mapToResponseDto(updatedUsageTracking);
  }

  /**
   * Increment usage for a metric
   */
  async incrementUsage(
    businessId: string,
    userId: string,
    incrementUsageDto: IncrementUsageDto,
  ): Promise<UsageTrackingResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingUsageTracking = await this.db
      .select()
      .from(subscriptionUsageTracking)
      .where(
        and(
          eq(subscriptionUsageTracking.id, incrementUsageDto.usageTrackingId),
          eq(subscriptionUsageTracking.businessId, businessId),
          eq(subscriptionUsageTracking.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingUsageTracking) {
      throw new NotFoundException('Usage tracking not found');
    }

    const previousUsage = existingUsageTracking.currentUsage;
    const newUsage = previousUsage + incrementUsageDto.incrementBy;

    // Update current usage
    const [updatedUsageTracking] = await this.db
      .update(subscriptionUsageTracking)
      .set({
        currentUsage: newUsage,
        lastMeasuredAt: new Date(),
        updatedBy: userId,
      })
      .where(
        eq(subscriptionUsageTracking.id, incrementUsageDto.usageTrackingId),
      )
      .returning();

    // Update usage percentage and check limits
    await this.updateUsagePercentage(incrementUsageDto.usageTrackingId);

    // Create history record
    await this.createUsageHistoryRecord(
      incrementUsageDto.usageTrackingId,
      businessId,
      userId,
      newUsage,
      existingUsageTracking.usageLimit,
      previousUsage,
      incrementUsageDto.incrementBy,
      'increment',
      incrementUsageDto.notes,
    );

    // Check for alerts if usage limit is set
    if (existingUsageTracking.usageLimit) {
      await this.checkAndTriggerAlerts(updatedUsageTracking);
    }

    return this.mapToResponseDto(updatedUsageTracking);
  }

  /**
   * Reset usage for a metric
   */
  async resetUsage(
    businessId: string,
    userId: string,
    resetUsageDto: ResetUsageDto,
  ): Promise<UsageTrackingResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const existingUsageTracking = await this.db
      .select()
      .from(subscriptionUsageTracking)
      .where(
        and(
          eq(subscriptionUsageTracking.id, resetUsageDto.usageTrackingId),
          eq(subscriptionUsageTracking.businessId, businessId),
          eq(subscriptionUsageTracking.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingUsageTracking) {
      throw new NotFoundException('Usage tracking not found');
    }

    const previousUsage = existingUsageTracking.currentUsage;
    const resetToValue = resetUsageDto.resetToValue || 0;

    const updateData: any = {
      currentUsage: resetToValue,
      lastResetDate: new Date().toISOString().split('T')[0],
      lastMeasuredAt: new Date(),
      updatedBy: userId,
    };

    if (resetUsageDto.newPeriodStart) {
      updateData.periodStart = resetUsageDto.newPeriodStart;
    }

    if (resetUsageDto.newPeriodEnd) {
      updateData.periodEnd = resetUsageDto.newPeriodEnd;
    }

    if (resetUsageDto.nextResetDate) {
      updateData.nextResetDate = resetUsageDto.nextResetDate;
    }

    const [updatedUsageTracking] = await this.db
      .update(subscriptionUsageTracking)
      .set(updateData)
      .where(eq(subscriptionUsageTracking.id, resetUsageDto.usageTrackingId))
      .returning();

    // Update usage percentage
    await this.updateUsagePercentage(resetUsageDto.usageTrackingId);

    // Create history record
    await this.createUsageHistoryRecord(
      resetUsageDto.usageTrackingId,
      businessId,
      userId,
      resetToValue,
      existingUsageTracking.usageLimit,
      previousUsage,
      resetToValue - previousUsage,
      'reset',
      resetUsageDto.notes,
    );

    return this.mapToResponseDto(updatedUsageTracking);
  }

  /**
   * Get usage history for a metric
   */
  async getUsageHistory(
    usageTrackingId: string,
    businessId: string,
    userId: string,
    page = 1,
    limit = 10,
    dateFrom?: string,
    dateTo?: string,
  ): Promise<UsageHistoryListResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    // Verify usage tracking exists and belongs to the business
    await this.verifyUsageTrackingAccess(usageTrackingId, businessId);

    const offset = (page - 1) * limit;
    const conditions = [
      eq(subscriptionUsageHistory.usageTrackingId, usageTrackingId),
      eq(subscriptionUsageHistory.businessId, businessId),
    ];

    if (dateFrom) {
      conditions.push(
        gte(subscriptionUsageHistory.recordedAt, new Date(dateFrom)),
      );
    }

    if (dateTo) {
      conditions.push(
        lte(subscriptionUsageHistory.recordedAt, new Date(dateTo)),
      );
    }

    // Get history with pagination
    const history = await this.db
      .select()
      .from(subscriptionUsageHistory)
      .where(and(...conditions))
      .orderBy(desc(subscriptionUsageHistory.recordedAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(subscriptionUsageHistory)
      .where(and(...conditions));

    return {
      history: history.map(this.mapHistoryToResponseDto),
      total,
      page,
      limit,
    };
  }

  /**
   * Get usage summary for a business
   */
  async getUsageSummary(
    businessId: string,
    userId: string,
    subscriptionId?: string,
  ): Promise<UsageSummaryDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const conditions = [
      eq(subscriptionUsageTracking.businessId, businessId),
      eq(subscriptionUsageTracking.isDeleted, false),
    ];

    if (subscriptionId) {
      conditions.push(
        eq(subscriptionUsageTracking.subscriptionId, subscriptionId),
      );
    }

    const usageMetrics = await this.db
      .select({
        metricType: subscriptionUsageTracking.metricType,
        currentUsage: subscriptionUsageTracking.currentUsage,
        usageLimit: subscriptionUsageTracking.usageLimit,
        usagePercentage: subscriptionUsageTracking.usagePercentage,
        isOverLimit: subscriptionUsageTracking.isOverLimit,
        isNearLimit: subscriptionUsageTracking.isNearLimit,
        isActive: subscriptionUsageTracking.isActive,
        overageChargeTotal: subscriptionUsageTracking.overageChargeTotal,
      })
      .from(subscriptionUsageTracking)
      .where(and(...conditions));

    const totalMetrics = usageMetrics.length;
    const activeMetrics = usageMetrics.filter((m) => m.isActive).length;
    const overLimitMetrics = usageMetrics.filter((m) => m.isOverLimit).length;
    const nearLimitMetrics = usageMetrics.filter((m) => m.isNearLimit).length;

    const totalOverageCharges = usageMetrics
      .reduce(
        (sum, metric) => sum + parseFloat(metric.overageChargeTotal || '0'),
        0,
      )
      .toFixed(2);

    const averageUsagePercentage =
      totalMetrics > 0
        ? usageMetrics
            .filter((m) => m.usageLimit && m.usagePercentage)
            .reduce(
              (sum, metric) => sum + parseFloat(metric.usagePercentage || '0'),
              0,
            ) / totalMetrics
        : 0;

    // Group by metric type
    const metricsBreakdown = Object.values(UsageMetricType)
      .map((metricType) => {
        const metricsOfType = usageMetrics.filter(
          (m) => m.metricType === metricType,
        );
        const averageUsage =
          metricsOfType.length > 0
            ? metricsOfType.reduce(
                (sum, metric) => sum + metric.currentUsage,
                0,
              ) / metricsOfType.length
            : 0;

        return {
          metricType,
          count: metricsOfType.length,
          averageUsage,
        };
      })
      .filter((breakdown) => breakdown.count > 0);

    return {
      totalMetrics,
      activeMetrics,
      overLimitMetrics,
      nearLimitMetrics,
      totalOverageCharges,
      averageUsagePercentage,
      metricsBreakdown,
    };
  }

  /**
   * Update usage percentage and check limits
   */
  private async updateUsagePercentage(usageTrackingId: string): Promise<void> {
    const usageTracking = await this.db
      .select()
      .from(subscriptionUsageTracking)
      .where(eq(subscriptionUsageTracking.id, usageTrackingId))
      .then((result) => result[0]);

    if (!usageTracking || !usageTracking.usageLimit) {
      return;
    }

    const usagePercentage =
      (usageTracking.currentUsage / usageTracking.usageLimit) * 100;
    const isOverLimit = usageTracking.currentUsage > usageTracking.usageLimit;
    const isNearLimit = usagePercentage >= 80 && !isOverLimit;

    // Calculate overage
    const overageUsage = isOverLimit
      ? usageTracking.currentUsage - usageTracking.usageLimit
      : 0;
    const overageChargeTotal = usageTracking.overageChargePerUnit
      ? (overageUsage * parseFloat(usageTracking.overageChargePerUnit)).toFixed(
          2,
        )
      : '0.00';

    await this.db
      .update(subscriptionUsageTracking)
      .set({
        usagePercentage: usagePercentage.toFixed(2),
        isOverLimit,
        isNearLimit,
        overageUsage,
        overageChargeTotal,
      })
      .where(eq(subscriptionUsageTracking.id, usageTrackingId));
  }

  /**
   * Check and trigger alerts for usage thresholds
   */
  private async checkAndTriggerAlerts(usageTracking: any): Promise<void> {
    if (
      !usageTracking.alertThresholds ||
      usageTracking.alertThresholds.length === 0
    ) {
      return;
    }

    const currentPercentage = parseFloat(usageTracking.usagePercentage || '0');
    const alertsSent = usageTracking.alertsSent || [];
    const today = new Date().toISOString().split('T')[0];

    // Check if any threshold is exceeded and alert not sent today
    for (const threshold of usageTracking.alertThresholds) {
      if (currentPercentage >= threshold && !alertsSent.includes(today)) {
        // Add today to alerts sent
        const updatedAlertsSent = [...alertsSent, today];

        await this.db
          .update(subscriptionUsageTracking)
          .set({
            alertsSent: updatedAlertsSent,
            lastAlertDate: today,
          })
          .where(eq(subscriptionUsageTracking.id, usageTracking.id));

        // Here you would typically send the actual alert (email, notification, etc.)
        // For now, we just update the tracking record
        break;
      }
    }
  }

  /**
   * Create usage history record
   */
  private async createUsageHistoryRecord(
    usageTrackingId: string,
    businessId: string,
    userId: string,
    usageValue: number,
    usageLimit: number | null,
    previousUsage: number | null,
    usageChange: number,
    changeReason: string,
    notes?: string,
  ): Promise<void> {
    const usagePercentage = usageLimit
      ? ((usageValue / usageLimit) * 100).toFixed(2)
      : null;

    const usageTracking = await this.db
      .select({
        periodStart: subscriptionUsageTracking.periodStart,
        periodEnd: subscriptionUsageTracking.periodEnd,
      })
      .from(subscriptionUsageTracking)
      .where(eq(subscriptionUsageTracking.id, usageTrackingId))
      .then((result) => result[0]);

    await this.db.insert(subscriptionUsageHistory).values({
      usageTrackingId,
      businessId,
      usageValue,
      usageLimit,
      usagePercentage,
      previousUsage,
      usageChange,
      changeReason,
      periodStart: usageTracking.periodStart,
      periodEnd: usageTracking.periodEnd,
      notes,
      triggeredBy: userId,
      createdBy: userId,
    });
  }

  /**
   * Verify user has access to the business
   */
  private async verifyBusinessAccess(
    businessId: string,
    userId: string,
  ): Promise<void> {
    const businessUser = await this.db
      .select({
        status: businessUsers.status,
      })
      .from(businessUsers)
      .where(
        and(
          eq(businessUsers.userId, userId),
          eq(businessUsers.businessId, businessId),
        ),
      )
      .then((result) => result[0]);

    if (!businessUser) {
      throw new ForbiddenException(
        'Access denied: User not associated with business',
      );
    }

    if (businessUser.status !== BusinessUserStatus.ACTIVE) {
      throw new ForbiddenException('Access denied: User account not active');
    }
  }

  /**
   * Verify subscription belongs to business
   */
  private async verifySubscriptionAccess(
    subscriptionId: string,
    businessId: string,
  ): Promise<void> {
    const subscription = await this.db
      .select({ id: businessSubscriptions.id })
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
  }

  /**
   * Verify usage tracking belongs to business
   */
  private async verifyUsageTrackingAccess(
    usageTrackingId: string,
    businessId: string,
  ): Promise<void> {
    const usageTracking = await this.db
      .select({ id: subscriptionUsageTracking.id })
      .from(subscriptionUsageTracking)
      .where(
        and(
          eq(subscriptionUsageTracking.id, usageTrackingId),
          eq(subscriptionUsageTracking.businessId, businessId),
          eq(subscriptionUsageTracking.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!usageTracking) {
      throw new NotFoundException('Usage tracking not found');
    }
  }

  /**
   * Map database entity to response DTO
   */
  private mapToResponseDto(usageTracking: any): UsageTrackingResponseDto {
    return {
      id: usageTracking.id,
      businessId: usageTracking.businessId,
      subscriptionId: usageTracking.subscriptionId,
      metricType: usageTracking.metricType,
      metricName: usageTracking.metricName,
      metricDescription: usageTracking.metricDescription,
      usageLimit: usageTracking.usageLimit,
      currentUsage: usageTracking.currentUsage,
      usageUnit: usageTracking.usageUnit,
      periodType: usageTracking.periodType,
      periodStart: usageTracking.periodStart,
      periodEnd: usageTracking.periodEnd,
      usagePercentage: usageTracking.usagePercentage,
      isOverLimit: usageTracking.isOverLimit,
      isNearLimit: usageTracking.isNearLimit,
      overageUsage: usageTracking.overageUsage,
      overageChargePerUnit: usageTracking.overageChargePerUnit,
      overageChargeTotal: usageTracking.overageChargeTotal,
      alertThresholds: usageTracking.alertThresholds,
      alertsSent: usageTracking.alertsSent,
      lastAlertDate: usageTracking.lastAlertDate,
      lastResetDate: usageTracking.lastResetDate,
      nextResetDate: usageTracking.nextResetDate,
      autoReset: usageTracking.autoReset,
      lastMeasuredAt: usageTracking.lastMeasuredAt.toISOString(),
      measurementFrequency: usageTracking.measurementFrequency,
      metadata: usageTracking.metadata,
      tags: usageTracking.tags,
      isActive: usageTracking.isActive,
      createdAt: usageTracking.createdAt.toISOString(),
      updatedAt: usageTracking.updatedAt.toISOString(),
    };
  }

  /**
   * Map database entity to history response DTO
   */
  private mapHistoryToResponseDto(history: any): UsageHistoryResponseDto {
    return {
      id: history.id,
      usageTrackingId: history.usageTrackingId,
      businessId: history.businessId,
      recordedAt: history.recordedAt.toISOString(),
      usageValue: history.usageValue,
      usageLimit: history.usageLimit,
      usagePercentage: history.usagePercentage,
      previousUsage: history.previousUsage,
      usageChange: history.usageChange,
      changeReason: history.changeReason,
      periodStart: history.periodStart,
      periodEnd: history.periodEnd,
      notes: history.notes,
      triggeredBy: history.triggeredBy,
      createdAt: history.createdAt.toISOString(),
      createdBy: history.createdBy,
    };
  }
}
