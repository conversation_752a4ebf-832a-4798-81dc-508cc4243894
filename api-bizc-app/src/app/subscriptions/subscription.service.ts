import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { eq, and, desc, count, isNull } from 'drizzle-orm';
import {
  businessSubscriptions,
  SubscriptionStatus,
} from '../drizzle/schema/business-subscriptions.schema';
import { business } from '../drizzle/schema/business.schema';
import { businessUsers } from '../drizzle/schema/business-users.schema';
import { BusinessUserRole, BusinessUserStatus } from '../shared/types';
import {
  CreateSubscriptionDto,
  UpdateSubscriptionDto,
  SubscriptionResponseDto,
  SubscriptionListResponseDto,
  SubscriptionStatusUpdateDto,
} from './dto/subscription.dto';

@Injectable()
export class SubscriptionService {
  constructor(@Inject(DRIZZLE) private readonly db: DrizzleDB) {}

  /**
   * Create a new subscription for a business
   * Only ADMIN users can create subscriptions
   */
  async createSubscription(
    businessId: string,
    userId: string,
    createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    // Verify user has ADMIN role for the business
    await this.verifyAdminAccess(businessId, userId);

    // Check if business exists
    const businessExists = await this.db
      .select({ id: business.id })
      .from(business)
      .where(and(eq(business.id, businessId), isNull(business.deletedAt)))
      .then((result) => result[0]);

    if (!businessExists) {
      throw new NotFoundException('Business not found');
    }

    // Check for existing active subscription
    const existingSubscription = await this.db
      .select({ id: businessSubscriptions.id })
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.status, SubscriptionStatus.ACTIVE),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (existingSubscription) {
      throw new BadRequestException(
        'Business already has an active subscription',
      );
    }

    // Create the subscription
    const [newSubscription] = await this.db
      .insert(businessSubscriptions)
      .values({
        businessId,
        planName: createSubscriptionDto.planName,
        planDescription: createSubscriptionDto.planDescription,
        billingType: createSubscriptionDto.billingType,
        subscriptionPeriod: createSubscriptionDto.subscriptionPeriod,
        monthlyPrice: createSubscriptionDto.monthlyPrice.toString(),
        quarterlyPrice: createSubscriptionDto.quarterlyPrice?.toString(),
        yearlyPrice: createSubscriptionDto.yearlyPrice?.toString(),
        currency: createSubscriptionDto.currency || 'LKR',
        startDate: createSubscriptionDto.startDate,
        endDate: createSubscriptionDto.endDate,
        trialStartDate: createSubscriptionDto.trialStartDate,
        trialEndDate: createSubscriptionDto.trialEndDate,
        paymentDueDays: createSubscriptionDto.paymentDueDays || 30,
        maxUsers: createSubscriptionDto.maxUsers,
        maxStorage: createSubscriptionDto.maxStorage,
        maxProjects: createSubscriptionDto.maxProjects,
        autoRenewal: createSubscriptionDto.autoRenewal ?? true,
        gracePeriodDays: createSubscriptionDto.gracePeriodDays || 7,
        notes: createSubscriptionDto.notes,
        metadata: createSubscriptionDto.metadata,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning();

    return this.mapToResponseDto(newSubscription);
  }

  /**
   * Get all subscriptions for a business
   */
  async getBusinessSubscriptions(
    businessId: string,
    userId: string,
    page = 1,
    limit = 10,
  ): Promise<SubscriptionListResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const offset = (page - 1) * limit;

    // Get subscriptions with pagination
    const subscriptions = await this.db
      .select()
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .orderBy(desc(businessSubscriptions.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count
    const [{ total }] = await this.db
      .select({ total: count() })
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      );

    return {
      subscriptions: subscriptions.map(this.mapToResponseDto),
      total,
      page,
      limit,
    };
  }

  /**
   * Get active subscription for a business
   */
  async getActiveSubscription(
    businessId: string,
    userId: string,
  ): Promise<SubscriptionResponseDto | null> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const subscription = await this.db
      .select()
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.status, SubscriptionStatus.ACTIVE),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    return subscription ? this.mapToResponseDto(subscription) : null;
  }

  /**
   * Get subscription by ID
   */
  async getSubscriptionById(
    subscriptionId: string,
    businessId: string,
    userId: string,
  ): Promise<SubscriptionResponseDto> {
    // Verify user has access to the business
    await this.verifyBusinessAccess(businessId, userId);

    const subscription = await this.db
      .select()
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    return this.mapToResponseDto(subscription);
  }

  /**
   * Update subscription
   * Only ADMIN users can update subscriptions
   */
  async updateSubscription(
    subscriptionId: string,
    businessId: string,
    userId: string,
    updateSubscriptionDto: UpdateSubscriptionDto,
  ): Promise<SubscriptionResponseDto> {
    // Verify user has ADMIN role for the business
    await this.verifyAdminAccess(businessId, userId);

    // Check if subscription exists and belongs to the business
    const existingSubscription = await this.db
      .select()
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingSubscription) {
      throw new NotFoundException('Subscription not found');
    }

    const updateData: any = {
      ...updateSubscriptionDto,
      updatedBy: userId,
    };

    // Convert number fields to strings for decimal storage
    if (updateSubscriptionDto.monthlyPrice !== undefined) {
      updateData.monthlyPrice = updateSubscriptionDto.monthlyPrice.toString();
    }
    if (updateSubscriptionDto.quarterlyPrice !== undefined) {
      updateData.quarterlyPrice =
        updateSubscriptionDto.quarterlyPrice.toString();
    }
    if (updateSubscriptionDto.yearlyPrice !== undefined) {
      updateData.yearlyPrice = updateSubscriptionDto.yearlyPrice.toString();
    }

    const [updatedSubscription] = await this.db
      .update(businessSubscriptions)
      .set(updateData)
      .where(eq(businessSubscriptions.id, subscriptionId))
      .returning();

    return this.mapToResponseDto(updatedSubscription);
  }

  /**
   * Update subscription status
   * Only ADMIN users can update subscription status
   */
  async updateSubscriptionStatus(
    subscriptionId: string,
    businessId: string,
    userId: string,
    statusUpdate: SubscriptionStatusUpdateDto,
  ): Promise<SubscriptionResponseDto> {
    // Verify user has ADMIN role for the business
    await this.verifyAdminAccess(businessId, userId);

    // Check if subscription exists and belongs to the business
    const existingSubscription = await this.db
      .select()
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingSubscription) {
      throw new NotFoundException('Subscription not found');
    }

    const [updatedSubscription] = await this.db
      .update(businessSubscriptions)
      .set({
        status: statusUpdate.status,
        notes: statusUpdate.reason
          ? `${existingSubscription.notes || ''}\nStatus change: ${statusUpdate.reason}`
          : existingSubscription.notes,
        updatedBy: userId,
      })
      .where(eq(businessSubscriptions.id, subscriptionId))
      .returning();

    return this.mapToResponseDto(updatedSubscription);
  }

  /**
   * Cancel subscription (soft delete)
   * Only ADMIN users can cancel subscriptions
   */
  async cancelSubscription(
    subscriptionId: string,
    businessId: string,
    userId: string,
    reason?: string,
  ): Promise<void> {
    // Verify user has ADMIN role for the business
    await this.verifyAdminAccess(businessId, userId);

    // Check if subscription exists and belongs to the business
    const existingSubscription = await this.db
      .select()
      .from(businessSubscriptions)
      .where(
        and(
          eq(businessSubscriptions.id, subscriptionId),
          eq(businessSubscriptions.businessId, businessId),
          eq(businessSubscriptions.isDeleted, false),
        ),
      )
      .then((result) => result[0]);

    if (!existingSubscription) {
      throw new NotFoundException('Subscription not found');
    }

    await this.db
      .update(businessSubscriptions)
      .set({
        status: SubscriptionStatus.CANCELLED,
        notes: reason
          ? `${existingSubscription.notes || ''}\nCancellation reason: ${reason}`
          : existingSubscription.notes,
        updatedAt: new Date(),
        updatedBy: userId,
        isDeleted: true,
      })
      .where(eq(businessSubscriptions.id, subscriptionId));
  }

  /**
   * Verify user has ADMIN access to the business
   */
  private async verifyAdminAccess(
    businessId: string,
    userId: string,
  ): Promise<void> {
    const businessUser = await this.db
      .select({
        role: businessUsers.role,
        status: businessUsers.status,
      })
      .from(businessUsers)
      .where(
        and(
          eq(businessUsers.userId, userId),
          eq(businessUsers.businessId, businessId),
        ),
      )
      .then((result) => result[0]);

    if (!businessUser) {
      throw new ForbiddenException(
        'Access denied: User not associated with business',
      );
    }

    if (
      businessUser.role !== BusinessUserRole.ADMIN ||
      businessUser.status !== BusinessUserStatus.ACTIVE
    ) {
      throw new ForbiddenException('Access denied: Admin role required');
    }
  }

  /**
   * Verify user has access to the business (any role)
   */
  private async verifyBusinessAccess(
    businessId: string,
    userId: string,
  ): Promise<void> {
    const businessUser = await this.db
      .select({
        status: businessUsers.status,
      })
      .from(businessUsers)
      .where(
        and(
          eq(businessUsers.userId, userId),
          eq(businessUsers.businessId, businessId),
        ),
      )
      .then((result) => result[0]);

    if (!businessUser) {
      throw new ForbiddenException(
        'Access denied: User not associated with business',
      );
    }

    if (businessUser.status !== BusinessUserStatus.ACTIVE) {
      throw new ForbiddenException('Access denied: User account not active');
    }
  }

  /**
   * Map database entity to response DTO
   */
  private mapToResponseDto(subscription: any): SubscriptionResponseDto {
    return {
      id: subscription.id,
      businessId: subscription.businessId,
      planName: subscription.planName,
      planDescription: subscription.planDescription,
      status: subscription.status,
      billingType: subscription.billingType,
      subscriptionPeriod: subscription.subscriptionPeriod,
      monthlyPrice: subscription.monthlyPrice,
      quarterlyPrice: subscription.quarterlyPrice,
      yearlyPrice: subscription.yearlyPrice,
      currency: subscription.currency,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      trialStartDate: subscription.trialStartDate,
      trialEndDate: subscription.trialEndDate,
      nextBillingDate: subscription.nextBillingDate,
      lastBillingDate: subscription.lastBillingDate,
      paymentDueDays: subscription.paymentDueDays,
      maxUsers: subscription.maxUsers,
      maxStorage: subscription.maxStorage,
      maxProjects: subscription.maxProjects,
      autoRenewal: subscription.autoRenewal,
      gracePeriodDays: subscription.gracePeriodDays,
      notes: subscription.notes,
      metadata: subscription.metadata,
      createdAt: subscription.createdAt.toISOString(),
      updatedAt: subscription.updatedAt.toISOString(),
    };
  }
}
