import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateLeaveBalanceDto } from './dto/create-leave-balance.dto';
import { UpdateLeaveBalanceDto } from './dto/update-leave-balance.dto';
import { LeaveBalanceDto } from './dto/leave-balance.dto';
import { LeaveBalanceSlimDto } from './dto/leave-balance-slim.dto';
import { leaveBalances } from '../drizzle/schema/leave-balances.schema';
import { leaveTypes } from '../drizzle/schema/leave-types.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { eq, and, isNull, sql, asc, desc } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { UsersService } from '../users/users.service';

@Injectable()
export class LeaveBalancesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createLeaveBalanceDto: CreateLeaveBalanceDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate employee exists
      const employee = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createLeaveBalanceDto.employeeId),
            eq(staffMembers.businessId, businessId),
            eq(staffMembers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!employee) {
        throw new BadRequestException('Employee not found');
      }

      // Validate leave type exists
      const leaveType = await this.db
        .select()
        .from(leaveTypes)
        .where(
          and(
            eq(leaveTypes.id, createLeaveBalanceDto.leaveTypeId),
            eq(leaveTypes.businessId, businessId),
            eq(leaveTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!leaveType) {
        throw new BadRequestException('Leave type not found');
      }

      // Check if balance already exists for this employee, leave type, and year
      const existingBalance = await this.db
        .select()
        .from(leaveBalances)
        .where(
          and(
            eq(leaveBalances.businessId, businessId),
            eq(leaveBalances.employeeId, createLeaveBalanceDto.employeeId),
            eq(leaveBalances.leaveTypeId, createLeaveBalanceDto.leaveTypeId),
            eq(leaveBalances.year, createLeaveBalanceDto.year),
            eq(leaveBalances.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingBalance) {
        throw new ConflictException(
          `Leave balance already exists for employee ${employee.firstName} ${employee.lastName} for leave type ${leaveType.leaveName} in year ${createLeaveBalanceDto.year}`,
        );
      }

      // Create the leave balance
      const [newLeaveBalance] = await this.db
        .insert(leaveBalances)
        .values({
          businessId,
          employeeId: createLeaveBalanceDto.employeeId,
          leaveTypeId: createLeaveBalanceDto.leaveTypeId,
          year: createLeaveBalanceDto.year,
          entitledDays: createLeaveBalanceDto.entitledDays,
          usedDays: createLeaveBalanceDto.usedDays,
          remainingDays: createLeaveBalanceDto.remainingDays,
          carriedForward: createLeaveBalanceDto.carriedForward,
          createdBy: userId,
        })
        .returning();

      // Log the leave balance creation activity
      await this.activityLogService.logCreate(
        newLeaveBalance.id,
        EntityType.LEAVE_BALANCE,
        userId,
        businessId,
        {
          reason: `Leave balance for ${employee.firstName} ${employee.lastName} was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newLeaveBalance.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof ConflictException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create leave balance: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    year?: number,
    employeeId?: string,
    leaveTypeId?: string,
  ): Promise<{
    data: LeaveBalanceDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(leaveBalances.isDeleted, false),
      eq(leaveBalances.businessId, businessId),
    ];

    // Add year filtering if provided
    if (year !== undefined) {
      whereConditions.push(eq(leaveBalances.year, year));
    }

    // Add employee filtering if provided
    if (employeeId) {
      whereConditions.push(eq(leaveBalances.employeeId, employeeId));
    }

    // Add leave type filtering if provided
    if (leaveTypeId) {
      whereConditions.push(eq(leaveBalances.leaveTypeId, leaveTypeId));
    }

    // Find all leave balances with related data
    const result = await this.db
      .select({
        leaveBalance: leaveBalances,
        employee: {
          id: staffMembers.id,
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
        },
      })
      .from(leaveBalances)
      .innerJoin(staffMembers, eq(leaveBalances.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveBalances.leaveTypeId, leaveTypes.id))
      .where(and(...whereConditions))
      .orderBy(desc(leaveBalances.year), asc(staffMembers.firstName))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(leaveBalances)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Disable view logging for performance in optimized queries

    return {
      data: await Promise.all(
        result.map((item) => this.mapToLeaveBalanceDto(item)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(userId: string, id: string): Promise<LeaveBalanceDto> {
    // Get the leave balance with related data
    const result = await this.db
      .select({
        leaveBalance: leaveBalances,
        employee: {
          id: staffMembers.id,
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
        },
      })
      .from(leaveBalances)
      .innerJoin(staffMembers, eq(leaveBalances.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveBalances.leaveTypeId, leaveTypes.id))
      .where(and(eq(leaveBalances.id, id), eq(leaveBalances.isDeleted, false)))
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(`Leave balance with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== result.leaveBalance.businessId
    ) {
      throw new UnauthorizedException('Access denied to this leave balance');
    }

    // Disable view logging for performance

    return await this.mapToLeaveBalanceDto(result);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeaveBalanceDto: UpdateLeaveBalanceDto,
    metadata?: ActivityMetadata,
  ): Promise<
    { id: string } | { error: string; message: string; statusCode: number }
  > {
    // Get the leave balance
    const existingLeaveBalance = await this.db
      .select()
      .from(leaveBalances)
      .where(and(eq(leaveBalances.id, id), eq(leaveBalances.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveBalance) {
      throw new NotFoundException(`Leave balance with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingLeaveBalance.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this leave balance',
      );
    }

    try {
      // Update the leave balance
      const [updatedLeaveBalance] = await this.db
        .update(leaveBalances)
        .set({
          ...updateLeaveBalanceDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(leaveBalances.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.LEAVE_BALANCE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      // Get the updated data with related information
      return {
        id: updatedLeaveBalance.id,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update leave balance: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the leave balance
    const existingLeaveBalance = await this.db
      .select()
      .from(leaveBalances)
      .where(and(eq(leaveBalances.id, id), eq(leaveBalances.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingLeaveBalance) {
      throw new NotFoundException(`Leave balance with ID ${id} not found`);
    }

    if (businessId !== existingLeaveBalance.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this leave balance',
      );
    }

    // Soft delete the leave balance
    await this.db
      .update(leaveBalances)
      .set({
        isDeleted: true,
        updatedAt: new Date(),
        updatedBy: userId,
      })
      .where(eq(leaveBalances.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.LEAVE_BALANCE,
      userId,
      businessId,
      {
        reason: `Leave balance was deleted`,
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Leave balance with ID ${id} has been deleted`,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<LeaveBalanceSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all leave balances with only essential fields
    const result = await this.db
      .select({
        id: leaveBalances.id,
        year: leaveBalances.year,
        entitledDays: leaveBalances.entitledDays,
        usedDays: leaveBalances.usedDays,
        remainingDays: leaveBalances.remainingDays,
        employeeName: sql<string>`${staffMembers.firstName} || ' ' || ${staffMembers.lastName}`,
        leaveTypeName: leaveTypes.leaveName,
      })
      .from(leaveBalances)
      .innerJoin(staffMembers, eq(leaveBalances.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveBalances.leaveTypeId, leaveTypes.id))
      .where(
        and(
          eq(leaveBalances.isDeleted, false),
          eq(leaveBalances.businessId, businessId),
        ),
      )
      .orderBy(desc(leaveBalances.year), asc(staffMembers.firstName));

    // Disable view logging for performance

    return result.map((item) => ({
      id: item.id.toString(),
      employeeName: item.employeeName,
      leaveTypeName: item.leaveTypeName,
      year: item.year,
      entitledDays: item.entitledDays,
      usedDays: item.usedDays,
      remainingDays: item.remainingDays,
    }));
  }

  async findByEmployee(
    userId: string,
    businessId: string | null,
    employeeId: string,
    year?: number,
  ): Promise<LeaveBalanceDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(leaveBalances.isDeleted, false),
      eq(leaveBalances.businessId, businessId),
      eq(leaveBalances.employeeId, employeeId),
    ];

    if (year !== undefined) {
      whereConditions.push(eq(leaveBalances.year, year));
    }

    const result = await this.db
      .select({
        leaveBalance: leaveBalances,
        employee: {
          id: staffMembers.id,
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        },
        leaveType: {
          id: leaveTypes.id,
          leaveName: leaveTypes.leaveName,
        },
      })
      .from(leaveBalances)
      .innerJoin(staffMembers, eq(leaveBalances.employeeId, staffMembers.id))
      .innerJoin(leaveTypes, eq(leaveBalances.leaveTypeId, leaveTypes.id))
      .where(and(...whereConditions))
      .orderBy(desc(leaveBalances.year), asc(leaveTypes.leaveName));

    // Disable view logging for performance

    return await Promise.all(
      result.map((item) => this.mapToLeaveBalanceDto(item)),
    );
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createLeaveBalanceDto: CreateLeaveBalanceDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const leaveBalance = await this.create(
      userId,
      businessId,
      createLeaveBalanceDto,
      metadata,
    );
    return { id: leaveBalance.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateLeaveBalanceDto: UpdateLeaveBalanceDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateLeaveBalanceDto, metadata);
    return { id };
  }

  private async mapToLeaveBalanceDto(data: any): Promise<LeaveBalanceDto> {
    const { leaveBalance, employee, leaveType } = data;

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      leaveBalance.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (leaveBalance.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        leaveBalance.updatedBy.toString(),
      );
    }

    const leaveBalanceDto: LeaveBalanceDto = {
      id: leaveBalance.id.toString(),
      businessId: leaveBalance.businessId.toString(),
      employeeId: leaveBalance.employeeId.toString(),
      employeeName: `${employee.firstName} ${employee.lastName}`,
      leaveTypeId: leaveBalance.leaveTypeId.toString(),
      leaveTypeName: leaveType.leaveName,
      year: leaveBalance.year,
      entitledDays: leaveBalance.entitledDays,
      usedDays: leaveBalance.usedDays,
      remainingDays: leaveBalance.remainingDays,
      carriedForward: leaveBalance.carriedForward,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: leaveBalance.createdAt,
      updatedAt: leaveBalance.updatedAt,
    };

    return leaveBalanceDto;
  }
}
