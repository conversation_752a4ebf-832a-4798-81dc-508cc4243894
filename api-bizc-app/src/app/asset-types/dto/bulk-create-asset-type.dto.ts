import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateAssetTypeDto } from './create-asset-type.dto';

export class BulkCreateAssetTypeDto {
  @ApiProperty({
    type: [CreateAssetTypeDto],
    description: 'Array of asset types to create',
  })
  @IsArray({ message: 'Asset types must be an array' })
  @ValidateNested({ each: true })
  @Type(() => CreateAssetTypeDto)
  assetTypes: CreateAssetTypeDto[];
}
