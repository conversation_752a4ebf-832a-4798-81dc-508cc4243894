import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
  IsInt,
  Min,
} from 'class-validator';
import { CategoryStatus, AssetTypeCategory } from '../../shared/types';

export class UpdateAssetTypeDto {
  @ApiProperty({
    example: 'Computers',
    description: 'Asset type name',
    maxLength: 100,
    required: false,
  })
  @IsString({ message: 'Asset type name must be a string' })
  @IsOptional()
  @MaxLength(100, {
    message: 'Asset type name must be 100 characters or less',
  })
  name?: string;

  @ApiProperty({
    example: 'Electronic computing devices and accessories',
    description: 'Asset type description',
    required: false,
    maxLength: 500,
  })
  @IsString({ message: 'Description must be a string' })
  @IsOptional()
  @MaxLength(500, { message: 'Description must be 500 characters or less' })
  description?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Parent asset type ID',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Parent ID must be a valid UUID' })
  parentId?: string;

  @ApiPropertyOptional({
    example: 1,
    description: 'Position for ordering',
    minimum: 0,
  })
  @IsOptional()
  @IsInt({ message: 'Position must be an integer' })
  @Min(0, { message: 'Position must be 0 or greater' })
  position?: number;

  @ApiPropertyOptional({
    enum: AssetTypeCategory,
    enumName: 'AssetTypeCategory',
    description: 'Asset type category',
    example: AssetTypeCategory.PHYSICAL,
  })
  @IsEnum(AssetTypeCategory, {
    message: 'Category must be a valid asset type category',
  })
  @IsOptional()
  category?: AssetTypeCategory;

  @ApiProperty({
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    description: 'Asset type status',
    required: false,
  })
  @IsEnum(CategoryStatus, {
    message: 'Status must be a valid category status',
  })
  @IsOptional()
  status?: CategoryStatus;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description:
      'Reference ID from source type (rental-item-category or vehicle-category)',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Reference ID must be a valid UUID' })
  referenceId?: string;

  @ApiPropertyOptional({
    example: 'rental-item-category',
    enum: ['rental-item-category', 'vehicle-category'],
    description: 'Type of the source module',
  })
  @IsOptional()
  @IsString({ message: 'Reference type must be a string' })
  referenceType?: 'rental-item-category' | 'vehicle-category';
}
