import { ApiProperty } from '@nestjs/swagger';
import { AssetTypeListDto } from './asset-type-list.dto';

export class PaginatedAssetTypesResponseDto {
  @ApiProperty({
    type: [AssetTypeListDto],
    description: 'List of asset types for the current page',
  })
  data: AssetTypeListDto[];

  @ApiProperty({
    example: {
      total: 100,
      page: 1,
      totalPages: 10,
    },
    description: 'Pagination metadata',
  })
  meta: {
    total: number;
    page: number;
    totalPages: number;
  };
}
