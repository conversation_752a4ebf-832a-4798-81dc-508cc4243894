import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CategoryStatus } from '../../shared/types';

export class AssetTypeListDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Asset Category ID',
  })
  id: string;

  @ApiProperty({
    example: 'Computers',
    description: 'Asset category name',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Electronic computing devices and accessories',
    description: 'Asset category description',
  })
  description?: string;

  @ApiPropertyOptional({
    example: 'Electronics',
    description: 'Parent asset category name',
  })
  parentName?: string;

  @ApiProperty({
    example: CategoryStatus.ACTIVE,
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    description: 'Asset category status',
  })
  status: CategoryStatus;
}
