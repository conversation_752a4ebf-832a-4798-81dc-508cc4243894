import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsUUID,
  ArrayMinSize,
} from 'class-validator';
import { CategoryStatus } from '../../shared/types';

export class BulkUpdateAssetTypeStatusItemDto {
  @ApiProperty({
    description: 'Asset type ID to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID('4', { message: 'Asset type ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Asset type ID is required' })
  assetTypeId: string;
}

export class BulkUpdateAssetTypeStatusDto {
  @ApiProperty({
    description: 'Array of asset type IDs to update',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1, {
    message: 'At least one asset type ID must be provided',
  })
  @IsUUID('4', { each: true, message: 'Each ID must be a valid UUID' })
  assetTypeIds: string[];

  @ApiProperty({
    description: 'Status to apply to all asset types',
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    example: CategoryStatus.ACTIVE,
  })
  @IsEnum(CategoryStatus, { message: 'Status must be a valid CategoryStatus' })
  @IsNotEmpty({ message: 'Status is required' })
  status: CategoryStatus;
}

export class BulkUpdateAssetTypeStatusFailureDto {
  @ApiProperty({
    description: 'Asset type ID that failed to update',
    example: '550e8400-e29b-41d4-a716-************',
  })
  assetTypeId: string;

  @ApiProperty({
    description: 'Error message explaining why the update failed',
    example: 'Asset type not found',
  })
  error: string;
}

export class BulkUpdateAssetTypeStatusResponseDto {
  @ApiProperty({
    description: 'Number of asset types successfully updated',
    example: 2,
  })
  updated: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully updated status for 2 asset types',
  })
  message: string;

  @ApiProperty({
    description: 'Array of asset type IDs that were successfully updated',
    type: [String],
    example: [
      '550e8400-e29b-41d4-a716-************',
      '550e8400-e29b-41d4-a716-************',
    ],
  })
  updatedIds: string[];

  @ApiProperty({
    description: 'Array of asset types that failed to update',
    type: [BulkUpdateAssetTypeStatusFailureDto],
    required: false,
  })
  failed?: BulkUpdateAssetTypeStatusFailureDto[];
}
