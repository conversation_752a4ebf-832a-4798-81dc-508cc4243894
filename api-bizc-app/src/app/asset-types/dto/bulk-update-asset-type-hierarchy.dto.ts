import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class BulkUpdateAssetTypeHierarchyItemDto {
  @ApiProperty({
    description: 'Asset category ID to update',
    example: 'asset-category-id-1',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'New parent asset category ID (null for root level)',
    example: 'parent-asset-category-id',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  parentId: string | null;
}

export class BulkUpdateAssetTypeHierarchyDto {
  @ApiProperty({
    description: 'Array of asset category hierarchy updates',
    type: [BulkUpdateAssetTypeHierarchyItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BulkUpdateAssetTypeHierarchyItemDto)
  updates: BulkUpdateAssetTypeHierarchyItemDto[];
}

export class BulkUpdateAssetTypeHierarchyFailureDto {
  @ApiProperty({
    description: 'Asset category ID that failed to update',
    example: 'asset-category-id-1',
  })
  id: string;

  @ApiProperty({
    description: 'Error message explaining why the update failed',
    example: 'Parent asset category not found',
  })
  error: string;
}

export class BulkUpdateAssetTypeHierarchyResponseDto {
  @ApiProperty({
    description: 'Number of asset categories successfully updated',
    example: 5,
  })
  updated: number;

  @ApiProperty({
    description: 'Array of asset categories that failed to update',
    type: [BulkUpdateAssetTypeHierarchyFailureDto],
  })
  failed: BulkUpdateAssetTypeHierarchyFailureDto[];
}
