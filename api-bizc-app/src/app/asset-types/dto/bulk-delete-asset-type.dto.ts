import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayMinSize } from 'class-validator';

export class BulkDeleteAssetTypeDto {
  @ApiProperty({
    description: 'Array of asset type IDs to delete',
    example: [
      'b47b8c3a-5d67-4e8f-9a1b-2c3d4e5f6g7h',
      'c58c9d4b-6e78-5f9g-ab2c-3d4e5f6g7h8i',
    ],
    type: [String],
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1, {
    message: 'At least one asset type ID must be provided',
  })
  @IsUUID('4', { each: true, message: 'Each ID must be a valid UUID' })
  assetTypeIds: string[];
}

export class BulkDeleteAssetTypeFailureDto {
  @ApiProperty({
    description: 'Asset type ID that failed to delete',
    example: 'b47b8c3a-5d67-4e8f-9a1b-2c3d4e5f6g7h',
  })
  assetTypeId: string;

  @ApiProperty({
    description: 'Error message explaining why the deletion failed',
    example: 'Asset type not found',
  })
  error: string;
}

export class BulkDeleteAssetTypeResponseDto {
  @ApiProperty({
    description: 'Number of asset types successfully deleted',
    example: 2,
  })
  deleted: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully deleted 2 asset types',
  })
  message: string;

  @ApiProperty({
    description: 'Array of asset types that failed to delete',
    type: [BulkDeleteAssetTypeFailureDto],
    required: false,
  })
  failed?: BulkDeleteAssetTypeFailureDto[];
}
