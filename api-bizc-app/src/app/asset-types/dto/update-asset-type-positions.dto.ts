import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>A<PERSON><PERSON>,
  IsString,
  IsN<PERSON>ber,
  IsUUID,
  Min,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';

export class AssetTypePositionUpdateDto {
  @ApiProperty({
    description: 'Asset category ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'New position for the asset category',
    example: 1,
    minimum: 1,
  })
  @IsNumber({}, { message: 'Position must be a number' })
  @Min(1, { message: 'Position must be 1 or greater' })
  position: number;
}

export class UpdateAssetTypePositionsDto {
  @ApiProperty({
    description: 'Array of asset category position updates',
    type: [AssetTypePositionUpdateDto],
    example: [
      { id: '123e4567-e89b-12d3-a456-************', position: 1 },
      { id: '123e4567-e89b-12d3-a456-************', position: 2 },
    ],
  })
  @IsArray()
  @ArrayMinSize(1, {
    message: 'At least one asset category position update is required',
  })
  @ValidateNested({ each: true })
  @Type(() => AssetTypePositionUpdateDto)
  updates: AssetTypePositionUpdateDto[];
}

export class UpdateAssetTypePositionsResponseDto {
  @ApiProperty({
    description: 'Number of asset categories updated',
    example: 5,
  })
  updated: number;

  @ApiProperty({
    description: 'Success message',
    example: 'Successfully updated 5 asset category positions',
  })
  message: string;
}
