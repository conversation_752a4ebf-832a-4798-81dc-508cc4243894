import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsUUID,
} from 'class-validator';
import { CategoryStatus, AssetTypeCategory } from '../../shared/types';

export class CreateAssetTypeDto {
  @ApiProperty({
    example: 'Computers',
    description: 'Asset type name',
    maxLength: 100,
  })
  @IsString({ message: 'Asset type name must be a string' })
  @IsNotEmpty({ message: 'Asset type name is required' })
  @MaxLength(100, {
    message: 'Asset type name must be 100 characters or less',
  })
  name: string;

  @ApiProperty({
    example: 'Electronic computing devices and accessories',
    description: 'Asset type description',
    required: false,
    maxLength: 500,
  })
  @IsString({ message: 'Description must be a string' })
  @IsOptional()
  @MaxLength(500, { message: 'Description must be 500 characters or less' })
  description?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Parent asset type ID',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Parent ID must be a valid UUID' })
  parentId?: string;

  @ApiProperty({
    enum: AssetTypeCategory,
    enumName: 'AssetTypeCategory',
    description: 'Asset type category',
    example: AssetTypeCategory.PHYSICAL,
  })
  @IsEnum(AssetTypeCategory, {
    message: 'Category must be a valid asset type category',
  })
  @IsNotEmpty({ message: 'Category is required' })
  category: AssetTypeCategory;

  @ApiProperty({
    enum: CategoryStatus,
    enumName: 'CategoryStatus',
    description: 'Asset type status',
    default: CategoryStatus.ACTIVE,
  })
  @IsEnum(CategoryStatus, {
    message: 'Status must be a valid category status',
  })
  @IsOptional()
  status?: CategoryStatus;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description:
      'Reference ID from source type (rental-item-category or vehicle-category)',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Reference ID must be a valid UUID' })
  referenceId?: string;

  @ApiPropertyOptional({
    example: 'rental-item-category',
    enum: ['rental-item-category', 'vehicle-category'],
    description: 'Type of the source module',
  })
  @IsOptional()
  @IsString({ message: 'Reference type must be a string' })
  referenceType?: 'rental-item-category' | 'vehicle-category';
}
