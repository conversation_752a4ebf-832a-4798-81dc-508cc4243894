import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AssetTypesService } from './asset-types.service';
import { CreateAssetTypeDto } from './dto/create-asset-type.dto';
import { BulkCreateAssetTypeDto } from './dto/bulk-create-asset-type.dto';
import { UpdateAssetTypeDto } from './dto/update-asset-type.dto';
import { AssetTypeDto } from './dto/asset-type.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { AssetTypeNameAvailabilityResponseDto } from './dto/check-asset-type-name.dto';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';
import { AssetTypeSlimDto } from './dto/asset-type-slim.dto';
import { PaginatedAssetTypesResponseDto } from './dto/paginated-asset-types-response.dto';
import { DeleteAssetTypeResponseDto } from './dto/delete-asset-type-response.dto';
import { AssetTypeIdResponseDto } from './dto/asset-type-id-response.dto';
import { BulkAssetTypeIdsResponseDto } from './dto/bulk-asset-type-ids-response.dto';
import {
  UpdateAssetTypePositionsDto,
  UpdateAssetTypePositionsResponseDto,
} from './dto/update-asset-type-positions.dto';
import {
  BulkUpdateAssetTypeHierarchyDto,
  BulkUpdateAssetTypeHierarchyResponseDto,
} from './dto/bulk-update-asset-type-hierarchy.dto';
import {
  BulkDeleteAssetTypeDto,
  BulkDeleteAssetTypeResponseDto,
} from './dto/bulk-delete-asset-type.dto';
import {
  BulkUpdateAssetTypeStatusDto,
  BulkUpdateAssetTypeStatusResponseDto,
} from './dto/bulk-update-asset-type-status.dto';

@ApiTags('asset-types')
@Controller('asset-types')
@UseGuards(PermissionsGuard)
export class AssetTypesController {
  constructor(private readonly assetTypesService: AssetTypesService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_CREATE)
  @ApiOperation({ summary: 'Create a new asset type' })
  @ApiResponse({
    status: 201,
    description: 'The asset type has been successfully created',
    type: AssetTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset type name already exists',
  })
  create(
    @Request() req: any,
    @Body() createAssetTypeDto: CreateAssetTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetTypeIdResponseDto> {
    return this.assetTypesService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAssetTypeDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_CREATE)
  @ApiOperation({
    summary: 'Bulk create asset categories',
  })
  @ApiResponse({
    status: 201,
    description: 'The asset categories have been successfully created',
    type: BulkAssetTypeIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data, duplicate names, or files',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset type names already exist',
  })
  bulkCreate(
    @Request() req: any,
    @Body() bulkCreateAssetTypeDto: BulkCreateAssetTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAssetTypeIdsResponseDto> {
    return this.assetTypesService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAssetTypeDto.assetTypes,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_DELETE)
  @ApiOperation({ summary: 'Bulk delete asset categories' })
  @ApiBody({
    description: 'Array of asset type IDs to delete',
    type: BulkDeleteAssetTypeDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset categories have been successfully deleted',
    type: BulkDeleteAssetTypeResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more asset categories not found',
  })
  async bulkDelete(
    @Request() req: any,
    @Body() bulkDeleteAssetTypeDto: BulkDeleteAssetTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAssetTypeResponseDto> {
    return this.assetTypesService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAssetTypeDto.assetTypeIds,
      metadata,
    );
  }

  @Patch('bulk-status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_UPDATE)
  @ApiOperation({ summary: 'Bulk update asset type status' })
  @ApiBody({
    description: 'Array of asset type IDs and status to update',
    type: BulkUpdateAssetTypeStatusDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset type status has been successfully updated',
    type: BulkUpdateAssetTypeStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkUpdateStatus(
    @Request() req: any,
    @Body() bulkUpdateStatusDto: BulkUpdateAssetTypeStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetTypeStatusResponseDto> {
    const result = await this.assetTypesService.bulkUpdateAssetTypeStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.assetTypeIds,
      bulkUpdateStatusDto.status,
      metadata,
    );

    return {
      updated: result.updated,
      message: `Successfully updated status for ${result.updated} asset categories`,
      updatedIds: result.updatedIds,
      failed: result.failed.length > 0 ? result.failed : undefined,
    };
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_READ)
  @ApiOperation({
    summary: 'Get all asset categories for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-05-24',
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by asset type name',
    required: false,
    type: String,
    example: 'Computers',
  })
  @ApiQuery({
    name: 'status',
    description:
      'Filter by status (comma-separated for multiple values). Supports URL encoding: status=active%2Cinactive',
    required: false,
    type: String,
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"name","value":"Computer","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"active","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: name, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"updatedAt","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active asset categories for the user's active business with pagination (optimized with only essential fields)",
    type: PaginatedAssetTypesResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('name') name?: string,
    @Query('status') status?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAssetTypesResponseDto> {
    return this.assetTypesService.findAllOptimized(
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      name,
      status,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get('check-name-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_READ)
  @ApiOperation({ summary: 'Check if an asset type name is available' })
  @ApiQuery({
    name: 'name',
    description: 'Asset type name to check',
    required: true,
    type: String,
    example: 'Computers',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns availability status of the asset type name',
    type: AssetTypeNameAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req: any,
    @Query('name') name: string,
  ): Promise<AssetTypeNameAvailabilityResponseDto> {
    return this.assetTypesService.checkNameAvailability(
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_READ)
  @ApiOperation({
    summary: 'Get all active asset categories in slim format for dropdowns',
  })
  @ApiResponse({
    status: 200,
    description:
      "Returns all active asset categories for the user's active business in slim format",
    type: [AssetTypeSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: any): Promise<AssetTypeSlimDto[]> {
    return this.assetTypesService.findAllSlim(req.user.activeBusinessId);
  }

  @Get('hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_READ)
  @ApiOperation({ summary: 'Get all asset categories in hierarchy format' })
  @ApiResponse({
    status: 200,
    description: 'Asset categories hierarchy returned successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          name: { type: 'string' },
          parentId: { type: 'string', format: 'uuid', nullable: true },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllHierarchy(
    @Request() req: any,
  ): Promise<{ id: string; name: string; parentId: string | null }[]> {
    return this.assetTypesService.findAllHierarchy(req.user.activeBusinessId);
  }

  @Patch('batch/positions')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_UPDATE)
  @ApiOperation({ summary: 'Batch update asset type positions' })
  @ApiBody({
    description: 'Array of asset type position updates',
    type: UpdateAssetTypePositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset type positions have been successfully updated',
    type: UpdateAssetTypePositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updatePositions(
    @Request() req: any,
    @Body() updateAssetTypePositionsDto: UpdateAssetTypePositionsDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<UpdateAssetTypePositionsResponseDto> {
    const result = await this.assetTypesService.updateAssetTypePositions(
      req.user.id,
      req.user.activeBusinessId,
      updateAssetTypePositionsDto.updates,
      metadata,
    );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} asset type positions`,
    };
  }

  @Patch('batch/hierarchy')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_UPDATE)
  @ApiOperation({ summary: 'Batch update asset type hierarchy' })
  @ApiBody({
    description: 'Array of asset type hierarchy updates',
    type: BulkUpdateAssetTypeHierarchyDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Asset type hierarchy has been successfully updated',
    type: BulkUpdateAssetTypeHierarchyResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or asset categories not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateHierarchy(
    @Request() req: any,
    @Body() bulkUpdateHierarchyDto: BulkUpdateAssetTypeHierarchyDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAssetTypeHierarchyResponseDto> {
    return this.assetTypesService.bulkUpdateAssetTypeHierarchy(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateHierarchyDto.updates,
      metadata,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_READ)
  @ApiOperation({ summary: 'Get a specific asset type by ID' })
  @ApiParam({
    name: 'id',
    description: 'Asset type ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the asset type with the specified ID',
    type: AssetTypeDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset type not found',
  })
  findOne(@Request() req: any, @Param('id') id: string): Promise<AssetTypeDto> {
    return this.assetTypesService.findOne(req.user.id, id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_UPDATE)
  @ApiOperation({ summary: 'Update an asset type' })
  @ApiParam({
    name: 'id',
    description: 'Asset type ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The asset type has been successfully updated',
    type: AssetTypeIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset type not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Asset type name already exists',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body() updateAssetTypeDto: UpdateAssetTypeDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AssetTypeIdResponseDto> {
    return this.assetTypesService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAssetTypeDto,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ASSET_TYPE_DELETE)
  @ApiOperation({ summary: 'Delete an asset type (soft delete)' })
  @ApiParam({
    name: 'id',
    description: 'Asset type ID',
    type: String,
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @ApiResponse({
    status: 200,
    description: 'The asset type has been successfully deleted',
    type: DeleteAssetTypeResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Asset type not found',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAssetTypeResponseDto> {
    return this.assetTypesService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }
}
