import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetTypeDto } from './dto/create-asset-type.dto';
import { UpdateAssetTypeDto } from './dto/update-asset-type.dto';
import { AssetTypeDto } from './dto/asset-type.dto';
import { AssetTypeSlimDto } from './dto/asset-type-slim.dto';
import { AssetTypeListDto } from './dto/asset-type-list.dto';
import { assetTypes } from '../drizzle/schema/asset-types.schema';
import { assets } from '../drizzle/schema/assets.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { CategoryStatus } from '../shared/types';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { UsersService } from '../users/users.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetTypesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetTypeDto: CreateAssetTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if an asset type with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingAssetType = await this.db
        .select()
        .from(assetTypes)
        .where(
          and(
            eq(assetTypes.businessId, businessId),
            ilike(assetTypes.name, createAssetTypeDto.name),
            eq(assetTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingAssetType) {
        throw new ConflictException(
          `An asset type with the name '${createAssetTypeDto.name}' already exists for this business`,
        );
      }

      // Handle parent category if provided
      if (createAssetTypeDto.parentId) {
        const parentCategory = await this.db
          .select()
          .from(assetTypes)
          .where(
            and(
              eq(assetTypes.id, createAssetTypeDto.parentId),
              eq(assetTypes.businessId, businessId),
              eq(assetTypes.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!parentCategory) {
          throw new BadRequestException('Parent asset type not found');
        }
      }

      // Create the asset type
      const [newAssetType] = await this.db
        .insert(assetTypes)
        .values({
          businessId,
          name: createAssetTypeDto.name,
          description: createAssetTypeDto.description,
          parentId: createAssetTypeDto.parentId,
          category: createAssetTypeDto.category,
          createdBy: userId,
          status: createAssetTypeDto.status ?? CategoryStatus.ACTIVE,
        })
        .returning();

      // Log the asset type creation activity
      await this.activityLogService.logCreate(
        newAssetType.id,
        EntityType.ASSET_TYPE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newAssetType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create asset type: ${error.message}`,
      );
    }
  }

  async findAll(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: AssetTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(assetTypes.isDeleted, false),
      eq(assetTypes.status, CategoryStatus.ACTIVE),
      eq(assetTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        // Add 23:59:59 to include the entire day
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetTypes.createdAt, toDate));
      }
    }

    // Find all asset categories for the user's active business with pagination
    const result = await this.db
      .select()
      .from(assetTypes)
      .where(and(...whereConditions))
      .orderBy(desc(assetTypes.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    return {
      data: await Promise.all(
        result.map((assetCategory) => this.mapToAssetTypeDto(assetCategory)),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: AssetTypeListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetTypes.isDeleted, false),
      eq(assetTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(assetTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(assetTypes.createdAt, toDate));
      }
    }

    // Add name filtering if provided
    if (name) {
      whereConditions.push(ilike(assetTypes.name, `%${name}%`));
    }

    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as CategoryStatus);
      whereConditions.push(inArray(assetTypes.status, statusArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(ilike(assetTypes.name, `%${value}%`));
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(assetTypes.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(assetTypes.name, value));
                break;
              case 'ne':
                filterConditions.push(sql`${assetTypes.name} != ${value}`);
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${assetTypes.name} IS NULL OR ${assetTypes.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${assetTypes.name} IS NOT NULL AND ${assetTypes.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(assetTypes.status, value as CategoryStatus[]),
                  );
                } else {
                  filterConditions.push(eq(assetTypes.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${assetTypes.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(sql`${assetTypes.status} != ${value}`);
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as CategoryStatus);
                  filterConditions.push(
                    inArray(assetTypes.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(assetTypes.status, value));
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions with default sort
    let orderBy = [asc(assetTypes.name), asc(assetTypes.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              orderBy = [
                isDesc ? desc(assetTypes.name) : asc(assetTypes.name),
                asc(assetTypes.id), // Secondary sort for consistency
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc ? desc(assetTypes.createdAt) : asc(assetTypes.createdAt),
                asc(assetTypes.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc ? desc(assetTypes.updatedAt) : asc(assetTypes.updatedAt),
                asc(assetTypes.id), // Secondary sort for consistency
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Execute query with optimized fields
    const result = await this.db
      .select({
        id: assetTypes.id,
        name: assetTypes.name,
        description: assetTypes.description,
        status: assetTypes.status,
        parentId: assetTypes.parentId,
        createdAt: assetTypes.createdAt,
        updatedAt: assetTypes.updatedAt,
      })
      .from(assetTypes)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(assetTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get asset type IDs for additional data
    const assetTypeIds = result.map((ac) => ac.id);

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: assetTypes.id,
        parentId: assetTypes.parentId,
        parentName: sql<string>`parent_ac.name`.as('parentName'),
      })
      .from(assetTypes)
      .leftJoin(
        sql`${assetTypes} parent_ac`,
        sql`${assetTypes.parentId} = parent_ac.id`,
      )
      .where(
        and(
          inArray(assetTypes.id, assetTypeIds),
          eq(assetTypes.isDeleted, false),
        ),
      );

    // Get subcategories count for each asset type
    const subcategoriesCountQuery = await this.db
      .select({
        parentId: assetTypes.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(assetTypes)
      .where(
        and(
          inArray(assetTypes.parentId, assetTypeIds),
          eq(assetTypes.isDeleted, false),
        ),
      )
      .groupBy(assetTypes.parentId);

    // Get assets count for each category
    const assetsCountMap = await this.getAssetsCountForCategories(
      assetTypeIds,
      businessId,
    );

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subcategoriesCountMap = new Map(
      subcategoriesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // Generate signed URLs for images and build final data
    const data = await Promise.all(
      result.map(async (assetCategory) => {
        return {
          id: assetCategory.id.toString(),
          name: assetCategory.name,
          description: assetCategory.description,
          status: assetCategory.status,
          parentId: assetCategory.parentId?.toString(),
          parentName: parentInfoMap.get(assetCategory.id) || undefined,
          subcategoriesCount: subcategoriesCountMap.get(assetCategory.id) || 0,
          assetsCount: assetsCountMap.get(assetCategory.id) || 0,
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if an asset type with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingAssetType = await this.db
      .select()
      .from(assetTypes)
      .where(
        and(
          eq(assetTypes.businessId, businessId),
          ilike(assetTypes.name, name),
          eq(assetTypes.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingAssetType };
  }

  async findOne(userId: string, id: string): Promise<AssetTypeDto> {
    // Get the asset type
    const assetCategory = await this.db
      .select()
      .from(assetTypes)
      .where(and(eq(assetTypes.id, id), eq(assetTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!assetCategory) {
      throw new NotFoundException(`Asset type with ID ${id} not found`);
    }

    // Get user's activeBusinessId to verify permission
    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== assetCategory.businessId
    ) {
      throw new UnauthorizedException('Access denied to this asset type');
    }

    return await this.mapToAssetTypeDto(assetCategory);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetTypeDto: UpdateAssetTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<AssetTypeDto> {
    // Get the asset type
    const existingAssetType = await this.db
      .select()
      .from(assetTypes)
      .where(and(eq(assetTypes.id, id), eq(assetTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingAssetType) {
      throw new NotFoundException(`Asset type with ID ${id} not found`);
    }

    // Verify business ownership
    if (businessId !== existingAssetType.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this asset type',
      );
    }

    // Check for name conflict if name is being updated
    if (
      updateAssetTypeDto.name &&
      updateAssetTypeDto.name !== existingAssetType.name
    ) {
      const nameConflict = await this.db
        .select()
        .from(assetTypes)
        .where(
          and(
            eq(assetTypes.businessId, businessId),
            ilike(assetTypes.name, updateAssetTypeDto.name),
            eq(assetTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (nameConflict) {
        throw new ConflictException(
          `An asset type with the name '${updateAssetTypeDto.name}' already exists for this business`,
        );
      }
    }

    // Handle parent category validation if being updated
    if (updateAssetTypeDto.parentId) {
      // Prevent asset type from being its own parent
      if (updateAssetTypeDto.parentId === id) {
        throw new BadRequestException('Asset type cannot be its own parent');
      }

      const parentCategory = await this.db
        .select()
        .from(assetTypes)
        .where(
          and(
            eq(assetTypes.id, updateAssetTypeDto.parentId),
            eq(assetTypes.businessId, existingAssetType.businessId),
            eq(assetTypes.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!parentCategory) {
        throw new BadRequestException('Parent asset type not found');
      }
    }

    try {
      // Update the asset type
      const [updatedAssetType] = await this.db
        .update(assetTypes)
        .set({
          ...updateAssetTypeDto,
          updatedAt: new Date(),
        })
        .where(eq(assetTypes.id, id))
        .returning();

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ASSET_TYPE,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return await this.mapToAssetTypeDto(updatedAssetType);
    } catch (error) {
      throw new BadRequestException(
        `Failed to update asset type: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ success: boolean; message: string }> {
    // Get the asset type
    const existingAssetType = await this.db
      .select()
      .from(assetTypes)
      .where(and(eq(assetTypes.id, id), eq(assetTypes.isDeleted, false)))
      .then((results) => results[0]);

    if (!existingAssetType) {
      throw new NotFoundException(`Asset type with ID ${id} not found`);
    }

    if (businessId !== existingAssetType.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this asset type',
      );
    }

    // Check for child categories
    const childCategories = await this.db
      .select()
      .from(assetTypes)
      .where(and(eq(assetTypes.parentId, id), eq(assetTypes.isDeleted, false)));

    if (childCategories.length > 0) {
      throw new BadRequestException(
        'Cannot delete asset type with child categories. Please remove or reassign child categories first.',
      );
    }

    // Soft delete the asset type
    await this.db
      .update(assetTypes)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(assetTypes.id, id));

    // Log the activity
    await this.activityLogService.logDelete(
      id,
      EntityType.ASSET_TYPE,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      success: true,
      message: `Asset type with ID ${id} has been deleted`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    assetTypeIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetTypeIds || assetTypeIds.length === 0) {
        throw new BadRequestException(
          'No asset type IDs provided for deletion',
        );
      }

      // Get all asset categories that exist and belong to the business
      const existingAssetCategories = await this.db
        .select({
          id: assetTypes.id,
          name: assetTypes.name,
          businessId: assetTypes.businessId,
        })
        .from(assetTypes)
        .where(
          and(
            inArray(assetTypes.id, assetTypeIds),
            eq(assetTypes.businessId, businessId),
            eq(assetTypes.isDeleted, false),
          ),
        );

      if (existingAssetCategories.length === 0) {
        throw new NotFoundException(
          'No valid asset categories found for deletion',
        );
      }

      // Check if any of the found asset categories have child categories
      const categoriesWithChildren = await this.db
        .select({
          parentId: assetTypes.parentId,
          childName: assetTypes.name,
        })
        .from(assetTypes)
        .where(
          and(
            inArray(
              assetTypes.parentId,
              existingAssetCategories.map((c) => c.id),
            ),
            eq(assetTypes.isDeleted, false),
          ),
        );

      if (categoriesWithChildren.length > 0) {
        const parentIds = [
          ...new Set(categoriesWithChildren.map((c) => c.parentId)),
        ];
        const parentNames = existingAssetCategories
          .filter((c) => parentIds.includes(c.id))
          .map((c) => c.name);

        throw new BadRequestException(
          `Cannot delete asset categories with child categories: ${parentNames.join(', ')}. Please remove or reassign child categories first.`,
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const assetCategory of existingAssetCategories) {
          // Soft delete the asset type
          await tx
            .update(assetTypes)
            .set({
              isDeleted: true,
              updatedBy: userId,
              updatedAt: currentTime,
            })
            .where(eq(assetTypes.id, assetCategory.id));

          deletedIds.push(assetCategory.id);
        }
      });

      // Log bulk delete operation
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.ASSET_TYPE,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { assetTypeIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} asset categories`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete asset categories: ${error.message}`,
      );
    }
  }

  async findAllSlim(businessId: string | null): Promise<AssetTypeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Find all asset categories with only essential fields
    const assetCategoryResults = await this.db
      .select({
        id: assetTypes.id,
        name: assetTypes.name,
      })
      .from(assetTypes)
      .where(
        and(
          eq(assetTypes.isDeleted, false),
          eq(assetTypes.status, CategoryStatus.ACTIVE),
          eq(assetTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(assetTypes.name), asc(assetTypes.id));

    return assetCategoryResults.map((assetCategory) => ({
      id: assetCategory.id.toString(),
      name: assetCategory.name,
    }));
  }

  async findAllHierarchy(
    businessId: string | null,
  ): Promise<{ id: string; name: string; parentId: string | null }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all asset categories with only the essential hierarchy fields
    const assetCategoryResults = await this.db
      .select({
        id: assetTypes.id,
        name: assetTypes.name,
        parentId: assetTypes.parentId,
      })
      .from(assetTypes)
      .where(
        and(
          eq(assetTypes.isDeleted, false),
          eq(assetTypes.status, CategoryStatus.ACTIVE),
          eq(assetTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(assetTypes.name), asc(assetTypes.id));

    return assetCategoryResults.map((assetCategory) => ({
      id: assetCategory.id.toString(),
      name: assetCategory.name,
      parentId: assetCategory.parentId?.toString() || null,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetTypeDto: CreateAssetTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    const assetCategory = await this.create(
      userId,
      businessId,
      createAssetTypeDto,
      metadata,
    );
    return { id: assetCategory.id };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createAssetCategoriesDto: CreateAssetTypeDto[],
    metadata?: ActivityMetadata,
  ): Promise<AssetTypeDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createAssetCategoriesDto || createAssetCategoriesDto.length === 0) {
        throw new BadRequestException(
          'No asset categories provided for creation',
        );
      }

      // Check for duplicate names within the request
      const requestNames = createAssetCategoriesDto.map((dto) =>
        dto.name.toLowerCase(),
      );
      const duplicateNames = requestNames.filter(
        (name, index) => requestNames.indexOf(name) !== index,
      );
      if (duplicateNames.length > 0) {
        throw new BadRequestException(
          `Duplicate asset type names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check if any asset categories with the same names already exist for this business
      const existingAssetCategories = await this.db
        .select()
        .from(assetTypes)
        .where(
          and(
            eq(assetTypes.businessId, businessId),
            sql`LOWER(${assetTypes.name}) IN (${requestNames.map((name) => `'${name}'`).join(',')})`,
            eq(assetTypes.isDeleted, false),
          ),
        );

      if (existingAssetCategories.length > 0) {
        const existingNames = existingAssetCategories.map((c) => c.name);
        throw new ConflictException(
          `Asset categories with the following names already exist: ${existingNames.join(', ')}`,
        );
      }

      const createdAssetCategories: AssetTypeDto[] = [];

      // Use a transaction to ensure all asset categories are created or none are
      await this.db.transaction(async (tx) => {
        for (const createAssetTypeDto of createAssetCategoriesDto) {
          // Create the asset type
          const [newAssetType] = await tx
            .insert(assetTypes)
            .values({
              businessId,
              name: createAssetTypeDto.name,
              description: createAssetTypeDto.description,
              parentId: createAssetTypeDto.parentId,
              category: createAssetTypeDto.category,
              createdBy: userId,
              status: createAssetTypeDto.status ?? CategoryStatus.ACTIVE,
            })
            .returning();

          createdAssetCategories.push(
            await this.mapToAssetTypeDto(newAssetType),
          );
        }
      });

      // Log bulk create operation
      const createdIds = createdAssetCategories.map((ac) => ac.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.ASSET_TYPE,
        createdIds,
        {
          names: createAssetCategoriesDto.map((dto) => dto.name),
          status: CategoryStatus.ACTIVE,
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createAssetCategoriesDto.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return createdAssetCategories;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create asset categories: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createAssetCategoriesDto: CreateAssetTypeDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[] }> {
    const assetTypes = await this.bulkCreate(
      userId,
      businessId,
      createAssetCategoriesDto,
      metadata,
    );
    return { ids: assetTypes.map((assetCategory) => assetCategory.id) };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetTypeDto: UpdateAssetTypeDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateAssetTypeDto, metadata);
    return { id };
  }

  async updateAssetTypePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
    metadata?: ActivityMetadata,
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for asset type ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all asset categories belong to the business
      const assetTypeIds = updates.map((update) => update.id);
      const existingAssetCategories = await this.db
        .select({
          id: assetTypes.id,
          parentId: assetTypes.parentId,
        })
        .from(assetTypes)
        .where(
          and(
            eq(assetTypes.businessId, businessId),
            inArray(assetTypes.id, assetTypeIds),
            eq(assetTypes.isDeleted, false),
          ),
        );

      if (existingAssetCategories.length !== assetTypeIds.length) {
        const foundIds = existingAssetCategories.map((ac) => ac.id);
        const missingIds = assetTypeIds.filter((id) => !foundIds.includes(id));
        throw new BadRequestException(
          `Asset categories not found or don't belong to this business: ${missingIds.join(', ')}`,
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          await tx
            .update(assetTypes)
            .set({
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(assetTypes.id, update.id),
                eq(assetTypes.businessId, businessId),
                eq(assetTypes.isDeleted, false),
              ),
            );
        }
        updatedCount = updates.length;
      });

      // Log bulk position update operation
      const updatedIds = updates.map((update) => update.id);
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_UPDATE,
        EntityType.ASSET_TYPE,
        updatedIds,
        { positionsUpdated: true },
        userId,
        businessId,
        {
          filterCriteria: { positionUpdates: updates.length },
          executionStrategy: ExecutionStrategy.PARALLEL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update asset type positions: ${error.message}`,
      );
    }
  }

  async bulkUpdateAssetTypeHierarchy(
    userId: string,
    businessId: string | null,
    updates: { id: string; parentId: string | null }[],
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    failed: Array<{ id: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No hierarchy updates provided');
      }

      const failed: Array<{ id: string; error: string }> = [];
      let updatedCount = 0;

      // Validate that all asset categories belong to the business
      const assetTypeIds = updates.map((update) => update.id);
      const existingAssetCategories = await this.db
        .select({ id: assetTypes.id, parentId: assetTypes.parentId })
        .from(assetTypes)
        .where(
          and(
            eq(assetTypes.businessId, businessId),
            inArray(assetTypes.id, assetTypeIds),
            eq(assetTypes.isDeleted, false),
          ),
        );

      const existingAssetTypeMap = new Map(
        existingAssetCategories.map((ac) => [ac.id, ac]),
      );

      // Use a transaction to ensure data consistency
      await this.db.transaction(async (tx) => {
        for (const update of updates) {
          try {
            // Check if asset type exists
            if (!existingAssetTypeMap.has(update.id)) {
              failed.push({
                id: update.id,
                error:
                  'Asset type not found or does not belong to this business',
              });
              continue;
            }

            // Check if parent exists (if parentId is not null)
            if (update.parentId !== null) {
              const parentExists = await tx
                .select({ id: assetTypes.id })
                .from(assetTypes)
                .where(
                  and(
                    eq(assetTypes.id, update.parentId),
                    eq(assetTypes.businessId, businessId),
                    eq(assetTypes.isDeleted, false),
                  ),
                )
                .limit(1);

              if (parentExists.length === 0) {
                failed.push({
                  id: update.id,
                  error: 'Parent asset type not found',
                });
                continue;
              }

              // Check for circular reference (asset type cannot be its own ancestor)
              if (update.parentId === update.id) {
                failed.push({
                  id: update.id,
                  error: 'Asset type cannot be its own parent',
                });
                continue;
              }

              // Check if this would create a circular reference
              const wouldCreateCircle = async (
                parentId: string,
                childId: string,
              ): Promise<boolean> => {
                const parent = await tx
                  .select({ parentId: assetTypes.parentId })
                  .from(assetTypes)
                  .where(
                    and(
                      eq(assetTypes.id, parentId),
                      eq(assetTypes.businessId, businessId),
                      eq(assetTypes.isDeleted, false),
                    ),
                  )
                  .limit(1);

                if (parent.length === 0) return false;
                if (parent[0].parentId === childId) return true;
                if (parent[0].parentId)
                  return wouldCreateCircle(parent[0].parentId, childId);
                return false;
              };

              if (await wouldCreateCircle(update.parentId, update.id)) {
                failed.push({
                  id: update.id,
                  error: 'Would create circular reference',
                });
                continue;
              }
            }

            // Update the asset type
            await tx
              .update(assetTypes)
              .set({
                parentId: update.parentId,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(assetTypes.id, update.id),
                  eq(assetTypes.businessId, businessId),
                  eq(assetTypes.isDeleted, false),
                ),
              );

            updatedCount++;

            // Log the hierarchy update activity
            await this.activityLogService.logUpdate(
              update.id,
              EntityType.ASSET_TYPE,
              userId,
              businessId,
              {
                source: metadata?.source || ActivitySource.WEB,
                ipAddress: metadata?.ipAddress,
                userAgent: metadata?.userAgent,
                sessionId: metadata?.sessionId,
              },
            );
          } catch (error) {
            failed.push({
              id: update.id,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return { updated: updatedCount, failed };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update asset type hierarchy: ${error.message}`,
      );
    }
  }

  async bulkUpdateAssetTypeStatus(
    userId: string,
    businessId: string | null,
    assetTypeIds: string[],
    status: CategoryStatus,
    metadata?: ActivityMetadata,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ assetTypeId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!assetTypeIds || assetTypeIds.length === 0) {
        throw new BadRequestException(
          'No asset type IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ assetTypeId: string; error: string }> = [];

      // Process each asset type ID
      await this.db.transaction(async (tx) => {
        for (const assetTypeId of assetTypeIds) {
          try {
            // Check if asset type exists and belongs to the business
            const existingAssetType = await tx
              .select()
              .from(assetTypes)
              .where(
                and(
                  eq(assetTypes.id, assetTypeId),
                  eq(assetTypes.businessId, businessId),
                  eq(assetTypes.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (!existingAssetType) {
              failed.push({
                assetTypeId,
                error: 'Asset type not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingAssetType.status === status) {
              failed.push({
                assetTypeId,
                error: `Asset type already has status: ${status}`,
              });
              continue;
            }

            // Update the asset type status
            await tx
              .update(assetTypes)
              .set({
                status,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(assetTypes.id, assetTypeId),
                  eq(assetTypes.businessId, businessId),
                  eq(assetTypes.isDeleted, false),
                ),
              );

            updatedCount++;
            updatedIds.push(assetTypeId);
          } catch (error) {
            failed.push({
              assetTypeId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      // Log bulk status change operation if any asset categories were updated
      if (updatedIds.length > 0) {
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_STATUS_CHANGE,
          EntityType.ASSET_TYPE,
          updatedIds,
          { status },
          userId,
          businessId,
          {
            filterCriteria: { assetTypeIds, targetStatus: status },
            failures: failed.map((f) => ({
              id: f.assetTypeId,
              error: f.error,
            })),
            executionStrategy: ExecutionStrategy.SEQUENTIAL,
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk update asset type status: ${error.message}`,
      );
    }
  }

  private async getAssetsCountForCategory(
    assetTypeId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(assets)
        .where(
          and(
            eq(assets.businessId, businessId),
            eq(assets.categoryId, assetTypeId),
            eq(assets.isDeleted, false),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get assets count for asset type ${assetTypeId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getAssetsCountForCategories(
    assetTypeIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (assetTypeIds.length === 0) {
        return new Map();
      }

      // Count assets where assetTypeId matches
      const assetResults = await this.db
        .select({
          assetTypeId: assets.categoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(assets)
        .where(
          and(
            eq(assets.businessId, businessId),
            inArray(assets.categoryId, assetTypeIds),
            eq(assets.isDeleted, false),
          ),
        )
        .groupBy(assets.categoryId);

      // Combine the counts
      const countsMap = new Map<string, number>();

      // Initialize all asset type IDs with 0
      assetTypeIds.forEach((id) => countsMap.set(id, 0));

      // Add asset counts
      assetResults.forEach((result) => {
        if (result.assetTypeId) {
          countsMap.set(
            result.assetTypeId,
            (countsMap.get(result.assetTypeId) || 0) + result.count,
          );
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get assets count for asset categories:',
        error.message,
      );
      return new Map();
    }
  }

  private async mapToAssetTypeDto(
    assetCategory: typeof assetTypes.$inferSelect,
  ): Promise<AssetTypeDto> {
    // Get assets count for this asset type
    const assetsCount = await this.getAssetsCountForCategory(
      assetCategory.id,
      assetCategory.businessId,
    );

    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      assetCategory.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (assetCategory.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        assetCategory.updatedBy.toString(),
      );
    }

    const assetCategoryDto: AssetTypeDto = {
      id: assetCategory.id.toString(),
      businessId: assetCategory.businessId.toString(),
      name: assetCategory.name,
      description: assetCategory.description,
      parentId: assetCategory.parentId?.toString(),
      category: assetCategory.category,
      createdBy: createdByName,
      updatedBy: updatedByName,
      status: assetCategory.status,
      assetsCount,
      createdAt: assetCategory.createdAt,
      updatedAt: assetCategory.updatedAt,
    };

    return assetCategoryDto;
  }
}
