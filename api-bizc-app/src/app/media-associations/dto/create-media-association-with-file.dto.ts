import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEnum,
  IsNumber,
  IsString,
  IsBoolean,
  IsOptional,
  IsUUID,
  Min,
  <PERSON>ength,
} from 'class-validator';
import { MediaReferenceType } from '../../drizzle/schema/media.schema';
import { MediaPurpose } from '../../drizzle/schema/media-associations.schema';

export class CreateMediaAssociationWithFileDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Reference entity ID',
  })
  @IsUUID()
  referenceId: string;

  @ApiProperty({
    example: MediaReferenceType.PRODUCTS,
    enum: MediaReferenceType,
    description: 'Type of the reference entity',
  })
  @IsEnum(MediaReferenceType)
  referenceType: MediaReferenceType;

  @ApiPropertyOptional({
    example: 0,
    description: 'Position/order of the media within the reference',
    default: 0,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value, 10))
  @IsNumber()
  @Min(0)
  position?: number = 0;

  @ApiPropertyOptional({
    example: MediaPurpose.PRIMARY,
    enum: MediaPurpose,
    description: 'Purpose of the media association',
    default: MediaPurpose.OTHER,
  })
  @IsOptional()
  @IsEnum(MediaPurpose)
  purpose?: MediaPurpose = MediaPurpose.OTHER;

  @ApiPropertyOptional({
    example: 'Product Main Image',
    description: 'Optional title for the media association',
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiPropertyOptional({
    example: 'High resolution product image showing front view',
    description: 'Optional description for the media association',
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether this is the primary media for the reference',
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isPrimary?: boolean = false;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether this media is visible',
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isVisible?: boolean = true;

  @ApiPropertyOptional({
    example: '{"dimensions": {"width": 1920, "height": 1080}}',
    description: 'Additional metadata as JSON string',
  })
  @IsOptional()
  @IsString()
  metadata?: string;
}