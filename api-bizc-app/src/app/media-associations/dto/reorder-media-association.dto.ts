import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsUUID, Min, ValidateNested } from 'class-validator';

export class MediaPositionDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Media association ID',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    example: 0,
    description: 'New position for the media',
  })
  @IsNumber()
  @Min(0)
  position: number;
}

export class ReorderMediaAssociationDto {
  @ApiProperty({
    type: [MediaPositionDto],
    description: 'Array of media positions to update',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MediaPositionDto)
  mediaPositions: MediaPositionDto[];
}