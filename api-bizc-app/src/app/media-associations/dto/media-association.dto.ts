import { ApiProperty } from '@nestjs/swagger';
import { MediaDto } from '../../media/dto/media.dto';
import { MediaReferenceType } from '../../drizzle/schema/media.schema';
import { MediaPurpose } from '../../drizzle/schema/media-associations.schema';

export class MediaAssociationDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Media association ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Business ID',
  })
  businessId: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Reference entity ID',
  })
  referenceId: string;

  @ApiProperty({
    example: MediaReferenceType.PRODUCTS,
    enum: MediaReferenceType,
    description: 'Type of the reference entity',
  })
  referenceType: MediaReferenceType;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Associated media ID',
  })
  mediaId: string;

  @ApiProperty({
    example: 0,
    description: 'Position/order of the media within the reference',
  })
  position: number;

  @ApiProperty({
    example: MediaPurpose.PRIMARY,
    enum: MediaPurpose,
    description: 'Purpose of the media association',
  })
  purpose: MediaPurpose;

  @ApiProperty({
    example: 'Product Main Image',
    description: 'Optional title for the media association',
    required: false,
  })
  title?: string;

  @ApiProperty({
    example: 'High resolution product image showing front view',
    description: 'Optional description for the media association',
    required: false,
  })
  description?: string;

  @ApiProperty({
    example: true,
    description: 'Whether this is the primary media for the reference',
  })
  isPrimary: boolean;

  @ApiProperty({
    example: true,
    description: 'Whether this media is visible',
  })
  isVisible: boolean;

  @ApiProperty({
    example: '{"dimensions": {"width": 1920, "height": 1080}}',
    description: 'Additional metadata as JSON string',
    required: false,
  })
  metadata?: string;

  @ApiProperty({
    type: MediaDto,
    description: 'Associated media details',
    required: false,
  })
  media?: MediaDto;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'User who created this record',
  })
  createdBy: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'User who last updated this record',
    required: false,
  })
  updatedBy?: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
