import {
  Injectable,
  Inject,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import {
  mediaAssociations,
  MediaPurpose,
} from '../drizzle/schema/media-associations.schema';
import { MediaReferenceType } from '../drizzle/schema/media.schema';
import { eq, and, isNull, isNotNull, not } from 'drizzle-orm';
import { MediaService } from '../media/media.service';
import { CreateMediaAssociationWithFileDto } from './dto/create-media-association-with-file.dto';
import { UpdateMediaAssociationDto } from './dto/update-media-association.dto';
import { MediaAssociationDto } from './dto/media-association.dto';
import { ReorderMediaAssociationDto } from './dto/reorder-media-association.dto';
import { CloudProvider } from '../shared/types';
import { AppLoggerService } from '../../core/services/logger.service';

@Injectable()
export class MediaAssociationsService {
  private readonly logger: AppLoggerService;

  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly mediaService: MediaService,
  ) {
    this.logger = AppLoggerService.create('MediaAssociationsService');
  }

  async create(
    file: Express.Multer.File,
    dto: CreateMediaAssociationWithFileDto,
    businessId: string,
    userId: string,
  ): Promise<MediaAssociationDto> {
    this.logger.logMethodEntry('create', {
      fileName: file.originalname,
      referenceId: dto.referenceId,
      referenceType: dto.referenceType,
      businessId,
      userId,
    });

    try {
      // Check if trying to set as primary
      if (dto.isPrimary) {
        // Check if another primary already exists for this reference and purpose
        const existingPrimary = await this.db
          .select()
          .from(mediaAssociations)
          .where(
            and(
              eq(mediaAssociations.businessId, businessId),
              eq(mediaAssociations.referenceId, dto.referenceId),
              eq(mediaAssociations.referenceType, dto.referenceType),
              eq(mediaAssociations.purpose, dto.purpose || MediaPurpose.OTHER),
              eq(mediaAssociations.isPrimary, true),
              isNull(mediaAssociations.isDeleted),
            ),
          )
          .then((results) => results[0]);

        if (existingPrimary) {
          throw new ConflictException(
            `A primary media already exists for this reference with purpose ${dto.purpose || MediaPurpose.OTHER}`,
          );
        }
      }

      // Upload the file using media service
      const uploadedMedia = await this.mediaService.uploadMediaWithReference(
        file,
        dto.referenceType,
        businessId,
        userId,
        dto.referenceId,
        CloudProvider.GCP,
      );

      // Create media association
      const [newAssociation] = await this.db
        .insert(mediaAssociations)
        .values({
          businessId,
          referenceId: dto.referenceId,
          referenceType: dto.referenceType,
          mediaId: uploadedMedia.id,
          position: dto.position || 0,
          purpose: dto.purpose || MediaPurpose.OTHER,
          title: dto.title,
          description: dto.description,
          isPrimary: dto.isPrimary || false,
          isVisible: dto.isVisible ?? true,
          metadata: dto.metadata,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      const result = this.mapToMediaAssociationDto(
        newAssociation,
        uploadedMedia,
      );

      this.logger.logBusinessOperation(
        'create',
        'media-association',
        result.id,
        userId,
      );
      this.logger.logMethodExit('create', { id: result.id });

      return result;
    } catch (error) {
      this.logger.error(
        'Failed to create media association',
        error.stack,
        'create',
      );

      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create media association: ${error.message}`,
      );
    }
  }

  async createMultiple(
    files: Express.Multer.File[],
    dto: CreateMediaAssociationWithFileDto,
    businessId: string,
    userId: string,
  ): Promise<MediaAssociationDto[]> {
    this.logger.logMethodEntry('createMultiple', {
      fileCount: files.length,
      referenceId: dto.referenceId,
      referenceType: dto.referenceType,
      businessId,
      userId,
    });

    const createdAssociations: MediaAssociationDto[] = [];

    // Upload all files first
    const uploadedMedia =
      await this.mediaService.uploadMultipleMediaWithReference(
        files,
        dto.referenceType,
        businessId,
        userId,
        dto.referenceId,
        CloudProvider.GCP,
      );

    // Create associations for each uploaded media
    for (let i = 0; i < uploadedMedia.length; i++) {
      const media = uploadedMedia[i];
      const position = (dto.position || 0) + i;

      try {
        const [newAssociation] = await this.db
          .insert(mediaAssociations)
          .values({
            businessId,
            referenceId: dto.referenceId,
            referenceType: dto.referenceType,
            mediaId: media.id,
            position,
            purpose: dto.purpose || MediaPurpose.OTHER,
            title: dto.title,
            description: dto.description,
            isPrimary: dto.isPrimary && i === 0, // Only first can be primary
            isVisible: dto.isVisible ?? true,
            metadata: dto.metadata,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning();

        createdAssociations.push(
          this.mapToMediaAssociationDto(newAssociation, media),
        );
      } catch (error) {
        this.logger.warn(
          `Failed to create association for media ${media.id}: ${error.message}`,
        );
      }
    }

    this.logger.logMethodExit('createMultiple', {
      createdCount: createdAssociations.length,
      totalFiles: files.length,
    });

    return createdAssociations;
  }

  async findByReferenceId(
    referenceId: string,
    businessId: string,
    includeMedia: boolean = false,
  ): Promise<MediaAssociationDto[]> {
    const associations = await this.db
      .select()
      .from(mediaAssociations)
      .where(
        and(
          eq(mediaAssociations.referenceId, referenceId),
          eq(mediaAssociations.businessId, businessId),
          isNull(mediaAssociations.isDeleted),
        ),
      )
      .orderBy(mediaAssociations.position);

    if (!includeMedia) {
      return associations.map((assoc) => this.mapToMediaAssociationDto(assoc));
    }

    // Fetch media details for each association
    const associationsWithMedia = await Promise.all(
      associations.map(async (assoc) => {
        try {
          const media = await this.mediaService.findById(
            assoc.mediaId,
            businessId,
          );
          return this.mapToMediaAssociationDto(assoc, media);
        } catch {
          // If media not found, return association without media
          return this.mapToMediaAssociationDto(assoc);
        }
      }),
    );

    return associationsWithMedia;
  }

  async findById(
    id: string,
    businessId: string,
    includeMedia: boolean = false,
  ): Promise<MediaAssociationDto> {
    const association = await this.db
      .select()
      .from(mediaAssociations)
      .where(
        and(
          eq(mediaAssociations.id, id),
          eq(mediaAssociations.businessId, businessId),
          isNull(mediaAssociations.isDeleted),
        ),
      )
      .then((results) => results[0]);

    if (!association) {
      throw new NotFoundException(`Media association with ID ${id} not found`);
    }

    if (!includeMedia) {
      return this.mapToMediaAssociationDto(association);
    }

    try {
      const media = await this.mediaService.findById(
        association.mediaId,
        businessId,
      );
      return this.mapToMediaAssociationDto(association, media);
    } catch {
      return this.mapToMediaAssociationDto(association);
    }
  }

  async update(
    id: string,
    dto: UpdateMediaAssociationDto,
    businessId: string,
    userId: string,
  ): Promise<MediaAssociationDto> {
    // Verify association exists
    await this.findById(id, businessId);

    // If trying to set as primary, check for conflicts
    if (dto.isPrimary === true) {
      const association = await this.db
        .select()
        .from(mediaAssociations)
        .where(eq(mediaAssociations.id, id))
        .then((results) => results[0]);

      const existingPrimary = await this.db
        .select()
        .from(mediaAssociations)
        .where(
          and(
            eq(mediaAssociations.businessId, businessId),
            eq(mediaAssociations.referenceId, association.referenceId),
            eq(mediaAssociations.referenceType, association.referenceType),
            eq(mediaAssociations.purpose, dto.purpose || association.purpose),
            eq(mediaAssociations.isPrimary, true),
            isNull(mediaAssociations.isDeleted),
            // Exclude current association
            not(eq(mediaAssociations.id, id)),
          ),
        )
        .then((results) => results[0]);

      if (existingPrimary) {
        throw new ConflictException(
          `A primary media already exists for this reference with purpose ${dto.purpose || association.purpose}`,
        );
      }
    }

    const [updatedAssociation] = await this.db
      .update(mediaAssociations)
      .set({
        ...dto,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(mediaAssociations.id, id),
          eq(mediaAssociations.businessId, businessId),
        ),
      )
      .returning();

    return this.mapToMediaAssociationDto(updatedAssociation);
  }

  async updateWithFile(
    id: string,
    file: Express.Multer.File,
    dto: UpdateMediaAssociationDto,
    businessId: string,
    userId: string,
  ): Promise<MediaAssociationDto> {
    // Get existing association
    const existingAssociation = await this.findById(id, businessId);

    // Upload new file
    const uploadedMedia = await this.mediaService.uploadMediaWithReference(
      file,
      existingAssociation.referenceType,
      businessId,
      userId,
      existingAssociation.referenceId,
      CloudProvider.GCP,
    );

    // Delete old media
    try {
      await this.mediaService.deleteMedia(
        existingAssociation.mediaId,
        businessId,
        existingAssociation.referenceType,
      );
    } catch (error) {
      this.logger.warn(`Failed to delete old media: ${error.message}`);
    }

    // Update association with new media ID
    const [updatedAssociation] = await this.db
      .update(mediaAssociations)
      .set({
        ...dto,
        mediaId: uploadedMedia.id,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(mediaAssociations.id, id),
          eq(mediaAssociations.businessId, businessId),
        ),
      )
      .returning();

    return this.mapToMediaAssociationDto(updatedAssociation, uploadedMedia);
  }

  async softDelete(
    id: string,
    businessId: string,
    userId: string,
  ): Promise<void> {
    this.logger.logMethodEntry('softDelete', { id, businessId, userId });

    // Verify association exists
    await this.findById(id, businessId);

    this.logger.logDatabaseOperation('soft delete', 'media_associations', {
      id,
    });

    await this.db
      .update(mediaAssociations)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(mediaAssociations.id, id),
          eq(mediaAssociations.businessId, businessId),
        ),
      );

    this.logger.logBusinessOperation(
      'soft delete',
      'media-association',
      id,
      userId,
    );
    this.logger.logMethodExit('softDelete');
  }

  async reorderMediaAssociations(
    referenceId: string,
    dto: ReorderMediaAssociationDto,
    businessId: string,
    userId: string,
  ): Promise<void> {
    // Verify all associations exist and belong to the reference
    for (const position of dto.mediaPositions) {
      const association = await this.db
        .select()
        .from(mediaAssociations)
        .where(
          and(
            eq(mediaAssociations.id, position.id),
            eq(mediaAssociations.businessId, businessId),
            eq(mediaAssociations.referenceId, referenceId),
            isNull(mediaAssociations.isDeleted),
          ),
        )
        .then((results) => results[0]);

      if (!association) {
        throw new NotFoundException(
          `Media association ${position.id} not found or doesn't belong to reference ${referenceId}`,
        );
      }
    }

    // Update positions
    for (const position of dto.mediaPositions) {
      await this.db
        .update(mediaAssociations)
        .set({
          position: position.position,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(mediaAssociations.id, position.id));
    }
  }

  async deleteByReferenceId(
    referenceId: string,
    businessId: string,
    userId: string,
  ): Promise<void> {
    // Soft delete all associations for the reference
    await this.db
      .update(mediaAssociations)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(mediaAssociations.referenceId, referenceId),
          eq(mediaAssociations.businessId, businessId),
          isNull(mediaAssociations.isDeleted),
        ),
      );
  }

  async restore(
    id: string,
    businessId: string,
    userId: string,
  ): Promise<MediaAssociationDto> {
    const association = await this.db
      .select()
      .from(mediaAssociations)
      .where(
        and(
          eq(mediaAssociations.id, id),
          eq(mediaAssociations.businessId, businessId),
          isNotNull(mediaAssociations.isDeleted),
        ),
      )
      .then((results) => results[0]);

    if (!association) {
      throw new NotFoundException(
        `Deleted media association with ID ${id} not found`,
      );
    }

    const [restoredAssociation] = await this.db
      .update(mediaAssociations)
      .set({
        isDeleted: false,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(mediaAssociations.id, id))
      .returning();

    return this.mapToMediaAssociationDto(restoredAssociation);
  }

  private mapToMediaAssociationDto(
    association: typeof mediaAssociations.$inferSelect,
    media?: any,
  ): MediaAssociationDto {
    return {
      id: association.id,
      businessId: association.businessId,
      referenceId: association.referenceId,
      referenceType: association.referenceType as MediaReferenceType,
      mediaId: association.mediaId,
      position: association.position,
      purpose: association.purpose as MediaPurpose,
      title: association.title,
      description: association.description,
      isPrimary: association.isPrimary,
      isVisible: association.isVisible,
      metadata: association.metadata,
      media: media || undefined,
      createdBy: association.createdBy,
      updatedBy: association.updatedBy,
      createdAt: association.createdAt,
      updatedAt: association.updatedAt,
    };
  }
}
