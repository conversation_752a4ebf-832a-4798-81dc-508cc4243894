import {
  Controller,
  Post,
  Get,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  Req,
  UploadedFile,
  UploadedFiles,
  ParseFilePipe,
  FileTypeValidator,
  MaxFileSizeValidator,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { MediaAssociationsService } from './media-associations.service';
import { CreateMediaAssociationWithFileDto } from './dto/create-media-association-with-file.dto';
import { UpdateMediaAssociationDto } from './dto/update-media-association.dto';
import { MediaAssociationDto } from './dto/media-association.dto';
import { MediaAssociationIdResponseDto } from './dto/media-association-id-response.dto';
import { ReorderMediaAssociationDto } from './dto/reorder-media-association.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { AppLoggerService } from '../../core/services/logger.service';

@ApiTags('Media Associations')
@Controller('media-associations')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@ApiBearerAuth()
export class MediaAssociationsController {
  private readonly logger: AppLoggerService;

  constructor(
    private readonly mediaAssociationsService: MediaAssociationsService,
  ) {
    this.logger = AppLoggerService.create('MediaAssociationsController');
  }

  @Post()
  @RequirePermissions(Permission.CATEGORY_CREATE)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Create a new media association with file upload' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file', 'referenceId', 'referenceType'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload',
        },
        referenceId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        referenceType: {
          type: 'string',
          enum: [
            'products',
            'assets',
            'staff',
            'accommodation-units',
            'packages',
            'comments',
            'games',
            'service-categories',
            'other',
          ],
        },
        position: {
          type: 'number',
          example: 0,
        },
        purpose: {
          type: 'string',
          enum: [
            'primary',
            'gallery',
            'attachment',
            'og_image',
            'banner',
            'icon',
            'other',
          ],
        },
        title: {
          type: 'string',
          example: 'Product Main Image',
        },
        description: {
          type: 'string',
          example: 'High resolution product image',
        },
        isPrimary: {
          type: 'boolean',
          example: false,
        },
        isVisible: {
          type: 'boolean',
          example: true,
        },
        metadata: {
          type: 'string',
          example: '{"dimensions": {"width": 1920, "height": 1080}}',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media association created successfully',
    type: MediaAssociationDto,
  })
  async create(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB
          new FileTypeValidator({
            fileType: /(jpg|jpeg|png|gif|pdf|doc|docx)$/,
          }),
        ],
      }),
    )
    file: Express.Multer.File,
    @Body() dto: CreateMediaAssociationWithFileDto,
    @Req() req: any,
  ): Promise<MediaAssociationDto> {
    this.logger.logApiRequest('POST', '/media-associations', req.user.id);

    const result = await this.mediaAssociationsService.create(
      file,
      dto,
      req.user.activeBusinessId,
      req.user.id,
    );

    this.logger.logApiResponse('POST', '/media-associations', 201);
    return result;
  }

  @Post('multiple')
  @RequirePermissions(Permission.CATEGORY_CREATE)
  @UseInterceptors(FilesInterceptor('files', 10))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create multiple media associations with file uploads',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['files', 'referenceId', 'referenceType'],
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Files to upload (max 10)',
        },
        referenceId: {
          type: 'string',
          format: 'uuid',
          example: '550e8400-e29b-41d4-a716-************',
        },
        referenceType: {
          type: 'string',
          enum: [
            'products',
            'assets',
            'staff',
            'accommodation-units',
            'packages',
            'comments',
            'games',
            'service-categories',
            'other',
          ],
        },
        position: {
          type: 'number',
          example: 0,
        },
        purpose: {
          type: 'string',
          enum: [
            'primary',
            'gallery',
            'attachment',
            'og_image',
            'banner',
            'icon',
            'other',
          ],
        },
        title: {
          type: 'string',
          example: 'Product Images',
        },
        description: {
          type: 'string',
          example: 'Product gallery images',
        },
        isPrimary: {
          type: 'boolean',
          example: false,
        },
        isVisible: {
          type: 'boolean',
          example: true,
        },
        metadata: {
          type: 'string',
          example: '{"gallery": true}',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media associations created successfully',
    type: [MediaAssociationDto],
  })
  async createMultiple(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB per file
          new FileTypeValidator({
            fileType: /(jpg|jpeg|png|gif|pdf|doc|docx)$/,
          }),
        ],
      }),
    )
    files: Express.Multer.File[],
    @Body() dto: CreateMediaAssociationWithFileDto,
    @Req() req: any,
  ): Promise<MediaAssociationDto[]> {
    return this.mediaAssociationsService.createMultiple(
      files,
      dto,
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @Get('reference/:referenceId')
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Get all media associations for a reference' })
  @ApiResponse({
    status: 200,
    description: 'List of media associations',
    type: [MediaAssociationDto],
  })
  async findByReference(
    @Param('referenceId') referenceId: string,
    @Query('includeMedia') includeMedia: boolean = false,
    @Req() req: any,
  ): Promise<MediaAssociationDto[]> {
    return this.mediaAssociationsService.findByReferenceId(
      referenceId,
      req.user.activeBusinessId,
      includeMedia,
    );
  }

  @Get(':id')
  @RequirePermissions(Permission.CATEGORY_READ)
  @ApiOperation({ summary: 'Get a media association by ID' })
  @ApiResponse({
    status: 200,
    description: 'Media association details',
    type: MediaAssociationDto,
  })
  async findOne(
    @Param('id') id: string,
    @Query('includeMedia') includeMedia: boolean = false,
    @Req() req: any,
  ): Promise<MediaAssociationDto> {
    return this.mediaAssociationsService.findById(
      id,
      req.user.activeBusinessId,
      includeMedia,
    );
  }

  @Patch(':id')
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Update a media association (without file)' })
  @ApiResponse({
    status: 200,
    description: 'Media association updated successfully',
    type: MediaAssociationDto,
  })
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateMediaAssociationDto,
    @Req() req: any,
  ): Promise<MediaAssociationDto> {
    return this.mediaAssociationsService.update(
      id,
      dto,
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @Patch(':id/file')
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Update a media association with new file' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'New file to upload',
        },
        position: {
          type: 'number',
          example: 0,
        },
        purpose: {
          type: 'string',
          enum: [
            'primary',
            'gallery',
            'attachment',
            'og_image',
            'banner',
            'icon',
            'other',
          ],
        },
        title: {
          type: 'string',
          example: 'Updated Product Image',
        },
        description: {
          type: 'string',
          example: 'Updated high resolution product image',
        },
        isPrimary: {
          type: 'boolean',
          example: false,
        },
        isVisible: {
          type: 'boolean',
          example: true,
        },
        metadata: {
          type: 'string',
          example: '{"updated": true}',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Media association updated with new file successfully',
    type: MediaAssociationDto,
  })
  async updateWithFile(
    @Param('id') id: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 10 * 1024 * 1024 }), // 10MB
          new FileTypeValidator({
            fileType: /(jpg|jpeg|png|gif|pdf|doc|docx)$/,
          }),
        ],
      }),
    )
    file: Express.Multer.File,
    @Body() dto: UpdateMediaAssociationDto,
    @Req() req: any,
  ): Promise<MediaAssociationDto> {
    return this.mediaAssociationsService.updateWithFile(
      id,
      file,
      dto,
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @Delete(':id')
  @RequirePermissions(Permission.CATEGORY_DELETE)
  @ApiOperation({ summary: 'Soft delete a media association' })
  @ApiResponse({
    status: 200,
    description: 'Media association deleted successfully',
    type: MediaAssociationIdResponseDto,
  })
  async delete(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<MediaAssociationIdResponseDto> {
    this.logger.logApiRequest('DELETE', `/media-associations/${id}`, req.user.id);

    await this.mediaAssociationsService.softDelete(
      id,
      req.user.activeBusinessId,
      req.user.id,
    );

    this.logger.logApiResponse('DELETE', `/media-associations/${id}`, 200);
    return { id };
  }

  @Patch('reference/:referenceId/reorder')
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Reorder media associations for a reference' })
  @ApiResponse({
    status: 200,
    description: 'Media associations reordered successfully',
  })
  async reorder(
    @Param('referenceId') referenceId: string,
    @Body() dto: ReorderMediaAssociationDto,
    @Req() req: any,
  ): Promise<void> {
    return this.mediaAssociationsService.reorderMediaAssociations(
      referenceId,
      dto,
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @Delete('reference/:referenceId')
  @RequirePermissions(Permission.CATEGORY_DELETE)
  @ApiOperation({ summary: 'Delete all media associations for a reference' })
  @ApiResponse({
    status: 200,
    description: 'All media associations for reference deleted successfully',
  })
  async deleteByReference(
    @Param('referenceId') referenceId: string,
    @Req() req: any,
  ): Promise<void> {
    return this.mediaAssociationsService.deleteByReferenceId(
      referenceId,
      req.user.activeBusinessId,
      req.user.id,
    );
  }

  @Patch(':id/restore')
  @RequirePermissions(Permission.CATEGORY_UPDATE)
  @ApiOperation({ summary: 'Restore a soft-deleted media association' })
  @ApiResponse({
    status: 200,
    description: 'Media association restored successfully',
    type: MediaAssociationDto,
  })
  async restore(
    @Param('id') id: string,
    @Req() req: any,
  ): Promise<MediaAssociationDto> {
    return this.mediaAssociationsService.restore(
      id,
      req.user.activeBusinessId,
      req.user.id,
    );
  }
}
