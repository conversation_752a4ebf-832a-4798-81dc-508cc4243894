import { Modu<PERSON> } from '@nestjs/common';
import { MediaAssociationsController } from './media-associations.controller';
import { MediaAssociationsService } from './media-associations.service';
import { AuthModule } from '../auth/auth.module';
import { DrizzleModule } from '../drizzle/drizzle.module';
import { MediaModule } from '../media/media.module';
import { LoggerModule } from '../../core/services/logger.module';

@Module({
  imports: [
    AuthModule, 
    DrizzleModule, 
    MediaModule,
    LoggerModule.forFeature('MediaAssociations'),
  ],
  controllers: [MediaAssociationsController],
  providers: [MediaAssociationsService],
  exports: [MediaAssociationsService],
})
export class MediaAssociationsModule {}