import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateSupplierDto } from './dto/create-supplier.dto';
import { UpdateSupplierDto } from './dto/update-supplier.dto';
import { SupplierDto } from './dto/supplier.dto';
import { SupplierSlimDto } from './dto/supplier-slim.dto';
import { SupplierAutocompleteDto } from './dto/supplier-autocomplete.dto';
import { SupplierListDto } from './dto/supplier-list.dto';
import { suppliers, SupplierStatus } from '../drizzle/schema/suppliers.schema';
import { BankAccountEntityType } from '../shared/types';
import { AddressService } from '../address/address.service';
import { BankAccountsService } from '../bank-accounts/bank-accounts.service';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { MediaService } from '../media/media.service';

@Injectable()
export class SuppliersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly addressService: AddressService,
    private readonly bankAccountsService: BankAccountsService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createSupplierDto: CreateSupplierDto,
    profileImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a supplier with the same display name already exists for this business
      const existingSupplier = await this.db
        .select()
        .from(suppliers)
        .where(
          and(
            eq(suppliers.businessId, businessId),
            ilike(suppliers.displayName, createSupplierDto.displayName),
            eq(suppliers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingSupplier) {
        throw new ConflictException(
          `A supplier with the display name '${createSupplierDto.displayName}' already exists for this business`,
        );
      }

      // Check if a supplier with the same email already exists for this business
      const existingEmailSupplier = await this.db
        .select()
        .from(suppliers)
        .where(
          and(
            eq(suppliers.businessId, businessId),
            eq(suppliers.email, createSupplierDto.email),
            eq(suppliers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingEmailSupplier) {
        throw new ConflictException(
          `A supplier with the email '${createSupplierDto.email}' already exists for this business`,
        );
      }

      let profileImageId: string | undefined;

      // Upload profile image if provided
      if (profileImageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          profileImageFile,
          'suppliers/profiles',
          businessId,
          userId,
        );
        profileImageId = uploadedMedia.id;
      }

      // Use a transaction to ensure position reordering and supplier creation are atomic
      const newSupplier = await this.db.transaction(async (tx) => {
        // Shift all existing suppliers down by 1 position to make room at position 1
        await this.reorderPositions(tx, businessId, 1);

        // Create address if provided
        let addressId: string | undefined;
        if (createSupplierDto.address) {
          const addressResult = await this.addressService.create(
            userId,
            {
              street: createSupplierDto.address.street,
              city: createSupplierDto.address.city,
              state: createSupplierDto.address.state,
              zipCode: createSupplierDto.address.zipCode,
              country: createSupplierDto.address.country,
              addressType: createSupplierDto.address.addressType,
              businessId,
              isDefault: createSupplierDto.address.isDefault ?? false,
            },
            tx,
          );
          addressId = addressResult.id;
        }

        // Create bank account if provided
        let bankAccountId: string | undefined;
        if (createSupplierDto.bankAccount) {
          const bankAccountResult = await this.bankAccountsService.create(
            userId,
            {
              businessId,
              entityType: BankAccountEntityType.SUPPLIER,
              bankName: createSupplierDto.bankAccount.bankName,
              accountHolderName:
                createSupplierDto.bankAccount.accountHolderName,
              branchName: createSupplierDto.bankAccount.branchName,
              bankCode: createSupplierDto.bankAccount.bankCode,
              branchCode: createSupplierDto.bankAccount.branchCode,
              accountName: createSupplierDto.bankAccount.accountName,
              accountNumber: createSupplierDto.bankAccount.accountNumber,
              routingNumber: createSupplierDto.bankAccount.routingNumber,
              iban: createSupplierDto.bankAccount.iban,
              swiftCode: createSupplierDto.bankAccount.swiftCode,
              accountType: createSupplierDto.bankAccount.accountType,
              currency: createSupplierDto.bankAccount.currency,
              isPrimary: createSupplierDto.bankAccount.isPrimary ?? false,
              isActive: createSupplierDto.bankAccount.isActive ?? true,
            },
            undefined, // metadata
            tx, // Pass transaction
          );
          bankAccountId = bankAccountResult.id;
        }

        // Insert new supplier at position 1
        const [supplier] = await tx
          .insert(suppliers)
          .values({
            businessId,
            title: createSupplierDto.title,
            firstName: createSupplierDto.firstName,
            lastName: createSupplierDto.lastName,
            companyName: createSupplierDto.companyName,
            displayName: createSupplierDto.displayName,
            email: createSupplierDto.email,
            website: createSupplierDto.website,
            fax: createSupplierDto.fax,
            phoneNumber: createSupplierDto.phoneNumber,
            mobileNumber: createSupplierDto.mobileNumber,
            openingBalance: createSupplierDto.openingBalance,
            openingBalanceDate: createSupplierDto.openingBalanceDate,
            netDays: createSupplierDto.netDays,
            creditLimit: createSupplierDto.creditLimit,
            taxNumber: createSupplierDto.taxNumber,
            notes: createSupplierDto.notes,
            position: 1, // Always create new suppliers at position 1 (first)
            attachments: createSupplierDto.attachments ?? [],
            profileImage: profileImageId || createSupplierDto.profileImage,
            addressId,
            bankAccountId,
            status: createSupplierDto.status ?? SupplierStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        // Update the bank account with the supplier's entity ID if bank account was created
        if (bankAccountId) {
          await this.bankAccountsService.updateEntityId(
            userId,
            bankAccountId,
            supplier.id,
            businessId,
            tx,
          );
        }

        return supplier;
      });

      // Log the supplier creation activity
      await this.activityLogService.logCreate(
        newSupplier.id,
        EntityType.SUPPLIER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newSupplier.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create supplier: ${error.message}`,
      );
    }
  }

  /**
   * Reorder positions to make room for a new supplier at the specified position
   */
  private async reorderPositions(
    tx: any,
    businessId: string,
    insertPosition: number,
  ): Promise<void> {
    // Shift all suppliers at or after the insert position down by 1
    await tx
      .update(suppliers)
      .set({
        position: sql`${suppliers.position} + 1`,
      })
      .where(
        and(
          eq(suppliers.businessId, businessId),
          gte(suppliers.position, insertPosition),
          eq(suppliers.isDeleted, false),
        ),
      );
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createSupplierDto: CreateSupplierDto,
    profileImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(
      userId,
      businessId,
      createSupplierDto,
      profileImageFile,
      metadata,
    );
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createSupplierDtos: CreateSupplierDto[],
    profileImages?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check for duplicate display names within the batch
    const displayNames = createSupplierDtos.map((dto) => dto.displayName);
    const duplicateDisplayNames = displayNames.filter(
      (name, index) => displayNames.indexOf(name) !== index,
    );

    if (duplicateDisplayNames.length > 0) {
      throw new BadRequestException(
        `Duplicate display names found in batch: ${duplicateDisplayNames.join(', ')}`,
      );
    }

    // Check for duplicate emails within the batch
    const emails = createSupplierDtos.map((dto) => dto.email);
    const duplicateEmails = emails.filter(
      (email, index) => emails.indexOf(email) !== index,
    );

    if (duplicateEmails.length > 0) {
      throw new BadRequestException(
        `Duplicate emails found in batch: ${duplicateEmails.join(', ')}`,
      );
    }

    // Check if any display names already exist in the database
    const existingDisplayNames = await this.db
      .select({ displayName: suppliers.displayName })
      .from(suppliers)
      .where(
        and(
          eq(suppliers.businessId, businessId),
          inArray(suppliers.displayName, displayNames),
          eq(suppliers.isDeleted, false),
        ),
      );

    if (existingDisplayNames.length > 0) {
      const conflictingNames = existingDisplayNames.map((s) => s.displayName);
      throw new ConflictException(
        `Suppliers with these display names already exist: ${conflictingNames.join(', ')}`,
      );
    }

    // Check if any emails already exist in the database
    const existingEmails = await this.db
      .select({ email: suppliers.email })
      .from(suppliers)
      .where(
        and(
          eq(suppliers.businessId, businessId),
          inArray(suppliers.email, emails),
          eq(suppliers.isDeleted, false),
        ),
      );

    if (existingEmails.length > 0) {
      const conflictingEmails = existingEmails.map((s) => s.email);
      throw new ConflictException(
        `Suppliers with these emails already exist: ${conflictingEmails.join(', ')}`,
      );
    }

    const createdIds: string[] = [];

    try {
      // Create suppliers one by one to maintain position ordering
      for (let i = 0; i < createSupplierDtos.length; i++) {
        const dto = createSupplierDtos[i];
        const profileImageFile = profileImages?.[i];

        const result = await this.create(
          userId,
          businessId,
          dto,
          profileImageFile,
        );
        createdIds.push(result.id);
      }

      return { ids: createdIds };
    } catch (error) {
      throw new BadRequestException(
        `Failed to bulk create suppliers: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createSupplierDtos: CreateSupplierDto[],
    profileImages?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    return this.bulkCreate(
      userId,
      businessId,
      createSupplierDtos,
      profileImages,
    );
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<SupplierDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const supplier = await this.db
      .select()
      .from(suppliers)
      .where(
        and(
          eq(suppliers.id, id),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!supplier) {
      throw new NotFoundException('Supplier not found');
    }

    // View logging removed to reduce activity log volume

    return this.mapToSupplierDto(supplier);
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<SupplierSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: suppliers.id,
        displayName: suppliers.displayName,
        companyName: suppliers.companyName,
        email: suppliers.email,
        phoneNumber: suppliers.phoneNumber,
        status: suppliers.status,
        profileImage: suppliers.profileImage,
      })
      .from(suppliers)
      .where(
        and(
          eq(suppliers.isDeleted, false),
          eq(suppliers.status, SupplierStatus.ACTIVE),
          eq(suppliers.businessId, businessId),
        ),
      )
      .orderBy(asc(suppliers.position), asc(suppliers.id));

    // View logging removed to reduce activity log volume

    return await Promise.all(
      result.map(async (supplier) => {
        let profileImageUrl: string | undefined;

        if (supplier.profileImage) {
          try {
            const mediaRecord = await this.mediaService.findById(
              supplier.profileImage,
              businessId,
            );
            profileImageUrl = mediaRecord?.publicUrl;
          } catch {
            // Ignore media fetch errors
          }
        }

        return {
          id: supplier.id,
          displayName: supplier.displayName,
          companyName: supplier.companyName,
          email: supplier.email,
          phoneNumber: supplier.phoneNumber,
          status: supplier.status,
          profileImage: profileImageUrl,
        } as SupplierSlimDto;
      }),
    );
  }

  async findAutocomplete(
    businessId: string,
    search?: string,
    limit = 10,
  ): Promise<SupplierAutocompleteDto[]> {
    const conditions = [
      eq(suppliers.businessId, businessId),
      eq(suppliers.status, SupplierStatus.ACTIVE),
      eq(suppliers.isDeleted, false),
    ];

    if (search && search.trim()) {
      conditions.push(ilike(suppliers.displayName, `%${search.trim()}%`));
    }

    const result = await this.db
      .select({
        id: suppliers.id,
        displayName: suppliers.displayName,
      })
      .from(suppliers)
      .where(and(...conditions))
      .orderBy(asc(suppliers.displayName))
      .limit(limit);

    return result.map((supplier) => ({
      id: supplier.id,
      name: supplier.displayName,
    }));
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    email?: string,
    phoneNumber?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: SupplierListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(suppliers.isDeleted, false),
      eq(suppliers.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(suppliers.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(suppliers.createdAt, toDate));
      }
    }

    // Add name filtering if provided (searches display name and company name)
    if (name) {
      whereConditions.push(
        or(
          ilike(suppliers.displayName, `%${name}%`),
          ilike(suppliers.companyName, `%${name}%`),
        ),
      );
    }

    // Add email filtering if provided
    if (email) {
      whereConditions.push(ilike(suppliers.email, `%${email}%`));
    }

    // Add phone number filtering if provided
    if (phoneNumber) {
      whereConditions.push(
        or(
          ilike(suppliers.phoneNumber, `%${phoneNumber}%`),
          ilike(suppliers.mobileNumber, `%${phoneNumber}%`),
        ),
      );
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as SupplierStatus);
      whereConditions.push(inArray(suppliers.status, statusArray));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'displayName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(suppliers.displayName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(suppliers.displayName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(suppliers.displayName, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${suppliers.displayName} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${suppliers.displayName} IS NULL OR ${suppliers.displayName} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${suppliers.displayName} IS NOT NULL AND ${suppliers.displayName} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(suppliers.status, value as SupplierStatus[]),
                  );
                } else {
                  filterConditions.push(eq(suppliers.status, value));
                }
                break;
              case 'ne':
                filterConditions.push(sql`${suppliers.status} != ${value}`);
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        throw new BadRequestException('Invalid filters format');
      }
    }

    // Build order by clause
    let orderByClause: any;
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        if (Array.isArray(sortConfig) && sortConfig.length > 0) {
          const { id: sortField, desc: isDesc } = sortConfig[0];

          switch (sortField) {
            case 'displayName':
              orderByClause = isDesc
                ? desc(suppliers.displayName)
                : asc(suppliers.displayName);
              break;
            case 'companyName':
              orderByClause = isDesc
                ? desc(suppliers.companyName)
                : asc(suppliers.companyName);
              break;
            case 'email':
              orderByClause = isDesc
                ? desc(suppliers.email)
                : asc(suppliers.email);
              break;
            case 'status':
              orderByClause = isDesc
                ? desc(suppliers.status)
                : asc(suppliers.status);
              break;
            case 'createdAt':
              orderByClause = isDesc
                ? desc(suppliers.createdAt)
                : asc(suppliers.createdAt);
              break;
            case 'updatedAt':
              orderByClause = isDesc
                ? desc(suppliers.updatedAt)
                : asc(suppliers.updatedAt);
              break;
            default:
              orderByClause = asc(suppliers.position);
          }
        } else {
          orderByClause = asc(suppliers.position);
        }
      } catch {
        orderByClause = asc(suppliers.position);
      }
    } else {
      orderByClause = asc(suppliers.position);
    }

    // Execute the main query with optimized selection
    const result = await this.db
      .select({
        id: suppliers.id,
        firstName: suppliers.firstName,
        lastName: suppliers.lastName,
        companyName: suppliers.companyName,
        displayName: suppliers.displayName,
        email: suppliers.email,
        phoneNumber: suppliers.phoneNumber,
        mobileNumber: suppliers.mobileNumber,
        openingBalance: suppliers.openingBalance,
        openingBalanceDate: suppliers.openingBalanceDate,
        netDays: suppliers.netDays,
        creditLimit: suppliers.creditLimit,
        status: suppliers.status,
        profileImage: suppliers.profileImage,
        taxNumber: suppliers.taxNumber,
        notes: suppliers.notes,
      })
      .from(suppliers)
      .where(and(...whereConditions))
      .orderBy(orderByClause, asc(suppliers.id))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(suppliers)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Map results to SupplierListDto
    const mappedResults = await Promise.all(
      result.map(async (supplier) => {
        let profileImageUrl: string | undefined;

        if (supplier.profileImage) {
          try {
            const mediaRecord = await this.mediaService.findById(
              supplier.profileImage,
              businessId,
            );
            profileImageUrl = mediaRecord?.publicUrl;
          } catch {
            // Ignore media fetch errors
          }
        }

        return {
          id: supplier.id,
          firstName: supplier.firstName,
          lastName: supplier.lastName,
          companyName: supplier.companyName,
          displayName: supplier.displayName,
          email: supplier.email,
          phoneNumber: supplier.phoneNumber,
          mobileNumber: supplier.mobileNumber,
          openingBalance: supplier.openingBalance,
          openingBalanceDate: supplier.openingBalanceDate,
          netDays: supplier.netDays,
          creditLimit: supplier.creditLimit,
          status: supplier.status,
          profileImage: profileImageUrl,
          taxNumber: supplier.taxNumber,
          notes: supplier.notes,
        } as SupplierListDto;
      }),
    );

    return {
      data: mappedResults,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkDisplayNameAvailability(
    _userId: string,
    businessId: string | null,
    displayName: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingSupplier = await this.db
      .select()
      .from(suppliers)
      .where(
        and(
          eq(suppliers.businessId, businessId),
          ilike(suppliers.displayName, displayName),
          eq(suppliers.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingSupplier };
  }

  async checkEmailAvailability(
    _userId: string,
    businessId: string | null,
    email: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingSupplier = await this.db
      .select()
      .from(suppliers)
      .where(
        and(
          eq(suppliers.businessId, businessId),
          eq(suppliers.email, email),
          eq(suppliers.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingSupplier };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateSupplierDto: UpdateSupplierDto,
    profileImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if supplier exists
      const existingSupplier = await this.db
        .select()
        .from(suppliers)
        .where(
          and(
            eq(suppliers.id, id),
            eq(suppliers.businessId, businessId),
            eq(suppliers.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingSupplier) {
        throw new NotFoundException('Supplier not found');
      }

      // Check if display name is being updated and if it conflicts
      if (
        updateSupplierDto.displayName &&
        updateSupplierDto.displayName !== existingSupplier.displayName
      ) {
        const conflictingSupplier = await this.db
          .select()
          .from(suppliers)
          .where(
            and(
              eq(suppliers.businessId, businessId),
              ilike(suppliers.displayName, updateSupplierDto.displayName),
              sql`${suppliers.id} != ${id}`,
              eq(suppliers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingSupplier) {
          throw new ConflictException(
            `A supplier with the display name '${updateSupplierDto.displayName}' already exists for this business`,
          );
        }
      }

      // Check if email is being updated and if it conflicts
      if (
        updateSupplierDto.email &&
        updateSupplierDto.email !== existingSupplier.email
      ) {
        const conflictingEmailSupplier = await this.db
          .select()
          .from(suppliers)
          .where(
            and(
              eq(suppliers.businessId, businessId),
              eq(suppliers.email, updateSupplierDto.email),
              sql`${suppliers.id} != ${id}`,
              eq(suppliers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (conflictingEmailSupplier) {
          throw new ConflictException(
            `A supplier with the email '${updateSupplierDto.email}' already exists for this business`,
          );
        }
      }

      let profileImageId: string | undefined;

      // Upload new profile image if provided
      if (profileImageFile) {
        const uploadedMedia = await this.mediaService.uploadMedia(
          profileImageFile,
          'suppliers/profiles',
          businessId,
          userId,
        );
        profileImageId = uploadedMedia.id;
      }

      // Use a transaction to handle address and bank account updates
      const updatedSupplier = await this.db.transaction(async (tx) => {
        // Handle address update/creation
        let addressId = existingSupplier.addressId;
        if (updateSupplierDto.address) {
          if (existingSupplier.addressId) {
            // Update existing address
            await this.addressService.update(
              userId,
              existingSupplier.addressId,
              {
                street: updateSupplierDto.address.street,
                city: updateSupplierDto.address.city,
                state: updateSupplierDto.address.state,
                zipCode: updateSupplierDto.address.zipCode,
                country: updateSupplierDto.address.country,
                addressType: updateSupplierDto.address.addressType,
                isDefault: updateSupplierDto.address.isDefault,
              },
              businessId,
              tx,
            );
          } else {
            // Create new address
            const addressResult = await this.addressService.create(
              userId,
              {
                street: updateSupplierDto.address.street,
                city: updateSupplierDto.address.city,
                state: updateSupplierDto.address.state,
                zipCode: updateSupplierDto.address.zipCode,
                country: updateSupplierDto.address.country,
                addressType: updateSupplierDto.address.addressType,
                businessId,
                isDefault: updateSupplierDto.address.isDefault ?? false,
              },
              tx,
            );
            addressId = addressResult.id;
          }
        }

        // Handle bank account update/creation
        let bankAccountId = existingSupplier.bankAccountId;
        if (updateSupplierDto.bankAccount) {
          if (existingSupplier.bankAccountId) {
            // Update existing bank account
            await this.bankAccountsService.update(
              userId,
              existingSupplier.bankAccountId,
              {
                bankName: updateSupplierDto.bankAccount.bankName,
                accountHolderName:
                  updateSupplierDto.bankAccount.accountHolderName,
                branchName: updateSupplierDto.bankAccount.branchName,
                bankCode: updateSupplierDto.bankAccount.bankCode,
                branchCode: updateSupplierDto.bankAccount.branchCode,
                accountName: updateSupplierDto.bankAccount.accountName,
                accountNumber: updateSupplierDto.bankAccount.accountNumber,
                routingNumber: updateSupplierDto.bankAccount.routingNumber,
                iban: updateSupplierDto.bankAccount.iban,
                swiftCode: updateSupplierDto.bankAccount.swiftCode,
                accountType: updateSupplierDto.bankAccount.accountType,
                currency: updateSupplierDto.bankAccount.currency,
                isPrimary: updateSupplierDto.bankAccount.isPrimary,
                isActive: updateSupplierDto.bankAccount.isActive,
              },
              undefined, // metadata
              businessId,
              tx,
            );
          } else {
            // Create new bank account
            const bankAccountResult = await this.bankAccountsService.create(
              userId,
              {
                businessId,
                entityId: id, // Set the supplier ID as entity ID
                entityType: BankAccountEntityType.SUPPLIER,
                bankName: updateSupplierDto.bankAccount.bankName,
                accountHolderName:
                  updateSupplierDto.bankAccount.accountHolderName,
                branchName: updateSupplierDto.bankAccount.branchName,
                bankCode: updateSupplierDto.bankAccount.bankCode,
                branchCode: updateSupplierDto.bankAccount.branchCode,
                accountName: updateSupplierDto.bankAccount.accountName,
                accountNumber: updateSupplierDto.bankAccount.accountNumber,
                routingNumber: updateSupplierDto.bankAccount.routingNumber,
                iban: updateSupplierDto.bankAccount.iban,
                swiftCode: updateSupplierDto.bankAccount.swiftCode,
                accountType: updateSupplierDto.bankAccount.accountType,
                currency: updateSupplierDto.bankAccount.currency,
                isPrimary: updateSupplierDto.bankAccount.isPrimary ?? false,
                isActive: updateSupplierDto.bankAccount.isActive ?? true,
              },
              undefined, // metadata
              tx,
            );
            bankAccountId = bankAccountResult.id;
          }
        }

        // Update the supplier
        const [supplier] = await tx
          .update(suppliers)
          .set({
            title: updateSupplierDto.title,
            firstName: updateSupplierDto.firstName,
            lastName: updateSupplierDto.lastName,
            companyName: updateSupplierDto.companyName,
            displayName: updateSupplierDto.displayName,
            email: updateSupplierDto.email,
            website: updateSupplierDto.website,
            fax: updateSupplierDto.fax,
            phoneNumber: updateSupplierDto.phoneNumber,
            mobileNumber: updateSupplierDto.mobileNumber,
            openingBalance: updateSupplierDto.openingBalance,
            openingBalanceDate: updateSupplierDto.openingBalanceDate,
            netDays: updateSupplierDto.netDays,
            creditLimit: updateSupplierDto.creditLimit,
            taxNumber: updateSupplierDto.taxNumber,
            notes: updateSupplierDto.notes,
            attachments: updateSupplierDto.attachments,
            profileImage: profileImageId || updateSupplierDto.profileImage,
            addressId,
            bankAccountId,
            status: updateSupplierDto.status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(suppliers.id, id),
              eq(suppliers.businessId, businessId),
              eq(suppliers.isDeleted, false),
            ),
          )
          .returning();

        return supplier[0];
      });

      if (!updatedSupplier) {
        throw new NotFoundException('Supplier not found');
      }

      // Log the supplier update activity
      await this.activityLogService.logUpdate(
        updatedSupplier.id,
        EntityType.SUPPLIER,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedSupplier.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update supplier: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateSupplierDto: UpdateSupplierDto,
    profileImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(
      userId,
      businessId,
      id,
      updateSupplierDto,
      profileImageFile,
      metadata,
    );
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const supplier = await this.db
      .select()
      .from(suppliers)
      .where(
        and(
          eq(suppliers.id, id),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!supplier) {
      throw new NotFoundException('Supplier not found');
    }

    // Soft delete the supplier
    await this.db
      .update(suppliers)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(suppliers.id, id),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      );

    // Log the supplier deletion activity
    await this.activityLogService.logDelete(
      supplier.id,
      EntityType.SUPPLIER,
      userId,
      businessId,
      {
        source: metadata?.source || ActivitySource.WEB,
        ipAddress: metadata?.ipAddress,
        userAgent: metadata?.userAgent,
        sessionId: metadata?.sessionId,
      },
    );

    return {
      id: supplier.id,
      message: 'Supplier deleted successfully',
    };
  }

  async bulkUpdateStatus(
    userId: string,
    businessId: string | null,
    ids: string[],
    status: SupplierStatus,
  ): Promise<{
    updated: number;
    message: string;
    updatedIds: string[];
    failed?: Array<{ supplierId: string; error: string }>;
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check which suppliers exist and belong to the business
    const existingSuppliers = await this.db
      .select({ id: suppliers.id, displayName: suppliers.displayName })
      .from(suppliers)
      .where(
        and(
          inArray(suppliers.id, ids),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      );

    if (existingSuppliers.length === 0) {
      throw new NotFoundException('No suppliers found to update');
    }

    const existingIds = existingSuppliers.map((s) => s.id);
    const failed: Array<{ supplierId: string; error: string }> = [];

    // Find suppliers that were not found (for failed array)
    const notFoundIds = ids.filter((id) => !existingIds.includes(id));
    notFoundIds.forEach((id) => {
      failed.push({ supplierId: id, error: 'Supplier not found' });
    });

    // Update the status of existing suppliers
    await this.db
      .update(suppliers)
      .set({
        status,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          inArray(suppliers.id, existingIds),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      );

    // Log the bulk status update activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_UPDATE,
      EntityType.SUPPLIER,
      existingIds,
      { status },
      userId,
      businessId,
      {
        filterCriteria: { supplierCount: existingSuppliers.length },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: ActivitySource.WEB,
      },
    );

    return {
      updated: existingSuppliers.length,
      message: `Successfully updated ${existingSuppliers.length} supplier${existingSuppliers.length > 1 ? 's' : ''}`,
      updatedIds: existingIds,
      failed: failed.length > 0 ? failed : undefined,
    };
  }

  async bulkRemove(
    userId: string,
    businessId: string | null,
    ids: string[],
  ): Promise<{ deletedCount: number; deletedIds: string[] }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check which suppliers exist and belong to the business
    const existingSuppliers = await this.db
      .select({ id: suppliers.id, displayName: suppliers.displayName })
      .from(suppliers)
      .where(
        and(
          inArray(suppliers.id, ids),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      );

    if (existingSuppliers.length === 0) {
      throw new NotFoundException('No suppliers found to delete');
    }

    const existingIds = existingSuppliers.map((s) => s.id);

    // Soft delete the suppliers
    await this.db
      .update(suppliers)
      .set({
        isDeleted: true,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(
        and(
          inArray(suppliers.id, existingIds),
          eq(suppliers.businessId, businessId),
          eq(suppliers.isDeleted, false),
        ),
      );

    // Log the bulk deletion activity
    await this.activityLogService.logBulkOperation(
      ActivityType.BULK_DELETE,
      EntityType.SUPPLIER,
      existingIds,
      {},
      userId,
      businessId,
      {
        filterCriteria: { supplierCount: existingSuppliers.length },
        executionStrategy: ExecutionStrategy.SEQUENTIAL,
        source: ActivitySource.WEB,
      },
    );

    return {
      deletedCount: existingSuppliers.length,
      deletedIds: existingIds,
    };
  }

  /**
   * Map database supplier record to SupplierDto
   */
  private async mapToSupplierDto(supplier: any): Promise<SupplierDto> {
    let profileImageUrl: string | undefined;
    let attachmentUrls: string[] = [];

    // Get profile image URL if exists
    if (supplier.profileImage) {
      try {
        const mediaRecord = await this.mediaService.findById(
          supplier.profileImage,
          supplier.businessId,
        );
        profileImageUrl = mediaRecord?.publicUrl;
      } catch {
        // Ignore media fetch errors
      }
    }

    // Get attachment URLs if exist
    if (supplier.attachments && supplier.attachments.length > 0) {
      try {
        const mediaRecords = await Promise.all(
          supplier.attachments.map((id: string) =>
            this.mediaService
              .findById(id, supplier.businessId)
              .catch(() => null),
          ),
        );
        attachmentUrls = mediaRecords
          .filter((record) => record !== null)
          .map((record) => record!.publicUrl);
      } catch {
        // Ignore media fetch errors
      }
    }

    return {
      id: supplier.id,
      businessId: supplier.businessId,
      title: supplier.title,
      firstName: supplier.firstName,
      lastName: supplier.lastName,
      companyName: supplier.companyName,
      displayName: supplier.displayName,
      email: supplier.email,
      website: supplier.website,
      fax: supplier.fax,
      phoneNumber: supplier.phoneNumber,
      mobileNumber: supplier.mobileNumber,
      openingBalance: supplier.openingBalance,
      openingBalanceDate: supplier.openingBalanceDate,
      netDays: supplier.netDays,
      creditLimit: supplier.creditLimit,
      taxNumber: supplier.taxNumber,
      notes: supplier.notes,
      position: supplier.position,
      attachments: attachmentUrls,
      profileImage: profileImageUrl,
      addressId: supplier.addressId,
      bankAccountId: supplier.bankAccountId,
      status: supplier.status,
      createdBy: supplier.createdBy,
      updatedBy: supplier.updatedBy,
      deletedBy: supplier.deletedBy,
      deletedAt: supplier.deletedAt,
      createdAt: supplier.createdAt,
      updatedAt: supplier.updatedAt,
    };
  }
}
