import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAssetDamageDto } from './dto/create-asset-damage.dto';
import { UpdateAssetDamageDto } from './dto/update-asset-damage.dto';
import { AssetDamageDto } from './dto/asset-damage.dto';
import { AssetDamageListDto } from './dto/asset-damage-list.dto';
import { AssetDamageIdResponseDto } from './dto/asset-damage-id-response.dto';
import { BulkDeleteAssetDamageResponseDto } from './dto/bulk-delete-asset-damage-response.dto';
import { PaginatedAssetDamagesResponseDto } from './dto/paginated-asset-damages-response.dto';
import { assetDamage } from '../drizzle/schema/asset-damage.schema';
import { assets } from '../drizzle/schema/assets.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { users } from '../drizzle/schema/users.schema';

import {
  and,
  eq,
  desc,
  asc,
  gte,
  lte,
  ilike,
  inArray,
  count,
  sql,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName, EntityType } from '../shared/types';
import { MediaService } from '../media/media.service';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class AssetDamagesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private activityLogService: ActivityLogService,
    private mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAssetDamageDto: CreateAssetDamageDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Validate asset exists and belongs to the business
      const assetExists = await this.db
        .select({ id: assets.id })
        .from(assets)
        .where(
          and(
            eq(assets.id, createAssetDamageDto.assetId),
            eq(assets.businessId, businessId),
            eq(assets.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!assetExists) {
        throw new BadRequestException(
          'Asset not found or does not belong to this business',
        );
      }

      // Validate staff members if provided
      if (createAssetDamageDto.fixedBy) {
        const staffExists = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createAssetDamageDto.fixedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffExists) {
          throw new BadRequestException(
            'Fixed by staff member not found or does not belong to this business',
          );
        }
      }

      if (createAssetDamageDto.checkedBy) {
        const staffExists = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, createAssetDamageDto.checkedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffExists) {
          throw new BadRequestException(
            'Checked by staff member not found or does not belong to this business',
          );
        }
      }

      const result = await this.db
        .insert(assetDamage)
        .values({
          businessId,
          assetId: createAssetDamageDto.assetId,
          damageType: createAssetDamageDto.damageType,
          amount: createAssetDamageDto.amount,
          incidentDate: createAssetDamageDto.incidentDate,
          incidentTime: createAssetDamageDto.incidentTime,
          details: createAssetDamageDto.details,
          isFixed: createAssetDamageDto.isFixed || false,
          fixedDate: createAssetDamageDto.fixedDate,
          fixedBy: createAssetDamageDto.fixedBy,
          checkedBy: createAssetDamageDto.checkedBy,
          fixedDetails: createAssetDamageDto.fixedDetails,
          fixedCost: createAssetDamageDto.fixedCost,
          createdBy: userId,
        })
        .returning({ id: assetDamage.id });

      const assetDamageId = result[0].id;

      // Log activity
      await this.activityLogService.logCreate(
        assetDamageId,
        EntityType.ASSET_DAMAGE,
        userId,
        businessId,
        {
          reason: 'Asset damage record created',
        },
      );

      return { id: assetDamageId };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to create asset damage record');
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAssetDamageDto: CreateAssetDamageDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetDamageIdResponseDto> {
    const result = await this.create(
      userId,
      businessId,
      createAssetDamageDto,
      metadata,
    );

    // Handle file attachments if provided
    if (attachmentFiles && attachmentFiles.length > 0) {
      const filesToUpload = createAssetDamageDto.attachmentIndexes
        ? createAssetDamageDto.attachmentIndexes
            .map((index: number) => attachmentFiles[index])
            .filter(Boolean)
        : attachmentFiles;

      if (filesToUpload.length > 0) {
        await this.mediaService.uploadMultipleMediaWithReference(
          filesToUpload,
          'asset-damages',
          businessId,
          userId,
          result.id,
        );
      }
    }

    return {
      id: result.id,
      message: 'Asset damage record created successfully',
    };
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    assetId?: string,
    damageType?: string,
    isFixed?: string,
    sort?: string,
  ): Promise<PaginatedAssetDamagesResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(assetDamage.isDeleted, false),
      eq(assetDamage.businessId, businessId),
    ];

    // Add filters
    if (from) {
      whereConditions.push(gte(assetDamage.incidentDate, from));
    }
    if (to) {
      whereConditions.push(lte(assetDamage.incidentDate, to));
    }
    if (assetId) {
      whereConditions.push(eq(assetDamage.assetId, assetId));
    }
    if (damageType) {
      whereConditions.push(ilike(assetDamage.damageType, `%${damageType}%`));
    }
    if (isFixed !== undefined) {
      whereConditions.push(eq(assetDamage.isFixed, isFixed === 'true'));
    }

    // Build sort order
    let orderBy: any;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction?.toLowerCase() === 'desc';

      switch (field) {
        case 'incidentDate':
          orderBy = isDesc
            ? desc(assetDamage.incidentDate)
            : asc(assetDamage.incidentDate);
          break;
        case 'amount':
          orderBy = isDesc ? desc(assetDamage.amount) : asc(assetDamage.amount);
          break;
        case 'damageType':
          orderBy = isDesc
            ? desc(assetDamage.damageType)
            : asc(assetDamage.damageType);
          break;
        case 'createdAt':
          orderBy = isDesc
            ? desc(assetDamage.createdAt)
            : asc(assetDamage.createdAt);
          break;
        default:
          orderBy = desc(assetDamage.createdAt);
      }
    } else {
      orderBy = desc(assetDamage.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(assetDamage)
      .where(and(...whereConditions));

    const total = totalResult[0].count;
    const totalPages = Math.ceil(total / limit);

    // Get paginated data
    const results = await this.db
      .select({
        id: assetDamage.id,
        assetId: assetDamage.assetId,
        assetName: sql<string>`CASE 
          WHEN ${assets.name} IS NOT NULL
          THEN ${assets.name}
          ELSE 'Unknown Asset'
        END`,
        damageType: assetDamage.damageType,
        amount: assetDamage.amount,
        incidentDate: assetDamage.incidentDate,
        incidentTime: assetDamage.incidentTime,
        isFixed: assetDamage.isFixed,
        fixedDate: assetDamage.fixedDate,
        fixedCost: assetDamage.fixedCost,
        createdAt: assetDamage.createdAt,
        updatedAt: assetDamage.updatedAt,
      })
      .from(assetDamage)
      .leftJoin(assets, eq(assetDamage.assetId, assets.id))
      .where(and(...whereConditions))
      .orderBy(orderBy)
      .limit(limit)
      .offset(offset);

    const data: AssetDamageListDto[] = results.map((row) => ({
      id: row.id,
      assetId: row.assetId,
      assetName: row.assetName || 'Unknown Asset',
      damageType: row.damageType,
      amount: row.amount,
      incidentDate: row.incidentDate,
      incidentTime: row.incidentTime,
      isFixed: row.isFixed,
      fixedDate: row.fixedDate,
      fixedCost: row.fixedCost,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    }));

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<AssetDamageDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: assetDamage.id,
        businessId: assetDamage.businessId,
        assetId: assetDamage.assetId,
        assetName: sql<string>`CASE
          WHEN ${assets.name} IS NOT NULL
          THEN ${assets.name}
          ELSE 'Unknown Asset'
        END`,
        damageType: assetDamage.damageType,
        amount: assetDamage.amount,
        incidentDate: assetDamage.incidentDate,
        incidentTime: assetDamage.incidentTime,
        details: assetDamage.details,
        isFixed: assetDamage.isFixed,
        fixedDate: assetDamage.fixedDate,
        fixedBy: assetDamage.fixedBy,
        fixedByName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        checkedBy: assetDamage.checkedBy,
        fixedDetails: assetDamage.fixedDetails,
        fixedCost: assetDamage.fixedCost,
        createdBy: assetDamage.createdBy,
        createdByName: users.displayName,
        updatedBy: assetDamage.updatedBy,
        createdAt: assetDamage.createdAt,
        updatedAt: assetDamage.updatedAt,
      })
      .from(assetDamage)
      .leftJoin(assets, eq(assetDamage.assetId, assets.id))
      .leftJoin(staffMembers, eq(assetDamage.fixedBy, staffMembers.id))
      .leftJoin(users, eq(assetDamage.createdBy, users.id))
      .where(
        and(
          eq(assetDamage.id, id),
          eq(assetDamage.businessId, businessId),
          eq(assetDamage.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException('Asset damage record not found');
    }

    // Get attachments
    const attachments = await this.getAttachments(result.id, businessId);

    // Get staff member names separately to avoid complex joins
    let checkedByName: string | undefined;
    let updatedByName: string | undefined;

    if (result.checkedBy) {
      const checkedByStaff = await this.db
        .select({
          name: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, result.checkedBy))
        .then((results) => results[0]);
      checkedByName = checkedByStaff?.name;
    }

    if (result.updatedBy) {
      const updatedByUser = await this.db
        .select({ displayName: users.displayName })
        .from(users)
        .where(eq(users.id, result.updatedBy))
        .then((results) => results[0]);
      updatedByName = updatedByUser?.displayName;
    }

    return {
      id: result.id,
      businessId: result.businessId,
      assetId: result.assetId,
      assetName: result.assetName || 'Unknown Asset',
      damageType: result.damageType,
      amount: result.amount,
      incidentDate: result.incidentDate,
      incidentTime: result.incidentTime,
      details: result.details,
      isFixed: result.isFixed,
      fixedDate: result.fixedDate,
      fixedBy: result.fixedBy,
      fixedByName: result.fixedByName,
      checkedBy: result.checkedBy,
      checkedByName,
      fixedDetails: result.fixedDetails,
      fixedCost: result.fixedCost,
      createdBy: result.createdBy,
      createdByName: result.createdByName || 'Unknown User',
      updatedBy: result.updatedBy,
      updatedByName,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      attachments,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetDamageDto: UpdateAssetDamageDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if asset damage record exists
      const existingRecord = await this.db
        .select({ id: assetDamage.id })
        .from(assetDamage)
        .where(
          and(
            eq(assetDamage.id, id),
            eq(assetDamage.businessId, businessId),
            eq(assetDamage.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingRecord) {
        throw new NotFoundException('Asset damage record not found');
      }

      // Validate asset if provided
      if (updateAssetDamageDto.assetId) {
        const assetExists = await this.db
          .select({ id: assets.id })
          .from(assets)
          .where(
            and(
              eq(assets.id, updateAssetDamageDto.assetId),
              eq(assets.businessId, businessId),
              eq(assets.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!assetExists) {
          throw new BadRequestException(
            'Asset not found or does not belong to this business',
          );
        }
      }

      // Validate staff members if provided
      if (updateAssetDamageDto.fixedBy) {
        const staffExists = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateAssetDamageDto.fixedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffExists) {
          throw new BadRequestException(
            'Fixed by staff member not found or does not belong to this business',
          );
        }
      }

      if (updateAssetDamageDto.checkedBy) {
        const staffExists = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.id, updateAssetDamageDto.checkedBy),
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (!staffExists) {
          throw new BadRequestException(
            'Checked by staff member not found or does not belong to this business',
          );
        }
      }

      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update provided fields
      if (updateAssetDamageDto.assetId !== undefined) {
        updateData.assetId = updateAssetDamageDto.assetId;
      }
      if (updateAssetDamageDto.damageType !== undefined) {
        updateData.damageType = updateAssetDamageDto.damageType;
      }
      if (updateAssetDamageDto.amount !== undefined) {
        updateData.amount = updateAssetDamageDto.amount;
      }
      if (updateAssetDamageDto.incidentDate !== undefined) {
        updateData.incidentDate = updateAssetDamageDto.incidentDate;
      }
      if (updateAssetDamageDto.incidentTime !== undefined) {
        updateData.incidentTime = updateAssetDamageDto.incidentTime;
      }
      if (updateAssetDamageDto.details !== undefined) {
        updateData.details = updateAssetDamageDto.details;
      }
      if (updateAssetDamageDto.isFixed !== undefined) {
        updateData.isFixed = updateAssetDamageDto.isFixed;
      }
      if (updateAssetDamageDto.fixedDate !== undefined) {
        updateData.fixedDate = updateAssetDamageDto.fixedDate;
      }
      if (updateAssetDamageDto.fixedBy !== undefined) {
        updateData.fixedBy = updateAssetDamageDto.fixedBy;
      }
      if (updateAssetDamageDto.checkedBy !== undefined) {
        updateData.checkedBy = updateAssetDamageDto.checkedBy;
      }
      if (updateAssetDamageDto.fixedDetails !== undefined) {
        updateData.fixedDetails = updateAssetDamageDto.fixedDetails;
      }
      if (updateAssetDamageDto.fixedCost !== undefined) {
        updateData.fixedCost = updateAssetDamageDto.fixedCost;
      }

      await this.db
        .update(assetDamage)
        .set(updateData)
        .where(eq(assetDamage.id, id));

      // Log activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.ASSET_DAMAGE,
        userId,
        businessId,
        {
          source: metadata?.source,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update asset damage record');
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAssetDamageDto: UpdateAssetDamageDto,
    attachmentFiles?: Express.Multer.File[],
    metadata?: ActivityMetadata,
  ): Promise<AssetDamageIdResponseDto> {
    const result = await this.update(
      userId,
      businessId,
      id,
      updateAssetDamageDto,
      metadata,
    );

    // Handle file attachments if provided
    if (attachmentFiles && attachmentFiles.length > 0) {
      const filesToUpload = updateAssetDamageDto.attachmentIndexes
        ? updateAssetDamageDto.attachmentIndexes
            .map((index: number) => attachmentFiles[index])
            .filter(Boolean)
        : attachmentFiles;

      if (filesToUpload.length > 0) {
        await this.mediaService.updateMediaForReference(
          id,
          filesToUpload,
          'asset-damages',
          businessId,
          userId,
        );
      }
    }

    return {
      id: result.id,
      message: 'Asset damage record updated successfully',
    };
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if asset damage record exists
      const existingRecord = await this.db
        .select({ id: assetDamage.id })
        .from(assetDamage)
        .where(
          and(
            eq(assetDamage.id, id),
            eq(assetDamage.businessId, businessId),
            eq(assetDamage.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingRecord) {
        throw new NotFoundException('Asset damage record not found');
      }

      // Soft delete
      await this.db
        .update(assetDamage)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(assetDamage.id, id));

      // Log activity
      await this.activityLogService.logDelete(
        id,
        EntityType.ASSET_DAMAGE,
        userId,
        businessId,
        {
          source: metadata?.source,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to delete asset damage record');
    }
  }

  async removeAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<AssetDamageIdResponseDto> {
    const result = await this.remove(userId, businessId, id, metadata);
    return {
      id: result.id,
      message: 'Asset damage record deleted successfully',
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    ids: string[],
    metadata?: ActivityMetadata,
  ): Promise<BulkDeleteAssetDamageResponseDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No IDs provided for deletion');
      }

      // Check which records exist
      const existingRecords = await this.db
        .select({ id: assetDamage.id })
        .from(assetDamage)
        .where(
          and(
            inArray(assetDamage.id, ids),
            eq(assetDamage.businessId, businessId),
            eq(assetDamage.isDeleted, false),
          ),
        );

      const existingIds = existingRecords.map((record) => record.id);
      const notFoundIds = ids.filter((id) => !existingIds.includes(id));

      if (existingIds.length === 0) {
        throw new NotFoundException('No asset damage records found to delete');
      }

      // Soft delete existing records
      await this.db
        .update(assetDamage)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(assetDamage.id, existingIds));

      // Log activity for each deleted record
      for (const recordId of existingIds) {
        await this.activityLogService.logDelete(
          recordId,
          EntityType.ASSET_DAMAGE,
          userId,
          businessId,
          {
            source: metadata?.source,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );
      }

      return {
        deletedIds: existingIds,
        notFoundIds,
        message: `Successfully deleted ${existingIds.length} asset damage record(s)`,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        'Failed to bulk delete asset damage records',
      );
    }
  }

  private async getAttachments(
    assetDamageId: string,
    businessId: string,
  ): Promise<Array<{ id: string; originalName: string; signedUrl: string }>> {
    try {
      const mediaRecords = await this.mediaService.findByReferenceId(
        assetDamageId,
        businessId,
      );

      const attachmentsWithSignedUrls = await Promise.all(
        mediaRecords.map(async (mediaRecord) => {
          try {
            const signedUrl = await this.mediaService.generateSignedUrlForMedia(
              mediaRecord.id,
              businessId,
              'asset-damages',
            );
            return {
              id: mediaRecord.id,
              originalName: mediaRecord.originalName,
              signedUrl,
            };
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for media ${mediaRecord.id}:`,
              error.message,
            );
            return {
              id: mediaRecord.id,
              originalName: mediaRecord.originalName,
              signedUrl: mediaRecord.publicUrl,
            };
          }
        }),
      );

      return attachmentsWithSignedUrls;
    } catch (error) {
      console.warn(
        `Failed to get attachments for asset damage ${assetDamageId}:`,
        error.message,
      );
      return [];
    }
  }
}
