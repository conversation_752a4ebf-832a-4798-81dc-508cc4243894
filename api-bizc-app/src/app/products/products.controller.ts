import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import {
  FilesInterceptor,
  FileFieldsInterceptor,
} from '@nestjs/platform-express';
import { ProductsService } from './products.service';
import { ComprehensiveCreateProductDto } from './dto/comprehensive-create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductDto } from './dto/product.dto';
import { ProductSlimDto } from './dto/product-slim.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import { ProductIdResponseDto } from './dto/product-id-response.dto';
import {
  BulkCreateProductDto,
  BulkProductIdsResponseDto,
} from './dto/bulk-create-product.dto';
import { DeleteProductResponseDto } from './dto/delete-product-response.dto';
import {
  BulkDeleteProductDto,
  BulkDeleteProductResponseDto,
} from './dto/bulk-delete-product.dto';
import { PaginatedProductsResponseDto } from './dto/paginated-products-response.dto';
import { ProductAvailabilityResponseDto } from './dto/check-product-availability.dto';
import {
  UpdateProductGlobalPositionsDto,
  UpdateProductCategoryPositionsDto,
  UpdateProductSubCategoryPositionsDto,
  UpdateProductPositionsResponseDto,
} from './dto/update-product-positions.dto';
import {
  CreateProductImageDto,
  UpdateProductImagesSortOrderDto,
} from './dto/product-image.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

interface AuthenticatedRequest {
  user: {
    id: string;
    activeBusinessId: string | null;
  };
}

@ApiTags('products')
@Controller('products')
@UseGuards(PermissionsGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_CREATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'images', maxCount: 10 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Create a new product (single, variable, or combo)',
    description:
      'Creates a product with support for variants (variable products) and combo items (combo products). Product type determines required fields.',
  })
  @ApiBody({
    description: 'Comprehensive product creation supporting all product types',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Samsung Galaxy S24',
          description: 'Product name (required)',
        },
        productType: {
          type: 'string',
          enum: [
            'single',
            'variable',
            'combo',
            'ingredient',
            'menu_item',
            'raw_material',
            'work_in_progress',
            'finished_good',
          ],
          example: 'variable',
          description:
            'Product type - determines which additional fields are required',
        },
        sku: {
          type: 'string',
          example: 'SGS24-BASE',
          description: 'SKU (required for single products)',
        },
        shortCode: {
          type: 'string',
          example: 'SGS24',
          description: 'Short code for the product',
        },
        barcode: {
          type: 'string',
          example: '1234567890123',
          description: 'Product barcode',
        },
        description: {
          type: 'string',
          example: 'Latest Samsung Galaxy smartphone with advanced features',
          description: 'Product description',
        },
        slug: {
          type: 'string',
          example: 'samsung-galaxy-s24',
          description: 'URL-friendly version of the product name',
        },
        categoryId: {
          type: 'string',
          format: 'uuid',
          description: 'Category ID',
        },
        brandId: {
          type: 'string',
          format: 'uuid',
          description: 'Brand ID',
        },
        basePrice: {
          type: 'string',
          example: '999.99',
          description: 'Base price (for single products)',
        },
        isSellable: {
          type: 'boolean',
          example: true,
          description: 'Whether the product is sellable',
          default: true,
        },
        availableOnline: {
          type: 'boolean',
          example: true,
          description: 'Whether the product is available online',
          default: false,
        },
        variants: {
          type: 'array',
          description: 'Product variants (for variable products)',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', example: '128GB Black' },
              sku: { type: 'string', example: 'SGS24-128GB-BLK' },
              price: { type: 'string', example: '999.99' },
              barcode: { type: 'string', example: '1234567890124' },
            },
            required: ['name', 'sku'],
          },
        },
        comboItems: {
          type: 'array',
          description: 'Combo product items (for combo products)',
          items: {
            type: 'object',
            properties: {
              childProductId: { type: 'string', format: 'uuid' },
              childVariantId: {
                type: 'string',
                format: 'uuid',
                description: 'Optional variant ID',
              },
              quantity: { type: 'string', example: '2.000' },
              isMandatory: { type: 'boolean', example: true },
            },
            required: ['childProductId', 'quantity'],
          },
        },
        locationIds: {
          type: 'array',
          items: {
            type: 'string',
            format: 'uuid',
          },
          description: 'Array of location IDs where this product is available',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'Product image files (up to 10 images)',
          maxItems: 10,
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Open Graph image file for social media',
        },
      },
      required: ['name', 'productType'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The product has been successfully created',
    type: ProductIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or missing required fields',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Product name, SKU, or slug already exists',
  })
  create(
    @Request() req: AuthenticatedRequest,
    @Body() createProductDto: ComprehensiveCreateProductDto,
    @UploadedFiles()
    files: {
      images?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    } = {},
  ): Promise<ProductIdResponseDto> {
    const images = files?.images || [];
    const ogImage = files?.ogImage?.[0];

    return this.productsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createProductDto,
      images,
      ogImage,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_CREATE)
  @UseInterceptors(FilesInterceptor('images'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Bulk create products with variants and combo support',
    description:
      'Create multiple products at once with support for variants and combo items. Images can be mapped to products using imageIndex.',
  })
  @ApiBody({
    description: 'Bulk product creation with optional image uploads',
    schema: {
      type: 'object',
      properties: {
        products: {
          type: 'string',
          description:
            'JSON string containing array of product objects with optional imageIndex property',
          example:
            '[{"name":"Product 1","productType":"single","sku":"P1","imageIndex":0},{"name":"Product 2","productType":"variable","variants":[{"name":"Variant 1","sku":"P2-V1"}]}]',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description:
            'Array of product image files (optional). Images are mapped using imageIndex.',
        },
      },
      required: ['products'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'The products have been successfully created',
    type: BulkProductIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or files',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async bulkCreate(
    @Request() req: AuthenticatedRequest,
    @Body() bulkCreateProductDto: BulkCreateProductDto,
    @UploadedFiles() images: Express.Multer.File[] = [],
  ): Promise<BulkProductIdsResponseDto> {
    const result = await this.productsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateProductDto.products,
      images,
    );

    // Transform the service response to match the expected DTO
    const successfulResults = result.results.filter((r) => r.success);
    const ids = successfulResults.map((r) => r.productId).filter(Boolean);

    return {
      ids,
      message: result.message,
      count: successfulResults.length,
    };
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({
    summary: 'Get all products for the active business with advanced filtering',
    description:
      'Retrieve products with support for filtering by type, variants, combo items, and comprehensive search options.',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
    example: 20,
  })
  @ApiQuery({
    name: 'search',
    description: 'Search across name, SKU, barcode, and short code',
    required: false,
    type: String,
    example: 'samsung',
  })
  @ApiQuery({
    name: 'name',
    description: 'Filter by product name',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'sku',
    description: 'Filter by SKU',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'barcode',
    description: 'Filter by barcode',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'productType',
    description: 'Filter by product type',
    required: false,
    enum: [
      'single',
      'variable',
      'combo',
      'ingredient',
      'menu_item',
      'raw_material',
      'work_in_progress',
      'finished_good',
    ],
    example: 'variable',
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by status (comma-separated for multiple)',
    required: false,
    type: String,
    example: 'active,inactive',
  })
  @ApiQuery({
    name: 'categoryId',
    description: 'Filter by category ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'brandId',
    description: 'Filter by brand ID',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'availableOnline',
    description: 'Filter by online availability',
    required: false,
    type: String,
    example: 'true,false',
  })
  @ApiQuery({
    name: 'featured',
    description: 'Filter by featured status',
    required: false,
    type: String,
    example: 'true',
  })
  @ApiQuery({
    name: 'includeVariants',
    description: 'Include product variants in response',
    required: false,
    type: Boolean,
    example: false,
  })
  @ApiQuery({
    name: 'includeComboItems',
    description: 'Include combo items in response',
    required: false,
    type: Boolean,
    example: false,
  })
  @ApiQuery({
    name: 'includeInventory',
    description: 'Include inventory information',
    required: false,
    type: Boolean,
    example: false,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter from date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter to date (YYYY-MM-DD format)',
    required: false,
    type: String,
    example: '2025-12-31',
  })
  @ApiQuery({
    name: 'minPrice',
    description: 'Minimum price filter',
    required: false,
    type: String,
    example: '10.00',
  })
  @ApiQuery({
    name: 'maxPrice',
    description: 'Maximum price filter',
    required: false,
    type: String,
    example: '1000.00',
  })
  @ApiQuery({
    name: 'filters',
    description: 'Advanced filters as JSON string',
    required: false,
    type: String,
    example:
      '[{"id":"name","value":"Samsung","operator":"iLike","type":"text"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description: 'Sort configuration as JSON string',
    required: false,
    type: String,
    example: '[{"id":"name","desc":false}]',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns paginated products with optional variants and combo items',
    type: PaginatedProductsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: AuthenticatedRequest,
    @Query() queryDto: ProductQueryDto,
  ): Promise<PaginatedProductsResponseDto> {
    return this.productsService.findAll(req.user.activeBusinessId, queryDto);
  }

  @Get('check-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({ summary: 'Check if a product name is available' })
  @ApiQuery({
    name: 'name',
    description: 'Product name to check',
    required: true,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the product name is available',
    type: ProductAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkNameAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('name') name: string,
  ): Promise<{ available: boolean }> {
    return this.productsService.checkNameAvailability(
      req.user.activeBusinessId,
      name,
    );
  }

  @Get('check-sku-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({ summary: 'Check if a product SKU is available' })
  @ApiQuery({
    name: 'sku',
    required: true,
    description: 'The SKU to check for availability',
    example: 'SGS24-128GB',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns SKU availability',
    type: ProductAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkSkuAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('sku') sku: string,
  ): Promise<{ available: boolean }> {
    return this.productsService.checkSkuAvailability(
      req.user.activeBusinessId,
      sku,
    );
  }

  @Get('check-slug-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({ summary: 'Check if a product slug is available' })
  @ApiQuery({
    name: 'slug',
    required: true,
    description: 'The slug to check for availability',
    example: 'samsung-galaxy-s24',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns slug availability',
    type: ProductAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkSlugAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('slug') slug: string,
  ): Promise<{ available: boolean }> {
    return this.productsService.checkSlugAvailability(
      req.user.activeBusinessId,
      slug,
    );
  }

  @Get('check-barcode-availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({ summary: 'Check if a product barcode is available' })
  @ApiQuery({
    name: 'barcode',
    required: true,
    description: 'The barcode to check for availability',
    example: '1234567890123',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns barcode availability',
    type: ProductAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  checkBarcodeAvailability(
    @Request() req: AuthenticatedRequest,
    @Query('barcode') barcode: string,
  ): Promise<{ available: boolean }> {
    return this.productsService.checkBarcodeAvailability(
      req.user.activeBusinessId,
      barcode,
    );
  }

  @Get('slim')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({
    summary: 'Get all products in slim format',
    description:
      'Returns products with minimal information for dropdowns and quick lists',
  })
  @ApiResponse({
    status: 200,
    description: 'All products returned successfully in slim format',
    type: [ProductSlimDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAllSlim(@Request() req: AuthenticatedRequest): Promise<ProductSlimDto[]> {
    return this.productsService.findAllSlim(req.user.activeBusinessId);
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_READ)
  @ApiOperation({
    summary: 'Get a product by ID',
    description:
      'Returns detailed product information including variants and combo items if applicable',
  })
  @ApiParam({
    name: 'id',
    description: 'Product ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the product with complete details',
    type: ProductDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this product',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findOne(
    @Request() _req: AuthenticatedRequest,
    @Param('id') id: string,
  ): Promise<ProductDto> {
    return this.productsService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_UPDATE)
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'images', maxCount: 10 },
      { name: 'ogImage', maxCount: 1 },
    ]),
  )
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Update a product with multiple images and media management',
    description:
      'Updates product information with support for multiple image uploads and media management. You can add new images (with automatic sort order), specify which existing media to keep or remove. New images are appended to existing ones with incremental sort order. Note: Variants and combo items should be managed through separate endpoints.',
  })
  @ApiParam({
    name: 'id',
    description: 'Product ID',
  })
  @ApiBody({
    description:
      'Product update with optional image uploads and media management',
    schema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          example: 'Updated Product Name',
          description: 'Product name',
        },
        description: {
          type: 'string',
          example: 'Updated product description',
          description: 'Product description',
        },
        basePrice: {
          type: 'string',
          example: '1199.99',
          description: 'Updated base price',
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive', 'draft', 'out_of_stock'],
          description: 'Product status',
        },
        keepMediaIds: {
          type: 'array',
          items: { type: 'string' },
          example: [
            '550e8400-e29b-41d4-a716-************',
            '660e8400-e29b-41d4-a716-************',
          ],
          description:
            'Array of existing media IDs to keep (all others will be removed)',
        },
        removeMediaIds: {
          type: 'array',
          items: { type: 'string' },
          example: ['770e8400-e29b-41d4-a716-446655440002'],
          description: 'Array of media IDs to explicitly remove',
        },
        images: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
          description: 'New product image files to add (up to 10 images)',
          maxItems: 10,
        },
        ogImage: {
          type: 'string',
          format: 'binary',
          description: 'Updated Open Graph image file',
        },
        imageUpdates: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              imageId: {
                type: 'string',
                format: 'uuid',
                description: 'ID of the existing image to update',
              },
              sortOrder: {
                type: 'integer',
                minimum: 0,
                description: 'New sort order for the image',
              },
              isPrimary: {
                type: 'boolean',
                description: 'Set as primary image',
              },
              isActive: {
                type: 'boolean',
                description: 'Set image active/inactive status',
              },
            },
            required: ['imageId'],
          },
          description:
            'Updates to existing images (sort order, primary status, etc.)',
          example: [
            {
              imageId: '550e8400-e29b-41d4-a716-************',
              sortOrder: 0,
              isPrimary: true,
            },
            {
              imageId: '660e8400-e29b-41d4-a716-************',
              sortOrder: 1,
              isPrimary: false,
            },
          ],
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description:
      'The product has been successfully updated. Response includes removed media IDs if any media was removed.',
    type: ProductIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or file',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to this product',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  update(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @UploadedFiles()
    files: {
      images?: Express.Multer.File[];
      ogImage?: Express.Multer.File[];
    } = {},
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<ProductIdResponseDto> {
    const images = files?.images || [];
    const ogImage = files?.ogImage?.[0];

    return this.productsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateProductDto,
      images,
      ogImage,
      metadata,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_DELETE)
  @ApiOperation({ summary: 'Bulk delete products' })
  @ApiBody({
    description: 'Array of product IDs to delete',
    type: BulkDeleteProductDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Products have been successfully deleted',
    type: BulkDeleteProductResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or products not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more products not found',
  })
  async bulkDelete(
    @Request() req: AuthenticatedRequest,
    @Body() bulkDeleteProductDto: BulkDeleteProductDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteProductResponseDto> {
    return this.productsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteProductDto.productIds,
      metadata,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_DELETE)
  @ApiOperation({ summary: 'Delete a product' })
  @ApiParam({
    name: 'id',
    description: 'Product ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The product has been successfully deleted',
    type: DeleteProductResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this product',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req: AuthenticatedRequest,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteProductResponseDto> {
    return this.productsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('batch/positions/global')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_UPDATE)
  @ApiOperation({ summary: 'Batch update product global positions' })
  @ApiBody({
    description: 'Array of product global position updates',
    type: UpdateProductGlobalPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Product global positions have been successfully updated',
    type: UpdateProductPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or products not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateGlobalPositions(
    @Request() req: AuthenticatedRequest,
    @Body() updateProductGlobalPositionsDto: UpdateProductGlobalPositionsDto,
    @ActivityMetadata() _metadata: ActivityMetadataType,
  ): Promise<UpdateProductPositionsResponseDto> {
    const result = await this.productsService.updateProductGlobalPositions(
      req.user.id,
      req.user.activeBusinessId,
      updateProductGlobalPositionsDto.updates,
    );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} product global positions`,
    };
  }

  @Patch('batch/positions/category')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_UPDATE)
  @ApiOperation({ summary: 'Batch update product category positions' })
  @ApiBody({
    description: 'Array of product category position updates',
    type: UpdateProductCategoryPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Product category positions have been successfully updated',
    type: UpdateProductPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or products not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateCategoryPositions(
    @Request() req: AuthenticatedRequest,
    @Body()
    updateProductCategoryPositionsDto: UpdateProductCategoryPositionsDto,
    @ActivityMetadata() _metadata: ActivityMetadataType,
  ): Promise<UpdateProductPositionsResponseDto> {
    const result = await this.productsService.updateProductCategoryPositions(
      req.user.id,
      req.user.activeBusinessId,
      updateProductCategoryPositionsDto.updates,
    );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} product category positions`,
    };
  }

  @Patch('batch/positions/sub-category')
  @ApiBearerAuth()
  @RequirePermissions(Permission.PRODUCT_UPDATE)
  @ApiOperation({ summary: 'Batch update product sub-category positions' })
  @ApiBody({
    description: 'Array of product sub-category position updates',
    type: UpdateProductSubCategoryPositionsDto,
  })
  @ApiResponse({
    status: 200,
    description:
      'Product sub-category positions have been successfully updated',
    type: UpdateProductPositionsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or products not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async updateSubCategoryPositions(
    @Request() req: AuthenticatedRequest,
    @Body()
    updateProductSubCategoryPositionsDto: UpdateProductSubCategoryPositionsDto,
    @ActivityMetadata() _metadata: ActivityMetadataType,
  ): Promise<UpdateProductPositionsResponseDto> {
    const result = await this.productsService.updateProductSubCategoryPositions(
      req.user.id,
      req.user.activeBusinessId,
      updateProductSubCategoryPositionsDto.updates,
    );

    return {
      updated: result.updated,
      message: `Successfully updated ${result.updated} product sub-category positions`,
    };
  }

  @Post(':id/images')
  @ApiOperation({
    summary: 'Add images to a product',
    description: 'Add multiple images to a product with sort order',
  })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiResponse({
    status: 201,
    description: 'Images added successfully',
    type: [Object],
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  async addProductImages(
    @Request() req: AuthenticatedRequest,
    @Param('id') productId: string,
    @Body() images: CreateProductImageDto[],
    @ActivityMetadata() _metadata: ActivityMetadataType,
  ) {
    return this.productsService.addProductImages(
      req.user.id,
      req.user.activeBusinessId,
      productId,
      images,
    );
  }

  @Patch(':id/images/sort-order')
  @ApiOperation({
    summary: 'Update product images sort order',
    description: 'Update the sort order of product images',
  })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiResponse({
    status: 200,
    description: 'Sort order updated successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  async updateProductImagesSortOrder(
    @Request() req: AuthenticatedRequest,
    @Param('id') productId: string,
    @Body() updates: UpdateProductImagesSortOrderDto,
    @ActivityMetadata() _metadata: ActivityMetadataType,
  ) {
    return this.productsService.updateProductImagesSortOrder(
      req.user.id,
      req.user.activeBusinessId,
      productId,
      updates,
    );
  }

  @Delete(':id/images/:imageId')
  @ApiOperation({
    summary: 'Remove a product image',
    description: 'Remove a specific image from a product',
  })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiParam({ name: 'imageId', description: 'Image ID to remove' })
  @ApiResponse({
    status: 200,
    description: 'Image removed successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  async removeProductImage(
    @Request() req: AuthenticatedRequest,
    @Param('id') productId: string,
    @Param('imageId') imageId: string,
    @ActivityMetadata() _metadata: ActivityMetadataType,
  ) {
    return this.productsService.removeProductImage(
      req.user.id,
      req.user.activeBusinessId,
      productId,
      imageId,
    );
  }

  @Patch(':id/images/:imageId/primary')
  @ApiOperation({
    summary: 'Set primary image',
    description:
      'Set a specific image as the primary image for a product or variant',
  })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiParam({ name: 'imageId', description: 'Image ID to set as primary' })
  @ApiQuery({
    name: 'variantId',
    required: false,
    description: 'Variant ID (if setting primary for a variant)',
  })
  @ApiResponse({
    status: 200,
    description: 'Primary image set successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found',
  })
  async setProductPrimaryImage(
    @Request() req: AuthenticatedRequest,
    @Param('id') productId: string,
    @Param('imageId') imageId: string,
    @ActivityMetadata() _metadata: ActivityMetadataType,
    @Query('variantId') variantId?: string,
  ) {
    return this.productsService.setProductPrimaryImage(
      req.user.id,
      req.user.activeBusinessId,
      productId,
      imageId,
      variantId,
    );
  }
}
