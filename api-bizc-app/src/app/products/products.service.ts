import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { ComprehensiveCreateProductDto } from './dto/comprehensive-create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductDto } from './dto/product.dto';
import { ProductSlimDto } from './dto/product-slim.dto';
import { ProductQueryDto } from './dto/product-query.dto';
import {
  CreateProductImageDto,
  UpdateProductImagesSortOrderDto,
} from './dto/product-image.dto';
import {
  products,
  productVariants,
  comboProducts,
  productImages,
} from '../drizzle/schema/products.schema';
import {
  productLocations,
  inventory,
} from '../drizzle/schema/inventory.schema';
import { categories } from '../drizzle/schema/categories.schema';
import { brands } from '../drizzle/schema/brands.schema';
import { warrantyTemplates } from '../drizzle/schema/warranties.schema';
import { suppliers } from '../drizzle/schema/suppliers.schema';
import { locations } from '../drizzle/schema/locations.schema';
import {
  eq,
  and,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
  count,
  sum,
  isNull,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ActivitySource,
  ExecutionStrategy,
} from '../shared/types/activity.enum';
import { MediaService } from '../media/media.service';
import { ProductStatus, ProductType } from '../shared/types';
import { MediaReferenceType } from '../drizzle/schema/media.schema';
import { ActivityMetadata } from '../shared/types/activity-metadata.type';

@Injectable()
export class ProductsService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    // private readonly usersService: UsersService, // Not used
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createProductDto: ComprehensiveCreateProductDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; variantIds?: string[]; comboItemIds?: string[] }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      return await this.db.transaction(async (tx) => {
        // Check for duplicate name
        const existingProduct = await tx
          .select()
          .from(products)
          .where(
            and(
              eq(products.businessId, businessId),
              ilike(products.name, createProductDto.name),
              eq(products.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingProduct) {
          throw new ConflictException(
            `A product with the name '${createProductDto.name}' already exists`,
          );
        }

        // Check for duplicate SKU (only for single products)
        if (
          createProductDto.productType === ProductType.SINGLE &&
          createProductDto.sku
        ) {
          const existingSku = await tx
            .select()
            .from(products)
            .where(
              and(
                eq(products.businessId, businessId),
                eq(products.sku, createProductDto.sku),
                eq(products.isDeleted, false),
              ),
            )
            .then((results) => results[0]);

          if (existingSku) {
            throw new ConflictException(
              `A product with SKU '${createProductDto.sku}' already exists`,
            );
          }
        }

        // Generate slug if not provided
        if (!createProductDto.slug) {
          createProductDto.slug = createProductDto.name
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '');
        }

        // Check for duplicate slug
        const existingSlug = await tx
          .select()
          .from(products)
          .where(
            and(
              eq(products.businessId, businessId),
              eq(products.slug, createProductDto.slug),
              eq(products.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingSlug) {
          throw new ConflictException(
            `A product with slug '${createProductDto.slug}' already exists`,
          );
        }

        // Handle image uploads after product creation
        let ogImageId: string | undefined;

        // Create the main product
        const productData = {
          businessId,
          name: createProductDto.name,
          shortCode: createProductDto.shortCode,
          sku: createProductDto.sku, // Only for single products
          barcode: createProductDto.barcode,
          description: createProductDto.description,
          shortDescription: createProductDto.shortDescription,
          slug: createProductDto.slug,
          categoryId: createProductDto.categoryId,
          subCategoryId: createProductDto.subCategoryId,
          brandId: createProductDto.brandId,
          warrantyId: createProductDto.warrantyId,
          preferredSupplierId: createProductDto.preferredSupplierId,
          productType: (createProductDto.productType &&
          [
            ProductType.SINGLE,
            ProductType.COMBO,
            ProductType.INGREDIENT,
            ProductType.MENU_ITEM,
            ProductType.RAW_MATERIAL,
            ProductType.WORK_IN_PROGRESS,
            ProductType.FINISHED_GOOD,
          ].includes(createProductDto.productType)
            ? createProductDto.productType
            : ProductType.SINGLE) as
            | ProductType.SINGLE
            | ProductType.COMBO
            | ProductType.INGREDIENT
            | ProductType.MENU_ITEM
            | ProductType.RAW_MATERIAL
            | ProductType.WORK_IN_PROGRESS
            | ProductType.FINISHED_GOOD,
          isSellable: createProductDto.isSellable ?? true,
          isIngredient: createProductDto.isIngredient ?? false,
          isManufacturable: createProductDto.isManufacturable ?? false,
          isPurchasable: createProductDto.isPurchasable ?? true,
          basePrice: createProductDto.basePrice,
          standardCost: createProductDto.standardCost,
          lastPurchasePrice: createProductDto.lastPurchasePrice,
          taxType: createProductDto.taxType,
          defaultTaxRateId: createProductDto.defaultTaxRateId,
          incomeAccountId: createProductDto.incomeAccountId,
          expenseAccountId: createProductDto.expenseAccountId,
          inventoryAccountId: createProductDto.inventoryAccountId,
          manageInventory: createProductDto.manageInventory ?? true,
          trackingType: createProductDto.trackingType,
          trackSerialNumbers: createProductDto.trackSerialNumbers ?? false,
          standardUnitOfMeasure: createProductDto.standardUnitOfMeasure,
          customUnitId: createProductDto.customUnitId,
          leadTimeDays: createProductDto.leadTimeDays,
          minStockLevel: createProductDto.minStockLevel,
          maxStockLevel: createProductDto.maxStockLevel,
          reorderPoint: createProductDto.reorderPoint,
          expiryPeriod: createProductDto.expiryPeriod,
          expiryPeriodType: createProductDto.expiryPeriodType,
          weight: createProductDto.weight,
          calories: createProductDto.calories,
          allergens: createProductDto.allergens,
          availableOnline: createProductDto.availableOnline ?? false,
          featured: createProductDto.featured ?? false,
          categoryPosition: createProductDto.categoryPosition ?? 0,
          subCategoryPosition: createProductDto.subCategoryPosition ?? 0,
          globalPosition: createProductDto.globalPosition ?? 0,
          isAllocatedToAllLocations:
            createProductDto.isAllocatedToAllLocations ?? false,
          seoTitle: createProductDto.seoTitle,
          seoDescription: createProductDto.seoDescription,
          seoKeywords: createProductDto.seoKeywords,
          ogImage: ogImageId,
          tags: createProductDto.tags,
          model: createProductDto.model,
          createdBy: userId,
          status:
            createProductDto.status &&
            createProductDto.status !== ProductStatus.DELETED
              ? createProductDto.status
              : ProductStatus.ACTIVE,
        };

        const [newProduct] = await tx
          .insert(products)
          .values(productData)
          .returning({ id: products.id });

        const productId = newProduct.id;
        const variantIds: string[] = [];
        const comboItemIds: string[] = [];

        // Handle multiple image uploads with product reference
        if (imageFiles && imageFiles.length > 0) {
          for (let i = 0; i < imageFiles.length; i++) {
            const imageFile = imageFiles[i];
            const mediaResult =
              await this.mediaService.uploadMediaWithReference(
                imageFile,
                MediaReferenceType.PRODUCTS,
                businessId,
                userId,
                productId,
              );

            // Add image to productImages table with sort order
            await tx.insert(productImages).values({
              businessId,
              productId,
              variantId: null,
              imageId: mediaResult.id,
              sortOrder: i,
              isPrimary: i === 0, // First image is primary
              isActive: true,
              createdBy: userId,
              updatedBy: userId,
            });
          }
        }

        if (ogImageFile) {
          const ogMediaResult =
            await this.mediaService.uploadMediaWithReference(
              ogImageFile,
              MediaReferenceType.PRODUCTS,
              businessId,
              userId,
              productId,
            );
          ogImageId = ogMediaResult.id;
        }

        // Handle product variants (for products with variants)
        if (createProductDto.variants && createProductDto.variants.length > 0) {
          for (const variant of createProductDto.variants) {
            // Check for duplicate variant SKU
            const existingVariantSku = await tx
              .select()
              .from(productVariants)
              .where(
                and(
                  eq(productVariants.businessId, businessId),
                  eq(productVariants.sku, variant.sku),
                  eq(productVariants.isDeleted, false),
                ),
              )
              .then((results) => results[0]);

            if (existingVariantSku) {
              throw new ConflictException(
                `A variant with SKU '${variant.sku}' already exists`,
              );
            }

            const [newVariant] = await tx
              .insert(productVariants)
              .values({
                productId,
                businessId,
                name: variant.name,
                sku: variant.sku,
                barcode: variant.barcode,
                price: variant.price,
                weight: variant.weight,
                variationTemplateId: variant.variationTemplateId,
                variationValueTemplateId: variant.variationValueTemplateId,
                image: variant.image,
                createdBy: userId,
                // status: variant.status ?? ProductStatus.ACTIVE, // status property doesn't exist on variant DTO
              })
              .returning({ id: productVariants.id });

            variantIds.push(newVariant.id);
          }
        }

        // Handle combo product items (for COMBO products)
        if (
          createProductDto.productType === ProductType.COMBO &&
          createProductDto.comboItems &&
          createProductDto.comboItems.length > 0
        ) {
          for (const comboItem of createProductDto.comboItems) {
            const [newComboItem] = await tx
              .insert(comboProducts)
              .values({
                businessId,
                parentProductId: productId,
                childProductId: comboItem.childProductId,
                childVariantId: comboItem.childVariantId,
                quantity: comboItem.quantity,
                isMandatory: comboItem.isMandatory ?? true,
                createdBy: userId,
                createdAt: new Date(),
                updatedAt: new Date(),
              })
              .returning({ id: comboProducts.id });

            comboItemIds.push(newComboItem.id);
          }
        }

        // Handle location allocation
        if (createProductDto.isAllocatedToAllLocations) {
          // Get all business locations
          const businessLocations = await tx
            .select({ id: locations.id })
            .from(locations)
            .where(
              and(
                eq(locations.businessId, businessId),
                eq(locations.isDeleted, false),
              ),
            );

          for (const location of businessLocations) {
            await tx.insert(productLocations).values({
              businessId,
              productId,
              locationId: location.id,
              createdBy: userId,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          }
        } else if (createProductDto.locationIds?.length) {
          for (const locationId of createProductDto.locationIds) {
            await tx.insert(productLocations).values({
              businessId,
              productId,
              locationId,
              createdBy: userId,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          }
        }

        // Log activity
        await this.activityLogService.logCreate(
          productId,
          EntityType.PRODUCT,
          userId,
          businessId,
          {
            source: metadata?.source || ActivitySource.WEB,
            ipAddress: metadata?.ipAddress,
            userAgent: metadata?.userAgent,
            sessionId: metadata?.sessionId,
          },
        );

        return {
          id: productId,
          ...(variantIds.length > 0 && { variantIds }),
          ...(comboItemIds.length > 0 && { comboItemIds }),
        };
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create product: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createProductDto: ComprehensiveCreateProductDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ) {
    const result = await this.create(
      userId,
      businessId,
      createProductDto,
      imageFiles,
      ogImageFile,
      metadata,
    );

    return {
      id: result.id,
      message: 'Product created successfully',
      variantIds: result.variantIds,
      comboItemIds: result.comboItemIds,
    };
  }

  async findAll(businessId: string | null, queryDto: ProductQueryDto) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const {
      page = 1,
      limit = 20,
      search,
      name,
      sku,
      barcode,
      shortCode,
      slug,
      productType,
      status,
      categoryId,
      subCategoryId,
      brandId,
      supplierId,
      availableOnline,
      featured,
      isSellable,
      isPurchasable,
      isManufacturable,
      isIngredient,
      trackingType,
      minPrice,
      maxPrice,
      from,
      to,
      includeVariants = false,
      includeComboItems = false,
      includeInventory = false,
      filters,
      joinOperator = 'and',
      sort,
    } = queryDto;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(products.businessId, businessId),
      eq(products.isDeleted, false),
    ];

    // Basic filters
    if (search) {
      whereConditions.push(
        or(
          ilike(products.name, `%${search}%`),
          ilike(products.sku, `%${search}%`),
          ilike(products.barcode, `%${search}%`),
          ilike(products.shortCode, `%${search}%`),
        ),
      );
    }

    if (name) {
      whereConditions.push(ilike(products.name, `%${name}%`));
    }

    if (sku) {
      whereConditions.push(ilike(products.sku, `%${sku}%`));
    }

    if (barcode) {
      whereConditions.push(eq(products.barcode, barcode));
    }

    if (shortCode) {
      whereConditions.push(eq(products.shortCode, shortCode));
    }

    if (slug) {
      whereConditions.push(eq(products.slug, slug));
    }

    if (productType) {
      whereConditions.push(eq(products.productType, productType as any));
    }

    if (status) {
      const statusArray = status.split(',') as any[];
      whereConditions.push(inArray(products.status, statusArray));
    }

    if (categoryId) {
      whereConditions.push(eq(products.categoryId, categoryId));
    }

    if (subCategoryId) {
      whereConditions.push(eq(products.subCategoryId, subCategoryId));
    }

    if (brandId) {
      whereConditions.push(eq(products.brandId, brandId));
    }

    if (supplierId) {
      whereConditions.push(eq(products.preferredSupplierId, supplierId));
    }

    if (availableOnline !== undefined) {
      const onlineArray = availableOnline.split(',').map((v) => v === 'true');
      whereConditions.push(inArray(products.availableOnline, onlineArray));
    }

    if (featured !== undefined) {
      const featuredArray = featured.split(',').map((v) => v === 'true');
      whereConditions.push(inArray(products.featured, featuredArray));
    }

    if (isSellable !== undefined) {
      const sellableArray = isSellable.split(',').map((v) => v === 'true');
      whereConditions.push(inArray(products.isSellable, sellableArray));
    }

    if (isPurchasable !== undefined) {
      const purchasableArray = isPurchasable
        .split(',')
        .map((v) => v === 'true');
      whereConditions.push(inArray(products.isPurchasable, purchasableArray));
    }

    if (isManufacturable !== undefined) {
      const manufacturableArray = isManufacturable
        .split(',')
        .map((v) => v === 'true');
      whereConditions.push(
        inArray(products.isManufacturable, manufacturableArray),
      );
    }

    if (isIngredient !== undefined) {
      const ingredientArray = isIngredient.split(',').map((v) => v === 'true');
      whereConditions.push(inArray(products.isIngredient, ingredientArray));
    }

    if (trackingType) {
      whereConditions.push(eq(products.trackingType, trackingType));
    }

    if (minPrice) {
      whereConditions.push(gte(products.basePrice, minPrice));
    }

    if (maxPrice) {
      whereConditions.push(lte(products.basePrice, maxPrice));
    }

    if (from) {
      whereConditions.push(gte(products.createdAt, new Date(from)));
    }

    if (to) {
      whereConditions.push(lte(products.createdAt, new Date(to)));
    }

    // Handle advanced filters
    if (filters) {
      try {
        const filterArray = JSON.parse(filters);
        const filterConditions = filterArray
          .map((filter: any) => {
            const field = products[filter.id as keyof typeof products];
            if (!field) return null;

            switch (filter.operator) {
              case 'eq':
                return eq(field as any, filter.value);
              case 'ne':
                return sql`${field} != ${filter.value}`;
              case 'iLike':
                return ilike(field as any, `%${filter.value}%`);
              case 'notILike':
                return sql`${field} NOT ILIKE ${'%' + filter.value + '%'}`;
              case 'isEmpty':
                return sql`${field} IS NULL OR ${field} = ''`;
              case 'isNotEmpty':
                return sql`${field} IS NOT NULL AND ${field} != ''`;
              default:
                return null;
            }
          })
          .filter(Boolean);

        if (filterConditions.length > 0) {
          const combinedFilters =
            joinOperator === 'or'
              ? or(...filterConditions)
              : and(...filterConditions);
          whereConditions.push(combinedFilters);
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Handle sorting
    let orderBy = [desc(products.createdAt)]; // Default sort
    if (sort) {
      try {
        const sortArray = JSON.parse(sort);
        orderBy = sortArray.map((sortItem: any) => {
          const field = products[sortItem.id as keyof typeof products];
          return sortItem.desc ? desc(field as any) : asc(field as any);
        });
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Build the query
    const baseQuery = this.db
      .select({
        id: products.id,
        businessId: products.businessId,
        name: products.name,
        shortCode: products.shortCode,
        sku: products.sku,
        barcode: products.barcode,
        description: products.description,
        shortDescription: products.shortDescription,
        slug: products.slug,
        categoryId: products.categoryId,
        subCategoryId: products.subCategoryId,
        brandId: products.brandId,
        warrantyId: products.warrantyId,
        preferredSupplierId: products.preferredSupplierId,
        productType: products.productType,
        isSellable: products.isSellable,
        isIngredient: products.isIngredient,
        isManufacturable: products.isManufacturable,
        isPurchasable: products.isPurchasable,
        basePrice: products.basePrice,
        standardCost: products.standardCost,
        lastPurchasePrice: products.lastPurchasePrice,
        allowCustomerGroupPricing: products.allowCustomerGroupPricing,
        taxType: products.taxType,
        defaultTaxRateId: products.defaultTaxRateId,
        incomeAccountId: products.incomeAccountId,
        expenseAccountId: products.expenseAccountId,
        inventoryAccountId: products.inventoryAccountId,
        manageInventory: products.manageInventory,
        trackingType: products.trackingType,
        trackSerialNumbers: products.trackSerialNumbers,
        standardUnitOfMeasure: products.standardUnitOfMeasure,
        customUnitId: products.customUnitId,
        leadTimeDays: products.leadTimeDays,
        minStockLevel: products.minStockLevel,
        maxStockLevel: products.maxStockLevel,
        reorderPoint: products.reorderPoint,
        expiryPeriod: products.expiryPeriod,
        expiryPeriodType: products.expiryPeriodType,
        weight: products.weight,
        availableOnline: products.availableOnline,
        featured: products.featured,
        categoryPosition: products.categoryPosition,
        subCategoryPosition: products.subCategoryPosition,
        globalPosition: products.globalPosition,
        isAllocatedToAllLocations: products.isAllocatedToAllLocations,
        seoTitle: products.seoTitle,
        seoDescription: products.seoDescription,
        seoKeywords: products.seoKeywords,
        ogImage: products.ogImage,
        tags: products.tags,
        model: products.model,
        createdBy: products.createdBy,
        updatedBy: products.updatedBy,
        status: products.status,
        createdAt: products.createdAt,
        updatedAt: products.updatedAt,
        // Relations
        categoryName: categories.name,
        categorySlug: categories.slug,
        subCategoryName: sql<string>`sub_cat.name`,
        subCategorySlug: sql<string>`sub_cat.slug`,
        brandName: brands.name,
        brandSlug: brands.slug,
        warrantyName: warrantyTemplates.templateName,
        warrantyDuration: warrantyTemplates.duration,
        supplierName: suppliers.displayName,
        supplierContactPerson: suppliers.companyName,
      })
      .from(products)
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .leftJoin(
        sql`${categories} AS sub_cat`,
        eq(products.subCategoryId, sql`sub_cat.id`),
      )
      .leftJoin(brands, eq(products.brandId, brands.id))
      .leftJoin(
        warrantyTemplates,
        eq(products.warrantyId, warrantyTemplates.id),
      )
      .leftJoin(suppliers, eq(products.preferredSupplierId, suppliers.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy);

    // Get total count
    const [totalResult] = await this.db
      .select({ count: count() })
      .from(products)
      .where(and(...whereConditions));

    const total = totalResult.count;

    // Get paginated results
    const results = await baseQuery.limit(limit).offset(offset);

    // Transform results and add additional data if requested
    const transformedResults = await Promise.all(
      results.map(async (product) => {
        const productDto: ProductDto = {
          id: product.id,
          businessId: product.businessId,
          name: product.name,
          shortCode: product.shortCode,
          sku: product.sku,
          barcode: product.barcode,
          description: product.description,
          shortDescription: product.shortDescription,
          slug: product.slug,
          categoryId: product.categoryId,
          subCategoryId: product.subCategoryId,
          brandId: product.brandId,
          warrantyId: product.warrantyId,
          preferredSupplierId: product.preferredSupplierId,
          productType: product.productType,
          isSellable: product.isSellable,
          isIngredient: product.isIngredient,
          isManufacturable: product.isManufacturable,
          isPurchasable: product.isPurchasable,
          basePrice: product.basePrice,
          standardCost: product.standardCost,
          lastPurchasePrice: product.lastPurchasePrice,
          allowCustomerGroupPricing: product.allowCustomerGroupPricing,
          taxType: product.taxType,
          defaultTaxRateId: product.defaultTaxRateId,
          incomeAccountId: product.incomeAccountId,
          expenseAccountId: product.expenseAccountId,
          inventoryAccountId: product.inventoryAccountId,
          manageInventory: product.manageInventory,
          trackingType: product.trackingType,
          trackSerialNumbers: product.trackSerialNumbers,
          standardUnitOfMeasure: product.standardUnitOfMeasure,
          customUnitId: product.customUnitId,
          leadTimeDays: product.leadTimeDays,
          minStockLevel: product.minStockLevel,
          maxStockLevel: product.maxStockLevel,
          reorderPoint: product.reorderPoint,
          expiryPeriod: product.expiryPeriod,
          expiryPeriodType: product.expiryPeriodType,
          weight: product.weight,
          availableOnline: product.availableOnline,
          featured: product.featured,
          categoryPosition: product.categoryPosition,
          subCategoryPosition: product.subCategoryPosition,
          globalPosition: product.globalPosition,
          isAllocatedToAllLocations: product.isAllocatedToAllLocations,
          seoTitle: product.seoTitle,
          seoDescription: product.seoDescription,
          seoKeywords: product.seoKeywords,
          tags: product.tags,
          model: product.model,
          createdBy: product.createdBy,
          updatedBy: product.updatedBy,
          status: product.status,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        };

        // Add category info
        if (product.categoryName) {
          productDto.category = {
            id: product.categoryId,
            name: product.categoryName,
            slug: product.categorySlug,
          };
        }

        // Add sub-category info
        if (product.subCategoryName) {
          productDto.subCategory = {
            id: product.subCategoryId,
            name: product.subCategoryName,
            slug: product.subCategorySlug,
          };
        }

        // Add brand info
        if (product.brandName) {
          productDto.brand = {
            id: product.brandId,
            name: product.brandName,
            slug: product.brandSlug,
          };
        }

        // Add warranty info
        if (product.warrantyName) {
          productDto.warranty = {
            id: product.warrantyId,
            name: product.warrantyName,
            durationMonths: product.warrantyDuration,
          };
        }

        // Add supplier info
        if (product.supplierName) {
          productDto.preferredSupplier = {
            id: product.preferredSupplierId,
            name: product.supplierName,
            contactPerson: product.supplierContactPerson,
          };
        }

        // Add variants if requested
        if (includeVariants) {
          const variants = await this.db
            .select()
            .from(productVariants)
            .where(
              and(
                eq(productVariants.productId, product.id),
                eq(productVariants.isDeleted, false),
              ),
            );

          productDto.variants = variants;
        }

        // Add combo items if requested
        if (includeComboItems && product.productType === ProductType.COMBO) {
          const comboItems = await this.db
            .select({
              id: comboProducts.id,
              parentProductId: comboProducts.parentProductId,
              childProductId: comboProducts.childProductId,
              childVariantId: comboProducts.childVariantId,
              quantity: comboProducts.quantity,
              isMandatory: comboProducts.isMandatory,
              createdAt: comboProducts.createdAt,
              updatedAt: comboProducts.updatedAt,
              childProductName: products.name,
              childProductSku: products.sku,
              childProductBasePrice: products.basePrice,
            })
            .from(comboProducts)
            .leftJoin(products, eq(comboProducts.childProductId, products.id))
            .where(eq(comboProducts.parentProductId, product.id));

          productDto.comboItems = comboItems.map((item) => ({
            id: item.id,
            parentProductId: item.parentProductId,
            childProductId: item.childProductId,
            childVariantId: item.childVariantId,
            quantity: item.quantity,
            isMandatory: item.isMandatory,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            childProduct: {
              id: item.childProductId,
              name: item.childProductName,
              sku: item.childProductSku,
              basePrice: item.childProductBasePrice,
            },
          }));
        }

        // Add inventory if requested
        if (includeInventory) {
          const [inventoryResult] = await this.db
            .select({
              totalOnHand: sum(inventory.quantityOnHand),
              totalReserved: sum(inventory.quantityReserved),
              totalAvailable: sum(inventory.quantityAvailable),
            })
            .from(inventory)
            .where(eq(inventory.productId, product.id));

          if (inventoryResult) {
            productDto.totalInventory = {
              quantityOnHand: inventoryResult.totalOnHand || '0.000',
              quantityReserved: inventoryResult.totalReserved || '0.000',
              quantityAvailable: inventoryResult.totalAvailable || '0.000',
            };
          }
        }

        // Add variant count for products with variants
        const [variantCountResult] = await this.db
          .select({ count: count() })
          .from(productVariants)
          .where(
            and(
              eq(productVariants.productId, product.id),
              eq(productVariants.isDeleted, false),
            ),
          );
        productDto.variantCount = variantCountResult.count;

        return productDto;
      }),
    );

    const totalPages = Math.ceil(total / limit);

    return {
      data: transformedResults,
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    };
  }

  async findOne(id: string): Promise<ProductDto> {
    const product = await this.db
      .select()
      .from(products)
      .where(and(eq(products.id, id), eq(products.isDeleted, false)))
      .then((results) => results[0]);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    // Verify user has access to this product's business
    // await this.usersService.verifyBusinessAccess(userId, product.businessId); // Method doesn't exist

    // Transform and return with relations
    const productDto: ProductDto = {
      ...product,
    };

    // Add variants if product has variants
    const variants = await this.db
      .select()
      .from(productVariants)
      .where(
        and(
          eq(productVariants.productId, product.id),
          eq(productVariants.isDeleted, false),
        ),
      );
    if (variants.length > 0) {
      productDto.variants = variants;
    }

    // Add combo items if combo product
    if (product.productType === ProductType.COMBO) {
      const comboItems = await this.db
        .select()
        .from(comboProducts)
        .where(eq(comboProducts.parentProductId, product.id));
      productDto.comboItems = comboItems;
    }

    return productDto;
  }

  async findAllSlim(businessId: string | null): Promise<ProductSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const results = await this.db
      .select({
        id: products.id,
        name: products.name,
        shortCode: products.shortCode,
        sku: products.sku,
        barcode: products.barcode,
        slug: products.slug,
        productType: products.productType,
        basePrice: products.basePrice,
        availableOnline: products.availableOnline,
        featured: products.featured,
        status: products.status,
        categoryName: categories.name,
        brandName: brands.name,
      })
      .from(products)
      .leftJoin(categories, eq(products.categoryId, categories.id))
      .leftJoin(brands, eq(products.brandId, brands.id))
      .where(
        and(
          eq(products.businessId, businessId),
          eq(products.isDeleted, false),
          eq(products.status, ProductStatus.ACTIVE),
        ),
      )
      .orderBy(products.globalPosition, products.name);

    return results.map((product) => ({
      id: product.id,
      name: product.name,
      shortCode: product.shortCode,
      sku: product.sku,
      barcode: product.barcode,
      slug: product.slug,
      productType: product.productType,
      basePrice: product.basePrice,
      availableOnline: product.availableOnline,
      featured: product.featured,
      status: product.status,
      category: product.categoryName
        ? { id: product.id, name: product.categoryName }
        : undefined,
      brand: product.brandName
        ? { id: product.id, name: product.brandName }
        : undefined,
    }));
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateProductDto: UpdateProductDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    return await this.db.transaction(async (tx) => {
      const existingProduct = await tx
        .select()
        .from(products)
        .where(
          and(
            eq(products.id, id),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProduct) {
        throw new NotFoundException('Product not found');
      }

      // Track removed media IDs
      const removedMediaIds: string[] = [];

      // Handle media removal if specified
      if (
        updateProductDto.removeImageIds &&
        updateProductDto.removeImageIds.length > 0
      ) {
        // Get existing media to validate removal requests
        const existingMedia = await this.mediaService.findByReferenceId(
          id,
          businessId,
        );
        const existingMediaIds = existingMedia.map((m) => m.id);

        // Filter to only remove media that actually exists
        const validRemoveIds = updateProductDto.removeImageIds.filter(
          (mediaId) => existingMediaIds.includes(mediaId),
        );

        // Remove specified media
        for (const mediaId of validRemoveIds) {
          try {
            await this.mediaService.deleteMedia(
              mediaId,
              businessId,
              MediaReferenceType.PRODUCTS,
            );
            removedMediaIds.push(mediaId);
          } catch (error) {
            console.warn(`Failed to remove media ${mediaId}:`, error.message);
          }
        }
      }

      // Handle keepImageIds logic - remove all media except those specified to keep
      if (updateProductDto.keepImageIds !== undefined) {
        const existingMedia = await this.mediaService.findByReferenceId(
          id,
          businessId,
        );
        const mediaToRemove = existingMedia.filter(
          (media) => !updateProductDto.keepImageIds?.includes(media.id),
        );

        for (const media of mediaToRemove) {
          try {
            await this.mediaService.deleteMedia(
              media.id,
              businessId,
              MediaReferenceType.PRODUCTS,
            );
            removedMediaIds.push(media.id);
          } catch (error) {
            console.warn(`Failed to remove media ${media.id}:`, error.message);
          }
        }
      }

      // Handle multiple image uploads
      let ogImageId: string | undefined;

      if (imageFiles && imageFiles.length > 0) {
        // Get current max sort order for this product
        const existingImages = await tx
          .select({ sortOrder: productImages.sortOrder })
          .from(productImages)
          .where(
            and(
              eq(productImages.productId, id),
              eq(productImages.variantId, null),
            ),
          )
          .orderBy(desc(productImages.sortOrder));

        const maxSortOrder =
          existingImages.length > 0 ? existingImages[0].sortOrder : -1;

        // Upload new images with incremental sort order
        for (let i = 0; i < imageFiles.length; i++) {
          const imageFile = imageFiles[i];
          const mediaResult = await this.mediaService.uploadMediaWithReference(
            imageFile,
            MediaReferenceType.PRODUCTS,
            businessId,
            userId,
            id,
          );

          // Add image to productImages table with incremental sort order
          await tx.insert(productImages).values({
            businessId,
            productId: id,
            variantId: null,
            imageId: mediaResult.id,
            sortOrder: maxSortOrder + 1 + i,
            isPrimary: false, // New images are not primary by default
            isActive: true,
            createdBy: userId,
            updatedBy: userId,
          });
        }
      }

      if (ogImageFile) {
        const ogMediaResult = await this.mediaService.uploadMediaWithReference(
          ogImageFile,
          MediaReferenceType.PRODUCTS,
          businessId,
          userId,
          id,
        );
        ogImageId = ogMediaResult.id;
      }

      // Handle existing image updates (sort order, primary status, etc.)
      if (
        updateProductDto.imageUpdates &&
        updateProductDto.imageUpdates.length > 0
      ) {
        for (const imageUpdate of updateProductDto.imageUpdates) {
          // If setting as primary, first unset all other primary images for this product
          if (imageUpdate.isPrimary) {
            await tx
              .update(productImages)
              .set({
                isPrimary: false,
                updatedAt: new Date(),
                updatedBy: userId,
              })
              .where(
                and(
                  eq(productImages.productId, id),
                  eq(productImages.variantId, null),
                ),
              );
          }

          // Update the specific image
          const updateData: any = {
            updatedAt: new Date(),
            updatedBy: userId,
          };

          if (imageUpdate.sortOrder !== undefined) {
            updateData.sortOrder = imageUpdate.sortOrder;
          }
          if (imageUpdate.isPrimary !== undefined) {
            updateData.isPrimary = imageUpdate.isPrimary;
          }
          if (imageUpdate.isActive !== undefined) {
            updateData.isActive = imageUpdate.isActive;
          }

          await tx
            .update(productImages)
            .set(updateData)
            .where(
              and(
                eq(productImages.imageId, imageUpdate.imageId),
                eq(productImages.productId, id),
              ),
            );
        }
      }

      // Create update data without media-related fields
      // Note: keepImageIds, removeImageIds, imageUpdates handled separately above
      const {
        keepImageIds,
        removeImageIds,
        imageUpdates,
        ...productUpdateData
      } = updateProductDto;

      // Mark as used for linter
      void keepImageIds;
      void removeImageIds;
      void imageUpdates;

      // Update the product
      await tx
        .update(products)
        .set({
          ...productUpdateData,
          ...(ogImageId && { ogImage: ogImageId }),
          updatedBy: userId,
          updatedAt: new Date(),
        } as any)
        .where(eq(products.id, id));

      // Log activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Product updated successfully',
        ...(removedMediaIds.length > 0 && { removedMediaIds }),
      };
    });
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateProductDto: UpdateProductDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
    metadata?: ActivityMetadata,
  ) {
    return await this.update(
      userId,
      businessId,
      id,
      updateProductDto,
      imageFiles,
      ogImageFile,
      metadata,
    );
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    return await this.db.transaction(async (tx) => {
      const existingProduct = await tx
        .select()
        .from(products)
        .where(
          and(
            eq(products.id, id),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingProduct) {
        throw new NotFoundException('Product not found');
      }

      // Soft delete the product
      await tx
        .update(products)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(products.id, id));

      // Soft delete related variants
      await tx
        .update(productVariants)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(productVariants.productId, id));

      // Log activity
      await this.activityLogService.logDelete(
        id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id,
        message: 'Product deleted successfully',
      };
    });
  }

  // Availability check methods
  async checkNameAvailability(
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const existing = await this.db
      .select()
      .from(products)
      .where(
        and(
          eq(products.businessId, businessId),
          ilike(products.name, name),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existing };
  }

  async checkSkuAvailability(
    businessId: string | null,
    sku: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const existing = await this.db
      .select()
      .from(products)
      .where(
        and(
          eq(products.businessId, businessId),
          eq(products.sku, sku),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existing };
  }

  async checkSlugAvailability(
    businessId: string | null,
    slug: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const existing = await this.db
      .select()
      .from(products)
      .where(
        and(
          eq(products.businessId, businessId),
          eq(products.slug, slug),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existing };
  }

  async checkBarcodeAvailability(
    businessId: string | null,
    barcode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const existing = await this.db
      .select()
      .from(products)
      .where(
        and(
          eq(products.businessId, businessId),
          eq(products.barcode, barcode),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    return { available: !existing };
  }

  // Bulk operations
  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createProductsDto: ComprehensiveCreateProductDto[],
    images?: Express.Multer.File[],
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const results = [];
    let totalVariants = 0;
    let totalComboItems = 0;
    const failures: any[] = [];

    for (let i = 0; i < createProductsDto.length; i++) {
      try {
        const productDto = createProductsDto[i];
        // For bulk operations, images are mapped by index
        const imageFiles = images?.[i] ? [images[i]] : undefined;
        const ogImageFile = undefined; // OG images not supported in bulk operations

        const result = await this.create(
          userId,
          businessId,
          productDto,
          imageFiles,
          ogImageFile,
        );

        results.push({
          productId: result.id,
          variantIds: result.variantIds,
          comboItemIds: result.comboItemIds,
          success: true,
        });

        totalVariants += result.variantIds?.length || 0;
        totalComboItems += result.comboItemIds?.length || 0;
      } catch (error) {
        failures.push({
          productName: createProductsDto[i].name,
          productType: createProductsDto[i].productType,
          error: error.message,
          index: i,
        });
        results.push({
          productId: null,
          success: false,
          error: error.message,
        });
      }
    }

    return {
      results,
      message: `Processed ${createProductsDto.length} products`,
      totalProcessed: createProductsDto.length,
      successful: results.filter((r) => r.success).length,
      failed: failures.length,
      totalVariantsCreated: totalVariants,
      totalComboItemsCreated: totalComboItems,
      ...(failures.length > 0 && { failures }),
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    productIds: string[],
    metadata?: ActivityMetadata,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const deletedIds = [];
    const failed = [];

    for (const productId of productIds) {
      try {
        await this.remove(userId, businessId, productId, metadata);
        deletedIds.push(productId);
      } catch (error) {
        failed.push({
          id: productId,
          reason: error.message,
        });
      }
    }

    // Log bulk delete operation if any products were deleted
    if (deletedIds.length > 0) {
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.PRODUCT,
        deletedIds,
        { isDeleted: true, updatedBy: userId },
        userId,
        businessId,
        {
          filterCriteria: { productIds },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );
    }

    return {
      deleted: deletedIds.length,
      message: `Successfully deleted ${deletedIds.length} products`,
      deletedIds,
      ...(failed.length > 0 && { failed }),
    };
  }

  /**
   * Add images to a product or variant
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param productId - The product ID
   * @param images - Array of image DTOs to add
   */
  async addProductImages(
    userId: string,
    businessId: string | null,
    productId: string,
    images: CreateProductImageDto[],
  ): Promise<{ id: string }[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate product exists and belongs to business
    const product = await this.db
      .select({ id: products.id })
      .from(products)
      .where(
        and(
          eq(products.id, productId),
          eq(products.businessId, businessId),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return await this.db.transaction(async (tx) => {
      const results = [];

      for (const image of images) {
        // If this is marked as primary, unset other primary images for the same product/variant
        if (image.isPrimary) {
          await tx
            .update(productImages)
            .set({ isPrimary: false, updatedAt: new Date(), updatedBy: userId })
            .where(
              and(
                eq(productImages.productId, productId),
                image.variantId
                  ? eq(productImages.variantId, image.variantId)
                  : isNull(productImages.variantId),
              ),
            );
        }

        const newImage = await tx
          .insert(productImages)
          .values({
            businessId,
            productId,
            variantId: image.variantId || null,
            imageId: image.imageId,
            sortOrder: image.sortOrder || 0,
            isPrimary: image.isPrimary || false,
            isActive: image.isActive ?? true,
            createdBy: userId,
            updatedBy: userId,
          })
          .returning({ id: productImages.id });

        results.push(newImage[0]);
      }

      return results;
    });
  }

  /**
   * Update product images sort order
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param productId - The product ID
   * @param updates - Sort order updates
   */
  async updateProductImagesSortOrder(
    userId: string,
    businessId: string | null,
    productId: string,
    updates: UpdateProductImagesSortOrderDto,
  ): Promise<{ updated: number }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate product exists and belongs to business
    const product = await this.db
      .select({ id: products.id })
      .from(products)
      .where(
        and(
          eq(products.id, productId),
          eq(products.businessId, businessId),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return await this.db.transaction(async (tx) => {
      let updatedCount = 0;

      for (const update of updates.images) {
        await tx
          .update(productImages)
          .set({
            sortOrder: update.sortOrder,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(
            and(
              eq(productImages.id, update.id),
              eq(productImages.productId, productId),
            ),
          );

        updatedCount++;
      }

      return { updated: updatedCount };
    });
  }

  /**
   * Remove a product image
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param productId - The product ID
   * @param imageId - The image ID to remove
   */
  async removeProductImage(
    _userId: string,
    businessId: string | null,
    productId: string,
    imageId: string,
  ): Promise<{ deleted: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate product exists and belongs to business
    const product = await this.db
      .select({ id: products.id })
      .from(products)
      .where(
        and(
          eq(products.id, productId),
          eq(products.businessId, businessId),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    await this.db
      .delete(productImages)
      .where(
        and(
          eq(productImages.id, imageId),
          eq(productImages.productId, productId),
        ),
      );

    return { deleted: true };
  }

  /**
   * Set primary image for a product or variant
   * @param userId - The user ID performing the action
   * @param businessId - The business ID
   * @param productId - The product ID
   * @param imageId - The image ID to set as primary
   * @param variantId - Optional variant ID
   */
  async setProductPrimaryImage(
    userId: string,
    businessId: string | null,
    productId: string,
    imageId: string,
    variantId?: string,
  ): Promise<{ updated: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Validate product exists and belongs to business
    const product = await this.db
      .select({ id: products.id })
      .from(products)
      .where(
        and(
          eq(products.id, productId),
          eq(products.businessId, businessId),
          eq(products.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return await this.db.transaction(async (tx) => {
      // First, unset all primary images for this product/variant
      await tx
        .update(productImages)
        .set({ isPrimary: false, updatedAt: new Date(), updatedBy: userId })
        .where(
          and(
            eq(productImages.productId, productId),
            variantId
              ? eq(productImages.variantId, variantId)
              : eq(productImages.variantId, null),
          ),
        );

      // Then set the specified image as primary
      await tx
        .update(productImages)
        .set({ isPrimary: true, updatedAt: new Date(), updatedBy: userId })
        .where(
          and(
            eq(productImages.id, imageId),
            eq(productImages.productId, productId),
          ),
        );

      return { updated: true };
    });
  }

  /**
   * Update global positions for products
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateProductGlobalPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for product ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all products exist and belong to the business
      const productIds = updates.map((update) => update.id);
      const existingProducts = await this.db
        .select({ id: products.id, name: products.name })
        .from(products)
        .where(
          and(
            inArray(products.id, productIds),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        );

      if (existingProducts.length !== productIds.length) {
        throw new BadRequestException(
          'One or more products not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateGlobalPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log bulk position update operation
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.PRODUCT,
          updates.map((u) => u.id),
          { globalPositionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeGlobalPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update product global positions: ${error.message}`,
      );
    }
  }

  /**
   * Update category positions for products within their specific category
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateProductCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for product ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all products exist and belong to the business
      const productIds = updates.map((update) => update.id);
      const existingProducts = await this.db
        .select({
          id: products.id,
          name: products.name,
          categoryId: products.categoryId,
        })
        .from(products)
        .where(
          and(
            inArray(products.id, productIds),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        );

      if (existingProducts.length !== productIds.length) {
        throw new BadRequestException(
          'One or more products not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateCategoryPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log bulk position update operation
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.PRODUCT,
          updates.map((u) => u.id),
          { categoryPositionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeCategoryPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update product category positions: ${error.message}`,
      );
    }
  }

  /**
   * Update sub-category positions for products within their specific sub-category
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateProductSubCategoryPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for product ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all products exist and belong to the business
      const productIds = updates.map((update) => update.id);
      const existingProducts = await this.db
        .select({
          id: products.id,
          name: products.name,
          subCategoryId: products.subCategoryId,
        })
        .from(products)
        .where(
          and(
            inArray(products.id, productIds),
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        );

      if (existingProducts.length !== productIds.length) {
        throw new BadRequestException(
          'One or more products not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateSubCategoryPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log bulk position update operation
        await this.activityLogService.logBulkOperation(
          ActivityType.BULK_UPDATE,
          EntityType.PRODUCT,
          updates.map((u) => u.id),
          { subCategoryPositionsUpdated: true },
          userId,
          businessId,
          {
            filterCriteria: { positionUpdates: updates.length },
            executionStrategy: ExecutionStrategy.PARALLEL,
          },
        );

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeSubCategoryPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update product sub-category positions: ${error.message}`,
      );
    }
  }

  /**
   * Optimized batch global position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateGlobalPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(products)
            .set({
              globalPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(products.id, update.id),
                eq(products.businessId, businessId),
                eq(products.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(products)
          .set({
            globalPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(products.id, update.id),
              eq(products.businessId, businessId),
              eq(products.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update global positions:', error.message);
      throw error;
    }
  }

  /**
   * Optimized batch category position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateCategoryPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(products)
            .set({
              categoryPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(products.id, update.id),
                eq(products.businessId, businessId),
                eq(products.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(products)
          .set({
            categoryPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(products.id, update.id),
              eq(products.businessId, businessId),
              eq(products.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update category positions:', error.message);
      throw error;
    }
  }

  /**
   * Optimized batch sub-category position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateSubCategoryPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(products)
            .set({
              subCategoryPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(products.id, update.id),
                eq(products.businessId, businessId),
                eq(products.isDeleted, false),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(products)
          .set({
            subCategoryPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(products.id, update.id),
              eq(products.businessId, businessId),
              eq(products.isDeleted, false),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn(
        'Failed to batch update sub-category positions:',
        error.message,
      );
      throw error;
    }
  }

  /**
   * Conditionally normalize global positions only when needed
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizeGlobalPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          positions: sql<
            number[]
          >`array_agg(${products.globalPosition} ORDER BY ${products.globalPosition})`,
          count: sql<number>`count(*)`,
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        );

      if (positionCheck.length === 0) return;

      const { positions, count: totalCount } = positionCheck[0];

      // Check for gaps or duplicates
      const needsNormalization = this.checkIfNormalizationNeeded(
        positions,
        totalCount,
      );

      if (!needsNormalization) return;

      // Get all products ordered by current global position
      const allProducts = await tx
        .select({
          id: products.id,
          globalPosition: products.globalPosition,
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .orderBy(products.globalPosition, products.id);

      // Normalize positions
      const updates = allProducts
        .map((product: any, index: number) => ({
          id: product.id,
          position: index + 1,
        }))
        .filter(
          (update: any, index: number) =>
            allProducts[index].globalPosition !== update.position,
        );

      if (updates.length > 0) {
        await this.batchUpdateGlobalPositions(tx, businessId, updates);
      }
    } catch (error) {
      console.warn('Failed to normalize global positions:', error.message);
      // Don't throw error for normalization failures
    }
  }

  /**
   * Conditionally normalize category positions only when needed
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizeCategoryPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          categoryId: products.categoryId,
          positions: sql<
            number[]
          >`array_agg(${products.categoryPosition} ORDER BY ${products.categoryPosition})`,
          count: sql<number>`count(*)`,
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .groupBy(products.categoryId);

      // Process each category group
      for (const group of positionCheck) {
        const needsNormalization = this.checkIfNormalizationNeeded(
          group.positions,
          group.count,
        );

        if (!needsNormalization) continue;

        // Get all products in this category ordered by current position
        const categoryProducts = await tx
          .select({
            id: products.id,
            categoryPosition: products.categoryPosition,
          })
          .from(products)
          .where(
            and(
              eq(products.businessId, businessId),
              group.categoryId
                ? eq(products.categoryId, group.categoryId)
                : eq(products.categoryId, null),
              eq(products.isDeleted, false),
            ),
          )
          .orderBy(products.categoryPosition, products.id);

        // Normalize positions for this category
        const updates = categoryProducts
          .map((product: any, index: number) => ({
            id: product.id,
            position: index + 1,
          }))
          .filter(
            (update: any, index: number) =>
              categoryProducts[index].categoryPosition !== update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdateCategoryPositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn('Failed to normalize category positions:', error.message);
      // Don't throw error for normalization failures
    }
  }

  /**
   * Conditionally normalize sub-category positions only when needed
   * @param tx - Database transaction
   * @param businessId - The business ID
   */
  private async conditionalNormalizeSubCategoryPositions(
    tx: any,
    businessId: string,
  ): Promise<void> {
    try {
      // Check if normalization is needed by looking for gaps or duplicates
      const positionCheck = await tx
        .select({
          subCategoryId: products.subCategoryId,
          positions: sql<
            number[]
          >`array_agg(${products.subCategoryPosition} ORDER BY ${products.subCategoryPosition})`,
          count: sql<number>`count(*)`,
        })
        .from(products)
        .where(
          and(
            eq(products.businessId, businessId),
            eq(products.isDeleted, false),
          ),
        )
        .groupBy(products.subCategoryId);

      // Process each sub-category group
      for (const group of positionCheck) {
        const needsNormalization = this.checkIfNormalizationNeeded(
          group.positions,
          group.count,
        );

        if (!needsNormalization) continue;

        // Get all products in this sub-category ordered by current position
        const subCategoryProducts = await tx
          .select({
            id: products.id,
            subCategoryPosition: products.subCategoryPosition,
          })
          .from(products)
          .where(
            and(
              eq(products.businessId, businessId),
              group.subCategoryId
                ? eq(products.subCategoryId, group.subCategoryId)
                : isNull(products.subCategoryId),
              eq(products.isDeleted, false),
            ),
          )
          .orderBy(products.subCategoryPosition, products.id);

        // Normalize positions for this sub-category
        const updates = subCategoryProducts
          .map((product: any, index: number) => ({
            id: product.id,
            position: index + 1,
          }))
          .filter(
            (update: any, index: number) =>
              subCategoryProducts[index].subCategoryPosition !==
              update.position,
          );

        if (updates.length > 0) {
          await this.batchUpdateSubCategoryPositions(tx, businessId, updates);
        }
      }
    } catch (error) {
      console.warn(
        'Failed to normalize sub-category positions:',
        error.message,
      );
      // Don't throw error for normalization failures
    }
  }

  /**
   * Check if position normalization is needed
   * @param positions - Array of current positions
   * @param totalCount - Total count of items
   * @returns true if normalization is needed
   */
  private checkIfNormalizationNeeded(
    positions: number[],
    totalCount: number,
  ): boolean {
    if (!positions || positions.length === 0) return false;

    // Check if the number of positions matches the total count
    if (positions.length !== totalCount) return true;

    // Check for duplicates
    const uniquePositions = new Set(positions);
    if (uniquePositions.size !== positions.length) return true;

    // Check for gaps (positions should be 1, 2, 3, ..., totalCount)
    const sortedPositions = [...positions].sort((a, b) => a - b);
    for (let i = 0; i < sortedPositions.length; i++) {
      if (sortedPositions[i] !== i + 1) return true;
    }

    return false;
  }

  /**
   * Validate position updates for duplicates and other issues
   * @param updates - Array of position updates
   */
  private validatePositionUpdates(
    updates: { id: string; position: number }[],
  ): void {
    // Check for duplicate IDs
    const ids = updates.map((u) => u.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      throw new BadRequestException(
        `Duplicate product IDs found: ${duplicateIds.join(', ')}`,
      );
    }

    // Check for duplicate positions
    const positions = updates.map((u) => u.position);
    const duplicatePositions = positions.filter(
      (pos, index) => positions.indexOf(pos) !== index,
    );
    if (duplicatePositions.length > 0) {
      throw new BadRequestException(
        `Duplicate positions found: ${duplicatePositions.join(', ')}`,
      );
    }

    // Check for invalid positions
    const invalidPositions = updates.filter(
      (u) => !Number.isInteger(u.position) || u.position < 1,
    );
    if (invalidPositions.length > 0) {
      throw new BadRequestException(
        `Invalid positions found: ${invalidPositions.map((u) => `${u.id}:${u.position}`).join(', ')}`,
      );
    }
  }
}
