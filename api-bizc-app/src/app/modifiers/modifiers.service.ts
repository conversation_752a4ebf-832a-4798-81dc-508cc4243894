import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateModifierGroupDto } from './dto/create-modifier-group.dto';
import { UpdateModifierGroupDto } from './dto/update-modifier-group.dto';
import { ModifierGroupDto } from './dto/modifier-group.dto';
import { ModifierGroupSlimDto } from './dto/modifier-group-slim.dto';
import { ModifierGroupListDto } from './dto/modifier-group-list.dto';
import { modifierGroups } from '../drizzle/schema/modifier-groups.schema';
import { modifiers } from '../drizzle/schema/modifiers.schema';
import {
  eq,
  and,
  or,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import {
  EntityType,
  ActivityType,
  ExecutionStrategy,
  ActivitySource,
} from '../shared/types/activity.enum';
import type { ActivityMetadata } from '../shared/types/activity-metadata.type';
import { ModifierStatus, SelectionType } from '../shared/types';

@Injectable()
export class ModifiersService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createModifierGroupDto: CreateModifierGroupDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a modifier group with the same name already exists for this business
      const existingModifierGroup = await this.db
        .select()
        .from(modifierGroups)
        .where(
          and(
            eq(modifierGroups.businessId, businessId),
            ilike(modifierGroups.groupName, createModifierGroupDto.groupName),
            eq(modifierGroups.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (existingModifierGroup) {
        throw new ConflictException(
          `A modifier group with the name '${createModifierGroupDto.groupName}' already exists for this business`,
        );
      }

      // Use a transaction to ensure modifier group and modifiers creation are atomic
      const newModifierGroup = await this.db.transaction(async (tx) => {
        // Insert new modifier group
        const [modifierGroup] = await tx
          .insert(modifierGroups)
          .values({
            businessId,
            groupName: createModifierGroupDto.groupName,
            description: createModifierGroupDto.description,
            selectionType: createModifierGroupDto.selectionType,
            isRequired: createModifierGroupDto.isRequired ?? false,
            status: createModifierGroupDto.status ?? ModifierStatus.ACTIVE,
            createdBy: userId,
          })
          .returning();

        // Insert modifiers if provided
        if (
          createModifierGroupDto.modifiers &&
          createModifierGroupDto.modifiers.length > 0
        ) {
          const modifierValues = createModifierGroupDto.modifiers.map(
            (modifier) => ({
              businessId,
              modifierGroupId: modifierGroup.id,
              modifierName: modifier.modifierName,
              description: modifier.description,
              priceAdjustment: modifier.priceAdjustment,
              cost: modifier.cost,
              adjustmentType: modifier.adjustmentType,
              isDefault: modifier.isDefault ?? false,
              status: modifier.status ?? ModifierStatus.ACTIVE,
              createdBy: userId,
            }),
          );

          await tx.insert(modifiers).values(modifierValues);
        }

        return modifierGroup;
      });

      // Log the modifier group creation activity
      await this.activityLogService.logCreate(
        newModifierGroup.id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          reason: `Modifier group "${createModifierGroupDto.groupName}" was created`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: newModifierGroup.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create modifier group: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    groupName?: string,
    status?: string,
    selectionType?: string,
    isRequired?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ModifierGroupListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      eq(modifierGroups.isDeleted, false),
      eq(modifierGroups.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(modifierGroups.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(modifierGroups.createdAt, toDate));
      }
    }

    // Add group name filtering if provided
    if (groupName) {
      whereConditions.push(ilike(modifierGroups.groupName, `%${groupName}%`));
    }

    // Add status filtering if provided
    if (status) {
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as ModifierStatus);
      whereConditions.push(inArray(modifierGroups.status, statusArray));
    }

    // Add selection type filtering if provided
    if (selectionType) {
      const decodedSelectionType = decodeURIComponent(selectionType);
      const selectionTypeArray = decodedSelectionType
        .split(',')
        .map((s) => s.trim() as SelectionType);
      whereConditions.push(
        inArray(modifierGroups.selectionType, selectionTypeArray),
      );
    }

    // Add isRequired filtering if provided
    if (isRequired) {
      const decodedIsRequired = decodeURIComponent(isRequired);
      const isRequiredValues = decodedIsRequired
        .split(',')
        .map((s) => s.trim());
      if (isRequiredValues.length === 1) {
        const boolValue = isRequiredValues[0].toLowerCase() === 'true';
        whereConditions.push(eq(modifierGroups.isRequired, boolValue));
      } else {
        // Handle multiple boolean values (true, false)
        const boolValues = isRequiredValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(modifierGroups.isRequired, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(modifierGroups.isRequired, false));
        }
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'groupName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(modifierGroups.groupName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(modifierGroups.groupName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(modifierGroups.groupName, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${modifierGroups.groupName} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${modifierGroups.groupName} IS NULL OR ${modifierGroups.groupName} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${modifierGroups.groupName} IS NOT NULL AND ${modifierGroups.groupName} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(modifierGroups.status, value as ModifierStatus[]),
                  );
                } else {
                  filterConditions.push(eq(modifierGroups.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${modifierGroups.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${modifierGroups.status} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as ModifierStatus);
                  filterConditions.push(
                    inArray(modifierGroups.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(modifierGroups.status, value));
                }
                break;
            }
          } else if (fieldId === 'selectionType') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(modifierGroups.selectionType, value),
                  );
                } else {
                  filterConditions.push(
                    eq(modifierGroups.selectionType, value),
                  );
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${modifierGroups.selectionType} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${modifierGroups.selectionType} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const selectionTypeValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as SelectionType);
                  filterConditions.push(
                    inArray(modifierGroups.selectionType, selectionTypeValues),
                  );
                } else {
                  filterConditions.push(
                    eq(modifierGroups.selectionType, value),
                  );
                }
                break;
            }
          } else if (fieldId === 'isRequired') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(modifierGroups.isRequired, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(eq(modifierGroups.isRequired, false));
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(modifierGroups.isRequired, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(
                  eq(modifierGroups.isRequired, !boolValue),
                );
                break;
              }
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const isRequiredValues = decodedValue
                    .split(',')
                    .map((s) => s.trim());
                  const boolValues = isRequiredValues.map(
                    (s) => s.toLowerCase() === 'true',
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(modifierGroups.isRequired, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(eq(modifierGroups.isRequired, false));
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(modifierGroups.isRequired, boolValue),
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Parse sort parameter
    let orderBy = desc(modifierGroups.createdAt); // Default sort
    if (sort) {
      try {
        const sortConfig = JSON.parse(sort);
        if (Array.isArray(sortConfig) && sortConfig.length > 0) {
          const { id: sortField, desc: isDesc } = sortConfig[0];
          const direction = isDesc ? desc : asc;

          switch (sortField) {
            case 'groupName':
              orderBy = direction(modifierGroups.groupName);
              break;
            case 'status':
              orderBy = direction(modifierGroups.status);
              break;
            case 'selectionType':
              orderBy = direction(modifierGroups.selectionType);
              break;
            case 'isRequired':
              orderBy = direction(modifierGroups.isRequired);
              break;
            case 'createdAt':
              orderBy = direction(modifierGroups.createdAt);
              break;
            case 'updatedAt':
              orderBy = direction(modifierGroups.updatedAt);
              break;
            case 'modifiersCount':
              // This will be handled with post-query sorting since it's a calculated field
              // Use createdAt as default to maintain performance
              orderBy = desc(modifierGroups.createdAt);
              break;
            default:
              orderBy = desc(modifierGroups.createdAt);
          }
        }
      } catch {
        // If sort parsing fails, use default
        orderBy = desc(modifierGroups.createdAt);
      }
    }

    // Check if we need to sort by calculated fields
    const needsPostQuerySort =
      sort &&
      (() => {
        try {
          const parsedSort = JSON.parse(sort);
          return parsedSort.length > 0 && parsedSort[0].id === 'modifiersCount';
        } catch {
          return false;
        }
      })();

    // Find all modifier groups with modifiers count
    const result = await this.db
      .select({
        id: modifierGroups.id,
        groupName: modifierGroups.groupName,
        description: modifierGroups.description,
        selectionType: modifierGroups.selectionType,
        isRequired: modifierGroups.isRequired,
        status: modifierGroups.status,
        createdAt: modifierGroups.createdAt,
        updatedAt: modifierGroups.updatedAt,
        modifiersCount: sql<number>`count(${modifiers.id})`.as(
          'modifiersCount',
        ),
      })
      .from(modifierGroups)
      .leftJoin(
        modifiers,
        and(
          eq(modifiers.modifierGroupId, modifierGroups.id),
          eq(modifiers.isDeleted, false),
        ),
      )
      .where(and(...whereConditions))
      .groupBy(modifierGroups.id)
      .orderBy(orderBy)
      .limit(needsPostQuerySort ? undefined : limit)
      .offset(needsPostQuerySort ? undefined : offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(modifierGroups)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Transform result to the expected format
    let data = result.map((row) => ({
      id: row.id,
      groupName: row.groupName,
      description: row.description,
      selectionType: row.selectionType,
      isRequired: row.isRequired,
      status: row.status,
      modifiersCount: Number(row.modifiersCount),
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    }));

    // Handle post-query sorting for calculated fields
    if (needsPostQuerySort && sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const { id: sortField, desc: isDesc } = parsedSort[0];

          if (sortField === 'modifiersCount') {
            data.sort((a, b) => {
              const aValue = a.modifiersCount;
              const bValue = b.modifiersCount;
              return isDesc ? bValue - aValue : aValue - bValue;
            });
          }
        }
      } catch {
        // Invalid sort, ignore
      }

      // Apply pagination after sorting
      const startIndex = (page - 1) * limit;
      data = data.slice(startIndex, startIndex + limit);
    }

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<ModifierGroupSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all modifier groups with their modifiers
    const modifierGroupsResult = await this.db
      .select()
      .from(modifierGroups)
      .where(
        and(
          eq(modifierGroups.businessId, businessId),
          eq(modifierGroups.status, ModifierStatus.ACTIVE),
          eq(modifierGroups.isDeleted, false),
        ),
      )
      .orderBy(asc(modifierGroups.createdAt));

    const modifierGroupsSlim: ModifierGroupSlimDto[] = [];

    for (const group of modifierGroupsResult) {
      // Get modifiers for this group
      const groupModifiers = await this.db
        .select({
          id: modifiers.id,
          modifierName: modifiers.modifierName,
          priceAdjustment: modifiers.priceAdjustment,
          adjustmentType: modifiers.adjustmentType,
          isDefault: modifiers.isDefault,
        })
        .from(modifiers)
        .where(
          and(
            eq(modifiers.modifierGroupId, group.id),
            eq(modifiers.status, ModifierStatus.ACTIVE),
            eq(modifiers.isDeleted, false),
          ),
        )
        .orderBy(asc(modifiers.createdAt));

      modifierGroupsSlim.push({
        id: group.id,
        groupName: group.groupName,
        selectionType: group.selectionType,
        isRequired: group.isRequired,
        modifiers: groupModifiers,
      });
    }

    return modifierGroupsSlim;
  }

  async findOne(userId: string, id: string): Promise<ModifierGroupDto> {
    // Get modifier group with modifiers
    const modifierGroup = await this.db
      .select()
      .from(modifierGroups)
      .where(
        and(eq(modifierGroups.id, id), eq(modifierGroups.isDeleted, false)),
      )
      .then((results) => results[0]);

    if (!modifierGroup) {
      throw new NotFoundException('Modifier group not found');
    }

    // Get modifiers for this group
    const groupModifiers = await this.db
      .select()
      .from(modifiers)
      .where(
        and(eq(modifiers.modifierGroupId, id), eq(modifiers.isDeleted, false)),
      )
      .orderBy(asc(modifiers.createdAt));

    // Get creator and updater names
    const createdByUser = await this.db
      .select({ name: users.name })
      .from(users)
      .where(eq(users.id, modifierGroup.createdBy))
      .then((results) => results[0]);

    let updatedByUser = null;
    if (modifierGroup.updatedBy) {
      updatedByUser = await this.db
        .select({ name: users.name })
        .from(users)
        .where(eq(users.id, modifierGroup.updatedBy))
        .then((results) => results[0]);
    }

    // Disable view logging for performance

    return {
      id: modifierGroup.id,
      businessId: modifierGroup.businessId,
      groupName: modifierGroup.groupName,
      description: modifierGroup.description,
      selectionType: modifierGroup.selectionType,
      isRequired: modifierGroup.isRequired,
      status: modifierGroup.status,
      modifiers: groupModifiers.map((modifier) => ({
        id: modifier.id,
        businessId: modifier.businessId,
        modifierGroupId: modifier.modifierGroupId,
        modifierName: modifier.modifierName,
        description: modifier.description,
        priceAdjustment: modifier.priceAdjustment,
        cost: modifier.cost,
        adjustmentType: modifier.adjustmentType,
        isDefault: modifier.isDefault,
        status: modifier.status,
        createdBy: createdByUser?.name || 'Unknown',
        updatedBy: updatedByUser?.name,
        createdAt: modifier.createdAt,
        updatedAt: modifier.updatedAt,
      })),
      createdBy: createdByUser?.name || 'Unknown',
      updatedBy: updatedByUser?.name,
      modifiersCount: groupModifiers.length,
      createdAt: modifierGroup.createdAt,
      updatedAt: modifierGroup.updatedAt,
    };
  }

  async checkNameAvailability(
    _userId: string,
    businessId: string | null,
    groupName: string,
  ): Promise<{ available: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingModifierGroup = await this.db
      .select()
      .from(modifierGroups)
      .where(
        and(
          eq(modifierGroups.businessId, businessId),
          ilike(modifierGroups.groupName, groupName),
          eq(modifierGroups.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    const available = !existingModifierGroup;

    return {
      available,
      message: available
        ? 'Modifier group name is available'
        : 'Modifier group name is already taken',
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateModifierGroupDto: UpdateModifierGroupDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if modifier group exists and belongs to the business
      const existingModifierGroup = await this.db
        .select()
        .from(modifierGroups)
        .where(
          and(
            eq(modifierGroups.id, id),
            eq(modifierGroups.businessId, businessId),
            eq(modifierGroups.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingModifierGroup) {
        throw new NotFoundException('Modifier group not found');
      }

      // Check if name is being changed and if new name already exists
      if (
        updateModifierGroupDto.groupName &&
        updateModifierGroupDto.groupName !== existingModifierGroup.groupName
      ) {
        const existingNameGroup = await this.db
          .select()
          .from(modifierGroups)
          .where(
            and(
              eq(modifierGroups.businessId, businessId),
              ilike(modifierGroups.groupName, updateModifierGroupDto.groupName),
              eq(modifierGroups.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingNameGroup) {
          throw new ConflictException(
            `A modifier group with the name '${updateModifierGroupDto.groupName}' already exists for this business`,
          );
        }
      }

      // Use a transaction to ensure modifier group and modifiers update are atomic
      const updatedModifierGroup = await this.db.transaction(async (tx) => {
        // Update modifier group
        const [modifierGroup] = await tx
          .update(modifierGroups)
          .set({
            groupName: updateModifierGroupDto.groupName,
            description: updateModifierGroupDto.description,
            selectionType: updateModifierGroupDto.selectionType,
            isRequired: updateModifierGroupDto.isRequired,
            status: updateModifierGroupDto.status,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(modifierGroups.id, id))
          .returning();

        // Handle modifiers update if provided
        if (updateModifierGroupDto.modifiers) {
          for (const modifierUpdate of updateModifierGroupDto.modifiers) {
            if (modifierUpdate.id) {
              // Update existing modifier
              await tx
                .update(modifiers)
                .set({
                  modifierName: modifierUpdate.modifierName,
                  description: modifierUpdate.description,
                  priceAdjustment: modifierUpdate.priceAdjustment,
                  cost: modifierUpdate.cost,
                  adjustmentType: modifierUpdate.adjustmentType,
                  isDefault: modifierUpdate.isDefault,
                  status: modifierUpdate.status,
                  updatedBy: userId,
                  updatedAt: new Date(),
                })
                .where(
                  and(
                    eq(modifiers.id, modifierUpdate.id),
                    eq(modifiers.modifierGroupId, id),
                  ),
                );
            } else {
              // Create new modifier - validate required fields
              if (
                !modifierUpdate.modifierName ||
                !modifierUpdate.priceAdjustment ||
                !modifierUpdate.cost ||
                !modifierUpdate.adjustmentType
              ) {
                throw new BadRequestException(
                  'Missing required fields for new modifier: modifierName, priceAdjustment, cost, adjustmentType',
                );
              }

              await tx.insert(modifiers).values({
                businessId,
                modifierGroupId: id,
                modifierName: modifierUpdate.modifierName,
                description: modifierUpdate.description,
                priceAdjustment: modifierUpdate.priceAdjustment,
                cost: modifierUpdate.cost,
                adjustmentType: modifierUpdate.adjustmentType,
                isDefault: modifierUpdate.isDefault ?? false,
                status: modifierUpdate.status ?? ModifierStatus.ACTIVE,
                createdBy: userId,
              });
            }
          }
        }

        return modifierGroup;
      });

      // Log the modifier group update activity
      await this.activityLogService.logUpdate(
        updatedModifierGroup.id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: updatedModifierGroup.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update modifier group: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if modifier group exists and belongs to the business
      const existingModifierGroup = await this.db
        .select()
        .from(modifierGroups)
        .where(
          and(
            eq(modifierGroups.id, id),
            eq(modifierGroups.businessId, businessId),
            eq(modifierGroups.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingModifierGroup) {
        throw new NotFoundException('Modifier group not found');
      }

      // Use a transaction to ensure modifier group and modifiers deletion are atomic
      await this.db.transaction(async (tx) => {
        // Soft delete all modifiers in the group
        await tx
          .update(modifiers)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(modifiers.modifierGroupId, id));

        // Soft delete the modifier group
        await tx
          .update(modifierGroups)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(modifierGroups.id, id));
      });

      // Log the modifier group deletion activity
      await this.activityLogService.logDelete(
        existingModifierGroup.id,
        EntityType.PRODUCT,
        userId,
        businessId,
        {
          reason: `Modifier group "${existingModifierGroup.groupName}" was deleted`,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        id: existingModifierGroup.id,
        message: 'Modifier group deleted successfully',
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete modifier group: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createModifierGroupDtos: CreateModifierGroupDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; count: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createModifierGroupDtos || createModifierGroupDtos.length === 0) {
        throw new BadRequestException(
          'No modifier groups provided for creation',
        );
      }

      // Check for duplicate names within the request
      const groupNames = createModifierGroupDtos.map((dto) =>
        dto.groupName.toLowerCase(),
      );
      const duplicateNames = groupNames.filter(
        (name, index) => groupNames.indexOf(name) !== index,
      );

      if (duplicateNames.length > 0) {
        throw new ConflictException(
          `Duplicate modifier group names found in request: ${duplicateNames.join(', ')}`,
        );
      }

      // Check if any modifier group names already exist for this business
      const existingModifierGroups = await this.db
        .select({ groupName: modifierGroups.groupName })
        .from(modifierGroups)
        .where(
          and(
            eq(modifierGroups.businessId, businessId),
            inArray(modifierGroups.groupName, groupNames),
            eq(modifierGroups.isDeleted, false),
          ),
        );

      if (existingModifierGroups.length > 0) {
        const existingNames = existingModifierGroups.map(
          (group) => group.groupName,
        );
        throw new ConflictException(
          `Modifier group names already exist: ${existingNames.join(', ')}`,
        );
      }

      const createdIds: string[] = [];

      // Use a transaction to ensure all modifier groups are created atomically
      await this.db.transaction(async (tx) => {
        for (const createModifierGroupDto of createModifierGroupDtos) {
          // Insert modifier group
          const [modifierGroup] = await tx
            .insert(modifierGroups)
            .values({
              businessId,
              groupName: createModifierGroupDto.groupName,
              description: createModifierGroupDto.description,
              selectionType: createModifierGroupDto.selectionType,
              isRequired: createModifierGroupDto.isRequired ?? false,
              status: createModifierGroupDto.status ?? ModifierStatus.ACTIVE,
              createdBy: userId,
            })
            .returning();

          createdIds.push(modifierGroup.id);

          // Insert modifiers if provided
          if (
            createModifierGroupDto.modifiers &&
            createModifierGroupDto.modifiers.length > 0
          ) {
            const modifierValues = createModifierGroupDto.modifiers.map(
              (modifier) => ({
                businessId,
                modifierGroupId: modifierGroup.id,
                modifierName: modifier.modifierName,
                description: modifier.description,
                priceAdjustment: modifier.priceAdjustment,
                cost: modifier.cost,
                adjustmentType: modifier.adjustmentType,
                isDefault: modifier.isDefault ?? false,
                status: modifier.status ?? ModifierStatus.ACTIVE,
                createdBy: userId,
              }),
            );

            await tx.insert(modifiers).values(modifierValues);
          }
        }
      });

      // Log the bulk creation activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_CREATE,
        EntityType.PRODUCT,
        createdIds,
        {
          names: createModifierGroupDtos.map((dto) => dto.groupName),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: createdIds.length },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        ids: createdIds,
        count: createdIds.length,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create modifier groups: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    modifierGroupIds: string[],
    metadata?: ActivityMetadata,
  ): Promise<{ deleted: number; message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!modifierGroupIds || modifierGroupIds.length === 0) {
        throw new BadRequestException(
          'No modifier group IDs provided for deletion',
        );
      }

      // Check if all modifier groups exist and belong to the business
      const existingModifierGroups = await this.db
        .select()
        .from(modifierGroups)
        .where(
          and(
            eq(modifierGroups.businessId, businessId),
            inArray(modifierGroups.id, modifierGroupIds),
            eq(modifierGroups.isDeleted, false),
          ),
        );

      if (existingModifierGroups.length !== modifierGroupIds.length) {
        const foundIds = existingModifierGroups.map((group) => group.id);
        const missingIds = modifierGroupIds.filter(
          (id) => !foundIds.includes(id),
        );
        throw new NotFoundException(
          `Modifier groups not found: ${missingIds.join(', ')}`,
        );
      }

      let deletedCount = 0;

      // Use a transaction to ensure all deletions are atomic
      await this.db.transaction(async (tx) => {
        // Soft delete all modifiers in the groups
        await tx
          .update(modifiers)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(modifiers.modifierGroupId, modifierGroupIds));

        // Soft delete the modifier groups
        const result = await tx
          .update(modifierGroups)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(inArray(modifierGroups.id, modifierGroupIds))
          .returning();

        deletedCount = result.length;
      });

      // Log the bulk deletion activity
      await this.activityLogService.logBulkOperation(
        ActivityType.BULK_DELETE,
        EntityType.PRODUCT,
        modifierGroupIds,
        {
          names: existingModifierGroups.map((group) => group.groupName),
        },
        userId,
        businessId,
        {
          filterCriteria: { count: deletedCount },
          executionStrategy: ExecutionStrategy.SEQUENTIAL,
          source: metadata?.source || ActivitySource.WEB,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          sessionId: metadata?.sessionId,
        },
      );

      return {
        deleted: deletedCount,
        message: `Successfully deleted ${deletedCount} modifier groups`,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete modifier groups: ${error.message}`,
      );
    }
  }

  // Convenience methods for returning IDs
  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createModifierGroupDto: CreateModifierGroupDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createModifierGroupDto, metadata);
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateModifierGroupDto: UpdateModifierGroupDto,
    metadata?: ActivityMetadata,
  ): Promise<{ id: string }> {
    return this.update(
      userId,
      businessId,
      id,
      updateModifierGroupDto,
      metadata,
    );
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createModifierGroupDtos: CreateModifierGroupDto[],
    metadata?: ActivityMetadata,
  ): Promise<{ ids: string[]; count: number }> {
    return this.bulkCreate(
      userId,
      businessId,
      createModifierGroupDtos,
      metadata,
    );
  }
}
