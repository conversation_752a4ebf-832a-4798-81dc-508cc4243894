import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  Query,
  UseGuards,
} from '@nestjs/common';
import { AccommodationReservationsService } from './accommodation-reservations.service';
import { CreateAccommodationReservationDto } from './dto/create-accommodation-reservation.dto';
import { UpdateAccommodationReservationDto } from './dto/update-accommodation-reservation.dto';
import { AccommodationReservationDto } from './dto/accommodation-reservation.dto';

import { AccommodationReservationIdResponseDto } from './dto/accommodation-reservation-id-response.dto';
import { AccommodationGuestsResponseDto } from './dto/accommodation-guests-response.dto';
import {
  BulkUpdateAccommodationReservationStatusDto,
  BulkUpdateAccommodationReservationStatusResponseDto,
} from './dto/bulk-update-accommodation-reservation-status.dto';
import { CheckAvailabilityResponseDto } from './dto/check-availability.dto';
import {
  ConfirmReservationDto,
  ConfirmReservationResponseDto,
} from './dto/confirm-reservation.dto';
import {
  CancelReservationDto,
  CancelReservationResponseDto,
} from './dto/cancel-reservation.dto';
import {
  CalculateAccommodationPricingDto,
  CalculateAccommodationPricingResponseDto,
} from './dto/calculate-pricing.dto';
import { CheckInDto, CheckInResponseDto } from './dto/check-in.dto';
import { CheckOutDto, CheckOutResponseDto } from './dto/check-out.dto';
import {
  AddGuestToReservationDto,
  AddGuestToReservationResponseDto,
  AddMultipleGuestsToReservationDto,
  AddMultipleGuestsResponseDto,
  RemoveGuestResponseDto,
} from './dto/guest-management.dto';
import {
  SendConfirmationDto,
  SendConfirmationResponseDto,
  SendReminderDto,
  SendReminderResponseDto,
} from './dto/communication.dto';
import {
  ProcessPaymentDto,
  ProcessPaymentResponseDto,
  ProcessRefundDto,
  ProcessRefundResponseDto,
} from './dto/payment.dto';
import { BulkAccommodationReservationIdsResponseDto } from './dto/bulk-accommodation-reservation-ids-response.dto';
import { BulkCreateAccommodationReservationDto } from './dto/bulk-create-accommodation-reservation.dto';
import { BulkDeleteAccommodationReservationDto } from './dto/bulk-delete-accommodation-reservation.dto';
import { BulkDeleteAccommodationReservationResponseDto } from './dto/bulk-delete-accommodation-reservation-response.dto';
import { DeleteAccommodationReservationResponseDto } from './dto/delete-accommodation-reservation-response.dto';
import { PaginatedAccommodationReservationsResponseDto } from './dto/paginated-accommodation-reservations-response.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { ActivityMetadata } from '../auth/decorators/activity-metadata.decorator';
import { Permission } from '../shared/types/permission.enum';
import type { ActivityMetadata as ActivityMetadataType } from '../shared/types/activity-metadata.type';

@ApiTags('accommodation-reservations')
@Controller('accommodation-reservations')
@UseGuards(PermissionsGuard)
export class AccommodationReservationsController {
  constructor(
    private readonly accommodationReservationsService: AccommodationReservationsService,
  ) {}

  @Post()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_CREATE)
  @ApiOperation({
    summary:
      'Create a new accommodation reservation with existing or new guests',
    description:
      'Creates a new accommodation reservation with flexible guest handling. Supports two approaches: 1) Use existing guests by providing guestId, or 2) Create new guests automatically by providing customer details (customerDisplayName or firstName/lastName). For new guests, customer records are created first, then guest records are linked to them. All operations are performed within the same transaction to ensure data consistency.',
  })
  @ApiBody({
    description: 'Accommodation reservation creation data',
    type: CreateAccommodationReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Accommodation reservation has been successfully created',
    type: AccommodationReservationIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or validation errors',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation number already exists',
  })
  create(
    @Request() req: any,
    @Body()
    createAccommodationReservationDto: CreateAccommodationReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AccommodationReservationIdResponseDto> {
    return this.accommodationReservationsService.createAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      createAccommodationReservationDto,
      metadata,
    );
  }

  @Post('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_CREATE)
  @ApiOperation({
    summary: 'Create multiple accommodation reservations in bulk',
  })
  @ApiBody({
    description: 'Bulk accommodation reservation creation data',
    type: BulkCreateAccommodationReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Accommodation reservations have been successfully created',
    type: BulkAccommodationReservationIdsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or validation errors',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation numbers already exist',
  })
  bulkCreate(
    @Request() req: any,
    @Body()
    bulkCreateAccommodationReservationDto: BulkCreateAccommodationReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkAccommodationReservationIdsResponseDto> {
    return this.accommodationReservationsService.bulkCreateAndReturnIds(
      req.user.id,
      req.user.activeBusinessId,
      bulkCreateAccommodationReservationDto.accommodationReservations,
      metadata,
    );
  }

  @Get('guests')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_READ)
  @ApiOperation({
    summary: 'Get all accommodation guests',
    description:
      'Retrieves all guests with guest type as ACCOMMODATION for the current business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Filter by creation date from (YYYY-MM-DD)',
    required: false,
    type: String,
    example: '2024-01-01',
  })
  @ApiQuery({
    name: 'to',
    description: 'Filter by creation date to (YYYY-MM-DD)',
    required: false,
    type: String,
    example: '2024-12-31',
  })
  @ApiQuery({
    name: 'guestNumber',
    description: 'Filter by guest number',
    required: false,
    type: String,
    example: 'GUEST-2024-000001',
  })
  @ApiQuery({
    name: 'customerName',
    description: 'Filter by customer name',
    required: false,
    type: String,
    example: 'John Doe',
  })
  @ApiQuery({
    name: 'email',
    description: 'Filter by customer email',
    required: false,
    type: String,
    example: '<EMAIL>',
  })
  @ApiQuery({
    name: 'phoneNumber',
    description: 'Filter by customer phone number',
    required: false,
    type: String,
    example: '******-123-4567',
  })
  @ApiQuery({
    name: 'nationality',
    description: 'Filter by guest nationality',
    required: false,
    type: String,
    example: 'American',
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by guest status (comma-separated for multiple values)',
    required: false,
    type: String,
    example: 'ACTIVE,INACTIVE',
  })
  @ApiQuery({
    name: 'identificationType',
    description:
      'Filter by identification type (comma-separated for multiple values)',
    required: false,
    type: String,
    example: 'PASSPORT,NATIONAL_ID',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"customerDisplayName","value":"John","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"ACTIVE","operator":"eq","type":"select","rowId":"2"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: guestNumber, customerDisplayName, createdAt, updatedAt',
    required: false,
    type: String,
    example: '[{"id":"createdAt","desc":true}]',
  })
  @ApiQuery({
    name: 'search',
    description:
      'Legacy search term for guest name, email, or phone (use filters for advanced search)',
    required: false,
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of accommodation guests retrieved successfully',
    type: AccommodationGuestsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async getAllGuests(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('guestNumber') guestNumber?: string,
    @Query('customerName') customerName?: string,
    @Query('email') email?: string,
    @Query('phoneNumber') phoneNumber?: string,
    @Query('nationality') nationality?: string,
    @Query('status') status?: string,
    @Query('identificationType') identificationType?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
    @Query('search') search?: string,
  ) {
    return this.accommodationReservationsService.getAllAccommodationGuests(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      guestNumber,
      customerName,
      email,
      phoneNumber,
      nationality,
      status,
      identificationType,
      filters,
      joinOperator,
      sort,
      search,
    );
  }

  @Get('availability')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_READ)
  @ApiOperation({ summary: 'Check accommodation unit availability' })
  @ApiQuery({
    name: 'checkInDate',
    description: 'Check-in date (YYYY-MM-DD)',
    required: true,
    type: String,
    example: '2024-12-25',
  })
  @ApiQuery({
    name: 'checkOutDate',
    description: 'Check-out date (YYYY-MM-DD)',
    required: true,
    type: String,
    example: '2024-12-28',
  })
  @ApiQuery({
    name: 'adults',
    description: 'Number of adults',
    required: true,
    type: Number,
    example: 2,
  })
  @ApiQuery({
    name: 'children',
    description: 'Number of children',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'accommodationUnitIds',
    description: 'Specific accommodation unit IDs to check (comma-separated)',
    required: false,
    type: String,
    example:
      '123e4567-e89b-12d3-a456-426614174000,123e4567-e89b-12d3-a456-426614174001',
  })
  @ApiResponse({
    status: 200,
    description: 'Availability check completed successfully',
    type: CheckAvailabilityResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid dates or parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async checkAvailability(
    @Request() req: any,
    @Query('checkInDate') checkInDate: string,
    @Query('checkOutDate') checkOutDate: string,
    @Query('adults') adults: number,
    @Query('children') children?: number,
    @Query('accommodationUnitIds') accommodationUnitIds?: string,
  ): Promise<CheckAvailabilityResponseDto> {
    return this.accommodationReservationsService.checkAvailability(
      req.user.activeBusinessId,
      checkInDate,
      checkOutDate,
      adults,
      children,
      accommodationUnitIds?.split(','),
    );
  }

  @Post('calculate-pricing')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_READ)
  @ApiOperation({ summary: 'Calculate pricing for accommodation reservation' })
  @ApiBody({
    description: 'Pricing calculation details',
    type: CalculateAccommodationPricingDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Pricing calculated successfully',
    type: CalculateAccommodationPricingResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid dates or parameters',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more accommodation units not found',
  })
  async calculatePricing(
    @Request() req: any,
    @Body() calculatePricingDto: CalculateAccommodationPricingDto,
  ): Promise<CalculateAccommodationPricingResponseDto> {
    return this.accommodationReservationsService.calculatePricing(
      req.user.activeBusinessId,
      calculatePricingDto,
    );
  }

  @Get()
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_READ)
  @ApiOperation({
    summary: 'Get all accommodation reservations for the active business',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of items per page',
    required: false,
    type: Number,
  })
  @ApiQuery({
    name: 'from',
    description: 'Start date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'to',
    description: 'End date for filtering (YYYY-MM-DD)',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'reservationNumber',
    description: 'Filter by reservation number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'referenceNumber',
    description: 'Filter by reference number',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'status',
    description: 'Filter by reservation status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'paymentStatus',
    description: 'Filter by payment status',
    required: false,
    type: String,
  })
  @ApiQuery({
    name: 'reservationSource',
    description:
      'Filter by reservation source (comma-separated for multiple values)',
    required: false,
    type: String,
    example: 'ONLINE,PHONE',
  })
  @ApiQuery({
    name: 'filters',
    description:
      'Advanced filters as JSON string with operator support. Supported operators: iLike (contains), notILike (does not contain), eq (is), ne (is not), isEmpty (is empty), isNotEmpty (is not empty)',
    required: false,
    type: String,
    example:
      '[{"id":"reservationNumber","value":"RES-2024","operator":"iLike","type":"text","rowId":"1"},{"id":"status","value":"CONFIRMED","operator":"eq","type":"select","rowId":"2"},{"id":"total","value":"1000","operator":"eq","type":"number","rowId":"3"}]',
  })
  @ApiQuery({
    name: 'joinOperator',
    description: 'Join operator for advanced filters',
    required: false,
    type: String,
    enum: ['and', 'or'],
    example: 'and',
  })
  @ApiQuery({
    name: 'sort',
    description:
      'Sort configuration as JSON string. Supported fields: reservationNumber, checkInDate, checkOutDate, total, createdAt, updatedAt. Also supports legacy format: field:direction (e.g., createdAt:desc)',
    required: false,
    type: String,
    example: '[{"id":"createdAt","desc":true}]',
  })
  @ApiResponse({
    status: 200,
    description: 'All accommodation reservations returned successfully',
    type: PaginatedAccommodationReservationsResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(
    @Request() req: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('from') from?: string,
    @Query('to') to?: string,
    @Query('reservationNumber') reservationNumber?: string,
    @Query('referenceNumber') referenceNumber?: string,
    @Query('status') status?: string,
    @Query('paymentStatus') paymentStatus?: string,
    @Query('reservationSource') reservationSource?: string,
    @Query('filters') filters?: string,
    @Query('joinOperator') joinOperator?: 'and' | 'or',
    @Query('sort') sort?: string,
  ): Promise<PaginatedAccommodationReservationsResponseDto> {
    return this.accommodationReservationsService.findAllOptimized(
      req.user.id,
      req.user.activeBusinessId,
      page,
      limit,
      from,
      to,
      reservationNumber,
      referenceNumber,
      status,
      paymentStatus,
      reservationSource,
      filters,
      joinOperator,
      sort,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_READ)
  @ApiOperation({
    summary: 'Get accommodation reservation by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation reservation returned successfully',
    type: AccommodationReservationDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  findOne(
    @Request() req: any,
    @Param('id') id: string,
  ): Promise<AccommodationReservationDto> {
    return this.accommodationReservationsService.findOne(
      req.user.id,
      req.user.activeBusinessId,
      id,
    );
  }

  @Patch(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({
    summary: 'Update accommodation reservation by ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Accommodation reservation update data',
    type: UpdateAccommodationReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation reservation has been successfully updated',
    type: AccommodationReservationIdResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or validation errors',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Reservation number already exists',
  })
  update(
    @Request() req: any,
    @Param('id') id: string,
    @Body()
    updateAccommodationReservationDto: UpdateAccommodationReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AccommodationReservationIdResponseDto> {
    return this.accommodationReservationsService.updateAndReturnId(
      req.user.id,
      req.user.activeBusinessId,
      id,
      updateAccommodationReservationDto,
      metadata,
    );
  }

  @Patch(':id/confirm')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Confirm accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Confirmation details (optional)',
    type: ConfirmReservationDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Reservation confirmed successfully',
    type: ConfirmReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or reservation cannot be confirmed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async confirmReservation(
    @Request() req: any,
    @Param('id') id: string,
    @Body() confirmDto?: ConfirmReservationDto,
    @ActivityMetadata() metadata?: ActivityMetadataType,
  ): Promise<ConfirmReservationResponseDto> {
    return this.accommodationReservationsService.confirmReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      confirmDto,
      metadata,
    );
  }

  @Patch(':id/cancel')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Cancel accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Cancellation details',
    type: CancelReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Reservation cancelled successfully',
    type: CancelReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or reservation cannot be cancelled',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async cancelReservation(
    @Request() req: any,
    @Param('id') id: string,
    @Body() cancelDto: CancelReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<CancelReservationResponseDto> {
    return this.accommodationReservationsService.cancelReservation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      cancelDto,
      metadata,
    );
  }

  @Patch(':id/check-in')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Check-in guest for accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Check-in details (optional)',
    type: CheckInDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Guest checked in successfully',
    type: CheckInResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or reservation cannot be checked in',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async checkIn(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @Body() checkInDto?: CheckInDto,
  ): Promise<CheckInResponseDto> {
    return this.accommodationReservationsService.checkIn(
      req.user.id,
      req.user.activeBusinessId,
      id,
      checkInDto,
      metadata,
    );
  }

  @Patch(':id/check-out')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Check-out guest for accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Check-out details (optional)',
    type: CheckOutDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Guest checked out successfully',
    type: CheckOutResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or reservation cannot be checked out',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async checkOut(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @Body() checkOutDto?: CheckOutDto,
  ): Promise<CheckOutResponseDto> {
    return this.accommodationReservationsService.checkOut(
      req.user.id,
      req.user.activeBusinessId,
      id,
      checkOutDto,
      metadata,
    );
  }

  @Post(':id/guests')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Add guest to accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Guest details to add',
    type: AddGuestToReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Guest added to reservation successfully',
    type: AddGuestToReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid guest data or reservation at capacity',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async addGuest(
    @Request() req: any,
    @Param('id') id: string,
    @Body() addGuestDto: AddGuestToReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AddGuestToReservationResponseDto> {
    return this.accommodationReservationsService.addGuest(
      req.user.id,
      req.user.activeBusinessId,
      id,
      addGuestDto,
    );
  }

  @Post(':id/guests/multiple')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Add multiple guests to accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Multiple guests to add',
    type: AddMultipleGuestsToReservationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Guests added to reservation successfully',
    type: AddMultipleGuestsResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid guest data or reservation at capacity',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async addMultipleGuests(
    @Request() req: any,
    @Param('id') id: string,
    @Body() addMultipleGuestsDto: AddMultipleGuestsToReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<AddMultipleGuestsResponseDto> {
    return this.accommodationReservationsService.addMultipleGuests(
      req.user.id,
      req.user.activeBusinessId,
      id,
      addMultipleGuestsDto,
    );
  }

  @Delete(':id/guests/:guestId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Remove guest from accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiParam({
    name: 'guestId',
    description: 'Guest ID to remove',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Guest removed from reservation successfully',
    type: RemoveGuestResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Cannot remove primary guest or last guest',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation or guest not found',
  })
  async removeGuest(
    @Request() req: any,
    @Param('id') id: string,
    @Param('guestId') guestId: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<RemoveGuestResponseDto> {
    return this.accommodationReservationsService.removeGuest(
      req.user.id,
      req.user.activeBusinessId,
      id,
      guestId,
    );
  }

  @Post(':id/send-confirmation')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Send confirmation email/SMS for reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Confirmation sending options',
    type: SendConfirmationDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Confirmation sent successfully',
    type: SendConfirmationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or reservation not confirmed',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async sendConfirmation(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @Body() sendConfirmationDto?: SendConfirmationDto,
  ): Promise<SendConfirmationResponseDto> {
    return this.accommodationReservationsService.sendConfirmation(
      req.user.id,
      req.user.activeBusinessId,
      id,
      sendConfirmationDto,
    );
  }

  @Post(':id/send-reminder')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Send reminder email/SMS for reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Reminder sending options',
    type: SendReminderDto,
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Reminder sent successfully',
    type: SendReminderResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid data or reservation not suitable for reminder',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async sendReminder(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
    @Body() sendReminderDto?: SendReminderDto,
  ): Promise<SendReminderResponseDto> {
    return this.accommodationReservationsService.sendReminder(
      req.user.id,
      req.user.activeBusinessId,
      id,
      sendReminderDto,
    );
  }

  @Post(':id/payments')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Process payment for accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Payment details',
    type: ProcessPaymentDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Payment processed successfully',
    type: ProcessPaymentResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid payment data or reservation not payable',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async processPayment(
    @Request() req: any,
    @Param('id') id: string,
    @Body() paymentDto: ProcessPaymentDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<ProcessPaymentResponseDto> {
    return this.accommodationReservationsService.processPayment(
      req.user.id,
      req.user.activeBusinessId,
      id,
      paymentDto,
    );
  }

  @Post(':id/refund')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Process refund for accommodation reservation' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiBody({
    description: 'Refund details',
    type: ProcessRefundDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Refund processed successfully',
    type: ProcessRefundResponseDto,
  })
  @ApiResponse({
    status: 400,
    description:
      'Bad request - Invalid refund data or reservation not refundable',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Accommodation reservation not found',
  })
  async processRefund(
    @Request() req: any,
    @Param('id') id: string,
    @Body() refundDto: ProcessRefundDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<ProcessRefundResponseDto> {
    return this.accommodationReservationsService.processRefund(
      req.user.id,
      req.user.activeBusinessId,
      id,
      refundDto,
    );
  }

  @Delete('bulk')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_DELETE)
  @ApiOperation({ summary: 'Bulk delete accommodation reservations' })
  @ApiBody({
    description: 'Array of accommodation reservation IDs to delete',
    type: BulkDeleteAccommodationReservationDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation reservations have been successfully deleted',
    type: BulkDeleteAccommodationReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or reservations not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more accommodation reservations not found',
  })
  bulkDelete(
    @Request() req: any,
    @Body()
    bulkDeleteAccommodationReservationDto: BulkDeleteAccommodationReservationDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkDeleteAccommodationReservationResponseDto> {
    return this.accommodationReservationsService.bulkDelete(
      req.user.id,
      req.user.activeBusinessId,
      bulkDeleteAccommodationReservationDto.ids,
    );
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_DELETE)
  @ApiOperation({ summary: 'Delete accommodation reservation by ID' })
  @ApiParam({
    name: 'id',
    description: 'Accommodation reservation ID',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Accommodation reservation has been successfully deleted',
    type: DeleteAccommodationReservationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid ID or reservation not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Access denied to delete this reservation',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  remove(
    @Request() req: any,
    @Param('id') id: string,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<DeleteAccommodationReservationResponseDto> {
    return this.accommodationReservationsService.remove(
      req.user.id,
      req.user.activeBusinessId,
      id,
      metadata,
    );
  }

  @Patch('batch/status')
  @ApiBearerAuth()
  @RequirePermissions(Permission.ACCOMMODATION_RESERVATION_UPDATE)
  @ApiOperation({ summary: 'Batch update accommodation reservation status' })
  @ApiBody({
    description: 'Array of accommodation reservation IDs and new status',
    type: BulkUpdateAccommodationReservationStatusDto,
  })
  @ApiResponse({
    status: 200,
    description:
      'Accommodation reservation statuses have been successfully updated',
    type: BulkUpdateAccommodationReservationStatusResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid data or reservations not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - No active business found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more accommodation reservations not found',
  })
  bulkUpdateStatus(
    @Request() req: any,
    @Body() bulkUpdateStatusDto: BulkUpdateAccommodationReservationStatusDto,
    @ActivityMetadata() metadata: ActivityMetadataType,
  ): Promise<BulkUpdateAccommodationReservationStatusResponseDto> {
    return this.accommodationReservationsService.bulkUpdateStatus(
      req.user.id,
      req.user.activeBusinessId,
      bulkUpdateStatusDto.ids,
      bulkUpdateStatusDto.status,
      metadata,
    );
  }
}
