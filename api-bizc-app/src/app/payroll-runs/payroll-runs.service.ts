import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE as DRIZZLE_ORM } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreatePayrollRunDto } from './dto/create-payroll-run.dto';
import { UpdatePayrollRunDto } from './dto/update-payroll-run.dto';
import { PayrollRunDto } from './dto/payroll-run.dto';
import { PayrollRunSlimDto } from './dto/payroll-run-slim.dto';
import {
  payrollRuns,
  PayrollRunStatus,
} from '../drizzle/schema/payroll-run.schema';
import { eq, and, ilike, sql, gte, lte, desc, inArray } from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { EntityType, ActivitySource } from '../shared/types/activity.enum';

@Injectable()
export class PayrollRunsService {
  constructor(
    @Inject(DRIZZLE_ORM) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createPayrollRunDto: CreatePayrollRunDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a payroll run with the same run code already exists for this business
      if (createPayrollRunDto.runCode) {
        const existingPayrollRun = await this.db
          .select()
          .from(payrollRuns)
          .where(
            and(
              eq(payrollRuns.businessId, businessId),
              ilike(payrollRuns.runCode, createPayrollRunDto.runCode),
              eq(payrollRuns.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (existingPayrollRun) {
          throw new ConflictException(
            `A payroll run with the code '${createPayrollRunDto.runCode}' already exists for this business`,
          );
        }
      }

      // Validate the date range
      const startDate = new Date(createPayrollRunDto.payPeriodStart);
      const endDate = new Date(createPayrollRunDto.payPeriodEnd);

      if (startDate >= endDate) {
        throw new BadRequestException(
          'Pay period start date must be before end date',
        );
      }

      // Create the payroll run
      const insertData = {
        businessId,
        runCode: createPayrollRunDto.runCode,
        runType: createPayrollRunDto.runType,
        payrollMonth: createPayrollRunDto.payrollMonth,
        payrollYear: createPayrollRunDto.payrollYear,
        payPeriodStart: startDate,
        payPeriodEnd: endDate,
        paymentDate: new Date(createPayrollRunDto.paymentDate),
        runStatus: createPayrollRunDto.runStatus || PayrollRunStatus.DRAFT,
        totalEmployees: createPayrollRunDto.totalEmployees || 0,
        totalGrossPay: createPayrollRunDto.totalGrossPay || '0.00',
        totalDeductions: createPayrollRunDto.totalDeductions || '0.00',
        totalNetPay: createPayrollRunDto.totalNetPay || '0.00',
        notes: createPayrollRunDto.notes,
        createdBy: userId,
      } as any;

      const [newPayrollRun] = await this.db
        .insert(payrollRuns)
        .values(insertData)
        .returning();

      // Log the payroll run creation activity
      await this.activityLogService.logCreate(
        newPayrollRun.id,
        EntityType.PAYROLL_RUN,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id: newPayrollRun.id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create payroll run: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createPayrollRunDto: CreatePayrollRunDto,
  ): Promise<{ id: string }> {
    return this.create(userId, businessId, createPayrollRunDto);
  }

  async findAll(
    _userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: PayrollRunDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(payrollRuns.businessId, businessId),
      eq(payrollRuns.isDeleted, false),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(payrollRuns.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(payrollRuns.createdAt, toDate));
      }
    }

    // Get payroll runs with user information for createdBy
    const result = await this.db
      .select({
        id: payrollRuns.id,
        businessId: payrollRuns.businessId,
        runCode: payrollRuns.runCode,
        runType: payrollRuns.runType,
        payrollMonth: payrollRuns.payrollMonth,
        payrollYear: payrollRuns.payrollYear,
        payPeriodStart: payrollRuns.payPeriodStart,
        payPeriodEnd: payrollRuns.payPeriodEnd,
        paymentDate: payrollRuns.paymentDate,
        runStatus: payrollRuns.runStatus,
        totalEmployees: payrollRuns.totalEmployees,
        totalGrossPay: payrollRuns.totalGrossPay,
        totalDeductions: payrollRuns.totalDeductions,
        totalNetPay: payrollRuns.totalNetPay,
        notes: payrollRuns.notes,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: payrollRuns.createdAt,
        updatedAt: payrollRuns.updatedAt,
      })
      .from(payrollRuns)
      .leftJoin(users, eq(payrollRuns.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(payrollRuns.updatedBy, sql`updated_user.id`),
      )
      .where(and(...whereConditions))
      .orderBy(desc(payrollRuns.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(payrollRuns)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return {
      data: result,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllSlim(
    _userId: string,
    businessId: string | null,
  ): Promise<PayrollRunSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: payrollRuns.id,
        runCode: payrollRuns.runCode,
        runType: payrollRuns.runType,
        payrollMonth: payrollRuns.payrollMonth,
        payrollYear: payrollRuns.payrollYear,
        runStatus: payrollRuns.runStatus,
      })
      .from(payrollRuns)
      .where(
        and(
          eq(payrollRuns.businessId, businessId),
          eq(payrollRuns.isDeleted, false),
        ),
      )
      .orderBy(desc(payrollRuns.payPeriodStart));

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result;
  }

  async findOne(
    _userId: string,
    businessId: string | null,
    id: string,
  ): Promise<PayrollRunDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const result = await this.db
      .select({
        id: payrollRuns.id,
        businessId: payrollRuns.businessId,
        runCode: payrollRuns.runCode,
        runType: payrollRuns.runType,
        payrollMonth: payrollRuns.payrollMonth,
        payrollYear: payrollRuns.payrollYear,
        payPeriodStart: payrollRuns.payPeriodStart,
        payPeriodEnd: payrollRuns.payPeriodEnd,
        paymentDate: payrollRuns.paymentDate,
        runStatus: payrollRuns.runStatus,
        totalEmployees: payrollRuns.totalEmployees,
        totalGrossPay: payrollRuns.totalGrossPay,
        totalDeductions: payrollRuns.totalDeductions,
        totalNetPay: payrollRuns.totalNetPay,
        notes: payrollRuns.notes,
        createdBy: sql<string>`COALESCE(${users.firstName} || ' ' || ${users.lastName}, ${users.email})`,
        updatedBy: sql<string>`COALESCE(updated_user.first_name || ' ' || updated_user.last_name, updated_user.email)`,
        createdAt: payrollRuns.createdAt,
        updatedAt: payrollRuns.updatedAt,
      })
      .from(payrollRuns)
      .leftJoin(users, eq(payrollRuns.createdBy, users.id))
      .leftJoin(
        sql`${users} AS updated_user`,
        eq(payrollRuns.updatedBy, sql`updated_user.id`),
      )
      .where(
        and(
          eq(payrollRuns.id, id),
          eq(payrollRuns.businessId, businessId),
          eq(payrollRuns.isDeleted, false),
        ),
      )
      .then((results) => results[0]);

    if (!result) {
      throw new NotFoundException(`Payroll run with ID ${id} not found`);
    }

    // Note: Skipping view activity logging for performance reasons
    // await this.activityLogService.logView(...);

    return result;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updatePayrollRunDto: UpdatePayrollRunDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if payroll run exists and belongs to the business
      const existingPayrollRun = await this.db
        .select()
        .from(payrollRuns)
        .where(
          and(
            eq(payrollRuns.id, id),
            eq(payrollRuns.businessId, businessId),
            eq(payrollRuns.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPayrollRun) {
        throw new NotFoundException(`Payroll run with ID ${id} not found`);
      }

      // Check for run code conflicts if run code is being updated
      if (
        updatePayrollRunDto.runCode &&
        updatePayrollRunDto.runCode !== existingPayrollRun.runCode
      ) {
        const codeConflict = await this.db
          .select()
          .from(payrollRuns)
          .where(
            and(
              eq(payrollRuns.businessId, businessId),
              ilike(payrollRuns.runCode, updatePayrollRunDto.runCode),
              sql`${payrollRuns.id} != ${id}`,
              eq(payrollRuns.isDeleted, false),
            ),
          )
          .then((results) => results[0]);

        if (codeConflict) {
          throw new ConflictException(
            `A payroll run with the code '${updatePayrollRunDto.runCode}' already exists for this business`,
          );
        }
      }

      // Validate date range if dates are being updated
      if (
        updatePayrollRunDto.payPeriodStart ||
        updatePayrollRunDto.payPeriodEnd
      ) {
        const startDate = updatePayrollRunDto.payPeriodStart
          ? new Date(updatePayrollRunDto.payPeriodStart)
          : existingPayrollRun.payPeriodStart;
        const endDate = updatePayrollRunDto.payPeriodEnd
          ? new Date(updatePayrollRunDto.payPeriodEnd)
          : existingPayrollRun.payPeriodEnd;

        if (startDate >= endDate) {
          throw new BadRequestException(
            'Pay period start date must be before end date',
          );
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      // Only update fields that are provided
      if (updatePayrollRunDto.runCode !== undefined)
        updateData.runCode = updatePayrollRunDto.runCode;
      if (updatePayrollRunDto.runType !== undefined)
        updateData.runType = updatePayrollRunDto.runType;
      if (updatePayrollRunDto.payrollMonth !== undefined)
        updateData.payrollMonth = updatePayrollRunDto.payrollMonth;
      if (updatePayrollRunDto.payrollYear !== undefined)
        updateData.payrollYear = updatePayrollRunDto.payrollYear;
      if (updatePayrollRunDto.payPeriodStart !== undefined)
        updateData.payPeriodStart = new Date(
          updatePayrollRunDto.payPeriodStart,
        );
      if (updatePayrollRunDto.payPeriodEnd !== undefined)
        updateData.payPeriodEnd = new Date(updatePayrollRunDto.payPeriodEnd);
      if (updatePayrollRunDto.paymentDate !== undefined)
        updateData.paymentDate = new Date(updatePayrollRunDto.paymentDate);
      if (updatePayrollRunDto.runStatus !== undefined)
        updateData.runStatus = updatePayrollRunDto.runStatus;
      if (updatePayrollRunDto.totalEmployees !== undefined)
        updateData.totalEmployees = updatePayrollRunDto.totalEmployees;
      if (updatePayrollRunDto.totalGrossPay !== undefined)
        updateData.totalGrossPay = updatePayrollRunDto.totalGrossPay;
      if (updatePayrollRunDto.totalDeductions !== undefined)
        updateData.totalDeductions = updatePayrollRunDto.totalDeductions;
      if (updatePayrollRunDto.totalNetPay !== undefined)
        updateData.totalNetPay = updatePayrollRunDto.totalNetPay;
      if (updatePayrollRunDto.notes !== undefined)
        updateData.notes = updatePayrollRunDto.notes;

      // Update the payroll run
      await this.db
        .update(payrollRuns)
        .set(updateData)
        .where(eq(payrollRuns.id, id));

      // Log the activity
      await this.activityLogService.logUpdate(
        id,
        EntityType.PAYROLL_RUN,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update payroll run: ${error.message}`,
      );
    }
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updatePayrollRunDto: UpdatePayrollRunDto,
  ): Promise<{ id: string }> {
    return this.update(userId, businessId, id, updatePayrollRunDto);
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; deletedId: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if payroll run exists and belongs to the business
      const existingPayrollRun = await this.db
        .select({ runCode: payrollRuns.runCode })
        .from(payrollRuns)
        .where(
          and(
            eq(payrollRuns.id, id),
            eq(payrollRuns.businessId, businessId),
            eq(payrollRuns.isDeleted, false),
          ),
        )
        .then((results) => results[0]);

      if (!existingPayrollRun) {
        throw new NotFoundException(`Payroll run with ID ${id} not found`);
      }

      // Soft delete the payroll run
      await this.db
        .update(payrollRuns)
        .set({
          isDeleted: true,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(payrollRuns.id, id));

      // Log the activity
      await this.activityLogService.logDelete(
        id,
        EntityType.PAYROLL_RUN,
        userId,
        businessId,
        {
          source: ActivitySource.WEB,
        },
      );

      return {
        message: 'Payroll run deleted successfully',
        deletedId: id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete payroll run: ${error.message}`,
      );
    }
  }

  async checkRunCodeAvailability(
    _userId: string,
    businessId: string | null,
    runCode: string,
    excludeId?: string,
  ): Promise<{ available: boolean; message: string }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const conditions = [
      eq(payrollRuns.businessId, businessId),
      ilike(payrollRuns.runCode, runCode),
      eq(payrollRuns.isDeleted, false),
    ];

    if (excludeId) {
      conditions.push(sql`${payrollRuns.id} != ${excludeId}`);
    }

    const existingPayrollRun = await this.db
      .select({ id: payrollRuns.id })
      .from(payrollRuns)
      .where(and(...conditions))
      .then((results) => results[0]);

    const available = !existingPayrollRun;

    return {
      available,
      message: available
        ? 'Run code is available'
        : `Run code '${runCode}' is already in use`,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    payrollRunIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!payrollRunIds || payrollRunIds.length === 0) {
        throw new BadRequestException(
          'No payroll run IDs provided for deletion',
        );
      }

      // Get all payroll runs that exist and belong to the business
      const existingPayrollRuns = await this.db
        .select({
          id: payrollRuns.id,
          runCode: payrollRuns.runCode,
          businessId: payrollRuns.businessId,
        })
        .from(payrollRuns)
        .where(
          and(
            inArray(payrollRuns.id, payrollRunIds),
            eq(payrollRuns.businessId, businessId),
            eq(payrollRuns.isDeleted, false),
          ),
        );

      if (existingPayrollRuns.length === 0) {
        throw new NotFoundException('No valid payroll runs found for deletion');
      }

      const deletedIds: string[] = [];
      const now = new Date();

      // Perform soft delete for each payroll run
      for (const payrollRun of existingPayrollRuns) {
        await this.db
          .update(payrollRuns)
          .set({
            isDeleted: true,
            updatedBy: userId,
            updatedAt: now,
          })
          .where(eq(payrollRuns.id, payrollRun.id));

        // Log the payroll run deletion activity
        await this.activityLogService.logDelete(
          payrollRun.id,
          EntityType.PAYROLL_RUN,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );

        deletedIds.push(payrollRun.id);
      }

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} payroll runs`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete payroll runs: ${error.message}`,
      );
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createPayrollRunsDto: CreatePayrollRunDto[],
  ): Promise<PayrollRunDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createPayrollRunsDto || createPayrollRunsDto.length === 0) {
        throw new BadRequestException('No payroll runs provided for creation');
      }

      // Validate unique run codes within the batch
      const runCodes = createPayrollRunsDto
        .map((dto) => dto.runCode?.toLowerCase())
        .filter(Boolean);

      if (new Set(runCodes).size !== runCodes.length) {
        throw new ConflictException('Duplicate run codes found in the batch');
      }

      // Check for existing run codes in the database
      if (runCodes.length > 0) {
        const existingPayrollRuns = await this.db
          .select({
            runCode: payrollRuns.runCode,
          })
          .from(payrollRuns)
          .where(
            and(
              eq(payrollRuns.businessId, businessId),
              inArray(sql`LOWER(${payrollRuns.runCode})`, runCodes),
              eq(payrollRuns.isDeleted, false),
            ),
          );

        if (existingPayrollRuns.length > 0) {
          const existingCodes = existingPayrollRuns.map((pr) =>
            pr.runCode.toLowerCase(),
          );
          const duplicates = runCodes.filter((code) =>
            existingCodes.includes(code),
          );
          throw new ConflictException(
            `The following run codes already exist: ${duplicates.join(', ')}`,
          );
        }
      }

      const createdPayrollRuns: PayrollRunDto[] = [];

      // Process each payroll run in the batch
      for (const createPayrollRunDto of createPayrollRunsDto) {
        // Validate the date range
        const startDate = new Date(createPayrollRunDto.payPeriodStart);
        const endDate = new Date(createPayrollRunDto.payPeriodEnd);

        if (startDate >= endDate) {
          throw new BadRequestException(
            `Pay period start date must be before end date for run code '${createPayrollRunDto.runCode}'`,
          );
        }

        // Create payroll run
        const insertData = {
          businessId,
          runCode: createPayrollRunDto.runCode,
          runType: createPayrollRunDto.runType,
          payrollMonth: createPayrollRunDto.payrollMonth,
          payrollYear: createPayrollRunDto.payrollYear,
          payPeriodStart: startDate,
          payPeriodEnd: endDate,
          paymentDate: new Date(createPayrollRunDto.paymentDate),
          runStatus: createPayrollRunDto.runStatus || PayrollRunStatus.DRAFT,
          totalEmployees: createPayrollRunDto.totalEmployees || 0,
          totalGrossPay: createPayrollRunDto.totalGrossPay || '0.00',
          totalDeductions: createPayrollRunDto.totalDeductions || '0.00',
          totalNetPay: createPayrollRunDto.totalNetPay || '0.00',
          notes: createPayrollRunDto.notes,
          createdBy: userId,
        } as any;

        const [newPayrollRun] = await this.db
          .insert(payrollRuns)
          .values(insertData)
          .returning();

        // Log the payroll run creation activity
        await this.activityLogService.logCreate(
          newPayrollRun.id,
          EntityType.PAYROLL_RUN,
          userId,
          businessId,
          {
            source: ActivitySource.WEB,
          },
        );

        // Get the full payroll run data for response
        const fullPayrollRun = await this.findOne(
          userId,
          businessId,
          newPayrollRun.id,
        );
        createdPayrollRuns.push(fullPayrollRun);
      }

      return createdPayrollRuns;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create payroll runs: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createPayrollRunsDto: CreatePayrollRunDto[],
  ): Promise<{ ids: string[] }> {
    const payrollRuns = await this.bulkCreate(
      userId,
      businessId,
      createPayrollRunsDto,
    );
    return { ids: payrollRuns.map((pr) => pr.id) };
  }

  async bulkUpdatePayrollRunStatus(
    userId: string,
    businessId: string | null,
    payrollRunIds: string[],
    status: PayrollRunStatus,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ payrollRunId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!payrollRunIds || payrollRunIds.length === 0) {
        throw new BadRequestException(
          'No payroll run IDs provided for status update',
        );
      }

      // Get all payroll runs that exist and belong to the business
      const existingPayrollRuns = await this.db
        .select({
          id: payrollRuns.id,
          runCode: payrollRuns.runCode,
          runStatus: payrollRuns.runStatus,
        })
        .from(payrollRuns)
        .where(
          and(
            inArray(payrollRuns.id, payrollRunIds),
            eq(payrollRuns.businessId, businessId),
            eq(payrollRuns.isDeleted, false),
          ),
        );

      const updatedIds: string[] = [];
      const failed: Array<{ payrollRunId: string; error: string }> = [];

      // Process each payroll run
      for (const payrollRunId of payrollRunIds) {
        try {
          const payrollRun = existingPayrollRuns.find(
            (pr) => pr.id === payrollRunId,
          );

          if (!payrollRun) {
            failed.push({
              payrollRunId,
              error: 'Payroll run not found or access denied',
            });
            continue;
          }

          // Skip if status is already the same
          if (payrollRun.runStatus === status) {
            updatedIds.push(payrollRunId);
            continue;
          }

          // Update the payroll run status
          await this.db
            .update(payrollRuns)
            .set({
              runStatus: status,
              updatedBy: userId,
              updatedAt: new Date(),
            })
            .where(eq(payrollRuns.id, payrollRunId));

          // Log the status update activity
          await this.activityLogService.logUpdate(
            payrollRunId,
            EntityType.PAYROLL_RUN,
            userId,
            businessId,
            {
              source: ActivitySource.WEB,
            },
          );

          updatedIds.push(payrollRunId);
        } catch (error) {
          failed.push({
            payrollRunId,
            error: error.message || 'Unknown error occurred',
          });
        }
      }

      return {
        updated: updatedIds.length,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update payroll run status: ${error.message}`,
      );
    }
  }
}
