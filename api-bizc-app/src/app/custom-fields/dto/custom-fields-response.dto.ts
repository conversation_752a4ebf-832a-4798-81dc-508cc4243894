import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldDto } from './custom-field.dto';
import { EntityType } from '../../shared/types';
import { CustomFieldEntityType } from '../../shared/types/custom-field.enum';

export class PaginationMetaDto {
  @ApiProperty({ example: 50, description: 'Total number of items' })
  total: number;

  @ApiProperty({ example: 1, description: 'Current page number' })
  page: number;

  @ApiProperty({ example: 5, description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ example: 10, description: 'Items per page' })
  limit: number;
}

export class GetCustomFieldsResponseDto {
  @ApiProperty({
    type: [CustomFieldDto],
    description: 'Array of custom fields',
  })
  fields: CustomFieldDto[];

  @ApiProperty({
    type: PaginationMetaDto,
    description: 'Pagination metadata',
  })
  meta: PaginationMetaDto;
}

export class CustomFieldsCountDto {
  @ApiProperty({
    example: CustomFieldEntityType.CUSTOMERS,
    enum: CustomFieldEntityType,
    enumName: 'CustomFieldEntityType',
    description: 'Entity type',
  })
  entityType: CustomFieldEntityType;

  @ApiProperty({
    example: 5,
    description: 'Number of custom fields for this entity type',
  })
  count: number;
}

export class GetCustomFieldsCountResponseDto {
  @ApiProperty({
    type: [CustomFieldsCountDto],
    description: 'Array of custom field counts by entity type',
  })
  counts: CustomFieldsCountDto[];

  @ApiProperty({
    example: 25,
    description: 'Total number of custom fields across all entity types',
  })
  total: number;
}
