import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CustomFieldType, EntityType } from '../../shared/types';

export class CustomFieldDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Custom field ID',
  })
  id: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Business ID',
  })
  businessId: string;

  @ApiProperty({
    example: 'customer_preference',
    description: 'Field name',
  })
  name: string;

  @ApiProperty({
    example: CustomFieldType.TEXT,
    enum: CustomFieldType,
    enumName: 'CustomFieldType',
    description: 'Type of the custom field',
  })
  type: CustomFieldType;

  @ApiProperty({
    example: EntityType.CUSTOMER,
    enum: EntityType,
    enumName: 'EntityType',
    description: 'Entity type this field belongs to',
  })
  entityType: EntityType;

  @ApiProperty({
    example: false,
    description: 'Whether the field is required',
  })
  required: boolean;

  @ApiPropertyOptional({
    example: 'Enter your preference',
    description: 'Placeholder text for the field',
  })
  placeholder?: string;

  @ApiPropertyOptional({
    example: ['Option 1', 'Option 2', 'Option 3'],
    description: 'Options for select/multi-select/radio fields',
    type: [String],
  })
  options?: string[];

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Created by user ID',
  })
  createdBy: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Creation timestamp',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00Z',
    description: 'Last update timestamp',
  })
  updatedAt: Date;
}
