const fs = require('fs');
const path = require('path');
const glob = require('glob');

// The expanded fields from createBaseEntityFields
const expandedFields = `    // Base entity fields
    id: uuid('id').defaultRandom().primaryKey(),
    
    // Audit fields
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
    isDeleted: boolean('is_deleted').default(false).notNull()`;

// Function to process a single file
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if file uses createBaseEntityFields
  if (!content.includes('...createBaseEntityFields')) {
    console.log(`  - No spread operator found, skipping.`);
    return;
  }
  
  // Check if file already has the import for timestamp
  let hasTimestampImport = content.includes("timestamp");
  
  // Update imports if needed
  if (!hasTimestampImport) {
    // Find the drizzle-orm import line
    const importMatch = content.match(/from 'drizzle-orm\/pg-core';/);
    if (importMatch) {
      // Add timestamp to the imports
      content = content.replace(
        /import\s*{\s*([^}]+)\s*}\s*from\s*'drizzle-orm\/pg-core';/,
        (match, imports) => {
          const importList = imports.split(',').map(i => i.trim()).filter(i => i);
          if (!importList.includes('timestamp')) {
            importList.push('timestamp');
          }
          return `import {\n  ${importList.join(',\n  ')}\n} from 'drizzle-orm/pg-core';`;
        }
      );
    }
  }
  
  // Ensure users import exists
  if (!content.includes("import { users }") && !content.includes("from './users.schema'")) {
    // Add users import after other schema imports
    const schemaImportRegex = /import\s*{[^}]*}\s*from\s*['"]\.\/[^'"]+\.schema['"];/g;
    let lastImportMatch;
    let match;
    while ((match = schemaImportRegex.exec(content)) !== null) {
      lastImportMatch = match;
    }
    
    if (lastImportMatch) {
      const insertPosition = lastImportMatch.index + lastImportMatch[0].length;
      content = content.slice(0, insertPosition) + 
        "\nimport { users } from './users.schema';" +
        content.slice(insertPosition);
    }
  }
  
  // Replace ...createBaseEntityFields with the expanded fields
  const regex = /(\s*)\.\.\.createBaseEntityFields,?/g;
  content = content.replace(regex, (match, indent) => {
    // Preserve the original indentation
    const lines = expandedFields.split('\n');
    return lines.map((line, index) => {
      if (index === 0) return line; // First line doesn't need extra indent
      return indent + line;
    }).join('\n') + ',';
  });
  
  // Remove the createBaseEntityFields import if it's no longer used
  if (!content.includes('createBaseEntityFields')) {
    // Remove from import statement
    content = content.replace(
      /,?\s*createBaseEntityFields\s*,?/g,
      ''
    );
    
    // Clean up empty imports
    content = content.replace(
      /import\s*{\s*}\s*from\s*['"]\.\/common-fields\.schema['"];?\s*\n/g,
      ''
    );
  }
  
  // Write the modified content back
  fs.writeFileSync(filePath, content);
  console.log(`  - Replaced successfully!`);
}

// Main execution
async function main() {
  const schemaDir = '/Users/<USER>/Documents/e9-projects/shopc/apps/api-bizc-app/src/app/drizzle/schema';
  
  // Find all .schema.ts files
  const files = glob.sync(path.join(schemaDir, '*.schema.ts'));
  
  console.log(`Found ${files.length} schema files\n`);
  
  for (const file of files) {
    // Skip common-fields.schema.ts itself
    if (file.includes('common-fields.schema.ts')) {
      continue;
    }
    
    try {
      processFile(file);
    } catch (error) {
      console.error(`Error processing ${file}:`, error.message);
    }
  }
  
  console.log('\nDone!');
}

// Run the script
main().catch(console.error);