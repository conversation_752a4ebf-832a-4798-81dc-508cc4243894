const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Function to process a single file
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Check if file uses createBaseEntityFields
  if (!content.includes('...createBaseEntityFields')) {
    console.log(`  - No spread operator found, skipping.`);
    return;
  }
  
  // Check if we need timestamp import
  const hasTimestampImport = /import\s*{[^}]*timestamp[^}]*}\s*from\s*['"]drizzle-orm\/pg-core['"]/.test(content);
  
  // Add timestamp to drizzle imports if needed
  if (!hasTimestampImport) {
    content = content.replace(
      /import\s*{([^}]+)}\s*from\s*['"]drizzle-orm\/pg-core['"];?/,
      (match, imports) => {
        const importList = imports.split(',').map(i => i.trim()).filter(i => i);
        if (!importList.includes('timestamp')) {
          importList.push('timestamp');
        }
        return `import { ${importList.join(', ')} } from 'drizzle-orm/pg-core';`;
      }
    );
  }
  
  // Check if we need users import
  const hasUsersImport = /import\s*{[^}]*users[^}]*}\s*from\s*['"]\.\/users\.schema['"]/.test(content);
  
  // Add users import if needed
  if (!hasUsersImport) {
    // Find a good position to insert the users import (after other schema imports)
    const schemaImportMatch = content.match(/import\s*{[^}]*}\s*from\s*['"]\.\/[^'"]+\.schema['"];?\s*\n/g);
    if (schemaImportMatch) {
      const lastImport = schemaImportMatch[schemaImportMatch.length - 1];
      const insertPos = content.indexOf(lastImport) + lastImport.length;
      content = content.slice(0, insertPos) + 
        "import { users } from './users.schema';\n" +
        content.slice(insertPos);
    }
  }
  
  // Replace ...createBaseEntityFields with the expanded fields
  content = content.replace(
    /(\s*)\.\.\.createBaseEntityFields,?/g,
    (match, indent) => {
      const fields = [
        `${indent}// Base entity fields`,
        `${indent}id: uuid('id').defaultRandom().primaryKey(),`,
        `${indent}`,
        `${indent}// Audit fields`,
        `${indent}createdBy: uuid('created_by')`,
        `${indent}  .notNull()`,
        `${indent}  .references(() => users.id),`,
        `${indent}updatedBy: uuid('updated_by').references(() => users.id),`,
        `${indent}createdAt: timestamp('created_at').defaultNow().notNull(),`,
        `${indent}updatedAt: timestamp('updated_at').defaultNow().notNull(),`,
        `${indent}isDeleted: boolean('is_deleted').default(false).notNull(),`
      ];
      return fields.join('\n');
    }
  );
  
  // Clean up imports - remove createBaseEntityFields if it's no longer used
  if (!content.includes('createBaseEntityFields')) {
    // Remove createBaseEntityFields from imports
    content = content.replace(
      /import\s*{([^}]+)}\s*from\s*['"]\.\/common-fields\.schema['"];?/g,
      (match, imports) => {
        const importList = imports.split(',')
          .map(i => i.trim())
          .filter(i => i && i !== 'createBaseEntityFields');
        
        if (importList.length === 0) {
          return ''; // Remove the entire import if empty
        }
        return `import { ${importList.join(', ')} } from './common-fields.schema';`;
      }
    );
    
    // Clean up any resulting empty lines
    content = content.replace(/\n\n\n+/g, '\n\n');
  }
  
  // Only write if content changed
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content);
    console.log(`  - Replaced successfully!`);
  } else {
    console.log(`  - No changes needed.`);
  }
}

// Main execution
async function main() {
  const schemaDir = '/Users/<USER>/Documents/e9-projects/shopc/apps/api-bizc-app/src/app/drizzle/schema';
  
  // Find all .schema.ts files
  const files = glob.sync(path.join(schemaDir, '*.schema.ts'));
  
  console.log(`Found ${files.length} schema files\n`);
  
  for (const file of files) {
    // Skip common-fields.schema.ts and users.schema.ts
    if (file.includes('common-fields.schema.ts') || file.includes('users.schema.ts')) {
      console.log(`Skipping: ${file}`);
      continue;
    }
    
    try {
      processFile(file);
    } catch (error) {
      console.error(`Error processing ${file}:`, error.message);
    }
  }
  
  console.log('\nDone! Remember to:');
  console.log('1. Run "yarn lint" to fix any formatting issues');
  console.log('2. Run "yarn build" to ensure everything compiles');
  console.log('3. Test your application thoroughly');
}

// Run the script
main().catch(console.error);