#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

const schemaDir = '/Users/<USER>/Documents/e9-projects/shopc/apps/api-bizc-app/src/app/drizzle/schema';

// Function to generate direct indexes based on the original function logic
function generateDirectIndexes(tableName, tableVar = 't') {
  return `    // Base entity indexes
    idIndex: index('${tableName}_id_index').on(${tableVar}.id),
    businessIdIndex: index('${tableName}_business_id_index').on(${tableVar}.businessId),
    createdByIndex: index('${tableName}_created_by_index').on(${tableVar}.createdBy),
    updatedByIndex: index('${tableName}_updated_by_index').on(${tableVar}.updatedBy),
    createdAtIndex: index('${tableName}_created_at_index').on(${tableVar}.createdAt),
    updatedAtIndex: index('${tableName}_updated_at_index').on(${tableVar}.updatedAt),
    isDeletedIndex: index('${tableName}_is_deleted_index').on(${tableVar}.isDeleted),`;
}

// Function to process a single file
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if file uses createBaseEntityBusinessIndexes
  if (!content.includes('createBaseEntityBusinessIndexes')) {
    console.log(`  Skipping - no createBaseEntityBusinessIndexes usage found`);
    return;
  }
  
  // Remove the import of createBaseEntityBusinessIndexes
  content = content.replace(
    /,?\s*createBaseEntityBusinessIndexes,?/g, 
    ''
  );
  
  // Clean up any trailing commas in import statements
  content = content.replace(
    /import\s*{\s*([^}]*),\s*}\s*from/g,
    'import { $1 } from'
  );
  
  // Find all table definitions and their index usage
  const tableMatches = content.matchAll(/export const (\w+) = pgTable\(\s*['"`]([^'"`]+)['"`],[\s\S]*?\(([^)]+)\) => \(\{([\s\S]*?)\}\),?\s*\);/g);
  
  for (const match of tableMatches) {
    const tableName = match[1];
    const tableNameString = match[2];
    const tableParam = match[3].trim();
    const indexesContent = match[4];
    
    // Check if this table uses createBaseEntityBusinessIndexes
    const baseIndexPattern = new RegExp(`\\.\\.\\.createBaseEntityBusinessIndexes\\(${tableParam},\\s*['"\`]${tableNameString}['"\`]\\)`);
    
    if (baseIndexPattern.test(indexesContent)) {
      console.log(`  Replacing indexes in table: ${tableName}`);
      
      // Generate the direct indexes
      const directIndexes = generateDirectIndexes(tableNameString, tableParam);
      
      // Replace the function call with direct indexes
      const newIndexesContent = indexesContent.replace(
        baseIndexPattern,
        directIndexes
      );
      
      // Replace in the full content
      content = content.replace(indexesContent, newIndexesContent);
    }
  }
  
  // Write the updated content back to the file
  fs.writeFileSync(filePath, content);
  console.log(`  Updated: ${filePath}`);
}

// Main execution
function main() {
  console.log('Starting replacement of createBaseEntityBusinessIndexes...\n');
  
  // Get all .ts files in the schema directory
  const files = fs.readdirSync(schemaDir)
    .filter(file => file.endsWith('.schema.ts'))
    .map(file => path.join(schemaDir, file));
  
  console.log(`Found ${files.length} schema files to process\n`);
  
  // Process each file
  files.forEach(processFile);
  
  console.log('\nProcessing complete!');
  console.log('Next step: Remove createBaseEntityBusinessIndexes function from common-fields.schema.ts');
}

main();