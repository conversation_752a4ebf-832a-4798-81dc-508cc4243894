#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

const schemaDir = '/Users/<USER>/Documents/e9-projects/shopc/apps/api-bizc-app/src/app/drizzle/schema';

// Function to generate direct indexes based on the original function logic
function generateDirectIndexes(tableName, tableVar = 't') {
  return `    // Base entity indexes
    idIndex: index('${tableName}_id_index').on(${tableVar}.id),
    businessIdIndex: index('${tableName}_business_id_index').on(${tableVar}.businessId),
    createdByIndex: index('${tableName}_created_by_index').on(${tableVar}.createdBy),
    updatedByIndex: index('${tableName}_updated_by_index').on(${tableVar}.updatedBy),
    createdAtIndex: index('${tableName}_created_at_index').on(${tableVar}.createdAt),
    updatedAtIndex: index('${tableName}_updated_at_index').on(${tableVar}.updatedAt),
    isDeletedIndex: index('${tableName}_is_deleted_index').on(${tableVar}.isDeleted),`;
}

// Function to process a single file
function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Fix broken function calls like "...(t, 'table_name')"
  content = content.replace(/\.\.\.\(([^,)]+),\s*['"`]([^'"`]+)['"`]\)/g, (match, tableVar, tableName) => {
    console.log(`  Fixing broken reference: ${match}`);
    return generateDirectIndexes(tableName, tableVar);
  });
  
  // Clean up any remaining broken import references
  content = content.replace(/,\s*createBaseEntityBusinessIndexes\s*,/g, ',');
  content = content.replace(/createBaseEntityBusinessIndexes\s*,/g, '');
  content = content.replace(/,\s*createBaseEntityBusinessIndexes/g, '');
  
  // Clean up any double commas in imports
  content = content.replace(/,\s*,/g, ',');
  
  // Clean up trailing commas in import statements
  content = content.replace(/import\s*{\s*([^}]*),\s*}\s*from/g, 'import { $1 } from');
  content = content.replace(/import\s*{\s*,\s*([^}]*)\s*}\s*from/g, 'import { $1 } from');
  
  // Write the updated content back to the file
  fs.writeFileSync(filePath, content);
  console.log(`  Fixed: ${filePath}`);
}

// Main execution
function main() {
  console.log('Fixing broken index references...\n');
  
  // Get all .ts files in the schema directory
  const files = fs.readdirSync(schemaDir)
    .filter(file => file.endsWith('.schema.ts'))
    .map(file => path.join(schemaDir, file));
  
  // Process each file
  files.forEach(processFile);
  
  console.log('\nFix processing complete!');
}

main();