CREATE TYPE "public"."asset_transaction_status" AS ENUM('pending', 'in_progress', 'completed', 'cancelled', 'failed');--> statement-breakpoint
CREATE TYPE "public"."asset_type_category" AS ENUM('PHYSICAL', 'DIGITAL');--> statement-breakpoint
CREATE TYPE "public"."asset_type_reference_type" AS ENUM('rental-item-category', 'vehicle-category');--> statement-breakpoint
CREATE TYPE "public"."media_purpose" AS ENUM('primary', 'gallery', 'attachment', 'og_image', 'banner', 'icon', 'other');--> statement-breakpoint
CREATE TABLE "media_associations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"created_by" uuid NOT NULL,
	"updated_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"is_deleted" boolean DEFAULT false NOT NULL,
	"business_id" uuid NOT NULL,
	"reference_id" uuid NOT NULL,
	"reference_type" "media_reference_type" NOT NULL,
	"media_id" uuid NOT NULL,
	"position" integer DEFAULT 0 NOT NULL,
	"purpose" "media_purpose" DEFAULT 'other' NOT NULL,
	"title" text,
	"description" text,
	"is_primary" boolean DEFAULT false NOT NULL,
	"is_visible" boolean DEFAULT true NOT NULL,
	"metadata" text
);
--> statement-breakpoint
ALTER TABLE "media_array" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "user_sessions" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "media_array" CASCADE;--> statement-breakpoint
DROP TABLE "user_sessions" CASCADE;--> statement-breakpoint
ALTER TABLE "asset_types" DROP CONSTRAINT "asset_types_image_media_id_fk";
--> statement-breakpoint
ALTER TABLE "asset_types" DROP CONSTRAINT "asset_types_og_image_media_id_fk";
--> statement-breakpoint
DROP INDEX "asset_types_image_index";--> statement-breakpoint
DROP INDEX "asset_types_og_image_index";--> statement-breakpoint
ALTER TABLE "asset_transactions" ALTER COLUMN "transaction_datetime" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "asset_transactions" ADD COLUMN "status" "asset_transaction_status" DEFAULT 'completed' NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_transactions" ADD COLUMN "return_due_date" date;--> statement-breakpoint
ALTER TABLE "asset_transactions" ADD COLUMN "actual_return_date" date;--> statement-breakpoint
ALTER TABLE "asset_transactions" ADD COLUMN "condition_before" text;--> statement-breakpoint
ALTER TABLE "asset_transactions" ADD COLUMN "condition_after" text;--> statement-breakpoint
ALTER TABLE "asset_transactions" ADD COLUMN "notes" text;--> statement-breakpoint
ALTER TABLE "asset_types" ADD COLUMN "category" "asset_type_category" NOT NULL;--> statement-breakpoint
ALTER TABLE "asset_types" ADD COLUMN "reference_id" uuid;--> statement-breakpoint
ALTER TABLE "asset_types" ADD COLUMN "reference_type" "asset_type_reference_type";--> statement-breakpoint
ALTER TABLE "media_associations" ADD CONSTRAINT "media_associations_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "media_associations" ADD CONSTRAINT "media_associations_updated_by_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "media_associations" ADD CONSTRAINT "media_associations_business_id_business_id_fk" FOREIGN KEY ("business_id") REFERENCES "public"."business"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "media_associations" ADD CONSTRAINT "media_associations_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "media_associations_business_id_index" ON "media_associations" USING btree ("business_id");--> statement-breakpoint
CREATE INDEX "media_associations_id_index" ON "media_associations" USING btree ("id");--> statement-breakpoint
CREATE INDEX "media_associations_reference_id_index" ON "media_associations" USING btree ("reference_id");--> statement-breakpoint
CREATE INDEX "media_associations_reference_type_index" ON "media_associations" USING btree ("reference_type");--> statement-breakpoint
CREATE INDEX "media_associations_reference_composite_index" ON "media_associations" USING btree ("reference_id","reference_type");--> statement-breakpoint
CREATE INDEX "media_associations_media_id_index" ON "media_associations" USING btree ("media_id");--> statement-breakpoint
CREATE INDEX "media_associations_position_index" ON "media_associations" USING btree ("position");--> statement-breakpoint
CREATE INDEX "media_associations_purpose_index" ON "media_associations" USING btree ("purpose");--> statement-breakpoint
CREATE INDEX "media_associations_sorting_composite_index" ON "media_associations" USING btree ("reference_id","reference_type","purpose","position");--> statement-breakpoint
CREATE INDEX "media_associations_primary_index" ON "media_associations" USING btree ("reference_id","reference_type","is_primary");--> statement-breakpoint
CREATE UNIQUE INDEX "unique_primary_per_purpose" ON "media_associations" USING btree ("reference_id","reference_type","purpose","is_primary");--> statement-breakpoint
CREATE INDEX "asset_transactions_status_index" ON "asset_transactions" USING btree ("status");--> statement-breakpoint
CREATE INDEX "asset_transactions_business_status_index" ON "asset_transactions" USING btree ("business_id","status") WHERE "asset_transactions"."is_deleted" = false;--> statement-breakpoint
CREATE INDEX "asset_types_category_index" ON "asset_types" USING btree ("category");--> statement-breakpoint
CREATE INDEX "asset_types_reference_id_index" ON "asset_types" USING btree ("reference_id");--> statement-breakpoint
CREATE INDEX "asset_types_reference_type_index" ON "asset_types" USING btree ("reference_type");--> statement-breakpoint
CREATE INDEX "asset_types_business_category_index" ON "asset_types" USING btree ("business_id","category");--> statement-breakpoint
CREATE INDEX "asset_types_reference_index" ON "asset_types" USING btree ("reference_id","reference_type");--> statement-breakpoint
CREATE UNIQUE INDEX "asset_types_reference_unique" ON "asset_types" USING btree ("reference_id","reference_type") WHERE "asset_types"."reference_id" IS NOT NULL AND "asset_types"."reference_type" IS NOT NULL AND "asset_types"."is_deleted" = false;--> statement-breakpoint
ALTER TABLE "asset_transactions" DROP COLUMN "reason";--> statement-breakpoint
ALTER TABLE "asset_types" DROP COLUMN "image";--> statement-breakpoint
ALTER TABLE "asset_types" DROP COLUMN "og_image";--> statement-breakpoint
DROP TYPE "public"."device_type";--> statement-breakpoint
DROP TYPE "public"."session_source";--> statement-breakpoint
DROP TYPE "public"."session_status";