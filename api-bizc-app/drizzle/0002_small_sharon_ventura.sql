ALTER TABLE "services" ADD COLUMN "service_sub_category_id" uuid;--> statement-breakpoint
ALTER TABLE "services" ADD CONSTRAINT "services_service_sub_category_id_service_categories_id_fk" FOREIGN KEY ("service_sub_category_id") REFERENCES "public"."service_categories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "services_service_sub_category_id_index" ON "services" USING btree ("service_sub_category_id");